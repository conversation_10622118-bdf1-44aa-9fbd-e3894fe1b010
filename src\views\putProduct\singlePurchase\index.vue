<template>
	<div v-loading="loading" class="singlePurchase-page">
		<div style="fontWeight:500; fontSize:14px;padding:10px;">
			<span style="margin:0 10px;">当前已选品种</span>
			<el-tag>商品类型：批购包邮</el-tag>
			<div v-if="!$route.query.barcode" style="display:inline-block;float:right;">
				<!-- <el-button size="mini" @click="go('/putProduct/multiple', { fromType: 'edit' }, false)">返回上一页</el-button> -->
				<el-button size="mini" @click="go('/putProduct/multiple', { fromType: 'edit' }, true)">返回上一页</el-button>
				<el-button size="mini" @click="go('/putProduct/multiple', { fromType: 'edit' }, true)">取消</el-button>
				<el-button size="mini" type="primary" @click="commit()">提交</el-button>
			</div>
			<div v-else-if="$route.query.freeMail" style="display:inline-block;float:right;">
				<el-button size="mini" @click="go('/bulkPurchaseFreeMail', null, true)">返回上一页</el-button>
				<el-button v-if="fromType == 'edit'" size="mini" type="primary" @click="commit(true)">提交</el-button>
			</div>
			<div v-else style="display:inline-block;float:right;">
				<el-button size="mini" @click="go('/productList', null, true)">返回上一页</el-button>
				<el-button v-if="fromType == 'edit'" size="mini" type="primary" @click="commit()">提交</el-button>
			</div>
		</div>
		<!-- 商品信息 -->
		<div class="xProductInfo">
			<div>
				<img-list :value="formData.imageUrl" :hwRatio="1" :maxShowLength="1" :maxCol="1" :deleteBtn="false" :showNullEl="true"></img-list>
			</div>
			<div style="display:flex;">
				<div style="width:300px">
					<p class="item-left">
						<span>通用名称：</span>
						<span>{{ formData.commonName }}</span>
					</p>
					<p class="item-left">
						<span>规格：</span>
						<span>{{ formData.spec }}</span>
					</p>
					<p class="item-left">
						<span>包装单位：</span>
						<span>{{ formData.mediumPackageTitle }}</span>
					</p>
					<p class="item-left">
						<span>商品编码：</span>
						<span>{{ formData.barcode }}</span>
					</p>
				</div>
				<div style="width:300px;margin:0 40px;">
					<p class="item-left">
						<span>商品名称：</span>
						<span>{{ formData.productName }}</span>
					</p>
					<p class="item-left">
						<span>生产厂家：</span>
						<span>{{ formData.manufacturer }}</span>
					</p>
					<p class="item-left">
						<span>处方类型：</span>
						<span>{{ typeOption[formData.drugClassificationName || 0] }}</span>
					</p>
					<p class="item-left">
						<span>ERP编码：</span>
						<span>{{ formData.erpCode }}</span>
					</p>
				</div>
				<div style="width:300px">
					<p class="item-left">
						<span>展示名称：</span>
						<span>{{ formData.showName }}</span>
					</p>
					<p class="item-left">
						<span>批准文号：</span>
						<span>{{ formData.approvalNumber }}</span>
					</p>
					<p class="item-left">
						<span>价格：</span>
						<span>{{ formData.fob }}</span>
					</p>
				</div>
			</div>
		</div>
		<!-- 标签导航 -->
		<!-- 时间-->
		<el-form ref="form" label-width="170px" :rules="rules" :model="form">
			<el-form-item label="上下架时间：" prop="stime">
				<date :disabled="fromType == 'detail'" style="width:400px;" v-model="form" endProp="etime" startProp="stime"></date>
			</el-form-item>
			<el-tabs v-model="tabs" type="card">
				<el-tab-pane label="价格库存" name="first"></el-tab-pane>
				<el-tab-pane label="起购数量" name="second"></el-tab-pane>
				<el-tab-pane label="供货信息" name="third"></el-tab-pane>
				<el-tab-pane label="其它信息" name="fourth"></el-tab-pane>
			</el-tabs>
			<common-header id="first" tabindex="-1" title="价格库存" :showFold="false">
				<el-form-item label="原价：" label-width="170px">
					<el-input :disabled="fromType == 'detail'" :value="form.snapPrice" placeholder="非必填" size="mini" style="width:200px;" @input="(val) => costChange(val, 'form', 'snapPrice', /^[0-9]*([.][0-9]{0,2})?$/)" @blur="() => {if(form.snapPrice != '') form.snapPrice = Number(form.snapPrice).toFixed(2)}"></el-input>
				</el-form-item>
				<el-form-item label="活动价：" label-width="170px" prop="groupPrice">
					<el-input :disabled="fromType == 'detail'" :value="form.groupPrice" placeholder="必填" size="mini" style="width:200px;" @input="(val) => costChange(val, 'form', 'groupPrice', /^[0-9]*([.][0-9]{0,2})?$/)"></el-input>
				</el-form-item>
				<el-form-item label="商品实时库存：" label-width="170px">
					<el-input disabled :value="formData.availableQty" size="mini" style="width:200px;margin-right:10px;"></el-input>
				</el-form-item>
			</common-header>
			<common-header id="second" tabindex="-1" title="起购信息" :showFold="false">
				<el-form-item label="起购数量：" label-width="170px" prop="groupNum">
					<el-input :disabled="fromType == 'detail'" :value="form.groupNum" size="mini" style="width:200px;"  @input="(val) => costChange(val, 'form', 'groupNum', /^[0-9]*$/)"></el-input>
					<span style="margin-left:10px; color:#ff2121;">药店单次购买的最小采购数量，需为正整数。</span>
				</el-form-item>
				<el-form-item label="购买倍数：" label-width="170px" prop="mediumPackageNum">
					<el-input :disabled="fromType == 'detail'" :value="form.mediumPackageNum" size="mini" style="width:200px;" @input="(val) => costChange(val, 'form', 'mediumPackageNum', /^[0-9]*$/)"></el-input>
					<span style="margin-left:10px; color:#ff2121;">购买倍数即每次点击加购的数量。起购数量必须是购买倍数的整数倍。</span>
				</el-form-item>
				<!-- <el-form-item style="margin:0;">
					<p>购买倍数即每次点击加购的数量。起购数量必须是购买倍数的整数倍。</p>
				</el-form-item> -->
				<el-form-item label="活动数量是否限购：" label-width="170px">
					<!-- <el-radio :disabled="fromType == 'detail'" v-model="radio.total" label="1">不限购</el-radio>
					<el-radio :disabled="fromType == 'detail'" v-model="radio.total" label="2">限购</el-radio> -->
					<el-switch :disabled="fromType == 'detail'" v-model="radio.total" active-text="限制" inactive-text="不限制">
					</el-switch>
				</el-form-item>
				<el-form-item v-if="radio.total" label="活动数量总上限：" label-width="170px" prop="totalLimitNum">
					<el-input :disabled="fromType == 'detail'" :value="form.totalLimitNum" size="mini" placeholder="请输入正整数" style="width:200px;" @input="(val) => costChange(val, 'form', 'totalLimitNum', /^[0-9]*$/)"></el-input>
					<span style="margin-left:10px; color:#ff2121;display:inline-block;position: absolute;">
						• 所有药店在设定的周期内可购买的最大采购数量，需为正整数。<br />
						• 从不限购切换为限购，则从修改时间开始重新计算限购。
					</span>
				</el-form-item>
				<el-form-item label="单个药店采购是否限购：" label-width="170px">
					<!-- <el-radio :disabled="fromType == 'detail'" v-model="radio.personal" label="1">不限购</el-radio>
					<el-radio :disabled="fromType == 'detail'" v-model="radio.personal" label="2">限购</el-radio>	 -->
					<el-switch :disabled="fromType == 'detail'" v-model="radio.personal" active-text="限制" inactive-text="不限制">
					</el-switch>
				</el-form-item>
				<el-form-item v-if="radio.personal" label="单个药店采购类型：" label-width="170px">
					<el-select :disabled="fromType == 'detail'" v-model="form.personalLimitType" placeholder="请选择" size="mini">
						<el-option v-for="item in personalLimitTypeList" :key="item.code" :label="item.value" :value="item.code"></el-option>
					</el-select>
				</el-form-item>
				<el-form-item  v-if="radio.personal" label="单个药店采购数量：" label-width="170px" prop="personalLimitNum">
					<el-input :disabled="fromType == 'detail'" :value="form.personalLimitNum" size="mini" placeholder="请输入正整数" style="width:200px;" @input="(val) => costChange(val, 'form', 'personalLimitNum', /^[0-9]*$/)"></el-input>
					<span style="margin-left:10px; color:#ff2121;display:inline-block;position: absolute;">
						单个药店在设定的周期内可购买的最大采购数量，需为正整数。限购数量需≥起购数量。<br />
						从不限购切换为限购或调整限购类型，则从修改时间开始重新计算限购。
					</span>
				</el-form-item>
			</common-header>
			<common-header id="third" tabindex="-1" title="供货信息" :showFold="false">
				<el-form-item label="设置虚拟供应商：">
					<div>
						<el-radio :disabled="fromType == 'detail'" v-model="form.isVirtualShop" :label="1">是</el-radio>
						<el-radio :disabled="fromType == 'detail'" v-model="form.isVirtualShop" :label="2">否</el-radio>
					</div>
					<span style="color: #ff2121;display: inline-block;width: 80%;vertical-align: top">
					• 选“是”，则该拼团活动支付前都只显示虚拟供应商信息，支付成功后才显示真实供应商信息<br>
					• 选“否”，则正常显示供应商信息
					</span>
				</el-form-item>
				<!-- <el-form-item title="供货信息配置方式：">
					<el-radio v-model="form.isVirtualShop" label="1">是</el-radio>
					<el-radio v-model="form.isVirtualShop" label="2">否</el-radio>
				</el-form-item> -->
				<SupplyTypeConfig v-model="supplyInfo" :baseCustomerGroupName="null" :baseCustomerGroupId="null" :disabled="fromType != 'edit'" :subOtherDisabled="false" :sale-scope-dto="saleScopeDTO"/>
			</common-header>
			<common-header id="fourth" tabindex="-1" title="其它信息" :showFold="false">
				<p style="color:red;">商品近远效期、导单系数、中包装、商品佣金比例、商品图片、商品中包装和原品保持一致，原品变更后批购包邮商品同步变更。</p>
			</common-header>
		</el-form>
	</div>
</template>

<script>
import commonHeader from '../../afterSaleManager/components/common-header.vue'
import imgList from '../../afterSaleManager/components/imgList.vue'
import SupplyTypeConfig from '../component/supplyTypeConfig.vue';
import date from '../component/date.vue'
import {
	singleAdd
} from '../../../api/putProduct/index'
import { apiDetail } from "../../../api/product/index"
export default {
	name: 'product-single',
	components: {
		commonHeader,
		date,
		imgList,
		SupplyTypeConfig
	},
	watch: {
		tabs(newVal) {
			const targetElement = document.getElementById(newVal);
			if (targetElement) { 
				targetElement.scrollIntoView();
			}
		}
	},
	data() {
		return {
			loading: false,
			tabs: 'first',
			fromType: '',
			typeOption: ["无", "甲类OTC", "乙类OTC", "处方药"],
			rules: {
				stime: { trigger: 'blur', required: true, message: '请填写开始时间' },
				groupPrice: { trigger: 'blur', required: true, message: '请填写活动价' },
				groupNum: { trigger: 'blur', required: true, message: '请填写起购数量' },
				personalLimitNum: { trigger: 'blur', required: true, message: '请填写单个药店限购数量' },
				totalLimitNum: { trigger: 'blur', required: true, message: '请填写活动数量总上限'},
				mediumPackageNum: { trigger: 'blur', required: true, message: '请填写购买倍数' },
			},
			form: {
				stime: '',    //开始时间
				etime: '',	  //结束时间
				barcode: '',    //商品编码
				snapPrice: '',       // 原价 
				groupPrice: '',     //活动价
				groupNum: '',     // 起购数量 
				personalLimitType: '',   //个人限购类型
				personalLimitNum: '',   //个人限购数量
				totalLimitNum: '',     //活动总上限
				isVirtualShop: 2,    //虚拟供应商
				mediumPackageNum: 1,   //购买倍数
			},
			personalLimitTypeList: [{
				code: 1, value: "活动期间限购"
			}, {
				code: 2, value: "每天（每天00:00至24:00）"
			}, {
				code: 3, value: "单笔订单限购"
			}, {
				code: 4, value: "每周（周一00:00至周日24:00）"
			}, {
				code: 5, value: "每月（每月1号00:00至每月最后一天24:00）"
			}],
			radio: {
				total: false,
				personal: false,
			},
			formData: {
			},
			actId: '',
			supplyInfo: {},
			saleScopeDTO: null,
			barcode: '',
			myBarcode: '',
		}
	},
	// activated() {
	// 	let formData = window.sessionStorage.getItem("formData");
	// 	if (formData) {
	// 		formData = JSON.parse(formData);
	// 		console.log(formData.barcode, this.myBarcode);
	// 		if(formData.barcode == this.myBarcode) return
	// 	}
	// 	// console.log('ggg');
	// 	this.initNew();
	// },
	created() {
		// console.log('hhh');
		this.initNew();
	},
	
	methods: {
		go(to, query, close) {
			if (close) {
				window.closeTab(this.$route.fullPath, true);
				window.sessionStorage.removeItem("formData")
			}
			setTimeout(() => {
				window.openTab(to, query ? query : {});
			}, 0)
		},
		initNew(){
			this.fromType = this.$route.query.fromType;
			this.init();
			if (this.$route.query.barcode) {
				this.barcode = this.$route.query.barcode;
				this.actId = this.$route.query.actid;
				this.loading = true;
				apiDetail({ barcode: this.$route.query.barcode }).then(res => {
					if (res.code == 0) {
						for (const key in this.form) {
							this.form[key] = res.data[key] || this.form[key];
						}
						if (!this.form.personalLimitType || this.form.personalLimitNum == '') {
							this.radio.personal = false;
						} else {
							this.radio.personal = true;
						}
						if (this.form.totalLimitNum == '') {
							this.radio.total = false;
						} else {
							this.radio.total = true;
						}
						this.form.snapPrice = res.data.fob;
						this.formData.imageUrl = [res.data.imageUrl];
						this.formData.commonName = res.data.commonName;
						this.formData.spec = res.data.spec;
						this.formData.mediumPackageTitle = res.data.productUnit;
						this.formData.barcode = res.data.barcode
						this.formData.productName = res.data.productName;
						this.formData.manufacturer = res.data.manufacturer;
						this.formData.drugClassificationName = res.data.drugClassification;
						this.formData.erpCode = res.data.erpCode;
						this.formData.showName = res.data.showName;
						this.formData.approvalNumber = res.data.approvalNumber;
						this.formData.fob = res.data.fob;
						this.formData.guidePrice = res.data.guidePrice;
						this.formData.availableQty = res.data.availableQty;
						this.formData.activityStatus = res.data.activityStatus;
						this.saleScopeDTO = {
							busAreaId : res.data.busAreaId,
							busAreaName : res.data.busAreaConfigName,
							controlGroupId : res.data.controlGroupId,
							controlGroupName : res.data.controlGroupName,
							controlRosterType : res.data.controlRosterType,
							controlUserTypeList : res.data.controlUserTypes,
							controlUserTypes : res.data.controlUserTypes ? res.data.controlUserTypes.join(',') : '',
							creator : res.data.creator,
							ctime : res.data.ctime,
							customerGroupId : res.data.customerGroupId,
							customerGroupName : res.data.customerGroupName,
							etime : res.data.etime,
							id : res.data.id,
							isCopyBusArea : res.data.isCopyBusArea,
							isCopyControlRoster : res.data.isCopyControlRoster,
							isCopyControlUser : res.data.isCopyControlUser,
							isCopySaleArea : res.data.isCopySaleArea,
							reportId : res.data.reportId,
							scopeType : res.data.scopeType,
							shopCode : res.data.shopCode,
							stime : res.data.stime,
							updater : res.data.updater,
							utime : res.data.utime,
						}
						console.log(this.saleScopeDTO);
						console.log(this.form);
					}
				}).catch(err => {
					this.$message.error("查询失败！");
					window.closeTab(this.$route.fullPath);
				}).finally(() => {
					this.loading = false;
				})
			} else {
				this.barcode = '';
			}
		},
		init() {
			this.form.stime = Date.now();
			this.form.etime = this.form.stime + 60 * 60 * 24 * 365 * 2 * 1000;
			const formData = window.sessionStorage.getItem("formData");
			for (const key in this.form) {
				this.form[key] = '';
			}
			this.form.mediumPackageNum = 1;
			this.form.isVirtualShop = 2;
			if (formData) {
				this.formData = JSON.parse(formData);
				this.formData.imageUrl = [this.formData.imageUrl]
				this.form.stime = this.formData.stime;
				this.form.etime = this.formData.etime;
				this.myBarcode = this.formData.barcode;
			}
		},
		costChange(val, formKey, key, rexp) {
			if (rexp.test(val)) {
				this[formKey][key] = val;
			}
		},
		commit(formFreeMail) {
			if (this.loading) return;
			this.$refs.form.validate((valid, object) => {
				if (valid) {
					const params = {
						...this.form,
						barcode: this.formData.barcode,
						saleScopeDTO: {
							...this.supplyInfo
						}
					}
					let jumpUrl = formFreeMail ? '/bulkPurchaseFreeMail' : '/productList'
					if (params.stime + (60 * 60 * 24 * 732 * 1000) < params.etime) {
						this.$message.error("上下架时间最长间隔时间为2年")
						return 
					}
					if (params.groupPrice == 0) {
						this.$message.error("活动价需为正数，最多两位小数")
						return 
					}
					if (params.snapPrice && params.snapPrice == 0) {
						this.$message.error("原价需为正数，最多两位小数")
						return
					}
					/* if (params.snapPrice && Number(params.snapPrice) < Number(params.groupPrice)) {
						
					} */
					if (!this.radio.personal) {
						params.personalLimitType = '0';
						params.personalLimitNum = '-1';
					} else if (!params.personalLimitType || !params.personalLimitNum) {
						this.$message.error('请填写单个药店限购类型及数量上限')
						return;
					}
					if (!this.radio.total) {
						params.totalLimitNum = '';
					} else if (!params.totalLimitNum){
						this.$message.error('请填写活动数量总上限')
						return;
					}
					if (this.radio.total && params.totalLimitNum < 1) {
						this.$message.error("活动数量总上限需填写大于0的正整数")
						return 
					}
					if (this.radio.personal && params.personalLimitNum < 1) {
						this.$message.error('单个药店采购上限需填写大于0的正整数');
						return
					}
					if (this.radio.personal && Number(params.personalLimitNum) < Number(params.groupNum)) {
						this.$message.error('起购数量需小于等于单个药店采购上限');
						return
					}
					if (this.radio.total && Number(params.totalLimitNum) < Number(params.groupNum)) {
						this.$message.error('起购数量需小于等于活动数量总上限');
						return
					}
					if (this.radio.total && this.radio.personal && Number(params.personalLimitNum) > Number(params.totalLimitNum)) {
						this.$message.error('单个药店采购上限需小于等于活动数量总上限');
						return
					}
					if (params.mediumPackageNum < 1) {
						this.$message.error("购买倍数仅支持输入正整数")
						return
					}
					if (params.groupNum < 1) {
						this.$message.error("起购数量仅支持输入正整数")
						return
					}
					if (params.groupNum % params.mediumPackageNum != 0) {
						this.$message.error(`起购数量需满足购买倍数整数倍, 购买倍数为：${params.mediumPackageNum}`)
						return
					}
					this.loading = true;
					const type = this.barcode ? 'edit' : 'add';
					if (type == 'edit') {
						params.actId = this.actId;
					}
					/* if (params.saleScopeDTO) */
					if (params.saleScopeDTO.isCopyBusArea == 1 && params.saleScopeDTO.isCopyControlUser == 1) {
						apiDetail({ barcode: this.formData.barcode }).then(res => {
							if (res.code == 0) {
								if (res.data.controlRosterType != 0) {
									this.$confirm('原品仅限黑白名单使用，请重新选择', '提示', {
										confirmButtonText: '确定',
									})
									this.loading = false;
									return
								} else {
									singleAdd(params, type).then(res => {
										if (res.success) {
											this.$message.success("发布成功");
											window.sessionStorage.removeItem("formData")
											this.go(jumpUrl, { refresh: '1' }, true);
										} else {
											this.$confirm(res.msg, '提示', {
												confirmButtonText: '确定',
											})
										}
									}).finally(() => {
										this.loading = false;
									})
								}
							}
						})
					} else {
						singleAdd(params, type).then(res => {
							if (res.success) {
								this.$message.success("发布成功");
								window.sessionStorage.removeItem("formData")
								this.go(jumpUrl, { refresh: '1' }, true);
							} else {
								this.$confirm(res.msg, '提示', {
									confirmButtonText: '确定',
								})
							}
						}).finally(() => {
							this.loading = false;
						})
					}
					/* apiDetail({ barcode: this.formData.barcode }).then(res => {
						if (res.code == 0) {
							if (res.data.controlRosterType)
						}
					}) */
					/* singleAdd(params, type).then(res => {
						if (res.success) {
							this.$message.success("发布成功");
							window.sessionStorage.removeItem("formData")
							this.go('/productList', { refresh: '1' }, true);
						} else {
							this.$confirm(res.msg, '提示', {
								confirmButtonText: '确定',
							})
						}
					}).finally(() => {
						this.loading = false;
					}) */
				} else {
					return false;
				}
				return false;
			})
		}
	}
}
</script>

<style lang="scss">
.typeSpan {
	display: inline-block;
	padding: 1px 3px;
	background: #0086d3;
	color:white;
	border-radius: 3px;
	border: solid 1px #1079b6;
}
.xProductInfo {
	display: flex;
	gap: 20px;
	margin:10px 0;
}
.xProductInfo > div:first-child {
	width: 150px;
	padding: 20px; 
	flex-grow: 0;
	flex-shrink: 0;
	border-right: solid 1px #d6d6d6;
}
.item-left {
	width: 100%;
	display: flex;
	align-items: flex-start;
	margin: 5px 0;
	> span:first-child,
	> button:first-child {
		width: 80px;
		flex-shrink: 0;
		text-align: end;
		color:#8d8d8d;
	}
	.productInfo {
		display: flex;
		gap: 20px;
		margin:10px 0;
	}
	.productInfo > div:first-child {
		width: 150px;
		padding: 20px;
		flex-grow: 0;
		flex-shrink: 0;
		border-right: solid 1px #d6d6d6;
	}
	.item-left {
		width: 100%;
		display: flex;
		align-items: flex-start;
		margin: 5px 0;
		> span:first-child,
		> button:first-child {
			width: 80px;
			flex-shrink: 0;
			text-align: end;
			color:#8d8d8d;
		}
		> span:nth-child(2) {
			flex-grow: 0;
			display: -webkit-box;//对象作为弹性伸缩盒子模型显示
			-webkit-box-orient: vertical;//设置或检索伸缩盒对象的子元素的排列方式
			-webkit-line-clamp: 2;//溢出省略的界限
			overflow: hidden;
			text-overflow: ellipsis;
			text-align: start !important;
		}
	}
	.el-message-box__btns button:first-child {
		display: none;
	}
}

</style>