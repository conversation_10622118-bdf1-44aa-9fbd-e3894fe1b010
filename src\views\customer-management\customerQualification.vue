<template>
  <div class="customerWrap">
    <div class="titleBox">
      <div class="customerTitle">
        客户资质
      </div>
      <el-button
        type="primary"
        size="small"
        @click="goBack"
      >
        返回
      </el-button>
    </div>
    <el-divider />
    <div class="customInfo">
      <div>
        客户名称：{{ customInfo.customerName }} <i
          v-if="customInfo.customerName"
          class="el-icon-document-copy"
          @click.prevent="util.copyUrl(customInfo.customerName)"
        />
      </div>
      <div class="deliveryAddr">
        客户注册地址：{{ customInfo.deliveryAddr }} <i
          v-if="customInfo.deliveryAddr"
          class="el-icon-document-copy"
          @click.prevent="util.copyUrl(customInfo.deliveryAddr)"
        />
      </div>
    </div>
    <el-button
      type="primary"
      size="small"
      @click="downLoad"
    >
      下载
    </el-button>
    <div class="contentInfo">
      <el-checkbox
        v-model="checkAll"
        class="checkAll"
        :indeterminate="isIndeterminate"
        @change="handleCheckAllChange"
      >
        全选
      </el-checkbox>
      <div style="margin: 15px 0;" />
      <el-checkbox-group
        v-if="qualificationList.length>0"
        v-model="checkedList"
        @change="handleCheckedChange"
      >
        <el-checkbox
          v-for="item in qualificationList || []"
          :key="item.url"
          :label="item.credentialTypeId"
          class="itemBox"
        >
          <div>
            {{ item.credentialName }}
            <span
              v-if="item.validUntil < new Date(new Date().toLocaleDateString()).getTime()"
              class="expired"
            >已过期</span>
            <div
              class="imgItem"
              @click.prevent
            >
              <div class="infoBox">
                <el-button
                  v-if="(orderStatus == 1 || orderStatus == 7) && shopConfig.isFbp !== 1"
                  type="primary"
                  size="small"
                  @click="handleSignError(item)"
                >
                  标记证件异常
                </el-button>
                <p v-if="item.credentialTypeId !== 52">
                  证件编号：
                  <span v-if="item.code">{{ item.code }}
                    <i
                      class="el-icon-document-copy"
                      @click.prevent="util.copyUrl(item.code)"
                    />
                  </span>
                  <span v-else>--</span>
                </p>
                <p v-if="item.credentialTypeId !== 54">
                  发证日期：
                  <span v-if="item.validityStart">{{ formatDate(item.validityStart, 'YMD') }}
                    <i
                      class="el-icon-document-copy"
                      @click.prevent="util.copyUrl(formatDate(item.validityStart, 'YMD'))"
                    />
                  </span>
                  <span v-else>--</span>
                </p>
                <p>
                  有效期至：
                  <!-- eslint-disable-next-line vue/no-parsing-error -->
                  <span v-if="item.validUntil">{{ (item.validUntil < new Date('2999-12-31').getTime()) ? formatDate(item.validUntil, 'YMD') : '长期有效' }}
                    <i
                      class="el-icon-document-copy"
                      @click.prevent="util.copyUrl((item.validUntil < new Date('2999-12-31').getTime()) ? formatDate(item.validUntil, 'YMD') : '长期有效')"
                    />
                  </span>
                  <span v-else>--</span>
                </p>
                <p v-if="item.credentialTypeId === 52 || item.credentialTypeId === 54">
                  姓名：
                  <span v-if="item.name">{{ item.name }}
                    <i
                      class="el-icon-document-copy"
                      @click.prevent="util.copyUrl(item.name)"
                    />
                  </span>
                  <span v-else>--</span>
                </p>
              </div>
              <div class="infoImgList">
                <img
                  v-for="(items,indexs) in item.enclosureList"
                  :key="indexs"
                  class="img"
                  :src="(items.url || '')"
                  @click="toShowBig(item.enclosureList,indexs)"
                >
              </div>
            </div>
          </div>
        </el-checkbox>
      </el-checkbox-group>
      <div
        v-else
        class="listnone"
      >
        暂无资质信息
      </div>
    </div>
    <el-image-viewer
      v-if="showViewer"
      :url-list="srcArr"
      :on-close="closeViewer"
      :on-switch="onSwitch"
    />
    <MarkError
      v-if="errShow"
      :dialog-visible="errShow"
      :current="currentItem"
      @update:handleChange="handleCloseError"
    />
  </div>
</template>
<script>
import { getQualification, downloadByUrl } from '@/api/customer-management/index';
import ElImageViewer from 'element-ui/packages/image/src/image-viewer';
import { mapState } from 'vuex';
import MarkError from './markError.vue';
import { downloadZip } from '@/utils/download'

export default {
  name: 'CustomerQualification',
  components: { ElImageViewer, MarkError },
  data() {
    return {
      currentItem: {},
      customInfo: {},
      isIndeterminate: true,
      checkAll: false,
      checkedList: [],
      qualificationList: [],
      merchantId: '',
      srcArr: [],
      showViewer: false,
      actImgIndex: 1,
      errShow: false,
      orderStatus: '',
    };
  },
  computed: { ...mapState('app', ['shopConfig']) },
  activated() {
    this.orderStatus = this.$route.query.orderStatus;
    if (this.merchantId !== this.$route.query.merchantId) {
      this.merchantId = this.$route.query.merchantId;
      this.getQualification();
    }
  },
  created() {
    this.orderStatus = this.$route.query.orderStatus;
    if (this.$route.query.merchantId) {
      this.merchantId = this.$route.query.merchantId;
    }
    this.getQualification();
  },
  methods: {
    handleCloseError() {
      this.errShow = false;
    },
    handleSignError(item) {
      this.errShow = true;
      this.currentItem = item;
    },
    toShowBig(imgList, indexs) {
      if ((imgList || []).length > 0) {
        this.showViewer = true;
        this.srcArr = (imgList || []).map(i => i.url);
        console.log(this.getPrivewImages(indexs), '[[[[');
        this.srcArr = this.getPrivewImages(indexs);
        // this.$nextTick(() => {
        //   const imageViewer = document.querySelector('.el-image-viewer__wrapper');
        //   const node = document.createElement('span');
        //   const textnode = document.createTextNode(`${indexs+1}/${this.srcArr.length}`);
        //   node.appendChild(textnode);
        //   node.className = 'el-image-viewer__btn text';
        //   node.id = 'imgLocation';
        //   imageViewer.appendChild(node);
        // });
      } else {
        this.$message.warning('当前资质无相应图片');
      }
    },
    getPrivewImages(index) {
      const tempImgList = [...this.srcArr];// 所有图片地址
      if (index == 0) return tempImgList;
      // 调整图片顺序，把当前图片放在第一位
      const start = tempImgList.splice(index);
      const remain = tempImgList.splice(0, index);
      return start.concat(remain);// 将当前图片调整成点击缩略图的那张图片
    },
    closeViewer() {
      this.showViewer = false;
      this.actImgIndex = 1;
    },
    onSwitch(index) {
      this.actImgIndex = index + 1;
      // const imageLocation = document.querySelector('#imgLocation');
      // imageLocation.innerHTML = `${this.actImgIndex}/${this.srcArr.length}`;
    },
    goBack() {
      // this.$router.push('/customerList');
      this.$router.go(-1);
    },
    getQualification() {
      getQualification({ merchantId: this.merchantId }).then((res) => {
        if (res.code === 0) {
          this.customInfo = res.result || {};
          this.qualificationList = res.result.list || [];
          this.srcArr = res.result.list.map(i => i.url);
        }
      });
    },
    handleCheckAllChange(val) {
      const arr = this.qualificationList.map(v => v.credentialTypeId);
      this.checkedList = val ? arr : [];
      this.isIndeterminate = false;
    },
    handleCheckedChange(value) {
      const checkedCount = (value || []).length;
      this.checkAll = checkedCount === this.qualificationList.length;
      this.isIndeterminate = checkedCount > 0 && checkedCount < this.qualificationList.length;
      this.checkedList = value || [];
    },
    // 下载
    downLoad() {
      if (this.checkedList.length === 0) {
        this.$message.error('请选择需要下载的资质');
        return;
      }
      const licenses = [];
      this.qualificationList.map((item) => {
        this.checkedList.map((item2) => {
          if (item.credentialTypeId === item2) {
            (item.enclosureList || []).map((i, index) => {
              console.log(i.enclosureName);
              const obj = {
                url: i.url,
                certName: `${i.enclosureName}`,
              };
              licenses.push(obj);
            });
          }
        });
      });
      downloadByUrl({
        realName: this.customInfo.customerName,
        licenses,
      }).then((res) => {
        if(res.code == 0) {
          downloadZip(res.result, this.customInfo.customerName)
        } else {
          this.$message.error(res.msg || '下载失败！请稍后再试！');
        }
        // const blob = new Blob([res]); // 接受文档流
        // if ('msSaveOrOpenBlob' in navigator) {
        //   // IE下的导出
        //   window.navigator.msSaveOrOpenBlob(blob, `${(this.customInfo || {}).customerName}.zip`); // 设置导出的文件名
        // } else {
        //   // 非ie下的导出
        //   const a = document.createElement('a');
        //   const url = window.URL.createObjectURL(blob);
        //   const filename = `${(this.customInfo || {}).customerName}.zip`; // 设置导出的文件名
        //   a.href = url;
        //   a.download = filename;
        //   document.body.appendChild(a);
        //   a.click();
        //   window.URL.revokeObjectURL(url);
        //   document.body.removeChild(a);
        // }
      }).catch(() => {});
    },
  },
};
</script>
<style lang="scss" scoped>
::v-deep  .el-form-item__label {
  padding: 0;
}
::v-deep  .el-checkbox-group {
  display: flex;
  flex-wrap: wrap;
}
::v-deep  .el-checkbox {
  display: flex;
  align-items: flex-start;
  padding: 10px;
}
.customerWrap {
  padding: 20px 24px;
  .titleBox {
    display: flex;
    justify-content: space-between;
  }
  .customerTitle {
    font-size: 20px;
    font-weight: 500;
  }
  .customInfo {
    font-size: 16px;
    color: #222222;
    .deliveryAddr {
      margin: 10px 0 24px;
    }
    i{
      cursor: pointer;
    }
  }
  .contentInfo {
    border: 1px solid #eeeeee;
    margin-top: 15px;
    .itemBox {
      width: 100%;
    }
    .checkAll {
      background: #f9f9f9;
    }
    .expired {
      border: 1px solid tomato;
      color: tomato;
      padding: 0 5px;
      border-radius: 3px;
    }
    .imgItem {
      display: flex;
      align-items: center;
      margin-top: 10px;
      .infoBox {
        margin-left: 10px;
        color: #606266 !important;
        width: 240px;
        p{
          display: flex;
          align-items: center;
          span{
            white-space: initial;
          }
        }
      }
    }
    .img {
      width: 200px;
      height: 160px;
    }
  }
}
.infoImgList{
  img{
    margin-left: 15px;
  }
}
.listnone{
  text-align: center;
  padding: 40px 0 60px;
}
::v-deep  .text {
    left: 50%;
    top: 30px;
    transform: translateX(-50%);
    width: 282px;
    height: 44px;
    padding: 0 23px;
    color: #fff;
  }
</style>
