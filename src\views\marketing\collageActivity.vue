<template>
  <div class="contentBox collageActivityTable">
    <div class="topTip-prompt">
      温馨提示：为了提升药店的拼团体验，提交拼团前请认真核对起拼数量、拼团价等内容，报名截止后不可新增商品。编辑截止时间后，拼团价、起拼数量等不可修改。
    </div>
    <el-row type="flex" align="middle">
      <span class="sign" />
      <div class="searchMsg">拼团活动</div>
    </el-row>
    <div class="topTip">
      <div v-permission="['marketing_group_apply']" v-if="isShowTheme">
        <div class="topTip-info">
          活动说明：您可参与下列拼团主题，已上线的拼团活动在活动时间内将显示在药帮忙的拼团会场、搜索页、店铺首页
        </div>
        <el-tabs v-model="tagData.activeName" @tab-click="handleClick">
          <el-tab-pane v-for="item in emnu" :label="item.label" :name="`${item.code}`"></el-tab-pane>
          <el-tab-pane label="全部活动" name="0"></el-tab-pane>
        </el-tabs>
        <div class="tag-list" :style="{height: tagData.blod ? 'max-content' : (tagData.showList.length > 3 ? '354px' : '171px'), overflow: tagData.blod ? '' : 'hidden'}">
          <div v-for="tag in tagData.showList" :key="tag.id" @click="tagClick(tag.frameReportId)">
            <div style="display: flex;justify-content: space-between;align-items: center;">
              <p style="font-size: 16px;font-weight: 600;">{{ tag.actName }}</p>
              <span :class="tag.frameStatus == 1 ? 'tag-class1' : 'tag-class2'" style="padding: 2px 3px;border-radius: 5px;font-size: 13px;">{{ tag.frameStatusName }}</span>
            </div>
            <div style="font-size: 14px;color: rgb(147 147 147);margin: 5px 0;">{{ timestampToFormat(tag.validStime) }} ~ {{ timestampToFormat(tag.validEtime) }}</div>
            <div style="margin-top: 25px;">
              <!-- <span>报名截止: {{ timestampToFormat(tag.segmentType == 2 ? tag.validEtime : tag.registrationEtime) }}</span> -->
              <span>报名截止: {{ timestampToFormat(tag.registrationEtime) }}</span>
              <span style="margin-left: 10px;color:red;">(剩{{ Math.floor((tag.registrationEtime - Date.now()) / 3600000) }}小时)</span>
            </div>
            <div style="margin-top: 10px;">
              <span>编辑截止: {{ timestampToFormat(tag.auditEtime ? tag.auditEtime : tag.validEtime) }}</span>
              <span style="margin-left: 10px;color:red;">(剩{{ Math.floor(((tag.auditEtime ? tag.auditEtime : tag.validEtime) - Date.now()) / 3600000) }}小时)</span>
            </div>
            <div style="margin-top: 10px;">
              <span v-if="tag.shopReportNum && tag.shopReportLimit">已提报数: {{ tag.shopReportNum }} / {{ tag.shopReportLimit }}</span>
              <span v-else-if="tag.shopReportNum">已提报数: {{ tag.shopReportNum }}</span>
              <span v-else-if="tag.shopReportLimit">已提报数: 0 / {{ tag.shopReportLimit }}</span>
              <span v-else>已提报数: 0</span>
            </div>
          </div>
        </div>
        <div v-if="tagData.showList.length > 6" style="padding: 10px 0;display: flex;justify-content: center;background-color: #f7f7f7;margin-top: 10px;border-radius: 5px;">
          <span style="color: #4183d5;cursor: pointer;user-select: none;" @click="tagData.blod = !tagData.blod;">{{ tagData.blod ? '收起' : '展开' }}所有活动</span>
        </div>
      </div>
      <div class="tips">
        <div class="div-info">
          <p>
            <span class="status-span">
              <i style="background: #dcdfe6" />价格过低
            </span>
            <el-tooltip effect="dark" placement="top">
              <template #content>
                进行中、未启动的活动，拼团价格低于近30天正常成交价过多，则为价格过低，请检查定价。
              </template>
              <i class="el-icon-warning-outline" />
            </el-tooltip>
          </p>
          <p class="refundCountBox">
            <span class="refundCount">{{
              editConfig.warningPriceTooLowCount
            }}</span>
            <el-button
              type="primary"
              size="mini"
              class="seeCount"
              @click="tipsSearch('warningPriceTooLowCount')"
            >
              {{ editConfig.searchWarningPriceTooLowCount ? '取消' : '筛选' }}
            </el-button>
          </p>
        </div>
        <div class="div-info">
          <p>
            <span class="status-span">
              <i style="background: #dcdfe6" />平台补贴
            </span>
            <el-tooltip effect="dark" placement="top">
              <template #content>进行中、未启动的活动，存在平台补贴</template>
              <i class="el-icon-warning-outline" />
            </el-tooltip>
          </p>
          <p class="refundCountBox">
            <span class="refundCount">{{ editConfig.platformSubsidyNum }}</span>
            <el-button
              type="primary"
              size="mini"
              class="seeCount"
              @click="tipsSearch('platformSubsidyNum')"
              >{{
                editConfig.searchPlatformSubsidy ? '取消' : '筛选'
              }}</el-button
            >
          </p>
        </div>
        <div class="div-info">
          <p>
            <span class="status-span">
              <i style="background: #dcdfe6" />销售范围异常
            </span>
            <el-tooltip effect="dark" placement="top">
              <template #content
                >待审核、进行中或未启动的，商圈覆盖区域或供货对象为空的活动；请调整商圈或供货对象。</template
              >
              <i class="el-icon-warning-outline" />
            </el-tooltip>
          </p>
          <p class="refundCountBox">
            <span class="refundCount">{{
              editConfig.warningRateStatusCount
            }}</span>
            <el-button
              type="primary"
              size="mini"
              class="seeCount"
              @click="tipsSearch('warningRateStatusCount')"
              >{{
                editConfig.searchWarningRateStatusCount ? '取消' : '筛选'
              }}</el-button
            >
          </p>
        </div>
        <div class="div-info">
          <p>
            <span class="status-span">
              <i style="background: #dcdfe6" />驳回待修改
            </span>
            <!-- <el-tooltip effect="dark" placement="top">
              <template #content>商品的价格为0，请检查是否设置错误</template>
              <i class="el-icon-warning-outline" />
            </el-tooltip> -->
          </p>
          <p class="refundCountBox">
            <span class="refundCount">{{ editConfig.rejectedStatusNum }}</span>
            <el-button
              type="primary"
              size="mini"
              class="seeCount"
              @click="tipsSearch('rejectedStatusNum')"
              >{{
                editConfig.searchRejectedStatus ? '取消' : '筛选'
              }}</el-button
            >
          </p>
        </div>
        <div class="div-info">
          <p>
            <span class="status-span">
              <i style="background: #dcdfe6" />起拼/限购建议
            </span>
            <el-tooltip effect="dark" placement="top">
              <template #content
                >进行中、未启动的活动，拼团起拼数量或限购数量过低/过高</template
              >
              <i class="el-icon-warning-outline" />
            </el-tooltip>
          </p>
          <p class="refundCountBox">
            <span class="refundCount">{{
              editConfig.warningStartPersonalQtyCount
            }}</span>
            <el-button
              style="margin-left: 10px"
              class="seeCount"
              type="primary"
              size="mini"
              @click="handleDownWarningStartPersonalQtyCount"
            >
              下载
            </el-button>
            <el-button
              type="primary"
              size="mini"
              class="seeCount"
              @click="tipsSearch('warningStartPersonalQtyCount')"
              >{{
                editConfig.searchWarningStartPersonalQtyCount ? '取消' : '筛选'
              }}</el-button
            >
          </p>
        </div>
      </div>
    </div>
    <el-row :gutter="24">
      <el-form ref="listQuery" :model="listQuery" size="small" class="searchMy">
        <el-row :span="24">
          <el-col :span="8">
            <el-form-item prop="idStr">
              <el-input
                v-model="listQuery.idStr"
                placeholder="请输入报名ID/活动ID"
              >
                <template slot="prepend">报名ID/活动Id</template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item prop="code">
              <el-input
                v-model="listQuery.code"
                placeholder="SKU编码/商品原编码/ERP编码"
              >
                <template slot="prepend">编码信息</template>
              </el-input>

            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item prop="productName">
              <el-input v-model="listQuery.productName" placeholder="请输入">
                <template slot="prepend">商品名称</template>
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <!-- <el-form-item label="状态" prop="status">
          <el-select v-model='listQuery.status' clearable placeholder='请选择'>
            <el-option
              v-for="option in tabStatusOptions"
              :key="option.value"
              :value="option.value"
              :label="option.label"
            />
          </el-select>
        </el-form-item> -->
        <el-row>
          <el-col :span="8">
            <el-form-item prop="reportTheme">
              <span class="search-title">拼团主题</span>
              <el-select
                v-model="listQuery.reportTheme"
                filterable
                clearable
                placeholder="请选择"
              >
                <el-option value="" label="全部" />
                <el-option
                  v-for="(option, index) in reportThemeLists"
                  :key="index"
                  :value="option"
                  :label="option"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item prop="isVirtualShop">
              <span class="search-title">虚拟供应商</span>
              <el-select
                v-model="listQuery.isVirtualShop"
                clearable
                placeholder="请选择"
              >
                <el-option value="" label="全部" />
                <el-option :value="1" label="是" />
                <el-option :value="2" label="否" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item prop="stepPriceStatus">
              <span class="search-title">是否阶梯价</span>
              <el-select
                v-model="listQuery.stepPriceStatus"
                clearable
                placeholder="请选择"
              >
                <el-option value="" label="全部" />
                <el-option :value="1" label="是" />
                <el-option :value="2" label="否" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item prop="isPlatformBidding">
              <span class="search-title">平台竞价商品</span>
              <el-select
                v-model="listQuery.isPlatformBidding"
                clearable
                placeholder="请选择"
              >
                <el-option value="" label="全部" />
                <el-option :value="1" label="是" />
                <el-option :value="0" label="否" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-show="showMore">
          <el-col :span="8">
            <el-form-item prop="isOrder">
              <span class="search-title">是否成团</span>
              <el-select
                v-model="listQuery.isOrder"
                clearable
                placeholder="请选择"
              >
                <el-option value="" label="全部" />
                <el-option :value="1" label="是" />
                <el-option :value="2" label="否" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item prop="isSyncStock">
              <span class="search-title">是否同步库存</span>
              <el-select
                v-model="listQuery.isSyncStock"
                clearable
                placeholder="请选择"
              >
                <el-option value="" label="全部" />
                <el-option :value="0" label="是" />
                <el-option :value="1" label="否" />
              </el-select>
            </el-form-item>
          </el-col>
          <!-- <el-col :span="8">
            <el-form-item prop="isPlatformSubsidy">
              <span class="search-title">是否平台补贴</span>
              <el-select v-model="listQuery.isPlatformSubsidy" clearable placeholder="请选择">
                <el-option value="" label="全部" />
                <el-option :value="0" label="是" />
                <el-option :value="1" label="否" />
              </el-select>
            </el-form-item>
          </el-col> -->
          <el-col :span="8">
            <el-form-item prop="isGiveSku">
              <span class="search-title">是否送赠品</span>
              <el-select
                v-model="listQuery.isGiveSku"
                clearable
                placeholder="请选择"
              >
                <el-option value="" label="全部" />
                <el-option :value="1" label="是" />
                <el-option :value="0" label="否" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item prop="activityTime">
              <span class="search-title">活动时间</span>
              <div style="display: table-cell; line-height: 24px">
                <el-date-picker
                  v-model="listQuery.activityTime"
                  type="datetimerange"
                  range-separator="-"
                  start-placeholder="开始时间"
                  end-placeholder="结束时间"
                  :default-time="['00:00:00', '23:59:59']"
                />
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item prop="signUpTime">
              <span class="search-title">创建时间</span>
              <div style="display: table-cell; line-height: 24px">
                <el-date-picker
                  v-model="listQuery.signUpTime"
                  type="datetimerange"
                  range-separator="-"
                  start-placeholder="开始时间"
                  end-placeholder="结束时间"
                  :default-time="['00:00:00', '23:59:59']"
                />
              </div>
            </el-form-item>
          </el-col>
        </el-row>
        <!-- <el-form-item label class="btn-box"> -->
        <div
          class="btn-box"
          style="
            justify-content: space-between;
            float: none;
            padding-bottom: 20px;
          "
        >
          <div
            style="
              flex: 1;
              flex-shrink: 0;
              display: flex;
              justify-content: right;
            "
          >
            <div class="showMoreBtns" style="float: none">
              <div
                v-if="!showMore"
                class="showMore"
                style="float: none"
                @click="showMore = true"
              >
                展开
                <i class="el-icon-arrow-down" />
              </div>
              <div
                v-else
                class="showMore"
                style="float: none"
                @click="showMore = false"
              >
                收起
                <i class="el-icon-arrow-up" />
              </div>
            </div>
            <el-button size="small" @click="reset">重置</el-button>
            <el-button
              type="primary"
              size="small"
              @click="getList(listQuery, true)"
              >查询</el-button
            >
          </div>
          <!-- </el-form-item> -->
        </div>
      </el-form>
    </el-row>
    
    <div class="operation-info">
      <el-button
        v-permission="['marketing_group_export']"
        size="small"
        type="primary"
        @click="exportData"
        :loading="exportLoading"
        >导出</el-button
      >
      <el-button
        v-permission="['marketing_group_batchUpdate']"
        size="small"
        style="margin-right: 10px;"
        @click="bulkChanges"
        >批量修改</el-button
      >
      <el-badge value="new">
        <el-button
          size="small"
          type="primary"
          @click="toAuction"
        >
        竞价商品管理
        </el-button>
      </el-badge>
    </div>
    <el-tabs v-model="tabActiveName" @tab-click="tabHandleClick">
      <el-tab-pane
        v-for="item in tabStatusOptions"
        :key="item.value"
        :label="`${item.label}(${item.count})`"
        :name="String(item.value)"
      />
    </el-tabs>
    <xyy-table
      :data="list"
      :list-query="listQuery"
      :col="col"
      :has-selection="true"
      @get-data="getList"
      @selectionCallback="setCheckedDatas"
    >
      <template slot="frameReportId" slot-scope="{ col }">
        <el-table-column :key="col.index" :label="col.name" :width="col.width">
          <template slot-scope="{ row }">
            <div>报名ID：{{ row.reportIdStr }}</div>
            <div>活动ID：{{ row.actIdStr }}</div>
          </template>
        </el-table-column>
      </template>
      <template slot="status" slot-scope="{ col }">
        <el-table-column :key="col.index" :label="col.name" :width="col.width">
          <template slot-scope="{ row }">
            <div>
              {{
                {
                  1: '待审核',
                  2: '审核不通过-可修改',
                  3: '未启动',
                  4: '活动进行中',
                  5: '已下线',
                  6: '已结束'
                }[row.status]
              }}
            </div>
            <div v-if="row.remark">驳回原因：{{ row.remark }}</div>
          </template>
        </el-table-column>
      </template>
      <template slot="commodityInformation" slot-scope="{ col }">
        <el-table-column :key="col.index" :label="col.name" :width="col.width">
          <template slot-scope="{ row }">
            <div>
              店铺名称:{{ row.shopName
              }}
              <span v-if="(row.source && row.source == 2) && row.status != 5 && ((row.status == 1 &&
                  row.registrationEtime &&
                  (row.registrationEtime > newTime ||
                    row.registrationEtime <= newTime)) ||
                row.status == 2 ||
                row.status == 3 ||
                row.status == 4)">
                
              </span>
            </div>
           
            <div  style="color:rgb(104,179,104) ;">虚拟供应商： {{ Number(row.isVirtualShop) === 1 ? '是' : '否' }}&nbsp;<i class="el-icon-edit-outline" style="color: #4184d5; font-size: 16px; cursor: pointer;" @click="updateData.actId = row.reportIdStr;updateData.type=2;updateData.visible=true;"/></div>
            <div>CSUID:{{ row.skuId }}</div>
            <div>商品编号:{{ row.barcode }}</div>
            <div>商品ERP编码:{{ row.productCode }}</div>
            <div style="color: red">商品名称:{{ row.productName }}</div>
            <div style="color: red">规格:{{ row.spec }}</div>
            <div style="color: red">生产厂家:{{ row.manufacturer }}</div>
            <!-- <div style="color: red">加权后药帮忙价格:{{ row.fob }}</div> -->
          </template>
        </el-table-column>
      </template>
      <template slot="groupPrice" slot-scope="{ col }">
        <el-table-column :key="col.index" :label="col.name" :width="col.width">
          <template slot-scope="{ row }">
            <div v-if="row.biddingSuccess" style="position: absolute;top: 0;left: 0;padding: 0 4px;border-bottom-right-radius: 5px;background-color: #00BE48;color: white;font-size: 12px;">
              恭喜中标
            </div>
            <div>
              <div v-if="row.stepPriceStatus === 1" style="color: red">
                <p style="display: inline-block">拼团价格:</p>
                {{
                  `${
                    row.activityReportGroupLevelPriceDTOList &&
                    row.activityReportGroupLevelPriceDTOList[
                      row.activityReportGroupLevelPriceDTOList.length - 1
                    ].discountPrice
                  } - ${row.groupPrice}`
                }}
                <span
                  style="cursor: pointer; color: #4184d5"
                  @click="checkGroupLevelPrice(row)"
                >
                  查看
                </span>
              </div>
              <div v-if="row.accessGroupPrice">
                拼团准入价: {{ row.accessGroupPrice }}
              </div>
              <div style="color: red">
                拼团价:{{ row.groupPrice }}
                <span v-if="(row.source && row.source == 2) && !(row.status == 1 && row.registrationEtime <= new Date().getTime()) && ((row.status == 1 && row.registrationEtime) || [2, 3, 4].includes(row.status)) && ((row.status == 1 &&
                  row.registrationEtime &&
                  (row.registrationEtime > newTime ||
                    row.registrationEtime <= newTime)) ||
                row.status == 2 ||
                row.status == 3 ||
                row.status == 4)">
                  <i class="el-icon-edit-outline" style="color: #4184d5; font-size: 16px; cursor: pointer;" @click="updateData.actId = row.reportIdStr;updateData.type=1;updateData.visible=true;"/>
                </span>
              </div>
              <div v-if="row.buyMostPrice">
                <span style="color: #333">热销拼团价格: {{ row.buyMostPrice }}</span>
              </div>
              <div style="color: red">
                起拼数量:{{ row.groupNum }}
              </div>
              <div v-if="row.buyMostStartQty">
                <span style="color: #333">热销起拼数量: {{ row.buyMostStartQty }}</span>
              </div>
              <div v-if="row.actId">
                <div
                  v-for="item in row.activityReportGroupAmountDtos"
                  :key="item.id"
                >
                  <div v-if="item.name != 'POP店铺'">{{ item.name }}补贴金额：{{ item.amount }}</div>
                </div>
              </div>
              <!-- <div v-else>POP店铺补贴金额:{{ row.shopSubsidy }}</div> -->
              <div
                v-if="
                  shopConfig.shopPatternCode !== 'ybm' &&
                  row.activityPriceStrategyList !== null
                "
              >
                <div
                  v-for="(item, index) in row.activityPriceStrategyList"
                  :key="index"
                >
                  <div>
                    <div>
                      指定时段补贴：
                      <el-tag
                        :type="
                          { 0: 'warning', 1: 'success', 2: 'danger' }[
                            item.status
                          ]
                        "
                        size="small"
                        >{{
                          { 0: '待生效', 1: '生效中', 2: '已失效' }[item.status]
                        }}</el-tag
                      >
                    </div>
                    <span>{{ formatDate(item.stime) }}</span
                    >至
                    <span>{{ formatDate(item.etime) }}</span>
                  </div>
                  <div>平台补贴金额:{{ item.subsidyPrice }}</div>
                  <div>补贴后拼团价格:{{ item.discountPrice }}</div>
                </div>
              </div>
              <div v-if="row.isGiveSku">
                <el-tag size="small">送赠品</el-tag>
              </div>
            </div>
            <div v-if="row.biddingSuccess" style="font-size: 12px;color: #d16817;text-align: start;">
                <div>恭喜您竞价成功。</div>
                <div>权益：当用户在搜索 该商品名称时，您的链接将会获得曝光加持</div>
              </div>
          </template>
        </el-table-column>
      </template>
      <template slot="activityReportGroupAmountDtos" slot-scope="{ col }">
        <el-table-column :key="col.index" :label="col.name" :width="col.width">
          <template slot-scope="{ row }">
            <div
              v-for="(option, index) in row.activityReportGroupAmountDtos"
              :key="index"
            >
              <div>{{ option.name }}:¥{{ option.amount }}</div>
            </div>
          </template>
        </el-table-column>
      </template>
      <template slot="isCopyCsuModel" slot-scope="{ col }">
        <el-table-column :key="col.index" :label="col.name" :width="col.width">
          <template slot-scope="{ row }">
            <!-- <span>是否复制原品销售范围：{{ (row.isCopyCsuModel == 2 || (row.isCopyCsuModel == 1 && row.isCopySaleArea == 2)) ? '否' : '是' }}</span> -->
            <div style="text-align: left;">
              供货信息配置方式：
              <span style="color: red">
              {{ row.isCopySaleArea == 1 ? '复用原品销售范围' : { 1: '人群', 2: '业务商圈、供货对象、黑白名单' }[(row.saleScopeDTO || {}).scopeType] }}
              </span>
              <span v-if="(row.source && row.source == 2) && ((row.status == 1 && row.registrationEtime) || [2, 3, 4].includes(row.status) && ((row.status == 1 &&
                  row.registrationEtime &&
                  (row.registrationEtime > newTime ||
                    row.registrationEtime <= newTime)) ||
                row.status == 2 ||
                row.status == 3 ||
                row.status == 4))">
                <i class="el-icon-edit-outline" style="color: #4184d5; font-size: 16px; cursor: pointer;" @click="updateData.actId = row.reportIdStr;updateData.type=3;updateData.visible=true;"/>
              </span>
            </div>
            <!-- <div v-if="row.isCopySaleArea == 2 && (row.saleScopeDTO || {}).scopeType == 1 && (row.tagDef || []).length">
              人群定义: {{ (row.tagDef || []).join() }}
            </div> -->
            <div style="text-align: left;">
              <div v-if="(row.saleScopeDTO || {}).busAreaName" >
                业务商圈:{{ (row.saleScopeDTO || {}).busAreaName }}
                <i
                  class="el-icon-view"
                  style="color: #4183d5; font-size: 16px"
                  @click="
                    viewBusiness({ id: (row.saleScopeDTO || {}).busAreaId })
                  "
                ></i>
              </div>
              <div
                v-if="
                  ((row.saleScopeDTO || {}).controlUserTypeList || []).length
                "
                style="text-align: left;"
              >
                <el-tooltip class="item" effect="dark" placement="top">
                  <template slot="content">
                      供货对象:{{
                        (
                          (row.saleScopeDTO || {}).controlUserTypeList || []
                        ).join()
                      }}
                  </template>
                  <span class="tooltipEllipsis"
                    > 供货对象:{{
                      (
                        (row.saleScopeDTO || {}).controlUserTypeList || []
                      ).join()
                    }}</span
                  >         
                </el-tooltip>
              </div>
              <div v-if="(row.saleScopeDTO || {}).controlGroupName" style="text-align: left;">
                黑白名单:{{
                  { 1: '黑名单', 2: '白名单' }[
                    (row.saleScopeDTO || {}).controlRosterType
                  ]
                }}-{{ (row.saleScopeDTO || {}).controlGroupName }}
                <i
                  class="el-icon-view"
                  style="color: #4183d5; font-size: 16px"
                  @click="seeNameList((row.saleScopeDTO || {}).controlGroupId)"
                ></i>
              </div>
            </div>
            <div v-if="row.isPlatformCustomer" style="text-align: left;">
                指定人群<span style="color:red;">：平台指定人群</span>
            </div>
            <div
              v-if="
                (row.saleScopeDTO || {}).scopeType == 1 || (row.saleScopeDTO || {}).customerGroupId
              "
              style="text-align: left;"
            >
              人群ID:
              <span
                >{{ row.customerGroupId
                }}<i
                  class="el-icon-view"
                  style="color: #4183d5; font-size: 16px;"
                  @click="addCroed(row)"
                ></i
              ></span>
            </div>
            <div
              v-if="
                (row.saleScopeDTO || {}).scopeType == 1 || (row.saleScopeDTO || {}).customerGroupId
              "
              style="text-align: left;"
            >
              人群名称:{{ row.customerGroupName }}
            </div>
            <!-- <div
              v-if="row.baseCustomerGroupId"
              style="padding: 5px; background: yellow; margin-left: 5px"
            >
              平台限制销售范围
            </div> -->
            <!-- <div v-if="row.customerGroupVO">
              <div
                v-for="(item, index) in row.customerGroupVO
                  .contentBundleDescriptions"
                :key="index"
              >
                <div v-for="(one, index1) in item" :key="index1">
                  <el-tooltip>
                    <template slot="content">
                      <div>{{ one }}</div>
                    </template>
                    <p
                      style="
                        overflow: hidden;
                        white-space: nowrap;
                        text-overflow: ellipsis;
                      "
                    >
                      {{ one }}
                    </p>
                  </el-tooltip>
                </div>
              </div>
              <p v-if="row.customerGroupVO.specifyUserDescription">
                {{ row.customerGroupVO.specifyUserDescription }}
              </p>
            </div> -->
          </template>
        </el-table-column>
      </template>
      <template slot="activityInventory" slot-scope="{ col }">
        <el-table-column :key="col.index" :label="col.name" :width="col.width">
          <template slot-scope="{row}">
            <div>商品库存:{{ row.totalStock }}</div>
            <div>库存是否同步ERP: {{ row.stockSyncErp == 1 ? '是' : '否' }}</div>
            <div style="color: red;">
              活动总限购数量:{{ row.totalLimitNum }}
              <span>
                <i v-if="(row.source && row.source == 2) && ((row.status == 1 && row.registrationEtime) || [2, 3, 4].includes(row.status)) && ((row.status == 1 &&
                  row.registrationEtime &&
                  (row.registrationEtime > newTime ||
                    row.registrationEtime <= newTime)) ||
                row.status == 2 ||
                row.status == 3 ||
                row.status == 4)" class="el-icon-edit-outline" style="color: #4184d5; font-size: 16px; cursor: pointer;" @click="updateData.actId = row.reportIdStr;updateData.type=4;updateData.visible=true;"/>
              </span>
            </div>
			      <div>
              单店限购类型：{{ row.personalLimitType ? personalLimitTypeList[row.personalLimitType] : personalLimitTypeList[0] }}
            </div>
            <div style="color: red;">单客户限购数量:{{ row.personalLimitNum }}</div>
            <div>剩余活动库存:{{ row.surplusStockNum }}</div>
            <div>在途库存:{{ row.onTheWayStock }}</div>
          </template>
        </el-table-column>
      </template>
      <template slot="actTime" slot-scope="{ col }">
        <el-table-column :key="col.index" :label="col.name" width="400">
          <template slot-scope="{ row }">
            <div>
              <p style="color: red">活动时间：</p>
              <div style="color: red" v-for="(item,index) in validFormat(row)" v-if="index==0||index==1||row.isShow">{{item}} 
                <span class="ex" v-if="index==1&&validFormat(row).length>2" @click="ex(row)">&nbsp;{{row.isShow?'收起':'展开'}}
                </span>
              </div>
              <div>
                <p>创建时间：</p>
                {{ registrationFormat(row) }}
              </div>
            </div>
          </template>
        </el-table-column>
      </template>
      <template slot="agreementId" slot-scope="{ col }">
        <el-table-column :key="col.index" :label="col.name" :width="col.width">
          <template slot-scope="{ row }">
            <div>协议ID:{{ row.agreementId }}</div>
          </template>
        </el-table-column>
      </template>
      <template slot="operation" slot-scope="{ col }">
        <el-table-column
          :key="col.index"
          :prop="col.index"
          :label="col.name"
          :width="col.width"
          fixed="right"
        >
          <template
            slot-scope="scope"
            v-if="scope.row.source && scope.row.source == 2"
          >
            <div>
              <el-button
                size="small"
                type="text"
                @click="viewOperationLog(scope.row)"
                >操作日志</el-button
              >
            </div>
            <div
              v-permission="['marketing_group_edit']"
              v-if="
                (scope.row.status == 1 &&
                  scope.row.registrationEtime &&
                  (scope.row.registrationEtime > newTime ||
                    scope.row.registrationEtime <= newTime)) ||
                scope.row.status == 2 ||
                scope.row.status == 3 ||
                scope.row.status == 4
              "
            >
              <el-button size="small" type="text" @click="handerEdit(scope.row)"
                >编辑</el-button
              >
            </div>

            <div
              v-permission="['marketing_group_down']"
              v-if="scope.row.status != 5 && scope.row.status != 6"
            >
              <el-button
                size="small"
                type="text"
                @click="checkOffline(scope.row)"
                >下线</el-button
              >
            </div>
            <div
              v-permission="['marketing_group_give']"
              v-if="
                showGiveBtn &&
                scope.row.status != 5 &&
                scope.row.status != 6 &&
                scope.row.actId
              "
            >
              <el-button size="small" type="text" @click="giveSku(scope.row)"
                >单品送赠品</el-button
              >
            </div>
          </template>
        </el-table-column>
      </template>
      <template slot="activitySaleDataSummaryInfo" slot-scope="{ col }">
        <el-table-column
          :key="col.index"
          :label="col.name"
          :width="col.width"
          :render-header="renderHeader"
        >
          <template slot-scope="{ row }">
            <div>
              <p>
                <span style="color: red">{{
                  row.isOrder === 1 ? '已成团' : '未成团'
                }}</span>
                <span
                  v-if="row.isOrder === 1"
                  style="
                    cursor: pointer;
                    font-size: 16px;
                    vertical-align: middle;
                    margin-left: 5px;
                  "
                  @click="handleSeeActivitySaleDataSummaryInfo(row)"
                >
                  <i class="el-icon-view" />
                </span>
              </p>
              <div v-if="row.isOrder === 1 && row.activitySaleDataSummaryInfo">
                <p>
                  <el-tooltip
                    class="item"
                    content="有效订单中包含活动ID，对应客户去重"
                    placement="top"
                  >
                    <i class="el-icon-warning-outline" />
                  </el-tooltip>
                  采购店数：{{
                    row.activitySaleDataSummaryInfo.purchaseMerchantNum
                  }}
                </p>
                <p>
                  <el-tooltip
                    class="item"
                    content="有效订单中包含特价活动/拼团活动ID，对应订单计数"
                    placement="top"
                  >
                    <i class="el-icon-warning-outline" />
                  </el-tooltip>
                  采购订单数：{{
                    row.activitySaleDataSummaryInfo.purchaseOrderNum
                  }}
                </p>
                <p>
                  <el-tooltip
                    class="item"
                    content="有效订单、商品行中包含活动ID，取包含对应活动ID的各个商品行【应发货数量=商品数量-已退数量】之和"
                    placement="top"
                  >
                    <i class="el-icon-warning-outline" />
                  </el-tooltip>
                  采购数量：{{
                    row.activitySaleDataSummaryInfo.purchaseProductNum
                  }}
                </p>
                <p>
                  <el-tooltip
                    class="item"
                    content="有效订单、商品行中包含活动ID，取【实付金额*应发货数量/商品数量=实付金额*（商品数量-已退数量）/商品数量】之和"
                    placement="top"
                  >
                    <i class="el-icon-warning-outline" />
                  </el-tooltip>
                  采购金额：{{ row.activitySaleDataSummaryInfo.purchaseAmount }}
                </p>
                <span
                  style="color: #4183d5; cursor: pointer"
                  @click="handleGoGroupSalesData(row)"
                  >详情</span
                >
              </div>
            </div>
          </template>
        </el-table-column>
      </template>
    </xyy-table>
    <pintuanPrice v-model:data="updateData" @getList="getList(listQuery, true)"></pintuanPrice>
    <batch-edit
      v-if="bulkChangesDialog"
      @cancelModal="cancelModal"
      @refresh="getList(listQuery, true)"
    />
    <!-- <crowd-selector-dialog
      :crowd-dialog-vis="crowdDialogVis"
      :selected="innerSelected"
      @cancelModal="cancelModal"
      @selectChange="selectChange"
    /> -->
    <!-- 查看客户信息 -->
    <CustomerInfoLog
      v-if="crowdDialogVis"
      :market-customer-group-id="innerSelected"
      @cancelModal="cancelModal"
    />
    <listOperationLog
      v-if="listOperationLogVisible"
      :list-operation-log-visible.sync="listOperationLogVisible"
      :modify-config="listOperationLogConfig"
    />
    <!-- 查看业务商圈 -->
    <business-circle-detail-dialog
      :row="selectViewRow"
      v-if="viewBusinessDialog"
      v-model="viewBusinessDialog"
    ></business-circle-detail-dialog>
    <!-- 查看黑白名单 -->
    <ListOfControlGroups
      v-if="showNameList"
      :control-group-id="controlGroupId"
      @cancelDialog="cancelDialog"
    />
    <el-dialog
      title="查看阶梯价"
      :visible.sync="groupLevelPriceVis"
      width="750"
    >
      <p>
        起拼数量：{{ currentGroupLevelPriceItem.groupNum }}
        <span style="margin-left: 10px">
          拼团价格：{{ currentGroupLevelPriceItem.groupPrice }}
        </span>
      </p>
      <el-table
        :data="currentGroupLevelPriceItem.activityReportGroupLevelPriceDTOList"
        style="margin-top: 10px"
        border
        :header-cell-style="{ background: '#eeeeee', color: '#666666' }"
      >
        <el-table-column
          prop="startQty"
          align="center"
          label="促销起始数量"
          width="120"
        />
        <el-table-column
          prop="discountPrice"
          align="center"
          label="拼团价格"
          width="120"
        />
        <el-table-column
          prop="initiatorDTOList"
          align="center"
          label="补贴情况"
        >
          <template slot-scope="{ row }">
            <p v-for="(item, index) in row.initiatorDTOList" :key="index">
              {{ item.name }}：{{ item.amount }}
            </p>
          </template>
        </el-table-column>
      </el-table>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="groupLevelPriceVis = false">
          关闭
        </el-button>
      </span>
    </el-dialog>
    <export-tip
      :change-export="changeExport"
      @handleExoprClose="handleExoprClose"
      @handleChangeExport="handleChangeExport"
    />
     <el-dialog title="提示" :visible="actOffline.visible" @close="actOffline.visible = false">
      <div>
        <span>请确定是否继续下架</span>
        <div>
          <p>
              如遇<span style="color:red;">厂家控销、查价</span>，可将活动设置为“<span style="color:red;">虚拟供应商</span>”，
              即拼团活动支付成功后才显示真实供应商信息
              如仍被查价，还可将活动供货信息配置为<span style="color:red;">含“VIP活跃用户（月）”标签的人群</span>，即活动仅对平台前20%高频采购用户可见
          </p>
          <span style="color: #4183d5;cursor: pointer;" @click="actOffline.visible = false;handerEdit(actOffline.actRow)">前往修改》</span>
        </div>
      </div>
      <span slot="footer">
        <el-button size="medium" @click="actOffline.visible = false">取消</el-button>
        <el-button
          size="medium"
          style="margin-left: 20px"
          type="primary"
          @click="handerOffline(actOffline.actRow)"
          >确认下架</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import exportTip from '@/views/other/components/exportTip';
import {
  apiGetFrameActBaseForReport,
  apiApplyList,
  apiOffLine,
  apiSelectReportGroupCount,
  getStatusAndCount,
  isCanCreateFullGive,
  exportWarningLimitData,
  getBaseNameList,
  getSummaryInfo,
  getDefaultDataFromApollo,
  getApplyExport,
  apiCheckOffLine
} from '@/api/market/collageActivity'
import CustomerInfoLog from '@/components/customer/customerInfoLog.vue'
import { actionTracking } from '@/track/eventTracking'
import crowdSelectorDialog from './components/crowdSelectorDialog'
import batchEdit from './components/batchEdit'
import listOperationLog from './components/listOperationLog'
import BusinessCircleDetailDialog from '../business-circle/components/businessCircleDetailDialog.vue'
import ListOfControlGroups from '../product/components/listOfControlGroups'
import { mapState } from 'vuex'
import pintuanPrice from '../pintuanDataUpdate/pintuanPrice.vue'
export default {
  components: {
    crowdSelectorDialog,
    batchEdit,
    listOperationLog,
    BusinessCircleDetailDialog,
    ListOfControlGroups,
    CustomerInfoLog,
    exportTip,
    pintuanPrice
  },
  filters: {},
  props: {},
  data() {
    return {
      updateData: {
        actId: '',
        type: '',  //1:拼团价格，2：设置虚拟供应商，3：修改供货信息，4：修改活动库存
        visible: false,
      },
      emnu: [],
      changeExport:false,
	  personalLimitTypeList: ["不限制", "活动期间限购", "每天（每天00:00至24:00）", "单笔订单限购", "每周（周一00:00至周日24:00）", "每月（每月1号00:00至每月最后一天24:00）"],
      groupLevelPriceVis: false,
      currentGroupLevelPriceItem: {},
      reportThemeLists: [],
      list: [],
      listQuery: {
        stepPriceStatus: '',
        code: '',
        idStr: '',
        pageSize: 10,
        page: 1,
        total: 0,
        reportId: '',
        barcode: '',
        productCode: '',
        productName: '',
        status: '',
        reportTheme: '',
        isPlatformSubsidy: '',
        isAgreement: '',
        isSoldOut: '',
        isSyncStock: '',
        activityTime: [],
        signUpTime: [],
        isUpdateAccessPrice: '',
        warningRateStatus: 2,
        isVirtualShop: '',
        skuId: '',
        actId: '',
        isGiveSku: '',
        warningPriceIsTooLow: '',
        warningStartPersonalQty: '',
        isOrder: '',
        isPlatformBidding: ''
      },
      showMore: false,
      crowdDialogVis: false,
      selectIds: '',
      innerSelected: '',
      crowdDialog: false,
      bulkChangesDialog: false,
      selections: [],
      failed: 0,
      isShowTheme: false,
      curTagList: [],
      tagList: [],
      viewBusinessDialog: false, // 查看业务商圈
      showNameList: false, // 查看黑白名单
      selectViewRow: {},
      controlGroupId: '',
      editConfig: {
        platformSubsidyNum: 0,
        searchPlatformSubsidy: false,
        // agreementNum: 0,
        searchAgreement: false,
        rejectedStatusNum: 0,
        searchRejectedStatus: false,
        updateGroupBuyingAcceptPrice: 0,
        searchUpdateGroupBuyingAcceptPrice: false,
        warningRateStatusCount: 0,
        searchWarningRateStatusCount: false,
        warningPriceTooLowCount: 0,
        searchWarningPriceTooLowCount: false,
        warningStartPersonalQtyCount: 0,
        searchWarningStartPersonalQtyCount: false
      },
      col: [
        {
          index: 'frameReportId',
          name: '报名ID/活动ID',
          width: 100,
          slot: true
        },
        // {
        //   index: 'actId',
        //   name: '活动ID',
        //   width: 100,
        //   // slot: true,
        // },
        {
          index: 'status',
          name: '活动状态',
          width: 100,
          slot: true
        },
        {
          index: 'commodityInformation',
          name: '商品信息',
          width: 220,
          slot: true
        },
        {
          index: 'groupPrice',
          name: '拼团价格',
          width: 150,
          slot: true
        },
        {
          index: 'isCopyCsuModel',
          name: '供货信息',
          width: 200,
          slot: true
        },
        {
          index: 'activityInventory',
          name: '活动库存',
          width: 150,
          slot: true
        },
        {
          index: 'reportTheme',
          name: '拼团主题',
          width: 100
        },
        {
          index: 'activitySaleDataSummaryInfo',
          name: '销售数据',
          width: 160,
          slot: true
        },
        {
          index: 'actTime',
          name: '活动时间',
          slot: true
        },
        // {
        //   index: 'createTime',
        //   name: '创建时间',
        //   width: 180,
        //   formatter: this.registrationFormat,
        // },
        {
          index: 'operation',
          name: '操作',
          width: 80,
          slot: true
        }
      ],
      newTime: new Date().getTime(),
      weekObj: {
        1: '周一',
        2: '周二',
        3: '周三',
        4: '周四',
        5: '周五',
        6: '周六',
        7: '周日'
      },
      listOperationLogVisible: false,
      listOperationLogConfig: {},
      tabActiveName: '',
      tabStatusOptions: [
        {
          label: '全部',
          value: '',
          count: 0,
          name: 'totalCount'
        },
        {
          label: '待审核',
          value: 1,
          count: 0,
          name: 'waitCount'
        },
        {
          label: '审核不通过-可修改',
          value: 2,
          count: 0,
          name: 'rejectedCount'
        },
        {
          label: '未启动',
          value: 3,
          count: 0,
          name: 'unStartCount'
        },
        {
          label: '进行中',
          value: 4,
          count: 0,
          name: 'startingCount'
        },
        {
          label: '已结束/已下线',
          value: 7,
          count: 0,
          name: 'stopOrOffLineCount'
        }
      ],
      exportLoading: false,
      showGiveBtn: false,
      // customerInfo: {},
      subCount: 0,
      tagData: {
        showList: [],   //展示的活动列表
        blod: false,    //展开或折叠
        activeName: '0',
      },
      actOffline:{
        visible: false, // 信息弹框是否展示
        actRow: {}
      }
    }
  },
  computed: {
    ...mapState('app', ['shopConfig'])
  },
  watch: {},
  mounted() {
  },
  activated() {
    let showEditFlag = this.$route.query.showEdit || false;
    if (showEditFlag) {
      this.bulkChangesDialog = true;
    }
    if (this.$route.query.refresh) {
      this.getList(this.listQuery, true)
    }
    this.getDefaultDataFromApollo()
    if (this.$route.query.rejectNum) {
      setTimeout(() => {
        this.tipsSearch('rejectedStatusNum')
      }, 500)
    }
    if (this.$route.query.priceTooLowPrice) {
      setTimeout(() => {
        this.tipsSearch('warningPriceTooLowCount')
      }, 500)
    }
  },
  created() {
    this.GetFrameActBaseForReport()
    this.selectReportGroupCount()
    this.getList(this.listQuery, true)
    this.getButtons()
    this.getBaseNameList()
  },
  methods: {
    handleClick(e) {
      this.tagData.showList = this.tagList.filter(item => {
        if (e.name == 0) return true;
        if (e.name == 'x') return item.recommendFlag == 1;
        return item.activityClass == e.name
      })
    },
    timestampToFormat(timestamp) {
        // 如果时间戳是秒级，则需要转换为毫秒级
        if (timestamp < 999999999999) {
            timestamp *= 1000;
        }

        let date = new Date(timestamp);
        let year = date.getFullYear();
        let month = ("0" + (date.getMonth() + 1)).slice(-2); // 月份从0开始，所以加1，并补零
        let day = ("0" + date.getDate()).slice(-2); // 补零
        let hour = ("0" + date.getHours()).slice(-2); // 补零
        let minute = ("0" + date.getMinutes()).slice(-2); // 补零
        let second = ("0" + date.getSeconds()).slice(-2); // 补零

        return `${year}-${month}-${day} ${hour}:${minute}:${second}`;
    },
    handleExoprClose() {
      this.changeExport = false;
    },
    handleChangeExport(info) {
      this.changeExport = false;
      if (info === 'go') {
        const path = '/downloadList';
        window.openTab(path);
      }
    },
    handleSeeActivitySaleDataSummaryInfo(row) {
      getSummaryInfo({
        activityType: row.activityType,
        marketingIdStr: row.actIdStr
      }).then((res) => {
        if (res.success) {
          const { data } = res
          row.activitySaleDataSummaryInfo = data.summaryInfo || null
          this.$set(row, row)
        }
      })
    },
    renderHeader(h, { column }) {
      return h('div', [
        h('span', column.label),
        h(
          'el-tooltip',
          {
            props: {
              content:
                '销售数据剔除未支付、已取消、已退款且部分退中活动商品应发货数量等于0的订单数据，仅统计已支付的有效订单。可至详情页查询未支付、已取消订单',
              placement: 'right'
            }
          },
          [h('i', { class: 'el-icon-warning-outline' })]
        )
      ])
    },
    handleGoGroupSalesData(row) {
      // this.$router.push({ path: '/groupSalesData', query: { activityType: row.activityType, marketingIdStr: row.actIdStr } });
      window.openTab('/groupSalesData', {
        activityType: row.activityType,
        marketingIdStr: row.actIdStr
      })
    },
    getBaseNameList() {
      getBaseNameList().then((res) => {
        if (res.success) {
          this.reportThemeLists = res.data.result
        }
      })
    },
    checkGroupLevelPrice(row) {
      this.currentGroupLevelPriceItem = row
      this.groupLevelPriceVis = true
    },
    handleDownWarningStartPersonalQtyCount() {
      actionTracking('group_management_top_quick_search', {
        filter_item: 'suggestion_download'
      })
      exportWarningLimitData().then((res) => {
        this.util.exportExcel(res, '起拼/限购建议.xls')
      })
    },
    getButtons() {
      isCanCreateFullGive({}).then((res) => {
        if (res.code === 1000) {
          this.showGiveBtn = true
        } else {
          this.showGiveBtn = false
        }
      })
    },
    async getStatusAndCount(params) {
      const data = { ...params }
      delete data.status
      const res = await getStatusAndCount(data)
      if (res && res.code === 1000) {
        Object.keys(res.data.reportStatusCountDto).forEach((key) => {
          this.tabStatusOptions.map((item) => {
            if (item.name === key) {
              this.$set(item, 'count', res.data.reportStatusCountDto[key])
            }
            return item
          })
        })
      } else {
        this.$message.error(res.msg || '查询不同状态的报名条数出错')
      }
    },
    getShowTime(aTime) {
      const time = new Date(aTime + 8 * 3600 * 1000)
        .toJSON()
        .substr(0, 19)
        .replace('T', ' ')
      return time
    },
    validFormat(row) {
      const start = this.getShowTime(row.actStartTime)
      const end = this.getShowTime(row.actEndTime)
      if (Number(row.segmentType) === 1) {
        return [start && end ? `${start} 至 ${end}` : '-']
      }
      if (
        Number(row.segmentType) === 2 &&
        Array.isArray(row.multiSegmentDTOList)
      ) {
        const list = []
        list.push(
          `${this.formatDate(row.actStartTime, 'YMD')} 至 ${this.formatDate(
            row.actEndTime,
            'YMD'
          )}`
        )
        row.multiSegmentDTOList.forEach((obj) => {
          list.push(
            `${this.weekObj[obj.stime.cycleNum]} ${
              Number(obj.stime.hour) < 10
                ? '0' + obj.stime.hour
                : obj.stime.hour
            }:${
              Number(obj.stime.minute) < 10
                ? '0' + obj.stime.minute
                : obj.stime.minute
            } 至 ${this.weekObj[obj.etime.cycleNum]} ${
              Number(obj.etime.hour) < 10
                ? '0' + obj.etime.hour
                : obj.etime.hour
            }:${
              Number(obj.etime.minute) < 10
                ? '0' + obj.etime.minute
                : obj.etime.minute
            }`
          )
        })
        console.log(list)
        return list
      }
    },
    registrationFormat(row) {
      const start = this.getShowTime(row.createTime)
      return start || '-'
    },
    // 跳转活动报名页面
    tagClick(id) {
      const path = '/groupActivityTheme'
      const obj = {
        frameReportId: id
      }
      window.openTab(path, obj)
      // this.$router.push({ path: '/groupActivityTheme', query: { frameReportId: id } });
    },
    reset() {
      this.$refs.listQuery.resetFields()
      this.getList(this.listQuery, true)
    },
    ex(row){
      this.$set(row,'isShow',!row.isShow)
      console.log(row)
    },
    exportData() {
      if (!this.list.length) {
        this.$message.warning('暂无数据')
        return
      }
      // this.exportLoading = true;
      const {
        actId,
        barcode,
        productName,
        status,
        reportTheme,
        isPlatformSubsidy,
        isAgreement,
        isSoldOut,
        isSyncStock,
        activityTime,
        signUpTime,
        isUpdateAccessPrice,
        warningRateStatus,
        isVirtualShop,
        isGiveSku,
        reportId,
        skuId,
        idStr,
        code,
        stepPriceStatus,
        isOrder,
        warningPriceIsTooLow,
        warningStartPersonalQty
      } = this.listQuery
      const actStartTime =
        activityTime && activityTime.length ? activityTime[0].getTime() : ''
      const actEndTime =
        activityTime && activityTime.length ? activityTime[1].getTime() : ''
      const startCreateTime =
        signUpTime && signUpTime.length ? signUpTime[0].getTime() : ''
      const endCreateTime =
        signUpTime && signUpTime.length ? signUpTime[1].getTime() : ''
      const params = {
        actId,
        barcode,
        productName,
        status,
        reportTheme,
        isPlatformSubsidy,
        isAgreement,
        isSoldOut,
        actStartTime,
        actEndTime,
        startCreateTime,
        endCreateTime,
        isSyncStock,
        isUpdateAccessPrice,
        warningRateStatus,
        isVirtualShop,
        isGiveSku,
        reportId,
        skuId,
        idStr,
        code,
        stepPriceStatus,
        isOrder,
        warningPriceIsTooLow,
        warningStartPersonalQty
      }
      this.exportLoading = true;
      getApplyExport(params).then((res) => {
        this.exportLoading = false;
        if (res.code !== 0) {
          this.$message.error(res.message)
        } else {
          this.changeExport = true;
        }
      })
      // const url = `${process.env.VUE_APP_BASE_API}/report/groupbuying/apply/export${this.getParams(params)}`;
      // window.open(url, '_blank');
    },
    /**
     * 获取get请求参数
     */
    getParams(params) {
      let queryStr = '?'
      Object.keys(params).forEach((key) => {
        queryStr += `${key}=${params[key]}&`
      })
      queryStr = queryStr.substr(0, queryStr.length - 1)
      return queryStr
    },
    downLoadXls(data, filename) {
      if (typeof window.chrome !== 'undefined') {
        // Chrome version
        const blob = new Blob([data], { type: 'application/vnd.ms-excel' })
        const link = document.createElement('a')
        link.href = window.URL.createObjectURL(blob)
        link.download = filename
        link.click()
      } else if (typeof window.navigator.msSaveBlob !== 'undefined') {
        // IE version
        const blob = new Blob([data], { type: 'application/force-download' })
        window.navigator.msSaveBlob(blob, filename)
      } else {
        // Firefox version
        const file = new File([data], filename, {
          type: 'application/force-download'
        })
        window.open(URL.createObjectURL(file))
      }
    },
    GetFrameActBaseForReport() {
      apiGetFrameActBaseForReport().then((res) => {
        if (res.status === 'success') {
          if (
            res.data &&
            res.data.activityReportBaseResDTOS &&
            res.data.activityReportBaseResDTOS.length > 0
          ) {
            this.isShowTheme = true
            this.tagList = res.data.activityReportBaseResDTOS
            this.tagData.showList = this.tagList;
            this.emnu = [];
            if (this.tagList.some(item => item.recommendFlag == 1)) {
              this.emnu.push({
                label: '平台推荐',
                code: 'x',
                sort: 0
              })
            }
            this.tagList.forEach(item => {
              if (!item.activityClass) return
              if (!this.emnu.some(val => (val.code == item.activityClass))) {
                this.emnu.push({
                  label: item.activityClassText,
                  code: item.activityClass,
                  sort: item.activityClassSort
                })
              }
            })
            this.emnu = this.emnu.sort((a, b) => {
              return a.sort - b.sort
            })
          } else {
            this.isShowTheme = false
          }
        } else {
          this.$message.error(res.msg || '获取活动主题失败')
        }
      })
    },
    getList(listQuery, reset) {
      const { page, pageSize } = listQuery
      const {
        actId,
        barcode,
        productName,
        status,
        reportTheme,
        isPlatformSubsidy,
        isAgreement,
        isSoldOut,
        isSyncStock,
        activityTime,
        signUpTime,
        isUpdateAccessPrice,
        warningRateStatus,
        isVirtualShop,
        isGiveSku,
        skuId,
        reportId,
        warningPriceIsTooLow,
        warningStartPersonalQty,
        idStr,
        code,
        stepPriceStatus,
        isOrder,
        isPlatformBidding,
      } = this.listQuery
      const actStartTime =
        activityTime && activityTime.length ? activityTime[0].getTime() : ''
      const actEndTime =
        activityTime && activityTime.length ? activityTime[1].getTime() : ''
      const startCreateTime =
        signUpTime && signUpTime.length ? signUpTime[0].getTime() : ''
      const endCreateTime =
        signUpTime && signUpTime.length ? signUpTime[1].getTime() : ''
      const loading = this.$loading({
        lock: true,
        text: 'Loading',
        spinner: 'el-icon-loading',
        background: 'rgba(255,255,255, 0.8)'
      })
      this.tabActiveName = status ? String(status) : '0'
      const params = {
        pageNum: reset ? 1 : page,
        pageSize,
        actId,
        barcode,
        productName,
        status,
        reportTheme,
        isPlatformSubsidy,
        isAgreement,
        isSoldOut,
        isSyncStock,
        actStartTime,
        actEndTime,
        startCreateTime,
        endCreateTime,
        isUpdateAccessPrice,
        warningRateStatus,
        isVirtualShop,
        isGiveSku,
        skuId,
        reportId,
        warningPriceIsTooLow,
        warningStartPersonalQty,
        idStr,
        code,
        stepPriceStatus,
        isOrder,
        isPlatformBidding,
      }
      apiApplyList(params)
        .then((res) => {
          loading.close()
          if (res.status === 'success') {
            const { list, total } = res.data.data
            this.list = list || []
            // this.list.forEach((item) => {
            //   this.reportThemeLists.push(item.reportTheme);
            // });
            this.listQuery = {
              ...this.listQuery,
              total
            }
            if (reset) {
              this.getStatusAndCount(params)
            }
          } else {
            this.$message.error(res.msg || res.errorMsg || '服务异常')
          }
        })
        .catch(() => {
          loading.close()
        })
    },
    selectReportGroupCount() {
      apiSelectReportGroupCount({})
        .then((res) => {
          if (res.status === 'success') {
            if (res.data && res.data.data) {
              this.editConfig.rejectedStatusNum = res.data.data.rejectedCount
              this.editConfig.platformSubsidyNum = res.data.data.platformSubsidy
              // this.editConfig.agreementNum = res.data.data.noAgreementCount;
              this.editConfig.updateGroupBuyingAcceptPrice =
                res.data.data.updateGroupBuyingAcceptPrice
              this.editConfig.warningPriceTooLowCount =
                res.data.data.warningPriceTooLowCount
              this.editConfig.warningStartPersonalQtyCount =
                res.data.data.warningStartPersonalQtyCount
              this.editConfig.warningRateStatusCount =
                res.data.data.warningRateStatusCount
            }
          }
        })
        .catch(() => {})
    },
    tipsSearch(value) {
      // this.resetTipsSearch()
      if (value === 'platformSubsidyNum') {
        this.editConfig.searchPlatformSubsidy =
          !this.editConfig.searchPlatformSubsidy
        if (this.editConfig.searchPlatformSubsidy) {
          this.listQuery.isPlatformSubsidy = 0
        } else {
          this.listQuery.isPlatformSubsidy = ''
        }
        this.editConfig.searchAgreement = false
        this.listQuery.isAgreement = ''
        this.editConfig.searchRejectedStatus = false
        this.listQuery.status = ''
        this.editConfig.searchWarningRateStatusCount = false
        this.listQuery.warningRateStatus = 2
        this.editConfig.searchWarningPriceTooLowCount = false
        this.listQuery.warningPriceIsTooLow = ''
        this.editConfig.searchWarningStartPersonalQtyCount = false
        this.listQuery.warningStartPersonalQty = ''
      } else if (value === 'agreementNum') {
        this.editConfig.searchAgreement = !this.editConfig.searchAgreement
        if (this.editConfig.searchAgreement) {
          this.listQuery.isAgreement = 1
        } else {
          this.listQuery.isAgreement = ''
        }
        this.editConfig.searchPlatformSubsidy = false
        this.listQuery.isPlatformSubsidy = ''
        this.editConfig.searchRejectedStatus = false
        this.listQuery.status = ''
        this.editConfig.searchWarningRateStatusCount = false
        this.listQuery.warningRateStatus = 2
        this.editConfig.searchWarningPriceTooLowCount = false
        this.listQuery.warningPriceIsTooLow = ''
        this.editConfig.searchWarningStartPersonalQtyCount = false
        this.listQuery.warningStartPersonalQty = ''
      } else if (value === 'rejectedStatusNum') {
        this.editConfig.searchRejectedStatus =
          !this.editConfig.searchRejectedStatus
        if (this.editConfig.searchRejectedStatus) {
          this.listQuery.status = 2
        } else {
          this.listQuery.status = ''
        }
        this.editConfig.searchPlatformSubsidy = false
        this.listQuery.isPlatformSubsidy = ''
        this.editConfig.searchAgreement = false
        this.listQuery.isAgreement = ''
        this.editConfig.searchWarningRateStatusCount = false
        this.listQuery.warningRateStatus = 2
        this.editConfig.searchWarningPriceTooLowCount = false
        this.listQuery.warningPriceIsTooLow = ''
        this.editConfig.searchWarningStartPersonalQtyCount = false
        this.listQuery.warningStartPersonalQty = ''
      } else if (value === 'warningRateStatusCount') {
        this.editConfig.searchWarningRateStatusCount =
          !this.editConfig.searchWarningRateStatusCount
        if (this.editConfig.searchWarningRateStatusCount) {
          this.listQuery.warningRateStatus = 1
        } else {
          this.listQuery.warningRateStatus = 2
        }
        this.editConfig.searchPlatformSubsidy = false
        this.listQuery.isPlatformSubsidy = ''
        this.editConfig.searchAgreement = false
        this.listQuery.isAgreement = ''
        this.editConfig.searchRejectedStatus = false
        this.listQuery.status = ''
        this.editConfig.searchWarningPriceTooLowCount = false
        this.listQuery.warningPriceIsTooLow = ''
        this.editConfig.searchWarningStartPersonalQtyCount = false
        this.listQuery.warningStartPersonalQty = ''
      } else if (value === 'warningPriceTooLowCount') {
        this.editConfig.searchWarningPriceTooLowCount =
          !this.editConfig.searchWarningPriceTooLowCount
        if (this.editConfig.searchWarningPriceTooLowCount) {
          this.listQuery.warningPriceIsTooLow = 1
        } else {
          this.listQuery.warningPriceIsTooLow = ''
        }
        this.editConfig.searchPlatformSubsidy = false
        this.listQuery.isPlatformSubsidy = ''
        this.editConfig.searchAgreement = false
        this.listQuery.isAgreement = ''
        this.editConfig.searchRejectedStatus = false
        this.listQuery.status = ''
        this.editConfig.searchWarningRateStatusCount = false
        this.listQuery.warningRateStatus = 2
        this.editConfig.searchWarningStartPersonalQtyCount = false
        this.listQuery.warningStartPersonalQty = ''
      } else if (value === 'warningStartPersonalQtyCount') {
        this.editConfig.searchWarningStartPersonalQtyCount =
          !this.editConfig.searchWarningStartPersonalQtyCount
        if (this.editConfig.searchWarningStartPersonalQtyCount) {
          this.listQuery.warningStartPersonalQty = 1
        } else {
          this.listQuery.warningStartPersonalQty = ''
        }
        this.editConfig.searchPlatformSubsidy = false
        this.listQuery.isPlatformSubsidy = ''
        this.editConfig.searchAgreement = false
        this.listQuery.isAgreement = ''
        this.editConfig.searchRejectedStatus = false
        this.listQuery.status = ''
        this.editConfig.searchWarningRateStatusCount = false
        this.listQuery.warningRateStatus = 2
        this.editConfig.searchWarningPriceTooLowCount = false
        this.listQuery.warningPriceIsTooLow = ''
      }
      this.getList(this.listQuery, true)
      // this.getStatusAndCount(this.listQuery);
      actionTracking('group_management_top_quick_search', {
        filter_item: {
          warningPriceTooLowCount: 'low_price',
          platformSubsidyNum: 'platform_subsidy',
          warningRateStatusCount: 'off_shelf',
          rejectedStatusNum: 'reject',
          warningStartPersonalQtyCount: 'suggestion_search'
        }[value]
      })
    },
    /**
     * 设置选中数据
     */
    setCheckedDatas(datas) {
      this.selections = datas.map((el) => el.frameReportId)
    },
    handerEdit(row) {
      console.log(row);
      sessionStorage.setItem('editCollageItem', JSON.stringify(row));
      const path = '/editCollageActivity';
      const obj = {
        frameReportId: row.frameReportId,
        fromType: 'edit'
      }
      sessionStorage.setItem('collageActType', 'edit')
      window.openTab(path, obj)
      // this.$router.push({ path: '/editCollageActivity', query: { frameReportId: row.frameReportId, fromType: 'edit' } });
    },
    // 下线之前检查一下是否要增加弹框信息
    checkOffline(row) {
      this.actOffline.actRow = row
      apiCheckOffLine({ frameReportId: row.frameReportId }).then(res => {
        if(res.code === 1000) {
          this.actOffline.visible = res.data.checkResult
          if(!this.actOffline.visible) {
            this.handerOffline(row)
          }
        }
      }).catch(() => {})
    },
    // 下线
    handerOffline(row) {
      apiOffLine({ frameReportId: row.frameReportId })
        .then((res) => {
          if (res.status === 'success') {
            this.getStatusAndCount()
            this.getList(this.listQuery, true)
          } else {
            this.$message.error(res.msg || res.errorMsg || '服务异常')
          }
        })
        .catch(() => {
          this.$message.error('服务异常')
        }).finally(() => {
          this.actOffline = {
            visible: false,
            actRow: {}
          }
        })
    },
    selectChange(val) {
      this.selectIds = val
    },
    addCroed(row) {
      this.innerSelected = row.customerGroupId
      // this.customerInfo = {
      //   id: row.customerGroupId,
      //   name: row.customerGroupName,
      //   tagDef: row.tagDef,
      // }
      this.crowdDialogVis = true
    },
    cancelModal() {
      this.crowdDialogVis = false
      this.bulkChangesDialog = false
    },
    // 批量修改
    bulkChanges() {
      // this.downloadUrl = '';
      // this.files = '';
      // this.fileName = '';
      this.bulkChangesDialog = true
    },
    toAuction(){
      window.openTab('/auctionProductManage/index')
    },
    viewOperationLog(row) {
      this.listOperationLogConfig = { reportId: row.frameReportId }
      this.listOperationLogVisible = true
    },
    tabHandleClick() {
      this.listQuery.status =
        Number(this.tabActiveName) === 0 ? '' : Number(this.tabActiveName)
      this.getList(this.listQuery, false)
    },
    giveSku(row) {
      if (row.isGiveSku) {
        // this.$router.push(`/mzPromotion?csuid=${row.skuId}`);
        window.openTab('/mzPromotion', { csuid: row.skuId })
      } else {
        sessionStorage.setItem('pinTuanProductInfo', JSON.stringify(row))
        // window.openTab('/addMzActive', { fromType: 'pinTuan', reportId: row.frameReportId });
        this.$router.push(
          `/addMzActive?fromType=pinTuan&reportId=${row.frameReportId}`
        )
      }
    },
    // 查看业务商圈
    viewBusiness(row) {
      this.selectViewRow = row
      this.viewBusinessDialog = true
    },
    // 查看黑白名单
    seeNameList(id) {
      this.controlGroupId = id
      this.showNameList = true
    },
    cancelDialog() {
      this.showNameList = false
    }
  }
}
</script>

<style lang="scss" scoped>
.contentBox {
  padding: 16px;
  background: #fff;
  margin-bottom: 10px;
  .tooltipEllipsis {
    overflow: hidden;
    width: 140px;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-box-pack: center;
    -webkit-box-align: center;
    -webkit-line-clamp: 2; //折两行后显示'...'
  }
  .topTip-prompt {
    padding: 5px 20px;
    background: #f3d9b2;
    opacity: 0.8;
    color: #ff2121;
  }
  .searchMsg {
    font-weight: 700;
    width: 200px;
    font-size: 16px;
  }
  .sign {
    display: inline-table;
    width: 3px;
    height: 13px;
    background: linear-gradient(360deg, #1d69c4 1%, #8bbdfc 99%);
    border-radius: 2px;
    margin-right: 8px;
  }
  .topTip {
    padding: 20px 0;
    margin-bottom: 10px;
    .topTip-info {
      font-size: 14px;
      font-family: PingFangSC, PingFangSC-Regular;
      font-weight: 400;
      text-align: left;
      color: #3c93ee;
    }
    .tag-list {
      margin-top: 20px;
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 10px;
      height: auto;
      transition: height 0.3s;
      p {
        margin: 0;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      > div {
        cursor: pointer;
        padding: 10px;
        background-color: #f7f7f7;
        border-radius: 3px;
        border: 1px solid #f7f7f7;
        transition: all 0.3s;
      }
      > div:hover {
        border: 1px solid #45addd;
      }
    }
  }
  .tips {
    padding: 8px 16px;
    margin-bottom: 10px;
    padding-left: 0;
    .div-info:hover {
      border: 1px solid #4183d5;
      background-color: #fff;
      .status-span i {
        background: #4183d5 !important;
      }
    }
    .div-info {
      display: inline-block;
      padding: 5px 10px 10px 5px;
      border-radius: 2px;
      font-size: 14px;
      font-family: PingFangSC, PingFangSC-Regular;
      font-weight: 400;
      border: 1px solid #dcdfe6;
      margin: 0 10px 10px 0;
      min-width: 120px;
      p {
        margin: 0;
        padding-top: 5px;
        font-size: 14px;
        font-family: PingFangSC, PingFangSC-Medium;
        font-weight: 500;
        color: #333333;
      }
      .status-span {
        padding-left: 14px;
        position: relative;
        i {
          position: absolute;
          top: 50%;
          transform: translateY(-50%);
          left: 4px;
          display: inline-block;
          vertical-align: middle;
          width: 5px;
          height: 5px;
          border-radius: 50%;
        }
      }
      .refundCountBox {
        .refundCount {
          margin-left: 12px;
          font-size: 20px;
          font-weight: 500;
          color: #ff3945;
          font-family: PingFangSC, PingFangSC-Medium;
        }
        .seeCount {
          float: right;
          color: #ffffff;
        }
      }
    }
  }
  .operation-info {
    margin-bottom: 20px;
  }
  /* table */
  ::v-deep .el-table {
    &::before,
    ::before {
      background-color: #fff;
    }
    font-family: PingFangSC-Regular;
    font-weight: 400;
    color: #292933;
    border-radius: 2px;
    border: 1px solid #e4e4eb;
    tr {
      td {
        border-right: 1px solid #e4e4eb;
        .cell {
          padding-left: 20px;
          padding-right: 20px;
        }
        &.el-table-column--selection {
          .cell {
            padding: 0 10px;
          }
        }
      }
    }

    thead {
      color: #292933;
      th {
        height: 40px;
        padding: 0;
        font-weight: 400;
        background: #eeeeee;
        border: none;
        border-right: 1px solid #e4e4eb;
      }
    }
    tr {
      td {
        height: 40px;
        padding: 0;
        background: #fff;
        border-bottom: 1px solid #ebeef5;
        .cell {
          height: 100%;
          display: flex;
          flex-direction: column;
          justify-content: center;
          white-space: break-spaces;
          font-size: 12px;
          font-family: PingFangSC, PingFangSC-Regular;
          font-weight: 400;
          text-align: left;
          line-height: 22px;
          padding: 8px 16px;
        }
      }
    }
    .el-table__body {
      // 隔行变色
      tr.el-table__row--striped {
        td {
          background: #ffffff;
        }
      }
    }
    .table-row,
    .table-header-row {
      height: 40px;
    }
  }
  ::v-deep .cell {
    .el-checkbox__inner {
      border: 1px solid #000000;
    }
  }
}
.searchMy {
  padding-left: 30px;
}
.searchMy ::v-deep .el-form-item__label {
  margin-left: 20px;
  padding: 0;
}
.searchMy ::v-deep .el-input__inner {
  border-radius: 0 4px 4px 0;
}
.searchMy ::v-deep .el-date-editor {
  width: 100%;
}
.searchMy ::v-deep .el-select {
  display: table-cell;
  width: 100%;
}
.searchMy ::v-deep .el-form-item__content {
  width: 100%;
}
.searchMy ::v-deep .el-form-item--small.el-form-item {
  margin-bottom: 10px;
  width: 32%;
}
.searchMy ::v-deep .el-form-item--small .el-form-item__content {
  line-height: 30px;
  width: 100%;
}
.searchMy ::v-deep .el-input-group__prepend {
  background: none;
  color: #333333;
  padding: 0 10px;
}
.searchMy ::v-deep .el-form-item--small.el-form-item {
  margin-bottom: 10px;
  width: 97%;
}
.search-title {
  display: table-cell;
  padding: 0 10px;
  text-align: center;
  border: 1px solid #dcdfe6;
  height: 30px;
  line-height: 30px;
  vertical-align: middle;
  border-right: none;
  border-radius: 4px 0 0 4px;
  color: #333333;
  white-space: nowrap;
}
.btn-box {
  float: right;
  display: flex;
  align-items: center;
  .showMoreBtns {
    .showMore {
      color: #4184d5;
      font-size: 14px;
      float: right;
      padding: 5px 20px;
      cursor: pointer;
    }
  }
}
.tag-class1 {
  color: white;
  background-color:#22b14c;
}
.tag-class2 {
  color: rgb(243, 157, 28);
  border: 1px solid rgb(243, 157, 28);
}
.ex{
  color: #22b14c;
  cursor: pointer;
}
</style>
<style lang="scss" scoped>
 .collageActivityTable  ::v-deep .el-table .cell{
     line-height:unset !important
   }
</style>
