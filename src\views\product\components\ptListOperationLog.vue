<template>
  <el-dialog
    title='查看操作日志'
    :visible='dialogVisible'
    width='750px'
    :before-close='handleClose'>
    <xyy-table
      :loading='loading'
      :data='tableConfig.data'
      :list-query='listQuery'
      :col='tableConfig.col'
      @get-data='getList'
    >
      <template slot='operationContent' slot-scope='{col}'>
        <el-table-column :key='col.index' :label='col.name' :width='col.width'>
          <template slot-scope='{row}'>
            <div style='width: 100%;position: relative;height: 23px;overflow: hidden;line-height: 23px'
                 :style="row.isOpenCon?'height:auto':'height: 30px'">
              <a
                v-if="(row.operationContent || '').includes('http')"
                style="width:125px;line-height: 30px;color: #4183d5;display: block"
                :href="row.operationContent"
                download
              >
                {{ row.operationContent }}
              </a>
              <div v-else style='width:125px;line-height: 30px'>{{ row.operationContent }}</div>
              <el-button style='width:40px;position: absolute;top: 0;right: 0'
                         v-if='row.operationContent&&row.operationContent.length>11' size='small' type='text'
                         @click='row.isOpenCon=!row.isOpenCon'>
                {{ row.isOpenCon ? '收起' : '展开' }}
              </el-button>
            </div>
          </template>
        </el-table-column>
      </template>

    </xyy-table>
  </el-dialog>
</template>

<script>
import { selectOperationLog } from '@/api/market/collageActivity'

export default {
  name: 'ptListOperationLog',
  props: {
    modifyConfig: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      dialogVisible: true,
      loading: true,
      listQuery:{
        pageSize: 10,
        page: 1,
        total: 0,
      },
      tableConfig: {
        data: [],
        col:[
          {
            index: 'operation',
            name: '操作'
          },
          {
            index: 'operationContent',
            name: '操作内容',
            width: 200,
            slot: true,
          },
          {
            index: 'operator',
            name: '操作人'
          },
          {
            index: 'operationTime',
            name: '操作时间',
            width: 180,
            formatter: (row, col, cell) => {
              return this.formatDate(cell)
            },
          }
        ]
      }
    }
  },
  mounted() {
    this.getList()
  },
  methods: {
    async getList(listQuery) {
      if (listQuery) {
        const { pageSize, page } = listQuery;
        this.listQuery.pageSize = pageSize;
        this.listQuery.page = page;
      }
      const params = {};
      const { pageSize, page } = this.listQuery;
      params.pageNum = page;
      params.pageSize = pageSize;
      params.reportId = this.modifyConfig.reportId
      try {
        const res = await selectOperationLog(params)
        if (res.code === 1000) {
          this.tableConfig.data = res.data.list.map(item => {
            item.isOpenCon = false
            return item
          })
          this.listQuery.total = res.data.totalCount
        } else {
          // this.$message.error(res.msg)
          this.$alert(res.msg, { type: 'error' })
        }
      } catch (e) {
        console.log(e)
      }
      this.loading = false
    },
    handleClose() {
      this.$emit('update:listOperationLogVisible', false)
    }
  }
}
</script>

<style scoped lang='scss'>
::v-deep   .el-table thead th {
  background: #f9f9f9;
  border: none;

  .cell {
    font-family: PingFangSC, PingFangSC-Regular;
    font-weight: 400;
    text-align: left;
    color: rgba(51, 51, 51, 0.85);
    line-height: 22px;
  }
}

::v-deep   .el-table__body-wrapper {
  font-size: 12px;
  color: #666666;
}
</style>
