<!--created by <PERSON><PERSON><PERSON><PERSON><PERSON> in 2021/3/12
  创建人群/选择人群
-->
<template>
  <div>
    <part-title title="人群列表" />
    <div class="searchMy">
      <el-form
        ref="form"
        class="fix-item-width"
        size="small"
        label-position="right"
        :model="formData"
      >
        <el-row>
          <el-col
            :lg="6"
            :sm="12"
          >
            <el-form-item
              prop="id"
            >
              <el-input
                v-model="formData.id"
                clearable
                placeholder="请输入"
              >
                <template slot="prepend">
                  人群ID
                </template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col
            :lg="6"
            :sm="12"
          >
            <el-form-item
              prop="groupName"
            >
              <el-input
                v-model="formData.groupName"
                clearable
                placeholder="请输入"
              >
                <template slot="prepend">
                  人群名称
                </template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col
            :lg="6"
            :sm="12"
          >
            <el-form-item
              prop="createStartTime"
            >
              <span
                class="search-title"
              >创建时间</span>
              <div style="display: table-cell; line-height: 24px;width: 100%">
                <el-date-picker
                  v-model="formData.createStartTime"
                  type="datetime"
                  placeholder="选择日期时间"
                  prefix-icon="el-icon-date"
                />
              </div>
            </el-form-item>
          </el-col>
          <el-col
            :lg="6"
            :sm="12"
            class="btn-group"
          >
            <el-button
              size="small"
              type="primary"
              @click="searchData"
            >
              查询
            </el-button>
            <el-button
              size="small"
              @click="resetFields"
            >
              重置
            </el-button>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <el-button
      v-permission="['marketing_customerOperation_createCrowd']"
      type="primary"
      size="small"
      @click="createPeopleGroup(createOrCopyTypes.create)"
    >
      创建人群
    </el-button>
    <el-button v-if="showFactoryReport" size="small" @click="go('/factoryReport')">厂家查价举报</el-button>
    <div v-loading="loading">
      <el-table
        :data="crowdsData.list"
        style="margin-top: 10px;"
        border
        max-height="500px"
        :header-cell-style="{ background: '#eeeeee', color: '#666666' }"
        @row-click="rowClick"
      >
        <!--        单选选择-->
        <el-table-column
          v-if="selectAble"
          align="center"
          prop="selection"
          width="50"
        >
          <template #default="{ row }">
            <el-radio
              :value="selected"
              :label="row.id"
              @input="selectChange($event, row)"
            >
              <span />
            </el-radio>
          </template>
        </el-table-column>
        <el-table-column
          prop="id"
          align="center"
          label="人群ID"
          width="120"
        />
        <el-table-column
          prop="tagName"
          label="人群名称"
          align="center"
          width="250"
        />
        <el-table-column
          label="人群定义"
          prop="tagDef"
          min-width="300"
        >
          <template slot="header">
            <div style="width: 100%; text-align: center;">
              人群定义
            </div>
          </template>
          <template slot-scope="{ row }">
            <p
              v-for="(item, index) in row.tagDef"
              :key="index"
            >
              {{ item }}
            </p>
          </template>
        </el-table-column>
        <el-table-column
          label="创建时间"
          align="center"
          width="160"
        >
          <template slot-scope="scope">
            <span>
              {{ scope.row.createTime | formatDate }}
            </span>
          </template>
        </el-table-column>
        <el-table-column
          v-if="operateAble"
          align="center"
          label="操作"
          width="120"
          fixed="right"
        >
          <template slot-scope="{ row }">
            <el-button
              type="text"
              @click="viewItemDetail(row)"
            >
              查看
            </el-button>
            <el-button
              v-permission="['marketing_customerOperation_copyCrowd']"
              type="text"
              @click="copyFromItem(row)"
            >
              复制
            </el-button>
          </template>
        </el-table-column>
        <template slot="empty">
          <div class="noData">
            <p class="img-box">
              <img
                :src="emptyImg"
                alt
              >
            </p>
            <p>暂无数据</p>
          </div>
        </template>
      </el-table>
      <div
        v-show="crowdsData.total > 0"
        class="page-container"
      >
        <div class="pag-text">
          共 {{ crowdsData.total }} 条数据，每页{{ pageInfo.pageSize }}条，共{{
            Math.ceil(crowdsData.total / pageInfo.pageSize)
          }}页
        </div>
        <el-pagination
          background
          class="pager"
          :current-page.sync="pageInfo.pageNum"
          :page-size.sync="pageInfo.pageSize"
          layout="sizes, prev, pager, next, jumper"
          :page-sizes="[10, 20, 30, 50]"
          :total="crowdsData.total"
          prev-text="上一页"
          next-text="下一页"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
    <!--     查看人群弹窗-->
    <customer-list-dialog
      v-if="dialogCommand.viewPeopleInfoDialog"
      v-model="dialogCommand.viewPeopleInfoDialog"
      :customer-execute-type="customerDialogType.viewDetail"
      :append-params="dialogAppendParams"
      :append-to-body="true"
    />
    <!--    创建人群弹窗-->
    <create-peoples-dialog
      v-if="dialogCommand.createGroup"
      v-model="dialogCommand.createGroup"
      :create-or-copy="createOrCopyType"
      :group-data="copyGroupData"
      :append-to-body="true"
      :province-and-merchant-type="provinceAndMerchantType"
      @confirm="refreshPage"
    />
  </div>
</template>
<script>
import PartTitle from '@/components/part-title/index';
import { actionTracking } from '@/track/eventTracking';
import {
  getCrowdList,
  getCrowdDetail,
  getProvincesAndMerchantTypes,
} from './fetch/fetch';
import CustomerListDialog from './customer-list-dialog';
import CreatePeoplesDialog from './create-peoples-dialog';
import { customerDialogType, createOrCopyTypes, emptyImg } from './constant';

export default {
  name: 'CrowdSelector',
  components: {
    PartTitle,
    CreatePeoplesDialog,
    CustomerListDialog,
  },
  props: {
    showFactoryReport: {
      default: true,
      type: Boolean
    },
    // 是否可以选择
    selectAble: Boolean,
    // 是否可以操作按钮
    operateAble: Boolean,
    // eslint-disable-next-line no-bitwise,vue/require-prop-type-constructor
    selected: Number | String,
  },
  data() {
    return {
      emptyImg,
      dialogCommand: {
        viewPeopleInfoDialog: false,
        createGroup: false,
      },
      dialogAppendParams: undefined,
      crowdsData: {
        list: [],
        total: 0,
      },
      pageInfo: {
        pageNum: 1,
        pageSize: 10,
      },
      formData: {
        createStartTime: '',
        groupName: '',
        id: '',
      },
      loading: false,
      customerDialogType,
      createOrCopyType: createOrCopyTypes.create,
      createOrCopyTypes,
      copyGroupData: undefined,
      provinceAndMerchantType: undefined,
      handlePromise: promise => promise
        .then(resolved => [resolved, undefined])
        .catch(err => [undefined, err]),
    };
  },
  mounted() {
    this.searchData();
  },
  methods: {
    // 刷新页面
    refreshPage() {
      this.getPeopleList();
    },
    handleSizeChange(val) {
      this.pageInfo.pageSize = val;
      this.getPeopleList();
    },
    handleCurrentChange() {
      this.getPeopleList();
    },
    selectChange($event, row) {
      this.$emit('update:selected', row.id);
      this.$emit('selectChange', row);
    },
    rowClick(row) {
      if (this.selectAble) {
        this.selectChange(row.id, row);
      }
    },
    async createPeopleGroup() {
      actionTracking('create_crowd_click', { })
      const provinceAndMerchantType = await this.getProvincesAndMerchantTypes();
      if (provinceAndMerchantType) {
        this.provinceAndMerchantType = provinceAndMerchantType;
      }
      this.createOrCopyType = this.createOrCopyTypes.create;
      this.dialogCommand.createGroup = true;
    },
    // 获取省市及客户类型
    async getProvincesAndMerchantTypes() {
      const provinceAndMerchantType = await this.handlePromise(
        getProvincesAndMerchantTypes(),
      );
      if (
        provinceAndMerchantType[0]
        && provinceAndMerchantType[0].status === 'success'
        && provinceAndMerchantType[0].data
      ) {
        this.provinceAndMerchantType = provinceAndMerchantType[0].data;
        return provinceAndMerchantType[0].data;
      }
      this.$message.warning('获取信息失败');
      return undefined;
    },
    // 复制
    async copyFromItem(row) {
      const provinceAndMerchantType = await this.getProvincesAndMerchantTypes();
      if (provinceAndMerchantType) {
        this.provinceAndMerchantType = provinceAndMerchantType;
      } else {
        return;
      }
      const detail = await this.handlePromise(getCrowdDetail({ id: row.id }));
      if (detail[0] && detail[0].status === 'success' && detail[0].data) {
        this.copyGroupData = detail[0].data.detail;
      } else {
        this.$message.warning('获取信息失败');
        return;
      }
      this.createOrCopyType = this.createOrCopyTypes.copy;
      this.dialogCommand.createGroup = true;
    },
    viewItemDetail(row) {
      actionTracking('crowd_view_click', {
        crowd_id : row.id
      })
      this.dialogAppendParams = {};
      this.dialogAppendParams.customerGroupId = row.id;
      this.dialogCommand.viewPeopleInfoDialog = true;
    },
    resetFields() {
      this.$refs.form.resetFields();
      this.searchData();
    },
    searchData() {
      this.pageInfo.pageNum = 1;
      this.getPeopleList();
    },
    getPeopleList() {
      const data = { ...this.pageInfo, ...this.formData };
      this.loading = true;
      if (data.createStartTime) {
        data.createStartTime = new Date(data.createStartTime).getTime();
      }
      getCrowdList(data)
        .then((res) => {
          if (res.code === 1000) {
            this.crowdsData.list = res.data.list ? res.data.list : [];
            this.crowdsData.total = res.data.totalCount;
          } else {
            this.$message({
              message: '请求失败',
              type: 'error',
            });
          }
        })
        .catch((error) => {
          console.log(error);
          this.loading = false;
        })
        .finally(() => {
          this.loading = false;
        });
    },
    go(path) {
      window.openTab(path);
    }
  },
};
</script>
<style scoped lang="scss">
.searchMy ::v-deep  .el-form-item__label {
  margin-left: 20px;
  padding: 0;
}
.searchMy ::v-deep  .el-input__inner {
  border-radius: 0 4px 4px 0;
}
.searchMy ::v-deep  .el-date-editor{
  width: 100%;
}
.search-title {
  display: table-cell;
  padding: 0 10px;
  text-align: center;
  border: 1px solid #dcdfe6;
  height: 30px;
  line-height: 30px;
  vertical-align: middle;
  border-right: none;
  border-radius: 4px 0 0 4px;
  color: #333333;
  white-space: nowrap;
}
.searchMy ::v-deep  .el-select {
  display: table-cell;
  width: 100%;
}
.searchMy ::v-deep  .el-form-item__content{
  width: 100%;
}
.searchMy ::v-deep  .el-form-item--small.el-form-item{
  margin-bottom: 10px;
  width: 32%;
}
.searchMy ::v-deep  .el-form-item--small .el-form-item__content{
  line-height: 30px;
  width: 100%;
}
.searchMy ::v-deep  .el-input-group__prepend {
  background: none;
  color: #333333;
  padding: 0 10px;
}
.Fsearch {
  padding: 0px 20px 10px;
  font-weight: 500;
  .searchMsg {
    font-weight: 700;
    width: 200px;
  }
  .sign {
    display: inline-table;
    width: 3px;
    height: 13px;
    background: linear-gradient(360deg, #1d69c4 1%, #8bbdfc 99%);
    border-radius: 2px;
    margin-right: 8px;
  }
}
.searchMy ::v-deep  .el-form-item--small.el-form-item{
  margin-bottom: 10px;
  width: 97%;
}
form.el-form.fix-item-width {
  .el-row {
    ::v-deep   .el-col .el-form-item {
      .el-form-item__content {
        > .el-input {
          width: 100%;
        }

        > .el-date-editor {
          width: 100%;
        }
      }
    }
  }
}

.btn-group {
  display: flex;
  justify-content: flex-end;
}
@import 'style/style';
</style>
