<template>
  <div>
    <common-header title="售后管理" :shouHeightLine="false">
      <template slot="header-right">
        <el-button size="small" @click="reset">重置</el-button>
				<el-button size="small" type="primary" @click="$emit('search')">查询</el-button>
			</template>
      <el-form label-position="right" label-width="10px" style="margin-top:10px;">
        <el-row>
          <el-col :xs="16" :md="12" :lg="5" :xl="5">
            <el-form-item>
              <el-input v-model="form[formKey[0]]" placeholder="请输入订单号" size="small" clearable>
                <template slot="prepend">订单号</template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :xs="16" :md="12" :lg="5" :xl="5">
            <el-form-item>
              <el-input v-model="form[formKey[1]]" placeholder="请输入订单号" size="small" clearable>
                <template slot="prepend">商品</template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :xs="16" :md="12" :lg="5" :xl="5">
            <el-form-item>
              <province-list v-model="form[formKey[2]]" label="订单状态" :list="menuList.orderStatusList"></province-list>
            </el-form-item>
          </el-col>
          <el-col :xs="16" :md="12" :lg="5" :xl="5">
            <el-form-item>
              <province-list v-model="form[formKey[3]]" label="售后发起方" :list="menuList.afterSaleSourceList"></province-list>
            </el-form-item>
          </el-col>
          <el-col :xs="16" :md="12" :lg="5" :xl="5">
            <el-form-item>
              <province-list v-model="form[formKey[4]]" label="售后类型" :list="menuList.afterSalesTypeList"></province-list>
            </el-form-item>
          </el-col>
          <el-col :xs="16" :md="12" :lg="5" :xl="5">
            <el-form-item>
              <province-list v-model="form[formKey[5]]" label="售后状态" :multiple="true" :list="menuList.afterSaleStatusList"></province-list>
            </el-form-item>
          </el-col>
          <el-col :xs="16" :md="12" :lg="5" :xl="5">
            <el-form-item>
              <province-list v-model="form[formKey[6]]" label="售后原因" :list="AllRefundReasonList" valueProp="showText" labelProp="showText"></province-list>
            </el-form-item>
          </el-col>
          <el-col :xs="16" :md="12" :lg="5" :xl="5">
            <el-form-item>
              <el-input v-model="form[formKey[7]]" size="small" clearable>
                <template slot="prepend">标记备注</template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :xs="16" :md="12" :lg="5" :xl="5">
            <el-form-item>
              <province-list v-model="form[formKey[8]]" label="退款支付状态" :list="menuList.payStatusList"></province-list>
            </el-form-item>
          </el-col>
          <el-col :xs="16" :md="12" :lg="5" :xl="5">
            <el-form-item>
              <el-input v-model="form[formKey[9]]" placeholder="ERP编码 / 客户名称 / 客户手机号" size="small" clearable>
                <template slot="prepend">客户</template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :xs="16" :md="12" :lg="5" :xl="5">
            <el-form-item>
              <province-list v-model="form[formKey[10]]" label="客户省份" :multiple="true" :list="provinceList" valueProp="branchCode" labelProp="branchName"></province-list>
            </el-form-item>
          </el-col>
          <el-col :xs="16" :md="12" :lg="5" :xl="5">
            <el-form-item>
              <province-list v-model="form[formKey[11]]" label="支付方式" :list="menuList.payTypeList"></province-list>
            </el-form-item>
          </el-col>
          <el-col :xs="16" :md="12" :lg="5" :xl="5">
            <el-form-item>
              <province-list v-model="form[formKey[12]]" label="支付渠道" :list="menuList.paySourceList"></province-list>
            </el-form-item>
          </el-col>
          <el-col :xs="20" :md="15" :lg="7" :xl="7">
            <el-form-item>
              <span class="search-title">申请时间</span>
              <div class="left-input">
                <el-date-picker
                  size="small"
                  v-model="form[formKey[13]]"
                  type="datetimerange"
                  format="yyyy-MM-dd HH:mm:ss"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  :default-time="['00:00:00', '23:59:59']"
                  style="width: 100%;"
                />
              </div>
            </el-form-item>
          </el-col>
          <el-col :xs="20" :md="15" :lg="7" :xl="7">
            <el-form-item>
              <span class="search-title">退款时间</span>
              <div class="left-input">
                <el-date-picker
                  size="small"
                  v-model="form[formKey[14]]"
                  type="datetimerange"
                  format="yyyy-MM-dd HH:mm:ss"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  :default-time="['00:00:00', '23:59:59']"
                  style="width: 100%;"
                />
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </common-header>
    <div style="padding: 0 30px;">
      <el-tabs @tab-click="tabClick" :value="tagActive">
        <el-tab-pane name="all">
					<template slot="label">
						<p style="margin:0" :title="count.total">
							<span>全部</span>
							&nbsp;
							<span class="badge">{{ count.total > 99 ? '99+' : count.total }}</span>
						</p>
					</template>
				</el-tab-pane>
        <el-tab-pane name="1,7,6">
					<template slot="label">
						<p style="margin:0" :title="count.waitSellerHandleCount">
							<span>待客户处理</span>
							&nbsp;
							<span class="badge">{{ count.waitSellerHandleCount > 99 ? '99+' : count.waitSellerHandleCount }}</span>
						</p>
					</template>
				</el-tab-pane>
        <el-tab-pane name="1,7">
					<template slot="label">
						<p style="margin:0" :title="count.waitSellerHandleCount">
							<span>待商家审核/处理</span>
							&nbsp;
							<span class="badge">{{ count.waitSellerHandleCount > 99 ? '99+' : count.waitSellerHandleCount }}</span>
						</p>
					</template>
				</el-tab-pane>
				<el-tab-pane name="7">
					<template slot="label">
						<p style="margin:0" :title="count.waitSellerReceiveCount">
							<span>待商家收货</span>
							&nbsp;
							<span class="badge">{{ count.waitSellerReceiveCount > 99 ? '99+' : count.waitSellerReceiveCount }}</span>
						</p>
					</template>
				</el-tab-pane>
				<el-tab-pane name="6">
					<template slot="label">
						<p style="margin:0" :title="count.waitDeliveryCount">
							<span>待商家退款</span>
							&nbsp;
							<span class="badge">{{ count.waitDeliveryCount > 99 ? '99+' : count.waitDeliveryCount }}</span>
						</p>
					</template>
				</el-tab-pane>
        <el-tab-pane name="66">
					<template slot="label">
						<p style="margin:0" :title="count.waitDeliveryCount">
							<span>客户已拒绝</span>
							&nbsp;
							<span class="badge">{{ count.waitDeliveryCount > 99 ? '99+' : count.waitDeliveryCount }}</span>
						</p>
					</template>
				</el-tab-pane>
      </el-tabs>
    </div>
    <div style="padding: 0 30px 10px 30px;display: flex;justify-content: space-between;">
      <div>
        <el-button type="primary" size="small">导出明细</el-button>
      </div>
      <div>
        <span style="margin-right: 40px;">
          <span>订单状态：</span>
          <el-radio v-model="orderStatus" label="">全部</el-radio>
          <el-radio v-model="orderStatus" label="2">未发货</el-radio>
          <el-radio v-model="orderStatus" label="3">已发货</el-radio>
        </span>
        <span @click="changeSort" :style="{color: sort ? '#409EFF' : ''}" class="sort">
          <span>按申请时间降序</span>
          <i class="el-icon-sort"></i>
        </span>
      </div>
    </div>
  </div>
</template>
<script>
import commonHeader from '../afterSaleManager/components/common-header.vue';
import provinceList from './components/provinceList.vue'
import { formKeyFormate, menuList } from './utils.js'
import { getListProvinceList, getListAllRefundReason } from '@/api/afterSale/index';
const normalForm = {
  orderId: '',   //订单号 对应formKey[0]
  product: '',      //商品 对应formKey[1]
  orderStatus: '',   //订单状态 对应formKey[2]
  initiator: '',    //售后发起方 对应formKey[3]
  afterSalesType: '',
  auditProcessStates: [],
  refundReason: '',
  remark: '',
  payStatus: '',
  customer: '',
  provinceCodes: [],
  payType: '',
  payChannel: '',
  time1: null, //申请时间 对应formKey[13]    时间段相关的在index.vue中处理
  time2: null   //退款时间 对应formKey[14]
}
export default {
  name: 'afterSaleManager-form',
  components: {
    commonHeader,
    provinceList
  },
  props: {
    value: {
      default: () => {},
      type: Object
    }
  },
  watch: {
    value: {
      handler(newVal) {
        const keys = Object.keys(newVal);
        if (keys.length == 0) {
          Object.keys(this.form).forEach(key => {
            this.form[key] = normalForm[key];
          })
          return;
        }
        keys.forEach(key =>{
          this.form[key] = newVal[key];
        })
      },
      deep: true
    },
    form: {
      handler(newVal) {
        this.$emit('input', newVal)
      },
      deep: true
    }
  },

  data() {
    return {
      form: {
        orderId: '',   //订单号 对应formKey[0]
        product: '',      //商品 对应formKey[1]
        orderStatus: '',   //订单状态 对应formKey[2]
        initiator: '',    //售后发起方 对应formKey[3]
        afterSalesType: '', //售后类型 对应formKey[4]
        auditProcessStates: [],  //售后状态 对应formKey[5]
        refundReason: '',         //售后原因 对应formKey[6]
        remark: '',         //标记备注 对应formKey[7]
        payStatus: '',    //退款支付状态 对应formKey[8]
        customer: '',    //客户 对应formKey[9]
        provinceCodes: [], //客户省份 对应formKey[10]
        payType: '',    //支付方式 对应formKey[11]
        payChannel: '', //支付渠道 对应formKey[12]
        time1: null, //申请时间 对应formKey[13]
        time2: null   //退款时间 对应formKey[14]

      },
      formKey: [],
      menuList: menuList,
      provinceList: [],   //客户省份枚举
      AllRefundReasonList: [],   //退款原因枚举
      tagActive: 'all',  //标签
      sort: false, //按申请时间降序  true：降序  false：默认
      orderStatus: '', //
      count: {
        total: 0,
        waitDeliveryCount: 0,
        waitSellerHandleCount: 0,
        waitSellerReceiveCount: 0,
      }
    }
  },
  created() {
    this.formKey = formKeyFormate(this.form);
    this.$emit('input', this.form)
    getListProvinceList().then((res) => {
      this.provinceList = res.data || [];
    });
    getListAllRefundReason().then((res) => {
      this.AllRefundReasonList = res.data || [];
    });
  },
  activated() {

  },
  methods: {
    reset() {
      Object.keys(this.form).forEach(key => {
        this.form[key] = normalForm[key];
      })
    },
    tabClick() {

    },
    changeSort() {
      this.sort = !this.sort;

    }
  }
}
</script>
<style scoped>
.search-title {
	display: table-cell;
	padding: 0 20px;
	text-align: center;
	border: 1px solid #dcdfe6;
	height: 30px;
	line-height: 30px;
	vertical-align: middle;
	border-right: none;
	border-radius: 4px 0 0 4px;
	color: #909399;
	white-space: nowrap;
	background-color: #F5F7FA;
}
.badge {
	background: #ff4d4f;
	border-radius: 11px;
	color: #fff;
	font-size: 12px;
	margin-left: 2px;
	padding: 0 8px;
}
.left-input {
	display: table-cell;
	width: 100%;
	line-height: 24px;
}
.sort {
  cursor: pointer;
  user-select: none;
  transition: all 0.2s;
}
.left-input .el-date-editor {
  border-top-left-radius: 0px;
  border-bottom-left-radius: 0px;
}
</style>
