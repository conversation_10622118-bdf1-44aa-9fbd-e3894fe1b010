export const formKeyFormate = (form) => {
  const keys = []
  Object.keys(form).forEach((key) => {
    keys.push(key);
  })
  return keys
}


export const menuList = {
  /**
   * 订单状态枚举
   */
  orderStatusList: [
    { value: '', label: '全部' },
    { value: 2, label: '配送中' },
    { value: 3, label: '已完成' },
    { value: 91, label: '已退款' },
  ],
  /**
   * 售后发起方
   */
  afterSaleSourceList: [
    { value: '', label: '全部' },
    { value: 1, label: '用户发起' },
    { value: 2, label: '平台发起' },
    { value: 4, label: '商家发起' },
    { value: 5, label: '系统发起' },
  ],
  /**
   * 售后类型
   */
  afterSalesTypeList: [
    { value: '', label: '全部' },
    { value: 1, label: '仅退款' },
    { value: 2, label: '退货退款' },
    { value: 3, label: '退运费' },
    { value: 4, label: '小额打款' },
    { value: 5, label: '资质售后' },
    { value: 6, label: '发票售后' },
  ],
  /**
   * 售后状态
   */
  afterSaleStatusList: [
    { value: 1, label: '等待商家确认' },
    { value: 2, label: '用户取消' },
    { value: 3, label: '商家已处理' },
    { value: 4, label: '申请被退回' },
    { value: 5, label: '商家已补发' },
    { value: 6, label: '待客户退回发票' },
    { value: 7, label: '客户已退回发票' },
  ],
  /**
   * 售后原因
   */
  payStatusList: [
    { value: '', label: '全部' },
    { value: 4, label: '退款中' },
    { value: 2, label: '退款成功' },
    { value: 3, label: '退款失败' },
  ],
  /**
   * 支付方式
   */
  payTypeList: [
    { value: '', label: '全部' },
    { value: 4, label: '在线支付' },
    { value: 2, label: '线下转账' },
  ],
  /**
   * 支付渠道
   */
  paySourceList: [
    { label:"全部", value: "" },
    { label:"微信", value: 2 },
    { label:"支付宝", value: 1 },
    { label:"电汇平台", value: 7 },
    { label:"电汇商业", value: 8 },
    { label:"平安ePay", value: 10 },
    { label:"JD银行卡支付", value: 11 },
    { label:"京东采购融资", value: 12 }
  ]

}
