<template>
    <div class="editPrice">
     <el-dialog width="850px" title="设置价格/库存" :close-on-click-modal="false" :visible="dialogVisible"
         @close="closeDialog">
         <el-form ref="form" :model="formState" label-width="150px" :rules="rules">
             <el-form-item label="提报价格：" prop="reportPrice">
                 <el-input v-model="formState.reportPrice" style="width:300px"></el-input><span
                     style="color:red;margin-left: 15px;">拼团价格：{{ formState.groupPrice }}<span
                         v-if="row.platformSubsides">，平台补贴：{{ row.platformSubsides }}</span></span>
             </el-form-item>
             <el-form-item label="起拼数量：" prop="groupNum">
                 <el-input v-model="formState.groupNum" style="width:300px"></el-input>
             </el-form-item>
             <el-form-item label="阶梯价：">
                 <el-radio-group v-model="formState.stepPriceStatus" @change="stepPriceStatusChange"
                     :disabled="(disabled && otherRuleDisabled) || pintuanDisabled">
                     <el-radio :label="1">是</el-radio>
                     <el-radio :label="2">否</el-radio>
                     <el-tooltip placement="bottom">
                         <div slot="content">
                             1.阶梯价最多支持3个阶梯。<br />
                             2. 设置阶梯价时 最大阶梯起拼数量≤活动个人限购数量≤活动总限购数量，不满足则提交失败。<br />
                             3.商品列表中展示的起拼数量为默认起拼数量，一个拼团活动中最多可支持4个拼团价格。<br />
                             4.药店买满起始促销数量后，成团价自动改为对应阶梯价；<br />
                             起拼数量＜促销起始数量1＜促销起始数量2＜促销起始数量3≤活动个人限购数量，拼团价格＞阶梯价1＞阶梯价2＞阶梯价3，不符合则提交失败；
                         </div>
                         <i class="el-icon-warning-outline" />
                     </el-tooltip>
                 </el-radio-group>
             </el-form-item>
             <el-form-item v-if="formState.stepPriceStatus === 1" label="">
                 <div v-for="(item, index) in formState.activityReportGroupLevelPriceDTOList" :key="index"
                     style="display: flex; align-items: center;margin-bottom: 10px;">
                     <span style="margin-right: 10px;">促销起始数量{{ index + 1 }}</span>
                     <el-input v-model.number="item.startQty" :precision="0" placeholder="请输入" style="width: 120px;"
                         :disabled="disabled && pintuanDisabled && otherRuleDisabled" />
                     <span style="margin: 0 10px;">阶梯价{{ index + 1 }}</span>
                     <el-input-number v-model="item.discountPrice" :precision="2" placeholder="请输入" style="width: 150px"
                         :disabled="disabled && pintuanDisabled && otherRuleDisabled" />
                     <div>
                         <i v-if="index === 0"
                             style="font-size: 30px; cursor: pointer;margin-left: 10px;margin-top: 6px;"
                             class="el-icon-circle-plus-outline"
                             @click="handleSetGroupLevelPriceDTOList('add', index)" />
                         <i v-if="index !== 0"
                             style="font-size: 30px; cursor: pointer;margin-left: 10px;margin-top: 6px;"
                             class="el-icon-remove-outline" @click="handleSetGroupLevelPriceDTOList('reduce', index)" />
                     </div>
                 </div>
             </el-form-item>
             <el-form-item label="活动总限购库存：">
                 <el-radio-group v-model="isLimit" @change="isLimitChange"
                     :disabled="(disabled && totalLimitNumDisabled) || status4">
                     <el-radio label="0">不限购</el-radio>
                     <el-radio label="1">限购</el-radio>
                 </el-radio-group>
                 <span style="color:red;margin-left: 75px;">剩余可售卖数量：{{ row.totalStock }}</span>
             </el-form-item>
             <el-form-item label="拼团活动采购上限：" prop="totalLimitNum" v-if="isLimit === '1'">
                 <el-input v-model="formState.totalLimitNum" placeholder="请输入" style="width: 200px"
                     :disabled="(disabled && totalLimitNumDisabled) || status4" />
                 <span style="color: #ff2121"> 所有参团药店采购总数量不能大于该数值</span>
             </el-form-item>
         </el-form>
         <div style="text-align: center;">
             <el-button @click="dialogVisible = false" style="width: 130px;">取消</el-button>
             <el-button type="primary" style="width: 130px;" @click="commit">确定</el-button>
         </div>
     </el-dialog>
    </div>
 </template>
 
 <script>
 import { saveActivity,apiApplyList } from '@/api/market/collageActivity';
 
 export default {
     data() {
         return {
             dialogVisible: false,
             isLimit: "0",
             row: {},
             submitLoading:false,
             formState: {
                 "reportPrice":"",
                 "barcode": "",             
                 "groupPrice": "",
                 "groupNum": "",
                 "totalLimitNum": "",
                 "isVirtualShop": "",
                 "stepPriceStatus": "",
                 "activityReportGroupLevelPriceDTOList": null,
                 "baseId": "",
                 "frameReportId": "",
                 "personalLimitNum": "",
                 "personalLimitType": "",
                 "onTheWayStock": "",
                 "version": ""
             },
             rules: {
                 reportPrice:[{ required: true, message: '请填写提报价格', trigger: 'blur' }, {
                     validator: (rule, value, callback) => {
                         if (!/^(0\.[1-9]\d*|[1-9]\d*(\.\d+)?)$/.test(value)) {
                             callback(new Error('需维护大于0的正数'))
                         }else if(!/^(0|[1-9]\d*)(\.\d{1,2})?$/.test(value)){
                             callback(new Error('最多支持维护两位小数'))
                         }else if( Number(value)-Number(this.row.platformSubsides)<0.01){
                             callback(new Error('提报价格需大于平台补贴金额'))
                         }else if(this.row.platformSubsides&&Number(value)>Number(this.formState.reportPrice)){
                             callback(new Error('提报价格仅支持调低'))
                         }else {
                             callback()
                         }
                     },trigger: 'blur'
                 }],
                 // groupPrice: [{ required: true, message: '请填写拼团价格', trigger: 'blur' }, {
                 //     validator: (rule, value, callback) => {
                 //         if (!/^[1-9]\d*$/.test(value)) {
                 //             callback(new Error('请输入大于0的正整数'))
                 //         } else {
                 //             callback()
                 //         }
                 //     }
                 // }],
                 groupNum: [{ required: true, message: '请填写起拼数量', trigger: 'blur' }],
                 totalLimitNum: [
                    { required: true, message: '请填写拼团活动采购上限', trigger: 'blur' },
                    { pattern: /^[1-9]\d*$/, message: '请维护正整数', trigger: ['blur', 'change'] }
                ],
                 // personalLimitNum: [{ required: false, message: '请填写单个药店参团数量上限',trigger: 'blur' }]
             },
             addInitiatorDTOItem: {  },
             disabled: false,
             otherRuleDisabled: false,
             pintuanDisabled: false,
             status4: false,
             totalLimitNumDisabled:false
         }
     },
     methods: {
         isLimitChange(val) {
             if (val === '0') {
                 this.formState.totalLimitNum = ''
             }
         },
         stepPriceStatusChange(val) {
             if (val === 1) {
                 const obj = { ...this.addInitiatorDTOItem };
                 this.formState.activityReportGroupLevelPriceDTOList = [obj]
             } else if (val === 2) {
                 this.formState.activityReportGroupLevelPriceDTOList = null
             }
         },
         handleSetGroupLevelPriceDTOList(type, index) {
 
             if (type === 'add') {
                 if (
                     this.formState.activityReportGroupLevelPriceDTOList.length === 3
                 ) {
                     this.$message.warning('最多添加3条数据');
                     return;
                 }
                 const obj = { ...this.addInitiatorDTOItem };
                 this.formState.activityReportGroupLevelPriceDTOList.push(obj);
                 return;
             }
             this.formState.activityReportGroupLevelPriceDTOList.splice(index, 1);
         },
       async  open(row) {
             
             this.row = row
             this.formState={
                 "reportPrice":"",
                 "barcode": "",             
                 "groupPrice": "",
                 "groupNum": "",
                 "totalLimitNum": "",
                 "isVirtualShop": "",
                 "stepPriceStatus": "",
                 "activityReportGroupLevelPriceDTOList": null,
                 "baseId": "",
                 "frameReportId": "",
                 "personalLimitNum": "",
                 "personalLimitType": "",
                 "onTheWayStock": "",
                 "version": "",
                 'allS':{},
                 "platformSubsides":""
             }
            this.dialogVisible = true
            await apiApplyList({ idStr: 'PT' + row.activityId, pageNum : 1, pageSize: 10 }).then(res => {
                 if (res.code == 1000) {
                     for (let i = 0; i < Object.keys(this.formState).length; i++) {
                     if (res.data.data.list[0][Object.keys(this.formState)[i]] != undefined) {
                         this.formState[Object.keys(this.formState)[i]] = res.data.data.list[0][Object.keys(this.formState)[i]]
                     }
 
                  }
                  this.formState.allS= res.data.data.list[0]
                  this.formState.platformSubsides=row.platformSubsides//平台补贴
                  this.formState.reportPrice=row.reportPrice//提报价格
                     this.dialogVisible = true
                 }
                 }).finally(() => {
                
                 })
             
             console.log(this.formState)
             // this.formState=Object.assign(this.formState,row)
             this.dialogVisible = true
             if (this.formState.totalLimitNum) {
                 this.isLimit = "1"
             } else {
                 this.isLimit = "0"
             }
             console.log(this.isLimit,123)
             //校验是否可修改
             this.disabled = false
             this.otherRuleDisabled = false
             this.pintuanDisabled = false
             this.status4 = false
             if (this.row.status == 1 && this.row.registrationEtime && this.row.registrationEtime <= new Date().getTime()) {
                 this.disabled = true;
             }
             if (this.row.status === 3 || this.row.status === 4) {
                 this.disabled = true;
                 if (this.row.subsidyInitiatorJson) {
                     this.totalLimitNumDisabled = true;
                 }
             }
             if (this.row.status === 3 || this.row.status === 4) {
                 this.otherRuleDisabled = false;
             } else {
                 this.otherRuleDisabled = true;
             }
             if (this.row.activityReportGroupAmountDtos && this.row.activityReportGroupAmountDtos.some(item => item.name == '平台')) {
                 this.pintuanDisabled = true;
                 if (this.row.status == 3 || this.row.status == 4) {
                     this.status4 = true;   
                 }
             } else {
                 this.pintuanDisabled = false;
             }
             this.$nextTick(() => {
                 this.$refs.form.clearValidate();
             })
         },
         closeDialog() {
             this.dialogVisible = false
         },
         commit() {
             console.log(this.formState)
             this.$refs['form'].validate((valid) => {
                 if (valid) {
                     console.log('ok')
                     let query={}
                     this.submitLoading = true;
                     console.log(this.formState)
                     query = {
                     frameReportId: this.formState.frameReportId || null,
                     barcode: this.formState.barcode,
                     version: this.formState.version || null,
                     groupPrice: Number(this.formState.reportPrice)-Number(this.formState.platformSubsides),
                     groupNum: this.formState.groupNum,
                     totalLimitNum: this.formState.totalLimitNum,
                     personalLimitNum:this.formState.personalLimitNum,
                     personalLimitType: this.formState.personalLimitType||0,
                     
                     isVirtualShop: this.formState.isVirtualShop,
                     onTheWayStock: this.formState.onTheWayStock,
                     stepPriceStatus: this.formState.stepPriceStatus,
                     activityReportGroupLevelPriceDTOList: this.formState.activityReportGroupLevelPriceDTOList || null,};
                     query.baseId = this.formState.baseId;
                     let supplyInfo = {
                         isCopyBusArea: this.formState.allS.isCopyBusArea,
                         controlRosterType: this.formState.allS.controlRosterType,
                         isCopySaleArea: this.formState.allS.isCopySaleArea,
                         controlGroupId: this.formState.allS.controlGroupId,
                         controlGroupName: this.formState.allS.controlGroupName,
                         controlUserTypes:this.formState.allS.controlUserTypes,
                         isCopyControlRoster: this.formState.allS.isCopyControlRoster,
                         isCopyControlUser: this.formState.allS.isCopyControlUser,
                         customerGroupId: this.formState.allS.customerGroupId,
                     }
                     query.saleScopeDTO = { isCopySaleArea: supplyInfo.isCopySaleArea };
 
                     if (supplyInfo.isCopySaleArea === 3) {
                         query.saleScopeDTO = { ...supplyInfo };
                         if (supplyInfo.isCopyControlUser === 2 && supplyInfo.controlRosterType != 2) {
                         const controlUserTypes = supplyInfo.controlUserTypes ? supplyInfo.controlUserTypes : [];
                         query.saleScopeDTO.controlUserTypes = controlUserTypes.join();
                         } else {
                         query.saleScopeDTO.controlUserTypes = '';
                         }
                     } else if (supplyInfo.isCopySaleArea === 2) {
                         let { customerGroupId } = supplyInfo;
                         query.saleScopeDTO = {
                         isCopySaleArea: 2,
                         customerGroupId,
                         };
                     }
                     if(Array.isArray(query.activityReportGroupLevelPriceDTOList)){
                        query.activityReportGroupLevelPriceDTOList.forEach((element,index) => {
                            element.level=index+1
                        });
                     }
                     saveActivity(query)
                     .then((res) => {
                         if (res.success) {
                         this.$message.success('提交成功');
                         setTimeout(() => {
                             // this.$emit("getList","")
                             this.closeDialog();
                             this.$emit("queryList")
                             // this.$router.replace({
                             //   path: '/collageActivity',
                             //   query: { refresh: true },
                             // });
                         }, 500);
                         } else if (res.msg) this.$message.warning(res.msg);
                     }).finally(() => {
                         this.submitLoading = false;
                     })
                 } else {
                     return false
                 }
             })
 
         }
     }
 }
 
 </script>
 <style lang="scss" >
 .editPrice{
  
 }
 // .el-radio-button__orig-radio:checked + .el-radio-button__inner{
 //         background-color: rgb(153,255,255) !important;
 //         color:black;
 //         border: 1px solid rgb(90, 90, 90);
 //     }
 // .el-radio-button__orig-radio + .el-radio-button__inner{
 //     background-color: rgb(204,204,204) !important;
 //     border: 1px solid rgb(90, 90, 90);
 //     color:black;
 // }   
 </style>