<template>
  <div>
    <!-- 备注的弹框 -->
    <el-dialog
      title="编辑退款备注"
      :visible.sync="dialogVisible"
      @close="dialogVisible = false"
      @closed="resetForm"
      width="40%"
      append-to-body
      :close-on-click-modal="false"
    >
      <div class="box">
        <p>退款备注仅内部可见，限100字</p>
        <el-input
            type="textarea"
            :rows="4"
            style="width: 80%"
            maxlength="100"
            show-word-limit
            placeholder="请输入内容"
            v-model="subForm.textarea">
        </el-input>
      </div>
      <span slot="footer" class="dialog-footer">
        <!-- <el-button @click="$emit('update:dialogVisible')">取 消</el-button> -->
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitForm()">确定 </el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { editSellerRemark } from '@/api/afterSale/index';

export default {
  props: {
  },
  // computed: {
  //   dialogVisible: {
  //     // getter
  //     get() {
  //       return this.dialogVisible1
  //     },
  //     // setter
  //     set(newValue) {
  //       // 注意：我们这里使用的是解构赋值语法
  //       // this.$emit('update:dialogVisible')
  //     }
  //   }
  // },
  data() {
    return {
      dialogVisible: false,
      subForm: {
        id: null, // 退款单明细id
        textarea: null, // 物流公司
      },
    }
  },
  created() {
    
  },
  methods: {
    openDialog(id) {
      // 拿到表格行数据赋值给相关字段，用于后面提交表单的相关参数
      this.subForm.id = id
      this.dialogVisible = true
    },
    submitForm() {
        if(!this.subForm.textarea || this.subForm.textarea == '') {
            this.$message({ type: 'error', message: '请输入备注内容' })
            return
        }
        let subData = {
            id: this.subForm.id,
            sellerRemark: this.subForm.textarea,
        }
        editSellerRemark(subData).then((res) => {
            if (res.code == 0) {
                this.$message({ type: 'success', message: '备注成功' })
                this.dialogVisible = false
                this.$parent.$emit('refreshList')
            } else {
                this.$message({ type: 'error', message: res.msg || '备注失败' })
            }
        })
    },
    resetForm() {
      this.subForm.id = null
      this.subForm.textarea = null
    },
  }
}
</script>

<style scoped lang="scss">
.box {
  margin-top: -20px;
  width: 100%;
  display: flex;
  flex-direction: column;
  // justify-content: center;
  // align-items: center;
  padding: 0 10px;
}
.plainTextSty {
  width: 100%;
  // height: 100px;
  padding: 0 10px;
  background-color: #fafafa;
}
.plainTextSty span {
  width: 30px;
}
.textSty {
  color: #575757;
}
/* 推荐，实现简单 */
::v-deep   .el-upload-list__item.is-ready,
::v-deep   .el-upload-list__item.is-uploading {
  display: none !important;
}
</style>
