<template>
  <div class="companyOpenAccount">
    <div
      v-if="Number(shopConfig.shopCreateStatus)===0&&Number(corBaseVo.status)===0"
      class="el-icon-warning NoCreatedStore"
    >
      请先进行企业信息认证，认证成功后可进行企业开户！
    </div>
    <div v-else class="headerStatus">
      <div class="companyStatus" v-if="!isSignatory">
        <div class="status">企业开户状态:</div>
        <div
          class="statusStr"
          :style="{ color:statusObj.colorStr }"
        >
          {{ statusObj.name }}
        </div>
        <div style="font-size: 12px">
          <div>{{ statusObj.tips }}</div>
          <div style="color: #f5222d; ">{{ statusObj.tips2 }}</div>
        </div>
      </div>
      <div
        v-if="isSignatory"
        style="padding: 10px;color: red;"
      >
        <p style="margin: 0">您当前正在执行平安银行开户换签，请仔细核实下方信息后提交。</p>
        <p style="margin: 0">换签成功前将不支持修改结算账户信息，但不会影响您当前店铺的运营、结算、提现等操作</p>
      </div>
      <div
        v-if="!isSignatory"
        style="margin-right: 16px"
      >
        <template v-if="Number(corBaseVo.status) === 0">
          <el-button @click="staged">暂存</el-button>
          <el-button type="primary" @click="submit" :loading="submitLoading">提交</el-button>
        </template>
        <template v-else-if='Number(corBaseVo.status) ===3 || Number(corBaseVo.status) === 2'>
          <template v-if="Number(corBaseVo.status)===2">
            <el-button type="primary" @click="viewOperationFlow">查看操作流程</el-button>
          </template>
          <el-button
            v-if="corBaseVo.signatory === 0"
            type="primary"
            @click="handleSignChange"
          >
            换签平安银行
          </el-button>
          <el-button v-if="!hasUnderReview && !hasPaymentVerification" type='primary' @click='modifySettlement'>修改结算信息</el-button>
          <el-button v-if="corBaseVo.signatory !== 0 && hasRecord && !hasPaymentVerification" type='primary' @click='modifySettlementRecord'>结算信息修改记录</el-button>
          <el-button v-if="corBaseVo.signatory !== 0 && hasPaymentVerification" type='primary' @click='paymentVerification'>打款认证</el-button>
        </template>
        <template v-else-if="Number(corBaseVo.status)===4 || Number(corBaseVo.status)===6">
          <el-button @click="viewRejectionReasonVisible">查看驳回原因</el-button>
          <el-button
            v-if="disabled && bankCardApprovalFailedDisabled"
            v-permission="['corporation_openAccount_edit']"
            type="primary"
            @click="edit"
          >编辑</el-button>
          <el-button v-else type="primary" @click="submit" :loading="submitLoading">提交</el-button>
        </template>
        <template v-else-if="Number(corBaseVo.status) === 7">
          <el-button @click="paymentVerification">打款验证</el-button>
        </template>
      </div>
      <div
        v-if="isSignatory"
        style="margin-right: 16px"
      >
        <el-button @click="handleCancelSignChange">
          返回
        </el-button>
        <el-button
          type="primary"
          :loading="submitLoading"
          @click="submit"
        >
          提交
        </el-button>
      </div>
    </div>
    <div v-show="Number(shopConfig.shopCreateStatus)!==0" class="leftContent">
      <div class="title_line first">企业信息</div>
      <el-form :model="corBaseVo" :rules="corBaseVoRules" ref="corBaseVo" label-width="100px">
        <el-form-item label="企业名称:" prop="companyName">
          <el-input
            v-model="corBaseVo.companyName"
            placeholder="请输入营业执照上的企业名称"
            :disabled="disabled"
            maxlength="30"
          ></el-input>
        </el-form-item>
        <el-form-item label="营业执照号:" prop="businessLicenseCode">
          <el-input
            v-model="corBaseVo.businessLicenseCode"
            placeholder="请输入营业执照上的统一社会信用代码"
            :disabled="disabled"
            maxlength="30"
          ></el-input>
        </el-form-item>
        <el-form-item label="经营范围:" prop="businessScop">
          <el-input
            v-model="corBaseVo.businessScop"
            placeholder="请输入营业执照的经营范围（只需要填写经营范围的前五项，每项用“,”分隔）"
            :disabled="disabled"
            maxlength="100"
          ></el-input>
        </el-form-item>
        <el-form-item label="注册资本金:" prop="registeredCapital">
          <el-input
            v-model="corBaseVo.registeredCapital"
            placeholder="请输入营业执照注册资本，请填写阿拉伯数字，单位默认“元”。例如注册资本“壹佰万元整”填写：1000000"
            :disabled="disabled"
            maxlength="20"
          ></el-input>
        </el-form-item>
        <el-form-item label="企业地址:" required>
          <div class="addrForm">
            <el-form-item prop="prov">
              <el-select
                v-model="corBaseVo.prov"
                placeholder="请选择营业执照住所省份"
                :disabled="disabled"
                @change="(val)=>provinceChange(val,'corBaseVo','provId','provinceList')"
                :key="corBaseVo.prov"
              >
                <el-option
                  v-for="province in provinceList"
                  :key="province.areaId"
                  :label="province.areaName"
                  :value="province.areaName"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item prop="city">
              <el-select
                v-model="corBaseVo.city"
                placeholder="请选择营业执照住所城市"
                :disabled="disabled"
                @change="(val)=>provinceChange(val,'corBaseVo','cityId','cityList')"
                :key="corBaseVo.city"
              >
                <el-option
                  v-for="city in corBaseVo.cityList"
                  :key="city.areaId"
                  :label="city.areaName"
                  :value="city.areaName"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item prop="area">
              <el-select
                v-model="corBaseVo.area"
                placeholder="请选择营业执照住所区县"
                :disabled="disabled"
                @change="(val)=>provinceChange(val,'corBaseVo','areaId','areaList')"
                :key="corBaseVo.area"
              >
                <el-option
                  v-for="area in corBaseVo.areaList"
                  :key="area.areaId"
                  :label="area.areaName"
                  :value="area.areaName"
                ></el-option>
              </el-select>
            </el-form-item>
          </div>
        </el-form-item>
        <el-form-item label="注册地址:" prop="addr">
          <el-input
            v-model="corBaseVo.addr"
            placeholder="请输入营业执照住所"
            :disabled="disabled"
            maxlength="50"
          ></el-input>
        </el-form-item>
        <el-form-item label="营业期限:" required>
          <el-col :span="10">
            <el-form-item prop="businessStartTime">
              <el-date-picker
                value-format="timestamp"
                placeholder="请输入营业执照的开始时间"
                type="date"
                v-model="corBaseVo.businessStartTime"
                style="width: 100%;"
                :disabled="disabled"
              ></el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="10" style="margin-right: 40px">
            <el-form-item prop="businessEndTime">
              <el-date-picker
                value-format="timestamp"
                placeholder="请输入营业执照的有效期至"
                type="date"
                v-model="corBaseVo.businessEndTime"
                style="width: 100%;margin: 0 16px"
                :disabled="businessEndTimeDisabled || disabled"
              ></el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="2">
            <el-form-item>
              <el-checkbox
                v-model="corBaseVo.longTerm"
                @change="longTermChange"
                :disabled="disabled"
              >长期</el-checkbox>
            </el-form-item>
          </el-col>
        </el-form-item>
        <el-form-item label="客服电话:" prop="customerServicePhone">
          <el-input
            v-model="corBaseVo.customerServicePhone"
            placeholder="请输入企业的客服电话"
            :disabled="disabled"
            maxlength="20"
            onkeyup="this.value=this.value.replace(/[^\d-]/g,'')"
          ></el-input>
        </el-form-item>
        <el-form-item label="股东信息列表:" required>
          <div style="font-size: 12px;color: #666">
            <span>请添加一位控股股东信息</span>
            <span style="margin-left:10px">
              可登录
              <el-link
                href="https://aiqicha.baidu.com"
                target="_blank"
                type="primary"
                :underline="false"
                style="color: #5d96db;font-size:12px;text-decoration:underline;"
              >爱企查》</el-link>，查询当前企业的股东信息
              <el-button
                type="text"
                style="color: #5d96db;font-size:12px;"
                @click="lookImg(shareholdersInformationUrl)"
              >股东信息示例图》</el-button>
            </span>
          </div>
          <el-table :data="corBaseVo.shareholderInfo" style="width: 100%">
            <el-table-column label="名称" class-name="table_required" width="100">
              <template slot-scope="scope">
                <el-form-item
                  :prop="'shareholderInfo.'+scope.$index+'.shareholderName'"
                  :rules="corBaseVoRules.shareholderInfo.shareholderName"
                >
                  <el-input
                    v-model="scope.row.shareholderName"
                    placeholder="请输入股东名称"
                    :disabled="disabled"
                    maxlength="30"
                  />
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column
              prop="shareholderType"
              label="证件类型"
              class-name="table_required"
              width="150"
            >
              <template slot-scope="scope">
                <el-form-item
                  :prop="'shareholderInfo.'+scope.$index+'.shareholderType'"
                  :rules="corBaseVoRules.shareholderInfo.shareholderType"
                >
                  <el-select
                    v-model="scope.row.shareholderType"
                    placeholder="请选证件类型"
                    :disabled="disabled"
                    :key="scope.row.shareholderType"
                  >
                    <el-option label="身份证" value="1"></el-option>
                    <el-option label="营业执照" value="2"></el-option>
                    <!-- <el-option label="统一信息代码" value="3"></el-option> -->
                  </el-select>
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column prop="shareholderNumber" class-name="table_required" label="证件号码" width="180">
              <template slot-scope="scope">
                <el-form-item
                  :prop="'shareholderInfo.'+scope.$index+'.shareholderNumber'"
                  :rules="corBaseVoRules.shareholderInfo.shareholderNumber"
                >
                  <el-input
                    v-model="scope.row.shareholderNumber"
                    placeholder="请输入证件号码"
                    :disabled="disabled"
                    maxlength="30"
                  ></el-input>
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column prop="shareholderStart" class-name="table_required" label="证件发证日期" width="150">
              <template slot-scope="scope">
                <el-form-item
                  :prop="'shareholderInfo.'+scope.$index+'.shareholderStart'"
                  :rules="corBaseVoRules.shareholderInfo.shareholderStart"
                >
                  <el-date-picker
                    value-format="timestamp"
                    placeholder="请选择证件开始日期"
                    type="date"
                    v-model="scope.row.shareholderStart"
                    style="width: 100%;"
                    :disabled="disabled"
                  ></el-date-picker>
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column prop="shareholderEnd" class-name="table_required" label="证件到期日期">
              <template slot-scope="scope">
                <el-form-item
                  :prop="'shareholderInfo.'+scope.$index+'.shareholderEnd'"
                  :rules="corBaseVoRules.shareholderInfo.shareholderEnd"
                >
                  <el-date-picker
                    v-model="scope.row.shareholderEnd"
                    value-format="timestamp"
                    placeholder="请选择证件有效期至"
                    type="date"
                    style="width: 150px;"
                    :disabled="shareholderEndTimeDisabled || disabled"
                  />
                  <el-checkbox
                    v-model="scope.row.longTerm"
                    :disabled="disabled"
                    style="margin-left: 10px;"
                    @change="shareholdChange"
                  >长期</el-checkbox>
                </el-form-item>
              </template>
            </el-table-column>
          </el-table>
        </el-form-item>
        <el-form-item label="受益人信息列表:" required>
          <div style="font-size: 12px;color: #666">
            请添加一位“最终受益股份”最多的收益人
            <span style="margin-left:10px">
              可登录
              <el-link
                href="https://aiqicha.baidu.com"
                target="_blank"
                type="primary"
                :underline="false"
                style="color: #5d96db;font-size:12px;text-decoration:underline;"
              >爱企查》</el-link>，查询当前企业的收益人信息
              <el-button
                type="text"
                style="color: #5d96db;font-size:12px;"
                @click="lookImg(beneficiaryInformationUrl)"
              >受益人信息示例图》</el-button>
            </span>
          </div>
          <el-table :data="corBaseVo.beneficiaryInfo" style="width: 100%">
            <el-table-column prop="favoreeName" label="名称" class-name="table_required">
              <template slot-scope="scope">
                <el-form-item
                  :prop="'beneficiaryInfo.'+scope.$index+'.favoreeName'"
                  :rules="corBaseVoRules.beneficiaryInfo.favoreeName"
                >
                  <el-input
                    v-model="scope.row.favoreeName"
                    placeholder="请输入受益人名称"
                    :disabled="disabled"
                    maxlength="30"
                  ></el-input>
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column prop="favoreeType" label="证件类型" class-name="table_required">
              <template slot-scope="scope">
                <el-form-item
                  :prop="'beneficiaryInfo.'+scope.$index+'.favoreeType'"
                  :rules="corBaseVoRules.beneficiaryInfo.favoreeType"
                >
                  <el-select
                    v-model="scope.row.favoreeType"
                    placeholder="请选证件类型"
                    :disabled="disabled"
                    :key="scope.row.favoreeType"
                  >
                    <el-option label="身份证" value="1"></el-option>
                    <!-- <el-option label="营业执照" value="2"></el-option> -->
                    <!-- <el-option label="统一信息代码" value="3"></el-option> -->
                  </el-select>
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column prop="favoreeNumber" class-name="table_required" label="证件号码">
              <template slot-scope="scope">
                <el-form-item
                  :prop="'beneficiaryInfo.'+scope.$index+'.favoreeNumber'"
                  :rules="corBaseVoRules.beneficiaryInfo.favoreeNumber"
                >
                  <el-input
                    v-model="scope.row.favoreeNumber"
                    placeholder="请输入证件号码"
                    :disabled="disabled"
                    maxlength="30"
                  ></el-input>
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column
              prop="favoreeStart"
              class-name="table_required"
              label="证件发证日期"
              width="190"
            >
              <template slot-scope="scope">
                <el-form-item
                  :prop="'beneficiaryInfo.'+scope.$index+'.favoreeStart'"
                  :rules="corBaseVoRules.beneficiaryInfo.favoreeStart"
                >
                  <el-date-picker
                    value-format="timestamp"
                    placeholder="请选择证件开始日期"
                    type="date"
                    v-model="scope.row.favoreeStart"
                    style="width: 100%;"
                    :disabled="disabled"
                  ></el-date-picker>
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column
              prop="favoreeEnd"
              class-name="table_required"
              label="证件到期日期"
              width="190"
            >
              <template slot-scope="scope">
                <el-form-item
                  :prop="'beneficiaryInfo.'+scope.$index+'.favoreeEnd'"
                  :rules="corBaseVoRules.beneficiaryInfo.favoreeEnd"
                >
                  <el-date-picker
                    value-format="timestamp"
                    placeholder="请选择证件有效期至"
                    type="date"
                    v-model="scope.row.favoreeEnd"
                    style="width: 100px;"
                    :disabled="favoreeEndTimeDisabled || disabled"
                  />
                  <el-checkbox
                    v-model="scope.row.longTerm"
                    :disabled="disabled"
                    style="margin-left: 10px;"
                    @change="favoreeChange"
                  >长期</el-checkbox>
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column prop="favoreeAddress" class-name="table_required" label="地址">
              <template slot-scope="scope">
                <el-form-item
                  :prop="'beneficiaryInfo.'+scope.$index+'.favoreeAddress'"
                  :rules="corBaseVoRules.beneficiaryInfo.favoreeAddress"
                >
                  <el-input
                    v-model="scope.row.favoreeAddress"
                    placeholder="请输入地址"
                    :disabled="disabled"
                    maxlength="50"
                  ></el-input>
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column prop="favoreePhone" class-name="table_required" label="联系电话">
              <template slot-scope="scope">
                <el-form-item
                  :prop="'beneficiaryInfo.'+scope.$index+'.favoreePhone'"
                  :rules="corBaseVoRules.beneficiaryInfo.favoreePhone"
                >
                  <el-input
                    v-model="scope.row.favoreePhone"
                    placeholder="请输入联系电话"
                    :disabled="disabled"
                    maxlength="20"
                    onkeyup="this.value=this.value.replace(/[^\d-]/g,'')"
                  ></el-input>
                </el-form-item>
              </template>
            </el-table-column>
          </el-table>
        </el-form-item>
        <el-form-item label="企业门头照:" prop="companyUrl">
          <div style="font-size: 12px;color: #666">
            请上传含有公司名称的门头照片（公司前台或公司大门等）。大小不超过1.5MB，支持jpg、jpeg、png
            <br><span style="color:#ff2400">
              门头照中需要显示门框，且门框内能够显示完整的公司名称
              <el-button
                type="text"
                style="color: #5d96db;font-size:12px;"
                @click="lookImg(companyDoorUrl)"
              >企业门头照示例图》</el-button>
            </span>
          </div>
          <el-upload
            :disabled="disabled"
            class="avatar-uploader"
            :show-file-list="false"
            action
            :http-request="(obj)=>httpRequest(obj,corBaseVo,'companyUrl')"
            :before-upload="beforeAvatarUpload"
          >
            <img v-if="corBaseVo.companyUrl" :src="corBaseVo.companyUrl" class="avatar" />
            <i v-else class="el-icon-plus avatar-uploader-icon"></i>
          </el-upload>
        </el-form-item>
        <el-form-item label="营业执照:" prop="businessLicenseUrl">
          <div style="font-size: 12px;color: #666">请上传企业营业执照原件。大小不超过1.5MB，支持jpg、jpeg、png</div>
          <el-upload
            :disabled="disabled"
            class="avatar-uploader"
            :show-file-list="false"
            action
            :http-request="(obj)=>httpRequest(obj,corBaseVo,'businessLicenseUrl')"
            :before-upload="beforeAvatarUpload"
          >
            <img
              v-if="corBaseVo.businessLicenseUrl"
              :src="corBaseVo.businessLicenseUrl"
              class="avatar"
            />
            <i v-else class="el-icon-plus avatar-uploader-icon"></i>
          </el-upload>
        </el-form-item>
        <el-form-item label="开户许可证:" prop="accountLicenceUrl">
          <div style="font-size: 12px;color: #666">请上传开户许可证原件。大小不超过1.5MB，支持jpg、jpeg、png</div>
          <el-upload
            :disabled="disabled"
            class="avatar-uploader"
            :show-file-list="false"
            action
            :http-request="(obj)=>httpRequest(obj,corBaseVo,'accountLicenceUrl')"
            :before-upload="beforeAvatarUpload"
          >
            <img
              v-if="corBaseVo.accountLicenceUrl"
              :src="corBaseVo.accountLicenceUrl"
              class="avatar"
            />
            <i v-else class="el-icon-plus avatar-uploader-icon"></i>
          </el-upload>
        </el-form-item>
      </el-form>
      <el-divider />
      <div class="title_line second">法人信息</div>
      <el-form :model="jpersonVo" :rules="jpersonVoRules" ref="jpersonVo" label-width="100px">
        <el-form-item label="法人名称:" prop="juridicalPersonName">
          <el-input
            v-model="jpersonVo.juridicalPersonName"
            placeholder="请输入企业法人姓名"
            :disabled="disabled && bankCardApprovalFailedDisabled"
            maxlength="30"
          ></el-input>
        </el-form-item>
        <el-form-item label="法人性别:" prop="sex">
          <el-radio v-model="jpersonVo.sex" :label="1" :disabled="disabled && bankCardApprovalFailedDisabled">男</el-radio>
          <el-radio v-model="jpersonVo.sex" :label="2" :disabled="disabled && bankCardApprovalFailedDisabled">女</el-radio>
        </el-form-item>
        <el-form-item label="法人身份证号:" prop="cardNumber">
          <el-input
            v-model="jpersonVo.cardNumber"
            placeholder="请输入企业法人身份证号"
            :disabled="disabled && bankCardApprovalFailedDisabled"
            maxlength="30"
          ></el-input>
        </el-form-item>
        <el-form-item label="身份证效期:" required>
          <el-col :span="10">
            <el-form-item prop="cardStartTime">
              <el-date-picker
                value-format="timestamp"
                placeholder="请选择法人身份证的开始时间"
                type="date"
                v-model="jpersonVo.cardStartTime"
                style="width: 100%;"
                :disabled="disabled && bankCardApprovalFailedDisabled"
              ></el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="10" style="margin-right: 40px">
            <el-form-item prop="cardEndTime">
              <el-date-picker
                value-format="timestamp"
                placeholder="请选择法人身份证的有效期至"
                type="date"
                v-model="jpersonVo.cardEndTime"
                style="width: 100%;margin: 0 16px"
                :disabled="juridicalPersonDisabled || (disabled  && bankCardApprovalFailedDisabled)"
              ></el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="2">
            <el-form-item>
              <el-checkbox
                v-model="jpersonVo.jpersonLongTerm"
                @change="jpersonLongTermChange"
                :disabled="disabled && bankCardApprovalFailedDisabled"
              >长期</el-checkbox>
            </el-form-item>
          </el-col>
        </el-form-item>
        <el-form-item label="法人户籍地址:" prop="permanentAddress">
          <el-input
            v-model="jpersonVo.permanentAddress"
            placeholder="请输入企业法人身份证正面地址"
            :disabled="disabled && bankCardApprovalFailedDisabled"
            maxlength="50"
          ></el-input>
        </el-form-item>
        <el-form-item label="法人身份证签发地址:" prop="cardIssueAddress">
          <el-input
            v-model="jpersonVo.cardIssueAddress"
            placeholder="请输入企业法人身份证正面地址"
            :disabled="disabled && bankCardApprovalFailedDisabled"
            maxlength="50"
          ></el-input>
        </el-form-item>
        <el-form-item label="法人手机号:" prop="phone">
          <el-input
            v-model="jpersonVo.phone"
            placeholder="请输入企业法人手机号"
            :disabled="disabled && bankCardApprovalFailedDisabled"
            maxlength="11"
            onkeyup="this.value=this.value.replace(/[^\d]/g,'')"
          ></el-input>
        </el-form-item>
        <el-form-item label="法人身份证正面照片:" prop="cardUrl">
          <div style="font-size: 12px;color: #666">请上传法人身份证原件。大小不超过1.5MB，支持jpg、jpeg、png</div>
          <el-upload
            :disabled="disabled && bankCardApprovalFailedDisabled"
            class="avatar-uploader"
            :show-file-list="false"
            action
            :http-request="(obj)=>httpRequest(obj,jpersonVo,'cardUrl')"
            :before-upload="beforeAvatarUpload"
          >
            <img v-if="jpersonVo.cardUrl" :src="jpersonVo.cardUrl" class="avatar" />
            <i v-else class="el-icon-plus avatar-uploader-icon"></i>
          </el-upload>
        </el-form-item>
        <el-form-item label="法人身份证反面照片:" prop="cardReverseUrl">
          <div style="font-size: 12px;color: #666">请上传法人身份证原件。大小不超过1.5MB，支持jpg、jpeg、png</div>
          <el-upload
            :disabled="disabled && bankCardApprovalFailedDisabled"
            class="avatar-uploader"
            :show-file-list="false"
            action
            :http-request="(obj)=>httpRequest(obj,jpersonVo,'cardReverseUrl')"
            :before-upload="beforeAvatarUpload"
          >
            <img v-if="jpersonVo.cardReverseUrl" :src="jpersonVo.cardReverseUrl" class="avatar" />
            <i v-else class="el-icon-plus avatar-uploader-icon"></i>
          </el-upload>
        </el-form-item>
      </el-form>
      <el-divider />
      <div class="title_line third">结算信息</div>
      <el-form :model="accountVo" :rules="accountVoRules" ref="accountVo" label-width="100px">
        <el-form-item label="开卡人名称:" prop="registeredName">
          <el-input
            v-model="accountVo.registeredName"
            placeholder="请输入开卡人名称"
            :disabled="disabled && bankCardApprovalFailedDisabled"
            maxlength="30"
          ></el-input>
        </el-form-item>
        <el-form-item
          label="银行账号:"
          prop="registeredBankAccount"
          maxlength="30"
          onkeyup="this.value=this.value.replace(/[^\d]/g,'')"
        >
          <el-input
            v-model="accountVo.registeredBankAccount"
            placeholder="请输入企业的对公收款账号"
            :disabled="disabled && bankCardApprovalFailedDisabled"
          ></el-input>
        </el-form-item>
        <el-form-item label="开户银行名称:" prop="bankName">
          <el-select
            v-model="accountVo.bankName"
            placeholder="请选择开户银行"
            style="width: 400px"
            :disabled="disabled && bankCardApprovalFailedDisabled"
            :filterable="true"
            :filter-method="getBankList"
            @change="bankChange"
            :key="accountVo.bankName"
          >
            <el-option
              v-for="item in bankList"
              :key="item.bankName"
              :label="item.bankName"
              :value="item.bankName"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="开户银行支行:" prop="subBankName">
          <el-select
            ref="subBank"
            v-model="accountVo.subBankName"
            placeholder="请选择开户支行名称"
            style="width: 400px"
            :disabled="disabled && bankCardApprovalFailedDisabled"
            :filterable="true"
            :filter-method="searchSubBank"
            @change="subBankChange"
            :key="accountVo.subBankName"
          >
            <el-option
              v-for="item in subBankList"
              :key="item.bankName"
              :label="item.bankName"
              :value="item.bankName"
            ></el-option>
          </el-select>
          <!-- <el-button v-if="!disabled" icon="el-icon-search" circle @click="submitSearchSub"></el-button>-->
        </el-form-item>
      </el-form>
      <el-divider />
      <div class="title_line fourth">联系人信息</div>
      <el-form
        :model="accountContactsVo"
        :rules="accountContactsVoRules"
        ref="accountContactsVo"
        label-width="100px"
      >
        <el-form-item label="商户联系人姓名:" prop="contactsName">
          <el-input
            v-model="accountContactsVo.contactsName"
            placeholder="请输入企业对外的常用联系人姓名"
            :disabled="disabled && bankCardApprovalFailedDisabled"
            maxlength="30"
          ></el-input>
        </el-form-item>

        <el-form-item label="联系人身份证号:" prop="cardNumber">
          <el-input
            v-model="accountContactsVo.cardNumber"
            placeholder="请输入企业联系人身份证号"
            :disabled="disabled && bankCardApprovalFailedDisabled"
            maxlength="30"
          />
        </el-form-item>

        <el-form-item label="商户联系人省市区县:" required>
          <div class="addrForm">
            <el-form-item prop="prov">
              <el-select
                v-model="accountContactsVo.prov"
                placeholder="请选择省份"
                :disabled="disabled && bankCardApprovalFailedDisabled"
                @change="(val)=>provinceChange(val,'accountContactsVo','provId','provinceList')"
                :key="accountContactsVo.prov"
              >
                <el-option
                  v-for="province in provinceList"
                  :key="province.id"
                  :label="province.areaName"
                  :value="province.areaName"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item prop="city">
              <el-select
                v-model="accountContactsVo.city"
                placeholder="请选择城市"
                :disabled="disabled && bankCardApprovalFailedDisabled"
                @change="(val)=>provinceChange(val,'accountContactsVo','cityId','cityList')"
                :key="accountContactsVo.city"
              >
                <el-option
                  v-for="city in accountContactsVo.cityList"
                  :key="city.id"
                  :label="city.areaName"
                  :value="city.areaName"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item prop="area">
              <el-select
                v-model="accountContactsVo.area"
                placeholder="请选择区县"
                :disabled="disabled && bankCardApprovalFailedDisabled"
                @change="(val)=>provinceChange(val,'accountContactsVo','areaId','areaList')"
                :key="accountContactsVo.area"
              >
                <el-option
                  v-for="area in accountContactsVo.areaList"
                  :key="area.id"
                  :label="area.areaName"
                  :value="area.areaName"
                ></el-option>
              </el-select>
            </el-form-item>
          </div>
        </el-form-item>
        <el-form-item label="商户联系人地址:" prop="address">
          <el-input
            v-model="accountContactsVo.address"
            placeholder="请输入企业联系人的详细地址"
            :disabled="disabled && bankCardApprovalFailedDisabled"
            maxlength="50"
          ></el-input>
        </el-form-item>
        <el-form-item label="商户邮箱:" prop="email">
          <el-input
            v-model="accountContactsVo.email"
            placeholder="请输入企业联系人的常用邮箱。后期将接收银行的确认邮件，请认真填写"
            :disabled="disabled && bankCardApprovalFailedDisabled"
            maxlength="30"
          ></el-input>
        </el-form-item>
        <el-form-item label="商户手机号:" prop="phone">
          <el-input
            v-model="accountContactsVo.phone"
            placeholder="请输入企业联系人的手机号。银行审核资料期间，可能与该手机号进行企业信息核实，请认真填写"
            :disabled="disabled && bankCardApprovalFailedDisabled"
          ></el-input>
        </el-form-item>
        <el-form-item v-if="!disabled || !bankCardApprovalFailedDisabled" label="短信验证码:" prop="smsVerificationCode">
          <el-input
            v-model="accountContactsVo.smsVerificationCode"
            placeholder="请输入商户手机号收到的验证码"
            style="width: 50%;margin-right: 30px"
            maxlength="10"
          ></el-input>
          <el-button
            type="primary"
            @click="sendVerificationCode"
            :disabled="sendDisabled"
          >发送验证码{{ sendDisabled ? `(${sendCountDown})` : '' }}</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div v-show="Number(shopConfig.shopCreateStatus)!==0" class="rightContent">
      <el-tabs
        v-model="activeName"
        tab-position="right"
        style="height: 160px;"
        @tab-click="tabClick"
      >
        <el-tab-pane label="企业信息" name="first"></el-tab-pane>
        <el-tab-pane label="法人信息" name="second"></el-tab-pane>
        <el-tab-pane label="结算信息" name="third"></el-tab-pane>
        <el-tab-pane label="联系人信息" name="fourth"></el-tab-pane>
      </el-tabs>
    </div>
    <OperationFlow
      v-if="operationFlowVisible"
      :operationFlowVisible.sync="operationFlowVisible"
      :wxCodeUrl="corBaseVo.wxCodeUrl"
    />
    <RejectionReason
      v-if="rejectionReasonVisible"
      :rejectionReasonVisible.sync="rejectionReasonVisible"
      :failReason="corBaseVo.failReason"
    />
    <el-image-viewer
      v-if="showViewer"
      :url-list="srcArr"
      :on-close="closeViewer"
      :z-index="100000"
    />
    <ModifySettlementAccount
      v-if="modifySettlementVisible"
      :modify-settlement-visible.sync="modifySettlementVisible"
      :modify-settlement-obj="modifySettlementObj"
      @updateInfo="getInfo"
    />

    <PaymentVerificationDialog
      v-if="paymentVerificationDialogVis"
      :payment-verification-dialog-vis.sync="paymentVerificationDialogVis"
      :base="accountContactsVo"
      :auth-type="hasPaymentVerification ? 2 : authType"
      @updateInfo="getInfo"
    />
  </div>
</template>

<script>
import { mapState } from 'vuex';
import { uploadFDFS } from '@/api/order';
import { getAddresInfo } from '@/api/qual';
import { bankQuery, tempSave, save, queryBankList, querySubBankList, sendActiveCode, queryBankListForPA, querySubBankListForPA, queryBankAccountModifyInfo } from '@/api/companyOpenAccount';

import ElImageViewer from 'element-ui/packages/image/src/image-viewer';
import OperationFlow from './componments/operationFlow';
import RejectionReason from './componments/viewRejectionReason';
import ModifySettlementAccount from './componments/ModifySettlementAccount';
import PaymentVerificationDialog from './componments/PaymentVerificationDialog';

export default {
  name: 'CompanyOpenAccount',
  components: {
    OperationFlow,
    RejectionReason,
    ElImageViewer,
    ModifySettlementAccount,
    PaymentVerificationDialog,
  },
  data() {
    return {
      hasRecord: false,
      hasUnderReview: false,
      hasPaymentVerification: false,
      isSignatory: false,
      authType: 1,
      paymentVerificationDialogVis: false,
      activeName: 'first',
      disabled: false,
      bankCardApprovalFailedDisabled: false,
      corBaseVo: {
        accountLicenceUrl: '', // 开户许可证url
        addr: '', // 注册地址
        area: '', // 区名称
        areaId: '', // 区编码
        beneficiaryInfo: [
          {
            favoreeName: '',
            favoreeType: '',
            favoreeNumber: '',
            favoreeStart: '',
            favoreeEnd: '',
            favoreeAddress: '',
            favoreePhone: '',
            longTerm: '',
          },
        ], // 受益人信息
        businessEndTime: '', //营业执照结束时间
        businessLicenseCode: '',//企业营业执照编码
        businessLicenseUrl: '',//营业执照url
        businessScop: '',//经营范围
        businessStartTime: '',//营业执照开始时间
        city: '',//市
        cityId: '',//市编码
        companyName: '',//企业名称
        companyUrl: '',//公司照片url
        customerServicePhone: '',//客服电话号码
        id: '',//主键
        prov: '',//省
        provId: '',//省编码
        registeredCapital: '',//注册资本金
        shareholderInfo: [
          {
            shareholderName: '',
            shareholderType: '',
            shareholderNumber: '',
            shareholderStart: '',
            shareholderEnd: '',
            longTerm: '',
          },
        ], // 股东信息
        longTerm: '', // 是否长期
        status: 0, // 0、未开户,1、开户中，2待微信注册，3、开户成功,4,开户失败
        wxCodeUrl: '', // 微信二维码url
        signatory: 2,
      },
      jpersonVo: {
        cardEndTime: '',//身份证结束时间
        cardIssueAddress: '',//身份证签发地址
        cardReverseUrl: '',//身份证反面照片
        cardStartTime: '',//身份证开始时间
        cardUrl: '',//身份证正面照片
        id: '',//主键id
        juridicalPersonName: '',//法人名称
        permanentAddress: '',//户籍地址
        phone: '',//法人手机号
        sex: '',//性别 1-男 2-女
        cardNumber: '', //法人身份证号
        jpersonLongTerm: '',//是否长期
      },
      accountVo: {
        bankCode: '', // 开户银行编码
        bankName: '', // 开户银行名称
        id: '', // id
        registeredBankAccount: '', // 银行账号
        registeredName: '', // 开卡人姓名
        subBankName: '', // 开户支行名称
        subBankCode: '', // 开户支行编码
      },
      accountContactsVo: {
        address: '', // 地址
        area: '', // 区县名称
        areaId: '', // 区县ID
        city: '', // 市
        cityId: '', // 市ID
        contactsName: '', // 联系人姓名
        email: '', // 邮件地址
        id: '', // 主键ID
        phone: '', // 联系人电话
        prov: '', // 省
        provId: '', // 省ID
        smsVerificationCode: '', // 短信验证码
        cardNumber: '', // 企业联系人身份证号
      },
      corBaseVoRules: {
        companyName: [
          {required: true, message: '请输入营业执照上的企业名称', trigger: 'blur'}
        ],
        businessLicenseCode: [
          {required: true, message: '请输入营业执照上的统一社会信用代码', trigger: 'blur'}
        ],
        businessScop: [
          {required: true, message: '请输入营业执照的经营范围', trigger: 'blur'}
        ],
        registeredCapital: [
          {required: true, message: '请输入营业执照注册资本，请填写阿拉伯数字，单位默认“元”。例如注册资本“壹佰万元整”填写：1000000', trigger: 'blur'}
        ],
        prov: [
          {required: true, message: '请选择营业执照住所省份', trigger: 'change'}
        ],
        city: [
          {required: true, message: '请选择营业执照住所城市', trigger: 'change'}
        ],
        area: [
          {required: true, message: '请选择营业执照住所区县', trigger: 'change'}
        ],
        addr: [
          {required: true, message: '请输入营业执照住所', trigger: 'blur'}
        ],
        businessStartTime: [
          {required: true, message: '请输入营业执照的开始时间', trigger: 'change'}
        ],
        businessEndTime: [
          {required: true, message: '请输入营业执照的有效期至', trigger: 'change'}
        ],
        customerServicePhone: [
          {required: true, message: '请输入企业的客服电话', trigger: 'blur'}
        ],
        shareholderInfo: {
          shareholderName: [{required: true, message: '请输入股东名称', trigger: 'blur'}],
          shareholderType: [{required: true, message: '请选证件类型', trigger: 'change'}],
          shareholderNumber: [{required: true, message: '请输入证件号码', trigger: 'blur'}],
          shareholderStart: [{required: true, message: '请选择证件开始日期', trigger: 'change'}],
          shareholderEnd: [{required: true, message: '请选择证件有效期至', trigger: 'change'}]
        },
        beneficiaryInfo: {
          favoreeName: [{required: true, message: '请输入受益人名称', trigger: 'blur'}],
          favoreeType: [{required: true, message: '请选证件类型', trigger: 'change'}],
          favoreeNumber: [{required: true, message: '请输入证件号码', trigger: 'blur'}],
          favoreeStart: [{required: true, message: '请选择证件开始日期', trigger: 'change'}],
          favoreeEnd: [{required: true, message: '请选择证件有效期至', trigger: 'change'}],
          favoreeAddress: [{required: true, message: '请输入地址', trigger: 'blur'}],
          favoreePhone: [{required: true, message: '请输入联系电话', trigger: 'change'}],
        },
        companyUrl: [
          {required: true, message: '请上传含有公司名称的门头照片'}
        ],
        businessLicenseUrl: [
          {required: true, message: '请上传企业营业执照原件'}
        ],
        accountLicenceUrl: [
          {required: true, message: '请上传开户许可证原件'}
        ]
      },
      jpersonVoRules: {
        juridicalPersonName: [
          {required: true, message: '请输入企业法人姓名', trigger: 'blur'}
        ],
        sex: [
          {required: true, message: '请选择企业法人性别'}
        ],
        cardNumber: [
          {required: true, message: '请输入企业法人身份证号', trigger: 'blur'}
        ],
        cardStartTime: [
          {required: true, message: '请选择法人身份证的开始时间', trigger: 'change'}
        ],
        cardEndTime: [
          {required: true, message: '请选择法人身份证的有效期至', trigger: 'change'}
        ],
        permanentAddress: [
          {required: true, message: '请输入企业法人身份证正面地址', trigger: 'blur'}
        ],
        cardIssueAddress: [
          {required: true, message: '请输入企业法人身份证正面地址', trigger: 'blur'}
        ],
        phone: [
          {required: true, message: '请输入企业法人手机号', trigger: 'blur'}
        ],
        cardUrl: [
          {required: true, message: '请上传法人身份证正面照'}
        ],
        cardReverseUrl: [
          {required: true, message: '请上传法人身份证反面照片'}
        ],
      },
      accountVoRules: {
        registeredName: [
          {required: true, message: '请输入开卡人名称', trigger: 'blur'}
        ],
        registeredBankAccount: [
          {required: true, message: '请输入企业的对公收款账号', trigger: 'blur'}
        ],
        bankName: [
          {required: true, message: '请选择开户银行', trigger: 'change'}
        ],
        subBankName: [
          {required: true, message: '请选择开户支行名称', trigger: 'change'}
        ],
      },
      accountContactsVoRules: {
        contactsName: [
          {required: true, message: '请输入企业对外的常用联系人姓名', trigger: 'blur'}
        ],
        cardNumber: [
          {required: true, message: '请输入企业联系人身份证号', trigger: 'blur'}
        ],
        prov: [
          {required: true, message: '请选择省份', trigger: 'change'}
        ],
        city: [
          {required: true, message: '请选择城市', trigger: 'change'}
        ],
        area: [
          {required: true, message: '请选择区县', trigger: 'change'}
        ],
        address: [
          {required: true, message: '请输入企业联系人的详细地址', trigger: 'blur'}
        ],
        email: [
          {required: true, message: '请输入企业联系人的常用邮箱', trigger: 'blur'}
        ],
        phone: [
          {required: true, message: '请输入企业联系人的手机号', trigger: 'blur'},
          // {pattern: /^[1][3-8]\d{9}$/, message: '请输入正确的手机号'}
        ],
        smsVerificationCode: [
          {required: true, message: '请输入商户手机号收到的验证码', trigger: 'blur'},
        ]
      },
      formRefs: ['corBaseVo', 'jpersonVo', 'accountVo', 'accountContactsVo'],
      businessEndTimeDisabled: false,
      shareholderEndTimeDisabled: false,
      favoreeEndTimeDisabled: false,
      juridicalPersonDisabled: false,
      provinceList: [],
      bankList: [],
      subBankList: [],
      operationFlowVisible: false,
      rejectionReasonVisible: false,
      sendCountDown: 60,
      sendDisabled: false,
      submitLoading: false,
      shareholdersInformationUrl: require('@/assets/image/common/shareholders_information.png'),
      beneficiaryInformationUrl: require('@/assets/image/common/beneficiary_information.png'),
      companyDoorUrl: require('@/assets/image/common/company_door.jpg'),
      showViewer: false,
      srcArr: [],
      modifySettlementVisible: false,
      modifySettlementObj: {},
    };
  },
  computed: {
    ...mapState('app', ['shopConfig']),
    statusObj() {
      const obj = {}
      switch (Number(this.corBaseVo.status)) {
        case 0:
          obj.name = '未开户'
          obj.colorStr = '#f5222d'
          obj.tips = '银行开户备案成功，未结算资金银行保护更安全，已结算资金当天提现当天到账 以下信息只用于银行认证备案，平台将保护您的企业信息安全'
          break
        case 1:
          obj.name = '开户中'
          obj.colorStr = '#ff9800'
          obj.tips = '您的企业相关信息已提交至银行进行人工审批，审批周期预计1-2个工作日，请耐心等待！'
          obj.tips2 = '审批通过后，银行将会向您的商户邮箱发送邮件，需要您按照邮件要求进行登录确认，请您关注新邮件'
          break
        case 2:
          obj.name = '待企业法人授权'
          obj.colorStr = '#ff9800'
          obj.tips = '您的企业信息银行已审批通过！请按照操作流程进行企业法人微信授权 法人授权完成后，企业完成开户'
          break
        case 3:
          obj.name = '开户成功'
          obj.colorStr = '#52c41a'
          obj.tips = '您已完成企业开户。'
          let str = '如需修改结算账户信息，可点击右侧的“修改结算信息”进行变更'
          if (this.hasUnderReview) {
            str = '当前有一条结算信息修改记录【银行卡审核中】，审批周期预计5-10分钟，可到修改记录查询详情'
          }
          if (this.hasPaymentVerification) {
            str = '当前有一条结算信息修改记录【银行卡待验证】，请在48小时内完成打款认证'
          }
          obj.tips2 = str
          break
        case 4:
          obj.name = '开户失败'
          obj.colorStr = '#f5222d'
          obj.tips = '您的企业信息银行审批未通过，请参考驳回原因修改信息后重新提交'
          break
        case 5:
          obj.name = '银行卡审核中'
          obj.colorStr = '#ff9800'
          obj.tips = '您的企业相关信息已提交至银行进行人工审批，审批周期预计5-10分钟，请耐心等待！审批通过后，银行将会向您的结算信息银行账号打款，请按照页面提示进行打款验证'
          break
        case 6:
          obj.name = '银行卡审核失败'
          obj.colorStr = '#f5222d'
          obj.tips = '您的企业信息银行审批未通过，请参考驳回原因修改信息后重新提交'
          break
        case 7:
          obj.name = '银行卡待验证'
          obj.colorStr = '#ff9800'
          obj.tips = '您的企业信息银行已审批通过！'
          obj.tips2 = '为核验身份，银行将会向您的结算信息银行账号打款，请点击“打款验证”进行打款验证操作'
      }
      return obj
    }
  },
  created() {
    this.getAddr(0, 'provinceList');
    this.getBankList();
  },
  mounted() {
    this.$nextTick(() => {
      const el = document.querySelector('.leftContent');
      el.addEventListener('scroll', this.onScroll);
    });
  },
  activated() {
    this.getInfo();
  },
  methods: {
    handleCancelSignChange() {
      this.$confirm('返回至企业开户页面，当前换签页面信息将不会保存，确认返回吗？', '温馨提示', {
        confirmButtonText: '确认返回',
        cancelButtonText: '再想想',
      })
        .then(() => {
          this.disabled = true;
          this.isSignatory = false;
          this.getInfo();
          this.getBankList();
        })
        .catch(() => {});
    },
    handleSignChange() {
      this.disabled = false;
      this.isSignatory = true;
      this.accountVo.bankName = '';
      this.accountVo.subBankName = '';
      this.getBankList();
    },
    paymentVerification() {
      // this.paymentVerificationDialogVis = true;
      this.$router.push({
        name: 'companyCheckPayment',
        query: {
          authType: this.hasPaymentVerification ? 2 : this.authType,
        }
      })
    },
    lookImg(url) {
      this.srcArr = [];
      this.srcArr.push(url);
      this.showViewer = true;
    },
    closeViewer() {
      this.showViewer = false;
    },
    async getBankList(val) {
      let res = '';
      if (this.isSignatory || this.corBaseVo.signatory !== 0 || this.corBaseVo.status === 0) {
        res = await queryBankListForPA({ bankName: val || '' });
      } else {
        res = await queryBankList();
      }
      if (res && res.code === 0) {
        this.bankList = res.result;
      } else {
        this.$message.error(res.msg || '获取开户行失败');
      }
    },
    async bankChange(val) {
      const [obj] = this.bankList.filter(item => item.bankName === val);
      this.accountVo.bankCode = obj.bankCode;
      this.accountVo.subBankName = '';
      this.accountVo.subBankCode = '';
      let res = '';
      if (this.isSignatory || this.corBaseVo.signatory !== 0 || this.corBaseVo.status === 0) {
        res = await querySubBankListForPA({ bankName: this.accountVo.bankName, subBankName: '' });
      } else {
        res = await querySubBankList({ bankName: val, subBankName: '' });
      }
      if (res && res.code === 0) {
        this.subBankList = res.result;
      } else {
        this.$message.error(res.msg || '获取开户支行失败');
      }
    },
    async searchSubBank(val) {
      console.log(val);
      let res = '';
      if (this.isSignatory || this.corBaseVo.signatory !== 0 || this.corBaseVo.status === 0) {
        res = await querySubBankListForPA({ bankName: this.accountVo.bankName, subBankName: val });
      } else {
        res = await querySubBankList({ bankName: this.accountVo.bankName, subBankName: val });
      }
      if (res && res.code === 0) {
        this.subBankList = res.result;
      } else {
        this.$message.error(res.msg || '获取开户支行失败');
      }
    },
    subBankChange(val) {
      const [obj] = this.subBankList.filter(item => item.bankName === val);
      this.accountVo.subBankCode = obj.bankCode;
    },
    getAddr(code, listName, objName) {
      getAddresInfo({ parentCode: code }).then((res) => {
        if (res && res.code === 0) {
          if (listName !== 'provinceList') {
            this.$set(this[objName], listName, res.data);
            this.$forceUpdate();
          } else {
            this.provinceList = res.data;
          }
        }
      });
    },
    provinceChange(val, objName, strName, listName) {
      if (listName === 'provinceList') {
        const [targetObj] = this.provinceList.filter(item => item.areaName === val);
        if (targetObj) {
          this.$set(this[objName], strName, targetObj.areaCode);
          this.getAddr(targetObj.areaCode, 'cityList', objName);
        }
        this.areaList = [];
        const ary = ['cityId', 'city', 'areaId', 'area'];
        ary.forEach(key => {
          this.$set(this[objName], key, '');
        })
        const list = ['cityList', 'areaList'];
        list.forEach(key => {
          this.$set(this[objName], key, []);
        });
      } else {
        const [targetObj] = this[objName][listName].filter(item => item.areaName === val);
        if (targetObj) {
          this.$set(this[objName], strName, targetObj.areaCode);
        }
      }
      if (listName === 'cityList') {
        this.$set(this[objName], 'areaList', [])
        const [targetObj] = this[objName][listName].filter(item => item.areaName === val)
        if (targetObj) {
          this.getAddr(targetObj.areaCode, 'areaList', objName)
        }
        const ary = ['areaId', 'area']
        ary.forEach(key => {
          this.$set(this[objName], key, '')
        })
      }
    },
    beforeAvatarUpload(file) {
      console.log(file)
      if (file.type !== 'image/jpg' && file.type !== 'image/jpeg' && file.type !== 'image/png') {
        this.$message.error('上传附件只能是 jpg、jpeg、png 格式！')
        return false
      }
      if (file.size > 1572864) {
        this.$message.error('上传附件大小不能超过 1.5M！')
        return false
      }
    },
    async httpRequest(obj, data, str) {
      try {
        const res = await uploadFDFS(obj)
        if (Number(res.code) === 200) {
          const imgUrl = process.env.VUE_APP_UPLOAD_API + '/' + res.data
          this.$set(data, str, imgUrl)
          if (this.corBaseVoRules[str]) {
            this.$refs.corBaseVo.validateField([str])
          }
          if (this.jpersonVoRules[str]) {
            this.$refs.jpersonVo.validateField([str])
          }
        } else {
          this.$message.error(res.msg)
        }
      } catch (err) {
        console.log(err)
      }
    },
    favoreeChange(type) {
      if (type) {
        this.$set(this.corBaseVo.beneficiaryInfo[0], 'favoreeEnd', '');
        this.favoreeEndTimeDisabled = true;
        this.$refs.corBaseVo.clearValidate(['favoreeEnd']);
        this.corBaseVoRules.beneficiaryInfo.favoreeEnd = [{required: false}];
      } else {
        this.favoreeEndTimeDisabled = false;
        this.corBaseVoRules.beneficiaryInfo.favoreeEnd = [{required: true, message: '请选择证件有效期至', trigger: 'change'}];
      }
    },
    shareholdChange(type) {
      if (type) {
        this.$set(this.corBaseVo.shareholderInfo[0], 'shareholderEnd', '');
        this.shareholderEndTimeDisabled = true;
        this.$refs.corBaseVo.clearValidate(['shareholderEnd']);
        this.corBaseVoRules.shareholderInfo.shareholderEnd = [{required: false}];
      } else {
        this.shareholderEndTimeDisabled = false;
        this.corBaseVoRules.shareholderInfo.shareholderEnd = [{required: true, message: '请选择证件有效期至', trigger: 'change'}];
      }
    },
    longTermChange(type) {
      if (type) {
        this.$set(this.corBaseVo, 'businessEndTime', '');
        this.businessEndTimeDisabled = true;
        this.$refs.corBaseVo.clearValidate(['businessEndTime']);
        this.corBaseVoRules.businessEndTime = [{required: false}];
      } else {
        this.businessEndTimeDisabled = false;
        this.corBaseVoRules.businessEndTime = [{required: true, message: '请输入营业执照的有效期至', trigger: 'change'}];
      }
    },
    jpersonLongTermChange(type) {
      if (type) {
        this.$set(this.jpersonVo, 'cardEndTime', '')
        this.juridicalPersonDisabled = true
        this.$refs.jpersonVo.clearValidate(['cardEndTime'])
        this.jpersonVoRules.cardEndTime = [{required: false}]
      } else {
        this.juridicalPersonDisabled = false
        this.jpersonVoRules.cardEndTime = [{required: true, message: '请选择法人身份证的有效期至', trigger: 'change'}]

      }
    },
    queryBankAccountModifyInfo() {
      queryBankAccountModifyInfo().then((res) => {
        if (res.code === 0) {
          console.log(555555, res);
          const { result } = res;
          if (result) {
            this.hasRecord = true;
            if (result.status === 1) {
              this.hasUnderReview = true;
            }
            if (result.status === 3) {
              this.hasPaymentVerification = true;
            }
          }
        }
      });
    },
    async getInfo() {
      this.hasUnderReview = false;
      this.hasPaymentVerification = false;
      const res = await bankQuery()
      if (res && res.code === 0) {
        console.log(**********, res.result);
        const {accountContactsVo, accountVo, corBaseVo, jpersonVo} = res.result;
        if (corBaseVo.status === 3) {
          this.queryBankAccountModifyInfo();
        } 
        corBaseVo.beneficiaryInfo = Array.isArray(JSON.parse(corBaseVo.beneficiaryInfo)) ? JSON.parse(corBaseVo.beneficiaryInfo) : [JSON.parse(corBaseVo.beneficiaryInfo)]
        corBaseVo.shareholderInfo = Array.isArray(JSON.parse(corBaseVo.shareholderInfo)) ? JSON.parse(corBaseVo.shareholderInfo) : [JSON.parse(corBaseVo.shareholderInfo)]
        this.accountContactsVo = {...accountContactsVo}
        this.accountVo = {...accountVo}
        this.corBaseVo = {...corBaseVo}
        this.jpersonVo = {...jpersonVo}
        const objNameList = ['corBaseVo', 'accountContactsVo']
        objNameList.forEach(key => {
          const cityList = this.util.getLocal(`companyOpenAccount_${key}_cityList`)
          if (cityList) {
            this[key].cityList = cityList
          }
          const areaList = this.util.getLocal(`companyOpenAccount_${key}_areaList`)
          if (areaList) {
            this[key].areaList = areaList
          }
        })
        const endTime = this.formatDate(this.corBaseVo.businessEndTime, 'YMD')
        if (endTime === '2099-12-31') {
          this.longTermChange(true);
          this.corBaseVo.longTerm = true;
        }
        if (this.formatDate(this.corBaseVo.shareholderInfo[0]['shareholderEnd'], 'YMD') === '2099-12-31') {
          this.shareholdChange(true);
          this.corBaseVo.shareholderInfo[0]['longTerm'] = true;
        }
        if (this.formatDate(this.corBaseVo.beneficiaryInfo[0]['favoreeEnd'], 'YMD') === '2099-12-31') {
          this.favoreeChange(true);
          this.corBaseVo.beneficiaryInfo[0]['longTerm'] = true;
        }
        if (Number(this.corBaseVo.status) !== 0) {
          this.disabled = true
          this.bankCardApprovalFailedDisabled = true
        } else {
          this.disabled = false
          this.bankCardApprovalFailedDisabled = false
          this.accountVo.bankName = '';
          this.accountVo.subBankName = '';
        }
        const cardEndTime = this.formatDate(this.jpersonVo.cardEndTime, 'YMD')
        if (cardEndTime === '2999-12-31') {
          this.jpersonLongTermChange(true)
          this.jpersonVo.jpersonLongTerm = true
        }
        setTimeout(() => {
          this.formRefs.forEach(key => {
            this.$refs[key].clearValidate()
          })
        }, 0)
      } else {
        this.$message.error(res.msg || '信息查询失败')
      }
    },
    getParams() {
      const accountContactsVo = {...this.accountContactsVo}
      if (accountContactsVo.cityList && accountContactsVo.cityList.length > 0) {
        this.util.setLocal(`companyOpenAccount_accountContactsVo_cityList`, accountContactsVo.cityList)
      }
      if (accountContactsVo.areaList && accountContactsVo.areaList.length > 0) {
        this.util.setLocal(`companyOpenAccount_accountContactsVo_areaList`, accountContactsVo.areaList)
      }
      delete accountContactsVo.areaList
      delete accountContactsVo.cityList
      const accountVo = {...this.accountVo}
      const corBaseVo = {...this.corBaseVo}
      if (corBaseVo.shareholderInfo[0].longTerm) {
        corBaseVo.shareholderInfo[0].shareholderEnd = new Date('2099-12-31').getTime()
        delete corBaseVo.shareholderInfo[0].longTerm
      }
      if (corBaseVo.beneficiaryInfo[0].longTerm) {
        corBaseVo.beneficiaryInfo[0].favoreeEnd = new Date('2099-12-31').getTime()
        delete corBaseVo.beneficiaryInfo[0].longTerm
      }
      corBaseVo.beneficiaryInfo = JSON.stringify(corBaseVo.beneficiaryInfo)
      corBaseVo.shareholderInfo = JSON.stringify(corBaseVo.shareholderInfo)
      if (corBaseVo.cityList && corBaseVo.cityList.length > 0) {
        this.util.setLocal(`companyOpenAccount_corBaseVo_cityList`, corBaseVo.cityList)
      }
      if (corBaseVo.areaList && corBaseVo.areaList.length > 0) {
        this.util.setLocal(`companyOpenAccount_corBaseVo_areaList`, corBaseVo.areaList)
      }
      delete corBaseVo.areaList
      delete corBaseVo.cityList
      if (corBaseVo.longTerm) {
        corBaseVo.businessEndTime = new Date('2999-12-31').getTime()
      }
      if (this.isSignatory) {
        corBaseVo.signatory = 1;
      }
      if (this.corBaseVo.status === 0) {
        corBaseVo.signatory = 2;
      }
      if ((this.corBaseVo.status === 4 || this.corBaseVo.status === 6) && this.corBaseVo.signatory === 2 ) {
        corBaseVo.signatory = 2;
      }
      if ((this.corBaseVo.status === 4 || this.corBaseVo.status === 6) && this.corBaseVo.signatory === 1 ) {
        corBaseVo.signatory = 1;
      }
      delete corBaseVo.longTerm
      const jpersonVo = {...this.jpersonVo}
      if (jpersonVo.jpersonLongTerm) {
        jpersonVo.cardEndTime = new Date('2999-12-31').getTime()
      }
      delete jpersonVo.jpersonLongTerm
      return {accountContactsVo, accountVo, corBaseVo, jpersonVo}
    },
    async staged() {
      const res = await tempSave(this.getParams())
      if (res && res.code === 0) {
        this.$message.success('暂存成功')
      } else {
        this.$message.error('暂存失败')
      }
    },
    async submit() {
      this.checkForm()
    },
    checkForm() {
      const newList = []
      this.formRefs.forEach(str => {
        const result = new Promise((resolve, reject) => {
          this.$refs[str].validate((valid) => {
            if (valid) {
              resolve()
            } else {
              reject()
            }
          });
        })
        newList.push(result)
      })
      const params = this.getParams()
      if (!params.accountVo.subBankCode) {
        this.$alert('请在下拉列表选择开户支行', '提示', {
          confirmButtonText: '确定',
          callback: action => {
          }
        });
        return false
      }
      Promise.all(newList).then(async () => {
        console.log('全部校验通过')
        this.submitLoading = true
        params.accountContactsVo.codeType = 1;
        const res = await save(params)
        if (res && res.code === 0) {
          this.$message.success('提交成功')
        } else {
          this.$alert(res.msg || '提交失败', '提示', {
            confirmButtonText: '确定',
            callback: action => {
            }
          });
          
        }
        // if (this.isSignatory) {
        //   this.getInfo();
        //   this.getBankList();
        //   this.isSignatory = false;
        // }
        this.getInfo();
        this.getBankList();
        this.isSignatory = false;
        this.submitLoading = false
      }).catch((err) => {
        console.log('校验失败', err)
        this.$message.warning('请将开户信息补充完整')
      })
    },
    viewOperationFlow() {
      this.operationFlowVisible = true
    },
    viewRejectionReasonVisible() {
      this.rejectionReasonVisible = true
    },
    modifySettlementRecord() {
      this.$router.push({
        name: 'modifySettlementRecordPingAn',
        params: {
          corBaseVo: this.corBaseVo,
          accountContactsVo: this.accountContactsVo,
          accountVo: this.accountVo,
          jpersonVo: this.jpersonVo,
          type: 'check',
        }
      })
    },
    modifySettlement() {
      if (this.corBaseVo.signatory === 0) {
        this.modifySettlementObj = {
          registeredName: this.accountVo.registeredName,
          phone: this.accountContactsVo.phone
        }
        this.modifySettlementVisible = true
      } else {
        this.$router.push({
          name: 'modifySettlementRecordPingAn',
          params: {
            corBaseVo: this.corBaseVo,
            accountContactsVo: this.accountContactsVo,
            accountVo: this.accountVo,
            jpersonVo: this.jpersonVo,
            type: 'modify',
          }
        })
      }
    },
    edit() {
      if (+this.corBaseVo.status === 6) {
        this.bankCardApprovalFailedDisabled = false;
      } else {
        this.disabled = false
      }
    },
    sendVerificationCode() {
      this.$refs.accountContactsVo.validateField('phone', async (f) => {
        if (!f) {
          this.sendDisabled = true
          this.timeOutSend()
          const res = await sendActiveCode({phone: this.accountContactsVo.phone, codeType: 1 })
          if (res && res.code === 0) {
            this.$message.success('验证码发送成功')
          } else {
            this.sendDisabled = false
            this.sendCountDown = 60
            this.$message.error(res.msg)
          }
        }
      })
    },
    timeOutSend() {
      setTimeout(() => {
        if (this.sendCountDown > 0) {
          this.sendCountDown--
          this.timeOutSend()
        } else {
          this.sendDisabled = false
          this.sendCountDown = 60
        }
      }, 1000)
    },
    tabClick(tab) {
      const name = '.' + tab.name
      const top = document.querySelector(name).offsetTop
      $('.leftContent').animate({scrollTop: Number(top) - 80}, 500)
    },
    onScroll(e) {
      const scrollTop = e.target.scrollTop + 200
      const firstOffsetTop = document.querySelector('.first').offsetTop
      const secondOffsetTop = document.querySelector('.second').offsetTop
      const thirdOffsetTop = document.querySelector('.third').offsetTop
      const fourthOffsetTop = document.querySelector('.fourth').offsetTop
      if (scrollTop >= firstOffsetTop && scrollTop < secondOffsetTop) {
        this.activeName = 'first'
      } else if (scrollTop >= secondOffsetTop && scrollTop < thirdOffsetTop) {
        this.activeName = 'second'
      } else if (scrollTop >= thirdOffsetTop && scrollTop < fourthOffsetTop) {
        this.activeName = 'third'
      } else if (scrollTop >= fourthOffsetTop) {
        this.activeName = 'fourth'
      }
    }
  }
}
</script>

<style scoped lang="scss">
.el-button {
  padding: 8px 20px;
}

.el-button.is-circle {
  padding: 7px;
  border: none;
}

.companyOpenAccount {
  //min-width: 1400px;
  width: 100%;
  height: 100%;
  position: relative;
  padding-top: 50px;

  .NoCreatedStore {
    width: 100%;
    padding-left: 30px;
    line-height: 77px;
    font-size: 18px;
    color: #ffa012;
    background: #fffaf2;
    position: absolute;
    top: 0;
    left: 0;
  }

  .headerStatus {
    position: absolute;
    top: 0;
    left: 0;
    //min-width: 1400px;
    width: 100%;
    height: 50px;
    background: #fffbf1;
    display: flex;
    justify-content: space-between;
    align-items: center;
    z-index: 100;

    .companyStatus {
      width: 60%;
      display: flex;
      justify-content: start;
      align-items: center;

      .status {
        color: #333333;
        font-size: 16px;
        margin-left: 16px;
      }

      .statusStr {
        margin: 0 16px;
      }
    }
  }

  .leftContent {
    width: 100%;
    height: 100%;
    overflow-y: auto;
    padding: 30px 16px 0;

    ::v-deep   .el-form {
      width: 80%;

      .el-select {
        margin-right: 14px;
      }

      .el-form-item__label {
        font-size: 12px;
        line-height: 30px;
      }

      .el-form-item__content {
        line-height: 30px;
      }

      .el-input__inner {
        line-height: 30px;
        height: 30px;
        font-size: 12px;
      }

      .el-input__icon {
        line-height: 30px;
      }
    }

    ::v-deep   .el-table__body .el-form-item {
      padding: 20px 0;
    }

    .addrForm .el-form-item {
      display: inline-block;
    }

    .avatar-uploader ::v-deep   .el-upload {
      border: 1px dashed #d9d9d9;
      border-radius: 6px;
      cursor: pointer;
      position: relative;
      overflow: hidden;
    }

    .avatar-uploader ::v-deep   .el-upload:hover {
      border-color: #409eff;
    }

    .avatar-uploader-icon {
      font-size: 28px;
      color: #8c939d;
      width: 178px;
      height: 178px;
      line-height: 178px;
      text-align: center;
    }

    .avatar {
      width: 178px;
      height: 178px;
      display: block;
    }
  }

  .leftContent::-webkit-scrollbar {
    width: 0 !important;
  }

  .rightContent {
    position: absolute;
    right: 0;
    top: 75px;
    z-index: 1001;
    background-color: #fff;
  }
}
</style>
