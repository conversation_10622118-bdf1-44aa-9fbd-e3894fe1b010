<template>
  <el-dialog
    :title="actType === 'totalStock' ? '总库存' : '订单占用库存'"
    :visible="true"
    width="600px"
    :before-close="handleCloseModal"
		:close-on-click-modal="false"
	>
    <p>{{ actType === 'totalStock' ? '总库存' : '订单占用库存' }}: {{ totalData }}</p>
  </el-dialog>
</template>

<script>
import { fbpTotalStock, fbpOrderStock } from '@/api/product';

export default {
  name: "totlaModal",
  props: {
    actId: {
      type: Number,
      default: () => {},
    },
    actType: {
			type: String,
			default: () => {},
    },
  },
  data() {
    return {
      totalData: 0,
    }
  },
  mounted() {
    this.getTotal();
  },
  methods: {
    async getTotal() {
      try {
				if (this.actType === 'totalStock') { // 总库存
					const res = await fbpTotalStock({ csuid: this.actId })
					if (res.code === 0) {
						this.totalData = res.data || 0;
					} else {
						this.$message.error(res.message);
					}
				} else if (this.actType === 'occupyStock') { // 订单占用库存
					const res = await fbpOrderStock({ csuid: this.actId })
					if (res.code === 0) {
						this.totalData = res.data || 0;
					} else {
						this.$message.error(res.message);
					}
				}
      } catch (e) {
        console.log(e)
      }
      this.loading = false
    },
    handleCloseModal() {
			this.$emit('handleClose');
    }
  }
}
</script>

<style scoped lang="scss">

</style>
