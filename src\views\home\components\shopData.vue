<template>
  <div class="shopDataContainer">
    <div class="shop_title">
      <span>店铺数据</span>
      <div class="setting" @click="openEditDialog">
        <el-tooltip  effect="dark" content="可增减首页指标内容以及展示顺序" placement="top">
          编辑 <i class="iconfont el-icon-setting"></i>
    </el-tooltip>

      </div>
    </div>
    <dialogList title="今日店铺数据" :list="tadayShopList" :todoConfig="todoConfig" :storeData="storeData" :isTadayData="true"/>
    <dialogList title="售前待办" :list="preTodoList" :todoConfig="todoConfig" />
    <dialogList title="商品管理" :list="goodsManageList" :todoConfig="todoConfig" />
    <dialogList title="售后待办" :list="afterTodoList" :todoConfig="todoConfig" />


    <editDailog ref="editDailogRef" :list="list" @updateShopData="updateShopData" />
  </div>
</template>

<script>
import dialogList from './diaLogList.vue'
import editDailog from './editDialog.vue'
import { mapState } from 'vuex'
import { getOrgIndexModule,waitDeal ,todayData} from '@/api/home/<USER>'
export default {
  components: { editDailog, dialogList },

  data() {
    return {
      storeData: {
        newOrderCount: 0,
        amountPayable: 0,
        refundOrderCount: 0,
        refundTotalMoney: 0,
        newCustomerCount: 0,
        sellingInStockSkuCount: 0
      },
      editDailogRef: null,
      goodsManageList: [],
      tadayShopList: [],
      preTodoList:[],
      afterTodoList:[],
      list: [],
      todoConfig:{

      }
    }
  },
  created(){
    this.getTodayData();
    waitDeal().then(res=>{
      this.todoConfig = res.result
    })
  },
  mounted() {
    if(this.shopConfig.mobile){
      getOrgIndexModule({ regMobile: this.shopConfig.mobile }).then((res) => {
          this.list = res.result
          this.goodsManageList = res.result
            .filter((item) => item.type === 2 && item.sort)
            .sort((a, b) => a.sort - b.sort)
          this.tadayShopList = res.result
            .filter((item) => item.type === 4 && item.sort)
            .sort((a, b) => a.sort - b.sort)
            this.preTodoList = res.result
            .filter((item) => item.type === 1 && item.sort)
            .sort((a, b) => a.sort - b.sort)
            this.afterTodoList = res.result
            .filter((item) => item.type === 3 && item.sort)
            .sort((a, b) => a.sort - b.sort)
        })
    }

  },
  computed: {
    ...mapState('app', ['shopConfig'])
  },

  watch: {
    shopConfig: {
      handler() {
        getOrgIndexModule({ regMobile: this.shopConfig.mobile }).then((res) => {
          this.list = res.result
          this.goodsManageList = res.result
            .filter((item) => item.type === 2 && item.sort)
            .sort((a, b) => a.sort - b.sort)
          this.tadayShopList = res.result
            .filter((item) => item.type === 4 && item.sort)
            .sort((a, b) => a.sort - b.sort)
            this.preTodoList = res.result
            .filter((item) => item.type === 1 && item.sort)
            .sort((a, b) => a.sort - b.sort)
            this.afterTodoList = res.result
            .filter((item) => item.type === 3 && item.sort)
            .sort((a, b) => a.sort - b.sort)
        })
      },
      deep: true
    }
  },
  methods: {
    async getTodayData() {
      const res = await todayData();
      if (res && res.code === 0) {
        Object.keys(res.result)
          .map(key => {
            this.$set(this.storeData, key, res.result[key]);
          });
      }
    },
    openEditDialog() {
      this.$refs.editDailogRef.getData()
      this.$nextTick(()=>{
          this.$refs.editDailogRef.openDialog()
      })
    },
    updateShopData() {
      this.goodsManageList = this.$refs.editDailogRef.dragList
        .filter((item) => item.type === 2 && item.sort)
        .sort((a, b) => a.sort - b.sort)
      this.tadayShopList = this.$refs.editDailogRef.dragList
        .filter((item) => item.type === 4 && item.sort)
        .sort((a, b) => a.sort - b.sort)
        this.preTodoList = this.$refs.editDailogRef.dragList
            .filter((item) => item.type === 1 && item.sort)
            .sort((a, b) => a.sort - b.sort)
            this.afterTodoList = this.$refs.editDailogRef.dragList
            .filter((item) => item.type === 3 && item.sort)
            .sort((a, b) => a.sort - b.sort)
        this.list = [
            ...this.$refs.editDailogRef.preTodo,
            ...this.$refs.editDailogRef.goodsManage,
            ...this.$refs.editDailogRef.afterTodo,
                   ...this.$refs.editDailogRef.tadayShopList,
                  ]
    }

  }
}
</script>

<style scoped lang="scss">
.shopDataContainer {
  width: 100%;
  padding: 16px;
  background: #fff;
  border-radius: 2px;
  margin-bottom: 16px;
}
.shop_title {
  display: flex;
  justify-content: space-between;
  margin-bottom: 16px;
  font-weight: bold;
}
.shop_title span {
  font-size: 18px;
}
.shop_body {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  grid-gap: 16px;
}
.item {
  border: 1px solid #ccc;
  padding: 6px;
}
.setting {
  font-size: 12px;
  color: #777777;
  cursor: pointer;
}
::v-deep   .el-dialog__wrapper{
  // width:1800px;
  position: fixed;
  margin:auto
}
.el-icon-setting{
  font-size: 20px;
}
</style>
