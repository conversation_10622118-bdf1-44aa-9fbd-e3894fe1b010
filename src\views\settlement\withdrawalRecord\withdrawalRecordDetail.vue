<template>
  <div class="withdrawalRecordDetail">
    <div class="contentBox">
      <div class="title">提现记录详情</div>
      <div class="operations">
        <el-button type="primary" @click="goBack" size="small">返回</el-button>
      </div>
      <div class="withdrawalInfo">
        <el-row>
          <el-col :span="8">
            <div class="grid-content bg-purple">提现申请时间：{{ formatDate(cashAdvanceInfo.applyTime, 'YMD') }}</div>
          </el-col>
          <el-col :span="8">
            <div class="grid-content bg-purple-light">提现申请金额：{{ cashAdvanceInfo.applyAmount }}</div>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <div class="grid-content bg-purple">提现手续费：{{ cashAdvanceInfo.fee }}</div>
          </el-col>
          <el-col :span="8">
            <div class="grid-content bg-purple-light">实际到账金额：{{ cashAdvanceInfo.realityAmount }}</div>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <div class="grid-content bg-purple">提现账号名：{{ cashAdvanceInfo.accountName }}</div>
          </el-col>
          <el-col :span="8">
            <div class="grid-content bg-purple-light">账号：{{ cashAdvanceInfo.accountNum }}</div>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <div class="grid-content bg-purple">开户行：{{ cashAdvanceInfo.accountBank }}</div>
          </el-col>
          <el-col :span="8">
            <div class="grid-content bg-purple">开户支行：{{ cashAdvanceInfo.accountSubBank }}</div>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <div class="grid-content bg-purple">备注/原因：{{ cashAdvanceInfo.reason }}</div>
          </el-col>
          <el-col :span="8">
            <div class="grid-content bg-purple-light">打款状态：{{ getPaymentStatus(Number(cashAdvanceInfo.paymentStatus))}}
            </div>
          </el-col>
        </el-row>
      </div>
      <xyyTable
        ref="withdrawalRecordDetail"
        v-loading="tableLoading"
        :data="tableConfig.data"
        :col="tableConfig.col"
        :list-query="tableConfig.pagConfig"
        @get-data="queryList"
      />
    </div>
  </div>
</template>

<script>
import { getCashAdvanceInfo, getCashAdvanceDetail } from '@/api/settlement/withdrawal';

export default {
  name: 'WithdrawalRecordDetail',
  data() {
    return {
      cashAdvanceNum: '',
      cashAdvanceInfo: {},
      tableLoading: false,
      tableConfig: {
        data: [],
        col: [
          {
            name: '账单号',
            index: 'billNo',
            width: 180
          },
		  {
			index: 'popOrderId',
			name: '订单ID',
			width: 200
		  },
          {
            name: '佣金结算方式',
            index: 'settlementTypeName',
            width: 120
          },
          {
            name: '账单状态',
            width: 120,
            index: 'billPaymentStatusName'
          },
          {
            name: '生成时间',
            width: 180,
            index: 'billCreateTimeStr'
          },
          {
            name: '入账时间',
            width: 180,
            index: 'billPaymentTimeStr'
          },
          {
            name: '单据号',
            width: 240,
            index: 'businessNo'
          },
          {
            name: '单据类型',
            width: 180,
            index: 'businessTypeName'
          },
          {
            name: '客户ERP编码',
            width: 240,
            index: 'sellerUserId'
          },
          {
            name: '客户名称',
            width: 180,
            index: 'merchantName'
          },
          {
            name: '商品金额',
            index: 'productMoney'
          },
          {
            name: '运费金额',
            index: 'freightAmount'
          },
          {
            name: '单据总额（含运费）',
            index: 'totalMoney',
            width: 150,
          },
          {
            name: '店铺总优惠',
            index: 'shopTotalDiscount'
          },
          {
            name: '平台总优惠',
            index: 'platformTotalDiscount'
          },
          {
            name: '实付金额',
            index: 'money'
          },
          {
            name: '佣金金额',
            index: 'hireMoney',
            renderHeader: (h, { column }) => {
              return h(
                'div', [
                  h('span', column.label),
                  h('el-tooltip', {
                    props: {
                      content: '单据中每个商品的佣金金额=（商品的实付金额+商品分摊的平台优惠金额）*下单时刻商品的佣金比例 单据的佣金金额=单据中所有商品的佣金金额求和',
                      placement: 'right',
                    },
                  }, [
                    h('i', { class: 'el-icon-warning-outline' }),
                  ]),
                ],
              );
            },
          },
          {
            name: '应结算金额',
            index: 'statementTotalMoney',
            renderHeader: (h, { column }) => {
              return h(
                'div', [
                  h('span', column.label),
                  h('el-tooltip', {
                    props: {
                      content: 'a、当单据佣金结算方式为“非月结”且支付方式为“在线支付、线下转账电汇平台“，应结算金额=单据实付金额-（佣金金额-平台总优惠）\nb、当单据佣金结算方式为“月结”且支付方式为“在线支付、线下转账电汇平台“，应结算金额=单据实付金额\nc、当单据佣金结算方式为“月结”且支付方式为“线下转账电汇商业“，应结算金额=0',
                      placement: 'right',
                    },
                  }, [
                    h('i', { class: 'el-icon-warning-outline' }),
                  ]),
                ],
              );
            },
          },
          {
            name: '应缴纳佣金',
            index: 'deductedCommission',
            renderHeader: (h, { column }) => {
              return h(
                'div', [
                  h('span', column.label),
                  h('el-tooltip', {
                    props: {
                      content: '补贴冲抵佣金商业：应缴纳佣金=佣金金额*佣金折扣-平台总优惠\n补贴不冲抵佣金商业：应缴纳佣金=佣金金额*佣金折扣',
                      placement: 'right',
                    },
                  }, [
                    h('i', { class: 'el-icon-warning-outline' }),
                  ]),
                ],
              );
            },
          },
          {
            name: '支付时间',
            index: 'orderPayTimeStr',
            width: 180
          }
        ],
        pagConfig: {
          pageSize: 10,
          page: 1,
          total: 0
        }
      }
    }
  },
  created() {
    this.search()
  },
  activated() {
    if (this.cashAdvanceNum && this.cashAdvanceNum !== this.$route.query.cashAdvanceNum) {
      this.search()
    }
  },
  methods: {
    getPaymentStatus(type){
      let statusArr = ['','待核款', '已打款', '打款失败', '退汇', '待打款', '打款中']
      return statusArr[type]
    },
    search(){
      this.cashAdvanceNum = this.$route.query.cashAdvanceNum
      this.queryCashAdvanceInfo()
      this.queryList()
    },
    goBack() {
      this.$router.push({path: '/withdrawalRecord'})
    },
    async queryCashAdvanceInfo() {
      try {
        const res = await getCashAdvanceInfo({cashAdvanceNum: this.cashAdvanceNum})
        if (res && res.code === 0) {
          this.cashAdvanceInfo = {...res.data}
        }
      } catch (e) {
        console.log(e)
      }
    },
    async queryList(listQuery) {
      this.tableLoading = true
      const params = {cashAdvanceNum: this.cashAdvanceNum}
      if (listQuery) {
        const {pageSize, page} = listQuery
        this.tableConfig.pagConfig.pageSize = pageSize
        this.tableConfig.pagConfig.page = page
      }
      const {pageSize, page} = this.tableConfig.pagConfig
      params.pageNum = page
      params.pageSize = pageSize
      try {
        const res = await getCashAdvanceDetail(params)
        if (res && res.code === 0) {
          this.tableConfig.data = res.data.list
          this.tableConfig.pagConfig.total = res.data.total
        }
      } catch (e) {
        console.log(e)
      }
      this.tableLoading = false
    }
  }
}
</script>

<style scoped lang="scss">
.contentBox {
  //height: 100%;
  padding: 16px 16px;
  background: #fff;
  margin-bottom: 10px;
  position: relative;

  .title {
    font-weight: 500;
    text-align: left;
    color: #000000;
    line-height: 14px;
    margin-bottom: 24px;
  }

  .title:before {
    content: '';
    display: inline-block;
    width: 3px;
    height: 13px;
    background: linear-gradient(360deg, #1d69c4 1%, #8bbdfc 99%);
    border-radius: 2px;
    margin-right: 8px;
    vertical-align: middle;
  }

  .operations {
    position: absolute;
    top: 16px;
    right: 16px;
  }

  .withdrawalInfo {
    padding: 0 11px;
  }

  .el-row {
    margin-bottom: 20px;
  }
}
</style>
<style>
  .el-tooltip__popper {
    white-space: pre-line;
  }
</style>
