<template>
  <div class="main-box">
    <div class="Fsearch">
      <el-row type="flex" align="middle" justify="space-between" class="my-row">
        <el-row type="flex" align="middle">
          <span class="sign" />
          <div class="searchMsg">控销商品列表</div>
        </el-row>
      </el-row>
    </div>
    <div class="explain-search searchMy" style="padding: 0 20px">
      <el-form ref="ruleForm" size="small" :inline="true">
        <el-form-item>
          <el-input v-model.trim="searchData.barcode" placeholder="商品编码">
            <template slot="prepend">商品编码</template>
          </el-input>
        </el-form-item>
        <el-form-item>
          <el-input v-model.trim="searchData.showName" placeholder="商品展示名称">
            <template slot="prepend">商品展示名称</template>
          </el-input>
        </el-form-item>
        <el-form-item>
          <el-input v-model.trim="searchData.approvalNumber" placeholder="批准文号">
            <template slot="prepend">批准文号</template>
          </el-input>
        </el-form-item>
        <el-form-item>
          <el-input v-model.trim="searchData.code" placeholder="商品条码">
            <template slot="prepend">商品条码</template>
          </el-input>
        </el-form-item>
        <el-form-item>
          <el-input v-model.trim="searchData.erpCode" placeholder="商户ERP编码">
            <template slot="prepend">商户ERP编码</template>
          </el-input>
        </el-form-item>
        <el-form-item>
          <span class="search-title">状态</span>
          <el-select v-model.trim="searchData.status" placeholder="请选择">
            <el-option label="全部" value />
            <el-option label="停用" :value="0" />
            <el-option label="启用" :value="1" />
          </el-select>
        </el-form-item>

        <el-form-item>
          <span class="search-title">商品类型</span>
          <el-select v-model.trim="searchData.productType" placeholder="请选择">
            <el-option label="全部" value />
            <el-option label="普通商品" :value="1" />
            <el-option label="活动商品" :value="3" />
          </el-select>
        </el-form-item>

        <el-form-item style="width: 48%">
          <span class="search-title">生效时间</span>
          <div style="display: table-cell; line-height: 24px">
            <el-date-picker
              v-model="searchData.startTime"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              format="yyyy-MM-dd HH:mm:ss"
              value-format="timestamp"
              :default-time="['00:00:00', '23:59:59']"
            />
          </div>
        </el-form-item>
        <el-form-item style="width: 48%;margin-right: 20px">
          <span class="search-title">创建时间</span>
          <div style="display: table-cell; line-height: 24px">
            <el-date-picker
              v-model="searchData.createTime"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              format="yyyy-MM-dd HH:mm:ss"
              value-format="timestamp"
              :default-time="['00:00:00', '23:59:59']"
            />
          </div>
        </el-form-item>
        <el-form-item>
          <el-input v-model.trim="searchData.controlSaleName" placeholder="控销名称">
            <template slot="prepend">控销名称</template>
          </el-input>
        </el-form-item>
        <el-form-item class="search-btn" style="text-align: right;padding-right: 20px">
          <el-button type="primary" @click="getList('search')">查询</el-button>
          <el-button @click="resetForm('ruleForm')">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="list-box">
      <!-- <div class="con-title">
        <span class="line"></span><span>控销商品列表</span>
      </div>-->
      <div style="padding: 10px 20px">
        <el-row style="padding-bottom: 20px">
          <!-- <el-button v-permission="['product_control_add']" class="xyy-blue" type="primary" size="small" @click="addControlSell">添加控销设置</el-button> -->
          <!-- <el-button v-permission="['product_control_batchDelete']" size="small" @click="removeTableData">批量删除</el-button> -->
          <el-button v-permission="['product_control_export']" size="small" @click="exportExcel">导出</el-button>
          <!-- <el-popover v-model="visible" placement="bottom">
            <div style="text-align: center; margin: 0">
              <el-button
                type="text"
                size="medium"
                style="color: #333333;font-weight: 400; font-size:14px;"
                @click="toBatchOperation"
              >批量调整控销数据</el-button>
            </div>
            <el-button
              v-permission="['product_control_batchUpdate']"
              slot="reference"
              size="small"
              class="search-btn edit-btn"
              type="text"
              icon="el-icon-edit-outline"
            >批量修改</el-button>
          </el-popover> -->
        </el-row>

        <div class="customer-tabs">
          <el-table
            ref="goodTable"
            v-loading="laodingBoole"
            max-height="397"
            :data="tableData.list"
            stripe
            style="width: 100%"
            :header-cell-style="{ background: '#F9F9F9', color: '#666666' }"
            @selection-change="seleteShopHander"
          >
            <el-table-column type="selection" width="55" />
            <el-table-column label="商品信息" width="300">
              <template slot-scope="scope">
                <el-row type="flex" justify="start" align="middle">
                  <img class="imgInfo" :src="scope.row.imageUrl" />
                  <div style=" float: left;">
                    <div>{{ scope.row.commonName }}</div>
                    <div style="font-size: 12px;color: #999999;">{{ scope.row.erpCode }}</div>
                  </div>
                </el-row>
              </template>
            </el-table-column>
            <el-table-column prop="controlSaleName" label="控销名称" width="220"/>
            <el-table-column prop="id" label="控销ID" />
            <!-- <el-table-column
              prop="controlAreaName"
              label="可售区域"
              show-overflow-tooltip
            ></el-table-column>-->
            <el-table-column prop="barcode" label="商品编码" />
            <!-- <el-table-column
              prop="userTypeName"
              label="可售商户类型"
               show-overflow-tooltip
                width="170"
            ></el-table-column>-->
            <el-table-column label="状态">
              <template slot-scope="scope">
                <span v-if="scope.row.status == 1">启用</span>
                <span v-else>停用</span>
              </template>
            </el-table-column>
            <el-table-column prop="startTime" label="生效时间">
              <!-- <template slot-scope="scope">
                <span>{{ scope.row.startTime }}至</span><br />
                <span>{{ scope.row.endTime }}</span>
              </template>-->
            </el-table-column>
            <el-table-column prop="createTime" label="创建时间" width="220" />

            <el-table-column fixed="right" label="操作" width="60">
              <template slot-scope="scope">
                <el-button v-permission="['product_control_edit']" type="text" size="small" @click="edit(scope.row)">编辑</el-button>
                <!-- <el-button v-permission="['product_control_delete']" type="text" size="small" @click="handledelClick(scope.row)">删除</el-button> -->
              </template>
            </el-table-column>

            <template slot="empty">
              <div class="noData">
                <p class="img-box">
                  <img src="@/assets/image/marketing/noneImg.png" alt />
                </p>
                <p>暂无数据</p>
              </div>
            </template>
          </el-table>
          <div class="explain-pag">
            <Pagination
              v-show="tableData.total > 0"
              :total="tableData.total"
              :page.sync="pageData.pageNum"
              :limit.sync="pageData.pageSize"
              @pagination="getList"
            />
          </div>
        </div>
      </div>
    </div>
    <export-tip
      :change-export="changeExport"
      @handleExoprClose="handleExoprClose"
      @handleChangeExport="handleChangeExport"
    />
  </div>
</template>
<style>
@import '../../../assets/css/changeElement.scss';
</style>
<script>
import Pagination from '@/components/Pagination';
import utils from '@/utils/filter';
import exportTip from '@/views/other/components/exportTip';
import {
  getControlList,
  getControlOne,
  deletebBatchDel,
  exportControlSale,
} from '@/api/goods/controlGoods.js';
import {actionTracking} from "@/track/eventTracking";

export default {
  name: 'ControlList',
  data() {
    return {
      searchData: {
        barcode: '', // 商品编码
        showName: '', // 展示名称
        code: '', // 商品条码
        approvalNumber: '', // 批准文号
        status: '', // 状态
        erpCode: '', // ERP编码
        productType: '', // 商品类型，1普通商品，2秒杀，3活动商品
        startTime: [],
        createTime: [],
        controlSaleName: ''
      },
      tableData: {
        total: 0,
        list: [],
      },
      pageData: {
        pageSize: 20,
        pageNum: 1,
      },
      laodingBoole: false,
      sellerAry: [],
      checkListAry: [],
      detailData: {},
      visible: false,
      changeExport: false,
    }
  },
  components: {
    Pagination,
    exportTip
  },
  created() {},
  activated() {
    const barcode = this.$route.query.barcode;
    if (barcode) {
      this.searchData.barcode = barcode;
    }
    // 获取列表数据
    this.getList();
  },
  methods: {
    getParams() {
      const param = {
        ...this.searchData,
        ...this.pageData
      };
      if (Array.isArray(param.startTime)){
        param.validStartTime = this.formatDate(param.startTime[0]);
        param.validEndTime = this.formatDate(param.startTime[1]);
      } else {
        param.validStartTime = '';
        param.validEndTime = '';
      }
      delete param.startTime;
      if (Array.isArray(param.createTime)) {
        param.createStartTime = this.formatDate(param.createTime[0]);
        param.createEndTime = this.formatDate(param.createTime[1]);
      } else {
        param.createStartTime = '';
        param.createEndTime = '';
      }
      delete param.createTime;
      return param
    },
    // 获取列表数据
    getList(from) {
      const that = this;
      if (from == 'search') {
        this.pageData.pageNum = 1;
      }
      this.laodingBoole = true;
      const param = this.getParams();
      getControlList(param).then((res) => {
        if (res.code === 0) {
          this.laodingBoole = false;
          if (res.data) {
            that.tableData.list = res.data.list;
            let createTime = {};
            let startTime = {};
            let endTime = {};
            this.tableData.list.forEach((item, index) => {
              createTime = {};
              startTime = {};
              endTime = {};
              if (item.createTime == null) {
                createTime = '-';
              } else {
                createTime = utils.dataTime(
                  item.createTime,
                  'yy-mm-dd HH:ss:nn',
                );
              }
              if (item.startTime == null) {
                startTime = '-';
              } else {
                startTime = utils.dataTime(
                  item.startTime,
                  'yy-mm-dd HH:ss:nn',
                );
              }
              if (item.endTime == null) {
                endTime = '-';
              } else {
                endTime = utils.dataTime(
                  item.endTime,
                  'yy-mm-dd HH:ss:nn',
                );
              }
              this.tableData.list[index] = Object.assign({}, this.tableData.list[index], {
                createTime,
                startTime,
                endTime,
              });
            });
          } else {
            that.tableData.list = [];
          }
          that.tableData.total = res.data.total;
        } else {
          this.laodingBoole = false;
          this.$message({
            message: res.message,
            type: 'error',
          });
        }
      }).catch(() => {});
    },
    // 重置列表数据
    resetForm() {
      this.searchData = {
        barcode: '', // 商品编码
        showName: '', // 展示名称
        code: '', // 商品条码
        approvalNumber: '', // 批准文号
        status: '', // 状态
        erpCode: '', // ERP编码
        productType: '', // 商品类型，1普通商品，2秒杀，3活动商品
        startTime: [],
        createTime: [],
        controlSaleName: ''
      };
      this.pageData = {
        pageSize: 20,
        pageNum: 1,
      };
      this.getList();
    },
    // 选择列表数据
    seleteShopHander(val) {
      this.checkListAry = val;
    },
    // 单个删除
    handledelClick(data) {
      actionTracking('control_delete', {
         id : data.id
      })
      const ids = [];
      if (data) {
        ids.push(data.id);
      }
      if (ids) {
        this.$confirm('确定要删除选择的控销商品吗？删除后将无法恢复', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: '',
          customClass: 'delete-dialog-customClass',
        })
          .then(() => {
            deletebBatchDel(JSON.stringify(ids)).then((res) => {
              if (res.code == 0) {
                this.$message({
                  message: '删除成功',
                  type: 'success',
                });
                this.getList();
              } else {
                this.$message({
                  message: '删除失败',
                  type: 'error',
                });
              }
            });
          })
          .catch(() => {
          });
      }
    },
    // 编辑
    edit(val) {
      if (val.id) {
        actionTracking('control_modification', {
          id : val.id
        })
        getControlOne({ id: val.id }).then((res) => {
          if (res.code == 0) {
            res.data.sku.initialData = true
            this.detailData = res.data;
            this.$emit('goNext', { from: 'addCon', data: this.detailData });
          } else {
            this.$message({
              message: res.message,
              type: 'error',
            });
          }
        });
      }
    },
    // 添加控销
    addControlSell() {
      actionTracking('control_click', {})
      this.$emit('goNext', { from: 'addCon', data: '' });
    },
    // 跳转批量操作
    toBatchOperation() {
      // actionTracking('control_batch_modification', {})
      this.visible = false;
      this.$emit('goNext', { from: 'batchOpe', data: '' });
    },
    // 批量删除
    removeTableData() {
      actionTracking('control_batch_delete', {})
      if (this.checkListAry.length > 0) {
        const ids = [];
        this.checkListAry.forEach((item) => {
          ids.push(item.id);
        });
        this.$confirm('确定要删除选择的控销商品吗？删除后将无法恢复', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: '',
          customClass: 'delete-dialog-customClass',
        })
          .then(() => {
            deletebBatchDel(JSON.stringify(ids)).then((res) => {
              if (res.code == 0) {
                this.$message({
                  message: '删除成功',
                  type: 'success',
                });
                this.getList();
              } else {
                this.$message({
                  message: '删除失败',
                  type: 'error',
                });
              }
            });
          })
          .catch(() => {
            this.$refs.goodTable.clearSelection();
          });
      } else {
        this.$message({
          message: '请选中数据再批量删除',
          type: 'warning',
        });
      }
    },
    // 导出
    exportExcel() {
      actionTracking('control_export', {})
      const that = this;
      if (!that.tableData.list || !that.tableData.list.length) {
        that.$message({
          type: 'info',
          message: '无导出数据！',
        });
        return false;
      }
      const param = this.getParams()
      delete param.pageSize
      delete param.pageNum
      exportControlSale(param).then(res => {
        // if (res.hasOwnProperty('code')) {
        //    that.$message.error(res.message);
        // } else {
        //   // 申请接口带入参数查询数据
        //   let url = `${process.env.VUE_APP_BASE_API}/salesControl/manage/exportControlProducts?barcode=${this.searchData.barcode}&showName=${this.searchData.showName}&code=${this.searchData.code}&approvalNumber=${this.searchData.approvalNumber}&status=${this.searchData.status}&erpCode=${this.searchData.erpCode}`;
        //   const a = document.createElement('a');
        //   a.href = url;
        //   a.click();
        // }
        if (res.code !== 0) {
          this.$message.error(res.message);
          return;
        }
        this.changeExport = true;
      });
    },
    handleChangeExport(info) {
      // const that = this;
      this.changeExport = false;
      if (info === 'go') {
        const path = '/downloadList'
        window.openTab(path)
        // that.$router.push({ path: '/downloadList' });
      }
    },
    handleExoprClose() {
      this.changeExport = false;
    },
  }
}
</script>

<style lang="scss" scoped>
// @import '../../../assets/css/market';
.searchMy ::v-deep  .el-form-item__label {
  margin-left: 20px;
  padding: 0;
}
.searchMy ::v-deep  .el-input__inner {
  border-radius: 0 4px 4px 0;
}
.searchMy ::v-deep  .el-date-editor {
  width: 100%;
}
.search-title {
  display: table-cell;
  padding: 0 10px;
  text-align: center;
  border: 1px solid #dcdfe6;
  height: 30px;
  line-height: 30px;
  vertical-align: middle;
  border-right: none;
  border-radius: 4px 0 0 4px;
  color: #333333;
  white-space: nowrap;
}
.searchMy ::v-deep  .el-select {
  display: table-cell;
  width: 100%;
}
.searchMy ::v-deep  .el-form-item__content {
  width: 100%;
}
.searchMy ::v-deep  .el-form-item--small.el-form-item {
  margin-bottom: 10px;
  width: 32%;
}
.searchMy ::v-deep  .el-form-item--small .el-form-item__content {
  line-height: 30px;
  width: 100%;
}
.searchMy ::v-deep  .el-input-group__prepend {
  background: none;
  color: #333333;
  padding: 0 10px;
}
.Fsearch {
  padding: 0px 20px 10px;
  font-weight: 500;
  .searchMsg {
    font-weight: 700;
    width: 200px;
  }
  .sign {
    display: inline-table;
    width: 3px;
    height: 13px;
    background: linear-gradient(360deg, #1d69c4 1%, #8bbdfc 99%);
    border-radius: 2px;
    margin-right: 8px;
  }
}
.searchMy ::v-deep   .el-form-item--small.el-form-item {
  width: 24%;
}
.con-inner {
  padding-top: 15px;
  padding-left: 23px;
  margin-right: 17px;
  padding-bottom: 10px;
  border-bottom: 1px solid #efefef;
  div {
    display: inline-block;
  }
  .img {
    width: 92px;
    height: 92px;
    img {
      width: 100%;
      height: 100%;
    }
  }
  .text {
    padding-left: 20px;
    vertical-align: top;
    h3 {
      font-size: 14px;
      color: #000000;
      padding: 0;
      margin: 0;
    }
    p {
      padding: 0;
      margin: 0;
      font-size: 12px;
      color: #333333;
      padding-top: 10px;
    }
  }
  .btn {
    float: right;
    padding-top: 26px;
    button {
      width: 100px;
      height: 30px;
      text-align: center;
      line-height: 30px;
      padding: 0;
      background: rgba(65, 131, 213, 1);
      border-color: rgba(65, 131, 213, 1);
      border-radius: 4px;
      font-size: 14px;
    }
    a {
      color: #ffffff;
      text-decoration: none;
    }
    .router-link-active {
      color: #ffffff;
      text-decoration: none;
    }
  }
}
.pag-info {
  width: 500px;
}
.main-box {
  .list-box {
    .customer-tabs {
      .el-button + .el-button {
        margin-left: 0px;
      }
    }
  }
}

.search-btn {
  float: right;
}
.imgInfo {
  width: 80px;
  height: 80px;
  margin-right: 16px;
}
::v-deep  .el-table {
  .el-checkbox__inner {
    border: 1px solid #000000;
  }
}
</style>
