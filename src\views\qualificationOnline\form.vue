<template>
  <div>
    <CommonHeader :showFold="false" :shouHeightLine="false">
      <template slot="title">
        <goOpen :from="1"></goOpen>
      </template>
      <template slot="header-right">
        <el-button size="small" type="primary" @click="$emit('search', [{key: 'pageNum', value: 1}])">查询</el-button>
        <el-button size="small" type="primary" @click="go">批量上传</el-button>
        <el-button size="small" type="primary" @click="$emit('asyncExport')">导出</el-button>
			</template>
			<div>
				<el-form label-position="right" label-width="10px" style="margin-top:10px;">
					<el-row>
						<el-col :xs="16" :md="12" :lg="6" :xl="6">
							<el-form-item>
                <el-input placeholder="商品编码 / ERP编码 / CSUID" size="small" v-model="form[formKeys[0].prop]" clearable>
                  <template slot="prepend">商品编码</template>
                </el-input>
              </el-form-item>
						</el-col>
            <el-col :xs="16" :md="12" :lg="6" :xl="6">
							<el-form-item>
                <el-input placeholder="商品名称" size="small" v-model="form[formKeys[1].prop]" clearable>
                  <template slot="prepend">商品名称</template>
                </el-input>
              </el-form-item>
						</el-col>
            <el-col :xs="16" :md="12" :lg="6" :xl="6">
							<el-form-item>
                <el-input placeholder="批准文号" size="small" v-model="form[formKeys[2].prop]" clearable>
                  <template slot="prepend">批准文号</template>
                </el-input>
              </el-form-item>
						</el-col>
            <el-col :xs="16" :md="12" :lg="6" :xl="6">
							<el-form-item>
                <el-input placeholder="生产厂家" size="small" v-model="form[formKeys[3].prop]" clearable>
                  <template slot="prepend">生产厂家</template>
                </el-input>
              </el-form-item>
						</el-col>
            <el-col :xs="16" :md="12" :lg="8" :xl="6">
							<el-form-item label-width="260px">
                <template slot="label">
                  <div style="font-size: 0;">
                    <ISelect style="margin-left: 10px;font-weight: 500;" label="申请下载次数" :list="selectList.downloadCount"  v-model="form[formKeys[4].prop]"></ISelect>
                  </div>
                </template>
                <div>
                  <el-input style="display: flex;" :disabled="!form[formKeys[4].prop]" placeholder="数量" size="small" :value="form[formKeys[5].prop]" @input="valueInput($event, formKeys[5].prop, /^(0|[1-9][0-9]*)?$/)" clearable>
                  </el-input>
                </div>
              </el-form-item>
						</el-col>
            <el-col :xs="16" :md="12" :lg="8" :xl="6">
							<el-form-item>
                <ISelect style="font-weight: 500;" label="是否上传资质" :list="selectList.isUpload"  v-model="form[formKeys[6].prop]"></ISelect>
              </el-form-item>
						</el-col>
          </el-row>
        </el-form>
      </div>
    </CommonHeader>
  </div>
</template>
<script>
import CommonHeader from '../afterSaleManager/components/common-header.vue';
import ISelect from './components/i-select.vue'
import goOpen from './components/goOpen.vue'
import { formSelectList } from './data.js'
export default {
  components: {
    CommonHeader,
    ISelect,
    goOpen
  },
  props: {
    value: {
      default: () => {},
      type: Object
    },
    isHomeEnter: {
      type: [Number, String],
      default: 0
    }
  },
  data() {
    return {
      form: {},
      //formKeys 需要按照在模板中的顺序添加
      formKeys: [{
        prop: 'code',
        default: ''
      }, {
        prop: 'productName',
        default: ''
      }, {
        prop: 'approvalNumber',
        default: ''
      }, {
        prop: 'manufacturer',
        default: ''
      }, {
        prop: 'applyType',
        default: ''
      }, {
        prop: 'applyDownloadCount',
        default: ''
      }, {
        prop: 'isUpload',
        default: ''
      }],
      selectList: formSelectList
    }
  },
  watch: {
    form: {
      handler(newVal) {
        this.$emit('input', newVal);
      },
      deep: true
    },
    isHomeEnter(newVal) {
      this.reset()
      if (newVal == '1') {
        this.form.applyType = 3;
        this.form.applyDownloadCount = 0;
        this.form.isUpload = 0;
        this.$nextTick(() => {
          this.$emit("search");
        })
      }
    }
  },
  created() {
    this.reset()
  },
  methods: {
    valueInput(val, key, exp) {
      if (exp.test(val)) {
        this.form[key] = val;
      }
    },
    go() {
      window.openTab('/qualificationMultipartUpload', {
        from: 1
      })
    },
    reset() {
      const res = {}
      this.formKeys.forEach(item => {
        res[item.prop] = item.default;
      })
      this.form = res
    }
  }
}
</script>
<style scoped>
</style>
