import request from '@/utils/request';

export default {
  /**
   * get方法，对应get请求
   * @param {String} url [请求的url地址]
   * @param {Object} params [请求时携带的参数]
   */
  get(url, params, config) {
    return new Promise((resolve, reject) => {
      request
        .get(url, {
          params,
          ...config,
        })
        .then((res) => {
          resolve(res);
        })
        .catch((err) => {
          reject(err);
        });
    });
  },
  /**
   * post方法，对应post请求
   * @param {String} url [请求的url地址]
   * @param {Object} params [请求时携带的参数]
   */
  post(url, params, config) {
    return new Promise((resolve, reject) => {
      request
        .post(url, params, config)
        .then((res) => {
          resolve(res);
        })
        .catch((err) => {
          reject(err);
        });
    });
  },
  /**
   * postFormData方法，对应post请求，用来提交文件+数据
   * @param {String} url [请求的url地址]
   * @param {Object} params [请求时携带的参数]
   */
  postFormData(url, params, config) {
    return new Promise((resolve, reject) => {
      request({
        headers: { 'Content-Type': 'multipart/form-data' },
        transformRequest: [
          (data) => {
            // 在请求之前对data传参进行格式转换
            const formData = new FormData();
            Object.keys(data).forEach((key) => {
              formData.append(key, data[key]);
            });
            return formData;
          },
        ],
        url,
        method: 'post',
        data: params,
        ...config,
      })
        .then((res) => {
          resolve(res);
        })
        .catch((err) => {
          reject(err);
        });
    });
  },
};
