<template>
  <div class="main-box" v-loading="loading">
    <part-title title="平台公告" />
    <div class="searchMy">
      <el-form ref="form" class="fix-item-width"  :model="formData" >
        <div class="head">
          <div style="font-size: 16px; font-weight: 500;">平台公告</div>
          <el-form-item >
            <el-input placeholder="请输入公告名称" clearable v-model="formData.title" suffix-icon="el-icon-search" @keyup.enter.native="searchData()" @clear="searchData()">
            </el-input>
          </el-form-item>
        </div>
      </el-form>
    </div>
    <div>
      <el-table
        :data="noticeData.list"
        max-height="550px"
        :header-cell-style="{ color: '#4183d5' ,borderBottom:'3px solid #4183d5' ,fontWeight: '500',padding:'10px 0 5px'}"
        @row-click="showNoticeHandle"
        @sort-change="handleSortChange"
      >
        <el-table-column label="公告名称" prop="title" >
          <template slot-scope="scope">
            <div style="display: flex; text-align: center;">
            <div v-if="scope.row.redTitle" class="importent"> 重要</div>
            <span>{{ scope.row.title}}</span>
            </div>

          </template>
        </el-table-column>
        <el-table-column label="公告分类" prop="announceTypeDesc" width="250">
          <template #header>
            <el-dropdown trigger="click" @command="handleCommand">
              <span class="el-dropdown-link">
                公告分类<i class="el-icon-caret-bottom el-icon--right" style="color: #C0C4CC;"></i>
              </span>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item v-for="item in announceTypeEnum" :key="item.value" :command="item.value" :style="item.value === formData.type ? { color: '#4183d5' } : {}">{{ item.text }}</el-dropdown-item>

              </el-dropdown-menu>
            </el-dropdown>

          </template>

        </el-table-column>
        <el-table-column label="创建时间" prop="createTime" width="250" sortable="custom">
          <template slot-scope="scope">
            <span>{{ scope.row.createTime | formatDate }}</span>
          </template>
        </el-table-column>
      </el-table>
      <el-row>
        <div class="pagination-container">
          <div class="pag-text">
            共 {{ noticeData.total }} 条数据，每页{{ pageInfo.pageSize }}条，共{{
              Math.ceil(noticeData.total / pageInfo.pageSize) || 0
            }}页
          </div>
          <el-pagination
            background
            :page-sizes="[10, 20, 30, 50]"
            prev-text="上一页"
            next-text="下一页"
            layout=" prev, pager, next, jumper"
            :total="noticeData.total"
            :current-page="pageInfo.pageNum"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </el-row>
    </div>

    <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="70%">
      <div v-html="dialogContent"></div>
      <span slot="footer" class="dialog-footer"> </span>
    </el-dialog>
  </div>
</template>
<script>
import { getBannerPageList } from '@/api/home'
export default {
  name: 'noticeListPage',
  components: {},
  data() {
    return {
      value:'',
      loading: false,
      formData: {
        title:'',
        type: '',
        status: 1,
        sortType: 1
      },
      noticeData: {
        list: [],
        total: 0
      },
      pageInfo: {
        pageNum: 1,
        pageSize: 10
      },
      announceTypeEnum: [
        {
          value:'',
          text:'全部'
        },
        {
          value: 1,
          text: '平台规则'
        },
        {
          value: 2,
          text: '平台通知'
        },
        {
          value: 3,
          text: '药品质量公告'
        },
        {
          value: 4,
          text: '医疗器械质量公告'
        },
        {
          value: 5,
          text: '化妆品质量公告'
        },
        {
          value: 6,
          text: '其他'
        }
      ],
      dialogVisible: false,
      dialogTitle: '',
      dialogContent: ''
    }
  },
  created() {},
  mounted() {
    this.searchData()
  },
  activated() {
    if (this.$route.query.refresh) {
      this.getNoticeList()
    }
  },
  methods: {
    //时间排序
    handleSortChange( {order} ){
      if(order === 'descending'){
        this.formData.sortType = 1
      } else {
        this.formData.sortType = 0
      }
      this.searchData()
    },

    //标题选择公告类型
    handleCommand(val) {
      this.formData.type = val
      this.searchData()
    },

    resetFields() {
      this.$refs.form.resetFields()
      this.searchData()
    },
    searchData() {
      this.pageInfo.pageNum = 1
      this.getNoticeList()
    },
    handleSizeChange(size) {
      this.pageInfo.pageSize = size
      this.getNoticeList()
    },
    handleCurrentChange(val) {
      this.pageInfo.pageNum = val
      this.getNoticeList()
    },
    getNoticeList() {
      const that = this
      this.loading = true
      let param = {
        ...this.pageInfo,
        ...this.formData
      }

      getBannerPageList(param)
        .then((res) => {
          if (res.code == 0) {
            that.loading = false
            that.noticeData.list = res.result.list || []
            that.noticeData.total = res.result.total
          } else {
            that.loading = false
            that.$message({
              message: res.message,
              type: 'error'
            })
          }
        })
        .catch(() => {})
    },

    // 打开公告diallog
    showNoticeHandle(row) {
      this.dialogTitle = row.title
      this.dialogContent = row.content
      this.dialogVisible = true
    },

    goBack() {
      this.$router.push('/home')
    }
  }
}
</script>

<style lang="scss" scoped>
.main-box {
  background: rgba(255, 255, 255, 1);
  border-radius: 4px;
  padding: 15px 20px;
  .searchMy ::v-deep  .el-form-item__label {
    margin-left: 20px;
    padding: 0;
  }
  .searchMy ::v-deep  .el-input__inner {
    border-radius: 0 4px 4px 0;
  }
  .searchMy ::v-deep  .el-date-editor {
    width: 100%;

  }
  .head {
    line-height: 50px;
    height: 50px;
    display: flex;
    justify-content: space-between
  }
  .searchMy .el-form{
    .el-form-item{
      ::v-deep   .el-form-item__content{
        .el-input--suffix {
          width: 220px;
          border-radius: 4px;

          // .el-button--default{
          //   cursor: default;
          //   width: 30px;
          //   line-height: 30px;
          //   font-size: 20px;
          //   padding-left: 10px;

          // }
        }
        // .el-input{
        //   .el-input__inner{
        //     width: 220px;
        //   }

        // }
        .el-select{
          el-input--suffix{
            width: 220px
          }
        }
      }
    }
  }
  .importent{
    margin-right: 10px;
    text-align: center;
    font-size: 12px;
    width: 35px;
    height: 20px;
    line-height: 20px;
    background: #ff44441a;
    color: #FF4444;
    border-radius: 3px;
  }
  .el-table{

    .el-table__header-wrapper{
      // ::v-deep  .el-table__header{
      //   font-size: 16px;
      // }
      .el-dropdown{
        // font-size: 16px;
        cursor: pointer;
        color: #4183d5;
    }
    }
  }

  .pagination-container {
    margin: 15px 0;
    display: flex;
    justify-content: end;
    align-items: center;
  }
}
</style>
