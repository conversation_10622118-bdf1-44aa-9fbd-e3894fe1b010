<template>
  <placard v-if="!isShowPlacard && menuGray == 1"></placard>
  <div class="expressDeliveryConfig" v-else>
    <div class="headerStatus">
      <div class="companyStatus">
        <div>快递面单打印配置</div>
      </div>
      <div>
        <el-button type="primary" @click="submit" :loading="submitLoading">{{
          disabled ? '编辑' : '提交'
        }}</el-button>
      </div>
    </div>
    <div class="contentBox">
      <div class="title_line">打印配置</div>
      <div class="printConfiguration">
        <span>是否跳过打印预览页面：</span>
        <el-radio-group
          v-model="shippingInfo.skipPrintPreview"
          :disabled="disabled"
        >
          <el-radio :label="1">
            跳过打印预览页面
            <el-tooltip placement="top">
              <div slot="content">
                打印快递面单时，将使用本地默认打印机进行打印，打印过程中不支持选择打印机
              </div>
              <i class="el-icon-warning-outline"></i>
            </el-tooltip>
          </el-radio>
          <el-radio :label="0">
            保留打印预览页面
            <el-tooltip placement="top">
              <div slot="content">
                打印快递面单时，将弹出打印预览页面，支持每笔订单选择打印机
              </div>
              <i class="el-icon-warning-outline"></i>
            </el-tooltip>
          </el-radio>
        </el-radio-group>
      </div>
      <div class="printConfiguration">
        <span>面单备注代入信息：</span>
        <el-checkbox
          v-model="shippingInfo.selectType"
          :label="1"
          :disabled="disabled"
          >订单编号</el-checkbox
        >
        <el-checkbox
          v-model="shippingInfo.selectType"
          :label="2"
          :disabled="disabled"
          >客户名称</el-checkbox
        >
      </div>
      <div class="title_line">发货信息配置</div>
      <el-form :model="shippingInfo" ref="shippingInfo" label-width="120px">
        <el-form-item
          label="发件人:"
          prop="consignor"
          :rules="[
            { required: true, message: '请输入发件人', trigger: 'blur' }
          ]"
        >
          <el-input
            v-model="shippingInfo.consignor"
            placeholder="限30个字"
            :disabled="disabled"
            maxlength="30"
          ></el-input>
        </el-form-item>
        <el-form-item
          label="发货联系电话:"
          prop="deliveryMobile"
          :rules="[
            { required: true, message: '请输入发货联系电话', trigger: 'blur' }
          ]"
        >
          <el-input
            v-model="shippingInfo.deliveryMobile"
            placeholder="可输入手机号或座机，限制20个字符"
            :disabled="disabled"
            maxlength="20"
          ></el-input>
        </el-form-item>
        <el-form-item label="发货省市区:" required>
          <div class="addrForm">
            <el-form-item
              prop="deliveryProvinceCode"
              :rules="[
                { required: true, message: '请选择省份', trigger: 'change' }
              ]"
            >
              <el-select
                v-model="shippingInfo.deliveryProvinceName"
                placeholder="请选择省份"
                :disabled="disabled"
                @change="
                  (val) =>
                    provinceChange(
                      val,
                      'shippingInfo',
                      'deliveryProvinceCode',
                      'provinceList'
                    )
                "
                :key="shippingInfo.deliveryProvinceName"
              >
                <el-option
                  v-for="province in provinceList"
                  :key="province.areaId"
                  :label="province.areaName"
                  :value="province.areaName"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item
              prop="deliveryCityCode"
              :rules="[
                { required: true, message: '请选择城市', trigger: 'change' }
              ]"
            >
              <el-select
                v-model="shippingInfo.deliveryCityName"
                placeholder="请选择城市"
                :disabled="disabled"
                @change="
                  (val) =>
                    provinceChange(
                      val,
                      'shippingInfo',
                      'deliveryCityCode',
                      'cityList'
                    )
                "
                :key="shippingInfo.deliveryCityCode"
              >
                <el-option
                  v-for="city in shippingInfo.cityList"
                  :key="city.areaId"
                  :label="city.areaName"
                  :value="city.areaName"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item
              prop="deliveryAreaCode"
              :rules="[
                { required: true, message: '请选择区县', trigger: 'change' }
              ]"
            >
              <el-select
                v-model="shippingInfo.deliveryAreaName"
                placeholder="请选择区县"
                :disabled="disabled"
                @change="
                  (val) =>
                    provinceChange(
                      val,
                      'shippingInfo',
                      'deliveryAreaCode',
                      'areaList'
                    )
                "
                :key="shippingInfo.deliveryAreaName"
              >
                <el-option
                  v-for="area in shippingInfo.areaList"
                  :key="area.areaId"
                  :label="area.areaName"
                  :value="area.areaName"
                ></el-option>
              </el-select>
            </el-form-item>
          </div>
        </el-form-item>
        <el-form-item
          label="详细地址:"
          prop="deliveryAddress"
          :rules="[
            { required: true, message: '请输入详细地址', trigger: 'blur' }
          ]"
        >
          <el-input
            v-model="shippingInfo.deliveryAddress"
            placeholder="限100个字"
            :disabled="disabled"
            maxlength="100"
          ></el-input>
        </el-form-item>
        <el-form-item
          label="发件邮政编码:"
          prop="deliveryZipCode"
          :rules="[
            { required: true, message: '请输入发件邮政编码', trigger: 'blur' }
          ]"
        >
          <el-input
            v-model="shippingInfo.deliveryZipCode"
            placeholder="限20个字符"
            :disabled="disabled"
            maxlength="20"
          ></el-input>
        </el-form-item>
      </el-form>
      <div class="title_line first">承运方配置</div>
      <div
        v-for="(businessType, index) in shippingInfo.popDeliveryCarrierDtos"
        :key="index"
      >
        <div class="secondTitle">{{ businessType.logisticsCompanyName }}</div>
        <el-form
          class="customConfig"
          :inline="true"
          :model="businessType"
          ref="formRefs"
          label-width="140px"
        >
          <el-form-item
            style="display: block; width: 100%"
            label="业务类型:"
            prop="deliveryCarrierTypes"
          >
            <el-checkbox-group
              v-model="businessType.deliveryCarrierTypesList"
              :disabled="disabled"
            >
              <el-checkbox
                v-for="item in businessType.popDeliveryBusinessTypes"
                :label="item.key"
                :key="item.key"
                @change="
                  (val) =>
                    popDeliveryBusinessTypeChange(val, item, businessType)
                "
                >{{ item.value }}
              </el-checkbox>
            </el-checkbox-group>
          </el-form-item>
          <template v-if="businessType.showConfig">
            <el-form-item
              label="面单尺寸:"
              prop="templateSize"
              :rules="[{ required: true, message: '请选择', trigger: 'blur' }]"
            >
              <el-select
                v-model="businessType.templateSize"
                placeholder="请选择"
                :disabled="disabled"
              >
                <el-option
                  v-for="item in businessType.templateSizes"
                  :key="item.key"
                  :label="item.value"
                  :value="Number(item.key)"
                />
              </el-select>
            </el-form-item>
            <el-form-item
              label="隐私面单:"
              prop="privacy"
              :rules="[{ required: true, message: '请选择', trigger: 'blur' }]"
            >
              <el-radio-group
                v-model="businessType.privacy"
                :disabled="disabled"
              >
                <el-radio :label="0">否</el-radio>
                <el-radio :label="1">是</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item
              v-if="electronicAccountText[businessType.logisticsCompanyType]"
              :label="electronicAccountText[businessType.logisticsCompanyType]"
              prop="electronicAccount"
              :rules="[{ required: true, message: '请填写', trigger: 'blur' }]"
            >
              <el-input
                v-model="businessType.electronicAccount"
                placeholder=""
                :disabled="disabled"
                maxlength="30"
              />
            </el-form-item>
            <el-form-item
              v-if="verificationCodeText[businessType.logisticsCompanyType]"
              :label="verificationCodeText[businessType.logisticsCompanyType]"
              prop="verificationCode"
              :rules="[{ required: true, message: '请填写', trigger: 'blur' }]"
            >
              <el-input
                v-model="businessType.verificationCode"
                placeholder=""
                :disabled="disabled"
                maxlength="30"
              />
            </el-form-item>
            <el-form-item
              v-if="branchesCodeText[businessType.logisticsCompanyType]"
              :label="branchesCodeText[businessType.logisticsCompanyType]"
              prop="branchesCode"
              :rules="[{ required: true, message: '请填写', trigger: 'blur' }]"
            >
              <el-input
                v-model="businessType.branchesCode"
                placeholder=""
                :disabled="disabled"
                maxlength="30"
              />
            </el-form-item>
            <el-form-item
              label="运费支付方式:"
              prop="deliveryPayType"
              :rules="[{ required: true, message: '请选择', trigger: 'blur' }]"
            >
              <el-select
                v-model="businessType.deliveryPayType"
                size="small"
                :disabled="disabled"
                placeholder="请选择"
                style="width: 100%"
              >
                <el-option label="现付" :value="1" />
                <el-option label="到付" :value="2" />
                <el-option label="月结" :value="3" />
              </el-select>
            </el-form-item>
            <el-form-item
              v-if="
                businessType.logisticsCompanyType == 2 ||
                businessType.logisticsCompanyType == 35 ||
                businessType.logisticsCompanyType == 13
              "
              label="送货方式:"
              prop="deliverGoodsType"
              :rules="[{ required: true, message: '请选择', trigger: 'blur' }]"
            >
              <el-select
                v-model="businessType.deliverGoodsType"
                placeholder="请选择"
                :disabled="disabled"
              >
                <el-option
                  v-for="item in businessType.deliverGoodsTypes"
                  :key="item.key"
                  :label="item.value"
                  :value="Number(item.key)"
                />
              </el-select>
            </el-form-item>

            <template
              v-if="
                Number(businessType.logisticsCompanyType) === 11 ||
                Number(businessType.logisticsCompanyType) === 1 ||
                Number(businessType.logisticsCompanyType) === 31
              "
            >
              <el-form-item
                label="月结卡号:"
                prop="settlementCardNo"
                :rules="[
                  { required: true, message: '请输入月结卡号', trigger: 'blur' }
                ]"
              >
                <el-input
                  v-model="businessType.settlementCardNo"
                  placeholder=""
                  :disabled="disabled"
                  maxlength="30"
                />
              </el-form-item>
            </template>

            <template
              v-if="
                Number(businessType.logisticsCompanyType) === 11 ||
                Number(businessType.logisticsCompanyType) === 31
              "
            >
              <!--          京东      -->
              <template v-if="Number(businessType.logisticsCompanyType) === 11">
                <el-form-item
                  label="大客户编号:"
                  prop="vipUserNumber"
                  :rules="[
                    {
                      required: true,
                      message: '请输入大客户编号',
                      trigger: 'blur'
                    }
                  ]"
                >
                  <el-input
                    v-model="businessType.vipUserNumber"
                    placeholder=""
                    :disabled="disabled"
                    maxlength="30"
                  ></el-input>
                </el-form-item>
              </template>
              <template v-else>
                <el-form-item label="大客户编号:" prop="vipUserNumber">
                  <el-input
                    v-model="businessType.vipUserNumber"
                    placeholder=""
                    :disabled="disabled"
                    maxlength="30"
                  ></el-input>
                </el-form-item>
              </template>
              <el-form-item
                label="app_key:"
                prop="appId"
                :rules="[
                  { required: true, message: '请输入app_key', trigger: 'blur' }
                ]"
              >
                <el-input
                  v-model="businessType.appId"
                  placeholder=""
                  :disabled="disabled"
                  maxlength="50"
                ></el-input>
              </el-form-item>
              <el-form-item
                label="app_Secret:"
                prop="appSecret"
                :rules="[
                  {
                    required: true,
                    message: '请输入app_Secret',
                    trigger: 'blur'
                  }
                ]"
              >
                <el-input
                  v-model="businessType.appSecret"
                  placeholder=""
                  :disabled="disabled"
                  maxlength="50"
                ></el-input>
              </el-form-item>
              <el-form-item
                label="app_Token:"
                prop="appToken"
                :rules="[
                  {
                    required: true,
                    message: '请输入app_Token',
                    trigger: 'blur'
                  }
                ]"
              >
                <el-input
                  v-model="businessType.appToken"
                  placeholder=""
                  :disabled="disabled"
                  maxlength="50"
                ></el-input>
              </el-form-item>
              <el-form-item
                label="token过期时间:"
                prop="tokenExpiredTime"
                :rules="[
                  {
                    required: true,
                    message: '请选择token过期时间',
                    trigger: 'blur'
                  }
                ]"
              >
                <el-date-picker
                  :disabled="disabled"
                  v-model="businessType.tokenExpiredTime"
                  type="date"
                  value-format="yyyy-MM-dd"
                  placeholder="选择日期"
                >
                </el-date-picker>
              </el-form-item>
            </template>
            <template v-if="Number(businessType.logisticsCompanyType) === 8">
              <!--          中通-->
              <el-form-item
                label="电子面单账户:"
                prop="electronicAccount"
                :rules="[
                  {
                    required: true,
                    message: '请输入电子面单账户',
                    trigger: 'blur'
                  }
                ]"
              >
                <el-input
                  v-model="businessType.electronicAccount"
                  placeholder=""
                  :disabled="disabled"
                  maxlength="30"
                />
              </el-form-item>
              <el-form-item
                label="验证码:"
                prop="verificationCode"
                :rules="[
                  { required: true, message: '请输入验证码', trigger: 'blur' }
                ]"
              >
                <el-input
                  v-model="businessType.verificationCode"
                  placeholder=""
                  :disabled="disabled"
                  maxlength="30"
                />
              </el-form-item>
              <el-form-item
                label="网点名称:"
                prop="branchesName"
                :rules="[
                  { required: true, message: '请输入网点名称', trigger: 'blur' }
                ]"
              >
                <el-input
                  v-model="businessType.branchesName"
                  placeholder=""
                  :disabled="disabled"
                  maxlength="30"
                />
              </el-form-item>
              <el-form-item
                label="网点编码:"
                prop="branchesCode"
                :rules="[
                  { required: true, message: '请输入网点编码', trigger: 'blur' }
                ]"
              >
                <el-input
                  v-model="businessType.branchesCode"
                  placeholder=""
                  :disabled="disabled"
                  maxlength="30"
                />
              </el-form-item>
            </template>
            <el-form-item
              style="display: block; width: 100%"
              label="合同有效期:"
              prop="contractTime"
              :rules="[
                { required: true, message: '请选择合同有效期', trigger: 'blur' }
              ]"
            >
              <el-date-picker
                v-model="businessType.contractTime"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                value-format="yyyy-MM-dd"
                :disabled="disabled"
              >
              </el-date-picker>
            </el-form-item>
            <template
              v-if="
                Number(businessType.logisticsCompanyType) !== 8 &&
                Number(businessType.logisticsCompanyType) !== 11 &&
                Number(businessType.logisticsCompanyType) !== 1 &&
                Number(businessType.logisticsCompanyType) !== 31
              "
            >
              <el-form-item style="width: 180px" label="是否保价">
                <el-checkbox
                  v-model="businessType.isInsuredPrice"
                  :disabled="
                    disabled ||
                    businessType.logisticsCompanyType == 32 ||
                    businessType.logisticsCompanyType == 35 ||
                    businessType.logisticsCompanyType == 10 ||
                    businessType.logisticsCompanyType == 12 ||
                    businessType.logisticsCompanyType == 17
                  "
                  @change="insuredPriceChange(businessType)"
                ></el-checkbox>
                <el-tooltip placement="top" style="margin-left: 5px">
                  <div slot="content">
                    1、已与承运方签署了保价合同；2、若勾选保价，则在打印面单时所有运单均执行保价服务；
                  </div>
                  <i class="el-icon-warning-outline"></i>
                </el-tooltip>
              </el-form-item>
              <el-form-item
                class="customFormItem"
                label="保价金额:"
                prop="insuredPriceUpperLimit"
                :rules="
                  businessType.isInsuredPrice
                    ? priceUpperLimitRule
                    : [{ required: false }]
                "
              >
                <el-input
                  v-model="businessType.insuredPriceUpperLimit"
                  placeholder=""
                  :disabled="!businessType.isInsuredPrice || disabled"
                  maxlength="20"
                ></el-input>
              </el-form-item>
            </template>
            <template
              v-if="
                Number(businessType.logisticsCompanyType) === 11 ||
                Number(businessType.logisticsCompanyType) === 1 ||
                Number(businessType.logisticsCompanyType) === 31
              "
            >
              <el-form-item style="width: 350px" label="是否保价">
                <template slot="label">
                  <el-tooltip placement="top" style="margin-left: 5px">
                    <div slot="content">
                      1、请在勾选前确认已与承运方签署了相关保价合同；
                      2、若选择“单单保”，即表示每笔订单承运方收取合同约定的保费，承运方保价合同约定的“保价金额”。若选择“单单保“，需参考与承运方的线下合同填写保价金额，保价金额需填写0-9999999之间的值；
                      3、若选择“箱箱保”，即表示每个包裹面单承运方收取固定保费，承运方保价固定货值；
                      4、若选择“按货值保”，即表示每个包裹面单承运方按照“订单的商品实付金额“X“货值保价比例”收取保费，承运方保价订单商品实付金额。若选择“按货值保”，需参考与承运方的线下合同填写“货值保价比例”，货值保价比例需填写0.0001至0.005之间的值；
                      5、最终实际保价金额以承运方为准，如对保价方式有异议，可优先向承运方咨询；
                    </div>
                    <i class="el-icon-warning-outline"></i>
                  </el-tooltip>
                  是否保价
                </template>

                <el-select
                  v-model="businessType.insureType"
                  placeholder="请选择"
                  :disabled="disabled"
                >
                  <el-option :label="'不保价'" :value="0"></el-option>
                  <el-option :label="'单单保'" :value="2"></el-option>
                  <el-option :label="'箱箱保'" :value="1"></el-option>
                  <el-option :label="'按货值保'" :value="3"></el-option>
                </el-select>
              </el-form-item>
              <el-form-item
                v-if="businessType.insureType === 2"
                class="customFormItem"
                label="保价金额:"
                prop="insuredPriceUpperLimit"
                :rules="priceUpperLimitRule1"
              >
                <el-input
                  v-model="businessType.insuredPriceUpperLimit"
                  placeholder="请输入保价金额，最大保价9999999"
                  :disabled="disabled"
                  style="width: 250px"
                  maxlength="20"
                ></el-input>
              </el-form-item>
              <el-form-item
                v-if="businessType.insureType === 3"
                label="货值报价金额:"
                prop="insuredPriceRatio"
                label-width="140px"
                :rules="priceUpperLimitRule2"
              >
                <template slot="label">
                  货值报价金额
                  <el-tooltip placement="top" style="margin-left: 5px">
                    <div slot="content">
                      请参考合同填写0.0001至0.005之间的数值
                    </div>
                    <i class="el-icon-warning-outline"></i>
                  </el-tooltip>
                </template>
                <el-input
                  v-model="businessType.insuredPriceRatio"
                  placeholder="请参考合同填写0.0001至0.005之间的数值"
                  :disabled="disabled"
                  @input="insuredPriceRatioChange(index)"
                ></el-input>
              </el-form-item>
            </template>
            <template v-if="businessType.deliveryPlatform == 1">
              <el-form-item style="display: block; width: 100%">
                <div style="font-size: 12px; color: #999">
                  请上传承运商的相关合同电子版，支持文件类型：doc，docx，csv，xlsx，pdf，jpeg，png，jpg，gif每类合同最多上传5个文件，每个文件大小不超过10M
                </div>
              </el-form-item>
              <el-form-item label="承运商主合同:">
                <el-upload
                  :disabled="disabled"
                  class="avatar-uploader"
                  action=""
                  :limit="5"
                  :http-request="
                    (obj) =>
                      httpRequest(
                        obj,
                        businessType,
                        'carrierMasterContractFileList'
                      )
                  "
                  :before-upload="beforeAvatarUpload"
                  :file-list="businessType.carrierMasterContractFileList"
                  :on-remove="
                    (file) =>
                      fileRemove(
                        file,
                        businessType,
                        'carrierMasterContractFileList'
                      )
                  "
                >
                  <el-button :disabled="disabled" size="small" type="primary"
                    >上传承运商主合同</el-button
                  >
                </el-upload>
              </el-form-item>
              <el-form-item label="承运商子合同:">
                <el-upload
                  :disabled="disabled"
                  class="avatar-uploader"
                  action=""
                  :limit="5"
                  :http-request="
                    (obj) =>
                      httpRequest(
                        obj,
                        businessType,
                        'carrierChildContractUrlFileList'
                      )
                  "
                  :before-upload="beforeAvatarUpload"
                  :file-list="businessType.carrierChildContractUrlFileList"
                  :on-remove="
                    (file) =>
                      fileRemove(
                        file,
                        businessType,
                        'carrierChildContractUrlFileList'
                      )
                  "
                >
                  <el-button :disabled="disabled" size="small" type="primary"
                    >上传承运商子合同</el-button
                  >
                </el-upload>
              </el-form-item>
              <el-form-item label="承运商营业执照:">
                <el-upload
                  :disabled="disabled"
                  class="avatar-uploader"
                  action=""
                  :limit="5"
                  :http-request="
                    (obj) =>
                      httpRequest(
                        obj,
                        businessType,
                        'carrierBusinessLicenseFileList'
                      )
                  "
                  :before-upload="beforeAvatarUpload"
                  :file-list="businessType.carrierBusinessLicenseFileList"
                  :on-remove="
                    (file) =>
                      fileRemove(
                        file,
                        businessType,
                        'carrierBusinessLicenseFileList'
                      )
                  "
                >
                  <el-button :disabled="disabled" size="small" type="primary"
                    >上传承运商营业执照</el-button
                  >
                </el-upload>
              </el-form-item>
              <el-form-item label="道路运输经营许可证:">
                <el-upload
                  :disabled="disabled"
                  class="avatar-uploader"
                  action=""
                  :limit="5"
                  :http-request="
                    (obj) =>
                      httpRequest(
                        obj,
                        businessType,
                        'transportationOperationLicenseUrlFileList'
                      )
                  "
                  :before-upload="beforeAvatarUpload"
                  :file-list="
                    businessType.transportationOperationLicenseUrlFileList
                  "
                  :on-remove="
                    (file) =>
                      fileRemove(
                        file,
                        businessType,
                        'transportationOperationLicenseUrlFileList'
                      )
                  "
                >
                  <el-button :disabled="disabled" size="small" type="primary"
                    >上传道路运输经营许可证</el-button
                  >
                </el-upload>
              </el-form-item>
              <el-form-item label="提货委托书(备案人员):">
                <el-upload
                  :disabled="disabled"
                  class="avatar-uploader"
                  action=""
                  :limit="5"
                  :http-request="
                    (obj) =>
                      httpRequest(
                        obj,
                        businessType,
                        'deliveryEntrusterUrlFileList'
                      )
                  "
                  :before-upload="beforeAvatarUpload"
                  :file-list="businessType.deliveryEntrusterUrlFileList"
                  :on-remove="
                    (file) =>
                      fileRemove(
                        file,
                        businessType,
                        'deliveryEntrusterUrlFileList'
                      )
                  "
                >
                  <el-button :disabled="disabled" size="small" type="primary"
                    >上传提货委托书</el-button
                  >
                </el-upload>
              </el-form-item>
            </template>
          </template>
        </el-form>
        <el-divider />
      </div>
    </div>
  </div>
</template>

<script>
import { uploadFDFS } from '@/api/order'
import { getAddresInfo } from '@/api/qual'
import {
  getBusinessTypes,
  addOrUpdatePopDeliveryInfo,
  getPopDeliveryInfo
} from '@/api/storeManagement'
import placard from './placard.vue'
import {mapState} from "vuex";
export default {
  name: 'expressDeliveryConfig',
  components: {
    placard
  },
  computed: {
    ...mapState('permission', ['menuGray']),
  },
  data() {
    const checkPrice = (rule, value, callback) => {
      console.log(value)
      if (!value) {
        return callback(new Error('请输入保价金额'))
      }
      if (value > 10000 || !/(^[1-9]\d*$)/.test(value)) {
        callback(new Error('请输入>0,且<10000的正整数'))
      } else {
        callback()
      }
    }
    const checkPrice1 = (rule, value, callback) => {
      console.log(value)
      if (!value) {
        return callback(new Error('请输入保价金额'))
      }
      if (value > 9999999 || !/(^[1-9]\d*$)/.test(value)) {
        callback(new Error('请输入>0,且<9999999的正整数'))
      } else {
        callback()
      }
    }
    const checkPrice2 = (rule, value, callback) => {
      console.log(value)
      if (!value) {
        return callback(new Error('请参考合同填写0.0001至0.005之间的数值'))
      }
      if (!/0\.000[1-9]|0\.00[1-4]|0\.005/.test(value)) {
        callback(new Error('请参考合同填写0.0001至0.005之间的数值'))
      } else {
        callback()
      }
    }
    return {
      shippingInfo: {
        selectType: [],
        skipPrintPreview: '',
        consignor: '',
        deliveryAddress: '',
        deliveryAreaCode: '',
        deliveryAreaName: '',
        deliveryCityCode: '',
        deliveryCityName: '',
        deliveryMobile: '',
        deliveryProvinceCode: '',
        deliveryProvinceName: '',
        deliveryZipCode: '',
        id: '',
        orgId: '',
        popDeliveryCarrierDtos: []
      },
      shippingInfoRules: {},
      JDConfig: {},
      JDConfigRules: {},
      SFConfig: {},
      ZTOConfig: {},
      provinceList: [],
      cityList: [],
      areaList: [],
      disabled: false,
      submitLoading: false,
      priceUpperLimitRule: [
        {
          required: true,
          validator: checkPrice,
          trigger: 'blur'
        }
      ],
      priceUpperLimitRule1: [
        {
          required: true,
          validator: checkPrice1,
          trigger: 'blur'
        }
      ],
      priceUpperLimitRule2: [
        {
          required: true,
          validator: checkPrice2,
          trigger: 'blur'
        }
      ],

      formRefs: ['formRu1', 'formRu2', 'formRu3'],
      electronicAccountText: {
        32: '客户编码',
        34: '取号账户',
        16: '客户代码',
        3: '客户代码',
        5: '客户编码',
        6: '客户名称',
        2: '月结编码',
        35: '月结编码',
        7: '白马账户',
        13: '客户号',
        10: '操作编码',
        18: '账户信息',
        12: '客户编码',
        17: '商家ID'
      },
      verificationCodeText: {
        32: '客户密钥',
        34: '取号密码',
        5: '密钥',
        6: '客户密码',
        7: '接口联调密码',
        13: '密码',
        10: 'erp密钥',
        18: '账户密码',
        12: '客户密钥',
        17: '订购申请ID'
      },
      branchesCodeText: {
        32: '网点编码',
        6: '归属网点'
      },
      isShowPlacard: 0,
    }
  },
  created() {
    this.getAddr(0, 'provinceList')
    this.getBusinessTypes('init')
  },
  methods: {
    insuredPriceRatioChange(index) {
      //使val输入不超过4个小数,且不允许为负数
      if (
        this.shippingInfo.popDeliveryCarrierDtos[index].insuredPriceRatio < 0
      ) {
        this.shippingInfo.popDeliveryCarrierDtos[index].insuredPriceRatio = 0
      }
      if (
        this.shippingInfo.popDeliveryCarrierDtos[index].insuredPriceRatio
          .toString()
          .split('.')[1]
      ) {
        if (
          this.shippingInfo.popDeliveryCarrierDtos[index].insuredPriceRatio
            .toString()
            .split('.')[1].length > 4
        ) {
          this.shippingInfo.popDeliveryCarrierDtos[index].insuredPriceRatio =
            this.shippingInfo.popDeliveryCarrierDtos[index].insuredPriceRatio
              .toString()
              .split('.')[0] +
            '.' +
            this.shippingInfo.popDeliveryCarrierDtos[index].insuredPriceRatio
              .toString()
              .split('.')[1]
              .substring(0, 4)
        }
      }
    },
    getAddr(code, listName, objName) {
      getAddresInfo({ parentCode: code }).then((res) => {
        if (res && res.code === 0) {
          if (listName !== 'provinceList') {
            this.$set(this[objName], listName, res.data)
            this.$forceUpdate()
          } else {
            this.provinceList = res.data
          }
        }
      })
    },
    async getBusinessTypes(type) {
      const res = await getBusinessTypes()
      if (res && res.code === 0) {
        this.shippingInfo.popDeliveryCarrierDtos = res.data.map((item) => {
          item.carrierMasterContractFileList = []
          item.carrierChildContractUrlFileList = []
          item.carrierBusinessLicenseFileList = []
          item.transportationOperationLicenseUrlFileList = []
          item.deliveryEntrusterUrlFileList = []
          item.deliveryCarrierTypesList = []

          if (type === 'init') {
            item.privacy = 0
            item.isInsuredPrice =
              item.logisticsCompanyType == 32 ||
              item.logisticsCompanyType == 35 ||
              item.logisticsCompanyType == 18 ||
              item.logisticsCompanyType == 12 ||
              item.logisticsCompanyType == 17
                ? true
                : false
            item.templateSize = Number(item.templateSizes[0].key)
          } else {
            item.privacy = item.privacy || 0
            item.isInsuredPrice =
              item.isInsuredPrice ||
              (item.logisticsCompanyType == 32 ||
              item.logisticsCompanyType == 35 ||
              item.logisticsCompanyType == 18 ||
              item.logisticsCompanyType == 12 ||
              item.logisticsCompanyType == 17
                ? true
                : false)
            item.templateSize =
              Number(item.templateSize) || Number(item.templateSizes[0].key)
          }
          return item
        })
        this.getPopDeliveryInfo()
      } else {
        this.$message.warning(res.message || '查询业务类型失败！')
      }
    },
    async getPopDeliveryInfo() {
      const res = await getPopDeliveryInfo()
      if (res && res.code === 0) {
        const result = res.data
        this.isShowPlacard = res.data.whiteMenu || 0
        Object.keys(result).forEach((key) => {
          if (key === 'popDeliveryCarrierDtos') {
            const list = [...this.shippingInfo.popDeliveryCarrierDtos]
            const ary = []
            // this.$refs.shippingInfo.clearValidate();
            list.forEach((item) => {
              const obj = {}
              if (result[key] && Array.isArray(result[key])) {
                result[key].forEach((li) => {
                  if (li.logisticsCompanyCode === item.logisticsCompanyType) {
                    // deliveryCarrierTypesList
                    if (li.deliveryCarrierTypes) {
                      li.deliveryCarrierTypesList =
                        li.deliveryCarrierTypes.split(',')
                      li.showConfig = true
                    }
                    li.contractTime = [
                      this.formatDate(li.contractStartTime, 'YMD'),
                      this.formatDate(li.contractEndTime, 'YMD')
                    ]
                    li.tokenExpiredTime = this.formatDate(
                      li.tokenExpiredTime,
                      'YMD'
                    )
                    if (li.insuredPriceUpperLimit) {
                      li.isInsuredPrice = true
                    }
                    li.carrierMasterContractFileList = this.filterUrl(
                      li.carrierMasterContractUrl
                    )
                    li.carrierChildContractUrlFileList = this.filterUrl(
                      li.carrierChildContractUrl
                    )
                    li.carrierBusinessLicenseFileList = this.filterUrl(
                      li.carrierBusinessLicenseUrl
                    )
                    li.transportationOperationLicenseUrlFileList =
                      this.filterUrl(li.transportationOperationLicenseUrl)
                    li.deliveryEntrusterUrlFileList = this.filterUrl(
                      li.deliveryEntrusterUrl
                    )

                    Object.assign(obj, { ...li })
                  }
                })
              }
              ary.push({ ...item, ...obj })
            })
            this.$set(this.shippingInfo, key, ary)
          } else {
            this.$set(this.shippingInfo, key, result[key])
          }
        })

        if (this.shippingInfo.selectType) {
          if (this.shippingInfo.selectType.toString() === '0') {
            this.shippingInfo.selectType = []
          } else if (this.shippingInfo.selectType.toString() === '3') {
            this.shippingInfo.selectType = [1, 2]
          } else {
            this.shippingInfo.selectType = [this.shippingInfo.selectType]
          }
        } else {
          this.shippingInfo.selectType = []
        }
        if (this.shippingInfo.id) {
          if (this.shippingInfo.deliveryProvinceCode) {
            this.getAddr(
              this.shippingInfo.deliveryProvinceCode,
              'cityList',
              'shippingInfo'
            )
          }
          if (this.shippingInfo.deliveryCityCode) {
            this.getAddr(
              this.shippingInfo.deliveryCityCode,
              'areaList',
              'shippingInfo'
            )
          }
          this.disabled = true
        }
      }
    },
    filterUrl(urlStr) {
      if (urlStr) {
        const list = urlStr
          .split(',')
          .map((item) => {
            const name = item.split('/')[item.split('/').length - 1]
            return {
              name: name,
              path: item
            }
          })
          .filter((item) => item.name)
        return list
      } else {
        return []
      }
    },
    popDeliveryBusinessTypeChange(val, obj, businessType) {
      const logisticsCompanyType = businessType.logisticsCompanyType
      this.shippingInfo.popDeliveryCarrierDtos =
        this.shippingInfo.popDeliveryCarrierDtos.map((item) => {
          if (item.logisticsCompanyType === logisticsCompanyType) {
            const list = item.deliveryCarrierTypes
              ? item.deliveryCarrierTypes.split(',')
              : []
            if (val) {
              list.push(obj.key)
            } else {
              list.splice(list.indexOf(obj.key), 1)
            }
            if (list.length > 0) {
              item.showConfig = true
            } else {
              item.showConfig = false
            }
            item.deliveryCarrierTypes = list.join(',')
          }
          return item
        })
      console.log(this.shippingInfo.popDeliveryCarrierDtos)
    },
    insuredPriceChange(row) {
      this.$set(row, 'insuredPriceUpperLimit', '')
    },
    fileRemove(file, row, str) {
      const path = file.path
      const list = row[str].filter((item) => item.path !== path)
      this.$set(row, str, list)
    },
    provinceChange(val, objName, strName, listName) {
      if (listName === 'provinceList') {
        const [targetObj] = this.provinceList.filter(
          (item) => item.areaName === val
        )
        if (targetObj) {
          this.$set(this[objName], strName, targetObj.areaCode)
          this.getAddr(targetObj.areaCode, 'cityList', objName)
        }
        this.areaList = []
        const ary = [
          'deliveryCityCode',
          'deliveryCityName',
          'deliveryAreaCode',
          'deliveryAreaName'
        ]
        ary.forEach((key) => {
          this.$set(this[objName], key, '')
        })
        const list = ['cityList', 'areaList']
        list.forEach((key) => {
          this.$set(this[objName], key, [])
        })
      } else {
        const [targetObj] = this[objName][listName].filter(
          (item) => item.areaName === val
        )
        if (targetObj) {
          this.$set(this[objName], strName, targetObj.areaCode)
        }
      }
      if (listName === 'cityList') {
        this.$set(this[objName], 'areaList', [])
        const [targetObj] = this[objName][listName].filter(
          (item) => item.areaName === val
        )
        if (targetObj) {
          this.getAddr(targetObj.areaCode, 'areaList', objName)
        }
        const ary = ['deliveryAreaCode', 'deliveryAreaName']
        ary.forEach((key) => {
          this.$set(this[objName], key, '')
        })
      }
      if (listName === 'areaList') {
        this.$refs.shippingInfo.clearValidate([
          'deliveryProvinceCode',
          'deliveryCityCode',
          'deliveryAreaCode'
        ])
      }
    },
    beforeAvatarUpload(file) {
      const fileName = file.name
      const fileType = fileName.split('.')[fileName.split('.').length - 1]
      const fileTypes = [
        'doc',
        'jpeg',
        'png',
        'jpg',
        'docx',
        'csv',
        'xlsx',
        'pdf',
        'gif'
      ]
      if (!fileTypes.includes(fileType)) {
        this.$message.error(
          '上传附件只能是 doc，docx，csv，xlsx，pdf，jpeg，png，jpg，gif 格式！'
        )
        return false
      }
      if (file.size / 1024 / 1024 > 10) {
        this.$message.error('上传附件大小不能超过 10M！')
        return false
      }
    },
    async httpRequest(obj, data, str) {
      const fileName = obj.file.name
      const list = [...data[str]]
      const res = await uploadFDFS(obj)
      if (Number(res.code) === 200) {
        const imgUrl = process.env.VUE_APP_UPLOAD_API + '/' + res.data
        if (!list.includes(imgUrl)) {
          list.push({
            name: fileName,
            path: imgUrl
          })
        }
      } else {
        this.$message.error(res.msg)
      }
      this.$set(data, str, list)
    },
    getParams() {
      const params = { ...this.shippingInfo }
      console.log(params)
      if (params.selectType.length === 2) {
        params.selectType = 3
      } else if (params.selectType.length === 0) {
        params.selectType = 0
      } else if (params.selectType.length === 1) {
        params.selectType = params.selectType[0]
      }

      let parmsCopy = []
      this.shippingInfo.popDeliveryCarrierDtos.forEach((item) => {
        const obj = { ...item }
        obj.logisticsCompanyCode = obj.logisticsCompanyType
        if (
          Number(obj.logisticsCompanyType) === 11 ||
          Number(obj.logisticsCompanyType) === 1 ||
          Number(obj.logisticsCompanyType) === 31
        ) {
          if(obj.insureType !== 2){
            obj.insuredPriceUpperLimit = null;
          }
          if(obj.insureType !== 3){
            obj.insuredPriceRatio = null;
          }
          debugger
        }
        if (obj.contractTime && obj.contractTime.length > 0) {
          obj.contractStartTime = obj.contractTime[0]
          obj.contractEndTime = obj.contractTime[1]
        } else {
          obj.contractStartTime = ''
          obj.contractEndTime = ''
        }
        obj.carrierMasterContractUrl = this.getUrlStr(
          obj.carrierMasterContractFileList
        )
        delete obj.carrierMasterContractFileList
        obj.carrierChildContractUrl = this.getUrlStr(
          obj.carrierChildContractUrlFileList
        )
        delete obj.carrierChildContractUrlFileList
        obj.carrierBusinessLicenseUrl = this.getUrlStr(
          obj.carrierBusinessLicenseFileList
        )
        delete obj.carrierBusinessLicenseFileList
        obj.transportationOperationLicenseUrl = this.getUrlStr(
          obj.transportationOperationLicenseUrlFileList
        )
        delete obj.transportationOperationLicenseUrlFileList
        obj.deliveryEntrusterUrl = this.getUrlStr(
          obj.deliveryEntrusterUrlFileList
        )
        delete obj.deliveryEntrusterUrlFileList
        delete obj.contractTime
        delete obj.logisticsCompanyName
        delete obj.logisticsCompanyType
        delete obj.popDeliveryBusinessTypes
        delete obj.showConfig
        delete obj.isInsuredPrice
        if (obj.deliveryCarrierTypes && obj.logisticsCompanyCode) {
          parmsCopy.push(obj)
        }
      })
      params.popDeliveryCarrierDtos = parmsCopy
      delete params.cityList
      delete params.areaList
      params.popDeliveryCarrierDtos = params.popDeliveryCarrierDtos.filter(
        (item) => {
          return item.logisticsCompanyCode !== undefined
        }
      )
      params.popDeliveryCarrierDtosStr = JSON.stringify(
        params.popDeliveryCarrierDtos
      )

      return params
    },

    getUrlStr(ary) {
      let str = ''
      if (ary && Array.isArray(ary)) {
        str = ary
          .map((item) => {
            return item.path
          })
          .join(',')
      }
      return str
    },
    async submit() {
      if (this.disabled) {
        this.disabled = false
        return false
      }
      this.checkForm()
    },
    checkForm() {
      const newList = []
      const shipRes = new Promise((resolve, reject) => {
        this.$refs.shippingInfo.validate((valid) => {
          if (valid) {
            resolve()
          } else {
            reject()
          }
        })
      })
      newList.push(shipRes)
      this.$refs.formRefs.map((item) => {
        const result = new Promise((resolve, reject) => {
          item.validate((valid) => {
            if (valid) {
              resolve()
            } else {
              reject()
            }
          })
        })
        newList.push(result)
      })
      const params = this.getParams()
      if (!params.popDeliveryCarrierDtos.length > 0) {
        this.$message.warning('至少选择一个承运方的业务类型!')
        return false
      }
      Promise.all(newList)
        .then(async () => {
          console.log('全部校验通过')
          this.submitLoading = true
          const res = await addOrUpdatePopDeliveryInfo(params)
          if (res && res.code === 0) {
            this.$message.success('提交成功')
            this.disabled = true
            this.getBusinessTypes('refresh')
          } else {
            this.$alert(res.message || '提交失败', '提示', {
              confirmButtonText: '确定',
              callback: (action) => {}
            })
          }
        })
        .catch((err) => {
          console.log('校验失败', err)
          // this.$message.warning('请将开户信息补充完整');
        })
        .finally(() => {
          this.submitLoading = false
        })
    }
  }
}
</script>

<style scoped lang="scss">
.el-button {
  padding: 8px 20px;
}

.el-button.is-circle {
  padding: 7px;
  border: none;
}

.expressDeliveryConfig {
  //min-width: 1400px;
  width: 100%;
  height: 100%;
  position: relative;
  padding: 16px;
  padding-top: 0;
  // padding-top: 70px;

  .headerStatus {
    position: fixed;
    width: calc(100% - 300px);
    // top: 0;
    // left: 0;
    //min-width: 1400px;
    // width: 100%;
    height: 50px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    z-index: 100;
    background: #fff;
    padding: 16px;
    // margin-left: 16px;
    border-bottom: 1px solid #dcdfe6;

    .companyStatus {
      width: 60%;
      display: flex;
      justify-content: start;
      align-items: center;
      font-size: 18px;
    }
  }
  .contentBox {
    padding-top: 70px;
  }

  .secondTitle {
    width: 100px;
    display: inline-block;
    vertical-align: top;
    font-size: 16px;
    padding-left: 10px;
  }

  ::v-deep   .el-form {
    width: calc(100% - 150px);
    max-width: 1000px;
    display: inline-block;

    .el-select {
      margin-right: 14px;
    }

    .el-form-item__label {
      font-size: 14px;
      line-height: 30px;
    }

    .el-form-item__content {
      line-height: 30px;
    }

    .el-select__caret {
      line-height: 30px;
    }

    .el-input__inner {
      line-height: 30px;
      height: 30px;
      font-size: 12px;
    }

    .el-range-separator {
      line-height: 22px;
    }

    .el-range__icon {
      line-height: 22px;
    }

    .el-input__icon {
      line-height: initial;
    }

    .el-range__close-icon {
      line-height: initial;
    }

    ::v-deep   .el-table__body .el-form-item {
      padding: 20px 0;
    }

    .addrForm .el-form-item {
      display: inline-block;
    }

    .avatar-uploader {
      .el-upload-list__item {
        max-width: 300px;
      }
    }
  }

  .customConfig {
    .el-form-item {
      width: 450px;
      margin-bottom: 20px;
    }
  }

  .customFormItem {
    ::v-deep   .el-form-item__label {
      width: 85px !important;
    }
  }

  .title_line {
    font-size: 18px;
    // margin-top: 70px;
  }

  .printConfiguration {
    margin-bottom: 24px;
    > span {
      margin-right: 20px;
    }
  }
}
</style>
