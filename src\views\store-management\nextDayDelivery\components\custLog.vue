<template>
<div>
  <el-dialog title="操作日志" :visible="dialogVisible" width="800px" :before-close="handleClose">
    <el-table v-loading="loading" :data="tableConfig.data" border height="400" style="width: 100%">
      <!-- <el-table-column prop="operateBy" label="操作动作" align="center" width="200">
      </el-table-column> -->
      <el-table-column prop="operateTime" label="操作时间" align="center" width="200">
        <template slot-scope="scope">
          <div style="text-align: center">{{ formatDate(scope.row.operateTime)}}</div>

        </template>
      </el-table-column>
      <el-table-column prop="operateName" width="200" label="操作人" align="center" />
      <el-table-column prop="content"  label="操作日志" align="center">
        <template slot-scope="scope">
          <div style="text-align: center">
            <span class="activeTo" @click="lookInfo(scope.row)">查看</span>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <div style="text-align: right">
      <el-pagination :current-page="pageNum" :page-size="pageSize" :page-sizes="[10, 20, 50]"
        layout="total, sizes, prev, pager, next, jumper" :total="total" @size-change="handleSizeChange"
        @current-change="handleCurrentChange" style="margin-top: 15px" />
    </div>
  
  </el-dialog>
  <el-dialog v-if="Object.keys(row).length" title="日志详情" :visible="lockInfoDialogVisible" width="600px" :before-close="()=>{lockInfoDialogVisible=false}">
    <div style="padding-bottom:30px">
      <div class="lockInfo-info-div">
      <div class="lockInfo-title">是否开启次日达</div>
      <div class="lockInfo-info">{{row.nextdayEnabled==1?'是':'否'}}</div>
     </div>
     <hr style="opacity: 0.5;">
     <div class="lockInfo-info-div">
      <div class="lockInfo-title">时间</div>
      <div class="lockInfo-info">
        <div  v-for="item in row.timeConfList||[]" >
          <template v-if="!item.endPayTime.includes('-1')" >
            <div style="padding-bottom: 3px">
              {{`${{ 0: '周一', 1: '周二', 2: '周三', 3: '周四', 4: '周五', 5: '周六', 6: '周日' }[item.timeDay-1]}&nbsp;:&nbsp;${item.endPayTime}前付款，预计明天${item.arriveTime}前送达；`}}
            </div>           
          </template>
        </div>
      </div>
     </div>
     <hr style="opacity: 0.5;">
     <div class="lockInfo-info-div">
      <div class="lockInfo-title">地区</div>
      <div class="lockInfo-info">{{(row.areaNameList||[]).join("；")}}</div>
     </div>
    </div>
     
    </el-dialog>
</div>
</template>

<script>

import { queryOperateLog } from '@/api/storeManagement';
export default {
  name: "custLog",
  props: {
    name: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      pageNum: 1,
      pageSize: 10,
      total: 0,
      dialogVisible: true,
      loading: true,
      tableConfig: {
        data: []
      },
      lockInfoDialogVisible: false,
      row:{}
    }
  },
  mounted() {
    this.queryOperateLog()
  },
  methods: {
    lookInfo(row){
      //lwq
      this.row = {
        ...row,
        ...row.nextdayConfLogContentDTO
      }
      // this.row = {"operateTime":"ullamco officia laborum","operateBy":20223195,"shopCode":"Lorem minim eiusmod in","orgId\t":"qui laborum ipsum ut commo","timeConfList":[{"timeDay":60644177.12441638,"endPayTime":"mag","arriveTime":"dolore ea"},{"timeDay":-76964628.36292599,"endPayTime":"deserunt","arriveTime":"ipsum sed"},{"timeDay":-95218249.27108362,"endPayTime":"reprehenderit laboris eu ut non","arriveTime":"ut Excepteur"},{"timeDay":-49110113.79807437,"endPayTime":"pariat","arriveTime":"anim officia reprehenderit"},{"timeDay":-53804808.176998794,"endPayTime":"eu ullamco sit anim nostrud","arriveTime":"ipsum reprehenderit"}],"areaConfList":[-7814121.342575356,52731158.431320995,38978934.522260696,-16901830.016381904],"nextdayEnabled":55450105.03801811}
      this.lockInfoDialogVisible = true
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.queryOperateLog()
    },
    handleCurrentChange(val) {
      this.pageNum = val
      this.queryOperateLog()
    },
    async queryOperateLog() {
      this.loading = true
      try {
        const res = await queryOperateLog({
          pageNum: this.pageNum,
          pageSize: this.pageSize
        })
        if (res.code === 0) {
          this.tableConfig.data = res.result.list
          this.total = res.result.total || 0
        } else {
          this.$message.error(res.msg)
          // this.$alert(res.msg, {type: 'error'})
        }
      } catch (e) {
        this.tableConfig.data = []
        console.log(e)
      }
      this.loading = false
    },
    handleClose() {
      this.$emit('update:skuLogDialogVisible', false)
    }
  }
}
</script>

<style scoped lang="scss">
.lockInfo-info-div{
  display: flex;
  align-items: center;
  align-items: start;

}
.lockInfo-title{
  display: inline-block;
  width: 120px !important;
  text-align: center;
  // padding-right: 30px;
  // margin-right: 50px;
  // box-sizing: border-box;
}
.lockInfo-info{
  flex: 1;
  margin-right: 50px;
}
.activeTo{
  cursor: pointer;
  color: #4183D5;
}
// ::.el-table thead th .cell
::v-deep .el-table thead th {
  background: #f9f9f9;
  // border: none;

  .cell {
    font-family: PingFangSC, PingFangSC-Regular;
    font-weight: 400;
    text-align: center;
    color: rgba(51, 51, 51, 0.85);
    line-height: 22px;
  }
}

::v-deep .el-table__body-wrapper {
  font-size: 12px;
  color: #666666;
}

::v-deep .el-dialog__body {
  padding-top: 10px;
}

.titlediv {
  padding-bottom: 10px;
  font-size: 16px;
  font-weight: bold;
}
</style>
