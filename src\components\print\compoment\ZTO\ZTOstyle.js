export const ZTOstyle = `<style>
/*仅样式调试用，调试完毕须cope到对应样式js里*/

.page-row img {
    padding: 0;
    margin: 0;
    width: 100%;
}

.content {
    width: 70mm;
    /*height: 128mm;*/
    margin: 0mm auto;
    font-family: 黑体;
    box-sizing: border-box;
    font-size: 12px;
    color: #000;
    border: 1px solid #000;
    font-weight: bold;
    padding: 1mm 0;
}

.page-row {
    border-bottom: 1px solid #000;
    text-align: left;
    background-color: #ffffff;
    overflow: hidden;
    position: relative;
}

.page-row.flex {
    display: flex;
    justify-content: left;
    align-items: center;
}

.content .page-row:last-child {
    border-bottom: none;
}

.cell_12 {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.filletStr {
    width: 10mm;
    height: 10mm;
    line-height: 11mm;
    text-align: center;
    font-size: 30px;
    font-weight: bold;
    border: 1px solid #000;
    border-radius: 100%;
}

.verticalCell_1 {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.verticalCell_1 > div {
    width: 100%;
    height: 100%;
    border-bottom: 1px solid #000;
    display: flex;
    align-items: center;
}

.verticalCell_1 > div:last-child {
    border-bottom: none;
}

.center{
    display: flex;
    justify-content: center;
    align-items: center;
}

</style>`;
