import layout from '@/layout';

const companyManage = {
  path: '/companyManage',
  name: 'companyManage',
  component: layout,
  meta: {
    title: '企业管理',
    icon: 'el-icon-s-operation',
  },
  children: [
    {
      path: '/qualtification',
      name: 'qualtification',
      component: () => import('@/views/qualtification/index.vue'),
      meta: { title: '企业信息' },
    },
    {
      path: '/qualtification/history',
      name: 'qualtificationHistory',
      component: () => import('@/views/qualtification/indexHistory.vue'),
      hidden: true,
    },
    {
      path: '/qualtificationDetail',
      name: 'qualtificationDetail',
      component: () => import('@/views/qualtification/detail.vue'),
      hidden: true,
    },
    // {
    //   path: process.env.VUE_APP_BASE_API + '/paymentProve/edit',
    //   name: 'paymentProveEdit',
    //   meta: {title: '付款证明'}
    // },
    {
      path: '/proofOfPayment',
      name: 'proofOfPayment',
      component: () => import('@/views/companyOpenAccount/proofOfPayment.vue'),
      meta: { title: '付款证明' },
    },
    {
      path: '/companyOpenAccount',
      name: 'companyOpenAccount',
      component: () => import('@/views/companyOpenAccount/index.vue'),
      meta: { title: '企业开户' },
    },
    {
      path: '/companyOpenAccount/companyCheckPayment',
      name: 'companyCheckPayment',
      component: () => import('@/views/companyOpenAccount/companyCheckPayment.vue'),
      meta: { title: '银行打款验证' },
      hidden: true,
    },
    {
      path: '/companyOpenAccount/modifiedDetail',
      name: 'companyOpenAccountModifiedDetail',
      component: () => import('@/views/companyOpenAccount/modifiedDetail.vue'),
      meta: { title: '修改记录详情' },
      hidden: true,
    },
    {
      path: '/companyOpenAccount/modifySettlementRecordPingAn',
      name: 'modifySettlementRecordPingAn',
      component: () => import('@/views/companyOpenAccount/modifySettlementRecordPingAn.vue'),
      meta: { title: '修改结算信息' },
      hidden: true,
    },
    {
      path: '/companyOpenAccount/platformServeAgreement',
      name: 'platformServeAgreement',
      component: () => import('@/views/companyOpenAccount/platformServeAgreement.vue'),
      meta: { title: '平台服务协议' },
    },
    {
      path: '/companyOpenAccount/openElectronicSign',
      name: 'platformServeAgreement',
      component: () => import('@/views/companyOpenAccount/componments/platServeAgreeComponents/openElectronicSign.vue'),
      meta: { title: '开通电子签服务' },
      hidden: true,
    },
    {
      path: '/companyOpenAccount/contractPasser',
      name: 'contractPasser',
      component: () => import('@/views/companyOpenAccount/componments/platServeAgreeComponents/contractPasser.vue'),
      meta: { title: '购销合同印章免验证签' },
      hidden: true,
    },
  ],
};
export default companyManage;
