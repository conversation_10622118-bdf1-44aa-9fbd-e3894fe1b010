<template>
  <el-dialog
    v-loading="loading"
    title="商品上架设置"
    :visible="dialogVisible"
    width="40%"
    :before-close="handleClose"
  >
    <div class="radioBox">
      <span>来货自动上架：</span>
      <el-radio-group
        v-model="saleOutAutoUp"
        style="padding: 20px 0 10px 20px;"
      >
        <el-radio :label="0">关闭</el-radio>
        <el-radio :label="1">开启</el-radio>
      </el-radio-group>
    </div>
    
    <p v-if="saleOutAutoUp === 1" style="color: red;">开启后，因缺货下架的商品在有库存后将自动上架。</p>
    <span
      slot="footer"
      class="dialog-footer"
    >
      <el-button
        size="small"
        @click="handleClose"
      >取 消</el-button>
      <el-button
        type="primary"
        size="small"
        :loading="loading"
        @click="submit"
      >
        确 定
      </el-button>
    </span>
  </el-dialog>
</template>

<script>
import { updateSkuAutoUpInfo, skuAutoUpInfo } from '@/api/product';

export default {
  name: 'BatchModifyProductActivity',
  props: {
    handleClosePutOnShelves: {
      type: Function,
      default: () => {}
    },
  },
  data() {
    return {
      loading: false,
      dialogVisible: true,
      saleOutAutoUp: 0,
    };
  },
  created() {
    this.getSkuAutoUpInfo();
  },
  methods: {
    getSkuAutoUpInfo() {
      skuAutoUpInfo({}).then((res) => {
        if (res.code === 0) {
          this.saleOutAutoUp = res.data || 0;
        }
      })
    },
    handleClose() {
      this.handleClosePutOnShelves();
    },
    submit() {
      this.loading = true;
      updateSkuAutoUpInfo({ saleOutAutoUp: this.saleOutAutoUp }).then((res) => {
        this.loading = false;
        if (res.success) {
          this.$message.success('设置成功');
          this.handleClose();
          setTimeout(() => {
            this.$emit('refreshTable');
          }, 500);
        }
      }).catch(() => {
        this.loading = false;
      })
    },
  }
}
</script>

<style lang="scss" scoped>
/* .radioBox {
  display: flex;
  align-items: center;
} */
::v-deep  .el-radio-group {
  padding: 0 0 0 20px !important;
}
</style>
