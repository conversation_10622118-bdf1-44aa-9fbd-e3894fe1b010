<template>
  <div>
    <div class="serch">
      <div class="Fsearch">
        <el-row
          type="flex"
          align="middle"
          justify="space-between"
          class="my-row"
        >
          <el-row
            type="flex"
            align="middle"
          >
            <span class="sign" />
            <div class="searchMsg">
              新增满赠活动
            </div>
          </el-row>
          <el-button
            type="primary"
            size="small"
            @click="goBack"
          >
            返回
          </el-button>
        </el-row>
      </div>
      <div class="fromBlock">
        <el-form
          ref="ruleForm"
          :model="ruleForm"
          :rules="rules"
          size="small"
          label-width="120px"
        >
          <el-form-item label="活动名称" prop="name">
            <el-input
              v-model="ruleForm.name"
              :disabled="seeDisabled"
              maxlength="20"
              placeholder="最多输入20个字"
              style="width: 50%"
            />
          </el-form-item>
          <el-form-item label="活动时间" prop="activeTime">
            <el-date-picker
              v-model.trim="ruleForm.activeTime"
              type="datetimerange"
              format="yyyy-MM-dd HH:mm:ss"
              range-separator="至"
              value-format="timestamp"
              :default-time="['00:00:00', '23:59:59']"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              :disabled="seeDisabled"
            />
          </el-form-item>
          <el-form-item prop="customerGroupName">
            <span slot="label"><i style="color: #F56C6C;margin-right: 5px">*</i>活动人群</span>
            <el-button
              type="primary"
              plain
              size="small"
              :disabled="seeDisabled"
              @click="addPeople"
            >选择人群
            </el-button>
            <span
              v-if="ruleForm.customerGroupName"
              style="margin-left: 10px"
            >
              已选人群：{{ ruleForm.customerGroupName }}
            </span>
          </el-form-item>
          <el-form-item prop="giveData">
            <span slot="label"><i style="color: #F56C6C;margin-right: 5px">*</i>设置优惠</span>
            <mz-you v-model="ruleForm.giveData" :disabled="seeDisabled"></mz-you>
          </el-form-item>
					<el-form-item prop="masterSku">
						<span slot="label"><i style="color: #F56C6C;margin-right: 5px">*</i>选择主商品</span>
            <!-- <el-button
              type="primary"
              :disabled="seeDisabled || fromType === 'pinTuan' || fromType === 'edit'"
              @click="onSelectProduct('masterSku')"
            >请选择</el-button> -->
            <MultipartUpload v-if="!(seeDisabled)" :type="1" :curList="masterSku" :maxCount="maxCount" @getList="getMasterList"></MultipartUpload>
            <el-button v-if="!(seeDisabled)" style="margin:0 5px;" type="primary" size="mini" @click="multipartDelete">批量删除</el-button>
						<selectProduct v-if="!(seeDisabled)" :type="2" :maxCount="maxCount" v-model="masterSku"></selectProduct>
            <el-table
              ref="table"
							v-if="masterSku.length"
							:data="masterSku"
							style="margin-top: 10px;"
              height="400px"
							border
               @selection-change="handleSelectionChange" row-key="barcode"
							:header-cell-style="{ background: '#eeeeee', color: '#666666' }"
						>
              <el-table-column v-if="!(seeDisabled)" type="selection" width="55" align="center" reserve-selection></el-table-column>
							<el-table-column prop="csuid" label="csuId" />
              <el-table-column prop="barcode" label="商品编码" />
              <el-table-column prop="erpCode" label="erp编码">
                <!-- {{ masterSku[0].activityType == '3' ? '' : masterSku[0].erpCode }} -->
                  <template slot-scope="scope">
                    {{ scope.row.erpCode }}
                  </template>
              </el-table-column>
              <el-table-column prop="showName" label="商品名称" />
              <el-table-column prop="spec" label="规格" />
              <el-table-column prop="manufacturer" label="厂家" />
              <el-table-column align="center" label="库存信息" width="200">
                <template slot-scope="scope">
                  <p>{{ scope.row.availableQty }}</p>
                  <p class="keyValue">
                    <span style="width: 30px;">近至</span>：
                    <span>{{ scope.row.nearEffect }}</span>
                  </p>
                  <p class="keyValue">
                    <span style="width: 30px;">远至</span>：
                    <span>{{ scope.row.farEffect }}</span>
                  </p>
                </template>
              </el-table-column>
              <el-table-column prop="tagName" label="商品来源" width="120">
                <template slot-scope="{ row }">
                  {{ {1: '拼团商品', 2: '赠品', 0: '普通商品', 3: '批购包邮商品' }[row.activityType] }}
                </template>
              </el-table-column>
              <el-table-column prop="tagName" label="价格信息" width="120">
                <template slot-scope="{ row }">
                  <div v-if="row.activityType == 1 || row.activityType == 3">
                    {{ row.activityType == 1 ? '拼团价' : '活动价' }}：{{ row.groupPrice || '-' }}
                  </div>
                  <div v-else-if="row.activityType == 0">
                    <div>单体采购价：{{ row.fob || '-'}}</div>
                    <div>连锁采购价：{{ row.chainPrice || '-' }}</div>
                  </div>
                </template>
              </el-table-column>
              <el-table-column v-if="!(seeDisabled)" align="center" label="操作" width="100">
                <template slot-scope="scope">
                  <el-button type="text" @click="removeProduct(scope.row)">移除</el-button>
                </template>
              </el-table-column>
						</el-table>
          </el-form-item>
					<!-- <el-form-item label="活动规则" prop="fullReductionType">
             <el-radio-group v-model="ruleForm.fullReductionType" @change="changeFullReductionType" :disabled="seeDisabled">
							<el-radio :label="2">买满一定数量赠送</el-radio>
							<el-radio :label="1">每买满一定数量赠送</el-radio>
						</el-radio-group>
          </el-form-item> -->
					<!-- <el-form-item label="" prop="fullGiveRuleDtoList">
            <div
              v-for="(i, ind) in ruleForm.fullGiveRuleDtoList"
              :key="ind"
            >
              满
							<el-input-number
								v-model="i.fullCount"
								controls-position="right"
								:precision="0"
								:min="1"
								style="width: 120px"
                :disabled="seeDisabled"
							/> 件/个，送
							<el-input-number
								v-model="i.giveCount"
								controls-position="right"
								:precision="0"
								:min="1"
								style="width: 120px"
                :disabled="seeDisabled"
							/> 件/个
              <span v-if="ruleForm.fullReductionType == 2 && !seeDisabled">
								 <span
								 	v-if="ruleForm.fullGiveRuleDtoList.length < 5"
									class="btnText"
									style="margin-left: 10px;"
									@click="addOrDelRules('add', ind)"
								> +添加阶梯</span>
								<span
									v-if="ruleForm.fullGiveRuleDtoList.length > 1"
									class="delBtn"
									@click="addOrDelRules('del', ind)"
								>删除</span>
							</span>
            </div>
          </el-form-item>
          <el-form-item label="赠品数量限制" prop="giveQtyType">
             <el-radio-group v-model="ruleForm.giveQtyType" :disabled="seeDisabled">
							<el-radio :label="-1">不限制</el-radio>
							<el-radio :label="1">限制</el-radio>
							<el-input-number
								v-if="ruleForm.giveQtyType === 1"
								v-model="ruleForm.giveQty"
								controls-position="right"
								:precision="0"
								:min="1"
								style="width: 120px"
                :disabled="seeDisabled"
							/>
						</el-radio-group>
          </el-form-item> -->
          <el-form-item class="searchBtn" v-if="!seeDisabled">
            <el-button
              type="primary"
              :loading="submitLoading"
              @click="submitForm('ruleForm')"
            >
              提交
            </el-button>
            <el-button @click="resetForm('ruleForm')"> 取消</el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>
    <crowd-selector-dialog
      v-if="dialogVisible"
      v-model="dialogVisible"
      :selected="ruleForm.customerGroupId"
      @onSelect="onSelect"
    />
		<!-- <select-product
			v-if="showProductsModal"
      :selected-product-type="selectedProductType"
			:selected-product-list="selectedProductType === 'masterSku' ? masterSku : giveSku"
			@cancelModal="cancelModal"
			@confirmProduct="confirmProduct"
		/> -->
    <el-dialog title="结果通知" :visible.sync="result.visible" append-to-body @close="result.msgList = [];result.errorUrl = '';" width="400px">
      <p style="text-align: center;" v-for="val in result.msgList">{{ val }}</p>
      <div slot="footer">
        <el-button type="normal" size="mini" @click="result.visible = false;">取消</el-button>
        <el-button v-if="result.errorUrl" type="primary" size="mini" @click="errorDownload">下载失败文件</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getDetail, savePromotion, fullGiveImportCount } from '@/api/market/mzPromotion';
import selectProduct from './components/selectProduct';
import CrowdSelectorDialog from '../../components/xyy/customerOperatoin/crowd-selector-dialog.vue';
import mzYou from './components/mzYou.vue'
import MultipartUpload from './components/multipartUpload.vue';
export default {
  name: 'addActive',
  components: {
    selectProduct,
    MultipartUpload,
    CrowdSelectorDialog,
    mzYou
  },
  data() {
    return {
      isLoading: false,
      actId: null,
      fromType: '',
      dialogVisible: false,
      seeDisabled: false,
      submitLoading: false,
      showProductsModal: false,
			masterSku: [],
			selectedProductType: '',
			giveSku: [],
      maxCount: 20,
      ruleForm: {
        name: '',
        //fullReductionType: 2,
				//giveQtyType: -1,
        //giveQty: '',
        customerGroupId: undefined,
        customerGroupName: '',
        activeTime: [],
				//fullGiveRuleDtoList: [{
				//	fullCount: 1,
				//	giveCount: 1,
				//}],
        giveData: {}
      },
      rules: Object.freeze({
        customerGroupId: [
          { required: true,  message: '请选择人群', trigger: 'blur' }
        ],
        name: [
          { required: true, message: '请输入活动名称', trigger: 'blur' }
        ],
				fullReductionType: [
					{ required: true, message: '请选择', }
				],
				giveQtyType: [
					{ required: true, message: '请选择', }
				],
        activeTime: [
          { required: true, message: '请选择时间',  trigger: 'blur' },
          {
            validator(rule, value, callback) {
              if (value[0] !== value[1]) {
                callback();
              } else {
                callback(new Error('开始结束时间不能相同'));
              }
            },
            trigger: 'blur'
          }
        ],
      }),
      masterSkuSelected: [],
      result: {
        visible: false,
        msgList: [],
        errorUrl: ''
      },
    };
  },
  activated() {
    fullGiveImportCount({productType: 1}).then(res => {
      if (res.code == 1000) {
        this.maxCount = res.data.importLimit;
      } else {
        this.$message.error(res.msg);
      }
    })
    this.initData();
  },
  mounted() {
    fullGiveImportCount({productType: 1}).then(res => {
      if (res.code == 1000) {
        this.maxCount = res.data.importLimit;
      } else {
        this.$message.error(res.msg);
      }
    })
    this.initData();
  },
  created() {
    this.initData();
  },
  methods: {
    errorDownload() {
      window.open(this.result.errorUrl);
    },
    getMasterList(rows) {
      this.masterSku = [...this.masterSku, ...rows]
    },
    removeProduct(row) {
      this.masterSku = this.masterSku.filter(item => (item.id !== row.id) || (item.skuId !== row.skuId))
    },
    handleSelectionChange(row) {
      this.masterSkuSelected = row;

    },
    multipartDelete() {
      // this.masterSku = this.masterSku.filter(item => !this.masterSkuSelected.includes(val => (val.id == item.id) || (val.skuId == item.skuId)));
      this.masterSku = this.removeArrayElementsById(this.masterSku, this.masterSkuSelected);
      this.$refs.table.clearSelection();
    },
    
    removeArrayElementsById(arrayA, arrayB) {
      const idsInB = new Set(arrayB.map(item => item.id || item.skuId));
      return arrayA.filter(item => !idsInB.has(item.id || item.skuId));
    },
    initData() {
      const { id, fromType } = this.$route.query;
      this.fromType = fromType;
      if (id) {
        this.actId = id;
        this.seeDisabled = fromType === 'see';
        this.getDetail(id);
      } else {
        Object.keys(this.ruleForm).forEach((key) => {
          this.ruleForm[key] = '';
        }, this);
        this.actId = null;
        this.masterSku = [];
        this.giveSku = [];
        this.ruleForm.customerGroupId = undefined;
        /* this.ruleForm.fullReductionType = 2; */
       /*  this.ruleForm.giveQtyType = -1; */
        this.ruleForm.activeTime = [];
        /* this.ruleForm.fullGiveRuleDtoList = [{
          fullCount: 1,
          giveCount: 1,
        }]; */
        this.ruleForm.giveData = {};
        this.seeDisabled = false;
      }
      if (fromType === 'pinTuan') {
        const row = JSON.parse(sessionStorage.getItem('pinTuanProductInfo'));
        this.masterSku = [{
          csuid: row.skuId,
          barcode: row.barcode,
          erpCode: row.erpCode,
          showName: row.productName,
          spec: row.spec,
          manufacturer: row.manufacturer,
          activityType: row.activityType,
        }];
      }
      this.submitLoading = false;
    },
    getDetail(id) {
      this.isLoading = true;
      getDetail({ id }).then((res) => {
        this.isLoading = false;
        if (res.success) {
          const detail = res.data.detail;
          Object.keys(this.ruleForm).forEach(key => {
            this.ruleForm[key] = detail[key];
          });
          this.ruleForm.activeTime = [detail.stime, detail.etime];
          //this.giveSku = [detail.giveSkuDto] || [];
          this.masterSku = [...detail.masterSkuDtoList] || [];
          this.ruleForm.giveData = {
            fullReductionCond: detail.fullReductionCond,
            fullReductionType: detail.fullReductionType,
            giveSkuType: detail.giveSkuType,
            fullGiveRuleDtoList: detail.fullGiveRuleDtoList,
            giveSkuList: detail.giveSkuDtoList.map(item => {
              return {
                skuId: item.csuid,
                ...item,
              }
            })
          }
          /* this.ruleForm.giveQtyType = detail.giveSkuDto.giveQty === -1 ? -1 : 1;
          this.ruleForm.giveQty = detail.giveSkuDto.giveQty; */
        } else if (res.msg) {
          this.$message.error(res.msg);
        }
      });
    },
    onSelect(selectItem) {
      this.ruleForm.customerGroupName = selectItem.tagName;
      this.ruleForm.customerGroupId = selectItem.id;
    },
    addPeople() {
      this.dialogVisible = true;
    },
    addOrDelRules(type, index) {
      if (type === 'add') {
        this.ruleForm.fullGiveRuleDtoList.splice(index + 1, 0, { fullCount: 1, giveCount: 1 });
      } else {
        this.ruleForm.fullGiveRuleDtoList.splice(index, 1);
      }
    },
		changeFullReductionType() {
			this.ruleForm.fullGiveRuleDtoList = [{
				fullCount: 1,
				giveCount: 1,
			}]
		},
		onSelectProduct(type) {
			this.selectedProductType = type;
			this.showProductsModal = true;
		},
		confirmProduct(row) {
			if (this.selectedProductType === 'masterSku') {
				this.masterSku = [row];
			} else {
				this.giveSku = [row];
			}
			this.cancelModal();
		},
		cancelModal() {
			this.showProductsModal = false;
		},
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          /* if (this.ruleForm.giveQtyType === 1 && !this.ruleForm.giveQty) {
            this.$message.error('请填写赠品数量限制');
            return;
          } */
					if (this.masterSku.length === 0) {
						this.$message.error('请选择主商品');
            return;
					}
          if (!this.ruleForm.giveData || !this.ruleForm.giveData.giveSkuList.length) {
            this.$message.error('请设置赠品');
            return
          }
					/* if (this.giveSku.length === 0) {
						this.$message.error('请选择赠品');
            return;
					} */
					if (!this.ruleForm.customerGroupId) {
            this.$message.error('请选择人群');
            return;
          }
          if (new Date(this.ruleForm.activeTime[0]).getTime() <= new Date().getTime()) {
            this.$message.error('开始时间需大于当前时间');
            return;
          }
          this.submitLoading = true;
          const { reportId } = this.$route.query;
          const params = {
            name: this.ruleForm.name,
            etime: this.ruleForm.activeTime[1],
            stime: this.ruleForm.activeTime[0],
            customerGroupId: this.ruleForm.customerGroupId,
            id: this.actId || null,
						//masterSkuId: this.masterSku[0].csuid,
						//giveSkuId: this.giveSku[0].csuid,
						//giveQty: this.ruleForm.giveQtyType === -1 ? -1 : this.ruleForm.giveQty,
            fullGiveType: this.masterSku[0].activityType === 1 ? 2 : 1,
            reportId: reportId || null,
            masterSkuList: this.masterSku.map(item => {
              return {
                skuId: item.csuid
              }
            }),
            getOrderGiveMinQty: -1,
            getOrderGiveMaxQty: -1,
            ...this.ruleForm.giveData
          };
          delete params.giveData;

          savePromotion(params).then((res) => {
            if (res.code === 1000) {
                this.$message.success('保存成功');
                setTimeout(() => {
                  this.$router.push('/mzPromotion');
                }, 500);
            } else {
              if (res.data.errDto) {
                this.result.msgList = [res.data.errDto.errMsg]
                this.result.errorUrl = res.data.errDto.errPath;
                this.result.visible = true;
              } else {
                const h = this.$createElement;
                const msgArr = res.msg.split(';');
                const tip = msgArr.map((i) => {
                  return h('div', { style: 'color: #F56C6C;' }, i + ';');
                });
                this.$confirm(h('p', null, tip), '温馨提示', {
                  confirmButtonText: '确定',
                  showCancelButton: false,
                }).then(() => {});
              }
              // this.$message({
              //   message: h('p', null, tip),
              //   type: 'error',
              // });
              this.submitLoading = false;
            }
          }).catch(() => {
            this.submitLoading = false;
          })
        } else {
          console.log('error submit!!');
          return false;
        }
        return false;
      });
    },

    resetForm(formName) {
      this.$refs[formName].resetFields();
      this.$router.push({ path: '/mzPromotion' });
    },
    goBack() {
      this.$router.go(-1);
    }
  }
};
</script>

<style lang="scss" scoped>
.radio-span {
  color: rgba(51, 51, 51, 1);
  line-height: 17px;
  margin-right: 30px;
}
.radio-class {
  margin-right: 0px;
  margin-top: 8px;
}
.serch {
  .el-form-item__label {
    padding: 0 20px 0 0 !important;
  }
  padding: 20px;
  font-weight: 500;
  .sign {
    display: inline-table;
    width: 3px;
    height: 13px;
    background: linear-gradient(360deg, #1d69c4 1%, #8bbdfc 99%);
    border-radius: 2px;
    margin-right: 8px;
  }
  .fromBlock {
    margin-top: 20px;
  }
	.btnText {
		color: #4183d5;
		cursor: pointer;
	}
	.delBtn {
		margin-left: 10px;
		cursor: pointer;
	}
  ::v-deep   .el-table tr td .cell {
    height: auto;
    line-height: normal;
    text-overflow: inherit;
    white-space: break-spaces;
    .el-radio-group {
      text-align: left;
    }
  }
}
.keyValue {
  display: flex;
}
.keyValue span:first-child {
  width: 70px;
  flex-shrink: 0;
  text-align: justify;
  text-align-last: justify;
}
.keyValue span:last-child {
  display: -webkit-box;
  -webkit-box-orient: vertical;
	-webkit-line-clamp: 2;
  overflow: hidden;
	text-overflow: ellipsis;
}
</style>
