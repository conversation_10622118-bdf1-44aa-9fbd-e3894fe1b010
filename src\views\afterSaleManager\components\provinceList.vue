<template>
<!-- 	<el-autocomplete v-model="data" :fetch-suggestions="query" @select="selected" :size="size" @clear="clear" @blur="data = temp" :placeholder="temp" clearable>
		<template slot="append">

		</template>
	</el-autocomplete> -->
	<el-input class="input-with-select" :size="size">
		<template slot="prepend">
			<span>{{ label }}</span>
		</template>
		<template slot="append">
			<el-select style="width:200px;margin-left:0px;" :value="value" @input="change" :multiple="multiple" collapse-tags :size="size" clearable>
				<el-option v-for="item in list" :key="item[valueProp]" :value="item[valueProp]" :label="item[labelProp]"></el-option>
			</el-select>
		</template>
	</el-input>
</template>

<script>
export default {
	props: {
		value: '',
		size: {
			default: "small",
			type: String
		},
		label: {
			default: '',
			type: String
		},
		list: {
			default: () => [],
			type: Array
		},
		multiple: {
			default: false,
			type: <PERSON>olean
		},
		valueProp: {
			default: 'value',
			type: String
		},
		labelProp: {
			default: 'label',
			type: String
		}
	},
	methods: {
		change(val) {
			this.$emit("input", val)
		}
	}
}
</script>

<style lang="scss" scoped>
::v-deep.input-with-select {
	width: 0;
	input {
		padding: 0 !important;
		border-right: none;
	}
  > input {
    width: 0px !important;
  }
}
::v-deep.input-with-select .el-input-group__append {
		background: white;
	}
</style>

