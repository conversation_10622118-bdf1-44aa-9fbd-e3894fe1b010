<template>
  <div
    v-if="shopConfig.shopPatternCode !== 'ybm'"
    class="customerService"
  >
    <div v-permission="['customerService','customerService_entry']">
      <div class="customer_warp">
        <div class="customerAvatar" />
        <div
          class="content"
          @click="openCustomer"
        >
          <p>
            在线客服<span
              v-if="msgCount > 0"
              class="msgCount"
              :class="flicker"
            >{{ msgCount > 99 ? '99+' : msgCount }}</span>
          </p>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapState } from 'vuex';
import { queryMsgCount, queryMsgToken } from '@/api/home';

export default {
  name: 'CustomerService',
  data() {
    return {
      msgCount: 0,
      internalQueryMsg: '',
    };
  },
  created() {
    this.queryMsgCount();
  },
  beforeMount() {
    if (this.internalQueryMsg) {
      clearInterval(this.internalQueryMsg);
    } else {
      this.internalQueryMsg = setInterval(() => {
        this.queryMsgCount();
      }, 15000);
    }
  },
  beforeDestroy() {
    clearInterval(this.internalQueryMsg);
  },
  computed: {
    ...mapState('app', ['shopConfig']),
    flicker() {
      if (Number(this.msgCount) > 0) {
        return 'flicker';
      } if (Number(this.msgCount) > 99) {
        return 'flicker maxH';
      }
      return '';
    },
  },
  methods: {
    async queryMsgCount() {
      const res = await queryMsgCount();
      if (res && res.code === 0) {
        this.msgCount = res.data;
      }
    },
    async openCustomer() {
      const res = await queryMsgToken();
      if (res && res.code === 0) {
        const { token, domainName } = res.data;
        const userId = `&userId=${res.data.userId}`;
        this.msgCount = 0;
        window.closeTab(window.location.href, true);
        console.log(domainName + token + userId);
        window.open(domainName + token + userId);
      }
    },
  },
};
</script>

<style scoped lang="scss">
.customerService {
  position: fixed;
  right: 0;
  top: 50%;
  z-index: 1001;
  transform: translateY(-50%);

  .customer_warp {
    width: 68px;
    height: 80px;
    position: relative;

    .customerAvatar {
      position: absolute;
      top: 0;
      left: 0;
      width: 68px;
      height: 80px;
      background: url("../../../assets/image/home/<USER>");
      background-size: 100% 100%;
    }

    .content {
      cursor: pointer;
      position: absolute;
      left: 50%;
      top: 68px;
      transform: translateX(-50%);
      width: 48px;
      //height: 69px;
      background: #00b955;
      border: 1px solid #05b456; 
      border-radius: 4px;
      color: #fff;
      font-size: 12px;
      line-height: 16px;
      box-szing: border-box;
      padding: 0 10px;
      text-align: center;

      p {
        margin: 7px 0;

        .msgCount {
          min-width: 18px;
          height: 18px;
          display: inline-block;
          margin-top: 4px;
          font-weight: 400;
          line-height: 18px;
          opacity: 1;
          background: #ff2121;
          border-radius: 50%;
        }

        .msgCount.maxH {
          width: 23px;
          height: 23px;
          line-height: 23px;
        }

        .msgCount.flicker {
          -webkit-animation: flicker 1s linear infinite;
          -moz-animation: flicker 1s linear infinite;
          -ms-animation: flicker 1s linear infinite;
          -o-animation: flicker 1s linear infinite;
        }

        @-webkit-keyframes flicker {
          0% {
            opacity: 1;
          }
          50% {
            opacity: 1;
          }
          50.01% {
            opacity: 0;
          }
          100% {
            opacity: 0;
          }
        }
      }

    }
  }

}
</style>
