import layout from '@/layout';

const storeManage = {
  path: '/storeManage',
  name: 'storeManage',
  component: layout,
  meta: {
    title: '店铺管理',
    icon: 'el-icon-s-shop',
  },
  children: [
    {
      path: '/nextDayDeliveryApply',
      name: 'nextDayDeliveryApply',
      component: () => import('@/views/store-management/nextDayDeliveryApply'),
      meta: { title: '次日达服务'},
    },
    {
      path: '/nextDayDelivery',
      name: 'nextDayDelivery',
      component: () => import('@/views/store-management/nextDayDelivery'),
      meta: { title: '次日达配置'},
    },
    {
      path: '/shopBusinessManage',
      name: 'shopBusinessManage',
      component: () => import('@/views/business-circle/index.vue'),
      meta: { title: '店铺商圈管理' },
    },
    {
      path: '/shopCustomerManage',
      name: 'shopCustomerManage',
      component: () => import('@/views/supplyObject/index.vue'),
      meta: { title: '店铺客户管理' },
    },
    {
      path: '/notice',
      name: 'notice',
      component: () => import('@/views/noticeAftersales/notice'),
      meta: { title: '店铺公告', noCache: true },
    },
    {
      path: '/freightManagement',
      name: 'freightManagement',
      component: () => import('@/views/freight-management/freightManagement.vue'),
      meta: { title: '运费管理' },
    },
    {
      path: '/freightManagement',
      name: 'freightManagement',
      component: () => import('@/views/freight-management/freightManagement.vue'),
      meta: { title: '运费配置' }, // 菜单改版
    },
    {
      path: '/exchangeManage',
      name: 'exchangeManage',
      component: () => import('@/views/noticeAftersales/index.vue'),
      meta: { title: '退换货管理' },
    },
    {
      path: '/shopDecoration',
      name: 'shopDecoration',
      component: () => import('@/views/store-management/index.vue'),
      meta: { title: '店铺装修' },
    },
    {
      path: '/expressDeliveryConfig',
      name: 'expressDeliveryConfig',
      component: () => import('@/views/store-management/expressDeliveryConfig/index.vue'),
      meta: { title: '快递面单打印配置' },
    },
    {
      path: '/expressDeliveryConfig',
      name: 'expressDeliveryConfig',
      component: () => import('@/views/store-management/expressDeliveryConfig/index.vue'),
      meta: { title: '面单打印配置' },
    },
    {
      path: '/shopAccountNumManage',
      name: 'shopAccountNumManage',
      component: () => import('@/views/store-management/shopAccountManage.vue'),
      meta: { title: '店铺账号管理' },
    },
    {
      path: '/storeIndex',
      name: 'storeIndex',
      component: () => import('@/views/store-management/storeIndex.vue'),
      meta: { title: '楼层设置' },
    },
    {
      path: '/advertisingSetList',
      name: 'advertisingSetList',
      component: () => import('@/views/store-management/advertisingSetList.vue'),
      meta: { title: '广告设置' },
    },
    {
      path: '/pictureFloor',
      name: 'pictureFloor',
      component: () => import('@/views/store-management/pictureFloor.vue'),
      meta: { title: '图片楼层设置' },
    },
    {
      path: `${process.env.VUE_APP_BASE_API}/address/index`,
      name: 'addressIndex',
      meta: { title: '退货地址管理' },
    },
    {
      path: '/aftersales',
      name: 'aftersales',
      component: () => import('@/views/noticeAftersales/afterSales'),
      meta: { title: '退货须知' },
    },
    {
      path: '/supplyObject',
      name: 'supplyObject',
      component: () => import('@/views/supplyObject/supplyObjectPage'),
      meta: { title: '供货对象设置' },
    },
    {
      path: '/blacklistStoreControlledSales',
      name: 'blacklistStoreControlledSales',
      component: () => import('@/views/store-management/blacklistStoreControlledSales/index.vue'),
      meta: { title: '店铺控销黑名单' },
    },
    {
      path: '/goodsControlledSales',
      name: 'goodsControlledSales',
      component: () => import('@/views/supplyObject/goodsControlledSales.vue'),
      meta: { title: '控销药品设置' },
    },
    {
      path: '/businessCircle',
      name: 'businessCircle',
      component: () => import('@/views/business-circle/businessCircle.vue'),
      meta: { title: '业务商圈管理' },
    },
    {
      path: '/storeAreaSalesControl', 
      name: 'storeAreaSalesControl',
      component: () => import('@/views/store-management/storeAreaSalesControl/index.vue'),
      meta: { title: '店铺区域控销', noCache: true },
    },
    {
      path: '/newRole',
      name: 'newRole',
      component: () => import('@/views/user-management/newRole.vue'),
      hidden: true,
    },
  ],
};
export default storeManage;
