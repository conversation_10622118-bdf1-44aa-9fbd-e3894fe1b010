import Vue from 'vue';
import ElementUI from 'element-ui';
import './styles.scss';
import locale from 'element-ui/lib/locale/lang/zh-CN'; // lang i18n
import dayjs from 'dayjs';
import VueClipboard from 'vue-clipboard2';
import 'dayjs/locale/zh-cn';
import _ from 'lodash';
import App from './App.vue';
import router from './router';
import store from './store';
import xyyTable from './components/table';
import axios from './utils/request';
import util, { openTab,disabledDirective } from './utils/util';
import './permission';
import '@/styles/index.scss';
import { trackingIdentify } from '@/track/eventTracking';
import '@/track/zhuge-io';
import permissionInfo from '@/store/modules/permissionInfo.js'; // 权限判断指令

window._ = _;
Vue.use(permissionInfo);
Vue.use(VueClipboard);

window.axios = axios;
Vue.use(ElementUI, { locale });
Vue.prototype.util = util;
Vue.prototype.$bus = new Vue();
util.install(Vue);
Vue.component(xyyTable.name, xyyTable);
window.openTab = openTab;

Vue.directive('disabled', disabledDirective);
dayjs.locale('zh-cn');
window.dayjs = dayjs;
Vue.config.productionTip = false;
// 用户监听Vue错误
window.Vue = Vue;
// 初始化
if (process.env.NODE_ENV !== 'development' && window.tw) {
  window.tw.set({
    pid: 'pop_merchant',
    dev_mode: process.env.NODE_ENV !== 'production',
  });
}
window.addEventListener('click', () => {
  window.parent.postMessage({ message: 'ifreamClick' }, '*');
});
new Vue({
  router,
  store,
  render: h => h(App),
}).$mount('#app');
