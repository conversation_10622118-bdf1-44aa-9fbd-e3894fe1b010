<template>
  <div class="boxDiv">
    <el-row>
      <el-form
        :model="goodsBasicComVo"
        :rules="goodsBasicComVoRules"
        ref="goodsBasicComVo"
        label-width="190px"
        class="demo-ruleForm"
      >
        <el-row>
          <el-col :span="12">
            <el-form-item label="商品编码:" prop="barcode">
              <el-input
                v-model.trim="goodsBasicComVo.barcode"
                placeholder
                disabled
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="productName">
              <template slot="label">
                <el-tooltip v-if="!disabled" effect="dark" placement="top">
                  <template #content>
                    1、中西成药、原料药类商品录入药品的通用名称。
                    <br />2、中药材、中药饮片类商品录入的商品名称应当与最新版《中国药典》或各省级炮制规范中收载的名称一致，不得使用别名。
                    <br />3、医疗器械类商品按照医疗器械注册证或备案凭证上的产品名称录入商品名称。
                    <br />4、非药类商品录入采用批准文件、备案凭证或说明书、商品标签上的全名，如需加注其它相关内容的统一用半角括号加注在后。
                  </template>
                  <i class="el-icon-warning-outline"></i>
                </el-tooltip>
                <span>商品名称:</span>
              </template>
              <el-input
                v-model.trim="goodsBasicComVo.productName"
                placeholder="支持输入文字、数字、字母及特殊字符"
                :disabled="disabled || goodsBasicComVo.standardProductId !== ''"
                @change="getProductNameChange"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              :label="
                goodsBasicComVo.isInstrument ? '医疗器械名称:' : '通用名称:'
              "
              prop="commonName"
            >
              <el-input
                v-model.trim="goodsBasicComVo.commonName"
                placeholder="支持输入文字、数字、字母及特殊字符"
                :disabled="disabled || goodsBasicComVo.standardProductId !== ''"
              ></el-input>
            </el-form-item>
          </el-col>
          <!-- <el-col :span="12">
            <el-form-item prop="zjm">
              <template slot="label">
                <el-tooltip v-if="!disabled" effect="dark" placement="top">
                  <template #content
                    >商品助记码供客户查询使用，系统默认为商品名称首字母拼接，请勿随意修改</template
                  >
                  <i class="el-icon-warning-outline"></i>
                </el-tooltip>
                <span>商品助记码:</span>
              </template>
              <el-input
                v-model.trim="goodsBasicComVo.zjm"
                placeholder="支持输入数字、字母"
                :disabled="disabled"
                type="text"
                onkeyup="value=value.replace(/[^\w\.\/]/ig,'')"
                @blur="goodsBasicComVo.zjm = $event.target.value"
              ></el-input>
            </el-form-item>
          </el-col> -->
          <el-col :span="12">
            <el-form-item prop="brand">
              <template slot="label">
                <el-tooltip v-if="!disabled" effect="dark" placement="top">
                  <template #content>
                    1、优先录入®标识名称。
                    <br />2、若无带®标识，则录入生产企业名称，录入原则为“去头去尾”；录入信息时需去除生产企业名称中代表地域（省、市、县、区）和
                    “集团”、“药业”、“制药”、“制药厂”、“股份”、“有限公司”等字眼。
                    <br />3、会根据品牌配置不同专区，请谨慎填写。
                  </template>
                  <i class="el-icon-warning-outline"></i>
                </el-tooltip>
                <span>品牌:</span>
              </template>
              <el-input
                v-model.trim="goodsBasicComVo.brand"
                placeholder="支持输入文字、数字、字母及特殊字符"
                :disabled="disabled || goodsBasicComVo.standardProductId !== ''"
                @change="getBrandChange"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="showName">
              <template slot="label">
                <el-tooltip v-if="!disabled" effect="dark" placement="top">
                  <template #content>
                    1、该字段为APP端及PC端客户可见名称，请谨慎填写。
                    <br />2、药品和中药应该包含商品名称内容，非药类商品录入采用批准文件、备案凭证或说明书、商品标签上的全名，如需加注其它相关内容的统一用半角括号加注在后。
                  </template>
                  <i class="el-icon-warning-outline"></i>
                </el-tooltip>
                <span>展示名称:</span>
              </template>
              <el-input
                v-model="goodsBasicComVo.showName"
                placeholder="支持输入文字、数字、字母及特殊字符"
                :disabled="disabled || goodsBasicComVo.standardProductId !== ''"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="code" :rules="[{ required: false},{ max: 13, message: '商品条码最多输入13个数字' }]">
              <template slot="label">
                <el-tooltip v-if="!disabled" effect="dark" placement="top">
                  <template #content> 无69码可录入0 </template>
                  <i class="el-icon-warning-outline" />
                </el-tooltip>
                <span>商品条码（69）码:</span>
              </template>

              <el-input
                v-model.trim="goodsBasicComVo.code"
                placeholder="支持输入数字"
                :disabled="disabled"
                type="text"
                :maxlength="13"
                onkeyup="value=value.replace(/[^\d]/g,'')"
                @blur="goodsBasicComVo.code = $event.target.value"
              />
            </el-form-item>
          </el-col>
             <!-- 是否有追溯码begin -->
             <el-col :span="12">
            <el-form-item label="是否有追溯码:">
              <el-select v-model="goodsBasicComVo.tracingCode" placeholder="请选择" class="regional" :disabled="disabled">
                <el-option
                  v-for="item in options"
                  :key="item.value"
                  :value="item.value"
                  :label="item.label"
                ></el-option>
              </el-select>
                  <span class="effectPictureHover">
                      <span>效果图</span>
                      <img src="@/assets/image/product/effect.png" alt="">
                  </span>
            </el-form-item>
          </el-col>
          <!-- 是否有追溯码end -->

          <el-col
            v-if="
              erpFirstCategoryId === '100004' ||
              erpFirstCategoryId === '100010' ||
              erpFirstCategoryId === '262683'
            "
            :span="12"
          >
            <el-form-item label="别名:">
              <el-input
                v-model.trim="goodsBasicComVo.aliasName"
                placeholder="请输入别名"
                :disabled="disabled"
                type="text"
              />
            </el-form-item>
          </el-col>
          <el-col
            v-if="
              erpFirstCategoryId !== '100004' &&
              erpFirstCategoryId !== '100010' &&
              erpFirstCategoryId !== '262683'
            "
            :span="12"
          >
            <el-form-item
              :prop="
                goodsBasicComVo.isInstrument
                  ? 'instrumentNumber'
                  : 'approvalNumber'
              "
            >
              <template slot="label">
                <el-tooltip v-if="!disabled" effect="dark" placement="top">
                  <template #content>
                    根据批准文件、说明书录入。“养生中药”、“配方饮片”、“中药材”无批准文号可填-
                  </template>
                  <i class="el-icon-warning-outline" />
                </el-tooltip>
                <span>{{
                  goodsBasicComVo.isInstrument
                    ? '医疗器械注册证或备案凭证编号:'
                    : ['100001', '100007'].includes(erpFirstCategoryId) ? "化妆品备案编号/注册证号:" : "批准文号:"
                }}</span>
              </template>

              <el-input
                v-model="
                  goodsBasicComVo[
                    goodsBasicComVo.isInstrument
                      ? 'instrumentNumber'
                      : 'approvalNumber'
                  ]
                "
                :placeholder="'支持输入文字、数字、字母及特殊字符'"
                :disabled="disabled || goodsBasicComVo.standardProductId !== ''"
                @change="handleChangeInstrumentNumberOrApprovalNumber"
              />
              <div
                v-if="goodsBasicComVo.isInstrument"
                style="font-size: 12px; color: #ff2400; line-height: 14px"
              >
                <p>
                  第一类医疗器械品种填写医疗器械备案凭证编号，第二、三类医疗器械品种填写医疗器械注册证编号
                </p>
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="12" v-if="goodsBasicComVo.isInstrument">
            <el-form-item>
              <template slot="label">
                <!-- <el-tooltip v-if="!disabled" effect="dark" placement="top">
                  <template #content>
                    根据批准文件、说明书录入。“养生中药”、“配方饮片”、“中药材”无批准文号可填-
                  </template>
                  <i class="el-icon-warning-outline" />
                </el-tooltip> -->
                <span
                  style="color: #f56c6c"
                  v-if="
                    goodsBasicComVoRules['instrumentLicenseEffect'][0]
                      .required == true
                  "
                  >*
                </span>
                <span>医疗器械注册证:</span>
              </template>
              <div style="display: flex; align-items: center">
                <div
                  style="
                    display: flex;
                    align-items: center;
                    color: #606266;
                    font-size: 12px;
                  "
                >
                  有效期至
                  <el-form-item prop="instrumentLicenseEffect">
                    <el-date-picker
                      v-model="goodsBasicComVo.instrumentLicenseEffect"
                      value-format="yyyy-MM-dd"
                      placeholder="有效期"
                      type="date"
                      style="width: 200px; margin-left: 10px"
                      :picker-options="instrumentOption"
                      :disabled="disabled"
                    />
                  </el-form-item>
                </div>
                <p
                  style="
                    color: #1890ff;
                    cursor: pointer;
                    margin: 0;
                    margin-left: 10px;
                  "
                  @click="showInstrumentViewer = true"
                >
                  查看示例
                </p>
                <el-dialog
                  title="医疗器械注册证示例"
                  width="70%"
                  @close="showInstrumentViewer = false"
                  :visible.sync="showInstrumentViewer"
                >
                  <div style="display: flex">
                    <div style="flex: 1">
                      <p>《第一类医疗器械备案凭证》</p>
                      <img
                        width="100%"
                        src="@/assets/image/product/instrument1.jpeg"
                        alt
                      />
                    </div>
                    <div style="flex: 1">
                      <p>《医疗器械注册证》</p>
                      <img
                        width="100%"
                        src="@/assets/image/product/instrument2.jpeg"
                        alt
                      />
                    </div>
                  </div>
                </el-dialog>
              </div>
              <div style="display: flex; align-items: center; margin-top: 10px">
                <el-upload
                  ref="myUploaderInstrument"
                  :disabled="disabled"
                  :class="{
                    hide:
                      disabled ||
                      goodsBasicComVo.instrumentLicenseImageList.isUpload
                  }"
                  action
                  :http-request="
                    (file) => uploadImg(file, 'instrumentLicenseImageList')
                  "
                  :before-upload="beforeAvatarUpload"
                  :on-remove="
                    (file) => {
                      return handleRemove(
                        file,
                        goodsBasicComVo.instrumentLicenseImageList.urlVal,
                        'instrumentLicenseImageList'
                      )
                    }
                  "
                  :limit="goodsBasicComVo.instrumentLicenseImageList.maxImg"
                  :file-list="goodsBasicComVo.instrumentLicenseImageList.urlVal"
                  :on-preview="handlePictureCardPreview"
                  name="files"
                  list-type="picture-card"
                  accept=".jpg, .jpeg, .png, .JPG, .JPEG"
                  class="avatar-uploader"
                >
                  <!-- :class="{ hide: !disabled || goodsBasicComVo.imagesList.isUpload }"
                  class="avatar-uploader"-->
                  <!-- <img v-if="goodsBasicComVo.imagesList" :src="goodsBasicComVo.imagesList" class="avatar" /> -->
                  <div style="margin-top: 5px">
                    <i class="el-icon-plus">
                      <div class="avatar-uploader-icon">上传图片</div>
                    </i>
                  </div>
                </el-upload>
              </div>
              <div style="font-size: 12px; color: #ff2400; line-height: 14px">
                <!-- <p>第一类医疗器械品种填写医疗器械备案凭证编号，第二、三类医疗器械品种填写医疗器械注册证编号</p> -->
                <p>
                  支持jpeg、png、jpg格式，宽度需大于等于800 px、大小不超过2M
                </p>
                <p>
                  依据《医疗器械网络销售监督管理办法》第十条要求：第一类医疗器械品种需上传《第一类医疗器械备案凭证》，第二、三类医疗器械品种需上传《医疗器械注册证》。
                </p>
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="12" v-if="goodsBasicComVo.isInstrument">
            <el-form-item>
              <template slot="label">
                <!-- <el-tooltip v-if="!disabled" effect="dark" placement="top">
                  <template #content>
                    根据批准文件、说明书录入。“养生中药”、“配方饮片”、“中药材”无批准文号可填-
                  </template>
                  <i class="el-icon-warning-outline" />
                </el-tooltip> -->
                <span
                  style="color: #f56c6c"
                  v-if="
                    goodsBasicComVoRules['manufacturingLicenseNo'][0]
                      .required == true
                  "
                  >*
                </span>
                <span>生产许可证号或备案凭证编号:</span>
              </template>
              <div style="display: flex; align-items: center">
                <el-form-item
                  :prop="
                    manufacturingLicenseNoALLDisable
                      ? 'noRequired'
                      : manufacturingLicenseNo
                  "
                >
                  <el-input
                    v-model.trim="goodsBasicComVo.manufacturingLicenseNo"
                    placeholder="请输入"
                    style="width: 140px"
                    :disabled="
                      manufacturingLicenseNoALLDisable &&
                      erpFirstCategoryId !== '100005'
                    "
                  />
                </el-form-item>
                <div
                  style="
                    display: flex;
                    align-items: center;
                    margin-left: 10px;
                    color: #606266;
                    font-size: 12px;
                  "
                >
                  有效期至：
                  <el-form-item prop="manufacturingLicenseEffect">
                    <el-date-picker
                      v-model="goodsBasicComVo.manufacturingLicenseEffect"
                      value-format="yyyy-MM-dd"
                      placeholder="有效期"
                      type="date"
                      style="width: 140px"
                      :picker-options="instrumentOption"
                      :disabled="
                        disabled ||
                        (manufacturingLicenseNoALLDisable &&
                          erpFirstCategoryId !== '100005')
                      "
                    />
                  </el-form-item>
                </div>
                <p
                  style="
                    color: #1890ff;
                    cursor: pointer;
                    margin: 0;
                    margin-left: 10px;
                  "
                  @click="showManufacturingViewer = true"
                >
                  查看示例
                </p>
                <el-dialog
                  title="医疗器械生产备案凭证或生产企业许可证示例"
                  width="70%"
                  @close="showManufacturingViewer = false"
                  :visible.sync="showManufacturingViewer"
                >
                  <div style="display: flex">
                    <div style="flex: 1">
                      <p>《第一类医疗器械生产备案凭证》</p>
                      <img
                        width="100%"
                        src="@/assets/image/product/manufacturing1.jpeg"
                        alt
                      />
                    </div>
                    <div style="flex: 1">
                      <p>《医疗器械生产许可证》</p>
                      <img
                        width="100%"
                        src="@/assets/image/product/manufacturing2.png"
                        alt
                      />
                    </div>
                  </div>
                </el-dialog>
              </div>
              <div style="display: flex; align-items: center; margin-top: 10px">
                <el-upload
                  ref="myUploaderInstrument"
                  :disabled="
                    disabled ||
                    (manufacturingLicenseNoALLDisable &&
                      erpFirstCategoryId !== '100005')
                  "
                  :class="{
                    hide:
                      disabled ||
                      goodsBasicComVo.manufacturingLicenseImageList.isUpload
                  }"
                  action
                  :http-request="
                    (file) => uploadImg(file, 'manufacturingLicenseImageList')
                  "
                  :before-upload="beforeAvatarUpload"
                  :on-remove="
                    (file) => {
                      return handleRemove(
                        file,
                        goodsBasicComVo.manufacturingLicenseImageList.urlVal,
                        'manufacturingLicenseImageList'
                      )
                    }
                  "
                  :limit="goodsBasicComVo.manufacturingLicenseImageList.maxImg"
                  :file-list="
                    goodsBasicComVo.manufacturingLicenseImageList.urlVal
                  "
                  :on-preview="handlePictureCardPreview"
                  name="files"
                  list-type="picture-card"
                  accept=".jpg, .jpeg, .png, .JPG, .JPEG"
                  class="avatar-uploader"
                >
                  <!-- :class="{ hide: !disabled || goodsBasicComVo.imagesList.isUpload }"
                  class="avatar-uploader"-->
                  <!-- <img v-if="goodsBasicComVo.imagesList" :src="goodsBasicComVo.imagesList" class="avatar" /> -->
                  <div style="margin-top: 5px">
                    <i class="el-icon-plus">
                      <div class="avatar-uploader-icon">上传图片</div>
                    </i>
                  </div>
                </el-upload>
              </div>
              <div style="font-size: 12px; color: #ff2400; line-height: 14px">
                <p>
                  第一类医疗器械需录入第一类医疗器械生产备案凭证，第二、三类医疗器械需录入医疗器械生产企业许可证
                </p>
                <p>
                  支持jpeg、png、jpg格式，宽度需大于等于800 px、大小不超过2M
                </p>
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="manufacturer">
              <template slot="label">
                <el-tooltip v-if="!disabled" effect="dark" placement="top">
                  <template #content>根据批准文件、说明书录入。</template>
                  <i class="el-icon-warning-outline"></i>
                </el-tooltip>
                <span>生产厂家:</span>
              </template>
              <el-input
                v-model.trim="goodsBasicComVo.manufacturer"
                :maxlength="128"
                placeholder="支持输入文字、数字、字母及特殊字符"
                :disabled="disabled || goodsBasicComVo.standardProductId !== ''"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="erpCode">
              <template slot="label">
                <el-tooltip v-if="!disabled" effect="dark" placement="top">
                  <template #content>ERP系统对接则必填。</template>
                  <i class="el-icon-warning-outline"></i>
                </el-tooltip>
                <span>商品ERP编码:</span>
              </template>
              <el-input
                v-model.trim="goodsBasicComVo.erpCode"
                placeholder="支持输入文字、数字、字母及特殊字符"
                :disabled="disabled || shopConfig.isFbp"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="productionAddress">
              <template slot="label">
                <span>生产厂家地址:</span>
              </template>
              <!-- :disabled="disabled || goodsBasicComVo.standardProductId !== ''" -->
              <el-input
                :maxlength="128"
                v-model.trim="goodsBasicComVo.productionAddress"
                placeholder="支持输入文字、数字、字母及特殊字符"
                :disabled="disabled"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="spec" v-if="!isShowNetUnit">
              <template slot="label">
                <el-tooltip v-if="!disabled" effect="dark" placement="top">
                  <template #content>根据批准文件、说明书录入。</template>
                  <i class="el-icon-warning-outline"></i>
                </el-tooltip>
                <span>{{
                  goodsBasicComVo.isInstrument ? '规格(型号)' : '规格:'
                }}</span>
              </template>
              <el-input
                v-model.trim="goodsBasicComVo.spec"
                placeholder="支持输入文字、数字、字母及特殊字符"
                :disabled="disabled || goodsBasicComVo.standardProductId !== ''"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12" v-if="goodsBasicComVo.isInstrument">
            <el-form-item
              prop="technicalRequirementNo"
              :rules="
                !goodsBasicComVo.technicalRequirementNoCanEmpty && [
                  {
                    required: true,
                    message: '产品技术要求编号不能为空',
                    trigger: 'blur'
                  }
                ]
              "
            >
              <template slot="label">
                <el-tooltip v-if="!disabled" effect="dark" placement="top">
                  <template #content
                    >请按照医疗器械注册证或备案凭证或包装上载明的对应信息录入</template
                  >
                  <i class="el-icon-warning-outline"></i>
                </el-tooltip>
                <span>产品技术要求编号</span>
              </template>
              <el-input
                v-model.trim="goodsBasicComVo.technicalRequirementNo"
                placeholder="请输入"
                :disabled="disabled"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col
            v-if="
              !goodsBasicComVo.isInstrument &&
              (erpFirstCategoryId === '100004' ||
                erpFirstCategoryId === '100010' ||
                erpFirstCategoryId === '262683')
            "
            :span="12"
          >
            <!-- (!goodsBasicComVo.isInstrument || goodsBasicComVo.producerCanEmpty) ? 'noRequired' : 'producer' -->
            <el-form-item prop="producerCopy" v-if="isShowNetUnit">
              <template slot="label">
                <el-tooltip v-if="!disabled" effect="dark" placement="top">
                  <template #content
                    >“养生中药”、“配方饮片”、“中药材”的产地请如实填写，无产地的商品无需填写</template
                  >
                  <i class="el-icon-warning-outline" />
                </el-tooltip>
                <span>产地:</span>
              </template>
              <div class="producterSelect">
                <el-select
                  v-model="goodsBasicComVo.international"
                  class="international"
                  placeholder="请选择"
                  :disabled="disabled || goodsBasicComVo.standardProductId !== ''"
                  @change="internationalChange"
                >
                  <el-option :value="1" label="国内"></el-option>
                  <el-option :value="2" label="国外"></el-option>
                </el-select>
                <!-- 省级选择 -->
                 <el-select
                  v-model="goodsBasicComVo.regional"
                  placeholder="请选择"
                  class="regional"
                  :disabled="disabled || goodsBasicComVo.standardProductId !== ''"
                  @change="changeRegional"
                >
                  <el-option
                    v-for="item in regionList"
                    :key="item.id"
                    :value="item.id"
                    :label="item.id"
                    filterable
                    ></el-option>
                </el-select>
                <el-input
                  v-model.trim="goodsBasicComVo.producerCopy"
                  placeholder="(选填)请填写市县等"
                  :disabled="disabled || goodsBasicComVo.standardProductId !== ''"
                />
              </div>
            </el-form-item>
            <el-form-item prop="producer" v-if="!isShowNetUnit">
              <template slot="label">
                <el-tooltip v-if="!disabled" effect="dark" placement="top">
                  <template #content
                    >“养生中药”、“配方饮片”、“中药材”的产地请如实填写，无产地的商品无需填写</template
                  >
                  <i class="el-icon-warning-outline" />
                </el-tooltip>
                <span>产地:</span>
              </template>
              <el-input
                v-model.trim="goodsBasicComVo.producer"
                placeholder="支持输入文字、数字、字母及特殊字符"
                :disabled="disabled || goodsBasicComVo.standardProductId !== ''"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="mediumPackageNum">
              <template slot="label">
                <el-tooltip v-if="!disabled" effect="dark" placement="top">
                  <template #content
                    >客户加购时会参考中包装数量，请谨慎填写。</template
                  >
                  <i class="el-icon-warning-outline"></i>
                </el-tooltip>
                <span>中包装:</span>
              </template>
              <el-input
                v-model.trim="goodsBasicComVo.mediumPackageNum"
                placeholder="支持输入正整数"
                :disabled="disabled || !isControlSales"
                type="text"
                onkeyup="value=value.replace(/[^\d]/g,'')"
                @blur="goodsBasicComVo.mediumPackageNum = $event.target.value"
              ></el-input>
            </el-form-item>
          </el-col>
          <!-- 需求未确定 !goodsBasicComVo.isInstrument -->
          <el-col
            v-if="['100002', '100009'].includes(erpFirstCategoryId)"
            :span="12"
          >
            <el-form-item prop="drugClassification">
              <template slot="label">
                <el-tooltip v-if="!disabled" effect="dark" placement="top">
                  <template #content>
                    1、药品必须选择处方药/甲类OTC/乙类OTC。
                    <br />2、中药、器械、食品等非药品类的选无。
                  </template>
                  <i class="el-icon-warning-outline"></i>
                </el-tooltip>
                <span>处方类型:</span>
              </template>
              <el-radio-group v-model="goodsBasicComVo.drugClassification">
                <el-radio
                  :label="0"
                  :disabled="
                    disabled || goodsBasicComVo.standardProductId !== ''
                  "
                  >无</el-radio
                >
                <el-radio
                  :label="1"
                  :disabled="
                    disabled || goodsBasicComVo.standardProductId !== ''
                  "
                  >甲类OTC</el-radio
                >
                <el-radio
                  :label="2"
                  :disabled="
                    disabled || goodsBasicComVo.standardProductId !== ''
                  "
                  >乙类OTC</el-radio
                >
                <el-radio
                  :label="3"
                  :disabled="
                    disabled || goodsBasicComVo.standardProductId !== ''
                  "
                  >处方药Rx</el-radio
                >
              </el-radio-group>
            </el-form-item>
          </el-col>


          <!-- 需求未确定 !goodsBasicComVo.isInstrument -->
          <el-col
            v-if="['100002', '100009'].includes(erpFirstCategoryId)"
            :span="12"
          >
            <el-form-item label="剂型:" prop="dosageForm">
              <el-select
                class="select-info"
                v-model="goodsBasicComVo.dosageForm"
                placeholder="请选择"
                @change="dosageFormChange"
                :disabled="disabled || goodsBasicComVo.standardProductId !== ''"
              >
                <el-option
                  v-for="item in dosageFormList"
                  :key="item.name"
                  :value="item.id"
                  :label="item.name"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col
            :span="12"
            v-if="['100002', '100009'].includes(erpFirstCategoryId)"
          >
            <el-form-item label="是否委托生产:" prop="isCommissionProduction">
              <el-radio-group v-model="goodsBasicComVo.isCommissionProduction">

                <el-radio :label="0" :disabled="disabled">否</el-radio>
                <el-radio :label="1" :disabled="disabled"> 是 </el-radio>
              </el-radio-group>
            </el-form-item>

          <el-form-item label="受托生产厂家:" prop="entrustedManufacturer" :rules="[{required:goodsBasicComVo.isCommissionProduction,message:'请填写受托生产厂家',trigger:'blur'}]">
            <el-input
            :disabled="disabled"
                v-model.trim="goodsBasicComVo.entrustedManufacturer"
                placeholder=""
                type="text"
              ></el-input>
            </el-form-item>
             <el-form-item label=" 受托生产厂家地址:" prop="entrustedManufacturerAddress" :rules="[{required:goodsBasicComVo.isCommissionProduction,message:'请填写受托生产厂家地址',trigger:'blur'}]">
            <el-input
            :disabled="disabled"
                v-model.trim="goodsBasicComVo.entrustedManufacturerAddress"
                placeholder=""
                type="text"
              ></el-input>
            </el-form-item>


          </el-col>
          <el-col :span="12">
            <el-form-item label="件装量:" prop="pieceLoading">
              <el-input
                v-model.trim="goodsBasicComVo.pieceLoading"
                placeholder="支持输入正整数"
                :disabled="disabled"
                type="text"
                onkeyup="value=value.replace(/[^\d]/g,'')"
                @blur="goodsBasicComVo.pieceLoading = $event.target.value"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="7" v-if="isShowNetUnit">
            <el-form-item prop="netContent">
              <template v-slot:label>
                <span>每{{ goodsBasicComVo.productUnit }}净含量</span>
              </template>
              <div class="input-with-select">
                <el-input
                  v-model="goodsBasicComVo.netContent"
                  style="width: 80%"
                  placeholder="请输入"
                  size="small"
                  :disabled="(disabled) || (goodsBasicComVo.productUnit !== '' && goodsBasicComVo.standardProductId ? true : false)"
                ></el-input>
                <el-form-item 
                  prop="netContentUnit">
                  <el-select
                    style="width: 80px"
                    v-model="goodsBasicComVo.netContentUnit"
                    placeholder="请选择"
                    :disabled="(disabled) || (goodsBasicComVo.productUnit !== '' && goodsBasicComVo.standardProductId ? true : false)"
                  >
                    <el-option
                      v-for="item in netContentUnitList"
                      :key="item.id"
                      :value="item.id"
                      :label="item.id"
                    ></el-option>
                  </el-select>
                </el-form-item>
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="5" v-if="isShowNetUnit">
            <el-form-item prop="spec" label="包装规格:" label-width="100px">
              <el-input
                v-model.trim="goodsBasicComVo.spec"
                style="width: 100%"
                size="small"
                :disabled="disabled || goodsBasicComVo.standardProductId !== ''"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="term">
              <template slot="label">
                <el-tooltip v-if="!disabled" effect="dark" placement="top">
                  <template #content>
                    1、根据批准文件、说明书录入。
                    <br />2、无有效期品种录入。
                  </template>
                  <i class="el-icon-warning-outline"></i>
                </el-tooltip>
                <span>{{
                  goodsBasicComVo.isInstrument
                    ? '有效期/失效期'
                    : '有效期/保质期:'
                }}</span>
              </template>
              <el-input
                v-model.trim="goodsBasicComVo.term"
                placeholder="有效期/保质期格式为正整数加年月日或-或*，例如：24月”"
                :disabled="disabled"
              ></el-input>
            </el-form-item>
          </el-col>
          
          <el-col :span="12">
            <el-form-item label="包装单位:" prop="productUnit">
              <el-select
                class="select-info"
                v-model="goodsBasicComVo.productUnit"
                placeholder="请选择"
                :disabled="(disabled) || (goodsBasicComVo.productUnit !== '' && goodsBasicComVo.standardProductId ? true : false)"
              >
                <el-option
                  v-for="item in productUnitList"
                  :key="item.name"
                  :value="item.id"
                  :label="item.name"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="isSplit">
              <template slot="label">
                <el-tooltip v-if="!disabled" effect="dark" placement="top">
                  <template #content
                    >客户加购时会根据中包装是否可拆零加购，请谨慎填写。</template
                  >
                  <i class="el-icon-warning-outline"></i>
                </el-tooltip>
                <span>是否可拆零:</span>
              </template>
              <el-radio-group v-model.trim="goodsBasicComVo.isSplit">
                <el-radio :label="1" :disabled="disabled">是</el-radio>
                <el-radio :label="0" :disabled="disabled">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
		  <el-col :span="12" v-if="erpFirstCategoryId === '100001' || erpFirstCategoryId === '100007' || erpFirstCategoryId === '100005'">
			<el-form-item :label="erpFirstCategoryId === '100005' ? '医疗器械注册人/备案人名称' : '化妆品备案人/注册人:'" prop="filingsAuthor">
				<el-input :value="goodsBasicComVo.filingsAuthor" :disabled="disabled"  @input="(val) => {valueInput('goodsBasicComVo', 'filingsAuthor', val, /^.{0,50}$/)}"></el-input>
			</el-form-item>
		  </el-col>
          <el-col
            v-if="
              erpFirstCategoryId === '100002' || erpFirstCategoryId === '100009'
            "
            :span="12"
          >
            <el-form-item label="上市许可持有人:" prop="marketAuthor">
              <el-input
                v-model.trim="goodsBasicComVo.marketAuthor"
                placeholder="请输入上市许可持有人"
                :disabled="disabled || goodsBasicComVo.standardProductId !== ''"
                :maxlength="128"
                type="text"
              />
            </el-form-item>
          </el-col>

          <!-- <el-col :span="12">
            <el-form-item prop="storageCondition">
              <template slot="label">
                <el-tooltip v-if="!disabled" effect="dark" placement="top">
                  <template #content>
                    <div>
                      常温：存储温度控制在10℃~30℃<br />
                      冷藏：存储温度控制在2℃~10℃<br />
                      冷冻：存储温度在0℃以下<br />
                      阴凉：存储温度不超过20℃<br />
                      凉暗：避光存储，且温度不超过20℃
                    </div>
                  </template>
                  <i class="el-icon-warning-outline"></i>
                </el-tooltip>
                <span>存储条件:</span>
              </template>
              <el-select
                class="select-info"
                v-model="goodsBasicComVo.storageCondition"
                placeholder="请选择"
                :disabled="disabled"
              >
                <el-option
                  v-for="item in storageConditionList"
                  :key="item.name"
                  :value="item.id"
                  :label="item.name"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col> -->
          <el-col
            v-if="
              erpFirstCategoryId === '100002' || erpFirstCategoryId === '100009'
            "
            :span="12"
          >
            <el-form-item label="上市许可持有人地址:" prop="marketAuthorAddress">
              <!-- :disabled="disabled || goodsBasicComVo.standardProductId !== ''" -->
              <el-input
                v-model.trim="goodsBasicComVo.marketAuthorAddress"
                placeholder="请输入上市许可持有人地址"
                :maxlength="128"
                type="text"
                :disabled="disabled"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12" v-if="!shopConfig.isFbp">
            <el-form-item>
              <template slot="label">
                <!-- <span style="color: red">* </span> -->
                <el-tooltip v-if="!disabled" effect="dark" placement="top">
                  <template #content>
                    <span
                      >1、导单系数=同一商品药帮忙单价/供应商系统单价。请输入大于0的数字，限3位小数</span
                    ><br />
                    <span
                      >2、系统会根据导单系数自动换算商品药帮忙售价和库存</span
                    ><br />
                    <span style="margin-left: 5px"
                      >2.1）商品药帮忙售价=供应商系统商品单价*导单系数，保留2位小数</span
                    ><br />
                    <span style="margin-left: 5px"
                      >2.2）商品药帮忙总库存=供应商系统商品可售库存/导单系数，向下取整数</span
                    >
                  </template>
                  <i class="el-icon-warning-outline"></i>
                </el-tooltip>

                <span>导单系数:</span>
              </template>
              <el-radio-group v-model="goodsBasicComVo.openFactor">
                <el-radio :label="0">关闭</el-radio>
                <el-radio :label="1"> 开启 </el-radio>
              </el-radio-group>
              <span
                v-if="goodsBasicComVo.openFactor === 1"
                style="
                  margin-left: 10px;
                  color: red;
                  font-size: 12px;
                  line-height: 12px;
                "
                >导单系数会影响商品的售卖价格和可售库存，开启设置后不要随意变更
              </span>
              <div style="margin-top: 10px">
                <el-form-item
                  label=""
                  :prop="
                    goodsBasicComVo.openFactor === 1 ? 'factor' : 'noRequired'
                  "
                >
                  <el-input
                    v-if="goodsBasicComVo.openFactor === 1"
                    v-model.trim="goodsBasicComVo.factor"
                    placeholder="导单系数=同一商品药帮忙单价/供应商系统单价，请输入大于0的数字，限3位小数"
                    onkeyup="value=value.replace(/^\D*(\d*(?:\.\d{0,3})?).*$/g,'$1')"
                    @change="handleChangeFactor"
                    :disabled="disabled"
                  />
                </el-form-item>
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="12" v-if="!goodsBasicComVo.useStandardImage">
            <el-form-item label="商品图片:" prop="imagesList">
              <el-upload
                :disabled="disabled"
                :class="{
                  hide: disabled || goodsBasicComVo.imagesList.isUpload
                }"
                action
                :http-request="(file) => uploadImg(file, 'imagesList')"
                :before-upload="beforeAvatarUploadForProductImg"
                ref="myUploader"
                :limit="goodsBasicComVo.imagesList.maxImg"
                :file-list="goodsBasicComVo.imagesList.urlVal"
                name="files"
                list-type="picture-card"
                accept=".jpg, .jpeg, .png, .JPG, .JPEG"
                class="avatar-uploader"
              >
                <!-- :class="{ hide: !disabled || goodsBasicComVo.imagesList.isUpload }"
                class="avatar-uploader"-->
                <!-- <img v-if="goodsBasicComVo.imagesList" :src="goodsBasicComVo.imagesList" class="avatar" /> -->
                <div style="margin-top: 5px">
                  <i class="el-icon-plus">
                    <div class="avatar-uploader-icon">上传图片</div>
                  </i>
                </div>
                <div slot="file" slot-scope="{ file }">
                  <img
                    :src="file.url"
                    alt=""
                    class="el-upload-list__item-thumbnail"
                    :ref="'file_' + file.uid"
                  />
                  <span class="el-upload-list__item-actions">
                    <span
                      v-if="showIcon(disabled, 'left', file)"
                      @click="
                        handlePictureCardLeft(
                          file,
                          goodsBasicComVo.imagesList.urlVal
                        )
                      "
                      ><i class="el-icon-back"></i
                    ></span>
                    <span
                      class="el-upload-list__item-preview"
                      @click="handlePictureCardPreview(file)"
                      ><i class="el-icon-zoom-in"></i
                    ></span>
                    <span
                      v-if="
                        !disabled ||
                        goodsBasicComVo.instrutionImagesList.isUpload
                      "
                      class="el-upload-list__item-delete"
                      @click="
                        handleRemove(
                          file,
                          goodsBasicComVo.imagesList.urlVal,
                          'imagesList'
                        )
                      "
                      ><i class="el-icon-delete"></i
                    ></span>
                    <span
                      v-if="showIcon(disabled, 'right', file)"
                      class="el-upload-list__item-right"
                      @click="
                        handlePictureCardRight(
                          file,
                          goodsBasicComVo.imagesList.urlVal
                        )
                      "
                      ><i class="el-icon-right"></i
                    ></span>
                  </span>
                </div>
              </el-upload>
            </el-form-item>
            <div
              style="font-size: 12px; color: #ff2400; padding: 0 60px"
              v-html="getwarningTitle()"
            ></div>
            <div style="font-size: 12px; color: #ff2400; padding: 0 60px">
              支持jpeg、png、jpg、gif格式，最多支持5张。图片宽高保持1:1，800px≤宽高≤1500px，大小不超过2M
            </div>
          </el-col>
          <el-col v-if="!goodsBasicComVo.useStandardImage" :span="12">
            <el-form-item label="详情图片:" prop="instrutionImagesList">
              <el-upload
                :disabled="disabled"
                :class="{
                  hide:
                    disabled || goodsBasicComVo.instrutionImagesList.isUpload
                }"
                action
                :http-request="
                  (file) => uploadImg(file, 'instrutionImagesList')
                "
                :before-upload="beforeAvatarUploadForProductImg"
                :on-remove="
                  (file) => {
                    return handleRemove(
                      file,
                      goodsBasicComVo.instrutionImagesList.urlVal,
                      'instrutionImagesList'
                    )
                  }
                "
                ref="myUploader"
                :limit="goodsBasicComVo.instrutionImagesList.maxImg"
                :file-list="goodsBasicComVo.instrutionImagesList.urlVal"
                :on-preview="handlePictureCardPreview"
                name="files"
                list-type="picture-card"
                accept=".jpg, .jpeg, .png, .JPG, .JPEG"
                class="avatar-uploader"
              >
                <div style="margin-top: 5px">
                  <i class="el-icon-plus">
                    <div class="avatar-uploader-icon">上传图片</div>
                  </i>
                </div>
              </el-upload>
              <div style="font-size: 12px; color: #ff2400">
                支持jpeg、png、jpg、gif格式，最多支持5张。800px≤图片宽度≤1500px，高≤1500px，大小不超过2M
              </div>
            </el-form-item>
          </el-col>
          <el-col v-if="goodsBasicComVo.imageUrlStandard" :span="12">
            <el-form-item label="标品主图:" prop="imagesList">
              <div style="display: flex; align-items: center; flex-wrap: wrap">
                <div style="display: flex">
                  <!-- <img
                    v-for="(item, index) in goodsBasicComVo.imageUrlStandard.split(',')"
                    :key="index"
                    :src="`${bigImgUrlPrefix}${item}`"
                    style="display: block;width: 64px;height: 64px;margin-right: 10px"
                  > -->
                  <el-image
                    v-for="(
                      item, index
                    ) in goodsBasicComVo.imageUrlStandard.split(',')"
                    :key="index"
                    style="
                      display: block;
                      width: 64px;
                      height: 64px;
                      margin-right: 10px;
                    "
                    :src="`${bigImgUrlPrefix}${item}`"
                    :preview-src-list="[`${bigImgUrlPrefix}${item}`]"
                    @click.prevent
                  />
                </div>
                <el-checkbox
                  v-model="goodsBasicComVo.useStandardImage"
                  :disabled="disabled"
                  @change="handleChangeUseStandardImage"
                >
                  使用标品图片（有新包装时，同步更新）
                </el-checkbox>
              </div>
            </el-form-item>
          </el-col>
          <el-col v-if="goodsBasicComVo.instrutionImageUrlStandard" :span="12">
            <el-form-item
              label="标品库详情图片:"
              prop="instrutionImageUrlStandard"
            >
              <div style="display: flex; flex-wrap: wrap">
                <el-image
                  v-for="(
                    item, index
                  ) in goodsBasicComVo.instrutionImageUrlStandard.split(',')"
                  :key="index"
                  style="
                    display: block;
                    width: 64px;
                    height: 64px;
                    margin-right: 10px;
                  "
                  :src="`${bigDescImgUrlPrefix}${item}`"
                  :preview-src-list="[`${bigDescImgUrlPrefix}${item}`]"
                  @click.prevent
                />
                <!-- <img
                  v-for="(item, index) in goodsBasicComVo.instrutionImageUrlStandard.split(',')"
                  :key="index"
                  :src="`${bigDescImgUrlPrefix}${item}`"
                  style="display: block;width: 64px;height: 64px;margin-right: 10px"
                > -->
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <el-divider />
    </el-row>
    <el-dialog :visible.sync="dialogVisible">
      <img width="100%" :src="dialogImageUrl" alt />
    </el-dialog>
    <el-dialog title="匹配结果" :visible.sync="dialogVisible1" width="80%">
      <div style="height: 500px;overflow: auto;">
        <ChooseList
        :prop-data="goodsList"
        @chooseGoods="chooseGoods"
        ></ChooseList>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible1 = false;init = true">取 消</el-button>
        <el-button type="primary" @click="getDetails">确 定</el-button>
      </span>
    </el-dialog>
    <el-dialog title="温馨提示" :visible.sync="dialogVisible2" width="40%">
      {{ dialogVisible2txt }}
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="dialogVisible2 = false;init = true">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import {
  apiUploadProductImage,
  apiConfig,
  apiProductUnitList,
  apiAttributeOptions,
  apiCamelChars,
  getProductEditProp,
  addProductEditProp,
  featureMatching,
  KSChecker
} from '@/api/product'
import ChooseList from './chooseList.vue'
import { getRegionListNoSuffix,getForeignEnum } from '@/api/data-statistics/index'
import { mapState } from 'vuex'

export default {
  name: 'GoodsBasicCom',
  props: {
    basicData: {
      type: Object,
      default() {
        return {}
      }
    },
    formModel: {
      type: Object,
      default() {
        return {}
      }
    }
  },
  components:{ ChooseList },
  data() {
    // https://yapi.int.ybm100.com/project/891/interface/api/89928
    var validatePass = (rule, value, callback) => {
      if (this.goodsBasicComVo.useStandardImage) {
        callback()
        return
      }
      if (
        this.erpFirstCategoryId === '100002' ||
        this.erpFirstCategoryId === '100009'
      ) {
        if (value.urlVal.length < 2) {
          callback(new Error('商品图片信息不完整'))
          return
        }
      } else {
        if (value.urlVal.length < 1) {
          callback(new Error('商品图片信息不完整'))
          return
        }
      }
      callback()
    }
    return {
      init: false,
      productData:{},
      goodsList: [],
	  firstCategoryName: '',
      erpFirstCategoryId: '', // 一级分类
      instrumentOption: { disabledDate: (time) => time.getTime() < Date.now() },
      disabled: false,
      isControlSales: false,//true为控销店铺false为非控销店铺
      dosageFormList: [],
      productUnitList: [],
      storageConditionList: [],
      businessEndTimeDisabled: false,
      dialogImageUrl: '',
      dialogVisible: false,
      dialogVisible1: false,
      dialogVisible2: false,
      dialogVisible2txt: '',
      propsData: {},
      bigDescImgUrlPrefix: '',
      bigImgUrlPrefix: '',
      smallDescImgUrlPrefix: '',
      smallImgUrlPrefix: '',
      showInstrumentViewer: false,
      showManufacturingViewer: false,
      options: [{
        value: 0,
        label: '否'
      }, {
        value: 1,
        label: '是'
      }],
      goodsBasicComVo: {
        tracingCode:null,
        aliasName: '', // 别名
        marketAuthor: '', // 上市许可持有人
        marketAuthorAddress: '', // 上市许可持有人地址
        factor: '',
        openFactor: 0,
        instrumentImageValid: false,
        manufacturingImageValid: false,
        instrumentNumber: '',
        manufacturingLicenseEffect: '',
        manufacturingLicenseNo: '',
        instrumentLicenseEffect: '',
        isInstrument: false,
        instrutionImageUrlStandard: '', // 标品库详情图片
        effectCanEmpty: false, // 有效期是否可以为空
        producerCanEmpty: false, // 产地是否可以为空
        imageUrlStandard: '', // 标品图片
        useStandardImage: false, // 使用标品图片
        barcode: '', // 商品编号
        productName: '', //商品名称
        commonName: '', //通用名称
		filingsAuthor: '',
        // zjm: '', //助记码
        brand: '', // 品牌
        showName: '', //展示名称
        code: '', //商品条码（69）码
        approvalNumber: '', //批准文号
        manufacturer: '', //生产厂家
        productionAddress: '', //生产厂家地址
        erpCode: '', // 商品ERP编码
        spec: '', // 规格
        technicalRequirementNo: '', // 产品技术要求编号
        technicalRequirementNoCanEmpty: '', // 产品技术要求编号是否可以为空,true可以为空
        drugClassification: 0, // 处方类型
        mediumPackageNum: '', // 中包装
        pieceLoading: '', // 件装量
        dosageForm: '', //剂型
        standardProductId: '', //标准库id
        productUnit: '', // 包装单位
        term: '', // 有效期/保质期
        storageCondition: '', // 储存条件
        isSplit: 0, // 是否可拆零（0:不可拆零；1:可拆零）
        producer: '', // 产地
        producerCopy: "",
        isCommissionProduction:0,//是否委托生产
        entrustedManufacturer:'',//受托生产厂家
        entrustedManufacturerAddress:"",
        imagesList: {
          urlVal: [],
          maxImg: 5,
          isUpload: false
        }, // 商品图片（短路径，需要拼接前缀才可展示）
        instrutionImagesList: {
          urlVal: [],
          maxImg: 5,
          isUpload: false
        }, // 详情（短路径，需要拼接前缀才可展示）
        instrumentLicenseImageList: {
          urlVal: [],
          maxImg: 5,
          isUpload: false
        },
        manufacturingLicenseImageList: {
          urlVal: [],
          maxImg: 5,
          isUpload: false
        },
        netContent: '', // 包装净含量
        //净含量单位
        netContentUnit: '',
        // 国内外选择
        international: 1,
        // 地区选择
        regional: '',
      },
      // 净含量枚举
      netContentUnitList: [
        {id: 'g'},{id: 'kg'}
      ],
      goodsBasicComVoRules: {
        marketAuthor: [
          {
            required: false,
            message: '上市许可持有人不能为空',
            trigger: 'blur'
          }
        ],
        marketAuthorAddress: [
          {
            required: false,
            message: '上市许可持有人地址不能为空',
            trigger: 'blur'
          }
        ],
        productionAddress: [
          {
            required: false,
            message: '生产厂家地址不能为空',
            trigger: 'blur'
          }
        ],
        factor: [
          { required: true, message: '导单系数不能为空', trigger: 'blur' }
        ],
        barcode: [
          { required: true, message: '商品编码不能为空', trigger: 'blur' }
        ],
        productName: [
          { required: true, message: '商品名称不能为空', trigger: 'blur' }
        ],
        commonName: [
          { required: true, message: '商品通用名不能为空', trigger: 'blur' }
        ],
        // zjm: [
        //   { required: true, message: '商品助记码不能为空', trigger: 'blur' }
        // ],
        showName: [
          { required: true, message: '展示名称不能为空', trigger: 'blur' }
        ],
        code: [
          { required: false, message: '商品条码不能为空', trigger: 'blur' },
          { max: 13, message: '商品条码最多输入13个数字' }
        ],
        approvalNumber: [
          { required: true, message: `批准文号不能为空`, trigger: 'blur' }
        ],
        instrumentNumber: [
          {
            required: true,
            message: '医疗器械注册证或备案凭证编号不能为空',
            trigger: 'blur'
          }
        ],
        manufacturingLicenseNo: [
          {
            required: false,
            message: '生产许可证号或备案凭证编号格式不正确',
            trigger: 'blur'
          }
        ],
        manufacturer: [
          { required: true, message: '生产厂家不能为空', trigger: 'blur' }
        ],
        spec: [{ required: true, message: '规格不能为空', trigger: 'blur' }],
        producer: [
          { required: false, message: '产地不能为空', trigger: 'blur' }
          // {
          //   pattern: /^[\u4e00-\u9fa5-]{0,}$/,
          //   message: '仅支持输入文字及特殊字符“-”',
          // },
        ],
        drugClassification: [
          { required: false, message: '处方类型不能为空', trigger: 'blur' }
        ],
        // mediumPackageNum: [
        //   { required: false, message: '中包装不能为空', trigger: 'blur' }
        // ],
        dosageForm: [
          { required: false, message: '剂型不能为空', trigger: 'blur' }
        ],
        productUnit: [
          {
            required: false,
            message: '单位不规范，请重新选择',
            trigger: 'blur'
          }
        ],
        producerCopy: [
          {
            // validator: (rule, value, callback) => {
            //   if(!this.goodsBasicComVo.regional) {
            //     callback(new Error('请选择地区'));
            //   }else {
            //     callback();
            //   }
            // }
            // , trigger: 'blur'
          }
        ],
        term: [
          {
            required: false,
            message: '有效期/保质期”不能为空',
            trigger: 'blur'
          }
          // {
          //   pattern: /^[0-9]*$/,
          //   message: '有效期/保质期格式为正整数加年月日或-或*，例如：24月'
          // }
        ],
        noRequired: [{ required: false }],
        instrumentLicenseEffect: [
          {
            required: false,
            message: '医疗器械注册证及有效期至不能为空',
            trigger: 'blur'
          }
        ],
        manufacturingLicenseEffect: [
          {
            required: false,
            message: '生产许可证书及有效期至不能为空',
            trigger: 'blur'
          }
        ],
        // storageCondition: [
        //   { required: false, message: '储存条件不能为空', trigger: 'blur' }
        // ],
        // isSplit: [
        //   { required: true, message: '是否可拆零不能为空', trigger: 'blur' }
        // ],
        pieceLoading: [
          { required: false, message: '件装量不能为空', trigger: 'blur' }
        ],
        brand: [{ required: false, message: '品牌不能为空', trigger: 'blur' }],
        erpCode: [
          { required: false, message: '商品ERP编码不能为空', trigger: 'blur' }
        ],
        instrutionImagesList: [
          { required: false, message: '详情图片不能为空', trigger: 'blur' }
        ],
        imagesList: [
          { required: false, validator: validatePass, trigger: 'blur' }
        ],
		filingsAuthor: [
			{ required: false, message: this.erpFirstCategoryId == '100005' ? '医疗器械注册人/备案人名称' : '化妆品备案人/注册人不能为空', trigger: 'blur' }
		]
      },
      manufacturingLicenseNoALLDisable: false,
      // 是否展示净含量单位
      isShowNetUnit: false,
      // 地区列表
      regionList: [],
      // 省份列表
      provinceList: [],
      // 国家列表
      countryList: [],
      // 原生防抖计时器
      debounceTimer: null,
    }
  },
  watch: {
    basicData: {
      immediate: true,
      handler(newVale) {
        this.$nextTick(() => {
          console.log(newVale,'newVale');
          
          this.propsData = JSON.parse(JSON.stringify(newVale))
          this.init = newVale.isInit
		  this.firstCategoryName = this.$route.query.firstCategoryName;
		  /* if (this.skuCategory == '化妆品') {
			this.goodsBasicComVo.filingsAuthor = '';
		  } */
          this.initData()
        })
      }
    },
    formModel: {
      handler(newVal) {
        console.log(newVal,'formModel');
        
        var base = JSON.parse(JSON.stringify(newVal))
        this.disabled = base.isEdit == 1
      },
      immediate: true,
      deep: true
    },
    goodsBasicComVo: {
      handler: function (newVal) {
        const pipei = sessionStorage.getItem('pipeiProduct')
        if (pipei == 'true') return
        this.debounceCheckFeatures(newVal);
        // this.initData()
      },
      deep: true
    },
    'goodsBasicComVo.netContentUnit': {
      handler(newVal) {
        // 当单位变化时，触发净含量的表单验证
        if(newVal) {
          this.$refs.goodsBasicComVo && this.$refs.goodsBasicComVo.validateField('netContent');
        }
      }
    },
  },
  created() {
    this.debounceTimer = null;
    window.sessionStorage.setItem(
      'goodsBasicComVoRules',
      JSON.stringify(this.goodsBasicComVoRules)
    )
    apiConfig().then((res) => {
      if (res.data) {
        this.bigDescImgUrlPrefix = res.data.bigDescImgUrlPrefix // 详情大图地址前缀
        this.bigImgUrlPrefix = res.data.bigImgUrlPrefix // 商品大图地址前缀
        this.smallDescImgUrlPrefix = res.data.smallDescImgUrlPrefix // 详情小图地址前缀
        this.smallImgUrlPrefix = res.data.smallImgUrlPrefix // 商品小图地址前缀
      }
    })
    apiProductUnitList().then((res) => {
      if (res.data) {
        this.productUnitList = res.data || []
      }
    })
    apiAttributeOptions({ fieldType: 'dosageForm' }).then((res) => {
      if (res.data) {
        this.dosageFormList = res.data || []
      }
    })
    apiAttributeOptions({ fieldType: 'storageCondition' }).then((res) => {
      if (res.data) {
        this.storageConditionList = res.data || []
      }
    })
    this.initRegionalList()
  },
  computed: { 
    ...mapState('app', ['shopConfig']),
  },
  mounted() {
    this.initShowNetUnit()
  },
  activated() {
    this.initShowNetUnit()
  },
  methods: {
    chooseGoods(data) {
      this.productData = {...data}
    },
    initShowNetUnit() {
      const spuCategoryCode = this.$route.query.spuCategoryCode
      if(spuCategoryCode) {
        this.isShowNetUnit = spuCategoryCode == 2
      }else {
        this.isShowNetUnit = ['100010','100004','262683'].includes(this.erpFirstCategoryId)
      }
    },
    initRegionalList() {
      getRegionListNoSuffix().then((res) => {
        this.provinceList = res.data || []
        if(this.goodsBasicComVo.international == 1) {
          this.regionList = this.provinceList
          // 匹配选择的地区
          this.matchArea(this.goodsBasicComVo.producer)
        }
      })
      getForeignEnum().then((res) => {
        this.countryList = res.data || []
        if(this.goodsBasicComVo.international == 2) {
          this.regionList = this.provinceList
          // 匹配选择的地区
          this.matchArea(this.goodsBasicComVo.producer)
        }
      })
    },
    internationalChange(value) {
      if(value == 1) {
        this.regionList = this.provinceList
      }else if(value == 2) {
        this.regionList = this.countryList
      }
      this.goodsBasicComVo.regional = ""
    },
    matchArea(value) {
      if(!value) {
        this.goodsBasicComVo.regional = ""
      } else if(this.isShowNetUnit) {
        // 先尝试匹配国内省份
        let matched = false
        // 遍历provinceList查找匹配
        for(let i = 0; i < this.provinceList.length; i++) {
          const province = this.provinceList[i]
          if(value.startsWith(province.id)) {
            this.goodsBasicComVo.regional = province.id
            this.goodsBasicComVo.international = 1
            // 提取省份后面的剩余地址部分放入producerCopy
            this.goodsBasicComVo.producerCopy = value.substring(province.id.length)
            matched = true
            break
          }
        }
        // 如果没有匹配到省份，尝试匹配国外
        if(!matched) {
          for(let i = 0; i < this.countryList.length; i++) {
            const country = this.countryList[i]
            if(value.startsWith(country.id)) {
              this.goodsBasicComVo.regional = country.id
              this.goodsBasicComVo.international = 2
              this.goodsBasicComVo.producerCopy = value.substring(country.id.length)
              matched = true
              break
            }
          }
        }
        // 如果都没匹配上，使用默认值
        if(!matched) {
          this.goodsBasicComVo.regional = ""
          this.goodsBasicComVo.producerCopy = value // 如果没匹配上，整个值放入producerCopy
        }
      }
    },
    getDetails() {
      var temp
      temp = this.productData.drugClassification
      this.productData.drugClassification = this.productData.drugClassificationCode
      this.productData.drugClassificationCode = temp
      sessionStorage.setItem('pipeiProduct', 'true')
      setTimeout(() => {
        this.dialogVisible1=false
        this.$emit('initData', {...this.productData, isInit: true})
      }, 300);
    },
    debounceCheckFeatures(newVal) {// 防抖函数
      clearTimeout(this.debounceTimer);
      this.debounceTimer = setTimeout(() => {
        this.checkFeatures(newVal);
      }, 500);
    },
    checkFeatures(newVal) {//五要素匹配
      const pipei = sessionStorage.getItem('pipeiProduct')
      if (pipei == 'true') return console.log("pipei",pipei);
      const { manufacturer, code, spec, approvalNumber, commonName, barcode, erpCode } = newVal;
      let isFeature = {};
      let allFieldsFilled = true;
      const categoryFeatureMap = {
        'chinese': () => {
          commonName ? isFeature.commonName = commonName : allFieldsFilled = false
          manufacturer ? isFeature.manufacturer = manufacturer : allFieldsFilled = false
          spec ? isFeature.spec = spec : allFieldsFilled = false
          isFeature.code = code || '0'
          isFeature.approvalNumber = '0'
        },
        'medical': () => {
          commonName ? isFeature.commonName = commonName : allFieldsFilled = false
          approvalNumber ? isFeature.approvalNumber = approvalNumber : allFieldsFilled = false
          spec ? isFeature.spec = spec : allFieldsFilled = false
          isFeature.code = code || '0'
          manufacturer ? isFeature.manufacturer = manufacturer : allFieldsFilled = false
        },
        'personalCare': () => {
          commonName ? isFeature.commonName = commonName : allFieldsFilled = false
          approvalNumber ? isFeature.approvalNumber = approvalNumber : allFieldsFilled = false
          spec ? isFeature.spec = spec : allFieldsFilled = false
          isFeature.code = code || '0'
          manufacturer ? isFeature.manufacturer = manufacturer : allFieldsFilled = false
        },
        'food': () => {
          commonName ? isFeature.commonName = commonName : allFieldsFilled = false
          approvalNumber ? isFeature.approvalNumber = approvalNumber : allFieldsFilled = false
          spec ? isFeature.spec = spec : allFieldsFilled = false
          isFeature.code = code || '0'
          manufacturer ? isFeature.manufacturer = manufacturer : allFieldsFilled = false
        },
        'default': () => {
          commonName ? isFeature.commonName = commonName : allFieldsFilled = false
          approvalNumber ? isFeature.approvalNumber = approvalNumber : allFieldsFilled = false
          spec ? isFeature.spec = spec : allFieldsFilled = false
          isFeature.code = code || '0'
          manufacturer ? isFeature.manufacturer = manufacturer : allFieldsFilled = false
        }
      };
      
      isFeature.barcode = barcode || '';
      isFeature.erpCode = erpCode || '';
      const categoryMap = {
        '100001': 4, // 个人护理品
        '100002': 1, // 西药
        '100003': 4, // 保健食品
        '100004': 2, // 中药
        '100005': 3, // 医疗器械
        '100007': 4, // 日用百货
        '100008': 4, // 普通食品
        '100009': 1, // 西药
        '100010': 2, // 中药
        '262683': 2  // 中药
      };
      isFeature.spuCategory = categoryMap[this.erpFirstCategoryId] || 1;
      const isChinese = ['100004', '100010', '262683'].includes(this.erpFirstCategoryId);
      const isMedical = this.erpFirstCategoryId === '100005';
      const isPersonalCare = ['100001', '100007'].includes(this.erpFirstCategoryId);
      const isFood = ['100003', '100008'].includes(this.erpFirstCategoryId);
      const categoryType = isChinese ? 'chinese' : 
                          isMedical ? 'medical' : 
                          isPersonalCare ? 'personalCare' : 
                          isFood ? 'food' : 'default';
      
      categoryFeatureMap[categoryType]();  
      if (allFieldsFilled) {
        featureMatching(isFeature).then((res) => {
          if (res.code == 0) {
            this.goodsList = res.data
            if(this.goodsList.length > 0 && this.init) {
              this.dialogVisible1 = true
            }
            } else {
            this.dialogVisible2txt = res.message
            this.dialogVisible2 = true
            this.$emit('goodsBasicComSbmit', true)
          }
        }).catch((e) => {
          this.$message.error("稍后再试")
        })
      }
    },
    showIcon(disabled, type, file) {
      const i = this.goodsBasicComVo.imagesList.urlVal.findIndex((item) => {
        return item.uid === file.uid
      })
      if (disabled || (i === 0 && type === 'left')) {
        return false
      }

      if (
        disabled ||
        (i === this.goodsBasicComVo.imagesList.urlVal.length - 1 &&
          type === 'right')
      ) {
        return false
      }

      return true
    },
    getwarningTitle() {
      //中药材还没有加上，没有找到编号
      if (
        this.erpFirstCategoryId === '100002' ||
        this.erpFirstCategoryId === '100009'
      ) {
        return '第一张图片应包含品牌信息的商品包装正面图，作为第一张在APP端展示；<br />第二张图片应包含商品名称、规格、厂家、批文等商品基础信息侧面图；'
      } else if (this.erpFirstCategoryId === '100005') {
        return '*包含商品名称、医疗器械名称、医疗器械许可证或备案凭证编号、生产厂家、规格(型号)等的商品包装正面图，作为第一张在APP端展示；'
      } else {
        return '*包含商品名称、通用名称、生产厂家、规格、产地的商品包装正面图，作为第一张在APP端展示；'
      }
    },
    handlePictureCardLeft(file, fileList) {
      let fileIndex = fileList.findIndex((item) => {
        return file.uid === item.uid
      })

      let cloneList = _.cloneDeep(fileList)
      if (fileIndex !== -1 && fileIndex !== 0) {
        ;[cloneList[fileIndex - 1], cloneList[fileIndex]] = [
          cloneList[fileIndex],
          cloneList[fileIndex - 1]
        ]
        this.goodsBasicComVo.imagesList.urlVal = cloneList
      }
    },
    handlePictureCardRight(file, fileList) {
      let fileIndex = fileList.findIndex((item) => {
        return file.uid === item.uid
      })
      let cloneList = _.cloneDeep(fileList)
      if (fileIndex !== -1 && fileIndex < fileList.length) {
        ;[cloneList[fileIndex], cloneList[fileIndex + 1]] = [
          cloneList[fileIndex + 1],
          cloneList[fileIndex]
        ]
        this.goodsBasicComVo.imagesList.urlVal = cloneList
      }
    },
    handleChangeFactor(val) {
      this.goodsBasicComVo.factor = val
    },
    handleChangeInstrumentNumberOrApprovalNumber(val) {
      if (this.goodsBasicComVo.isInstrument) {
        this.goodsBasicComVo.approvalNumber = val

        // 是否包含 “许”或“进”或“国械备”字
        if (new RegExp('国械备|进|许').test(val)) {
          this.manufacturingLicenseNoALLDisable = true
        } else {
          this.manufacturingLicenseNoALLDisable = false
        }
      }
	  /* if (this.skuCategory == '化妆品') {
		console.log(/^[a-z|A-Z|0-9|\u4e00-\u9fa5]{0,20}$/.test(val));
		if (/^[a-z|A-Z|0-9|\u4e00-\u9fa5]{0,20}$/.test(val)) {
			this.goodsBasicComVo.approvalNumber = val
		}
	  } */
    },
    handleChangeUseStandardImage(val) {
      if (val) {
        const h = this.$createElement
        this.$msgbox({
          title: '提示',
          message: h('p', null, [
            h(
              'span',
              null,
              '使用标品图片后，将删除原有图片（包括主图与详情图），确定使用标品图片吗？'
            )
          ]),
          confirmButtonText: '确定'
        })
          .then(() => {
            this.goodsBasicComVo.useStandardImage = true
          })
          .catch(() => {
            this.goodsBasicComVo.useStandardImage = false
          })
      } else {
        this.goodsBasicComVo.useStandardImage = false
      }
    },
    async initData() {
      console.log('this.propsData:', this.propsData)
      try {
        const res = await KSChecker()
        this.isControlSales = res.data // true表示店铺为特殊控销false则为普通店铺
        console.log(res, '是否特殊控销')
      } catch (error) {
        console.log('控销检查失败:', error)
      }
      
      this.init = this.propsData.isInit
      this.goodsBasicComVo.tracingCode =  this.propsData.tracingCode
      this.goodsBasicComVo.init = this.propsData.isInit
      this.goodsBasicComVo.barcode = this.propsData.barcode
      this.goodsBasicComVo.productName = this.propsData.productName
      this.goodsBasicComVo.commonName = this.propsData.commonName
      // this.goodsBasicComVo.zjm = this.propsData.zjm
      this.goodsBasicComVo.brand = this.propsData.brand
      this.goodsBasicComVo.showName = this.propsData.showName
      this.goodsBasicComVo.code = this.propsData.code
      this.goodsBasicComVo.approvalNumber = this.propsData.approvalNumber
      this.goodsBasicComVo.instrumentNumber =
        this.propsData.approvalNumber || ''
      this.goodsBasicComVo.manufacturer = this.propsData.manufacturer
      this.goodsBasicComVo.productionAddress = this.propsData.productionAddress
      this.goodsBasicComVo.erpCode = this.propsData.erpCode
      this.goodsBasicComVo.spec = this.propsData.spec
      this.goodsBasicComVo.technicalRequirementNo =
        this.propsData.technicalRequirementNo // 产品技术要求编号
      this.goodsBasicComVo.technicalRequirementNoCanEmpty =
        this.propsData.technicalRequirementNoCanEmpty // 产品技术要求编号是否可以为空,true可以为空
      this.goodsBasicComVo.producer = this.propsData.producer
      this.goodsBasicComVo.drugClassification =
        this.propsData.drugClassification
      this.goodsBasicComVo.mediumPackageNum = this.isControlSales?this.propsData.mediumPackageNum:1 // 控销店铺下的中包装数量可编辑 非控销下不可编辑且数量为1
      this.goodsBasicComVo.pieceLoading = this.propsData.pieceLoading
      this.goodsBasicComVo.dosageForm = this.propsData.dosageForm
      this.goodsBasicComVo.standardProductId =
        this.propsData.standardProductId || ''
      this.goodsBasicComVo.productUnit = this.propsData.productUnit
      this.goodsBasicComVo.term = this.propsData.term
      this.goodsBasicComVo.storageCondition = this.propsData.storageCondition
      this.goodsBasicComVo.isSplit = this.propsData.isSplit
      // this.goodsBasicComVo.useStandardImage = this.propsData.pipei ? false : this.propsData.useStandardImage
      this.goodsBasicComVo.useStandardImage = this.propsData.useStandardImage
      this.goodsBasicComVo.imageUrlStandard =
        this.propsData.imageUrlStandard || ''
      this.goodsBasicComVo.instrutionImageUrlStandard =
        this.propsData.instrutionImageUrlStandard || ''
      this.goodsBasicComVo.effectCanEmpty = this.propsData.effectCanEmpty
      this.goodsBasicComVo.producerCanEmpty = this.propsData.producerCanEmpty
      this.goodsBasicComVo.isInstrument = this.propsData.isInstrument
      this.goodsBasicComVo.instrumentLicenseEffect =
        this.propsData.instrumentLicenseEffect
      this.goodsBasicComVo.manufacturingLicenseNo =
        this.propsData.manufacturingLicenseNo
      this.goodsBasicComVo.manufacturingLicenseEffect =
        this.propsData.manufacturingLicenseEffect
      this.goodsBasicComVo.instrumentImageValid =
        this.propsData.instrumentImageValid
      this.goodsBasicComVo.manufacturingImageValid =
        this.propsData.manufacturingImageValid
      this.goodsBasicComVo.openFactor = this.propsData.openFactor
      this.goodsBasicComVo.factor = this.propsData.factor
      this.goodsBasicComVo.marketAuthor = this.propsData.marketAuthor
      this.goodsBasicComVo.marketAuthorAddress = this.propsData.marketAuthorAddress
      this.goodsBasicComVo.aliasName = this.propsData.aliasName
	  this.goodsBasicComVo.filingsAuthor = this.propsData.filingsAuthor
      this.erpFirstCategoryId = this.propsData.erpFirstCategoryId

       this.goodsBasicComVo.isCommissionProduction = this.propsData.isCommissionProduction
	  this.goodsBasicComVo.entrustedManufacturer = this.propsData.entrustedManufacturer
      	  this.goodsBasicComVo.entrustedManufacturerAddress = this.propsData.entrustedManufacturerAddress
      this.goodsBasicComVoRules.approvalNumber[0].message = `${['100007', '100001'].includes(this.erpFirstCategoryId) ? '化妆品备案编号/注册证号' :'批准文号'}不能为空`;
      this.goodsBasicComVoRules.filingsAuthor[0].message =  this.erpFirstCategoryId == '100005' ? '医疗器械注册人/备案人名称不能为空' : '化妆品备案人/注册人不能为空';
      this.goodsBasicComVo.netContentUnit = this.propsData.netContentUnit
      this.goodsBasicComVo.netContent = this.propsData.netContent
      // if(!['100002','100005','100009'].includes(this.erpFirstCategoryId || this.basicData.erpFirstCategoryId)){
      //     this.goodsBasicComVo.isSplit = ''
      //     this.goodsBasicComVo.openFactor = ''
      // }
      this.goodsBasicComVo.producerCopy = ''
      this.goodsBasicComVo.regional = ""
      this.initShowNetUnit()
      this.matchArea(this.propsData.producer)
      var urlVal = []
      const urlAryImagesList = this.propsData.imagesList
      if (urlAryImagesList) {
        urlAryImagesList.forEach((itAry, indAry) => {
          if (itAry) {
            urlVal.push({
              name: itAry,
              url: `${this.bigImgUrlPrefix}${itAry}`,
              uid: '_' + indAry
            })
          }
        })
        this.goodsBasicComVo.imagesList = {
          urlVal,
          maxImg: 5,
          isUpload:
            this.propsData.imagesList && this.propsData.imagesList.length === 5
        }
      } else {
        this.goodsBasicComVo.imagesList = {
          urlVal: urlVal,
          maxImg: 5,
          isUpload: false
        }
      }
      urlVal = []
      const urlAryInstrutionImagesList = this.propsData.instrutionImagesList
      if (urlAryInstrutionImagesList) {
        urlAryInstrutionImagesList.forEach((itAry, indAry) => {
          if (itAry) {
            urlVal.push({
              name: itAry,
              url: `${this.bigDescImgUrlPrefix}${itAry}`,
              uid: '_' + indAry
            })
          }
        })
        this.goodsBasicComVo.instrutionImagesList = {
          urlVal,
          maxImg: 5,
          isUpload:
            this.propsData.instrutionImagesList &&
            this.propsData.instrutionImagesList.length === 5
        }
      } else {
        this.goodsBasicComVo.instrutionImagesList = {
          urlVal: [],
          maxImg: 5,
          isUpload: false
        }
      }
      const arr = []
      const urlAryInstrumentImagesList =
        this.propsData.instrumentLicenseImageList
      if (urlAryInstrumentImagesList && urlAryInstrumentImagesList.length) {
        urlAryInstrumentImagesList.forEach((itAry, indAry) => {
          if (itAry) {
            arr.push({
              name: itAry,
              url: `${this.bigDescImgUrlPrefix}${itAry}`,
              uid: `_${indAry}`
            })
          }
        })
        this.goodsBasicComVo.instrumentLicenseImageList = {
          urlVal: arr,
          maxImg: 5,
          isUpload:
            this.propsData.instrumentLicenseImageList &&
            this.propsData.instrumentLicenseImageList.length === 5
        }
      } else {
        this.goodsBasicComVo.instrumentLicenseImageList = {
          urlVal: [],
          maxImg: 5,
          isUpload: false
        }
      }

      const arr1 = []
      const urlAryManufacturingLicenseImageList =
        this.propsData.manufacturingLicenseImageList
      if (urlAryManufacturingLicenseImageList) {
        urlAryManufacturingLicenseImageList.forEach((itAry, indAry) => {
          if (itAry) {
            arr1.push({
              name: itAry,
              url: `${this.bigDescImgUrlPrefix}${itAry}`,
              uid: `_${indAry}`
            })
          }
        })
        this.goodsBasicComVo.manufacturingLicenseImageList = {
          urlVal: arr1,
          maxImg: 5,
          isUpload:
            this.propsData.manufacturingLicenseImageList &&
            this.propsData.manufacturingLicenseImageList.length === 5
        }
      } else {
        this.goodsBasicComVo.manufacturingLicenseImageList = {
          urlVal: [],
          maxImg: 5,
          isUpload: false
        }
      }

      // 是否包含 “许”或“进”或“国械备”字
      if (
        new RegExp('国械备|进|许').test(this.goodsBasicComVo.instrumentNumber)
      ) {
        this.manufacturingLicenseNoALLDisable = true
      } else {
        this.manufacturingLicenseNoALLDisable = false
      }
    },
    async uploadImg(file, idStr) {
      const that = this
      let resetFile = ''
      const isSpecalImg =
        idStr === 'imagesList' || idStr === 'instrutionImagesList'
      if (isSpecalImg) {
        resetFile = await this.resetImg(file.file)
      }
      const params = {
        file: isSpecalImg ? resetFile : file.file,
        type: idStr === 'imagesList' ? 1 : 2
      }
      try {
        const res = await apiUploadProductImage(params)
        if (res.code === 0) {
          if (res.data) {
            file.onSuccess()
            if (idStr === 'imagesList') {
              that.goodsBasicComVo.imagesList.urlVal.push({
                name: res.data,
                url: `${this.bigImgUrlPrefix}${res.data}`,
                uid: file.file.uid
              })

              that.goodsBasicComVo.imagesList.urlVal.length ===
              that.goodsBasicComVo.imagesList.maxImg
                ? (that.goodsBasicComVo.imagesList.isUpload = true)
                : (that.goodsBasicComVo.imagesList.isUpload = false)
            } else {
              that.goodsBasicComVo[idStr].urlVal.push({
                name: res.data,
                url: `${this.bigDescImgUrlPrefix}${res.data}`,
                uid: file.file.uid
              })
              that.goodsBasicComVo[idStr].urlVal.length ===
              that.goodsBasicComVo[idStr].maxImg
                ? (that.goodsBasicComVo[idStr].isUpload = true)
                : (that.goodsBasicComVo[idStr].isUpload = false)
            }
          }
        } else {
          file.onError()
          this.$message.error(res.message)
        }
      } catch (err) {
        file.onError()
      }
    },
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.url
      this.dialogVisible = true
    },
    handleRemove(file, fileList, idStr) {
      fileList.forEach((item, index) => {
        if (item.name === file.name) {
          fileList.splice(index, 1)
        }
      })
      // if (fileList.length < 1) {
      //   this.$set(this.mergeData, `${idStr}imgList`, '');
      // }
      if (idStr) {
        this.goodsBasicComVo[idStr].isUpload = false
      }
    },
    onloadImg(reader, file) {
      const image = new Image()
      image.src = reader.result
      return new Promise((resolve) => {
        image.onload = () => {
          const maxWidth = 1500 // 最大宽度
          const minWidth = 800 // 最小宽度
          const maxSize = 2 * 1024 * 1024 // 最大大小
          const { width } = image
          const { height } = image
          let compressedWidth = width
          let compressedHeight = height
          if (width > maxWidth) {
            compressedWidth = maxWidth
            compressedHeight = height * (maxWidth / width)
          } else if (width < minWidth) {
            compressedWidth = minWidth
            compressedHeight = height * (minWidth / width)
          }
          // 创建canvas对象
          const canvas = document.createElement('canvas')
          const ctx = canvas.getContext('2d')
          canvas.width = compressedWidth
          canvas.height = compressedHeight
          // 绘制图片到canvas
          ctx.drawImage(image, 0, 0, compressedWidth, compressedHeight)
          // 压缩图片大小
          canvas.toBlob((blob) => {
            const { size } = blob
            const ratio = maxSize / size
            let compressedBlob = blob
            if (ratio < 1) {
              compressedBlob = blob.slice(0, size * ratio)
            }
            const compressedFile = new File([compressedBlob], file.name, {
              type: file.type
            })
            resolve(compressedFile)
          }, file.type)
        }
      })
    },
    resetImg(file) {
      const reader = new FileReader()
      reader.readAsDataURL(file)
      return new Promise((resolve) => {
        reader.onload = async () => {
          const res = await this.onloadImg(reader, file)
          resolve(res)
        }
      })
    },
    async beforeAvatarUploadForProductImg(file) {
      const isJPG = file.type === 'image/jpeg' || file.type === 'image/png'
      if (!isJPG) {
        this.$message.error('上传图片只能是 JPG、PNG 格式!')
        return false
      }

      // if (isJPG && isLt2M) {
            // return true
      // }

      return true
      const that = this

      return new Promise((resolve, reject) => {
        let img = new Image()
        img.onload = function () {
          if (img.height !== img.width) {
            that.$message.error('上传图片请保持1:1比例')
            reject(false)
            return
          }

          if (img.height < 800 || img.height > 1500) {
            that.$message.error('图片宽高请保持在800-1500之间')
            reject(false)
            return
          }

          if (isJPG) {
            resolve(true)
          } else {
            reject(false)
          }
        }
        img.src = URL.createObjectURL(file)
      })
    },
    beforeAvatarUpload(file) {
      // console.log(file);
      const isJPG = file.type === 'image/jpeg' || file.type === 'image/png'
      const isLt2M = file.size / 1024 / 1024 < 2

      if (!isJPG) {
        this.$message.error('上传图片只能是 JPG、PNG 格式!')
        return false
      }
      if (!isLt2M) {
        this.$message.error('上传图片大小不能超过 2MB!')
        return false
      }
      return isJPG && isLt2M
    },
    // 商品名称
    getProductNameChange(value) {
      if (!this.goodsBasicComVo.brand) {
        this.goodsBasicComVo.showName = this.goodsBasicComVo.productName
      } else {
        this.goodsBasicComVo.showName =
          this.goodsBasicComVo.brand + ' ' + this.goodsBasicComVo.productName
      }
      // this.getCamelChars()
    },
    // 品牌
    getBrandChange(value) {
      if (!this.goodsBasicComVo.productName) {
        this.goodsBasicComVo.showName = this.goodsBasicComVo.brand
      } else {
        this.goodsBasicComVo.showName =
          this.goodsBasicComVo.brand + ' ' + this.goodsBasicComVo.productName
      }
    },
    // 获取助记码首字母
    getCamelChars() {
      const params = { name: this.goodsBasicComVo.productName }
      apiCamelChars(params).then((res) => {
        if (res.code == 0) {
          this.goodsBasicComVo.zjm = res.data || ''
        } else {
          this.$message.error(res.message)
        }
      })
    },
	valueInput(formKey, key, value, regExp) {
		if (new RegExp(regExp).test(value)) {
			this[formKey][key] = value
		}
	}
  }
}
</script>

<style scoped lang="scss">
.el-button {
  padding: 8px 20px;
}
.el-button.is-circle {
  padding: 7px;
  border: none;
}
.boxDiv {
  width: 100%;
  height: 100%;
  padding: 0 0 0 10px;
  // overflow-y: auto;
  .select-info {
    width: 100%;
  }

  .span-tip {
    display: inline-block;
    width: 12px;
    height: 12px;
    font-size: 10px;
    border: 1px solid #999999;
    color: #999999;
    text-align: center;
    line-height: 12px;
    border-radius: 50%;
    margin-right: 2px;
    margin-left: -2px;
  }

  ::v-deep   .el-form {
    width: 100%;
    .el-select {
      margin-right: 14px;
    }

    .el-form-item__label {
      font-size: 12px;
      line-height: 30px;
    }

    .el-form-item__content {
      line-height: 30px;
    }

    .el-input__inner {
      line-height: 30px;
      height: 30px;
      font-size: 12px;
    }

    .el-input__icon {
      line-height: 30px;
    }
  }

  ::v-deep   .el-table__body .el-form-item {
    padding: 20px 0;
  }

  .addrForm .el-form-item {
    display: inline-block;
  }

  ::v-deep  .avatar-uploader .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 2px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    background: rgba(0, 0, 0, 0.02);
  }
  ::v-deep  .avatar-uploader .el-upload:hover {
    border-color: #409eff;
  }
  ::v-deep  .avatar-uploader .el-upload__tip {
    margin-top: 0;
    color: #999999;
    font-size: 12px;
  }
  .avatar-uploader-icon {
    opacity: 1;
    font-size: 12px;
    font-family: PingFangSC, PingFangSC-Regular;
    font-weight: 400;
    text-align: left;
    color: rgba(0, 0, 0, 0.65);
    line-height: 14px;
  }
  .avatar {
    width: 64px;
    height: 64px;
    display: block;
  }
  ::v-deep  
    .avatar-uploader
    .el-upload-list--picture-card
    .el-upload-list__item {
  }

  ::v-deep   .hide .el-upload--picture-card {
    display: none;
  }
}

.boxDiv::-webkit-scrollbar {
  width: 0 !important;
}

.effectPictureHover {
  margin-left: 10px;
  color: #3EBBF2;
  position: relative;

  img {
    z-index: 999;
    position: absolute;
    display: none;
    width: 250px;
    left: 43px;
    top: -290px;
  }
}

.effectPictureHover:hover {
  img {
    display: block;
  }
}
.input-with-select {
  display: flex;
  align-items: center;
  
  .el-input {
    margin-right: 0;
    ::v-deep .el-input__inner {
      border-top-right-radius: 0;
      border-bottom-right-radius: 0;
    }
  }

  .el-form-item {
    margin-bottom: 0px !important;
    ::v-deep .el-input__inner {
      border-bottom-left-radius: 0;
      border-top-left-radius: 0;
    }
  }
}
.producterSelect {
  display: flex;
  align-items: center;
  .el-input {
    margin-right: 0;
    ::v-deep .el-input__inner {
      border-top-left-radius: 0;
      border-bottom-left-radius: 0;
    }
  }
  .el-select {
    margin-right: 0 !important;
  }
  .regional {
    ::v-deep .el-input__inner {
      border-radius: 0;
    }
    
  }
  .international {
    ::v-deep .el-input__inner {
      border-top-right-radius: 0;
      border-bottom-right-radius: 0;
    }
  }
}
</style>
