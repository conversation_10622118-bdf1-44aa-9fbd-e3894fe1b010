<template>
  <div>
    <el-dialog
      title="更改物流信息"
      :visible="stateVisible"
      width="60%"
      @close="closeDialog"
    >
      <div>
        <el-form
          ref="logInfo"
          :model="logInfo"
          size="small"
          label-width="100px"
          class="demo-dynamic"
        >
          <el-form-item
            label="选择物流方式："
          >
            <el-select
              v-model="logInfo.logisticsWayDesc"
              placeholder="请选择物流方式"
            >
              <el-option
                v-for="(item,index) in logisticsList"
                :key="index"
                :label="item.name"
                :value="item.name"
              />
            </el-select>
          </el-form-item>
          <el-form-item
            label="物流单号："
          >
            <div
              v-for="(domain, index) in logInfo.trackingNoList"
              :key="index"
              class="inputDiv"
            >
              <el-input v-model.trim="logInfo.trackingNoList[index]" /><span
                v-if="index === 0"
                @click="addDomain"
              >+</span> <span
                v-else
                @click.prevent="removeDomain(domain)"
              >-</span>
            </div>
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer">
        <el-button
          size="mini"
          @click="closeDialog"
        >取 消</el-button>
        <el-button
          type="primary"
          size="mini"
          @click="confirmD"
        >确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { getQueryTrackInfo, queryLogisticsWayEnum, updateTrackInfo } from '@/api/order/index';

export default {
  name: 'ChangeLog',
  data() {
    return {
      logisticsList: [],
      stateVisible: false,
      logInfo: { logisticsWay: '', logisticsWayDesc: '', trackingNoList: [], orderNo: '' },
      tipHeight: document.documentElement.clientHeight / 3,
    };
  },
  methods: {
    getListData(orderNo) {
      this.stateVisible = true;
      this.logInfo.orderNo = orderNo;
      queryLogisticsWayEnum().then((res) => {
        if (res.code === 0) {
          this.logisticsList = res.result;
        }
      });
      getQueryTrackInfo({ orderNo }).then((res) => {
        if (res.code === 0) {
          if(res.result) {
            this.logInfo.logisticsWayDesc = res.result ? res.result.logisticsWayDesc : '';
            this.logInfo.logisticsWay = res.result ? res.result.logisticsWay : '';
            if (res.result.trackingNoList && res.result.trackingNoList.length > 0) {
              this.logInfo.trackingNoList = res.result.trackingNoList;
            } else {
              this.logInfo.trackingNoList.push('');
            }
          }else {
            this.logInfo.trackingNoList = [];
            this.logInfo.trackingNoList.push('');
          }
        } else {
          this.$message.error({ message: res.message, offset: 100 });
        }
      });
    },
    closeDialog() {
      this.stateVisible = false;
    },
    confirmD() {
      const that = this;
      this.logisticsList.map(function (item) {
        if(item.name == that.logInfo.logisticsWayDesc) {
          that.logInfo.logisticsWay = item.code;
        }
      });
      if (this.logInfo.trackingNoList.length === 1) {
        if (this.logInfo.trackingNoList[0] === ' ' || !this.logInfo.trackingNoList[0].trim()) {
          this.$message.warning({ message: '请先添加物流单号', offset: this.tipHeight });
          return;
        }
      }
      this.logInfo.trackingNoList.forEach((item, index) => {
        if (!item) {
          this.logInfo.trackingNoList.splice(index, 1);
        }
      });
      updateTrackInfo(this.logInfo).then((res) => {
        if (res.code === 0) {
          this.$message.success({ message: '编辑成功', offset: this.tipHeight });
          this.stateVisible = false;
          this.$emit('gitList');
        } else {
          this.$message.error({ message: res.message, offset: this.tipHeight });
        }
      });
    },
    removeDomain(data) {
      const index = this.logInfo.trackingNoList.indexOf(data);
      if (index !== -1) {
        this.logInfo.trackingNoList.splice(index, 1);
      }
    },
    addDomain() {
      this.logInfo.trackingNoList.push('');
    },
  },
};
</script>

<style scoped lang="scss">
.inputDiv{
  width: 243px;
  display: flex;
  align-items: center;
  padding-bottom: 20px;
  span{
    cursor: pointer;
    margin-left: 10px;
    display: flex;
    width: 21px;
    height: 18px;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    border-radius: 50%;
    border: 1px solid #5d96db;
    color: #5d96db;
  }
}
.flexBox{
  display: flex;
  align-items: center;
}
::v-deep  .el-dialog__body{
  padding: 10px 20px;
}
::v-deep  .el-dialog__header{
  padding: 10px 20px;
  background: #f9f9f9;
}
::v-deep  .el-dialog__headerbtn{
  top: 15px;
}
::v-deep  .el-dialog__title{
  font-size: 16px;
}
</style>
