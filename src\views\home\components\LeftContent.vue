<template>
  <div>

    <Notice2 v-if="isGrayUser && isReady"/>
    <Notice1 v-if="!isGrayUser"/>
    <TodoTask v-if="!isGrayUser && isReady"/>
    <ShopData v-if="isGrayUser"/>
    <OperationStatus_30day2 v-if="isGrayUser"/>
    <OperationStatus_30day1 v-else/>

  </div>
</template>

<script>
import Notice2 from './Notice2'
import Notice1 from './Notice1'
import TodoTask from './TodoTask'
import OperationStatus_30day2 from './OperationStatus_30day2'
import OperationStatus_30day1 from './OperationStatus_30day1'

import ShopData from './shopData'
import drag from './drag'
  import {mapState} from 'vuex'
export default {
  name: "LeftContent",
  components: {Notice2,Notice1, TodoTask, OperationStatus_30day2,OperationStatus_30day1,ShopData,drag},
  computed:{
    ...mapState({ isGrayUser: state => state.app.isGrayUser }),
  },
  data() {
    return {
      isReady: false // 初始值
    };
  },
  mounted() {
    // 1 秒后修改为 true
    setTimeout(() => {
      this.isReady = true;
    }, 500);
  },
}
</script>

<style scoped>

</style>
