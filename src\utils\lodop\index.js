import Vue from "vue";
let newVue = new Vue();
//==本JS是加载Lodop插件及CLodop服务的综合示例，可直接使用，建议看懂后融进自己页面程序==

var CreatedOKLodopObject, CLodopIsLocal, CLodopJsState;

//==判断是否需要CLodop(那些不支持插件的浏览器):==
function needCLodop() {
  try {
    var ua = navigator.userAgent;
    if (ua.match(/Windows\sPhone/i)) return true;
    if (ua.match(/iPhone|iPod|iPad/i)) return true;
    if (ua.match(/Android/i)) return true;
    if (ua.match(/Edge\D?\d+/i)) return true;

    var verTrident = ua.match(/Trident\D?\d+/i);
    var verIE = ua.match(/MSIE\D?\d+/i);
    var verOPR = ua.match(/OPR\D?\d+/i);
    var verFF = ua.match(/Firefox\D?\d+/i);
    var x64 = ua.match(/x64/i);
    if (!verTrident && !verIE && x64) return true;
    else if (verFF) {
      verFF = verFF[0].match(/\d+/);
      if (verFF[0] >= 41 || x64) return true;
    } else if (verOPR) {
      verOPR = verOPR[0].match(/\d+/);
      if (verOPR[0] >= 32) return true;
    } else if (!verTrident && !verIE) {
      var verChrome = ua.match(/Chrome\D?\d+/i);
      if (verChrome) {
        verChrome = verChrome[0].match(/\d+/);
        if (verChrome[0] >= 41) return true;
      }
    }
    return false;
  } catch (err) {
    return true;
  }
}

//==加载引用CLodop的主JS,用双端口8000和18000(以防其中一个被占):==
function loadCLodop(callBack) {
  if (CLodopJsState == "loading" || CLodopJsState == "complete") return;
  CLodopJsState = "loading";
  var head =
    document.head ||
    document.getElementsByTagName("head")[0] ||
    document.documentElement;
  var JS1 = document.createElement("script");
  var JS2 = document.createElement("script");
  JS1.src = "http://localhost:8000/CLodopfuncs.js?priority=1";
  // JS2.src = "http://localhost:18000/CLodopfuncs.js";

  // JS1.onload = JS2.onload = function() {
  //   CLodopJsState = "complete";
  //   if(callBack) callBack('success');
  // };
  // JS1.onerror = JS2.onerror = function() {
  //   CLodopJsState = "complete";
  //   if(callBack) callBack('error');
  // };

  // 串行加载 两个 CLodop的主JS
  getLoadCLodopJS1(JS1, function(MSG1) {
    if (MSG1 == 'success') {
      if(callBack) callBack('success');
    } else {
      JS2.src = "http://localhost:18000/CLodopfuncs.js";
      getLoadCLodopJS2(JS2, function(MSG2) {
        if (MSG2 == 'success') {
          if(callBack) callBack('success');
        }else {
          if(callBack) callBack('error');
        }
      })
    }
  });

  head.insertBefore(JS1, head.firstChild);
  head.insertBefore(JS2, head.firstChild);
  CLodopIsLocal = !!(JS1.src + JS2.src).match(/\/\/localho|\/\/127.0.0./i);
}

function getLoadCLodopJS1(JS1, callBack) {
  JS1.onload = function() {
    CLodopJsState = "complete";
    if(callBack) callBack('success');
  };
  JS1.onerror = function() {
    CLodopJsState = "complete";
    if(callBack) callBack('error');
  };
}

function getLoadCLodopJS2(JS2, callBack) {
  JS2.onload = function() {
    CLodopJsState = "complete";
    if(callBack) callBack('success');
  };
  JS2.onerror = function() {
    CLodopJsState = "complete";
    if(callBack) callBack('error');
  };
}

// if (needCLodop()) {
//   loadCLodop();
// } //加载

// 外部判断是否加载lodop js成功
export function outNeedLodop(callBack) {
  if (needCLodop()) {
    loadCLodop(callBack);
  }
}

// 外部判断 是否安装lodop服务
export function outJudgementInstallLodop(callBack) {
  if (needCLodop()) {
    loadCLodop(function(backMsg) {
      if (backMsg == "success") {
        console.log("外部加载lodopJS成功");
        getLodop();
      } else {
        console.log("外部加载lodopJS失败");
        var basePath = "https://downloads.ybm100.com/ykq/exe/";
        var setPdf = "https://oss-ec.ybm100.com/ybm/popDeliverLog/%E8%8D%AF%E5%B8%AE%E5%BF%99%E5%BF%AB%E9%80%92%E9%9D%A2%E5%8D%95%E6%89%93%E5%8D%B0%E9%A9%B1%E5%8A%A8%E5%8F%8A%E6%8F%92%E4%BB%B6%E5%AE%89%E8%A3%85%E6%B5%81%E7%A8%8B.docx";
        var msgStr1 = `<br><font color='#FF00FF'>Web打印服务CLodop未安装启动，点击这里<a href='${basePath}CLodop_Setup_for_Win32NT.exe' target='_self' style="color: #0000EE;">下载执行安装</a>，安装文档点击下载：<a style="color: #0000EE;" href='${setPdf}' target='_blank'>药帮忙快递面单打印驱动及插件安装流程.docx</a></font>`;
        var msgStr2 = `<br><font color='#FF00FF'>（若此前已安装过，可<a href='CLodop.protocol:setup' target='_self' style="color: #0000EE;">点这里直接再次启动</a>），成功后请刷新本页面。</font>`;
        newVue.$msgbox.confirm(msgStr1 + msgStr2, "提示", {
          cancelButtonText: "取消",
          type: "warning",
          showConfirmButton: false,
          dangerouslyUseHTMLString: true,
        });
      }
    });
  }
}


//==获取LODOP对象主过程,判断是否安装、需否升级:==
export function getLodop(oOBJECT, oEMBED) {
  // var basePath = 'http://39.106.53.245/attach/';
  var basePath = "https://downloads.ybm100.com/ykq/exe/";
  var strHtmInstall = `<br><font color='#FF00FF'>打印控件未安装!点击这里<a href='${basePath}install_lodop32.exe' target='_self'>执行安装</a>,安装后请刷新页面或重新进入。</font>`;
  var strHtmUpdate = `<br><font color='#FF00FF'>打印控件需要升级!点击这里<a href='${basePath}install_lodop32.exe' target='_self'>执行升级</a>,升级后请重新进入。</font>`;
  var strHtm64_Install = `<br><font color='#FF00FF'>打印控件未安装!点击这里<a href='${basePath}install_lodop64.exe' target='_self'>执行安装</a>,安装后请刷新页面或重新进入。</font>`;
  var strHtm64_Update = `<br><font color='#FF00FF'>打印控件需要升级!点击这里<a href='${basePath}install_lodop64.exe' target='_self'>执行升级</a>,升级后请重新进入。</font>`;
  var strHtmFireFox = `<br><br><font color='#FF00FF'>（注意：如曾安装过Lodop旧版附件npActiveXPLugin,请在【工具】->【附加组件】->【扩展】中先卸它）</font>`;
  var strHtmChrome = `<br><br><font color='#FF00FF'>(如果此前正常，仅因浏览器升级或重安装而出问题，需重新执行以上安装）</font>`;
  var strCLodopInstall_1 = `<br><font color='#FF00FF'>Web打印服务CLodop未安装启动，点击这里<a href='${basePath}CLodop_Setup_for_Win32NT.exe' target='_self' style="color: #0000EE;">下载执行安装</a>`;
  var strCLodopInstall_2 = `<br>（若此前已安装过，可<a href='CLodop.protocol:setup' target='_self'>点这里直接再次启动</a>）`;
  var strCLodopInstall_3 = `，成功后请刷新本页面。</font>`;
  var strCLodopUpdate = `<br><font color='#FF00FF'>Web打印服务CLodop需升级!点击这里<a href='${basePath}CLodop_Setup_for_Win32NT.exe' target='_self'>执行升级</a>,升级后请刷新页面。</font>`;
  var LODOP;
  try {
    var ua = navigator.userAgent;
    var isIE = !!ua.match(/MSIE/i) || !!ua.match(/Trident/i);
    var tml;

    if (needCLodop()) {
      try {
        LODOP = getCLodop();
      } catch (err) {
        console.log(err);
      }
      console.log(LODOP)
      console.log(CLodopJsState)
      if (!LODOP && CLodopJsState !== "complete") {
        if (CLodopJsState == "loading") {
          // alert("网页还没下载完毕，请稍等一下再操作.");
          tml = "打印服务异常，请确定是否安装打印服务。若已安装，请启动打印服务！";
          insertTmlToDetailsBox(tml);
        }
        else alert("没有加载CLodop的主js，请先调用loadCLodop过程.");
        return;
      }
      if (!LODOP) {
        tml =
          strCLodopInstall_1 +
          (CLodopIsLocal ? strCLodopInstall_2 : "") +
          strCLodopInstall_3;

        insertTmlToDetailsBox(tml);
        return;
      } else {
        if (CLODOP.CVERSION < "3.0.9.3") {
          tml = strCLodopUpdate;
          insertTmlToDetailsBox(tml);
        }
        if (oEMBED && oEMBED.parentNode) oEMBED.parentNode.removeChild(oEMBED);
        if (oOBJECT && oOBJECT.parentNode)
          oOBJECT.parentNode.removeChild(oOBJECT);
      }
    } else {
      var is64IE = isIE && !!ua.match(/x64/i);
      //==如果页面有Lodop就直接使用,否则新建:==
      if (oOBJECT || oEMBED) {
        if (isIE) LODOP = oOBJECT;
        else LODOP = oEMBED;
      } else if (!CreatedOKLodopObject) {
        LODOP = document.createElement("object");
        LODOP.setAttribute("width", 0);
        LODOP.setAttribute("height", 0);
        LODOP.setAttribute(
          "style",
          "position:absolute;left:0px;top:-100px;width:0px;height:0px;"
        );
        if (isIE)
          LODOP.setAttribute(
            "classid",
            "clsid:2105C259-1E0C-4534-8141-A753534CB4CA"
          );
        else LODOP.setAttribute("type", "application/x-print-lodop");
        document.documentElement.appendChild(LODOP);
        CreatedOKLodopObject = LODOP;
      } else LODOP = CreatedOKLodopObject;
      //==Lodop插件未安装时提示下载地址:==
      if (!LODOP || !LODOP.VERSION) {
        if (ua.indexOf("Chrome") >= 0) tml = strHtmChrome;
        insertTmlToDetailsBox(tml);
        if (ua.indexOf("Firefox") >= 0) tml = strHtmFireFox;
        insertTmlToDetailsBox(tml);

        tml = is64IE ? strHtm64_Install : strHtmInstall;
        insertTmlToDetailsBox(tml);
        return LODOP;
      }
    }
    if (LODOP.VERSION < "6.2.2.6") {
      if (!needCLodop()) tml = is64IE ? strHtm64_Update : strHtmUpdate;
      insertTmlToDetailsBox(tml);
    }
    //===如下空白位置适合调用统一功能(如注册语句、语言选择等):==
    LODOP.SET_LICENSES(
      "",
      "449D1C6B17D143182C5F7767E187AA2F3CF",
      "23580B189D96F33252722697A57AC00506B",
      ""
    );
    //=======================================================
    return LODOP;
  } catch (err) {
    alert("getLodop出错:" + err);
  }
}

function insertTmlToDetailsBox(tml) {
  newVue.$msgbox.confirm(tml, "提示", {
    cancelButtonText: "取消",
    type: "warning",
    showConfirmButton: false,
    dangerouslyUseHTMLString: true
  });
}
