/*仅样式调试用，调试完毕须cope到对应样式js里*/
/* @page {
    size: 100mm 113mm;
    margin: 0;
    padding: 0;
} */

.page-row img {
    padding: 0;
    margin: 0;
    width: 100%;
}

.content {
    width: 94mm;
    height: auto;
    margin: 0mm auto;
    border: 1px solid #000000;
    font-family: 黑体;
    box-sizing: border-box;
    font-size: 12px;
    font-weight: bold;
}

.page-row {
    border-bottom: 1px solid #000000;
    text-align: left;
    background-color: #ffffff;
    overflow: hidden;
    position: relative;
    display: flex;
    justify-content: left;
    align-items: flex-end;
}

.content .page-row:last-child{
    border-bottom: none;
}

.tag_one {
    width: 18mm;
    height: 5mm;
    line-height: 5mm;
    border: 1px solid #000;
    font-size: 12px;
    text-align: center;
    margin-bottom: 5mm;
    margin-right: 1mm;
}


.cell_1,.cell_3{
    width: 15%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    border-right: 1px solid #000;
    overflow: hidden;
}
.cell_2,.cell_4{
    width: 35%;
    height: 100%;
    border-right: 1px solid #000;
    overflow: hidden;
}

.cell_2 div,.cell_4 div{
    padding-left: 5px;
    display: flex;
    align-items: center;
    justify-content: left;
}

.cell_4{
    border-right: none;
}

.cell_5{
    width: 50%;
    height: 100%;
    border-right: 1px solid #000;
    display: flex;
    align-items: center;
    justify-content: center;
}

.page-row .cell_5:last-child{
    border-right: none;
}

.cell_12{
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.cell_25{
    width: 25%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    border-right: 1px solid #000;
}

.page-row .cell_25:last-child{
    border-right: none;
}




