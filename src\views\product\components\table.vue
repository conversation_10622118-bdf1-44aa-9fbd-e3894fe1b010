<template>
  <div class="table-containter">
    <el-table
      id="tabled"
      ref="table"
      :data="data"
      :show-summary="showSummary"
      :summary-method="getSummaries"
      :stripe="isStripe"
      :span-method="spanCallback"
      row-class-name="table-row"
      :row-key="rowKey"
      :header-cell-style="headerCellStyle"
      header-row-class-name="table-header-row"
      style="width: 100%"
      border
      :cell-style="cellStyle"
      @selection-change="handleSelectionChange"
      @cell-mouse-enter="handleCellMouseEnter"
      @cell-click="handleCellClick"
      @sort-change="soryByPrice"
    >
      <template slot="empty">
        <div class="empty-data">
          <img :src="emptyData">
          <div>{{ emptyText }}</div>
        </div>
      </template>
      <el-table-column
        v-if="hasSelection"
        :selectable="selectable"
        type="selection"
        :reserve-selection="reserveSelection"
        width="50"
      />
      <el-table-column
        v-if="hasIndex"
        :index="indexFormat"
        type="index"
        width="80"
        :label="indexLabel"
      />
      <template v-for="item in col">
        <slot
          v-if="item.slot"
          :name="item.index"
          :col="item"
        />
        <!-- 鼠标移入，对话框展示全部 -->
        <el-table-column
          v-else-if="item.ellipsis"
          :key="item.index"
          :label="item.name"
          :width="item.width"
          :min-width="item.minWidth || 100"
          :fixed="item.fixed"
          show-overflow-tooltip
        >
          <!-- tips提示框 只有超过显示的才会提示-->
          <template slot-scope="{ row }">
            {{ row[item.index] }}
          </template>
        </el-table-column>
        <!-- 图片 -->
        <el-table-column
          v-else-if="item.showImage"
          :key="item.index"
          :label="item.name"
          :width="item.width"
          :min-width="item.minWidth || 100"
        >
          <template slot-scope="{ row }">
            <img
              v-if="item.canClick"
              :src="row[item.index]"
              :min-width="item.width"
              :height="item.height"
              @click="handlePicture(row)"
            />
            <img
              v-if="!item.canClick"
              :src="row[item.index]"
              :min-width="item.width"
              :height="item.height"
            />
          </template>
        </el-table-column>
        <!-- 操作 -->
        <el-table-column
          v-else-if="item.operation"
          :key="item.index"
          :label="item.name"
          :width="item.width"
          :min-width="item.minWidth || 100"
          fixed="right"
          class-name="operation-box"
        >
          <template slot-scope="{ row, $index }">
            <template v-for="btn in operation">
              <el-button
                v-if="btn.condition ? btn.conditions(row, $index) : true"
                :key="btn.type"
                :disabled="(btn.disabled && btn.disabled(row)) || false"
                type="text"
                @click="operationClick(btn.type, row, $index)"
              >
                {{ (btn.format && btn.format(row)) || btn.name }}
              </el-button>
            </template>
          </template>
        </el-table-column>
        <!-- 多内容 -->
        <el-table-column
          v-else-if="item.addition"
          :key="item.index"
          :label="item.name"
          :width="item.width"
          :min-width="item.minWidth || 100"
          :fixed="item.fixed"
          class-name="addition-box"
          :formatter="item.formatter"
        >
          <template slot-scope="{ row, $index }">
            <!-- <span>1{{ row.name }}</span> -->
            <span></span>
            <template v-for="i in addition">
              <template
                v-if="i.canClick ? i.canClicks(row, $index) : false"
                type="text"
              >{{ i.name }}</template
              >
              <el-button
                v-if="i.condition ? i.conditions(row, $index) : false"
                :key="i.type"
                type="text"
                @click="additionClick(i.type, row, $index)"
              >{{ i.name }}</el-button
              >
            </template>
          </template>
        </el-table-column>
        <el-table-column
          v-else
          :key="item.index"
          :prop="item.index"
          :label="item.name"
          :width="item.width"
          :min-width="item.minWidth || 100"
          :fixed="item.fixed"
          :class-name="item.className"
          :formatter="item.formatter"
          :render-header="item.renderHeader"
        />
      </template>
    </el-table>

    <div
      v-if="isPagination"
      class="pagination-container"
    >
      <el-pagination
        :layout="pagination.layout"
        :page-sizes="pagination.pageSizes"
        :current-page.sync="listQuery.page"
        :page-size.sync="listQuery.pageSize"
        :total="listQuery.total"
        prev-text="上一页"
        next-text="下一页"
        @current-change="getList"
        @size-change="updateSize"
      >
        <span
          class="page-customer"
        >共{{ listQuery.total }}条数据，每页{{ listQuery.pageSize }}条，共{{
            listQuery.total % listQuery.pageSize === 0
              ? Math.floor(listQuery.total / listQuery.pageSize)
              : Math.floor(listQuery.total / listQuery.pageSize) + 1
          }}页</span>
      </el-pagination>
    </div>
  </div>
</template>
<script>
import empty from '@/assets/notUser.png';

export default {
  name: 'XyyTable',
  components: {},
  props: {
    spanMehtod: {
      type: Boolean,
      default: false,
    },
    data: {
      type: Array,
      default() {
        return [];
      },
    },
    listQuery: {
      type: Object,
      default() {
        return {
          page: 1,
          pageSize: 10,
          total: 0,
        };
      },
    },
    col: {
      type: Array,
      default() {
        return [];
      },
    },
    operation: {
      type: Array,
      default() {
        return [];
      },
    },
    addition: {
      type: Array,
      default() {
        return []
      }
    },
    isPagination: {
      type: Boolean,
      default: true,
    },
    hasSelection: {
      type: Boolean,
      default: false,
    },
    reserveSelection: {
      type: Boolean,
      default: false,
    },
    selectable: {
      type: Function,
      default() {
        return () => {}
      }
    },
    rowKey: {
      type: String,
      default: 'id',
    },
    hasIndex: {
      type: Boolean,
      default: false,
    },
    indexLabel: {
      type: String,
      default: '序号',
    },
    showSummary: {
      type: Boolean,
      default: false,
    },
    totalData: {
      type: Object,
      default() {
        return {};
      },
    },
    isStripe: {
      type: Boolean,
      default: true,
    },
    emptyText: {
      type: String,
      default: '暂无数据',
    },
    mergeCols: {
      type: Array,
      default: () => [],
    }, // 需要合并的列
    headerCellStyle: {},
  },
  watch: {
    listQuery:{//深度监听，可监听到对象、数组的变化
      handler(val, oldVal){
        this.listQuery = val
      },
      deep:true //true 深度监听
    }
  },
  data() {
    return {
      marginRight: true,
      tableHeight: 50,
      pagination: {
        layout: 'slot, sizes, prev, pager, next, jumper',
        pageSizes: [10, 20, 30, 40, 50, 100, 200, 500],
      },
      spanArr: [],
      emptyData: empty,
    };
  },
  methods: {
    soryByPrice(column){
        console.log(column)
        this.$emit('soryByPrice',column)
    },
    cellStyle({ row, column, rowIndex, columnIndex }){
      if(row.overdue === 1 && columnIndex === 7) {
        return 'color:#f00'
      }
    },
    getList() {
      const { page, pageSize } = this.listQuery;
      this.$emit('get-data', { page, pageSize });
    },
    updateSize() {
      this.getList();
    },
    operationClick(type, row, index) {
      this.$emit('operation-click', type, row, index);
    },
    handlePicture(row, index) {
      this.$emit('handlePicture-click', row, index)
    },
    additionClick(type, row, index) {
      this.$emit('addition-click', type, row, index)
    },
    /**
     * 选中操作
     */
    handleSelectionChange(val) {
      if (this.hasSelection) {
        this.$emit('selectionCallback', val);
      }
    },
    /**
     * 初始化选中
     */
    initSelections(ids) {
      this.$nextTick(() => {
        this.$refs.table.clearSelection();
        this.data.forEach((el) => {
          if (ids.includes(el[this.rowKey])) {
            this.$refs.table.toggleRowSelection(el, true);
          }
        }, this);
      });
    },
    handleCellClick(row, column, cell, event) {
      this.$emit('handleCellClick', row, column, cell, event);
    },
    handleCellMouseEnter(row, column, cell, event) {
      this.$emit('handleCellMouseEnter', row, column, cell, event);
    },
    indexFormat(index) {
      return index + 1;
    },
    getSummaries(param) {
      if (!this.showSummary) return -1;
      const { columns, data } = param;
      if (data.length === 0) return -1;
      const sums = [];
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = '合计';
          return;
        }
        sums[index] = this.totalData[column.property];
      });
      return sums;
    },
    // eslint-disable-next-line consistent-return
    spanCallback({ column, rowIndex }) {
      if (this.mergeCols.includes(column.property)) {
        if (rowIndex === 0) {
          return [this.listQuery.pageSize, 1];
        }
        return [0, 0];
      }
    },
  },
};
</script>

<style lang="scss" scoped>
/* 省略号 */
.popover-reference {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}
/* table */
::v-deep  .el-table {
  &::before,
  ::before {
    background-color: #fff;
  }
  font-family: PingFangSC-Regular;
  font-weight: 400;
  color: #292933;
  border-radius: 2px;
  border: 1px solid #e4e4eb;
  tr {
    td {
      border-right: 1px solid #e4e4eb;
      .cell {
        padding-left: 20px;
        padding-right: 20px;
      }
      &.el-table-column--selection {
        .cell {
          padding: 0 10px;
        }
      }
    }
  }

  thead {
    color: #292933;
    th {
      height: 40px;
      padding: 0;
      font-weight: 400;
      background: #f9f9f9;
      border: none;
      border-right: 1px solid #e0e0e0;
      text-align: center;
    }
  }
  tr {
    td {
      height: 40px;
      padding: 0;
      border: none;
      background: #ffffff;
      border-bottom: 1px solid #e0e0e0;
      border-right: 1px solid #e0e0e0;
      text-align: center;
      .cell {
        height: 40px;
        line-height: 40px;
        overflow-x: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        text-align: center;
      }
    }
  }
  .el-table__body {
    // 隔行变色
    tr.el-table__row--striped {
      td {
        background: #ffffff;
      }
    }
  }
  .table-row,
  .table-header-row {
    height: 40px;
  }
  // 解决右侧操作列错位问题
  .el-table__fixed-right {
    bottom: 0;
    height: auto !important;
  }
  .el-table__fixed-right-patch {
    background: #eeeeee;
  }
  .el-table__fixed-body-wrapper {
    position: absolute;
    top: 40px !important;
    bottom: 0;
    height: auto !important;
  }
  // 解决整体出现滚动条后底部白边
  &.el-table--scrollable-y .el-table__body-wrapper {
    position: absolute;
    top: 40px;
    bottom: 0;
    height: auto !important;
  }

  .operation-box {
    width: auto;
    white-space: nowrap;
    letter-spacing: 0;
    margin: 0 -10px;
    .el-button {
      position: relative;
      margin: 0 10px;
    }
    .el-button::before {
      position: absolute;
      top: 14px;
      right: -10px;
      content: '';
      display: block;
      width: 1px;
      height: 12px;
      background: #dcdfe6;
    }
    .el-button:first-child {
      margin-left: 0;
    }
    .el-button:last-child {
      margin-right: 0;
    }
    .el-button:last-child::before {
      display: none;
    }
  }
}
/* 分页 */
::v-deep  .pagination-container {
  margin-top: 20px;
  text-align: right;
  .el-pagination {
    position: relative;
    padding: 0;
    overflow: hidden;
    display: block;
    .page-customer {
      float: left;
      color: #999999;
      font-size: 12px;
    }
    // .el-pagination__total {
    //   position: absolute;
    //   top: 2px;
    //   left: 5px;
    //   height: 32px;
    //   line-height: 32px;
    //   font-size: 14px;
    //   font-family: PingFangSC-Regular;
    //   font-weight: 400;
    //   color: #575766;
    // }
    @mixin reset($h: 32px, $fs: 14px) {
      height: $h;
      line-height: $h;
      font-size: $fs;
      font-family: Helvetica;
      font-weight: 400;
    }
    span,
    button {
      @include reset;
    }
    .el-pager {
      li {
        @include reset;
      }
    }
    .el-input--mini .el-input__inner {
      @include reset;
    }
    .el-pagination__editor.el-input .el-input__inner {
      @include reset;
    }
    .btn-prev,
    .btn-next,
    .el-pager li {
      height: 32px;
    }
    .el-pagination__editor {
      margin: 0 9px;
    }
  }
}
/* tips */
::v-deep  .el-tooltip__popper.is-dark {
  width: 300px;
  background: #fff;
  margin: 0;
  color: #000;
  border-radius: 2px;
  box-shadow: 0 0 5px #a9a9a9;
  .popper__arrow {
    border-top-color: #fff;
    &:after {
      border-top-color: #fff;
    }
  }
}
</style>
