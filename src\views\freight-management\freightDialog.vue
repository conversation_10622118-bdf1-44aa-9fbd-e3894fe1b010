<template>
  <el-dialog
    v-loading="loadingDialog"
    element-loading-background="rgba(0, 0, 0, 0.3)"
    :title="freightDialogType === 'edit' ? '编辑运费' : freightDialogType === 'see' ? '运费详情' : '新增运费'"
    :visible="true"
    :before-close="cancellationBtn"
    class="freightDetail"
    :close-on-click-modal="false"
  >
    <el-form
      ref="freightForm"
      :model="freightForm"
      :rules="rules"
      size="small"
    >
      <el-form-item
        v-if="provinceData.length"
        label="区域"
        :label-width="formLabelWidth"
        prop="areaCodeStr"
      >
        <div class="areaBox">
          <el-tree
            ref="tree"
            :data="provinceData"
            show-checkbox
            :render-content="renderContent"
            :default-expanded-keys="defaultExpandedKeys"
            highlight-current
            :props="treeProps"
            node-key="areaCode"
            @check="cityCheckChange"
            @node-expand="handleExpand"
          />
        </div>
      </el-form-item>
      <el-form-item
        label="是否包邮"
        :label-width="formLabelWidth"
        prop="templateType"
      >
        <div class="Fradios">
          <el-radio
            v-model="freightForm.templateType"
            :label="2"
            :disabled="freightDialogType === 'see'"
          >
            商家承担运费
          </el-radio>
          <el-radio
            v-model="freightForm.templateType"
            :label="1"
            :disabled="freightDialogType === 'see'"
          >
            自定义运费
          </el-radio>
        </div>
      </el-form-item>
      <el-form-item
        label="运费名称"
        :label-width="formLabelWidth"
        prop="tempName"
      >
        <el-input
          v-model="freightForm.tempName"
          autocomplete="off"
          class="diaSWidth"
          :disabled="freightDialogType === 'see'"
        />
      </el-form-item>
      <el-form-item
        v-if="freightForm.templateType === 1"
        label="满"
        :label-width="formLabelWidth"
        prop="freePostageAmount"
      >
        <el-input
          v-model.number="freightForm.freePostageAmount"
          autocomplete="off"
          class="diaSWidth"
          :disabled="freightDialogType === 'see'"
        />
        <span>元包邮</span>
      </el-form-item>
      <el-form-item
        v-if="freightForm.templateType === 1"
        label="不满足条件时收取运费"
        :label-width="formLabelWidth"
        prop="postageFee"
      >
        <el-input
          v-model.number="freightForm.postageFee"
          autocomplete="off"
          class="diaSWidth"
          :disabled="freightDialogType === 'see'"
        />
        <span>元</span>
      </el-form-item>
      <el-form-item
        v-if="freightForm.templateType === 1"
        label="低于"
        :label-width="formLabelWidth"
        prop="lowBuyAmount"
      >
        <el-input
          v-model.number="freightForm.lowBuyAmount"
          autocomplete="off"
          class="diaSWidth"
          :disabled="freightDialogType === 'see'"
        />
        <span>元不允许下单</span>
      </el-form-item>
      <el-form-item>
        <div class="footerBtn">
          <el-button
            @click="cancellationBtn"
          >
            取 消
          </el-button>
          <el-button
            v-if="freightDialogType !== 'see'"
            type="primary"
            :loading="submitLoading"
            @click="newFreightTem('freightForm')"
          >
            确 定
          </el-button>
        </div>
      </el-form-item>
    </el-form>
  </el-dialog>
</template>
<script>
import {
  branchsList,
  addShipping,
  getAreaCode,
  apiQuerySaleableArea
} from '@/api/freightTemplate/index';

const DEFAULT_SELECT_ALL_CODE = 0;
export default {
  name: 'FreightDialog',
  props: {
    freightDialogType: {
      type: String,
      default: 'add',
    },
    freightDetail: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      regionasList: [],
      formLabelWidth: '90px',
      freightForm: {
        areaCodeStr: [],
        templateType: 1,
        tempStatus: 1,
        freePostageAmount: '',
        postageFee: '',
        lowBuyAmount: '',
        tempName: '',
      },
      loadingDialog: false,
      defaultExpandedKeys: [DEFAULT_SELECT_ALL_CODE],
      treeProps: {
        label: 'areaName',
        children: 'children',
        isLeaf: (data, node) => {
          return node.areaLevel === 4;
        },
      },
      checkAreas: [],
      provinceData: [],
      submitLoading: false,
      rules: {
        tempName: [
          { required: true, message: '请输入运费名称', trigger: 'blur' },
          { min: 1, max: 50, message: '长度在 1 到 50 个字符', trigger: 'blur' },
        ],
        freePostageAmount: [
          { required: true, message: '包邮金额不能为空', trigger: 'blur' },
          { type: 'number', message: '包邮金额必须为数字值', trigger: 'blur' },
        ],
        postageFee: [
          { required: true, message: '收取运费不能为空', trigger: 'blur' },
          { type: 'number', message: '收取运费必须为数字值', trigger: 'blur' },
        ],
        lowBuyAmount: [
          { required: true, message: '低于金额不能为空', trigger: 'blur' },
          { type: 'number', message: '低于金额必须为数字值', trigger: 'blur' },
        ],
      },
    };
  },
  created() {
    this.initData();
    this.queryRegional();
    this.getArea();
  },
  methods: {
    handleExpand() {
      this.$nextTick(() => {
        this.changeCss();
      });
    },
    renderContent(h, { node }) {
      // 树节点的内容区的渲染 Function
      let classname = '';
      // 由于项目中有三级菜单也有四级级菜单，就要在此做出判断
      if (node.level === 4) {
        classname = 'foo';
      }
      if (node.level === 3 && node.childNodes.length === 0) {
        classname = 'foo';
      }
      return h('p', { class: classname }, node.label);
    },
    changeCss() {
      const levelName = document.getElementsByClassName('foo'); // levelname是上面的最底层节点的名字
      for (let i = 0; i < levelName.length; i++) {
        // cssFloat 兼容 ie6-8  styleFloat 兼容ie9及标准浏览器
        levelName[i].parentNode.style.cssFloat = 'left'; // 最底层的节点，包括多选框和名字都让他左浮动
        levelName[i].parentNode.style.styleFloat = 'left';
        levelName[i].parentNode.onmouseover = function () {
          this.style.backgroundColor = '#fff';
        };
      }
    },
    initData() {
      this.freightForm = this.freightDialogType === 'add' ? this.freightForm : JSON.parse(JSON.stringify(this.freightDetail));
      this.checkAreas = this.freightForm.shopAreaCodeDtos || [];
    },
    async getArea() {
      this.loadingDialog = true;
      apiQuerySaleableArea({}).then((res) => {
        this.loadingDialog = false;
        const { data, code } = res;
        if (code === 0) {
          if (data.length === 0) {
            this.provinceData = [];
          } else {
            this.provinceData = [];
            const selectAll = {
              areaName: '全国',
              areaCode: DEFAULT_SELECT_ALL_CODE,
              areaLevel: 0,
              children: data,
            };
            this.provinceData.push(selectAll);
            this.$nextTick(() => {
              // 默认选中的树的数据
              this.$refs.tree.setCheckedKeys(this.freightForm.areaCodes || []);
              if (this.freightDialogType === 'see') {
                this.setDisabled(this.provinceData);
              }
            });
          }
        }
      }).catch(() => {
        this.loadingDialog = false;
      });
    },
    setDisabled(obj) {
      obj.forEach((item) => {
        this.$set(item, 'disabled', true);
        if (item.children) {
          this.setDisabled(item.children);
        }
      });
      return obj;
    },
    cityCheckChange(data, nodes) {
      const { checkedNodes } = nodes;
      // key 为选中节点，value 为是否要筛选出来 true false
      const selectCode = new Map();
      checkedNodes.forEach((item) => {
        if (!selectCode.has(item.areaCode)) {
          selectCode.set(item.areaCode, true);
          if (item.parentId && selectCode.has(item.parentId)) {
            selectCode.set(item.areaCode, false);
          }
        }
      });
      const selectArrayCode = [];
      selectCode.forEach((value, key) => {
        if (value) {
          selectArrayCode.push(key);
        }
      });
      const filterCheckNodes = [];
      checkedNodes.forEach((item) => {
        if (selectArrayCode.indexOf(item.areaCode) >= 0) {
          filterCheckNodes.push(item);
        }
      });
      this.checkAreas = filterCheckNodes;
    },
    queryRegional() {
      branchsList().then((res) => {
        this.regionasList = res.result;
      });
    },
    cancellationBtn() {
      this.$emit('cancellationBtn');
    },
    newFreightTem(formData) {
      this.$refs[formData].validate((valid) => {
        if (this.checkAreas.length <= 0) {
          this.$message.error('请选择正确的区域信息');
          return false;
        }
        if (this.checkAreas.find(i => i.areaCode === 0)) {
          this.checkAreas = this.checkAreas.filter(i => i.areaCode === 0);
        }
        const checkAreasArr = this.checkAreas.map((item) => {
          return {
            areaCode: item.areaCode,
            areaName: item.areaName,
            level: item.areaLevel || item.level,
          };
        });
        if (valid) {
          this.submitLoading = true;
          const addFormData = this.freightForm;
          const addFormData1 = {
            type: this.freightDialogType === 'edit' ? 2 : 1,
            // branchCodes: addFormData.branchCodes,
            templateType: addFormData.templateType,
            tempStatus: addFormData.tempStatus,
            freePostageAmount: addFormData.freePostageAmount,
            postageFee: addFormData.postageFee,
            lowBuyAmount: addFormData.lowBuyAmount,
            tempName: addFormData.tempName,
            id: this.freightDialogType === 'edit' ? addFormData.id : null,
            areaCodeStr: JSON.stringify(checkAreasArr),
          };
          const addFormData2 = {
            type: this.freightDialogType === 'edit' ? 2 : 1,
            // branchCodes: addFormData.branchCodes,
            templateType: addFormData.templateType,
            tempStatus: addFormData.tempStatus,
            tempName: addFormData.tempName,
            id: this.freightDialogType === 'edit' ? addFormData.id : null,
            areaCodeStr: JSON.stringify(checkAreasArr),
          };
          addShipping(
            addFormData.templateType === 1 ? addFormData1 : addFormData2,
          ).then((res) => {
            this.submitLoading = false;
            if (res.code === 0) {
              this.$message.success(`${this.freightDialogType === 'add' ? '新增' : '编辑'}运费模板成功`);
              this.$emit('refreshList');
            } else if (res.code === 2) {
              const content = `<div><p>所选区域已存在运费模板,不能重复创建</p><div>重复区域为：${res.data}</div></div>`;
              this.$confirm(content, '提示', {
                showCancelButton: false,
                dangerouslyUseHTMLString: true,
              });
            } else {
              this.$message.error(res.message);
            }
          });
          return true;
        }
      });
    },
  },
};
</script>
<style lang="scss" scoped>
.freightDetail {
  padding: 0 20px;
  .areaBox {
    max-height: 200px;
    min-height: 100px;
    overflow-y: auto;
    border: 1px solid rgba(208, 208, 208, 1);
    margin-top: 10px;
  }
  .diaSWidth {
    width: 320px;
    margin-left: 20px;
    margin-right: 10px;
  }
  .Fradios {
    margin-left: 40px;
  }
  .footerBtn {
    padding: 20px;
    padding-top: 10px;
    text-align: right;
    box-sizing: border-box;
    padding-bottom: 0;
  }
}

::v-deep  .el-divider--horizontal {
  margin: 0px;
}
::v-deep  .el-form-item__error {
  margin-left: 40px;
}
</style>
