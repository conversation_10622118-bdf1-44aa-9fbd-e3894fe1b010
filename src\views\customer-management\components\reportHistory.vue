<script>
import iSelect from '../../qualificationOnline/components/i-select.vue'
import lDialog from '../../../components/lwq-comp/l-dialog.vue';
import { getAuditHistory } from '../../../api/customer-management/index'
export default {
  components: {
    iSelect,
    lDialog
  },
  data() {
    return {
      form: {
        skuCode: '',
        merchantName: '',
        auditStatus: '',
      },
      pagination: {
        pageNum: 1,
        pageSize: 10,
        total: 0
      },
      list: [],
      auditStatus: [{
        value: '',
        label: '全部'
      }, {
        value: 0,
        label: '待审核'
      }, {
        value: 1,
        label: '审核通过'
      }, {
        value: -1,
        label: '审核驳回'
      }],
      visible: false,
      loading: false
    }
  },
  methods: {
    open() {
      this.visible = true;
      this.clear();
    },
    search() {
      if (this.loading) return
      this.loading = true;
      getAuditHistory({
        ...this.form,
        pageNum: this.pagination.pageNum,
        pageSize: this.pagination.pageSize
      }).then(res => {
        if (res.code == 0) {
          this.list = res.data.list;
          this.pagination.total = res.data.total;
        } else {
          this.$message.error(res.message);
        }
      }).finally(() => {
        this.loading = false;
      })
    },
    handleSizeChange(pageSize) {
      this.pagination.pageSize = pageSize;
      this.search();
    },
    handleCurrentChange(pageNum) {
      this.pagination.pageNum = pageNum;
      this.search();
    },
    clear() {
      this.form = {
        skuCode: '',
        merchantName: '',
        auditStatus: '',
      }
      this.pagination.pageNum = 1;
      this.search();
    }
  }
}
</script>

<template>
    <div style="display: inline-block;">
    <div @click="open" style="display: flex;">
      <slot>
        <el-button size="mini" type="primary">举报记录查询</el-button>
      </slot>
    </div>
    <lDialog :visible.sync="visible" title="举报记录查询" width="900px" @close="visible = false;">
      <el-row>
        <el-col :xs="16" :md="12" :lg="8" :xl="8" style="margin-right: 10px;">
          <el-input placeholder="请输入商品编码/CUSID" size="mini" v-model="form.skuCode" clearable>
            <template slot="prepend">商品编码</template>
          </el-input>
        </el-col>
        <el-col :xs="16" :md="12" :lg="6" :xl="6" style="margin-right: 10px;">
          <el-input placeholder="药店名称" size="mini" v-model="form.merchantName" clearable>
            <template slot="prepend">药店名称</template>
          </el-input>
        </el-col>
        <el-col :xs="16" :md="12" :lg="8" :xl="8">
          <iSelect size="mini" label="状态" :list="auditStatus"  v-model="form.auditStatus"></iSelect>
        </el-col>
      </el-row>
      <div style="display: flex;justify-content: end;margin-top:10px;">
        <el-button size="mini" type="primary" @click="search">查询</el-button>
        <el-button size="mini" @click="clear">重置</el-button>
      </div>
      <el-table ref="table" :data="list" border stripe style="margin: 10px 0;" height="300">
        <el-table-column align="center" prop="barcode" label="商品编码" width="180"></el-table-column>
        <el-table-column align="center" prop="csuid" label="CSUID" width="250"></el-table-column>
        <el-table-column align="center" prop="productName" label="商品名称" width="250"></el-table-column>
        <el-table-column align="center" prop="merchantId" label="药店ID" width="100"></el-table-column>
        <el-table-column align="center" prop="sellerUserId" label="客户ERP编码" width="100"></el-table-column>
        <el-table-column align="center" prop="merchantName" label="药店名称" width="100"></el-table-column>
        <el-table-column align="center" prop="reportTime" label="举报时间" width="100"></el-table-column>
        <el-table-column align="center" prop="auditStatusStr" label="状态" width="100"></el-table-column>
        <el-table-column align="center" prop="auditDesc" label="审核原因" width="150"></el-table-column>
      </el-table>
      <div style="display: flex;justify-content: end;margin: 5px 0;">
        <el-pagination
          background
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          layout="total, prev, pager, next, sizes"
          :page-sizes="[5, 10, 15, 20]"
          :total="pagination.total">
        </el-pagination>
      </div>
      <div slot="footer" style="display: flex;justify-content: end;">
        <el-button size="mini" type="primary" @click="visible = false;">确定</el-button>
      </div>
    </lDialog>
  </div>
</template>

<style scoped>
</style>
