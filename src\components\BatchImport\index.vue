<template>
  <el-dialog
    v-loading="loading"
    :title="title"
    :visible="dialogVisible"
    width="40%"
    :before-close="handleClose"
  >
    <el-upload
      ref="upload"
      action=""
      class="upload-box"
      :accept="acceptList"
      :on-change="uploadChange"
      :on-exceed="fileBeyond"
      :before-remove="remove"
      :http-request="httpRequest"
      :limit="1"
      :file-list="fileLists"
      :auto-upload="false"
    >
      <el-button type="primary" size="small"> 导入excel文件</el-button>
      <el-button class="downloadTemplate" slot="tip" plain size="small" @click="downloadTemplate">下载模板</el-button>
    </el-upload>
    <p>
      <slot name="tips"></slot>
    </p>
    <span slot="footer" class="dialog-footer">
        <el-button size="small" @click="handleClose">取 消</el-button>
        <el-button type="primary" size="small" @click="submit"
        >确 定</el-button
        >
      </span>
  </el-dialog>
</template>

<script>

export default {
  name: 'batchImport',
  props: {
    title: {
      type: String,
      default: '批量导入'
    },
    acceptList: {
      type: String,
      default: '.xls,.xlsx'
    }
  },
  data() {
    return {
      loading: false,
      dialogVisible: true,
      fileLists: []
    };
  },
  methods: {
    handleClose() {
      this.$emit('update:batchImportVisible', false);
    },
    submit() {
      console.log(this.fileLists);
      if (this.fileLists.length) {
        this.$refs.upload.submit();
      } else {
        this.loading = false;
        this.$alert('请先导入商品！', { type: 'warning' });
      }
    },
    uploadChange(file, fileList) {
      if (file.status === 'ready') {
        const fileType = '.' + file.raw.name.split('.')[1]
        if (this.acceptList.split(',').includes(fileType)) {
          this.fileLists = fileList
        } else {
          this.$message.warning(`请导入${this.acceptList}格式文件`)
          this.fileLists = []
        }
      }
    },
    async httpRequest(param) {
      const fd = new FormData(); // FormData 对象
      fd.append('file', param.file); // 文件对象
      this.loading = true;
      this.$emit('batchImportFn', fd);
    },
    closeLoading() {
      this.loading = false;
    },
    // 文件超出限制
    fileBeyond() {
      this.$message({
        message: '每次最多上传1个文件',
        type: 'warning'
      });
    },
    remove() {
      this.fileLists = [];
    },
    downloadTemplate() {
      this.$emit('downloadTemplate');
    }
  }
};
</script>

<style scoped>
.downloadTemplate {
  margin-left: 20px;
}
</style>
