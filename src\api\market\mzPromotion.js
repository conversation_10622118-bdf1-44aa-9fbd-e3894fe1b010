import request from '../index';

// 满赠活动列表
export function getPromotionList(data) {
  return request.post('/fullGive/pageList', data);
}

// 保存满赠活动
export function savePromotion(data) {
  return request.post('/fullGive/saveOrUpdate', data);
}

// 满赠活动详情
export function getDetail(data) {
  return request.get('/fullGive/detail', data);
}

// 修改满赠数量
export function updateGiveCount(data) {
  return request.postFormData('/fullGive/updateGiveCount', data);
}

// 保存满赠活动校验互斥
export function checkMutex(data) {
  return request.post('/fullGive/checkMutex', data);
}

// 下线
export function offLine(data) {
  return request.get('/fullGive/offLine', data);
}

// 获取赠品库存
export function getCsuAvailableQty(data) {
  return request.get('/fullGive/getCsuAvailableQty', data);
}

// 批量导入
export function batchCreateFullGive(data) {
  return request.post('/fullGive/batchCreateFullGive', data);
}

//满赠导出
export function asyncExport(data) {
  return request.post('/fullGive/asyncExport', data)
}
//批量导入主品 or 赠品池
export function importProduct(data) {
  const formData = new FormData();
  for (const key in data) {
    formData.append(key, data[key]);
  }
  return request.post('/fullGive/importProduct', formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  })
}
//批量导入赠品池模板
export function givePoolFullGiveTemplate() {
  location.href = '/fullGive/givePoolFullGiveTemplate';
   /* request.get('/fullGive/givePoolFullGiveTemplate', { responseType: 'blob' }).then(res => {
    const blob = new Blob([res])
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a');
    a.href = url;
    a.download = '批量导入赠品池.xls';
    a.target = '_blank';
    a.click();
    URL.revokeObjectURL(url)
  }) */
}
//修改赠品限购数量
export function batchUpdateGiveCount(params) {
  return request.post('/fullGive/batchUpdateGiveCount', params)
}
//批量导入主品模板
export function masterFullGiveTemplate() {
  location.href = '/fullGive/masterFullGiveTemplate';
  /* return request.get('/fullGive/masterFullGiveTemplate').then(res => {
    const blob = new Blob([res])
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a');
    a.href = url;
    a.download = '批量导入主品.xls';
    a.target = '_blank';
    a.click();
    URL.revokeObjectURL(url)
  }) */
}
//批量下线模板
export function batchOffLineFullGiveTemplate() {
  location.href = '/fullGive/batchOffLineFullGiveTemplate'
}
//批量下线
export function batchOffLine(params) {
  return request.postFormData('/fullGive/batchOffLine', params)
}
//获取选择赠品数量上线
export function fullGiveImportCount(params) {
  return request.get('/fullGive/importCount', params)
}
