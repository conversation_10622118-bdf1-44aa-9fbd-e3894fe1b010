<template>
  <el-dialog
    v-loading="loading"
    title="批量导入"
    :visible="dialogVisible"
    width="40%"
    :before-close="handleClose"
  >
    <el-upload
      ref="upload"
      action=""
      class="upload-box"
      accept=".xls,.xlsx"
      :on-change="uploadChange"
      :on-exceed="fileBeyond"
      :before-remove="remove"
      :http-request="httpRequest"
      :limit="1"
      :file-list="fileLists"
      :auto-upload="false"
    >
      <el-button type="primary" plain size="small"> 导入excel文件</el-button>
      <el-button class="downloadTemplate" slot="tip" plain size="small" @click="downloadTemplate">下载模板</el-button>
    </el-upload>
    <p>提示：</p>
    <div v-if="type !== 5">
      <p>1.请您留意商品编码和商户ERP编码是否一一对应；</p>
      <p>2.支持上传xlsx、xls文件，大小不好过3M，商品数量不超过1000个；</p>
      <p>3.模版中商户ERP编码和商品编码任意一项不为空即可，若同一ERP编码对应多个商品请填写商品编码。其他需要修改字段选填，不修改字段可不填；</p>
      <p v-if="type !== 4">4.如果导入数据达到1000条，大概导入时间为1分钟。</p>
      <p v-if="type === 4">4.如需修改商品ERP编码，则下载“批量修改ERP编码”后，第一列填写商品编码，第二
  列填写新的ERP编码；</p>
      <p v-if="type === 4">5.如果导入数据达到1000条，大概导入时间为1分钟。</p>
    </div>
    <div v-if="type === 5">
      <p>支持上传xlsx、xls文件，大小不超过3M，客户数量不超过2000个</p>
    </div>
    <span slot="footer" class="dialog-footer">
        <el-button size="small" @click="handleClose">取 消</el-button>
        <el-button type="primary" size="small" @click="submit"
        >确 定</el-button
        >
      </span>
  </el-dialog>
</template>

<script>
import { batchUpdateInfoFromExcel, apiDownloadTemplate, batchUpdatePrice, batchUpdateStock, batchUpdateErpCode } from '@/api/product';
import { cusTomerBatchUpdateErpCode } from '@/api/customer-management';
import { mapState } from 'vuex';

export default {
  name: 'BatchModifyProduct',
  props: {
    excelTemplate: {
      type: String,
      default: '',
    },
    priceType: {
      type: Number,
      default: 1,
    },
    type: {
      type: Number,
      default: 1,
    },
  },
  data() {
    return {
      loading: false,
      dialogVisible: true,
      fileLists: [],
    };
  },
  computed: { ...mapState('app', ['shopConfig']) },
  methods: {
    handleClose() {
      this.$emit('update:batchModifyProductVisible', false);
    },
    submit() {
      console.log(this.fileLists);
      if (this.fileLists.length) {
        this.$refs.upload.submit();
      } else {
        this.loading = false;
        // this.$message({
        //   message: '请先导入商品！',
        //   type: 'warning'
        // })
        this.$alert('请先导入商品！', { type: 'warning' });
      }
    },
    uploadChange(file, fileList) {
      if (file.status === 'ready') {
        this.fileLists = fileList;
      }
    },
    async httpRequest(param) {
      const fd = new FormData(); // FormData 对象
      fd.append('file', param.file); // 文件对象
      const h = this.$createElement;
      try {
        this.loading = true;
        let res = null;
        if (this.type === 1) {
          res = await batchUpdateInfoFromExcel(fd);
        }
        if (this.type === 2) {
          res = await batchUpdateStock(fd);
        }
        if (this.type === 3) {
          res = await batchUpdatePrice(fd);
        }
        if (this.type === 4) {
          res = await batchUpdateErpCode(fd);
        }
        if (this.type === 5) {
          res = await cusTomerBatchUpdateErpCode(fd);
        }
        if (res && res.code === 0) {
          const { error, success, errorFileName, errorFileUrl } = res.data;
          const baseUrl = process.env.VUE_APP_BASE_API;
          this.$msgbox({
            title: '上传文件反馈',
            message: h('p', null, [
              h('div', null, `共成功上传${success}个商品，失败${error}条数据`),
              h('span', `${error ? '下载失败文件：' : ''}`),
              error ? h('a', { attrs: { href: baseUrl + errorFileUrl, download: errorFileName }, style: 'color:#4183d5;' }, `${errorFileName}`) : '']),
            confirmButtonText: '确定',
          }).then(() => {
            if (success) this.$emit('refreshTable');
            this.handleClose();
          }).catch(() => {
            if (success) this.$emit('refreshTable');
            this.handleClose();
          });
        } else {
          this.fileLists = [];
          // this.$message.error(res.message || '导入失败，请重新上传！')
          this.$alert(res.message || '导入失败，请重新上传！', { type: 'error' });
        }
        this.loading = false;
      } catch (err) {
        console.log(err);
      }
      this.loading = false;
    },
    // 文件超出限制
    fileBeyond() {
      this.$message({
        message: '每次最多上传1个文件',
        type: 'warning',
      });
    },
    remove() {
      this.fileLists = [];
    },
    downloadTemplate() {
      // const a = document.createElement('a')
      // const filename = '上传模板.xlsx'
      // a.href = this.excelTemplate
      // a.download = filename
      // document.body.appendChild(a)
      // a.click()
      // document.body.removeChild(a)
      try {
        const fileName = { 1: '批量修改商品信息.xlsx', 2: '批量修改库存.xlsx', 3: '批量修改价格.xlsx', 4: '批量修改商品ERP编码.xlsx', 5: '批量修改客户ERP编码.xlsx' }[this.type];
        const params = { fileName };
        if (this.type === 3) {
          params.priceType = this.priceType;
        }
        console.log(232323232, params);
        apiDownloadTemplate(params).then((res) => {
          if (res.code && res.code !== 0) {
            this.$message.error(res.message || '请求异常');
          } else {
            this.util.exportExcel(res, fileName);
          }
        }).catch((err) => {
          console.log(err);
        });
      } catch (e) {}
    },
  },
};
</script>

<style scoped>
.downloadTemplate{
  margin-left: 20px;
}
</style>
