<template>
  <el-dialog
    v-loading="loadingDialog"
    element-loading-background="rgba(0, 0, 0, 0.3)"
    :title="accountDialogType === 'edit' ? '编辑账号' : '新增账号'"
    :visible="true"
    :before-close="cancellationBtn"
    class="accountDetail"
    width="55%"
    :close-on-click-modal="false"
  >
    <el-form ref="formData" :model="formData" :rules="rules" size="small">
      <el-form-item label="用户真实姓名" :label-width="formLabelWidth" prop="realName">
        <el-input
          v-model="formData.realName"
          autocomplete="off"
          class="diaSWidth"
          placeholder="请输入用户真实姓名"
        />
      </el-form-item>
      <el-form-item label="账号(手机号)" :label-width="formLabelWidth" prop="userName">
        <el-input
          v-model.number="formData.userName"
          autocomplete="off"
          placeholder="请填写用户手机号"
          maxlength="11"
          class="diaSWidth"
        />
        <span class="item-info">该手机号用于登录，仅支持数字</span>
      </el-form-item>
      <el-form-item
        v-if="accountDialogType !== 'edit'"
        label="初始登录密码"
        :label-width="formLabelWidth"
        prop="password"
      >
        <el-input
          v-model="formData.password"
          autocomplete="off"
          type="password"
          class="diaSWidth"
          placeholder="请填写登录密码"
        />
        <span class="item-info">该密码用于登录，仅支持数字、字母和特殊字符，后续用户可自行更改密码</span>
      </el-form-item>
      <el-form-item label="角色" :label-width="formLabelWidth" prop="roleId">
        <el-select v-model="formData.roleId" placeholder="请选择" class="diaSWidth">
          <el-option
            v-for="item in allRoles"
            :key="item.roleId"
            :label="item.roleName"
            :value="item.roleId"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="备注" :label-width="formLabelWidth" prop="description">
        <el-input
          v-model="formData.description"
          autocomplete="off"
          type="textarea"
          maxlength="200"
          show-word-limit
          :autosize="{ minRows: 5, maxRows: 8}"
        />
      </el-form-item>
      <el-form-item>
        <div class="footerBtn">
          <el-button @click="cancellationBtn">取 消</el-button>
          <el-button type="primary" :loading="submitLoading" @click="submitForm('formData')">提 交</el-button>
        </div>
      </el-form-item>
    </el-form>
  </el-dialog>
</template>
<script>
import { apiQueryAllRoles , apiQueryAccountInfoByUserId , apiSaveAccount , apiUpdateAccount } from '@/api/userManagement';
export default {
  name: 'AccountDialog',
  props: {
    accountDialogType: {
      type: String,
      default: 'add',
    },
    userId: {
      type: Number,
      default: 0,
    },
  },
  data() {
    return {
      formLabelWidth: '110px',
      formData: {
        realName: '',
        userName: '',
        password: '',
        roleId: '',
        description:''
      },
      loadingDialog: false,
      submitLoading: false,
      allRoles:[],
      rules: {
        realName: [
          { required: true, message: '用户真实姓名不能为空', trigger: 'blur' },
        ],
        userName: [
          { required: true, message: '账号（手机号）不能为空', trigger: 'blur' },
          { pattern: /^[0-9\\-]*$/, message: '该手机号用于登录，仅支持数字',trigger: 'blur' },
          {
            min: 11,
            max: 11,
            pattern: /^[\d|\w]{11,11}$/,
            message: '请填写11位的手机号',
            trigger: 'blur' 
          }
        ],
        password: [
          { required: true, message: '初始登录密码不能为空', trigger: 'blur' },
        ],
        roleId: [
          { required: true, message: '角色不能为空', trigger: 'blur' },
        ],
      },
    };
  },
  created() {
    this.queryAllRoles();
  },
  mounted() {
    if(this.accountDialogType !== 'add'){
      this.queryAccountInfoByUserId();
    }
  },
  methods: {
    // 查询账号详情
    queryAccountInfoByUserId(){
      const that = this;
      apiQueryAccountInfoByUserId({userId:this.userId}).then((res) => {
        if (res.code == 0) {
          this.formData.realName = res.data.realName;
          this.formData.userName = res.data.userName;
          this.formData.password = res.data.password;
          this.formData.roleId = res.data.roleId;
          this.formData.description = res.data.description;
        }else {
           that.$message({
              message: res.message,
              type: 'error'
            })
          }
      }) .catch(() => {});
    },
     // 查询所有角色
    queryAllRoles(){
      const that = this;
      apiQueryAllRoles().then((res) => {
        if (res.code == 0) {
          that.allRoles = res.data||[];
        }else {
           that.$message({
              message: res.message,
              type: 'error'
            })
          }
      }) .catch(() => {});
    },
    handleExpand() {
      this.$nextTick(() => {
        this.changeCss();
      });
    },
    renderContent(h, { node }) {
      // 树节点的内容区的渲染 Function
      let classname = '';
      // 由于项目中有三级菜单也有四级级菜单，就要在此做出判断
      if (node.level === 4) {
        classname = 'foo';
      }
      if (node.level === 3 && node.childNodes.length === 0) {
        classname = 'foo';
      }
      return h('p', { class: classname }, node.label);
    },
    changeCss() {
      const levelName = document.getElementsByClassName('foo'); // levelname是上面的最底层节点的名字
      for (let i = 0; i < levelName.length; i++) {
        // cssFloat 兼容 ie6-8  styleFloat 兼容ie9及标准浏览器
        levelName[i].parentNode.style.cssFloat = 'left'; // 最底层的节点，包括多选框和名字都让他左浮动
        levelName[i].parentNode.style.styleFloat = 'left';
        levelName[i].parentNode.onmouseover = function () {
          this.style.backgroundColor = '#fff';
        };
      }
    },
    cancellationBtn() {
      this.$emit('cancellationBtn');
    },
    // 提交
    submitForm(formData) {
      const that = this;
      that.$refs[formData].validate((valid) => {
        if (valid) {
          if(!that.formData.roleId){
            that.$message.success('请选择角色');
            return;
          }
          that.submitLoading = true;
          let param = {
            ...that.formData
          }
          if(that.accountDialogType === 'add'){
            apiSaveAccount(param).then((res) => {
              that.submitLoading = false;
              if (res.code === 0) {
                that.$message.success('新增成功');
                that.$emit('refreshList');
              } else {
                that.$message.error(res.message);
              }
            });
          }else{
            param.userId = that.userId;
            apiUpdateAccount(param).then((res) => {
              that.submitLoading = false;
              if (res.code === 0) {
                that.$message.success('编辑成功');
                that.$emit('refreshList');
              } else {
                that.$message.error(res.message);
              }
            });
          }
          return true;
        } else {
          return false;
        }
      });
    },
  },
};
</script>
<style lang="scss" scoped>
.accountDetail {
  padding: 0 20px;
  .areaBox {
    max-height: 200px;
    min-height: 100px;
    overflow-y: auto;
    border: 1px solid rgba(208, 208, 208, 1);
    margin-top: 10px;
  }
  .diaSWidth {
    width: 320px;
    margin-right: 10px;
  }
  .item-info {
    color: #ff2400;
  }
  .Fradios {
    margin-left: 40px;
  }
  .footerBtn {
    padding: 20px;
    padding-top: 10px;
    text-align: right;
    box-sizing: border-box;
    padding-bottom: 0;
  }
}

::v-deep  .el-divider--horizontal {
  margin: 0px;
}
::v-deep  .el-form-item__error {
  margin-left: 40px;
}
</style>
