<template>
  <div class="settlement-box">
    <!--待商业付款、待平台审核、审核未通过且未逾期 || 待商业付款、待平台审核、审核未通过且已逾期-->
    <div
      v-if="isHandle"
      class="commission-state"
    >
      {{ handleTip }}<span
        class="activeTo"
        @click="gotoCommission"
      >去处理></span>
    </div>
    <div
      v-if="tipOpeningAccount"
      class="tip-opening-account"
    >
      为保证平台业务合规性，提升商户资金安全性，请您在<span style="color:#ff2121;text-decoration:underline;">{{deadlineDatetime}}</span> 前尽快完成“企业开户”。逾期未完成企业开户，平台将限制您的余额提现功能，企业开户成功后将恢复提现功能。详细开户流程可参考首页的企业开户操作说明。<span
        class="activeTo"
        @click="toCompanyOpenAccount"
      >去企业开户></span>
    </div>
    <el-row
      :gutter="20"
      class="price-box"
    >
      <el-col
        :span="8"
      >
        可提现金额(元)
        <span>{{
          info.canCashAdvanceAmount || info.canCashAdvanceAmount === 0
            ? info.canCashAdvanceAmount.toFixed(2)
            : ''
        }}</span>
      </el-col>
      <el-col
        :span="8"
      >
        提现中金额（元）
        <span>{{
          info.amountInCashAdvance || info.amountInCashAdvance === 0
            ? info.amountInCashAdvance.toFixed(2)
            : ''
        }}</span>
      </el-col>
      <el-col
        :span="8"
      >
        已提现金额（元）
        <span>{{
          info.alreadyCashAdvanceAmount || info.alreadyCashAdvanceAmount === 0
            ? info.alreadyCashAdvanceAmount.toFixed(2)
            : ''
        }}</span>
      </el-col>
    </el-row>
    <div class="btn-box">
      <el-button
        v-permission="['settle_online_withdraw']"
        type="primary"
        @click="toApplyWithdraw(true)"
      >
        申请提现
      </el-button>
      <el-button
        plain
        @click="toWithdraw"
      >
        查看提现明细
      </el-button>
    </div>
    <p>
      结算规则：<br>
      1、订单完成时间+3个自然日可进行结算，退款单退款成功时间+3个自然日可进行结算。例如订单完成时间：2021-01-01，则订单会在1月4日凌晨结算并记入1月4日的账单<br>
      2、若账单的“应结算金额”>=0则将账单状态记为“已入账”，若账单的“应结算金额”<0则将账单状态记为“未入账”。未入账的账单会同后面生成的账单应结算金额求和，若求和金额为正数，则所有账单变为“已入账”<br>
      3、账单入账时刻将执行分润操作，“已入账”且“分润成功”的账单应结算金额将记入“可提现金额”中。若账单分润失败，请及时联系平台运营处理
    </p>
    <el-tabs
      v-model="activeTab"
      type="card"
      @tab-click="changeTab"
    >
      <el-tab-pane
        label="账单列表"
        name="bill"
      >
        <template v-if="activeTab === 'bill'">
          <div class="searchMy">
            <el-form
              ref="listQuery"
              :model="listQuery"
              :inline="true"
              size="small"
            >
              <el-form-item
                prop="billNo"
              >
                <el-input
                  v-model="listQuery.billNo"
                  placeholder="请输入"
                >
                  <template slot="prepend">
                    账单号
                  </template>
                </el-input>
              </el-form-item>
              <el-form-item
                prop="createdTime"
              >
                <span
                    class="search-title"
                >生成时间</span>
                <div style="display: table-cell; line-height: 24px">
                <el-date-picker
                  v-model="listQuery.createdTime"
                  type="datetimerange"
                  start-placeholder="开始时间"
                  end-placeholder="结束时间"
                  :default-time="['00:00:00', '23:59:59']"
                />
                </div>
              </el-form-item>
              <el-form-item
                prop="settlementType"
              >
                <span
                    class="search-title"
                >佣金结算方式</span>
                <el-select v-model="listQuery.settlementType">
                  <el-option
                    label="全部"
                    :value="0"
                  />
                  <el-option
                    label="月结"
                    :value="2"
                  />
                  <el-option
                    label="非月结"
                    :value="1"
                  />
                </el-select>
              </el-form-item>
              <el-form-item
                prop="billPaymentStatus"
              >
                <span
                    class="search-title"
                >账单状态</span>
                <el-select v-model="listQuery.billPaymentStatus">
                  <el-option
                    label="未入账"
                    :value="0"
                  />
                  <el-option
                    label="已入账"
                    :value="1"
                  />
                </el-select>
              </el-form-item>
              <el-form-item
                prop="recordedTime"
              >
                <span
                    class="search-title"
                >入账时间</span>
                <div style="display: table-cell; line-height: 24px">
                <el-date-picker
                  v-model="listQuery.recordedTime"
                  type="datetimerange"
                  start-placeholder="开始时间"
                  end-placeholder="结束时间"
                  :default-time="['00:00:00', '23:59:59']"
                />
                </div>
              </el-form-item>
              <el-form-item
                prop="billShareStatus"
              >
                <span
                  class="search-title"
                >分润状态</span>
                <el-select v-model="listQuery.billShareStatus" placeholder="全部">
                  <el-option
                    v-for="item in shareStatusOptions"
                    :key="item.code"
                    :label="item.name"
                    :value="item.code"
                  />
                </el-select>
              </el-form-item>
              <el-form-item prop="businessNo">
                <el-input v-model="listQuery.businessNo" placeholder="请输入订单号或退款单号">
                  <template slot="prepend">
                    单据号
                  </template>
                </el-input>
              </el-form-item>
              <el-form-item class="btn-item" style="text-align: right;padding-right: 20px">
                <el-button @click="reset">
                  重置
                </el-button>
                <el-button
                  type="primary"
                  @click="getList(listQuery, true)"
                >
                  查询
                </el-button>
              </el-form-item>
            </el-form>
          </div>
          <el-row
            :gutter="20"
            class="price-box mb15"
          >
            <el-col :span="24">
              <div class="btn-item">
                <el-button
                  v-permission="['settle_online_exportBill']"
                  plain
                  @click="exportList"
                >
                  导出账单
                </el-button>
                <el-button
                  v-permission="['settle_online_exportBillDetail']"
                  plain
                  @click="exportDetail"
                >
                  导出账单明细
                </el-button>
              </div>
            </el-col>
          </el-row>
          <el-row :gutter="20" class="price-box mb15">
            <el-col :span="6">
              <el-tooltip
                class="item"
                content="当前查询结果所有单据，应结算金额求和"
                placement="left"
              >
                <i class="el-icon-warning-outline" />
              </el-tooltip>
              应结算金额合计(元)
              <span>{{
                  statementTotalMoneyTotal || statementTotalMoneyTotal === 0
                    ? statementTotalMoneyTotal
                    : ''
                }}</span>
            </el-col>

            <el-col :span="6">
              <el-tooltip
                class="item"
                content="当前查询结果所有单据，应缴纳佣金求和"
                placement="left"
              >
                <i class="el-icon-warning-outline" />
              </el-tooltip>
              应缴纳佣金合计(元)
              <span>{{ deductedCommissionTotal }}</span>
            </el-col>

            <el-col :span="6">
              <el-tooltip
                class="item"
                content="当前查询结果所有单据，实际需缴纳佣金求和"
                placement="left"
              >
                <i class="el-icon-warning-outline" />
              </el-tooltip>
              实际需缴纳佣金合计(元)
              <span>{{
                  actualCommissionMoneyTotal || actualCommissionMoneyTotal === 0
                    ? actualCommissionMoneyTotal
                    : ''
                }}</span>
            </el-col>
            <el-col :span="6">
              <el-tooltip
                class="item"
                content="当前查询结果所有单据，佣金金额求和"
                placement="left"
              >
                <i class="el-icon-warning-outline" />
              </el-tooltip>
              佣金金额合计（元）
              <span>{{
                  hireMoneyTotal || hireMoneyTotal === 0 ? hireMoneyTotal : ''
                }}</span>
            </el-col>
            <el-col :span="6">
              <el-tooltip
                class="item"
                content="当前查询结果所有单据，佣金优惠求和"
                placement="left"
              >
                <i class="el-icon-warning-outline" />
              </el-tooltip>
              佣金优惠合计（元）
              <span>{{
                  commissionDiscountMoneyTotal || commissionDiscountMoneyTotal === 0 ? commissionDiscountMoneyTotal : ''
                }}</span>
            </el-col>
          </el-row>
          <xyy-table
            :data="list"
            :list-query="listQuery"
            :col="col"
            :operation="operation"
            @get-data="getList"
            @operation-click="operationClick"
          />
        </template>
      </el-tab-pane>
      <el-tab-pane
        label="结算单信息"
        name="settlement"
      >
        <template v-if="activeTab === 'settlement'">
          <settle-list
            ref="settleList"
            :type="1"
          />
        </template>
      </el-tab-pane>
    </el-tabs>
    <el-dialog
      :visible.sync="withdrawStatus"
      custom-class="withdraw-dialog"
      :close-on-click-modal="false"
      title="申请提现"
    >
      <div class="withdrawAmount">
        提现金额<span style="color: #ff2121;margin-left: 20px;font-size: 16px">{{ withdraw.applyAmount }}元</span>
      </div>
      <div style="color: #ff2121;font-size: 12px;padding: 0;margin-bottom: 5px">
        *请填写企业收款账号及开户行
      </div>
      <el-form
        ref="withdraw"
        :model="withdraw"
        :rules="rules"
        label-width="80px"
        label-position="top"
      >
        <el-form-item
          label="账户名称："
          prop="accountName"
        >
          <el-input
            v-model.trim="withdraw.accountName"
            disabled
          />
        </el-form-item>
        <el-form-item
          label="账户号："
          prop="accountNum"
        >
          <el-input
            v-model="withdraw.accountNum"
            placeholder="请输入企业收款账号"
            maxlength="30"
          />
        </el-form-item>
        <el-form-item
          label="开户行："
          prop="accountBank"
        >
          <el-input
            v-model.trim="withdraw.accountBank"
            placeholder="请输入开户行及支行名称"
            maxlength="50"
          />
        </el-form-item>
      </el-form>
      <div>
        <div>温馨提示：</div>
        <div>
          1、满{{ withdraw.lowestAmount }}元可发起提现，单笔提现金额需小于{{
            (withdraw.highestAmount / 10000).toFixed(0)
          }}万；
        </div>
        <div>2、每个自然日限提现{{ withdraw.number }}次；</div>
        <div> 3、周一至周五，当天提现，下一个工作日到账，节假日顺延；</div>
      </div>
      <div slot="footer">
        <el-button @click="withdrawStatus = false">
          取消
        </el-button>
        <el-button
          type="primary"
          :disabled="withdrawDisabled"
          @click="applyWithdraw"
        >
          确定
        </el-button>
      </div>
    </el-dialog>
    <!--待商业付款、待平台审核、审核未通过且已逾期的佣金缴纳记录-->
    <el-dialog
      :visible.sync="deadlineTip"
      custom-class="deadlineTip-dialog"
      top="0"
      title="提示"
    >
      <p>
        您有已逾期的佣金缴纳记录，系统已限制您的申请提现功能。佣金缴纳记录处理完成后系统将自动恢复您的申请提现功能，请及时处理
      </p>
      <div slot="footer">
        <el-button @click="deadlineTip = false">
          取消
        </el-button>
        <el-button
          type="primary"
          @click="gotoCommission"
        >
          去处理
        </el-button>
      </div>
    </el-dialog>
    <el-dialog
      title="企业开户提醒通知"
      :visible.sync="tipOpeningAccountDialogVisible"
      :before-close="handleClose"
      width="40%"
    >
      <div >
        为保证平台业务合规性，提升商户资金安全性，请您在
        <span style="color:#ff2121;">{{deadlineDatetime}}</span> 前尽快完成“企业开户”。逾期未完成企业开户，平台将限制您的余额提现功能，企业开户成功后将恢复提现功能。
        <br />详细开户流程可参考首页的<span style="font-weight: bold;">企业开户操作说明</span>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button v-if="isShowWithdrawal" size="small" @click="toApplyWithdraw(false)">继续提现</el-button>
        <el-button type="primary" size="small" @click="toCompanyOpenAccount">去企业开户</el-button>
      </span>
    </el-dialog>
    <export-tip
      :change-export="changeExport"
      @handleExoprClose="handleExoprClose"
      @handleChangeExport="handleChangeExport"
    />
    <ApplyWithdrawalDialog v-if="applyWithdrawalDialogVisible" :applyWithdrawalDialogVisible.sync="applyWithdrawalDialogVisible" :info="fuMinInfo"/>
  </div>
</template>

<script>
import settleList from './components/settleList'
import exportTip from '@/views/other/components/exportTip';
import ApplyWithdrawalDialog from './components/applyWithdrawalDialog'
import {
  getBillDatas,
  getBillDataNums,
  getBillDetailNums,
  getBillInfo,
  getPriceInfo,
  getWithdrawTimes,
  getAccountInfo,
  sendWithdraw,
  getSellerStatus,
  exportBillPaymemtList,
  exportBillPaymemtDetailList,
  getShareStatusOptions,
  apiQueryOpenAccountTips,
} from '../../api/settlement/online';

export default {
  components: {
    settleList,
    exportTip,
    ApplyWithdrawalDialog,
  },
  data() {
    return {
      activeTab: 'bill',
      isHandle: false,
      handleTip: '',
      isOverdue: '', // 逾期标志，1：逾期  2：不逾期
      listQuery: {
        pageSize: 10,
        page: 1,
        total: 0,
        billNo: '',
        createdTime: [],
        billPaymentStatus: '',
        settlementType: 0,
        recordedTime: [],
        billShareStatus: '',
        businessNo: '',
      },
      info: {
        canCashAdvanceAmount: '', // 可提现金额
        amountInCashAdvance: '', // 提现中金额
        alreadyCashAdvanceAmount: '', // 已提现金额
      },
      withdraw: {
        accountName: '',
        accountNum: '',
        accountBank: '',
        applyAmount: '',
        highestAmount: '',
        lowestAmount: '',
        number: '',
      },
      rules: {
        // accountName: [
        //   {
        //     required: true,
        //     message: '请输入账户名',
        //     trigger: ['blur', 'change']
        //   }
        // ],
        accountNum: [
          {
            required: true,
            message: '请输入企业收款账号',
            trigger: ['blur', 'change'],
          },
          { validator: this.validateNum, trigger: ['change', 'blur'] },
        ],
        accountBank: [
          {
            required: true,
            message: '请输入开户行及支行名称',
            trigger: ['blur', 'change'],
          },
        ],
        applyAmount: [
          {
            required: true,
            message: '请输入提现金额',
            trigger: ['blur', 'change'],
          },
          {
            validator: this.validateApplyAmount,
            trigger: ['change', 'blur'],
          },
        ],
      },
      withdrawStatus: false, // 提现弹框
      deadlineTip: false, // 逾期提示弹框
      withdrawDisabled: false, // 是否可提现
      list: [],
      col: [
        {
          index: 'billNo',
          name: '账单号',
          width: 200,
        },
        {
          index: 'settlementType',
          name: '佣金结算方式',
          width: 150,
          formatter: (row, col, cell) => {
            if (cell === 1) {
              return '非月结';
            }
            if (cell === 2) {
              return '月结';
            }
            return '';
          },
        },
        {
          index: 'productMoney',
          name: '商品金额（元）',
          width: 150,
          formatter: (row, col, cell) => (cell || cell === 0 ? cell.toFixed(2) : ''),
        },
        {
          index: 'freightAmount',
          name: '运费金额（元）',
          width: 150,
          formatter: (row, col, cell) => (cell || cell === 0 ? cell.toFixed(2) : ''),
        },
        {
          index: 'totalMoney',
          name: '单据总额含运费（元）',
          width: 200,
          formatter: (row, col, cell) => (cell || cell === 0 ? cell.toFixed(2) : ''),
        },
        {
          index: 'shopTotalDiscount',
          name: '店铺总优惠（元）',
          width: 150,
          formatter: (row, col, cell) => (cell || cell === 0 ? cell.toFixed(2) : ''),
        },
        {
          index: 'platformTotalDiscount',
          name: '平台总优惠（元）',
          width: 150,
          formatter: (row, col, cell) => (cell || cell === 0 ? cell.toFixed(2) : ''),
        },
        {
          index: 'money',
          name: '实付金额（元）',
          width: 150,
          formatter: (row, col, cell) => (cell || cell === 0 ? cell.toFixed(2) : ''),
        },
        {
          index: 'hireMoney',
          name: '佣金金额（元）',
          width: 150,
          formatter: (row, col, cell) => (cell || cell === 0 ? cell.toFixed(2) : ''),
          renderHeader: (h, { column }) => {
            return h(
              'div', [
                h('span', column.label),
                h('el-tooltip', {
                  props: {
                    content: '账单的佣金金额=账单中所有单据的佣金金额求和',
                    placement: 'right',
                  },
                }, [
                  h('i', { class: 'el-icon-warning-outline' }),
                ]),
              ],
            );
          },
        },
        {
          index: 'statementTotalMoney',
          name: '应结算金额（元）',
          width: 150,
          formatter: (row, col, cell) => (cell || cell === 0 ? cell.toFixed(2) : ''),
          renderHeader: (h, { column }) => {
            return h(
              'div', [
                h('span', column.label),
                h('el-tooltip', {
                  props: {
                    content: '账单的应结算金额=账单中所有单据的应结算金额求和',
                    placement: 'right',
                  },
                }, [
                  h('i', { class: 'el-icon-warning-outline' }),
                ]),
              ],
            );
          },
        },
        {
          index: 'deductedCommission',
          name: '应缴纳佣金（元）',
          width: 200,
          renderHeader: (h, { column }) => {
            return h(
              'div', [
                h('span', column.label),
                h('el-tooltip', {
                  props: {
                    content: '账单的应缴纳佣金=账单中所有单据的应缴纳佣金求和。单据平台补贴金额冲抵佣金金额后，对应账单商业应给平台缴纳的佣金金额',
                    placement: 'right',
                  },
                }, [
                  h('i', { class: 'el-icon-warning-outline' }),
                ]),
              ],
            );
          },
        },
        {
          index: 'actualCommissionMoney',
          name: '实际需缴纳佣金',
          width: 200,
          renderHeader: (h, { column }) => {
            return h(
              'div', [
                h('span', column.label),
                h('el-tooltip', {
                  props: {
                    content: '账单的实际需缴纳佣金=账单中所有单据的实际需缴纳佣金求和。单据享受佣金折扣政策优惠后，对应账单商业实际需给平台缴纳的佣金金额',
                    placement: 'right',
                  },
                }, [
                  h('i', { class: 'el-icon-warning-outline' }),
                ]),
              ],
            );
          },
        },
        {
          index: 'commissionDiscountMoney',
          name: '佣金优惠',
          width: 200,
          renderHeader: (h, { column }) => {
            return h(
              'div', [
                h('span', column.label),
                h('el-tooltip', {
                  props: {
                    content: '账单的佣金优惠=账单中所有单据的佣金优惠求和。单据因享受佣金折扣政策而产生的佣金优惠',
                    placement: 'right',
                  },
                }, [
                  h('i', { class: 'el-icon-warning-outline' }),
                ]),
              ],
            );
          },
        },
        {
          index: 'billPaymentStatus',
          name: '账单状态',
          formatter: (row, col, cell) => (cell === 1 ? '已入账' : '未入账'),
        },
        {
          index: 'billShareStatus',
          name: '分润状态',
          width: 200,
          formatter: (row, col, cell) => {
            return this.filterShareStatusName(cell);
          },
        },
        {
          index: 'billCreateTime',
          name: '生成时间',
          width: 200,
          formatter: (row, col, cell) => (cell ? new Date(cell + 8 * 3600 * 1000).toJSON().substr(0, 19).replace('T', ' ') : ''),
        },
        {
          index: 'billPaymentTime',
          name: '入账时间',
          width: 200,
          formatter: (row, col, cell) => (cell ? new Date(cell + 8 * 3600 * 1000).toJSON().substr(0, 19).replace('T', ' ') : ''),
        },
        {
          index: 'operation',
          name: '操作',
          operation: true,
          width: 150,
        },
      ],
      operation: [
        {
          name: '查看明细',
          type: 0,
        },
      ],
      hireMoneyTotal: '', // 佣金金额
      statementTotalMoneyTotal: '', // 应结算金额
      deductedCommissionTotal: '', // 应缴纳佣金合计
      actualCommissionMoneyTotal: '',//实际需缴纳佣金合计
      commissionDiscountMoneyTotal: '',//佣金优惠合计
      changeExport: false,
      applyWithdrawalDialogVisible: false,
      fuMinInfo: {},
      shareStatusOptions:[],
      deadlineDatetime:'',
      tipOpeningAccount:false,
      tipOpeningAccountDialogVisible:false,
      isShowWithdrawal:false
    };
  },
  created() {
    this.queryShareStatusOptions();
    this.changeTab({ name: 'bill' });
    this.getSellerStatus();
    this.getPriceInfo();
    this.queryOpenAccountTips();
  },
  methods: {
    async queryShareStatusOptions() {
      const res = await getShareStatusOptions()
      if (res && res.code === 0) {
        this.shareStatusOptions = res.data
      } else {
        this.$message.error(res.message || '获取分润状态失败')
      }
    },
    filterShareStatusName(code) {
      const [obj] = this.shareStatusOptions.filter(item => Number(item.code) === Number(code))
      if (obj) {
        return obj.name
      }
      return ''
    },
    // 获取商家状态
    getSellerStatus() {
      getSellerStatus()
        .then((res) => {
          if (res.code === '200') {
            if (res.data.showFlag) {
              this.isHandle = true;
              this.handleTip = res.data.content;
              this.isOverdue = res.data.overdue;
            }
          } else {
            this.$message.error({ message: res.errorMsg || res.msg, customClass: 'center-msg' });
          }
        })
        .catch(() => {});
    },
     // 获取商家状态
    queryOpenAccountTips() {
      const that = this;
      apiQueryOpenAccountTips()
        .then((res) => {
          if (res.code === 0) {
            if (res.result.showFlag) {
              const {showFlag, deadlineDatetime, currentDateTime} = res.result;
              that.tipOpeningAccount = showFlag;
              that.deadlineDatetime = deadlineDatetime;
              // 如果showFlag是true的话，deadlineDatetime有值，比较当前时间currentDateTime<deadlineDatetime，显示继续提现
              if(showFlag && deadlineDatetime && currentDateTime){
                that.isShowWithdrawal = new Date().getTime(currentDateTime) < new Date(deadlineDatetime).getTime();
              }
            }
          } else {
            this.$message.error({ message: res.errorMsg || res.msg, customClass: 'center-msg' });
          }
        })
        .catch(() => {});
    },
    validateNum(rules, val, cb) {
      if (!/^\d+$/.test(val)) {
        cb('请输入数字');
      } else {
        cb();
      }
    },
    validateApplyAmount(rules, val, cb) {
      const reg = /^(([1-9]{1}\d*)|([0]{1}))(\.(\d){0,2})?$/;
      if (!reg.test(val)) {
        cb('请输入数字，小数点后可保留两位');
      } else if (
        (this.withdraw.lowestAmount || this.withdraw.lowestAmount === 0)
        && val < this.withdraw.lowestAmount
      ) {
        cb('不能低于最小提现金额');
      } else if (
        (this.info.canCashAdvanceAmount || this.info.canCashAdvanceAmount === 0)
        && val > this.info.canCashAdvanceAmount
      ) {
        cb('提现金额不能大于可提现金额');
      } else if (
        (this.withdraw.highestAmount || this.withdraw.highestAmount === 0)
        && val > this.withdraw.highestAmount
      ) {
        cb('不能超过单笔最大提现金额');
      } else {
        cb();
      }
    },
    getList(listQuery, reset) {
      const loading = this.$loading({
        lock: true,
        text: 'Loading',
        spinner: 'el-icon-loading',
        background: 'rgba(255,255,255, 0.8)',
      });
      const { page, pageSize } = listQuery;
      const {
        billNo,
        billPaymentStatus,
        createdTime,
        recordedTime,
        settlementType,
        billShareStatus,
        businessNo
      } = this.listQuery;
      this.getBillPrice();
      getBillDatas({
        pageNum: reset ? 1 : page,
        pageSize,
        billNo,
        billPaymentStatus,
        settlementType,
        billShareStatus,
        startCreateTime:
          createdTime && createdTime.length ? this.formatDate(createdTime[0].getTime()) : '',
        endCreateTime:
          createdTime && createdTime.length ? this.formatDate(createdTime[1].getTime()) : '',
        startBillPaymentTime:
          recordedTime && recordedTime.length ? this.formatDate(recordedTime[0].getTime()) : '',
        endBillPaymentTime:
          recordedTime && recordedTime.length ? this.formatDate(recordedTime[1].getTime()) : '',
        businessNo
      })
        .then((res) => {
          loading.close();
          if (res.code === '200') {
            const { list, total, pageNum } = res.data;
            this.list = list;
            this.listQuery = {
              ...this.listQuery,
              total,
              page: pageNum,
            };
          } else {
            this.$message.error({ message: res.errorMsg || res.msg, customClass: 'center-msg' });
          }
        })
        .catch(() => {
          loading.close();
        });
    },
    /**
     * 导出账单数据
     */
    exportList() {
      const {
        billNo,
        billPaymentStatus,
        createdTime,
        recordedTime,
        settlementType,
        billShareStatus,
        businessNo
      } = this.listQuery;
      const startCreateTime = createdTime && createdTime.length ? this.formatDate(createdTime[0].getTime()) : '';
      const endCreateTime = createdTime && createdTime.length ? this.formatDate(createdTime[1].getTime()) : '';
      const startBillPaymentTime = recordedTime && recordedTime.length ? this.formatDate(recordedTime[0].getTime()) : '';
      const endBillPaymentTime = recordedTime && recordedTime.length ? this.formatDate(recordedTime[1].getTime()) : '';
      const params = {
        billNo,
        billPaymentStatus,
        startCreateTime,
        endCreateTime,
        startBillPaymentTime,
        endBillPaymentTime,
        settlementType,
        billShareStatus,
        businessNo
      };
      getBillDataNums(params)
        .then((res) => {
          if (res.code === '200') {
            if (res.data === 0) {
              this.$message.error({ message: '暂无账单数据', customClass: 'center-msg' });
            } else if (res.data > 10000) {
              this.$message.error({
                message: `导出上限为10000条，当前搜索结果导出数据为${res.data}条，超出导出上限`,
                customClass: 'center-msg',
              });
            } else {
              this.exportData()
            }
          } else {
            this.$message.error({ message: res.errorMsg || res.msg, customClass: 'center-msg' });
          }
        })
        .catch(() => {});
    },
    /**
     * 导出账单明细
     */
    exportDetail() {
      const {
        billNo,
        billPaymentStatus,
        createdTime,
        recordedTime,
        settlementType,
        billShareStatus,
        businessNo
      } = this.listQuery;
      const startCreateTime = createdTime && createdTime.length ? this.formatDate(createdTime[0].getTime()) : '';
      const endCreateTime = createdTime && createdTime.length ? this.formatDate(createdTime[1].getTime()) : '';
      const startBillPaymentTime = recordedTime && recordedTime.length ? this.formatDate(recordedTime[0].getTime()) : '';
      const endBillPaymentTime = recordedTime && recordedTime.length ? this.formatDate(recordedTime[1].getTime()) : '';
      const params = {
        billNo,
        billPaymentStatus,
        startCreateTime,
        endCreateTime,
        startBillPaymentTime,
        endBillPaymentTime,
        settlementType,
        billShareStatus,
        businessNo
      };
      getBillDetailNums(params)
        .then((res) => {
          if (res.code === '200') {
            if (res.data === 0) {
              this.$message.error({
                message: '暂无账单明细数据',
                customClass: 'center-msg',
              });
            } else if (res.data > 10000) {
              this.$message.error({
                message: `导出上限为10000条，当前搜索结果导出数据为${res.data}条，超出导出上限`,
                customClass: 'center-msg',
              });
            } else {
              this.exportData('detail')
            }
          } else {
            this.$message.error({ message: res.errorMsg || res.msg, customClass: 'center-msg' });
          }
        })
        .catch(() => {});
    },
    /**
     * 导出数据
     */
    exportData(link) {
      const {
        billNo,
        billPaymentStatus,
        createdTime,
        recordedTime,
        settlementType,
        billShareStatus,
        businessNo,
      } = this.listQuery;
      const startCreateTime = createdTime && createdTime.length ? this.formatDate(createdTime[0].getTime()) : '';
      const endCreateTime = createdTime && createdTime.length ? this.formatDate(createdTime[1].getTime()) : '';
      const startBillPaymentTime = recordedTime && recordedTime.length ? this.formatDate(recordedTime[0].getTime()) : '';
      const endBillPaymentTime = recordedTime && recordedTime.length ? this.formatDate(recordedTime[1].getTime()) : '';
      const params = {
        billNo,
        billPaymentStatus,
        startCreateTime,
        endCreateTime,
        startBillPaymentTime,
        endBillPaymentTime,
        settlementType,
        billShareStatus,
        businessNo
      }
      // const url = `${process.env.VUE_APP_BASE_API}${link}${this.getParams(params)}`
      // const a = document.createElement('a')
      // a.href = url
      // a.click()
      if (link == 'detail') {
        exportBillPaymemtDetailList(params).then((res) => {
          if (res.code !== 0) {
            this.$message.error(res.message);
            return;
          }
          this.changeExport = true;
        });
      } else {
        exportBillPaymemtList(params).then((res) => {
          if (res.code !== 0) {
            this.$message.error(res.message);
            return;
          }
          this.changeExport = true;
        });
      }
    },
    handleChangeExport(info) {
      this.changeExport = false;
      if (info === 'go') {
        const path = '/downloadList'
        window.openTab(path)
        //that.$router.push({ path: '/downloadList' });
      }
    },
    handleExoprClose() {
      this.changeExport = false;
    },
    /**
     * 获取get请求参数
     */
    getParams(params) {
      let queryStr = '?';
      Object.keys(params).forEach((key) => {
        queryStr += `${key}=${params[key]}&`;
      });
      queryStr = queryStr.substr(0, queryStr.length - 1);
      return queryStr;
    },
    /**
     * 重置数据
     */
    reset() {
      this.$refs.listQuery.resetFields();
      this.initCreatedDate();
      this.listQuery.recordedTime = [];
      this.getList(this.listQuery, true);
    },
    /**
     * 去处理-跳转佣金缴纳记录页面
     */
    gotoCommission() {
      this.$router.push('/commissionRecord');
    },
    /**
     * 操作选项
     */
    operationClick(type, row) {
      if (type === 0) {
        // 查看明细
        this.$router.push(`/settlementOnlineDetail?billNo=${row.billNo}`);
      }
    },
    /**
     * 获取入账单价格
     */
    getBillPrice() {
      const {
        billNo,
        billPaymentStatus,
        createdTime,
        recordedTime,
        settlementType,
        billShareStatus,
        businessNo
      } = this.listQuery;
      const startCreateTime = createdTime && createdTime.length ? this.formatDate(createdTime[0].getTime()) : '';
      const endCreateTime = createdTime && createdTime.length ? this.formatDate(createdTime[1].getTime()) : '';
      const startBillPaymentTime = recordedTime && recordedTime.length ? this.formatDate(recordedTime[0].getTime()) : '';
      const endBillPaymentTime = recordedTime && recordedTime.length ? this.formatDate(recordedTime[1].getTime()) : '';
      const params = {
        billNo,
        billPaymentStatus,
        startCreateTime,
        endCreateTime,
        startBillPaymentTime,
        endBillPaymentTime,
        settlementType,
        billShareStatus,
        businessNo
      };
      getBillInfo(params).then((res) => {
        if (res.code === '200') {
          this.hireMoneyTotal = res.data.hireMoneyTotal;
          this.statementTotalMoneyTotal = res.data.statementTotalMoneyTotal;
          this.deductedCommissionTotal = res.data.deductedCommissionTotal;
          this.actualCommissionMoneyTotal = res.data.actualCommissionMoneyTotal;
          this.commissionDiscountMoneyTotal = res.data.commissionDiscountMoneyTotal;
        } else {
          this.$message.error({ message: res.errorMsg || res.msg, customClass: 'center-msg' });
        }
      });
    },
    /**
     * 切换tab
     */
    changeTab(tab) {
      if (tab.name === 'bill') {
        this.$nextTick(() => {
          this.reset();
        });
      }
    },
    /**
     * 获取提现信息
     */
    getPriceInfo() {
      getPriceInfo()
        .then((res) => {
          if (res.code === '200') {
            Object.keys(this.info).forEach((key) => {
              this.info[key] = res.data[key];
            }, this);
          } else {
            this.$message.error({ message: res.errorMsg || res.msg, customClass: 'center-msg' });
          }
        })
        .catch(() => {});
    },
    /**
     * 跳转到提现明细
     */
    toWithdraw() {
      this.$router.push({ path: '/withdrawalRecord' });
      // window.parent.toWithdraw()
      // parent.$('#mainFrameTabs').bTabsAdd('private', '提现明细', '/accountStatement/accountStatementDetail')
    },
    /**
     * 去提现
     */
  async toApplyWithdraw(isTipOpeningAccount) {
      // 开户提示
      if (this.tipOpeningAccount && isTipOpeningAccount) {
        this.tipOpeningAccountDialogVisible = true;
        return;
      }
      // 待商业付款、待平台审核、审核未通过且已逾期的佣金缴纳记录
      if (this.isOverdue === 1) {
        this.tipOpeningAccountDialogVisible = false;
        this.deadlineTip = true;
        return;
      }
      const res = await getAccountInfo()
      if (res && Number(res.code) === 200) {
        if (Number(res.data.paymentChannel) === 1) {
          //-直连支付
          this.withdrawStatus = true;
          Object.keys(this.withdraw).forEach((key) => {
            if (res.data[key] || res.data[key] === 0) {
              this.withdraw[key] = res.data[key];
              // this.withdraw.applyAmount = ''
            }
          }, this);
          getWithdrawTimes()
            .then((_res) => {
              if (_res.code === '200') {
                if (_res.data >= this.withdraw.number) {
                  this.$message.warning({
                    message: `每天最多提现${this.withdraw.number}次`,
                    customClass: 'center-msg',
                  });
                  this.withdrawDisabled = true;
                } else {
                  this.withdrawDisabled = false;
                }
              } else {
                this.$message.error({
                  message: _res.errorMsg || _res.msg,
                  customClass: 'center-msg',
                });
              }
            })
            .catch(() => {
            });
        } else if (Number(res.data.paymentChannel) === 2) {
          this.applyWithdrawalDialogVisible = true
          this.fuMinInfo = res.data
        } else {
          this.$message.error('未查询到支付渠道')
        }
      } else {
        this.$message.error(res.msg || '查询账户信息失败')
      }
    },
    /**
     * 提现
     */
    applyWithdraw() {
      this.$refs.withdraw.validate((valid) => {
        if (valid) {
          this.applyWithdrawFn(this.withdraw)
        }
      });
    },

    applyWithdrawFn(data) {
      if (!data) return
      const {accountName, accountBank, accountNum, applyAmount} = data;
      const params = {accountName, accountBank, accountNum, applyAmount};
      sendWithdraw(params)
        .then((res) => {
          if (res.code === '200') {
            // 刷新提现金额
            this.getPriceInfo();
            // 刷新账单
            this.getList(this.listQuery, true);
            this.$nextTick(() => {
              // 刷新结算列表
              if (this.$refs.settleList) {
                this.$refs.settleList.reload();
              }
            });
            this.$message.success({message: '提现成功', customClass: 'center-msg'});
            this.withdrawStatus = false;
            this.applyWithdrawalDialogVisible = false
          } else {
            this.$message.error({message: res.errorMsg || res.msg, customClass: 'center-msg'});
          }
        })
        .catch(() => {
        });
    },
    /**
     * 格式化日期
     */
    formatDate(date) {
      return Number(date)
        ? new Date(date + 8 * 3600 * 1000).toJSON().substr(0, 19).replace('T', ' ')
        : '';
    },
    /**
     * 设置提现金额
     */
    setApplyAmount() {
      if (this.info.canCashAdvanceAmount && this.info.canCashAdvanceAmount > 0) {
        this.withdraw.applyAmount = this.info.canCashAdvanceAmount;
      }
    },
    /**
     * 初始化生成时间
     */
    initCreatedDate() {
      let year = new Date().getFullYear();
      let month = new Date().getMonth() - 2;
      if (month <= 0) {
        month += 12;
        year -= 1;
      }
      const start = new Date(`${year}-0${month}-01 00:00:00`);
      const end = new Date(
        `${
          new Date(new Date().getTime() + 8 * 3600 * 1000).toJSON().substr(0, 19).split('T')[0]
        } 23:59:59`,
      );
      this.listQuery.createdTime = [start, end];
    },
    handleClose(){
      this.tipOpeningAccountDialogVisible = false;
      // this.getFirstPageStatistics();
    },
    /**
     * 去处理-跳转企业开户页面
     */
    toCompanyOpenAccount() {
      this.handleClose();
      window.openTab('/companyOpenAccount');
    },
    // 当前时间
    getNowTimeDate() {
      const time = new Date(8 * 3600 * 1000 + +new Date())
        .toJSON()
        .substr(0, 10);
      return time;
    }
  },
};
</script>

<style lang="scss" scoped>
.settlement-box {
  padding: 15px;
  .commission-state {
    display: flex;
    align-items: center;
    padding: 0 20px;
    font-size: 14px;
    color: #ff2121;
    height: 50px;
    background: #fffbf1;
    .activeTo {
      color: #4183d5;
      cursor: pointer;
    }
  }
  .tip-opening-account{
    align-items: center;
    padding: 0 20px;
    font-size: 14px;
    color: #ff2121;
    height: 50px;
    background: #fffbf1;
    .activeTo {
      color: #4183d5;
      cursor: pointer;
    }
  }
  .price-box {
    font-size: 16px;
    font-family: PingFangSC, PingFangSC-Semibold;
    font-weight: 600;
    color: #303133;
    line-height: 40px;
    overflow: hidden;
    &.mb15 {
      margin-bottom: 15px;
    }
    span {
      font-size: 28px;
    }
    .el-button {
      padding: 0 12px;
      line-height: 30px;
      &.is-plain {
        color: #4183d5;
        border-color: #4183d5;
      }
    }
  }
  .btn-box {
    margin-top: 15px;
    .el-button {
      padding: 0 12px;
      line-height: 30px;
      &.is-plain {
        color: #4183d5;
        border-color: #4183d5;
      }
    }
  }
  > p {
    padding: 8px;
    background: #f9f9f9;
    border-radius: 2px;
    font-size: 14px;
    font-family: PingFangSC, PingFangSC-Regular;
    font-weight: 400;
    color: #666666;
    margin: 15px 0 25px;
  }

  .el-tabs {
    .el-tab-pane {
      padding: 0 20px;
      .el-form {
        overflow: hidden;
        &.settlement-form {
          ::v-deep  .el-form-item__content {
            .el-select {
              width: 246px;
            }
          }
        }
      }
      .btn-item {
        float: right;
      }
    }
  }
}
.deadlineTip-dialog {
  width: 418px;
  top: 50%;
  transform: translateY(-50%);
  .el-dialog__header {
    padding: 0 20px;
    line-height: 40px;
    .el-dialog__headerbtn {
      top: 13px;
    }
  }
  .el-dialog__body {
    padding: 10px 20px;
    line-height: 24px;
  }
  .el-button {
    padding: 0 20px;
    line-height: 28px;
    height: 30px;
  }
}
.el-dialog.withdraw-dialog {
  width: 600px;
  //top: 50%;
  //transform: translateY(-50%);
  .el-dialog__header {
    padding: 0 20px;
    line-height: 50px;
    .el-dialog__headerbtn {
      top: 17px;
    }
  }
  .el-dialog__body {
    box-sizing: border-box;
    padding: 10px 20px;
    //max-height: 400px;
    overflow-y: auto;

    .withdrawAmount{
      margin-bottom: 12px;
    }

    .el-form {
      overflow: hidden;
      padding-bottom: 22px;
      .el-form-item {
        margin-bottom: 12px;
        .el-form-item__label {
          height: 30px;
          line-height: 30px;
          text-align: left;
          font-size: 12px;
        }

        .el-form-item__content {
          height: 30px;
          line-height: 30px;
          padding-right: 200px;
          width: 100%;

          .el-input__inner {
            height: 30px;
            line-height: 30px;
          }
          .el-form-item__error{
            width: 200px;
            top: 15%;
            left: 200px;
          }
        }
        &.with-btn {
          .el-input {
            width: calc(100% - 80px);
          }
          .el-button {
            float: right;
            padding: 0;
            &::after {
              content: '';
              clear: both;
            }
          }
        }
      }
    }
    > div {
      padding: 4px;
      border-radius: 2px;
      font-size: 12px;
      line-height: 17px;
      font-family: PingFangSC, PingFangSC-Regular;
      font-weight: 400;
      color: #666666;
      margin-top: 0;
      div{
        margin-bottom: 8px;
      }
    }
  }
  .el-button {
    padding: 0 20px;
    line-height: 30px;
    //height: 30px;
  }
}
.searchMy ::v-deep  .el-form-item__label {
  margin-left: 20px;
  padding: 0;
}
.searchMy ::v-deep  .el-input__inner {
  border-radius: 0 4px 4px 0;
}
.searchMy ::v-deep  .el-date-editor{
  width: 100%;
}
.search-title {
  display: table-cell;
  padding: 0 10px;
  text-align: center;
  border: 1px solid #dcdfe6;
  height: 30px;
  line-height: 30px;
  vertical-align: middle;
  border-right: none;
  border-radius: 4px 0 0 4px;
  color: #333333;
  white-space: nowrap;
}
.searchMy ::v-deep  .el-select {
  display: table-cell;
  width: 100%;
}
.searchMy ::v-deep  .el-form-item__content{
  width: 100%;
}
.searchMy ::v-deep  .el-form-item--small.el-form-item{
  margin-bottom: 10px;
  width: 32%;
}
.searchMy ::v-deep  .el-form-item--small .el-form-item__content{
  line-height: 30px;
  width: 100%;
}
.searchMy ::v-deep  .el-input-group__prepend {
  background: none;
  color: #333333;
  padding: 0 10px;
}
.Fsearch {
  padding: 0px 20px 10px;
  font-weight: 500;
  .searchMsg {
    font-weight: 700;
    width: 200px;
  }
  .sign {
    display: inline-table;
    width: 3px;
    height: 13px;
    background: linear-gradient(360deg, #1d69c4 1%, #8bbdfc 99%);
    border-radius: 2px;
    margin-right: 8px;
  }
}
.el-dialog.withdraw-dialog .el-dialog__body .el-form .el-form-item .el-form-item__content{
  width: 100%;
}
</style>
