<template>
  <div class="auctionProductManagement">
     <div class="title">竞价商品管理列表</div>
    <SearchForm
       :hasOpenBtn='false'
        ref="searchForm"
        :model="formModel"
        :form-items="configJs.formItems"
        @submit="handleFormSubmit"
        @reset="handleFormReset"
      >
      </SearchForm>
      <div style="position:relative;top:-10px;width:200px">
         <el-button type="primary" style="width:100px" size="small" @click="exportExcel">导出</el-button>
      </div>
       <el-radio-group v-model="biddingStatus" style="margin-bottom: 30px;" @change="handleClick">
        <el-radio-button :label="1">生效中竞价商品</el-radio-button>
        <el-radio-button :label="2">已失效竞价商品</el-radio-button>
        <el-radio-button :label="20">已删除商品</el-radio-button>
       </el-radio-group>
     <xyyTable
        ref="productListTable"
        v-loading="tableLoading"
        :data="data"
        :col="configJs.col"
        :list-query="listQuery"
        @get-data="queryList"
       
      >
      <template slot="productInfo">
          <el-table-column label="商品信息">
            <template slot-scope="{ row }">
              <div class="row-content">
                <div>{{row.productName }}</div>
                <div style="color: red;">
                  <el-tag
                      v-if="
                        row.activeName === '特价' ||
                        row.activityType === 1 ||
                        row.activityType === 2 ||
                        row.activityType === 3
                      "
                      size="mini"
                      style="margin: 0 2px"
                    >
                      {{
                        row.activeName === '特价'
                          ? row.activeName
                          : { 2: '赠品', 1: '拼团', 3: '批购包邮' }[row.activityType]
                      }}
                    </el-tag>
                </div>
                <div>{{ row.manufacturer }}</div>
                <div>{{ row.spec }}</div>
                <div>{{  row.approvalNumber }}</div>
                <div>商品编码：{{ row.barcode }}</div>
                <div v-if="row.activityType === 3">CSUID：{{ row.csuid }}</div>
                <div v-else>sku编码：{{ row.csuid }}</div>
                <div>ERP编码：{{ row.erpCode }}</div>
                <div v-if="row.activityType === 1 || row.activityType === 2 || row.activityType === 3">
                  原商品：{{ row.originalBarcode }}
                </div>
              </div>
            </template>
          </el-table-column>
        </template>
        <template slot="showInfo">
          <el-table-column label="展示信息">
            <template slot-scope="{ row }">
              <div style="text-align: center;">
                <el-image
                style="width: 80px; height: 73px; margin-bottom: 4px"
                :src="row.fullImageUrl"
                :preview-src-list="row.allImageList"
                @click.prevent
              />
              </div>
              <div style="text-align: center;"><span v-if="row.activityType === 3">【包邮】</span>{{ row.showName }}</div>
            </template>
          </el-table-column>
        </template>
        <template slot="price">
          <el-table-column label="价格/库存">
            <template slot-scope="{ row }">
              <div class="row-content">
                <div>拼团价格：{{ row.groupPrice }}</div>
                <div v-if="row.platformSubsides"><span style="background-color: rgb(51,255,153);padding: 3px;">{{ `平台补贴：${row.platformSubsides}` }}</span></div>
                <div>提报价格：{{ row.reportPrice }} 
                  <!-- <i class="el-icon-edit-outline" @click="openPrice(row)" style="color: #4184d5; font-size: 16px; cursor: pointer;"/> -->
                </div>
                <div>起拼数量：{{ row.groupNum }}</div>
                <div>库存：{{ row.totalStock }}<span style="background-color: rgb(255,255,153);padding: 3px;">{{row.saleShopNums}}店</span></div>
              </div>
            </template>
          </el-table-column>
        </template>
        <template slot="sort">
          <el-table-column label="排名">
            <template slot-scope="{ row }">
              <div class="row-content">
              <div>提报价：{{ row.rankingReportPrice }}/{{  row.ruleProductNum }} <img v-if="row.rankingReportPrice==1" src="./img/good.png" alt=""> </div>
              <div>起拼：{{ row.rankingStartQty }}/{{  row.ruleProductNum }} <img  v-if="row.rankingStartQty==1" src="./img/good.png" alt=""></div>
              <div>库存：{{ row.rankingStock }}/{{  row.ruleProductNum }} <img   v-if="row.rankingStock==1" src="./img/good.png" alt=""></div>
              <div>{{row.ruleName}}<span style="background-color: rgb(255,204,255);padding: 3px;"  v-if="getActivity(row)">:{{getActivity(row)}}最优</span></div>
            </div>
            </template>
          </el-table-column>
        </template>
        <template slot="status">
          <el-table-column label="商品状态">
            <template slot-scope="{ row }">
              <div class="row-content" >
                <div 
                v-if="row.availableQty <= 0 && row.status == 1"
                style="color: #ff2121;text-align: center;" 
              >
                已售罄
              </div>
              <div v-else>
                <div  style="text-align: center;" >{{ statusNameStr(row.logicStatus) }}</div>
               
              </div>
              <div v-if="row.activityId">活动id：{{ row.activityId }}</div>
                <div v-if="row.activityStartTime&&row.activityEndTime">
                  活动时间：{{ transferTime(row.activityStartTime) }} ~
                  {{ transferTime(row.activityEndTime) }}
                  <!-- <p>活动时间：</p>
                  <div style="color: red" v-for="(item,index) in validFormat(row)" v-if="index==0||row.isShow">{{item}} <span class="ex" v-if="index==0&&validFormat(row).length>1" @click="ex(row)">&nbsp;{{row.isShow?'收起':'展开'}}</span></div> -->
                  </div>
            </div>
            </template>
          </el-table-column>
        </template>
      </xyyTable>
      <editPrice ref="editPrice" @queryList="queryList"></editPrice>
      <export-tip
      :change-export="changeExport"
      @handleExoprClose="handleExoprClose"
      @handleChangeExport="handleChangeExport"
    />
  </div>
</template>

<script>
import editPrice from './release/editPrice.vue';
import { mapState } from 'vuex'
import {productBiddingInfo,getQueryProductList,loadStatusCounts,exportBiddingSku} from "@/api/product"
import SearchForm from '@/components/searchForm'
import configJs from "./js/auctionProductManagement"
import exportTip from '@/views/other/components/exportTip';
export default {
components:{
    SearchForm,editPrice,exportTip
},
data(){
    return{
      changeExport: false,
        listQuery : {
          page: 1,
          pageSize: 10,
          total: 0
        },
        biddingStatus:1,
        tableLoading:false,
        activeName:"1",
        formModel:{
          barcode:"",
          erpCode:"",
          productName:"",
          manufacturer:"",
          approvalNumber:"",
          activityType:"",
          biddingStatus:1,
        },
        data:[],
        configJs,
        productStatusOptions: [],

    }
},
mounted(){
    this.queryList()
    this.loadStatusCounts(this.getParams())
},
computed: {
    ...mapState('app', ['shopConfig']),
   
  },
  activated(){
    this.handleFormReset()
  },
methods:{
  handleExoprClose() {
      this.changeExport = false;
    },
    handleChangeExport(info) {
      this.changeExport = false;
      if (info === 'go') {
        const path = '/downloadList';
        window.openTab(path);
      }
    },
  //导出
  exportExcel(){
    let params = JSON.parse(JSON.stringify(this.getParams()))
    Object.keys(params).forEach(key => {
      if (params[key] === '') {
        delete params[key];
      }
    });
    delete params.page;
    delete params.rows;
      exportBiddingSku(params).then(res=>{
        console.log(res)
        if (res.code == 0) {
            this.changeExport = true;
          } else {
            this.$message.error({
              message: res.message,
              offset: 100,
            });
          }
      })
  },
  getActivity(row){
   let str = []
   if(row.rankingReportPrice==1){
     str.push("提报价")
   }
   if(row.rankingStartQty==1){
     str.push("起拼")
   }
   if(row.rankingStock==1){
     str.push("库存")
   }
   return str.join("、")
  },
  transferTime(time) {
      return window.dayjs(time).format('YYYY-MM-DD HH:mm:ss')
    },
  async loadStatusCounts(params) {
      try {
        // const params = { ...this.formModel };
        // if (from == 2) {
        //   delete params.status;
        // }
        let tempParams = JSON.parse(JSON.stringify(params))
        delete tempParams.status
        const res = await loadStatusCounts(tempParams)
        if (res) {
          if (this.shopConfig.isFbp) {
            const arr = res.filter((i) => i.statusType != 2)
            this.productStatusOptions = [
              {
                statusName: '全部',
                statusType: -99
              },
              ...arr
            ]
          } else {
            this.productStatusOptions = [
              {
                statusName: '全部',
                statusType: -99
              },
              ...res
            ]
          }
        }
      } catch (e) {
        console.log(e)
      }
    },
  statusNameStr(value) {
      let name = ''
      this.productStatusOptions.forEach((item) => {
        if (item && Number(item.statusType) === Number(value)) {
          name = item.statusName
        }
      })
      return name || ''
    },
  //打开修改提报价格
    openPrice(row){
      
    this.$refs["editPrice"].open(row)
    },
    handleFormReset(){
    this.formModel={
           barcode:"",
          erpCode:"",
          productName:"",
          manufacturer:"",
          approvalNumber:"",
          activityType:"",
          biddingStatus:1,
    }
 this.queryList()
    },
    getParams(){
      let params = {
        page: this.listQuery.page,
        rows: this.listQuery.pageSize
      }
      params=Object.assign(this.formModel,params)
      return params
    },
    queryList(listQuery, from){
      //搜索
      if(listQuery){
        this.listQuery.page=listQuery.page
        this.listQuery.pageSize=listQuery.pageSize
      }
      
      this.tableLoading=true
      productBiddingInfo(this.getParams()).then(
        res=>{
          this.data=res.result.list
          this.listQuery.total=res.result.total
          this.biddingStatus=this.formModel.biddingStatus
        }
      ).finally(res=>{
        this.tableLoading=false
      })

    },
    handleClick(){
      console.log(1111)
      this.formModel.biddingStatus=this.biddingStatus
      this.handleFormSubmit()
    },
    handleFormSubmit(){
      this.listQuery.page=1
      this.queryList()
    },
    reset(){
    this.formModel={
      barcode:"",
          erpCode:"",
          productName:"",
          manufacturer:"",
          approvalNumber:"",
          activityType:"",
          biddingStatus:1,
     }
    }
}
}
</script>

<style lang="scss" scoped>
.auctionProductManagement{
    margin-top: 20px;
    padding: 0 10px;
.title {
      font-weight: 500;
      text-align: left;
      color: #000000;
      line-height: 14px;
      margin-bottom: 24px;
    }

    .title:before {
      content: '';
      display: inline-block;
      width: 3px;
      height: 13px;
      background: linear-gradient(360deg, #1d69c4 1%, #8bbdfc 99%);
      border-radius: 2px;
      margin-right: 8px;
      vertical-align: middle;
    }
    // .row-content div{
    //   word-break:break-all;
    //     display: block !important;
    //    overflow:visible;
    //     line-height: 20px;
    //     text-align: left;
    //     overflow-wrap: break-word !important;
        
    // }
    // .row-content{
    //   margin: 10px 6px;
    // }
}
 
</style>
<style lang="scss">
    .auctionProductManagement{
        .el-radio-group{
            
            margin: 0 !important;
            margin-top: 10px;
        }
        .el-table tr td .cell{
          height: 100%;
          display: flex;
          flex-direction: column;
          justify-content: center;
          white-space: break-spaces;
          font-size: 12px;
          font-family: PingFangSC, PingFangSC-Regular;
          font-weight: 400;
          text-align: left;
          color: #666666;
          line-height: 22px;
          padding-left: 16px;
        }
    }
</style>