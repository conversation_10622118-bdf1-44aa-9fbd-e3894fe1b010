<template>
  <el-dialog
    title="操作流程"
    :visible.sync="dialogVisible"
    width="30%"
    :before-close="handleClose">
    <div style="text-align: center">
      <el-image
        style="width: 170px; height: 170px"
        :src="wxCodeUrl"
        :fit="fit"/>
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: "operationFlow",
  props: {
    operationFlowVisible: {
      type: Boolean,
      default: true
    },
    wxCodeUrl: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      dialogVisible: true,
      fit: 'cover'
    }
  },
  methods: {
    handleClose() {
      this.$emit('update:operationFlowVisible', false)
    }
  }
}
</script>

<style scoped>

</style>
