<template>
  <div class="serch">
    <div class="Fsearch">
      <el-row
          type="flex"
          align="middle"
          justify="space-between"
          class="my-row"
      >
        <el-row
            type="flex"
            align="middle"
        >
          <span class="sign" />
          <div class="searchMsg">
            活动详情
          </div>
        </el-row>
        <el-button
            type="primary"
            size="small"
            @click="resetForm()"
        >
          返回
        </el-button>
      </el-row>
    </div>
    <div class="fromBlock">
      <el-form
        :model="ruleForm"
        ref="ruleForm"
        size="small"
        label-width="100px"
      >
        <el-form-item label="活动名称:">
          <el-input v-model="ruleForm.name"></el-input>
        </el-form-item>
        <el-form-item label="优惠券:">
          <div>
            <span class="couponInfo">券名称</span>
            <span class="couponInfo">限领数量</span>
          </div>
          <div v-for="item in couponDetail" :key="item.id">
            <span class="couponInfo">{{ item.name }}
                          <el-button
                            class="addcoupon"
                            type="text"
                            @click="addCouBtn(item.id)"
                            plain
                            size="small"
                          >查看</el-button>
            </span>
            <span class="couponInfo">{{filterCount(item)}}</span>
          </div>
<!--          <div v-for="item in couponDetail" :key="item.id">-->
<!--            <span style="margin: 0 15px; color: #666">券名称：{{ item.name }}</span>-->
<!--            <span style="margin: 0 15px; color: #666">限领数量：{{filterCount(item)}}</span>-->
<!--            <el-button-->
<!--              class="addcoupon"-->
<!--              type="text"-->
<!--              @click="addCouBtn(item.id)"-->
<!--              plain-->
<!--              size="small"-->
<!--            >查看</el-button-->
<!--            >-->
<!--          </div>-->
        </el-form-item>
        <el-form-item label="人群:">
          <label class="el-form-item_span">{{
            ruleForm.prMarketCustomerGroupId == -1
              ? '全部人群参与'
              : '定向人群参与'
          }}</label>
          <span
            class="content-span-shop"
            v-if="ruleForm.prMarketCustomerGroupId != -1"
            >{{ ruleForm.prMarketCustomerGroupName }}</span
          >
          <el-button
            type="text"
            v-if="ruleForm.prMarketCustomerGroupId != -1"
            size="small"
            style="margin-left: 10px"
            @click="seletePeople(ruleForm.prMarketCustomerGroupId)"
            >查看</el-button
          >
        </el-form-item>
        <el-form-item label="领取时间:">
          <div
            v-if="ruleForm.creatTime!=='-'"
            class="activity-detail-time"
          >
            {{ ruleForm.creatTime }}
          </div>
          <!-- <el-date-picker
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            v-model="ruleForm.creatTime"
          >
          </el-date-picker> -->
        </el-form-item>
        <el-form-item
          v-if="shopConfig.showTag"
          label="领券中心是否展示:"
        >
          {{ ruleForm.display ? '是' : '否' }}
        </el-form-item>
      </el-form>
    </div>
    <selectPepel ref="crowdialog"></selectPepel>
  </div>
</template>

<script>
import { mapState } from 'vuex';
import { getCouponDetail } from '@/api/activity/index'
import selectPepel from './components/selectPepel'

export default {
  name: 'activeDetail',
  components: { selectPepel },
  data() {
    return {
      addCoupon: false,
      ruleForm: {
        name: '',
        coupon: '',
        couponID: '',
        creatTime: '',
        prMarketCustomerGroupName: '',
        prMarketCustomerGroupId: '',
        display: 0,
      },
      couponDetail: { id: null },
      crowVisible: false,
      crowIds: '',
    }
  },
  computed: { ...mapState('app', ['shopConfig']) },
  beforeRouteEnter(to, from, next) {
    next((vm) => {
      console.log('vm.$router.query:', vm.$route.query.id)
      vm.getCouponDetail(vm.$route.query.id)
    })
  },
  // 更新埋点列表
  beforeRouteUpdate(to, from, next) {
    console.log('vm.$router.query:', this.$route)
    this.getCouponDetail(this.$route.query.id)
    next()
  },
  methods: {
    seletePeople(id) {
      this.$refs.crowdialog.open(id)
    },
    onDialogChange(val) {
      console.log(val)
    },
    async getCouponDetail(id) {
      const res = await getCouponDetail({ id });
      if (res.success) {
        const {
          couponDetailVos,
          startTime,
          endTime,
          name,
          prMarketCustomerGroupId,
          prMarketCustomerGroupName,
          logicalStatus,
          display,
        } = res.data.detail;
        this.ruleForm.name = name;
        this.couponDetail = couponDetailVos;
        this.ruleForm.prMarketCustomerGroupId = prMarketCustomerGroupId;
        // this.couponDetail.name = couponDetailVos.name
        this.ruleForm.logicalStatus = logicalStatus;
        this.ruleForm.creatTime = `${this.formatDate(startTime)} - ${this.formatDate(endTime)}`;
        this.ruleForm.display = display;
      } else {
        this.$message.error(res.msg);
        this.$router.go(-1)
      }

    },
    /**
     * 格式化日期
     */
    formatDate(date) {
      return new Date(date + 8 * 3600 * 1000)
        .toJSON()
        .substr(0, 19)
        .replace('T', ' ')
    },
    addCouBtn(id) {
      // this.addCoupon = true;
      console.log(id)
      this.$router.push({
        path: 'couponDetail',
        query: {
          id
        }
      })
    },
    querySel(selItem) {
      this.selItem = selItem
      this.ruleForm.coupon = this.selItem.name
      this.addCoupon = false
    },
    cancelSel() {
      this.addCoupon = false
    },
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          console.log(valid)
        } else {
          console.log('error submit!!')
          return false
        }
        return false
      })
    },
    resetForm(formName) {
      if(formName) {
        this.$refs[formName].resetFields()
      }
      if(this.$store.state.permission.menuGray == 1) {
        this.$router.replace({
          path: '/storeVoucher',
          query: {to: 'activemanage'}
        })
      }else {
        this.$router.push({ path: '/activemanage' })
      }
    },
    filterCount(item) {
      if (!item) return;
      const sendType = Number(item.sendType);
      let str = '';
      switch (sendType) {
        case 1:
          str = `活动期间限领${item.count}张`;
          break;
        case 2:
          str = `每人每天限领${item.count}张`;
          break;
        case 3:
          str = `不限制`;
      }
      return str;
    }
  }
}
</script>

<style lang="scss" scoped>
.activity-detail-time{
  display: flex;
  flex-direction: row;
  align-items: center;
}
.serch {
  padding: 20px;
  font-weight: 500;

  .sign {
    display: inline-table;
    width: 3px;
    height: 13px;
    background: linear-gradient(360deg, #1d69c4 1%, #8bbdfc 99%);
    border-radius: 2px;
    margin-right: 8px;
  }

  .fromBlock {
    width: 500px;
    margin-top: 20px;

    .couponName {
      display: inline-block;
      width: 70%;

      ::v-deep  .el-input__inner {
        border: none;
      }
    }
  }
}
::v-deep  .el-input .el-input__inner {
  border: none;
}
::v-deep  .el-input__inner {
  border: none;
}
::v-deep  .el-input__icon {
  display: none;
}

.couponInfo {
  display: inline-block;
  width: 200px
}
</style>
