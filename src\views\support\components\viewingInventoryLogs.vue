<template>
  <el-dialog title="商品库存日志" :visible="dialogVisible" width="80%" :before-close="handleClose">
    <div style="margin-bottom:10px">
      <span style="color:#333333;font-size:14px">{{inventoryConfig.showName}}</span>
      <span style="margin-left:10px">{{inventoryConfig.barcode}}</span>
      <span style="margin-left:10px">{{inventoryConfig.erpCode}}</span>
    </div>
    <el-table v-loading="loading" :data="tableConfig.data" border height="400">
      <el-table-column label="变更内容">
        <template slot-scope="{row}">
          <div>
            <div v-if="row.beforeQty!==null">变更前库存数量:{{row.beforeQty}}</div>
            <div>变更数量:{{row.changeQty}}</div>
            <div>变更后库存数量:{{row.afterQty}}</div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="说明">
        <template slot-scope="{row}">
          <div>
            <div v-if="row.comment">{{row.comment}}</div>
            <div v-if="row.orderNo">{{row.orderNo}}</div>
            <div v-if="row.objId">活动ID:{{row.objId}}</div>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="createTime" label="变更时间" :formatter="formatDate"></el-table-column>
      <template slot="empty">
        <div class="noData">
          <p class="img-box">
            <img src="@/assets/image/marketing/noneImg.png" alt />
          </p>
          <p>暂无数据</p>
        </div>
      </template>
    </el-table>
    <div class="explain-pag">
      <Pagination
        v-show="tableConfig.total > 0"
        :total="tableConfig.total"
        :page.sync="pageData.pageNum"
        :limit.sync="pageData.pageSize"
        @pagination="getSkulog"
      />
    </div>
  </el-dialog>
</template>

<script>
import {apiStockLogs} from '@/api/product'
import Pagination from '@/components/Pagination';

export default {
  name: "viewingInventoryLogs",
   components: { Pagination },
  props: {
    inventoryConfig: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      dialogVisible: true,
      loading: true,
      tableConfig: {
        data: [],
        total: 0,
      },
      pageData: {
        pageSize: 10,
        pageNum: 1,
      },
    }
  },
  mounted() {
    this.getSkulog()
  },
  methods: {
    async getSkulog() {
      try {
        let params = {
          barcode: this.inventoryConfig.barcode,
          ...this.pageData,
        } 
        const res = await apiStockLogs(params)
        if (res.code === 0) {
          this.tableConfig.data = res.data.list||[]
          this.tableConfig.total = res.data.total
        } else {
          this.$alert(res.msg, {type: 'error'})
        }
      } catch (e) {
        console.log(e)
      }
      this.loading = false
    },
    handleClose() {
      this.$emit('update:inventoryLogDialogVisible', false)
    },
    // 时间格式化
    formatDate(row, column, cellValue) {
      const date = new Date(cellValue)
      const y = date.getFullYear()
      let MM = date.getMonth() + 1
      MM = MM < 10 ? `0${MM}` : MM
      let d = date.getDate()
      d = d < 10 ? `0${d}` : d
      let h = date.getHours()
      h = h < 10 ? `0${h}` : h
      let m = date.getMinutes()
      m = m < 10 ? `0${m}` : m
      let s = date.getSeconds()
      s = s < 10 ? `0${s}` : s
      return `${y}-${MM}-${d} ${h}:${m}:${s}`
    },
  }
}
</script>

<style scoped lang="scss">
::v-deep   .el-table thead th {
  background: #f9f9f9;
  border: none;

  .cell {
    font-family: PingFangSC, PingFangSC-Regular;
    font-weight: 400;
    text-align: left;
    color: rgba(51, 51, 51, 0.85);
    line-height: 22px;
  }
}

::v-deep   .el-table__body-wrapper {
  font-size: 12px;
  color: #666666;
}
</style>
