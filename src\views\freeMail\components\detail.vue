<template>
  <div class="preview-detail">
    <el-form label-width="120px">
      <el-row>
        <el-col :span="12">
          <el-form-item label="商品分类">
            {{ detail.skuCategory }}
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="生产厂家">
            {{ detail.manufacturer }}
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="批准文号">
            {{ detail.approvalNumber }}
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="有效期">
            {{ detail.term }}
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="规格">
            {{ detail.spec }}
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="存储条件">
            {{ detail.storageCondition }}
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item
            label="中包装"
          >
            {{ detail.mediumPackageNum }}{{ detail.productUnit }}
            {{ detail.isSplit === 1 ? '可拆零' : '不可拆零' }}
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item
            label="件装量"
          >
            {{ detail.pieceLoading }}{{ detail.productUnit }}
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label="适用症/功能主治">
            {{ detail.indication }}
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label="用法与用量">
            {{ detail.usageAndDosage }}
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label="不良反应">
            {{ detail.untowardEffect }}
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <ul>
      <li
        v-for="(url, i) in detail.imgs"
        :key="i"
      >
        <img :src="url">
      </li>
    </ul>
  </div>
</template>

<script>
export default {
  props: {
    detail: {
      type: Object,
      default: () => ({
        skuCategory: '', // 商品分类
        manufacturer: '', // 生产厂家
        approvalNumber: '', // 批准文号
        term: '', // 有效期
        spec: '', // 规格
        storageCondition: '', // 存储条件
        mediumPackageNum: '', // 中包装
        pieceLoading: '', // 件装量
        indication: '', // 适应症/功能主治
        usageAndDosage: '', // 用法与用量
        untowardEffect: '', // 不良反应
        productUnit: '', // 单位
        isSplit: '', // 是否可拆零 1 是 0 否
        imgs: [], // 图片集合
      }),
    },
  },
};
</script>

<style lang="scss" scoped>
.preview-detail {
  padding: 5px;
  .el-form {
    .el-row {
      border-bottom: 1px dashed #f1f1f1;
      .el-form-item {
        margin-bottom: 10px;
        ::v-deep  .el-form-item__label {
          min-height: 30px;
          line-height: 30px;
          text-align: left;
        }
        ::v-deep  .el-form-item__content {
          min-height: 30px;
          line-height: 30px;
        }
      }
    }
  }
  ul {
    margin-top: 20px;
    li {
      list-style: none;
      img {
        max-width: 100%;
      }
    }
  }
}
</style>
