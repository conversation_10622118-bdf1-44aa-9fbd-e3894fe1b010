import request from '@/utils/request';

/**
 * 查询品分类列表
 * @param {params}查询参数
 */
export function selectPage(data) {
  return request({
    url: '/productCategoryAnalysis/selectPage',
    method: 'post',
    data,
  });
}

/**
 * 查询品分类列表 - 新的
 * @param {params}查询参数
 */
export function apiSelectMainPage(params) {
  return request({
    url: '/productCategoryAnalysis/selectMainPage',
    method: 'get',
    params,
  });
}

/**
 * 查询子品统计列表
 * @param {params}查询参数
 */
export function apiSelectSubPage(params) {
  return request({
    url: '/productCategoryAnalysis/selectSubPage',
    method: 'get',
    params,
  });
}

/**
 * 查询点击量和访客量
 * @param {data}查询参数
 */
export function getHitsAndvisitorReport(data) {
  return request({
    url: '/productCategoryAnalysis/getHitsAndvisitorReport',
    method: 'post',
    data,
  });
}

/**
 * 查询商品销售销量和销售额
 * @param {params}查询参数
 */
export function getSaleNumAndSalesMoney(data) {
  return request({
    url: '/productCategoryAnalysis/getSaleNumAndSalesMoney',
    method: 'post',
    data,
  });
}

/**
 * 查询商品价格和销量
 * @param {params}查询参数
 */
export function getPriceAndSaleNum(data) {
  return request({
    url: '/productCategoryAnalysis/getPriceAndSaleNum',
    method: 'post',
    data,
  });
}

/**
 * 查询商品连锁指导价和销量
 * @param {params}查询参数
 */
export function getKaPriceAndSaleNum(data) {
  return request({
    url: '/productCategoryAnalysis/getKaPriceAndSaleNum',
    method: 'post',
    data,
  });
}

/**
 * 导出分类列表
 * @param {params}查询参数
 */
export function apiExportList(data) {
  return request({
    url: '/productCategoryAnalysis/exportList',
    method: 'post',
    data,
  });
}

/**
 * 导出分析详情
 * @param {params}查询参数
 */
export function exportAnalysisDetail(data) {
  return request({
    url: '/productCategoryAnalysis/exportAnalysisDetail',
    method: 'post',
    data,
  });
}

/**
 * 导出数据明细
 * @param {params}查询参数
 */
export function apiExportDetail(params) {
  return request({
    url: '/productCategoryAnalysis/exportSubList',
    method: 'get',
    params,
  });
}

/**
 * 价格统计
 * @param {params}查询参数
 */
export function getPriceAndSaleNumV2(params) {
  return request({
    url: '/productCategoryAnalysis/getPriceAndSaleNumV2',
    method: 'get',
    params,
  });
}
