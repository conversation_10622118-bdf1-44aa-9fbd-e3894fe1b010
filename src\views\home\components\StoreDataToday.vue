<template>
  <div class="StoreDataToday" v-if="shopConfig.indexShow">
    <div class="task_title">今日店铺数据</div>
    <el-row class="storeDataList">
      <el-col v-permission="['deal_order']" :span="12" @click.native="openFn('newOrderCount')">
        <span class="storeDataItem">今日新订单</span>
        <el-tooltip placement="top">
          <div slot="content">今日支付的有效订单数量，统计订单状态包含“审核中、出库中、配送中、已完成</div>
          <i class="el-icon-warning-outline"></i>
        </el-tooltip>
        <div class="storeData newOrderCount">{{ storeData.newOrderCount }}</div>
      </el-col>
      <el-col v-permission="['deal_order']" :span="12" @click.native="openFn('amountPayable')">
        <span class="storeDataItem">今日订单实付</span>
        <el-tooltip placement="top">
          <div slot="content">今日支付的有效订单实付金额(含运费，不含优惠)-今日支付订单的实退金额，统计订单状态包含“审核中、出库中、配送中、已完成</div>
          <i class="el-icon-warning-outline"></i>
        </el-tooltip>
        <div class="storeData amountPayable">{{ storeData.amountPayable }}</div>
      </el-col>
      <el-col v-permission="['deal_refund']" :span="12" @click.native="openFn('refundOrderCount')">
        <span class="storeDataItem">今日退款单</span>
        <el-tooltip placement="top">
          <div slot="content">今日产生的所有退款单数量</div>
          <i class="el-icon-warning-outline"></i>
        </el-tooltip>
        <div class="storeData refundOrderCount">{{ storeData.refundOrderCount }}</div>
      </el-col>
      <!--      <el-col :span="12" @click.native="openFn('refundTotalMoney')">-->
      <el-col v-permission="['deal_refund']" class="refundTotalMoney" :span="12" @click.native="openFn('refundTotalMoney')">
        <span class="storeDataItem">今日已退款金额</span>
        <el-tooltip placement="top">
          <div slot="content">今日退款成功的所有退款金额（含运费）</div>
          <i class="el-icon-warning-outline"></i>
        </el-tooltip>
        <div class="storeData refundTotalMoney">{{ storeData.refundTotalMoney }}</div>
      </el-col>
      <el-col v-permission="['customer_list']" :span="12" @click.native="openFn('newCustomerCount')">
        <span class="storeDataItem">今日新增客户</span>
        <el-tooltip placement="top">
          <div slot="content">今日产生店铺首单的客户</div>
          <i class="el-icon-warning-outline"></i>
        </el-tooltip>
        <div class="storeData newCustomerCount">{{ storeData.newCustomerCount }}</div>
      </el-col>
      <el-col v-permission="['product_list']" :span="12" @click.native="openFn('sellingInStockSkuCount')">
        <span class="storeDataItem">在售有货商品</span>
        <el-tooltip placement="top">
          <div slot="content">当前在售且有库存的商品</div>
          <i class="el-icon-warning-outline"></i>
        </el-tooltip>
        <div class="storeData sellingInStockSkuCount">{{ storeData.sellingInStockSkuCount }}</div>
      </el-col>
<!--      <el-col :span="12">-->
<!--        <el-button type="primary" @click="print">预览打印</el-button>-->
<!--      </el-col>-->
    </el-row>
<!--        <el-dialog-->
<!--          title="提示"-->
<!--          :visible.sync="dialogVisible"-->
<!--          width="500px"-->
<!--          :before-close="()=>{dialogVisible=false}">-->
<!--          <ExpressPrint ref="expressPrint"></ExpressPrint>-->
<!--          <span slot="footer" class="dialog-footer">-->
<!--        <el-button @click="dialogVisible = false">取 消</el-button>-->
<!--        <el-button type="primary" @click="printFn">打印</el-button>-->
<!--      </span>-->
<!--        </el-dialog>-->
<!--    <ExpressPrint v-show="false" ref="expressPrint" />-->
  </div>
</template>

<script>
import { todayData } from '@/api/home';
import { mapState } from 'vuex';
import ExpressPrint from '@/components/print/expressPrint';
import {actionTracking} from "@/track/eventTracking";

export default {
  name: 'StoreDataToday',
  components: {
    ExpressPrint
  },
  data() {
    return {
      storeData: {
        newOrderCount: 0,
        amountPayable: 0,
        refundOrderCount: 0,
        refundTotalMoney: 0,
        newCustomerCount: 0,
        sellingInStockSkuCount: 0
      },
      expressPrintConfig: {},
      dialogVisible: true
    };
  },
  computed: {
    ...mapState('app', ['shopConfig'])
  },
  created() {
    this.getTodayData();
  },
  // mounted() {
  //   this.print()
  // },
  methods: {
    print() {
      const params =
        {
          'consignor': '张三',
          'contactor': '武汉千湖',
          'deliveryMobile': '1760123124',
          'isVip': 0,
          'logisticsCompanyCode': 1,
          'mailAddress': '白果镇易家大湾23号',
          'mailRegionName': '湖北省黄冈市麻城市',
          'merchantName': '武汉千湖大药房有限公司',
          'mobile': '041923696322',
          'orderCode': 'YSD1517435767964565602',
          'orderNo': 'YBM20220330095111100002',
          'payType': '寄付月结',
          'printCount': 1,
          'printTime': 1650619855167,
          'proName': '顺丰特快',
          'sFFace': {
            'abFlag': '',
            'codingMapping': 'WU',
            'codingMappingOut': '',
            'destRouteLabel': '027',
            'destTeamCode': '',
            'limitTypeCode': 'T4',
            'proCode': 'T4',
            'proName': '顺丰特快',
            'sourceTransferCode': '713',
            'twoDimensionCode': 'MMM={\'k1\':\'027\',\'k2\':\'027\',\'k3\':\'\',\'k4\':\'T4\',\'k5\':\'SF7444438948628\',\'k6\':\'\',\'k7\':\'e3e633ec\'}'
          },
          'settlementCardNo': '0276713871',
          'subWaybillNoList': [{
            'subSerialNumber': '1',
            'subWaybillNo': 'SF7444438948628'
          }, {
            'subSerialNumber': '2',
            'subWaybillNo': 'SF7444511145409'
          }],
          'takeAeliveryAddress': '湖北省武汉市汉南区团结佳兴园1栋22-23号.EC',
          'takeAeliveryRegionName': '湖北省武汉市汉南区',
          'tmsToWms': '1',
          'waybillNo': 'SF7444438948628'
        }
      setTimeout(()=>{
        this.$refs.expressPrint.printAction(params);
      },500)

    },
    printFn(){
      this.$refs.expressPrint.print();
    },
    async getTodayData() {
      const res = await todayData();
      if (res && res.code === 0) {
        Object.keys(res.result)
          .map(key => {
            this.$set(this.storeData, key, res.result[key]);
          });
      }
    },
    openFn(str) {
      console.log(str);
      const obj = {};
      let path = '';
      const YMD = this.formatDate(new Date().getTime(), 'YMD');
      const time = [YMD + ' 00:00:00', YMD + ' 23:59:59'];
      let trackarg = '';
      switch (str) {
        case 'newOrderCount':
          //今日新订单
          path = '/orderList';
          obj.time = time;
          obj.status = 10;
          trackarg = 'new_orders'
          break;
        case 'amountPayable':
          //今日订单实付
          obj.time = time;
          obj.status = 10;
          path = '/orderList';
          trackarg = 'new_orders_payment'
          break;
        case 'refundOrderCount':
          //今日退款单
          obj.startCreateTime = new Date(time[0]).getTime();
          obj.endCreateTime = new Date(time[1]).getTime();
          obj.status = '0';
          path = '/afterSaleList';
          trackarg = 'refund_order'
          break;
        case 'refundTotalMoney':
          //今日已退款金额
          obj.status = '0';
          path = '/afterSaleList';
          trackarg = 'refund_order_payment'
          break;
        case 'newCustomerCount':
          //今日新增客户
          path = '/customerList';
          obj.orderStartTime = time;
          trackarg = 'new_customers'
          break;
        case 'sellingInStockSkuCount':
          //在售有货商品
          path = '/productList';
          obj.status = 1;
          obj.stockStatus = '1';
          trackarg = 'goods_on_sale'
          break;
      }

      actionTracking('today_store_data_click', {
        today_store_data : trackarg
      })
      obj.homeEnter = 1;
      window.openTab(path, obj);
    }
  }
};
</script>

<style scoped lang="scss">
.StoreDataToday {
  background-color: #fff;
  padding: 16px;

  .task_title {
    margin-bottom: 22px;
  }

  .el-col {
    margin-bottom: 32px;
    cursor: pointer;
  }

  .el-col-12 {
    width: 30%;
    margin-right: 20%;
  }

  .el-col.refundTotalMoney {
    cursor: pointer;
  }

  .storeDataItem {
    margin-right: 4px;
  }

  .storeData {
    margin-top: 4px;
    font-size: 30px;
    font-family: PingFangSC, PingFangSC-Semibold;
    font-weight: 600;
    color: #323334;
    line-height: 42px;
  }

  .storeData:before {
    content: '';
    display: inline-block;
    width: 24px;
    height: 24px;
    margin-right: 7px;
  }

  .storeData.newOrderCount:before {
    background: url("../../../assets/image/home/<USER>");
    background-size: 100% 100%;
  }

  .storeData.amountPayable:before {
    background: url("../../../assets/image/home/<USER>");
    background-size: 100% 100%;
  }

  .storeData.refundOrderCount:before {
    background: url("../../../assets/image/home/<USER>");
    background-size: 100% 100%;
  }

  .storeData.refundTotalMoney:before {
    background: url("../../../assets/image/home/<USER>");
    background-size: 100% 100%;
  }

  .storeData.newCustomerCount:before {
    background: url("../../../assets/image/home/<USER>");
    background-size: 100% 100%;
  }

  .storeData.sellingInStockSkuCount:before {
    background: url("../../../assets/image/home/<USER>");
    background-size: 100% 100%;
  }
}
</style>
