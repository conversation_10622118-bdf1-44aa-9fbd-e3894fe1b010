<template>
  <el-dialog
    title="修改单位"
    :visible="dialogVisible"
    width="40%"
    :before-close="handleClose">
    <el-form :model="formModel" label-width="100px">
      <el-form-item label="单位" prop="productUnit">
        <el-select v-model="formModel.productUnit" placeholder="请选择">
          <el-option
            v-for="item in unitListOptions"
            :key="item.id"
            :label="item.name"
            :value="item.id"/>
        </el-select>
      </el-form-item>
    </el-form>

    <span slot="footer" class="dialog-footer">
    <el-button @click="handleClose">取 消</el-button>
    <el-button type="primary" @click="modifyConfirm" :loading="loading">确 定</el-button>
  </span>
  </el-dialog>
</template>

<script>
import {updateProductUnit} from '@/api/product'

export default {
  name: "modifyUnitDialog",
  props: {
    unitListOptions: {
      type: Array,
      default: () => {
        return []
      }
    },
    productData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      dialogVisible: true,
      formModel: {
        productUnit: ''
      },
      loading: false
    }
  },
  created() {
    this.formModel.productUnit = this.productData.productUnitStandard || this.productData.productUnit
  },
  methods: {
    handleClose() {
      this.$emit('update:modifyUnitDialog', false)
    },
    async modifyConfirm() {
      this.loading = true
      const res = await updateProductUnit({id: this.productData.id, productUnit: this.formModel.productUnit})
      if (res && res.code === 0) {
        this.$alert('修改成功', {type: 'success'}).then(()=>{
          this.$emit('modifyConfirm')
          this.handleClose()
        })
      } else {
        this.$alert(res.message, {type: 'error'})
      }
      this.loading = false
    }
  }
}
</script>

<style scoped>

</style>
