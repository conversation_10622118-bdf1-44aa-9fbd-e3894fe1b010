<template>
  <div class="main-box">
    <part-title title="查询条件" />
    <el-row class="searchMy">
      <el-form
        ref="form"
        class="fix-item-width"
        size="small"
        label-position="right"
        :model="formData"
        :inline="true"
      >
        <el-form-item prop="userName">
          <el-input v-model="formData.userName" clearable placeholder="请输入完整的登录账号">
            <template slot="prepend">登录账号</template>
          </el-input>
        </el-form-item>
        <el-form-item prop="realName">
          <el-input v-model="formData.realName" clearable placeholder="请输入姓名">
            <template slot="prepend">姓名</template>
          </el-input>
        </el-form-item>
        <el-form-item prop="roleIds">
          <span class="search-title">角色</span>
          <el-select v-model="formData.roleIds" placeholder="全部" multiple>
            <el-option
              v-for="item in allRoles"
              :key="item.roleId"
              :label="item.roleName"
              :value="item.roleId"
            />
          </el-select>
        </el-form-item>
        <el-form-item prop="userStatus">
          <span class="search-title">账号状态</span>
          <el-select v-model="formData.userStatus" placeholder="全部">
            <el-option label="全部" :value="null" />
            <el-option label="启用" :value="0" />
            <el-option label="停用" :value="1" />
          </el-select>
        </el-form-item>
        <el-form-item class="searchBtn">
          <el-button size="small" type="primary" @click="searchData">查询</el-button>
          <el-button size="small" @click="resetFields">重置</el-button>
        </el-form-item>
      </el-form>
    </el-row>
    <part-title title="账号列表" />
    <el-button type="primary" size="small" @click="createAccount">新建账号</el-button>
    <div v-loading="loading">
      <el-table
        :data="accountData.list"
        style="margin-top: 10px;"
        border
        max-height="500px"
        :header-cell-style="{ background: '#eeeeee', color: '#666666' }"
      >
        <el-table-column prop="userName" label="登录账号" />
        <el-table-column prop="realName" label="姓名" />
        <el-table-column prop="description" label="说明" />
        <el-table-column prop="roleName" label="角色" />
        <el-table-column label="操作" width="200" fixed="right">
          <template slot-scope="{ row }">
            <el-button type="text" @click="editRole(row)">编辑</el-button>
            <el-button v-if="row.userStatus == 0" type="text" @click="disableRole(row)">停用</el-button>
            <el-button v-else type="text" @click="disableRole(row)">启用</el-button>
            <el-button type="text" @click="deleteRole(row)">删除</el-button>
            <el-button type="text" @click="resetPassword(row)">重置密码</el-button>
          </template>
        </el-table-column>
        <template slot="empty">
          <div class="noData">
            <p class="img-box">
              <img :src="emptyImg" alt />
            </p>
            <p>暂无数据</p>
          </div>
        </template>
      </el-table>
      <el-row>
        <div class="pagination-container">
          <div
            class="pag-text"
          >共 {{ accountData.total }} 条数据，每页{{ pageInfo.pageSize }}条，共{{ Math.ceil(accountData.total / pageInfo.pageSize) || 0 }}页</div>
          <el-pagination
            :page-sizes="[10, 20, 30, 50]"
            prev-text="上一页"
            next-text="下一页"
            layout="sizes, prev, pager, next, jumper"
            :total="accountData.total"
            :current-page="pageInfo.pageNum"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </el-row>
    </div>
    <accountDialog
      v-if="addDialogForm"
      :account-dialog-type="accountDialogType"
      :user-id="userId"
      @cancellationBtn="cancellationBtn"
      @refreshList="refreshList"
    />
    <resetPasswordDialog
      v-if="resetPasswordVisible"
      :user-id="userId"
      @cancellationBtn="cancellationBtn"
      @refreshList="refreshList"
    />
  </div>
</template>
<script>
import { emptyImg } from '@/components/xyy/customerOperatoin/constant';
import partTitle from '@/components/part-title/index';
import accountDialog from './components/accountDialog.vue';
import resetPasswordDialog from './components/resetPasswordDialog.vue';
import { apiListSubAccount , apiQueryAllRoles , apiSwitchAccountStatus , apiDeleteAccount } from '@/api/userManagement';
export default {
  name: 'AccountManagement',
  components: {partTitle , accountDialog , resetPasswordDialog},
  data() {
    return {
      emptyImg,
      loading: false,
      formData: {
        userName:'',
        realName:'',
        roleIds:[],
        userStatus:null
      },
      accountData: {
        list: [],
        total: 0,
      },
      pageInfo: {
        pageNum: 1,
        pageSize: 10,
      },
      accountDialogType: 'add',
      addDialogForm:false,
      resetPasswordVisible:false,
      userId: 0,
      allRoles:[]
    }
  },
  created() {
    // this.queryAllRoles();
  },
  activated() {
    this.queryAllRoles();
    this.activate();
  },
  mounted() {
    this.searchData();
  },
  methods: {
    activate(){
      const query = JSON.parse(JSON.stringify(this.$route.query));
      delete query.to;// 维护原逻辑，去掉新增的to参数
      if (query && Object.keys(query).length > 0) {
        this.formData = this.$options.data().formData;
        const { roleId } = this.$route.query;
        if (roleId) {
          this.formData.roleIds.push(Number(roleId));
          this.searchData();
        }
      }
    },
    // 查询所有角色
    queryAllRoles(){
      const that = this;
      apiQueryAllRoles().then((res) => {
        if (res.code == 0) {
          that.allRoles = res.data||[];
        }else {
           that.$message({
              message: res.message,
              type: 'error'
            })
          }
      }) .catch(() => {});
    },
    refreshList() {
      this.cancellationBtn();
      this.getAccountList();
    },
    resetFields() {
      this.formData = this.$options.data().formData;
      this.searchData();
    },
    searchData() {
      this.pageInfo.pageNum = 1;
      this.getAccountList();
    },
    handleSizeChange(size) {
      this.pageInfo.pageSize = size;
      this.getAccountList();
    },
    handleCurrentChange(val) {
      this.pageInfo.pageNum = val;
      this.getAccountList();
    },
    getAccountList() {
      const that = this;
      this.loading = true;
      const {
        userName,
        realName,
        roleIds,
        userStatus
      } = this.formData
      let param = {
        ...this.pageInfo,
        userName,
        realName,
        userStatus,
        roleIds:roleIds&&roleIds.length ? roleIds.join(','):''
      }
      apiListSubAccount(param).then((res) => {
        if (res.code == 0) {
          that.loading = false
          that.accountData.list = res.data.list || [];
          that.accountData.total = res.data.total;
        }else {
          that.loading = false
           that.$message({
              message: res.message,
              type: 'error'
            })
          }
      }) .catch(() => {});
    },
    // 创建账号
    createAccount(){
      this.userId = 0;
      this.accountDialogType = 'add';
      this.addDialogForm = true;
    },
    cancellationBtn() {
      this.addDialogForm = false;
      this.resetPasswordVisible = false;
    },
    // 编辑角色
    editRole(row){
      this.userId = row.userId;
      this.accountDialogType = 'edit';
      this.addDialogForm = true;
    },
    // 删除角色
    deleteRole(row){
      let param = {
        userId:row.userId
      }
      apiDeleteAccount(param).then((res) => {
        if (res.code == 0) {
          this.$message.success('删除成功');
          this.getAccountList();
        }else {
           this.$message({
              message: res.message,
              type: 'error'
            })
          }
      }) .catch(() => {});
    },
    // 启用/停用
    disableRole(row){
      let param = {
        userId:row.userId,
        userStatus:row.userStatus===0?1:0
      }
      apiSwitchAccountStatus(param).then((res) => {
        if (res.code == 0) {
          this.$message.success(`${row.userStatus===0 ? '停用' : '启用'}成功`);
          this.getAccountList();
        }else {
          this.$message({
              message: res.message,
              type: 'error'
            })
          }
      }) .catch(() => {});
    },
    // 重置密码
    resetPassword(row) {
      this.userId = row.userId;
      this.resetPasswordVisible = true;
    },
  }
}
</script>

<style lang="scss" scoped>
.main-box {
  background: rgba(255, 255, 255, 1);
  border-radius: 4px;
  padding: 15px 20px;
  .searchMy ::v-deep  .el-form-item__label {
    margin-left: 20px;
    padding: 0;
  }
  .searchMy ::v-deep  .el-input__inner {
    border-radius: 0 4px 4px 0;
  }
  .searchMy ::v-deep  .el-date-editor {
    width: 100%;
  }
  .search-title {
    display: table-cell;
    padding: 0 10px;
    text-align: center;
    border: 1px solid #dcdfe6;
    height: 30px;
    line-height: 30px;
    vertical-align: middle;
    border-right: none;
    border-radius: 4px 0 0 4px;
    color: #333333;
    white-space: nowrap;
  }
  .searchMy ::v-deep  .el-select {
    display: table-cell;
    width: 100%;
  }
  .searchMy ::v-deep  .el-form-item__content {
    width: 100%;
  }
  .searchMy ::v-deep  .el-form-item--small.el-form-item {
    margin-bottom: 10px;
    width: 32%;
  }
  .searchMy ::v-deep  .el-form-item--small .el-form-item__content {
    line-height: 30px;
    width: 100%;
  }
  .searchMy ::v-deep  .el-input-group__prepend {
    background: none;
    color: #333333;
    padding: 0 10px;
  }
  .Fsearch {
    padding: 0px 20px 10px;
    font-weight: 500;
    .searchMsg {
      font-weight: 700;
      width: 200px;
    }
    .sign {
      display: inline-table;
      width: 3px;
      height: 13px;
      background: linear-gradient(360deg, #1d69c4 1%, #8bbdfc 99%);
      border-radius: 2px;
      margin-right: 8px;
    }
  }
  .searchBtn {
    float: right;
    text-align: right;
    padding-right: 20px;
  }
  .btn-group {
    display: flex;
    justify-content: flex-end;
  }
  ::v-deep  .pagination-container {
    margin: 15px 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
}
</style>
