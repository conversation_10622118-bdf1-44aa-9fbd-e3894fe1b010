<template>
  <div class="boxDiv">
    <el-row>
      <el-form
        :model="goodsBasicComVo"
        :rules="goodsBasicComVoRules"
        ref="goodsBasicComVo"
        label-width="130px"
        class="demo-ruleForm"
      >
        <el-row>
          <el-col :span="12">
            <el-form-item label="商品编码:" prop="barcode">
              <el-input
                v-model.trim="goodsBasicComVo.barcode"
                placeholder
                disabled
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="productName">
              <template slot="label">
                <el-tooltip v-if="!disabled" effect="dark" placement="top">
                  <template #content>
                    1、中西成药、原料药类商品录入药品的通用名称。
                    <br />2、中药材、中药饮片类商品录入的商品名称应当与最新版《中国药典》或各省级炮制规范中收载的名称一致，不得使用别名。
                    <br />3、医疗器械类商品按照医疗器械注册证或备案凭证上的产品名称录入商品名称。
                    <br />4、非药类商品录入采用批准文件、备案凭证或说明书、商品标签上的全名，如需加注其它相关内容的统一用半角括号加注在后。
                  </template>
                  <i class="el-icon-warning-outline"></i>
                </el-tooltip>
                <span>商品名称:</span>
              </template>
              <el-input
                v-model.trim="goodsBasicComVo.productName"
                placeholder="支持输入文字、数字、字母及特殊字符"
                :disabled="disabled || goodsBasicComVo.standardProductId !== ''"
                @change="getProductNameChange"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              :label="
                goodsBasicComVo.isInstrument ? '医疗器械名称:' : '通用名称:'
              "
              prop="commonName"
            >
              <el-input
                v-model.trim="goodsBasicComVo.commonName"
                placeholder="支持输入文字、数字、字母及特殊字符"
                :disabled="disabled || goodsBasicComVo.standardProductId !== ''"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item>
              <template slot="label">
                <el-tooltip v-if="!disabled" effect="dark" placement="top">
                  <template #content
                    >商品助记码供客户查询使用，系统默认为商品名称首字母拼接，请勿随意修改</template
                  >
                  <i class="el-icon-warning-outline"></i>
                </el-tooltip>
                <span>商品助记码:</span>
              </template>
              <el-input
                v-model.trim="goodsBasicComVo.zjm"
                placeholder="支持输入数字、字母"
                :disabled="disabled || shopConfig.shopPatternCode === 'ybm'"
                type="text"
                onkeyup="value=value.replace(/[^\w\.\/]/ig,'')"
                @blur="goodsBasicComVo.zjm = $event.target.value"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="brand">
              <template slot="label">
                <el-tooltip v-if="!disabled" effect="dark" placement="top">
                  <template #content>
                    1、优先录入®标识名称。
                    <br />2、若无带®标识，则录入生产企业名称，录入原则为“去头去尾”；录入信息时需去除生产企业名称中代表地域（省、市、县、区）和
                    “集团”、“药业”、“制药”、“制药厂”、“股份”、“有限公司”等字眼。
                    <br />3、会根据品牌配置不同专区，请谨慎填写。
                  </template>
                  <i class="el-icon-warning-outline"></i>
                </el-tooltip>
                <span>品牌:</span>
              </template>
              <el-input
                v-model.trim="goodsBasicComVo.brand"
                placeholder="支持输入文字、数字、字母及特殊字符"
                :disabled="disabled || goodsBasicComVo.standardProductId !== ''"
                @change="getBrandChange"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="showName">
              <template slot="label">
                <el-tooltip v-if="!disabled" effect="dark" placement="top">
                  <template #content>
                    1、该字段为APP端及PC端客户可见名称，请谨慎填写。
                    <br />2、药品和中药应该包含商品名称内容，非药类商品录入采用批准文件、备案凭证或说明书、商品标签上的全名，如需加注其它相关内容的统一用半角括号加注在后。
                  </template>
                  <i class="el-icon-warning-outline"></i>
                </el-tooltip>
                <span>展示名称:</span>
              </template>
              <el-input
                v-model="goodsBasicComVo.showName"
                placeholder="支持输入文字、数字、字母及特殊字符"
                :disabled="disabled || goodsBasicComVo.standardProductId !== ''"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="code">
              <template slot="label">
                <el-tooltip v-if="!disabled" effect="dark" placement="top">
                  <template #content> 无69码可录入0 </template>
                  <i class="el-icon-warning-outline" />
                </el-tooltip>
                <span>商品条码（69）码:</span>
              </template>

              <el-input
                v-model.trim="goodsBasicComVo.code"
                placeholder="支持输入数字"
                :disabled="disabled || shopConfig.shopPatternCode === 'ybm'"
                type="text"
                onkeyup="value=value.replace(/[^\d]/g,'')"
                @blur="goodsBasicComVo.code = $event.target.value"
              />
            </el-form-item>
          </el-col>

          <el-col
            v-if="
              erpFirstCategoryId === '100004' ||
              erpFirstCategoryId === '100010' ||
              erpFirstCategoryId === '262683'
            "
            :span="12"
          >
            <el-form-item label="别名:">
              <el-input
                v-model.trim="goodsBasicComVo.aliasName"
                placeholder="请输入别名"
                :disabled="disabled"
                type="text"
              />
            </el-form-item>
          </el-col>

          <el-col
            v-if="erpFirstCategoryId !== '100004' && erpFirstCategoryId !== '100010' && erpFirstCategoryId !== '262683'"
            :span="12"
          >
            <el-form-item
              :prop="
                goodsBasicComVo.isInstrument
                  ? 'instrumentNumber'
                  : 'approvalNumber'
              "
            >
              <template slot="label">
                <el-tooltip v-if="!disabled" effect="dark" placement="top">
                  <template #content>
                    根据批准文件、说明书录入。“养生中药”、“配方饮片”、“中药材”无批准文号可填-
                  </template>
                  <i class="el-icon-warning-outline" />
                </el-tooltip>
                <span>{{
                  goodsBasicComVo.isInstrument
                    ? '医疗器械注册证或备案凭证编号:'
                    : '批准文号:'
                }}</span>
              </template>
              <el-input
                v-model="
                  goodsBasicComVo[
                    goodsBasicComVo.isInstrument
                      ? 'instrumentNumber'
                      : 'approvalNumber'
                  ]
                "
                placeholder="支持输入文字、数字、字母及特殊字符"
                :disabled="disabled || goodsBasicComVo.standardProductId !== ''"
                @change="handleChangeInstrumentNumberOrApprovalNumber"
              />
              <div
                v-if="goodsBasicComVo.isInstrument"
                style="font-size: 12px; color: #ff2400; line-height: 14px"
              >
                <p>
                  第一类医疗器械品种填写医疗器械备案凭证编号，第二、三类医疗器械品种填写医疗器械注册证编号
                </p>
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="12" v-if="goodsBasicComVo.isInstrument">
            <el-form-item>
              <template slot="label">
                <!-- <el-tooltip v-if="!disabled" effect="dark" placement="top">
                  <template #content>
                    根据批准文件、说明书录入。“养生中药”、“配方饮片”、“中药材”无批准文号可填-
                  </template>
                  <i class="el-icon-warning-outline" />
                </el-tooltip> -->
                <span>医疗器械注册证:</span>
              </template>
              <div style="display: flex; align-items: center">
                <div
                  style="
                    display: flex;
                    align-items: center;
                    color: #606266;
                    font-size: 12px;
                  "
                >
                  有效期至
                  <el-form-item
                    :prop="
                      goodsBasicComVo.instrumentImageValid
                        ? 'instrumentLicenseEffect'
                        : 'noRequired'
                    "
                  >
                    <el-date-picker
                      v-model="goodsBasicComVo.instrumentLicenseEffect"
                      value-format="yyyy-MM-dd"
                      placeholder="有效期"
                      type="date"
                      style="width: 200px; margin-left: 10px"
                      :picker-options="instrumentOption"
                      :disabled="disabled"
                    />
                  </el-form-item>
                </div>
                <p
                  style="
                    color: #1890ff;
                    cursor: pointer;
                    margin: 0;
                    margin-left: 10px;
                  "
                  @click="showInstrumentViewer = true"
                >
                  查看示例
                </p>
                <el-dialog
                  title="医疗器械注册证示例"
                  width="70%"
                  @close="showInstrumentViewer = false"
                  :visible.sync="showInstrumentViewer"
                >
                  <div style="display: flex">
                    <div style="flex: 1">
                      <p>《第一类医疗器械备案凭证》</p>
                      <img
                        width="100%"
                        src="@/assets/image/product/instrument1.jpeg"
                        alt
                      />
                    </div>
                    <div style="flex: 1">
                      <p>《医疗器械注册证》</p>
                      <img
                        width="100%"
                        src="@/assets/image/product/instrument2.jpeg"
                        alt
                      />
                    </div>
                  </div>
                </el-dialog>
              </div>
              <div style="display: flex; align-items: center; margin-top: 10px">
                <el-upload
                  ref="myUploaderInstrument"
                  :disabled="disabled"
                  :class="{
                    hide:
                      disabled ||
                      goodsBasicComVo.instrumentLicenseImageList.isUpload
                  }"
                  action
                  :http-request="
                    (file) => uploadImg(file, 'instrumentLicenseImageList')
                  "
                  :before-upload="beforeAvatarUpload"
                  :on-remove="
                    (file) => {
                      return handleRemove(
                        file,
                        goodsBasicComVo.instrumentLicenseImageList.urlVal,
                        'instrumentLicenseImageList'
                      )
                    }
                  "
                  :limit="goodsBasicComVo.instrumentLicenseImageList.maxImg"
                  :file-list="goodsBasicComVo.instrumentLicenseImageList.urlVal"
                  :on-preview="handlePictureCardPreview"
                  name="files"
                  list-type="picture-card"
                  accept=".jpg, .jpeg, .png, .JPG, .JPEG"
                  class="avatar-uploader"
                >
                  <!-- :class="{ hide: !disabled || goodsBasicComVo.imagesList.isUpload }"
                  class="avatar-uploader"-->
                  <!-- <img v-if="goodsBasicComVo.imagesList" :src="goodsBasicComVo.imagesList" class="avatar" /> -->
                  <div style="margin-top: 5px">
                    <i class="el-icon-plus">
                      <div class="avatar-uploader-icon">上传图片</div>
                    </i>
                  </div>
                </el-upload>
              </div>
              <div style="font-size: 12px; color: #ff2400; line-height: 14px">
                <!-- <p>第一类医疗器械品种填写医疗器械备案凭证编号，第二、三类医疗器械品种填写医疗器械注册证编号</p> -->
                <p>
                  支持jpeg、png、jpg格式，宽度需大于等于800 px、大小不超过2M
                </p>
                <p>
                  依据《医疗器械网络销售监督管理办法》第十条要求：第一类医疗器械品种需上传《第一类医疗器械备案凭证》，第二、三类医疗器械品种需上传《医疗器械注册证》。
                </p>
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="12" v-if="goodsBasicComVo.isInstrument">
            <el-form-item>
              <template slot="label">
                <!-- <el-tooltip v-if="!disabled" effect="dark" placement="top">
                  <template #content>
                    根据批准文件、说明书录入。“养生中药”、“配方饮片”、“中药材”无批准文号可填-
                  </template>
                  <i class="el-icon-warning-outline" />
                </el-tooltip> -->
                <span style="color: #f56c6c">*</span>
                <span>生产许可证号或备案凭证编号:</span>
              </template>
              <div style="display: flex; align-items: center">
                <el-form-item
                  :prop="
                    !manufacturingLicenseNoALLDisable
                      ? 'manufacturingLicenseNo'
                      : 'noRequired'
                  "
                >
                  <el-input
                    v-model.trim="goodsBasicComVo.manufacturingLicenseNo"
                    placeholder="请输入"
                    style="width: 140px"
                    :disabled="disabled"
                  />
                </el-form-item>
                <div
                  style="
                    display: flex;
                    align-items: center;
                    margin-left: 10px;
                    color: #606266;
                    font-size: 12px;
                  "
                >
                  有效期至：
                  <el-form-item
                    :prop="
                      !goodsBasicComVo.manufacturingImageValid ||
                      manufacturingLicenseNoALLDisable
                        ? 'noRequired'
                        : 'manufacturingLicenseEffect'
                    "
                  >
                    <el-date-picker
                      v-model="goodsBasicComVo.manufacturingLicenseEffect"
                      value-format="yyyy-MM-dd"
                      placeholder="有效期"
                      type="date"
                      style="width: 140px"
                      :picker-options="instrumentOption"
                      :disabled="disabled || (manufacturingLicenseNoALLDisable && erpFirstCategoryId !== '100005')"
                    />
                  </el-form-item>
                </div>
                <p
                  style="
                    color: #1890ff;
                    cursor: pointer;
                    margin: 0;
                    margin-left: 10px;
                  "
                  @click="showManufacturingViewer = true"
                >
                  查看示例
                </p>
                <el-dialog
                  title="医疗器械生产备案凭证或生产企业许可证示例"
                  width="70%"
                  @close="showManufacturingViewer = false"
                  :visible.sync="showManufacturingViewer"
                >
                  <div style="display: flex">
                    <div style="flex: 1">
                      <p>《第一类医疗器械生产备案凭证》</p>
                      <img
                        width="100%"
                        src="@/assets/image/product/manufacturing1.jpeg"
                        alt
                      />
                    </div>
                    <div style="flex: 1">
                      <p>《医疗器械生产许可证》</p>
                      <img
                        width="100%"
                        src="@/assets/image/product/manufacturing2.png"
                        alt
                      />
                    </div>
                  </div>
                </el-dialog>
              </div>
              <div style="display: flex; align-items: center; margin-top: 10px">
                <el-upload
                  ref="myUploaderInstrument"
                  :disabled="disabled || (manufacturingLicenseNoALLDisable && erpFirstCategoryId !== '100005')"
                  :class="{
                    hide:
                      disabled ||
                      goodsBasicComVo.manufacturingLicenseImageList.isUpload
                  }"
                  action
                  :http-request="
                    (file) => uploadImg(file, 'manufacturingLicenseImageList')
                  "
                  :before-upload="beforeAvatarUpload"
                  :on-remove="
                    (file) => {
                      return handleRemove(
                        file,
                        goodsBasicComVo.manufacturingLicenseImageList.urlVal,
                        'manufacturingLicenseImageList'
                      )
                    }
                  "
                  :limit="goodsBasicComVo.manufacturingLicenseImageList.maxImg"
                  :file-list="
                    goodsBasicComVo.manufacturingLicenseImageList.urlVal
                  "
                  :on-preview="handlePictureCardPreview"
                  name="files"
                  list-type="picture-card"
                  accept=".jpg, .jpeg, .png, .JPG, .JPEG"
                  class="avatar-uploader"
                >
                  <!-- :class="{ hide: !disabled || goodsBasicComVo.imagesList.isUpload }"
                  class="avatar-uploader"-->
                  <!-- <img v-if="goodsBasicComVo.imagesList" :src="goodsBasicComVo.imagesList" class="avatar" /> -->
                  <div style="margin-top: 5px">
                    <i class="el-icon-plus">
                      <div class="avatar-uploader-icon">上传图片</div>
                    </i>
                  </div>
                </el-upload>
              </div>
              <div style="font-size: 12px; color: #ff2400; line-height: 14px">
                <p>
                  第一类医疗器械需录入第一类医疗器械生产备案凭证，第二、三类医疗器械需录入医疗器械生产企业许可证
                </p>
                <p>
                  支持jpeg、png、jpg格式，宽度需大于等于800 px、大小不超过2M
                </p>
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="manufacturer">
              <template slot="label">
                <el-tooltip v-if="!disabled" effect="dark" placement="top">
                  <template #content>根据批准文件、说明书录入。</template>
                  <i class="el-icon-warning-outline"></i>
                </el-tooltip>
                <span>生产厂家:</span>
              </template>
              <el-input
                v-model.trim="goodsBasicComVo.manufacturer"
                placeholder="支持输入文字、数字、字母及特殊字符"
                :disabled="disabled || goodsBasicComVo.standardProductId !== ''"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="spec">
              <template slot="label">
                <el-tooltip v-if="!disabled" effect="dark" placement="top">
                  <template #content>根据批准文件、说明书录入。</template>
                  <i class="el-icon-warning-outline"></i>
                </el-tooltip>
                <span>{{
                  goodsBasicComVo.isInstrument ? '规格(型号)' : '规格:'
                }}</span>
              </template>
              <el-input
                v-model.trim="goodsBasicComVo.spec"
                placeholder="支持输入文字、数字、字母及特殊字符"
                :disabled="disabled || goodsBasicComVo.standardProductId !== ''"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12" v-if="goodsBasicComVo.isInstrument">
            <el-form-item
              prop="technicalRequirementNo"
              :rules="
                !goodsBasicComVo.technicalRequirementNoCanEmpty && [
                  {
                    required: true,
                    message: '产品技术要求编号不能为空',
                    trigger: 'blur'
                  }
                ]
              "
            >
              <template slot="label">
                <el-tooltip v-if="!disabled" effect="dark" placement="top">
                  <template #content
                    >请按照医疗器械注册证或备案凭证或包装上载明的对应信息录入</template
                  >
                  <i class="el-icon-warning-outline"></i>
                </el-tooltip>
                <span>产品技术要求编号</span>
              </template>
              <el-input
                v-model.trim="goodsBasicComVo.technicalRequirementNo"
                placeholder="请输入"
                :disabled="disabled"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col
            v-if="
              !goodsBasicComVo.isInstrument &&
              (erpFirstCategoryId === '100004' ||
                erpFirstCategoryId === '100010' ||
                erpFirstCategoryId === '262683')
            "
            :span="12"
          >
            <el-form-item
              :prop="
                !goodsBasicComVo.isInstrument ||
                goodsBasicComVo.producerCanEmpty
                  ? 'noRequired'
                  : 'producer'
              "
            >
              <template slot="label">
                <el-tooltip v-if="!disabled && erpFirstCategoryId !== '100005'" effect="dark" placement="top">
                  <template #content
                    >“养生中药”、“配方饮片”、“中药材”的产地请如实填写，无产地的商品无需填写</template
                  >
                  <i class="el-icon-warning-outline" />
                </el-tooltip>
                <span>产地:</span>
              </template>
              <el-input
                v-model.trim="goodsBasicComVo.producer"
                placeholder="支持输入文字、数字、字母及特殊字符"
                :disabled="disabled || goodsBasicComVo.standardProductId !== ''"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="mediumPackageNum">
              <template slot="label">
                <el-tooltip v-if="!disabled" effect="dark" placement="top">
                  <template #content
                    >客户加购时会参考中包装数量，请谨慎填写。</template
                  >
                  <i class="el-icon-warning-outline"></i>
                </el-tooltip>
                <span>中包装:</span>
              </template>
              <el-input
                v-model.trim="goodsBasicComVo.mediumPackageNum"
                placeholder="支持输入正整数"
                :disabled="disabled || shopConfig.shopPatternCode === 'ybm'"
                type="text"
                onkeyup="value=value.replace(/[^\d]/g,'')"
                @blur="goodsBasicComVo.mediumPackageNum = $event.target.value"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col
            v-if="
              !goodsBasicComVo.isInstrument &&
              erpFirstCategoryId !== '100004' &&
              erpFirstCategoryId !== '100010' &&
              erpFirstCategoryId !== '262683'
            "
            :span="12"
          >
            <el-form-item>
              <template slot="label">
                <el-tooltip v-if="!disabled" effect="dark" placement="top">
                  <template #content>
                    1、药品必须选择处方药/甲类OTC/乙类OTC。
                    <br />2、中药、器械、食品等非药品类的选无。
                  </template>
                  <i class="el-icon-warning-outline"></i>
                </el-tooltip>
                <span>处方类型:</span>
              </template>
              <el-radio-group v-model="goodsBasicComVo.drugClassification">
                <el-radio
                  :label="0"
                  :disabled="
                    disabled || goodsBasicComVo.standardProductId !== ''
                  "
                  >无</el-radio
                >
                <el-radio
                  :label="1"
                  :disabled="
                    disabled || goodsBasicComVo.standardProductId !== ''
                  "
                  >甲类OTC</el-radio
                >
                <el-radio
                  :label="2"
                  :disabled="
                    disabled || goodsBasicComVo.standardProductId !== ''
                  "
                  >乙类OTC</el-radio
                >
                <el-radio
                  :label="3"
                  :disabled="
                    disabled || goodsBasicComVo.standardProductId !== ''
                  "
                  >处方药Rx</el-radio
                >
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col
            v-if="
              !goodsBasicComVo.isInstrument &&
              erpFirstCategoryId !== '100004' &&
              erpFirstCategoryId !== '100010' &&
              erpFirstCategoryId !== '262683'
            "
            :span="12"
          >
            <el-form-item label="剂型:">
              <el-select
                class="select-info"
                v-model="goodsBasicComVo.dosageForm"
                placeholder="请选择"
                :disabled="disabled || goodsBasicComVo.standardProductId !== ''"
              >
                <el-option
                  v-for="item in dosageFormList"
                  :value="item.id"
                  :label="item.name"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="件装量:" prop="pieceLoading">
              <el-input
                v-model.trim="goodsBasicComVo.pieceLoading"
                placeholder="支持输入正整数"
                :disabled="disabled || shopConfig.shopPatternCode === 'ybm'"
                type="text"
                onkeyup="value=value.replace(/[^\d]/g,'')"
                @blur="goodsBasicComVo.pieceLoading = $event.target.value"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="term">
              <template slot="label">
                <el-tooltip v-if="!disabled" effect="dark" placement="top">
                  <template #content>
                    1、根据批准文件、说明书录入。
                    <br />2、无有效期品种录入。
                  </template>
                  <i class="el-icon-warning-outline"></i>
                </el-tooltip>
                <span>{{
                  goodsBasicComVo.isInstrument
                    ? '有效期/失效期'
                    : '有效期/保质期:'
                }}</span>
              </template>
              <el-input
                v-model.trim="goodsBasicComVo.term"
                placeholder="有效期/保质期格式为正整数加年月日或-或*，例如：24月”"
                :disabled="disabled"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="包装单位:" prop="productUnit">
              <el-select
                class="select-info"
                v-model="goodsBasicComVo.productUnit"
                placeholder="请选择"
                :disabled="true"
              >
                <el-option
                  v-for="item in productUnitList"
                  :value="item.id"
                  :label="item.name"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="isSplit">
              <template slot="label">
                <el-tooltip v-if="!disabled" effect="dark" placement="top">
                  <template #content
                    >客户加购时会根据中包装是否可拆零加购，请谨慎填写。</template
                  >
                  <i class="el-icon-warning-outline"></i>
                </el-tooltip>
                <span>是否可拆零:</span>
              </template>
              <el-radio-group v-model.trim="goodsBasicComVo.isSplit">
                <el-radio :label="1" :disabled="disabled">是</el-radio>
                <el-radio :label="0" :disabled="disabled">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>

          <el-col
            v-if="
              erpFirstCategoryId === '100002' || erpFirstCategoryId === '100009'
            "
            :span="12"
          >
            <el-form-item label="上市许可持有人:">
              <el-input
                v-model.trim="goodsBasicComVo.marketAuthor"
                placeholder="请输入上市许可持有人"
                type="text"
                :disabled="disabled || goodsBasicComVo.standardProductId !== ''"
                />
              </el-form-item>
            </el-col>
          <el-col :span="12">
            <el-form-item>
              <template slot="label">
                <el-tooltip v-if="!disabled" effect="dark" placement="top">
                  <template #content>
                    <div>
                      常温：存储温度控制在10℃~30℃<br />
                      冷藏：存储温度控制在2℃~10℃<br />
                      冷冻：存储温度在0℃以下<br />
                      阴凉：存储温度不超过20℃<br />
                      凉暗：避光存储，且温度不超过20℃
                    </div>
                  </template>
                  <i class="el-icon-warning-outline"></i>
                </el-tooltip>
                <span>存储条件:</span>
              </template>
              <el-select
                class="select-info"
                v-model="goodsBasicComVo.storageCondition"
                placeholder="请选择"
                :disabled="disabled || shopConfig.shopPatternCode === 'ybm'"
              >
                <el-option
                  v-for="item in storageConditionList"
                  :value="item.id"
                  :label="item.name"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <!-- <el-col :span="12" v-if="!shopConfig.isFbp">
            <el-form-item>
              <template slot="label">
                <el-tooltip v-if="!disabled" effect="dark" placement="top">
                  <template #content>
                    <span
                      >1、导单系数=同一商品药帮忙单价/供应商系统单价。请输入大于0的数字，限3位小数</span
                    ><br />
                    <span
                      >2、系统会根据导单系数自动换算商品药帮忙售价和库存</span
                    ><br />
                    <span style="margin-left: 5px"
                      >2.1）商品药帮忙售价=供应商系统商品单价*导单系数，保留2位小数</span
                    ><br />
                    <span style="margin-left: 5px"
                      >2.2）商品药帮忙总库存=供应商系统商品可售库存/导单系数，向下取整数</span
                    >
                  </template>
                  <i class="el-icon-warning-outline"></i>
                </el-tooltip>
                <span>导单系数:</span>
              </template>
              <el-radio-group v-model="goodsBasicComVo.openFactor">
                <el-radio :label="0">关闭</el-radio>
                <el-radio :label="1"> 开启 </el-radio>
              </el-radio-group>
              <span
                v-if="goodsBasicComVo.openFactor === 1"
                style="
                  margin-left: 10px;
                  color: red;
                  font-size: 12px;
                  line-height: 12px;
                "
                >导单系数会影响商品的售卖价格和可售库存，开启设置后不要随意变更
              </span>
              <div style="margin-top: 10px">
                <el-form-item
                  label=""
                  :prop="
                    goodsBasicComVo.openFactor === 1 ? 'factor' : 'noRequired'
                  "
                >
                  <el-input
                    v-if="goodsBasicComVo.openFactor === 1"
                    v-model.trim="goodsBasicComVo.factor"
                    placeholder="导单系数=同一商品药帮忙单价/供应商系统单价，请输入大于0的数字，限3位小数"
                    onkeyup="value=value.replace(/^\D*(\d*(?:\.\d{0,3})?).*$/g,'$1')"
                    @change="handleChangeFactor"
                    :disabled="disabled"
                  />
                </el-form-item>
              </div>
            </el-form-item>
          </el-col> -->
          <el-col :span="12" v-if="!goodsBasicComVo.useStandardImage">
            <el-form-item label="商品图片:" prop="imagesList">
              <el-upload
                :disabled="disabled"
                :class="{
                  hide: disabled || goodsBasicComVo.imagesList.isUpload
                }"
                action
                :http-request="(file) => uploadImg(file, 'imagesList')"
                :before-upload="beforeAvatarUpload"
                :on-remove="
                  (file) => {
                    return handleRemove(
                      file,
                      goodsBasicComVo.imagesList.urlVal,
                      'imagesList'
                    )
                  }
                "
                ref="myUploader"
                :limit="goodsBasicComVo.imagesList.maxImg"
                :file-list="goodsBasicComVo.imagesList.urlVal"
                :on-preview="handlePictureCardPreview"
                name="files"
                list-type="picture-card"
                accept=".jpg, .jpeg, .png, .JPG, .JPEG"
                class="avatar-uploader"
              >
                <!-- :class="{ hide: !disabled || goodsBasicComVo.imagesList.isUpload }"
                class="avatar-uploader"-->
                <!-- <img v-if="goodsBasicComVo.imagesList" :src="goodsBasicComVo.imagesList" class="avatar" /> -->
                <div style="margin-top: 5px">
                  <i class="el-icon-plus">
                    <div class="avatar-uploader-icon">上传图片</div>
                  </i>
                </div>
              </el-upload>
              <div style="font-size: 12px; color: #ff2400">
                支持jpeg、png、jpg、gif格式，最多支持5张。图片宽高保持1:1，800px≤宽高≤1500px，大小不超过2M
              </div>
            </el-form-item>
          </el-col>
          <el-col v-if="!goodsBasicComVo.useStandardImage" :span="12">
            <el-form-item label="详情图片:" prop="instrutionImagesList">
              <el-upload
                :disabled="disabled"
                :class="{
                  hide:
                    disabled || goodsBasicComVo.instrutionImagesList.isUpload
                }"
                action
                :http-request="
                  (file) => uploadImg(file, 'instrutionImagesList')
                "
                :before-upload="beforeAvatarUpload"
                :on-remove="
                  (file) => {
                    return handleRemove(
                      file,
                      goodsBasicComVo.instrutionImagesList.urlVal,
                      'instrutionImagesList'
                    )
                  }
                "
                ref="myUploader"
                :limit="goodsBasicComVo.instrutionImagesList.maxImg"
                :file-list="goodsBasicComVo.instrutionImagesList.urlVal"
                :on-preview="handlePictureCardPreview"
                name="files"
                list-type="picture-card"
                accept=".jpg, .jpeg, .png, .JPG, .JPEG"
                class="avatar-uploader"
              >
                <div style="margin-top: 5px">
                  <i class="el-icon-plus">
                    <div class="avatar-uploader-icon">上传图片</div>
                  </i>
                </div>
              </el-upload>
              <div style="font-size: 12px; color: #ff2400">
                支持jpeg、png、jpg、gif格式，最多支持5张。800px≤图片宽度≤1500px，高≤1500px，大小不超过2M
              </div>
            </el-form-item>
          </el-col>
          <el-col v-if="goodsBasicComVo.imageUrlStandard" :span="12">
            <el-form-item label="标品主图:" prop="imagesList">
              <div style="display: flex; align-items: center; flex-wrap: wrap">
                <div style="display: flex">
                  <!-- <img
                    v-for="(item, index) in goodsBasicComVo.imageUrlStandard.split(',')"
                    :key="index"
                    :src="`${bigImgUrlPrefix}${item}`"
                    style="display: block;width: 64px;height: 64px;margin-right: 10px"
                  > -->
                  <el-image
                    v-for="(
                      item, index
                    ) in goodsBasicComVo.imageUrlStandard.split(',')"
                    :key="index"
                    style="
                      display: block;
                      width: 64px;
                      height: 64px;
                      margin-right: 10px;
                    "
                    :src="`${bigImgUrlPrefix}${item}`"
                    :preview-src-list="[`${bigImgUrlPrefix}${item}`]"
                    @click.prevent
                  />
                </div>
                <el-checkbox
                  v-model="goodsBasicComVo.useStandardImage"
                  :disabled="disabled"
                  @change="handleChangeUseStandardImage"
                >
                  使用标品图片（有新包装时，同步更新）
                </el-checkbox>
              </div>
            </el-form-item>
          </el-col>
          <el-col v-if="goodsBasicComVo.instrutionImageUrlStandard" :span="12">
            <el-form-item
              label="标品库详情图片:"
              prop="instrutionImageUrlStandard"
            >
              <div style="display: flex; flex-wrap: wrap">
                <el-image
                  v-for="(
                    item, index
                  ) in goodsBasicComVo.instrutionImageUrlStandard.split(',')"
                  :key="index"
                  style="
                    display: block;
                    width: 64px;
                    height: 64px;
                    margin-right: 10px;
                  "
                  :src="`${bigDescImgUrlPrefix}${item}`"
                  :preview-src-list="[`${bigDescImgUrlPrefix}${item}`]"
                  @click.prevent
                />
                <!-- <img
                  v-for="(item, index) in goodsBasicComVo.instrutionImageUrlStandard.split(',')"
                  :key="index"
                  :src="`${bigDescImgUrlPrefix}${item}`"
                  style="display: block;width: 64px;height: 64px;margin-right: 10px"
                > -->
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <el-divider />
    </el-row>
    <el-dialog :visible.sync="dialogVisible">
      <img width="100%" :src="dialogImageUrl" alt />
    </el-dialog>
  </div>
</template>
<script>
import {
  apiUploadProductImage,
  apiConfig,
  apiProductUnitList,
  apiAttributeOptions,
  apiCamelChars
} from '@/api/product'
import { mapState } from 'vuex'

export default {
  name: 'GoodsBasicCom',
  props: {
    basicData: {
      type: Object,
      default() {
        return {}
      }
    },
    formModel: {
      type: Object,
      default() {
        return {}
      }
    }
  },
  data() {
    // https://yapi.int.ybm100.com/project/891/interface/api/89928
    return {
      isApparatus:undefined,
      erpFirstCategoryId: '', // 一级分类
      instrumentOption: { disabledDate: (time) => time.getTime() < Date.now() },
      disabled: false,
      dosageFormList: [],
      productUnitList: [],
      storageConditionList: [],
      businessEndTimeDisabled: false,
      dialogImageUrl: '',
      dialogVisible: false,
      propsData: {},
      bigDescImgUrlPrefix: '',
      bigImgUrlPrefix: '',
      smallDescImgUrlPrefix: '',
      smallImgUrlPrefix: '',
      showInstrumentViewer: false,
      showManufacturingViewer: false,
      goodsBasicComVo: {
        aliasName: '', // 别名
        marketAuthor: '', // 上市许可持有人
        factor: '',
        openFactor: 0,
        instrumentImageValid: false,
        manufacturingImageValid: false,
        instrumentNumber: '',
        manufacturingLicenseEffect: '',
        manufacturingLicenseNo: '',
        instrumentLicenseEffect: '',
        isInstrument: false,
        instrutionImageUrlStandard: '', // 标品库详情图片
        effectCanEmpty: false, // 有效期是否可以为空
        producerCanEmpty: false, // 产地是否可以为空
        imageUrlStandard: '', // 标品图片
        useStandardImage: false, // 使用标品图片
        barcode: '', // 商品编号
        productName: '', //商品名称
        commonName: '', //通用名称
        zjm: '', //助记码
        brand: '', // 品牌
        showName: '', //展示名称
        code: '', //商品条码（69）码
        approvalNumber: '', //批准文号
        manufacturer: '', //生产厂家
        spec: '', // 规格
        technicalRequirementNo: '', // 产品技术要求编号
        technicalRequirementNoCanEmpty: '', // 产品技术要求编号是否可以为空,true可以为空
        drugClassification: 0, // 处方类型
        mediumPackageNum: '', // 中包装
        pieceLoading: '', // 件装量
        dosageForm: '', //剂型
        standardProductId: '', //标准库id
        productUnit: '', // 包装单位
        term: '', // 有效期/保质期
        storageCondition: '', // 储存条件
        isSplit: 0, // 是否可拆零（0:不可拆零；1:可拆零）
        producer: '', // 产地
        imagesList: {
          urlVal: [],
          maxImg: 5,
          isUpload: false
        }, // 商品图片（短路径，需要拼接前缀才可展示）
        instrutionImagesList: {
          urlVal: [],
          maxImg: 5,
          isUpload: false
        }, // 详情（短路径，需要拼接前缀才可展示）
        instrumentLicenseImageList: {
          urlVal: [],
          maxImg: 5,
          isUpload: false
        },
        manufacturingLicenseImageList: {
          urlVal: [],
          maxImg: 5,
          isUpload: false
        }
      },
      goodsBasicComVoRules: {
        marketAuthor: [
          { required: true, message: '上市许可持有人不能为空', trigger: 'blur' }
        ],
        factor: [
          { required: true, message: '导单系数不能为空', trigger: 'blur' }
        ],
        barcode: [
          { required: true, message: '商品编码不能为空', trigger: 'blur' }
        ],
        productName: [
          { required: true, message: '商品名称不能为空', trigger: 'blur' }
        ],
        commonName: [
          { required: true, message: '商品通用名不能为空', trigger: 'blur' }
        ],
        zjm: [
          { required: true, message: '商品助记码不能为空', trigger: 'change' }
        ],
        showName: [
          { required: true, message: '展示名称不能为空', trigger: 'blur' }
        ],
        code: [
          { required: true, message: '商品条码不能为空', trigger: 'blur' },
          { max: 20, message: '商品条码最多输入20个数字' }
        ],
        approvalNumber: [
          { required: true, message: '批准文号不能为空', trigger: 'blur' }
        ],
        instrumentNumber: [
          {
            required: true,
            message: '医疗器械注册证或备案凭证编号不能为空',
            trigger: 'blur'
          }
        ],
        manufacturingLicenseNo: [
          {
            required: true,
            message: '生产许可证号或备案凭证编号格式不正确',
            trigger: 'blur'
          }
        ],
        manufacturer: [
          { required: true, message: '生产厂家不能为空', trigger: 'blur' }
        ],
        spec: [{ required: true, message: '规格不能为空', trigger: 'blur' }],
        producer: [
          { required: true, message: '产地不能为空', trigger: 'blur' }
          // {
          //   pattern: /^[\u4e00-\u9fa5-]{0,}$/,
          //   message: '仅支持输入文字及特殊字符“-”',
          // },
        ],
        drugClassification: [
          { required: true, message: '处方类型不能为空', trigger: 'blur' }
        ],
        mediumPackageNum: [
          { required: true, message: '中包装不能为空', trigger: 'blur' }
        ],
        dosageForm: [
          { required: true, message: '剂型不能为空', trigger: 'blur' }
        ],
        productUnit: [
          { required: true, message: '单位不规范，请重新选择', trigger: 'blur' }
        ],
        term: [
          { required: true, message: '有效期/保质期”不能为空', trigger: 'blur' },
          {
            validator(rule, value, callback) {
              console.log('value99999',rule,value);
                if ((/^[1-9]\d*(年|月|日|-|\*)$/).test(value)) {
                  callback();
                } else {
                  callback(new Error('有效期/保质期格式为正整数加年月日或-或*，例如：24月'));
                }
              },
              trigger: 'blur'
          }
        ],
        noRequired: [{ required: false }],
        instrumentLicenseEffect: [
          {
            required: true,
            message: '医疗器械注册证及有效期至不能为空',
            trigger: 'blur'
          }
        ],
        manufacturingLicenseEffect: [
          {
            required: true,
            message: '生产许可证书及有效期至不能为空',
            trigger: 'blur'
          }
        ],
        storageCondition: [
          { required: true, message: '储存条件不能为空', trigger: 'blur' }
        ],
        isSplit: [
          { required: true, message: '是否可拆零不能为空', trigger: 'blur' }
        ]
      },
      manufacturingLicenseNoALLDisable: false,
      categoryFirstId:''
    }
  },
  watch: {
    basicData: {
      immediate: true,
      handler(newVale) {
        this.$nextTick(() => {
          this.propsData = JSON.parse(JSON.stringify(newVale))
          console.log(**********, this.propsData)
          this.initData()
        })
      }
    },
    formModel: {
      handler(newVal) {
        var base = JSON.parse(JSON.stringify(newVal))
        this.disabled = base.isEdit == 1
      },
      immediate: true,
      deep: true
    },
  },
  created() {
    apiConfig().then((res) => {
      if (res.data) {
        this.bigDescImgUrlPrefix = res.data.bigDescImgUrlPrefix // 详情大图地址前缀
        this.bigImgUrlPrefix = res.data.bigImgUrlPrefix // 商品大图地址前缀
        this.smallDescImgUrlPrefix = res.data.smallDescImgUrlPrefix // 详情小图地址前缀
        this.smallImgUrlPrefix = res.data.smallImgUrlPrefix // 商品小图地址前缀
      }
    })
    apiProductUnitList().then((res) => {
      if (res.data) {
        this.productUnitList = res.data || []
      }
    })
    apiAttributeOptions({ fieldType: 'dosageForm' }).then((res) => {
      if (res.data) {
        this.dosageFormList = res.data || []
      }
    })
    apiAttributeOptions({ fieldType: 'storageCondition' }).then((res) => {
      if (res.data) {
        this.storageConditionList = res.data || []
      }
    })
  },
  computed: { ...mapState('app', ['shopConfig']) },
  mounted() {},
  methods: {
    handleChangeFactor(val) {
      this.goodsBasicComVo.factor = val
    },
    handleChangeInstrumentNumberOrApprovalNumber(val) {
      if (this.goodsBasicComVo.isInstrument) {
        this.goodsBasicComVo.approvalNumber = val

        // 是否包含 “许”或“进”或“国械备”字
        if (new RegExp('国械备|进|许').test(val)) {
          this.manufacturingLicenseNoALLDisable = true
        } else {
          this.manufacturingLicenseNoALLDisable = false
        }
      }
    },
    handleChangeUseStandardImage(val) {
      if (val) {
        const h = this.$createElement
        this.$msgbox({
          title: '提示',
          message: h('p', null, [
            h(
              'span',
              null,
              '使用标品图片后，将删除原有图片（包括主图与详情图），确定使用标品图片吗？'
            )
          ]),
          confirmButtonText: '确定'
        })
          .then(() => {
            this.goodsBasicComVo.useStandardImage = true
          })
          .catch(() => {
            this.goodsBasicComVo.useStandardImage = false
          })
      } else {
        this.goodsBasicComVo.useStandardImage = false
      }
    },
    initData() {
      this.goodsBasicComVo.barcode = this.propsData.barcode
      this.goodsBasicComVo.productName = this.propsData.productName
      this.goodsBasicComVo.commonName = this.propsData.commonName
      this.goodsBasicComVo.zjm = this.propsData.zjm
      this.goodsBasicComVo.brand = this.propsData.brand
      this.goodsBasicComVo.showName = this.propsData.showName
      this.goodsBasicComVo.code = this.propsData.code
      this.goodsBasicComVo.approvalNumber = this.propsData.approvalNumber
      this.goodsBasicComVo.instrumentNumber =
        this.propsData.approvalNumber || ''
      this.goodsBasicComVo.manufacturer = this.propsData.manufacturer
      this.goodsBasicComVo.spec = this.propsData.spec
      this.goodsBasicComVo.technicalRequirementNo =
        this.propsData.technicalRequirementNo // 产品技术要求编号
      this.goodsBasicComVo.technicalRequirementNoCanEmpty =
        this.propsData.technicalRequirementNoCanEmpty // 产品技术要求编号是否可以为空,true可以为空
      this.goodsBasicComVo.producer = this.propsData.producer
      this.goodsBasicComVo.drugClassification =
        this.propsData.drugClassification
      this.goodsBasicComVo.mediumPackageNum = this.propsData.mediumPackageNum
      this.goodsBasicComVo.pieceLoading = this.propsData.pieceLoading
      this.goodsBasicComVo.dosageForm = this.propsData.dosageForm
      this.goodsBasicComVo.standardProductId =
        this.propsData.standardProductId || ''
      this.goodsBasicComVo.productUnit = this.propsData.productUnit
      this.goodsBasicComVo.term = this.propsData.term
      this.goodsBasicComVo.storageCondition = this.propsData.storageCondition
      this.goodsBasicComVo.isSplit = this.propsData.isSplit
      this.goodsBasicComVo.useStandardImage = this.propsData.useStandardImage
      this.goodsBasicComVo.imageUrlStandard =
        this.propsData.imageUrlStandard || ''
      this.goodsBasicComVo.instrutionImageUrlStandard =
        this.propsData.instrutionImageUrlStandard || ''
      this.goodsBasicComVo.effectCanEmpty = this.propsData.effectCanEmpty
      this.goodsBasicComVo.producerCanEmpty = this.propsData.producerCanEmpty
      this.goodsBasicComVo.isInstrument = this.propsData.isInstrument
      this.goodsBasicComVo.instrumentLicenseEffect =
        this.propsData.instrumentLicenseEffect
      this.goodsBasicComVo.manufacturingLicenseNo =
        this.propsData.manufacturingLicenseNo
      this.goodsBasicComVo.manufacturingLicenseEffect =
        this.propsData.manufacturingLicenseEffect
      this.goodsBasicComVo.instrumentImageValid =
        this.propsData.instrumentImageValid
      this.goodsBasicComVo.manufacturingImageValid =
        this.propsData.manufacturingImageValid
      this.goodsBasicComVo.openFactor = this.propsData.openFactor
      this.goodsBasicComVo.factor = this.propsData.factor
      this.goodsBasicComVo.marketAuthor = this.propsData.marketAuthor
      this.goodsBasicComVo.aliasName = this.propsData.aliasName
      this.erpFirstCategoryId = this.propsData.erpFirstCategoryId
      this.categoryFirstId = this.propsData.categoryFirstId
      console.log(111111111, this.categoryFirstId)
      if(this.goodsBasicComVo.erpFirstCategoryId === '100005'){
        this.isApparatus = false
      }else{
        this.isApparatus = true
      }

      var urlVal = []
      const urlAryImagesList = this.propsData.imagesList
      if (urlAryImagesList) {
        urlAryImagesList.forEach((itAry, indAry) => {
          if (itAry) {
            urlVal.push({
              name: itAry,
              url: `${this.bigImgUrlPrefix}${itAry}`,
              uid: '_' + indAry
            })
          }
        })
        this.goodsBasicComVo.imagesList = {
          urlVal,
          maxImg: 5,
          isUpload:
            this.propsData.imagesList && this.propsData.imagesList.length === 5
        }
      } else {
        this.goodsBasicComVo.imagesList = {
          urlVal: [],
          maxImg: 5,
          isUpload: false
        }
      }
      urlVal = []
      const urlAryInstrutionImagesList = this.propsData.instrutionImagesList
      if (urlAryInstrutionImagesList) {
        urlAryInstrutionImagesList.forEach((itAry, indAry) => {
          if (itAry) {
            urlVal.push({
              name: itAry,
              url: `${this.bigDescImgUrlPrefix}${itAry}`,
              uid: '_' + indAry
            })
          }
        })
        this.goodsBasicComVo.instrutionImagesList = {
          urlVal,
          maxImg: 5,
          isUpload:
            this.propsData.instrutionImagesList &&
            this.propsData.instrutionImagesList.length === 5
        }
      } else {
        this.goodsBasicComVo.instrutionImagesList = {
          urlVal: [],
          maxImg: 5,
          isUpload: false
        }
      }
      const arr = []
      const urlAryInstrumentImagesList =
        this.propsData.instrumentLicenseImageList
      if (urlAryInstrumentImagesList && urlAryInstrumentImagesList.length) {
        urlAryInstrumentImagesList.forEach((itAry, indAry) => {
          if (itAry) {
            arr.push({
              name: itAry,
              url: `${this.bigDescImgUrlPrefix}${itAry}`,
              uid: `_${indAry}`
            })
          }
        })
        this.goodsBasicComVo.instrumentLicenseImageList = {
          urlVal: arr,
          maxImg: 5,
          isUpload:
            this.propsData.instrumentLicenseImageList &&
            this.propsData.instrumentLicenseImageList.length === 5
        }
      } else {
        this.goodsBasicComVo.instrumentLicenseImageList = {
          urlVal: [],
          maxImg: 5,
          isUpload: false
        }
      }

      const arr1 = []
      const urlAryManufacturingLicenseImageList =
        this.propsData.manufacturingLicenseImageList
      if (urlAryManufacturingLicenseImageList) {
        urlAryManufacturingLicenseImageList.forEach((itAry, indAry) => {
          if (itAry) {
            arr1.push({
              name: itAry,
              url: `${this.bigDescImgUrlPrefix}${itAry}`,
              uid: `_${indAry}`
            })
          }
        })
        this.goodsBasicComVo.manufacturingLicenseImageList = {
          urlVal: arr1,
          maxImg: 5,
          isUpload:
            this.propsData.manufacturingLicenseImageList &&
            this.propsData.manufacturingLicenseImageList.length === 5
        }
      } else {
        this.goodsBasicComVo.manufacturingLicenseImageList = {
          urlVal: [],
          maxImg: 5,
          isUpload: false
        }
      }

      // 是否包含 “许”或“进”或“国械备”字
      if (
        new RegExp('国械备|进|许').test(this.goodsBasicComVo.instrumentNumber)
      ) {
        this.manufacturingLicenseNoALLDisable = true
      } else {
        this.manufacturingLicenseNoALLDisable = false
      }
    },
    async uploadImg(file, idStr) {
      const that = this
      let resetFile = await this.resetImg(file.file)
      const params = {
        file: resetFile,
        type: idStr == 'imagesList' ? 1 : 2
      }
      try {
        const res = await apiUploadProductImage(params)
        if (res.code === 0) {
          if (res.data) {
            file.onSuccess()
            if (idStr == 'imagesList') {
              that.goodsBasicComVo.imagesList.urlVal.push({
                name: res.data,
                url: `${this.bigImgUrlPrefix}${res.data}`,
                uid: file.file.uid
              })
              that.goodsBasicComVo.imagesList.urlVal.length ===
              that.goodsBasicComVo.imagesList.maxImg
                ? (that.goodsBasicComVo.imagesList.isUpload = true)
                : (that.goodsBasicComVo.imagesList.isUpload = false)
            } else {
              that.goodsBasicComVo[idStr].urlVal.push({
                name: res.data,
                url: `${this.bigDescImgUrlPrefix}${res.data}`,
                uid: file.file.uid
              })
              that.goodsBasicComVo[idStr].urlVal.length ===
              that.goodsBasicComVo[idStr].maxImg
                ? (that.goodsBasicComVo[idStr].isUpload = true)
                : (that.goodsBasicComVo[idStr].isUpload = false)
            }
          }
        } else {
          file.onError()
          this.$message.error(res.message)
        }
      } catch (err) {
        file.onError()
      }
    },
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.url
      this.dialogVisible = true
    },
    handleRemove(file, fileList, idStr) {
      fileList.forEach((item, index) => {
        if (item.name === file.name) {
          fileList.splice(index, 1)
        }
      })
      // if (fileList.length < 1) {
      //   this.$set(this.mergeData, `${idStr}imgList`, '');
      // }
      if (idStr) {
        this.goodsBasicComVo[idStr].isUpload = false
      }
    },
    beforeAvatarUpload(file) {
      console.log(file)
      const isJPG = file.type === 'image/jpeg' || file.type === 'image/png'
      const isLt2M = file.size / 1024 / 1024 < 2

      if (!isJPG) {
        this.$message.error('上传图片只能是 JPG、PNG 格式!')
        return false
      }
      if (!isLt2M) {
        this.$message.error('上传图片大小不能超过 2MB!')
        return false
      }
      return isJPG && isLt2M
    },
    // 商品名称
    getProductNameChange(value) {
      if (!this.goodsBasicComVo.brand) {
        this.goodsBasicComVo.showName = this.goodsBasicComVo.productName
      } else {
        this.goodsBasicComVo.showName =
          this.goodsBasicComVo.brand + ' ' + this.goodsBasicComVo.productName
      }
      this.getCamelChars()
    },
    // 品牌
    getBrandChange(value) {
      if (!this.goodsBasicComVo.productName) {
        this.goodsBasicComVo.showName = this.goodsBasicComVo.brand
      } else {
        this.goodsBasicComVo.showName =
          this.goodsBasicComVo.brand + ' ' + this.goodsBasicComVo.productName
      }
    },
    // 获取助记码首字母
    getCamelChars() {
      const params = { name: this.goodsBasicComVo.productName }
      apiCamelChars(params).then((res) => {
        if (res.code == 0) {
          this.goodsBasicComVo.zjm = res.data || ''
        } else {
          this.$message.error(res.message)
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
.el-button {
  padding: 8px 20px;
}
.el-button.is-circle {
  padding: 7px;
  border: none;
}
.boxDiv {
  width: 100%;
  height: 100%;
  padding: 0 0 0 10px;
  // overflow-y: auto;
  .select-info {
    width: 100%;
  }

  .span-tip {
    display: inline-block;
    width: 12px;
    height: 12px;
    font-size: 10px;
    border: 1px solid #999999;
    color: #999999;
    text-align: center;
    line-height: 12px;
    border-radius: 50%;
    margin-right: 2px;
    margin-left: -2px;
  }

  ::v-deep   .el-form {
    width: 100%;
    .el-select {
      margin-right: 14px;
    }

    .el-form-item__label {
      font-size: 12px;
      line-height: 30px;
    }

    .el-form-item__content {
      line-height: 30px;
    }

    .el-input__inner {
      line-height: 30px;
      height: 30px;
      font-size: 12px;
    }

    .el-input__icon {
      line-height: 30px;
    }
  }

  ::v-deep   .el-table__body .el-form-item {
    padding: 20px 0;
  }

  .addrForm .el-form-item {
    display: inline-block;
  }

  ::v-deep  .avatar-uploader .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 2px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    background: rgba(0, 0, 0, 0.02);
    width: 64px;
    height: 64px;
    line-height: 64px;
  }
  ::v-deep  .avatar-uploader .el-upload:hover {
    border-color: #409eff;
  }
  ::v-deep  .avatar-uploader .el-upload__tip {
    margin-top: 0;
    color: #999999;
    font-size: 12px;
  }
  .avatar-uploader-icon {
    opacity: 1;
    font-size: 12px;
    font-family: PingFangSC, PingFangSC-Regular;
    font-weight: 400;
    text-align: left;
    color: rgba(0, 0, 0, 0.65);
    line-height: 14px;
  }
  .avatar {
    width: 64px;
    height: 64px;
    display: block;
  }
  ::v-deep  .avatar-uploader .el-upload-list--picture-card .el-upload-list__item {
    width: 64px;
    height: 64px;
  }
  ::v-deep   .hide .el-upload--picture-card {
    display: none;
  }
}

.boxDiv::-webkit-scrollbar {
  width: 0 !important;
}
</style>
