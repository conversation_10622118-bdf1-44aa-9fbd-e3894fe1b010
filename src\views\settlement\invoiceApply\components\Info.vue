<template>
  <div class="invoiceInfo-box" style="padding-right: 40px">
    <el-form
      ref="basic"
      label-width="120px"
      size="small"
      label-position="right"
      :rules="basicRules"
      :model="basic"
    >
      <el-form-item label="发票类型：" class="width50" prop="invoiceType" ref="invoiceType">
        <el-radio-group v-model.trim="basic.invoiceType">
          <el-radio :disabled="isDetail" type="radio" label="4">全电普票</el-radio>
          <el-radio :disabled="isDetail" type="radio" label="5">全电专票</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="发票抬头：" class="width50" prop="invoiceTitle" ref="invoiceTitle">
        <el-input
          :disabled="true"
          type="text"
          v-model.trim="basic.invoiceTitle"
          placeholder="请输入"
        ></el-input>
      </el-form-item>
      <el-form-item label="地址：" class="width50" prop="invoiceAddress" ref="invoiceAddress">
        <el-input
          :disabled="isDetail"
          type="text"
          maxlength="80"
          v-model.trim="basic.invoiceAddress"
          placeholder="请输入"
        ></el-input>
      </el-form-item>
      <el-form-item label="电话：" class="width50" prop="invoicePhone" ref="invoicePhone">
        <el-input
          :disabled="isDetail"
          type="text"
          v-model.trim="basic.invoicePhone"
          placeholder="请输入"
        ></el-input>
      </el-form-item>
      <el-form-item label="税号：" class="width50" prop="taxNo" ref="taxNo">
        <el-input
          :disabled="isDetail"
          type="text"
          minlength="15"
          maxlength="20"
          v-model.trim="basic.taxNo"
          placeholder="请输入"
        ></el-input>
      </el-form-item>
      <el-form-item label="开户行：" class="width50" prop="bankName" ref="bankName">
        <el-input
          :disabled="isDetail"
          type="text"
          maxlength="160"
          v-model.trim="basic.bankName"
          placeholder="请输入"
        ></el-input>
      </el-form-item>
      <el-form-item label="开户账号：" class="width50" prop="bankAccount" ref="bankAccount">
        <el-input
          :disabled="isDetail"
          type="text"
          v-model.trim="basic.bankAccount"
          placeholder="请输入"
          autocomplete="off"
        ></el-input>
      </el-form-item>
      <el-form-item
        label="收件地址："
        class="width50"
        prop="contactorAddress"
        ref="contactorAddress"
      >
        <el-input
          :disabled="isDetail"
          type="text"
          maxlength="80"
          v-model.trim="basic.contactorAddress"
          placeholder="请输入"
        ></el-input>
      </el-form-item>
      <el-form-item label="收件人：" class="width50" prop="contactor" ref="contactor">
        <el-input
          :disabled="isDetail"
          type="text"
          maxlength="20"
          v-model.trim="basic.contactor"
          placeholder="请输入"
        ></el-input>
      </el-form-item>
      <el-form-item label="联系电话：" class="width50" prop="contactorPhone" ref="contactorPhone">
        <el-input
          :disabled="isDetail"
          type="text"
          maxlength="11"
          v-model.trim="basic.contactorPhone"
          placeholder="请输入"
        ></el-input>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" v-if="!isDetail" size="medium" @click="submitForm('basic')"
          >保存</el-button
        >
        <el-button v-permission="['settle_invoice_edit']" v-if="isDetail" type="primary" size="medium" @click="isDetail = !isDetail"
          >编辑</el-button
        >
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import { saveInvoiceInfo, getInvoiceInfo } from '@/api/settlement/invoiceApply'

export default {
  name: 'Info',
  data() {
    return {
      activeTab:'info',
      basic: {
        invoiceType: '4', // 发票类型 0-增值税电子普通发票 1-增值税纸质专用发票    ->  4:全电普票   5:全电专票
        invoiceTitle: '', // 抬头
        invoiceAddress: '', // 发票地址
        invoicePhone: '', // 电话
        taxNo: '', // 税号
        bankName: '', // 开户行
        bankAccount: '', // 开户账号
        contactorAddress: '', // 收件地址
        contactor: '', // 收件人
        contactorPhone: '' // 联系电话
      },
      basicRules: {
        invoiceType: [{ required: true,message: '请选择发票类型' }],
        invoiceTitle: [
          { required: true, message: '请输入发票抬头' },
          // {
          //   max: 80,
          //   pattern: /^[\u4e00-\u9fa5A-Za-z0-9]+$/,
          //   message: '只允许输入汉字、字母、数字，最大长度为80个字符'
          // }
        ],
        invoiceAddress: [
          { required: true, message: '请输入地址' },
          {
            max: 80,
            message: '最大长度为80个字符'
          }
        ],
        invoicePhone: [
          { required: true, message: '请输入电话' },
          { pattern: /^[0-9\\-]*$/, message: '请输入正确的电话号' }
        ],
        taxNo: [
          { required: true, message: '请输入税号' },
          {
            min: 15,
            max: 20,
            pattern: /^[\d|\w]{15,20}$/,
            message: '只允许输入字母、数字，不少于15个字符，不超过20个字符'
          }
        ],
        bankName: [
          { required: true, message: '请输入开户行' },
          {
            max: 160,
            pattern: /^[\u4e00-\u9fa5A-Za-z0-9]+$/,
            message: '只允许输入汉字、字母、数字，且最大长度为160个字符'
          }
        ],
        bankAccount: [
          { required: true, message: '请输入开户账号' },
          {
            // max: 20,
            pattern: /^[0-9]*$/,
            message: '只允许输入数字'
          }
        ],
        contactorAddress: [
          { required: true, message: '请输入收件地址' },
          {
            max: 80,
          }
        ],
        contactor: [
          { required: true, message: '请输入收件人' },
          {
            max: 20,
            pattern: /^[\u4e00-\u9fa5]*$/,
            message: '只允许输入汉字且最长不超过20个字符'
          }
        ],
        contactorPhone: [
          { required: true, message: '请输入联系电话' },
          {
            max: 11,
          }
        ]
      },
      isDetail: false // 是否可编辑
    }
  },
  created() {
    this.getInfo()
  },
  methods: {
    // 获取发票信息
    getInfo() {
      getInvoiceInfo({})
        .then((res) => {
          if (res.code === '200') {
            if(JSON.stringify(res.data) != "{}"){
              this.isDetail = true
              Object.keys(this.basic).forEach((key) => {
                if(res.data[key] != null){
                  this.basic[key] = res.data[key]
                }
              }, this)
              this.basic.invoiceType = String(this.basic.invoiceType)
            }
          } else {
            this.$message.error({
              message: res.errorMsg || res.msg,
              customClass: 'center-msg'
            })
          }
        })
        .catch(() => {})
    },
    // 保存发票信息
    submitForm(formName) {
      this.$refs[formName].validate((valid, rule) => {
        if (valid) {
          const sendAllData = { ...this.basic }
          saveInvoiceInfo(sendAllData).then((res) => {
            if (res.code === '200') {
              this.$message.success({
                message: '保存成功',
                type: 'success',
              })
              this.getInfo()
            } else {
              this.$message.error({
                message: res.errorMsg,
                type: 'error',
              })
            }
          })
        } else {
          const validAry = rule[Object.keys(rule)[0]]
          const msgName = validAry[0].message
          this.$message.warning({
            message: msgName,
            type: 'warning',
            offset: '60'
          })
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
.invoiceInfo-box {
  padding: 0 15px 15px;
  .width50 {
    width: 60%;
  }
  .width100 {
    width: 100%;
  }
  .width25 {
    width: 23%;
    display: inline-block;
    margin-right: 5px;
  }
  .padding10 {
    padding-bottom: 18px;
  }
}
</style>
