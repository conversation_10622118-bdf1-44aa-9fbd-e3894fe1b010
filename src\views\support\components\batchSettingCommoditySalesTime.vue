<template>
  <el-dialog
    v-loading="loading"
    :title="title"
    :visible="dialogVisible"
    width="680px"
    :before-close="handleClose">
    <div class="contentBox">
      <div v-if="isEdit" style="margin-bottom:24px">
        <el-row style="margin-bottom: 12px">
          <el-col :span="12">商品编码：{{ detail.barcode }}</el-col>
          <el-col :span="12">商品ERP编码：{{ detail.erpCode }}</el-col>
        </el-row>
        <el-row style="margin-bottom: 12px">
          <el-col :span="12">商品名称：{{ detail.showName }}</el-col>
          <el-col :span="12">规格：{{ detail.spec }}</el-col>
        </el-row>
        <el-row style="margin-bottom: 12px">
          <el-col :span="12">厂家：{{ detail.manufacturer }}</el-col>
        </el-row>
      </div>
      <div class="title">设置说明</div>
      <p style="margin-bottom: 20px">
        1、商家最多可以给1000个商品设置指定的售卖时间区间 <br>
        2、当商品状态为销售中时：客户只能在指定的售卖时间内购买商品；超出售卖时间，客户将无法购买 <br>
        3、当商品状态为“销售中”以外的其他状态时，客户均不可购买该商品<br>
        4、每个商品每天最多设置3组指定售卖时间<br>
        5、仅支持为普通商品/批购包邮设置售卖时间，不支持拼团品。
      </p>
      <div class="title">设置售卖时间</div>
      <el-radio v-model="saleTimeType" label="1">售卖时间计划</el-radio>
      <el-radio v-model="saleTimeType" label="2">自定义</el-radio>
      <template v-if="saleTimeType==='1'">
        <p>
          <el-select class="small_30" v-model="configId" placeholder="请选择计划" @change="planChange">
            <el-option
              v-for="(item, index) in saleTimePlanOptions"
              :key="index"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </p>
        <p v-html="saleTimePlanText"></p>
      </template>
      <template v-else>
        <div style="margin: 10px 0">
          <div class="timeSelectBox" v-for="(item,index) in time" :key="index">
            <span style="margin-right: 8px">{{ formatWeek(item.week) }}</span>
            <div class="timeSelect">
              <div style="margin-bottom: 8px" v-for=" (li,ind) in item.time" :key="ind">
                <el-select clearable v-model="li.startHour" placeholder="">
                  <el-option
                    v-for="item in hourOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value">
                  </el-option>
                </el-select>
                <span style="margin: 0 5px">:</span>
                <el-select clearable v-model="li.startMinute" placeholder="">
                  <el-option
                    v-for="item in startMinuteOptions"
                    :key="item"
                    :label="item"
                    :value="item">
                  </el-option>
                </el-select>
                <span style="margin: 0 8px">至</span>
                <el-select clearable v-model="li.endHour" placeholder="">
                  <el-option
                    v-for="item in hourOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value">
                  </el-option>
                </el-select>
                <span style="margin: 0 5px">:</span>
                <el-select clearable v-model="li.endMinute" placeholder="">
                  <el-option
                    v-for="item in endMinuteOptions"
                    :key="item"
                    :label="item"
                    :value="item">
                  </el-option>
                </el-select>
                <template v-if="ind===0&&item.time.length<3">
                  <i @click="addAndDelTime(index,ind)" class="icon el-icon-circle-plus-outline"></i>
                </template>
                <template v-else-if="ind!==0">
                  <i @click="addAndDelTime(index,ind)" class="icon el-icon-remove-outline"></i>
                </template>
              </div>
            </div>
          </div>
        </div>
      </template>
      <div v-if="!isEdit">
        <div class="title">设置商品</div>
        <div>
          <el-upload
            ref="upload"
            class="upload-demo"
            action
            accept=".xls, .xlsx"
            :on-change="handleChange"
            :on-remove="handleRemove"
            :http-request="httpRequest"
            :auto-upload="false"
            :file-list="fileList">
            <el-button size="small" type="primary">点击上传</el-button>
            <el-button style="margin-left: 10px" slot="tip" type="primary" size="small" @click="downloadImportTemplate">
              下载导入模板
            </el-button>
            <div slot="tip" class="el-upload__tip" v-if="!fileList.length">未选择文件</div>
          </el-upload>
        </div>
      </div>
    </div>
    <span slot="footer" class="dialog-footer">
    <el-button @click="handleClose">取 消</el-button>
    <el-button type="primary" @click="submit" :loading="submitLoading">提 交</el-button>
  </span>
  </el-dialog>
</template>

<script>
import {
  SelectSaleTimeList,
  batchSaveTime,
  saleTimeSkuInfo,
  updateSkuTime,
  batchUpdateInfoFromExcel
} from '@/api/product/index.js'

export default {
  name: "batchSettingCommoditySalesTime",
  props: {
    config: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      loading: false,
      title: '批量设置商品售卖时间',
      isEdit: false,
      dialogVisible: true,
      saleTimePlanOptions: [],
      saleTimePlanText: '',
      saleTimeType: '1',
      configId: '',
      time: [
        {
          week: 1,
          time: [
            {
              startHour: '',
              startMinute: '',
              endHour: '',
              endMinute: ''
            }
          ]
        },
        {
          week: 2,
          time: [
            {
              startHour: '',
              startMinute: '',
              endHour: '',
              endMinute: ''
            }
          ]
        },
        {
          week: 3,
          time: [
            {
              startHour: '',
              startMinute: '',
              endHour: '',
              endMinute: ''
            }
          ]
        },
        {
          week: 4,
          time: [
            {
              startHour: '',
              startMinute: '',
              endHour: '',
              endMinute: ''
            }
          ]
        },
        {
          week: 5,
          time: [
            {
              startHour: '',
              startMinute: '',
              endHour: '',
              endMinute: ''
            }
          ]
        },
        {
          week: 6,
          time: [
            {
              startHour: '',
              startMinute: '',
              endHour: '',
              endMinute: ''
            }
          ]
        },
        {
          week: 7,
          time: [
            {
              startHour: '',
              startMinute: '',
              endHour: '',
              endMinute: ''
            }
          ]
        }
      ],
      hourOptions: [],
      startMinuteOptions: ['00', '05', '10', '15', '20', '25', '30', '35', '40', '45', '50', '55'],
      endMinuteOptions: ['00', '05', '10', '15', '20', '25', '30', '35', '40', '45', '50', '55', '59'],
      productIds: [],
      fileList: [],
      detail: {
        showName: '',
        manufacturer: '',
        spec: '',
        barcode: '',
        erpCode: '',
        configId: '',
        time: [],
        timeStr: '',
        status: ''
      },
      submitLoading: false
    }
  },
  created() {
    for (let i = 0; i < 24; i++) {
      this.hourOptions.push({label: i < 10 ? '0' + i : String(i), value: i < 10 ? '0' + i : String(i)})
    }
    if (this.config.type === 'edit') {
      this.title = '编辑商品售卖时间'
      this.isEdit = true
      this.getSaleTimeSkuInfo()
    }
    this.getSelectSaleTimeList()
  },
  methods: {
    formatWeek(s) {
      const num = Number(s)
      let str = ''
      switch (num) {
        case 1:
          str = '周一'
          break
        case 2:
          str = '周二'
          break
        case 3:
          str = '周三'
          break
        case 4:
          str = '周四'
          break
        case 5:
          str = '周五'
          break
        case 6:
          str = '周六'
          break
        case 7:
          str = '周日'
          break
      }
      return str
    },
    async getSaleTimeSkuInfo() {
      this.loading = true
      try {
        const res = await saleTimeSkuInfo({id: this.config.id})
        if (res && res.code === 0) {
          Object.keys(res.data).forEach(key => {
            this.$set(this.detail, key, res.data[key])
            // this.detail[key] = res.data[key]
          })
        }
        if (this.detail.configId) {
          this.saleTimeType = '1'
          this.configId = this.detail.configId
          this.planChange(this.detail.configId)
        } else {
          this.saleTimeType = '2'
          const list = this.time.map(item => {
            const obj = this.detail.time.find(li => li.week === item.week)
            return {
              ...item,
              ...obj
            }
          })
          this.time = list
        }
      } catch (e) {
        console.log(e)
      }
      this.loading = false
    },
    async getSelectSaleTimeList() {
      const res = await SelectSaleTimeList()
      if (res && res.code === 0) {
        this.saleTimePlanOptions = res.data
      }
    },
    planChange(id) {
      let time = []
      this.saleTimePlanOptions.forEach(li => {
        if (li.id == id) {
          time = li.time
        }
      })
      if (time.length > 0) {
        let strList = []
        time.forEach(item => {
          const s = []
          if (item.time && Array.isArray(item.time)) {
            item.time.forEach(li => {
              s.push(`${li.startHour}:${li.startMinute}至${li.endHour}:${li.endMinute}`)
            })
          }
          strList.push(`${this.formatWeek(item.week)} ：${s.join('，')}`)
        })
        this.saleTimePlanText = strList.join('<br>')
      }
    },
    addAndDelTime(index, ind) {
      if (ind === 0) {
        //增加
        this.time[index].time.push({
          startHour: '',
          startMinute: '',
          endHour: '',
          endMinute: ''
        })
      } else {
        this.time[index].time.splice(ind, 1)
      }
    },
    handleClose() {
      this.$emit('update:batchSettingCommoditySalesTimeDialogVisible', false)
    },
    async submit() {
      console.log('submit')
      if (this.isEdit) {
        const params = {
          id: this.config.id
        }
        if (this.saleTimeType === '1') {
          params.configId = this.configId
        } else {
          params.time = this.time
        }
        this.submitLoading = true
        const res = await updateSkuTime(params)
        if (res && res.code === 0) {
          this.$message.success('修改成功！');
          this.$emit('queryList')
          this.handleClose()
        } else {
          this.$message.error(res.message || '修改失败！')
        }
        this.submitLoading = false
      } else {
        if (this.fileList.length > 0) {
          this.fileList.forEach(obj => {
            this.httpRequest(obj.raw)
          })
        } else {
          this.$message.warning('请上传商品并设置售卖时间！');
        }
      }
    },
    async httpRequest(param) {
      const fd = new FormData() // FormData 对象
      fd.append('file', param) // 文件对象
      if (this.saleTimeType === '1') {
        if (!this.configId) {
          this.$message.warning('请上传商品并设置售卖时间！');
          return false
        }
        fd.append('configId', this.configId)
      } else {
        let f = false
        this.time.forEach(item => {
          item.time.forEach(li => {
            if (Object.values(li).join('')) {
              f = true
            }
          })
        })
        if (!f) {
          this.$message.warning('请上传商品并设置售卖时间！');
          return false
        }
        fd.append('timeStr', JSON.stringify(this.time))
      }
      try {
        this.submitLoading = true
        const res = await batchSaveTime(fd)
        if (res && res.code === 0) {
          const {success, error, errorFileName, errorFileUrl} = res.data
          const baseUrl = process.env.VUE_APP_BASE_API
          let str = ''
          if (error && error > 0) {
            str = '<p>' + success + '个商品设置成功！' + error + '个商品设置失败！</p><p><a style="color: #4183d5;text-decoration:underline;cursor: pointer" href="' + baseUrl + errorFileUrl + '" download="' + errorFileName + '">导出设置失败商品</a></p>'
          } else {
            str = '<p>' + success + '个商品设置成功！'
          }
          this.$alert(str, '设置结果通知', {
            dangerouslyUseHTMLString: true,
            callback: () => {
              this.$emit('queryList')
              this.handleClose()
            }
          })
        } else {
          this.$message.error(res.message || '导入失败，请重新上传！');
        }
      } catch (err) {
        console.log(err)
      }
      this.submitLoading = false
    },
    handleChange(file, fileList) {
      if (file.status === 'ready') {
        this.fileList = [file]
      }
    },
    handleRemove() {
      this.fileList = []
    },
    downloadImportTemplate() {
      const a = document.createElement('a')
      const filename = '批量设置商品售卖时间导入模版.xlsx'
      let domain = process.env.VUE_APP_UPLOAD_API
      a.href = domain + '/pop/temp/批量设置商品售卖时间导入模版.xlsx'
      a.download = filename
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
    }
  }
}
</script>

<style scoped lang="scss">
.el-button {
  padding: 8px 20px;
}

::v-deep   .el-dialog__body {
  padding-top: 15px;
}

::v-deep   .contentBox {
  //height: 100%;
  background: #fff;
  margin-bottom: 10px;

  .title {
    font-weight: 500;
    text-align: left;
    color: #000000;
    line-height: 14px;
    margin-bottom: 8px;
  }

  .title:before {
    content: '';
    display: inline-block;
    width: 3px;
    height: 13px;
    background: linear-gradient(360deg, #1d69c4 1%, #8bbdfc 99%);
    border-radius: 2px;
    margin-right: 8px;
    vertical-align: middle;
  }

  .el-upload-list {
    width: 300px;
  }

  .small_30 {
    .el-input__inner {
      width: 100% !important;
    }
  }

  .timeSelectBox {
    display: flex;
    align-items: baseline;
    justify-content: left;
  }

  .icon {
    vertical-align: middle;
    font-size: 25px;
    line-height: 30px;
    margin-left: 12px;
    cursor: pointer;
  }

  .timeSelect, .small_30 {
    display: inline-block;

    .el-input__inner {
      width: 80px;
      height: 30px;
      line-height: 30px;
    }

    .el-input__icon {
      line-height: 30px;
    }
  }
}
</style>
