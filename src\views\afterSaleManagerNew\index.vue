<template>
  <div>
    <i-form v-model="formData" @search="pagination.pageNum=1;search();"></i-form>
    <i-table :data="[{id: 1, xxx:[{}]}, {id: 2, xxx:[{}]}]"></i-table>
    <el-pagination
      style="position:relative;left:100%;display:inline-block;transform:translateX(-100%)"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      :current-page="pagination.pageNum"
      :page-sizes="[20, 50, 100]"
      :page-size="pagination.pageSize"
      background
      layout="total, sizes, prev, pager, next, jumper"
      :total="pagination.total">
    </el-pagination>
  </div>
</template>
<script>
import iForm from './form.vue'
import iTable from './table.vue'
export default {
  name: 'afterSaleManager',
  components: {
    iForm,
    iTable
  },
  data() {
    return {
      formData: {},
      pagination: {
				pageNum: 10,
				pageSize: 20,
				total: 0
			},
      loading: false,
    }
  },
  activated() {
    console.log(666);
  },
  methods: {
    /**
     *
     * @param { {key: string, value: any }[] } formItems
     */
    search(formItems) {
      if (formItems) {
        formItems.forEach(item => {
          this.formData[item.key] = item.value;
        })
      }
      const form = {
        ...this.formData,
        pageNum: this.pagination.pageNum,
        pageSize: this.pagination.pageSize,
      }
      if (form.time1 === null) {
        form['startCreateTime'] = '';
        form['endCreateTime'] = '';
      } else if (typeof form.time1[0] === 'string') {
        form['startCreateTime'] = new Date(form.time1[0]).getTime();
        form['endCreateTime'] = new Date(form.time1[1]).getTime();
      }
      if (form.time2 === null) {
        form['startTime'] = '';
        form['endTime'] = '';
      } else if (typeof form.time2[0] === 'string') {
        form['startTime'] = new Date(form.time2[0]).getTime();
        form['endTime'] = new Date(form.time2[1]).getTime();
      }
      delete form.time1;
      delete form.time2;
      //处理申请时间和退款时间
      console.log(form);
    },
    handleCurrentChange(pageNum) {
			this.pagination.pageNum = pageNum;
			this.search();
		},
		handleSizeChange(pageSize) {
			this.pagination.pageSize = pageSize;
			this.search();
		},
  }
}
</script>
<style scoped>
</style>
