<template>
  <div class="choose">
    <el-dialog
      title="手动绑定标品"
      :visible.sync="dialogFormVisible"
      width="80%"
      @close="handleColse"
      >
      <div class="divBox">
        <el-row>
          <el-col :span="6">
            通用名称：{{ row.commonNameStandard ? row.commonNameStandard : row.commonName }}
          </el-col>
          <el-col :span="6">
            规格：{{ row.specStandard ? row.specStandard : row.spec }}
          </el-col>
          <el-col :span="6">
            批准文号：{{ row.approvalNumberStandard ? row.approvalNumberStandard : row.approvalNumber }}
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="6">
            <!-- 生产厂家：{{ row.manufacturerStandard ? row.manufacturerStandard : row.source == 1 ? `商家ERP：${row.manufacturer}` : `商家：${row.manufacturer}` }} -->
            生产厂家：{{ row.manufacturerStandard ? row.manufacturerStandard : row.manufacturer }}
          </el-col>
          <el-col :span="6">
            产地：{{ row.producerStandard ? row.producerStandard : row.producer }}
          </el-col>
          <el-col :span="6">
            条码：{{ row.codeStandard ? row.codeStandard : row.code }}
          </el-col>
          <el-col :span="6">
            品牌：{{ row.brand ? row.brand : '-' }}
          </el-col>
        </el-row>
        <div class="contentBox">
          <div class="title">查询商品</div>
          <div class="topTip">注：您可以通过查询，从标准库中选择商品</div>
          <SearchForm
            ref="searchForm"
            :model="formModel"
            :form-items="formItems"
            :has-open-btn="false"
            @submit="handleFormSubmit"
            @reset="handleFormReset"
          />
        </div>
        <div>
          <div v-if="checkedName" class="selectBox">
            您当前选择的是：
            <span>{{ checkedName }}</span>
          </div>
          <div>
            <div v-if="goodsList&&goodsList.length">
              <ChooseList
                :prop-data="goodsList"
                :is-submit="isSubmit"
                :id="standardProductId"
                @chooseGoods="chooseGoods"
                @getSubmit="submitState"
              />
            </div>
            <div v-else class="noData">
              <p class="img-box">
                <!-- <img src="@/assets/image/marketing/noneImg.png" alt /> -->
              </p>
              <p v-if="isShow">没有找到匹配的商品</p>
            </div>
          </div>
        </div>
        <div class="page-box">
          <Pagination
            v-show="listQuery.total > 0"
            :total="listQuery.total"
            :page.sync="listQuery.pageNum"
            :limit.sync="listQuery.pageSize"
            @pagination="getList"
          />
        </div>
        <div class="bottomBtn">
          <el-button
            type="primary"
            size="small"
            :disabled="(selectLevelCategory || checkedName) ? false : true"
            @click="goDetails"
          >绑定标品</el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { meProduceList, apiValidProtectSkuAndForbiddenName, sendMathReSet } from '@/api/product';
import SearchForm from '@/components/searchForm';
import Pagination from '../../../components/Pagination/index.vue';
import ChooseList from '../components/chooseList';
import { searchItem, Deduplication } from '../config';

export default {
  name: 'ManualBindingSkuDialog',
  components: { SearchForm, Pagination, ChooseList },
  data() {
    return {
      selectId: '',
      row: {},
      dialogFormVisible: false,
      formModel: {
        code: '',
        productName: '',
        manufacturer: '',
        approvalNumber: '',
      },
      listQuery: {
        pageSize: 10,
        pageNum: 1,
        total: 0,
      },
      selectLevelCategory: null,
      formItems: searchItem.formItemsIn,
      checkedName: '',
      goodsList: [],
      itemData: {},
      isSubmit: false,
      isDisable: false,
      isShow: false,
    };
  },
  methods: {
    handleColse(){
      this.formModel = {
        code: '',
        productName: '',
        manufacturer: '',
        approvalNumber: '',
      };
    },
    submitState() {
      this.isSubmit = false;
    },
    handleParamsSearch(id,approvalNumber,commonName, row){
      this.row = row;
      if(approvalNumber !== '' && approvalNumber !== 0 && approvalNumber !== '-'){
        this.formModel.approvalNumber = approvalNumber
      }else{
        this.formModel.productName = commonName
      }
      if (id && typeof id !== 'object') {
        this.selectId = id;
      }
      this.checkedName = '';
      this.selectLevelCategory = null;
      this.itemData = {};
      this.isSubmit = true;
      this.isDisable = false;
      this.listQuery = {
        pageNum: 1,
        pageSize: 10,
        total: 0,
      };
      this.dialogFormVisible = true;
      this.getList()
    },
    handleFormSubmit() {
      this.getList()
    },
    handleFormReset() {
      this.formModel = {
        code: '',
        productName: '',
        manufacturer: '',
        approvalNumber: '',
      };
      this.handleFormSubmit();
    },
    loadingFun() {
      const loading = this.$loading({
        lock: true,
        text: 'Loading',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)',
      });
      return loading;
    },
    getList() {
      console.log(this.formModel);
      const params = { ...this.formModel };
      const { pageSize, pageNum } = this.listQuery;
      params.pageSize = pageSize;
      params.pageNum = pageNum;
      const load = this.loadingFun();
      meProduceList(params).then((res) => {
        load.close();
        if (res.code === 0) {
          //去重!!!!!!!!!!!!!!!!!!
          this.goodsList = res.data.list ? res.data.list : [];
          this.goodsList = Deduplication(this.goodsList);
          this.listQuery.total = res.data.total ? res.data.total : 0;
          this.isShow = true;
        } else {
          this.$message.error(res.message);
        }
      });
    },
    chooseGoods(data) {
      this.checkedName = data.showName ? (data.brand?data.brand:'') + ' '+data.showName+'/'+data.spec+'/'+data.manufacturer : '';
      this.selectLevelCategory = data;
      this.itemData = data ? data : {};
      this.isDisable = false;
      if (data.showName && data.specialVarieties) {
        this.checkedName = '';
        this.selectLevelCategory = null;
        this.isDisable = true;
        this.$message.error('商品为国家管控的特殊品种，无法创建');
      }
      else if(data.firstCategory === 100006){ // 选择标品的一级分类为商业赠品
        this.checkedName = '';
        this.selectLevelCategory = null;
        this.isDisable = true;
        this.$message.error('不能绑定商业赠品');
      }
      if (data.showName) {
        this.getValidForbiddenName(data.showName,data.standardProductId);
      }
    },
    getValidForbiddenName(showName, standardProductId) {
      const that = this;
      const params = {
        productName: showName,
        standId: standardProductId,
      };
      apiValidProtectSkuAndForbiddenName(params).then((res) => {
        if (res.code === 0) {
          const validForbiddenName = res.data ? true : false;
          if (validForbiddenName) {
            that.checkedName = '';
            that.selectLevelCategory = null;
            that.isDisable = true;
            that.$message.error(res.message);
          }
        } else {
          that.$message.error(res.message);
        }
      });
    },
    goDetails() {
      if (this.selectLevelCategory && this.selectId) {
        sendMathReSet({ id: this.selectId, standardProductId: this.selectLevelCategory.standardProductId }).then((res) => {
          if (res.code === 0) {
            // this.$message.success('选取成功')
            this.$message({
              type: 'success',
              message: res.message || '绑定成功',
              offset: 60,
            });
            this.dialogFormVisible = false;
            this.$emit('resetList');
          } else {
            // this.$message.error(res.message)
            this.$message({
              type: 'error',
              message: res.message,
              offset: 60,
            });
          }
        });
      } else {
        this.$message.error('查询参数异常');
      }
    },
  },
};
</script>

<style scoped lang="less">
  .divBox {
    padding-bottom: 52px;
    position: relative;
    .bottomBtn {
      text-align: center;
      line-height: 59px;
      width: calc(100%);
      height: 59px;
      background-color: #fff;
      // position: fixed;
      position: sticky;
      bottom: 0;
      z-index: 1;
      button {
        line-height: normal;
      }
    }
    .nameTitle {
      padding: 15px 15px 0;
      font-size: 20px;
      font-weight: bold;
      text-align: left;
      color: #333333;
    }
    .selectBox {
      padding: 0 20px 20px;
      font-size: 13px;
      font-weight: 800;
      text-align: left;
      color: #333333;
      span {
        color: #ff9500;
      }
    }
  }
  .contentBox {
    //height: 100%;
    padding: 16px 16px;
    background: #fff;
    .search-title {
      display: table-cell;
      padding: 0 10px;
      text-align: center;
      border: 1px solid #dcdfe6;
      height: 32px;
      line-height: 32px;
      vertical-align: middle;
      border-right: none;
      border-radius: 4px 0 0 4px;
      color: #333333;
      white-space: nowrap;
    }
    .span-tip {
      display: flex;
      justify-content: center;
      align-items: center;
    }
    ::v-deep  .el-select {
      display: table-cell;
      width: 165px;
    }
    ::v-deep  .el-input__inner {
      border-radius: 0 4px 4px 0;
    }
    .title {
      font-weight: 500;
      text-align: left;
      color: #000000;
      line-height: 14px;
      margin-bottom: 24px;
    }

    .title:before {
      content: '';
      display: inline-block;
      width: 3px;
      height: 13px;
      background: linear-gradient(360deg, #1d69c4 1%, #8bbdfc 99%);
      border-radius: 2px;
      margin-right: 8px;
      vertical-align: middle;
    }
    .topTip {
      height: 36px;
      background: rgba(255, 180, 0, 0.05);
      border-radius: 4px;
      margin-bottom: 15px;
      padding-left: 5px;
      font-size: 12px;
      font-family: PingFangSC, PingFangSC-Regular;
      font-weight: 400;
      text-align: left;
      color: #ff9500;
      line-height: 36px;
    }
    .searchForm {
      overflow: hidden;
    }

    .el-form-item {
      vertical-align: middle;
      margin-right: 16px;
      margin-bottom: 12px;
      border: 1px solid #dcdfe6;
      border-radius: 4px;

      ::v-deep   .el-form-item__label {
        font-size: 12px;
        width: 95px;
        height: 33px;
        line-height: 33px;
        border-right: 1px solid #dcdfe6;
      }

      ::v-deep   .el-form-item__content {
        width: 120px;

        .el-input__inner {
          border: none;
          font-size: 12px;
        }
      }
    }

    ::v-deep   .formItemTime .el-form-item__content {
      width: 354px;
    }

    .operation {
      margin-bottom: 12px;
    }

    .tips {
      opacity: 1;
      background: #fafafa;
      border-radius: 4px;
      padding: 8px 16px;
      margin-bottom: 10px;

      div {
        font-size: 14px;
        font-family: PingFangSC, PingFangSC-Regular;
        font-weight: 400;
        text-align: left;
        color: #333333;
        line-height: 24px;

        .el-button {
          padding: 0;
          line-height: 20px;
          border: none;
        }
      }
    }
  }
  .noData {
    width: 100%;
    height: 100%;
    min-height: 60px;
    text-align: center;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  ::v-deep   .el-input-group__prepend {
    background: none;
    color: #333333;
    padding: 0 10px;
  }
  </style>
