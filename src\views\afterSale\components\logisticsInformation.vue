<template>
  <el-dialog title="物流信息" :visible="dialogVisible" width="60%" @close="closeDialog">
    <p>
      <span style="margin-right:6px">快递单号：{{ invoiceInfo.expressNo }}</span>
      <!-- <el-button
        size="mini"
        type="primary"
        v-if="invoiceInfo.expressNo"
        @click="selectCopy(invoiceInfo.expressNo)"
      >复制</el-button>-->
      <i class="el-icon-document-copy" v-if="invoiceInfo.expressNo" @click="selectCopy(invoiceInfo.expressNo)" />
    </p>
    <p>
      <span>配送方式：{{ invoiceInfo.expressName }}</span>
    </p>
    <p>
      <span class="img-info">运单截图：</span>
      <el-image
        class="img-box"
        v-if="invoiceInfo.expressEvidence"
        :src="invoiceInfo.expressEvidence"
        :preview-src-list="[invoiceInfo.expressEvidence]"
        @click.prevent
      >
        <div slot="error" class="image-slot">
          <el-image src="https://oss-ec.ybm100.com/ybm/product/defaultPhoto.jpg" />
        </div>
      </el-image>
    </p>
  </el-dialog>
</template>
<script>
import { apiGetRefundExpress } from '@/api/afterSale/index';

export default {
  name: 'LogisticsInformation',
  props: {
    dialogVisible: {
      type: Boolean,
      default: false,
    },
    orderRefundId: {
      type: Number,
      default: null,
    },
  },
  data() {
    return { 
      invoiceInfo: {},
      bigImgUrlPrefix:process.env.VUE_APP_UPLOAD_API,
    };
  },
  mounted() {
    this.getInfo();
  },
  methods: {
    closeDialog() {
      this.$emit('cancelDialog');
    },
    getInfo() {
      const that = this;
      apiGetRefundExpress({ orderRefundId: this.orderRefundId }).then((res) => {
        if (res.code === 0) {
          // this.invoiceInfo = res.result;
          let invoiceInfo = res.result || {};
          if(invoiceInfo.expressEvidence){
            invoiceInfo.expressEvidence = `${that.bigImgUrlPrefix}${invoiceInfo.expressEvidence}`
          }
          that.invoiceInfo = invoiceInfo;
        }else{
          this.$message.error({
            message: res.result,
            offset: 100,
          });
        }
      });
    },
    selectCopy(data) {
      const oInput = document.createElement('input');
      oInput.value = data;
      document.body.appendChild(oInput);
      oInput.select(); // 选择对象;
      document.execCommand('Copy'); // 执行浏览器复制命令
      this.$message({
        message: '复制成功',
        type: 'success',
      });
      oInput.remove();
    },
  },
};
</script>
<style lang="scss" scoped>
p {
  display: flex;
  align-items: center;
  span {
    display: inline-block;
    overflow-x: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  i {
    cursor: pointer;
  }
}
.img-info {
  height: 70px;
}
.img-box {
  text-align: center;
  display: flex;
  justify-content: center;
  align-items: center;
  background: #ffffff;
  ::v-deep   .el-image {
    width: 60px;
    height: 60px;
  }
  ::v-deep   .el-image__inner {
    width: 60px;
    height: 60px;
  }
}
</style>
