<template>
  <div>
    <el-dialog
      :visible="changeExport"
      :close-on-click-modal="false"
      title="提示"
      custom-class="changeExport"
      @close="close"
    >
      <span>
        文件生成中，请到
        <span class="worderId" @click="goDetail('go')">文件下载中心</span> 页面进行查看和下载
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button size="small" type="primary" class="searchCondition" @click="goDetail()">关闭</el-button>
        <el-button size="small" type="primary" class="searchCondition" @click="goDetail('go')">文件下载中心</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'Draft',
  props: {
    changeExport: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {};
  },
  methods: {
    close() {
      this.$emit('handleExoprClose');
    },
    goDetail(info) {
      this.$emit('handleChangeExport', info);
    }
  }
};
</script>

<style lang="scss" scoped>
::v-deep  .el-dialog.changeExport {
  width: 400px;
  height: 230px;
  ::v-deep  .el-dialog__header {
    padding: 15px;
    border-bottom: 1px solid rgba(238, 238, 238, 1);
  }
  .el-dialog__title {
    padding-left: 5px;
    font-size: 16px;
    font-weight: 600;
    color: rgba(48, 49, 51, 1);
  }
  .el-dialog__body {
    padding: 20px 20px 30px 20px;
    font-size: 14px;
    color: rgba(41, 41, 51, 1);
    .worderId {
      color: #4184D5;
      cursor: pointer;
    }
  }
  .el-dialog__footer {
    padding-top: 56px;
  }
  .searchCondition.is-plain {
    background: #4184D5;
    color: #fff;
  }
}
</style>
