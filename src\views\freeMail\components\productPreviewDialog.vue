<template>
  <el-dialog
    title="商品预览"
    :visible="dialogVisible"
    width="85%"
    :before-close="handleClose">
    <ProductPreview :barcodeProp="barcode"/>
  </el-dialog>
</template>

<script>
import ProductPreview from '../preview'

export default {
  name: "productPreviewDialog",
  components: {ProductPreview},
  props: {
    barcode: {
      type: String,
      default() {
        return '';
      }
    }
  },
  data() {
    return {
      dialogVisible: true
    }
  },
  methods: {
    handleClose() {
      this.$emit('update:productPreviewDialogVisible', false)
    }
  }
}
</script>

<style scoped>

</style>
