<template>
  <div class="search-info">
    <!-- <span class="sign" />
    <span class="searchMsg">{{titleInfo}}</span> -->
    <!-- <el-tooltip effect="dark" placement="top">
      <template #content>
        <span v-html="tooltip"></span>
      </template>
      <p class="span-tip">!</p>
    </el-tooltip> -->
    <div :id="chartId"></div>
  </div>
</template>

<script>
import * as echarts from 'echarts';

export default {
  name: "LineChart",
  props: {
    titleInfo:{
      type: String,
      default:'标题'
    },
    chartId: {
      type: String,
      default:''
    },
    chartColor:{
        type:String,
        default:'#4184D5'
    },
    tooltip:{
        type:String,
        default:''
    },
    asyncHandler:{
        type:Function,
        default:()=>{}
    },
    queryForm:{
        type:Object,
        required:true,
        default:()=>{{}}
    },
    addSymbol:{
      type:Boolean,
      default:false,
    }
  },
  data() {
    return {

    }
  },
  mounted() {
    this.getChart();
  },
  computed:{
    serviceTooltip(){
      let tooltip = {
        trigger: 'axis',
        formatter:function (params) {
            var html = ''
            html+=params[0].axisValue+"<br/>"
            params.forEach((item,index) => {
              html+=`<div>
              ${item.marker}
              <span style="font-size:14px;color:#666;font-weight:400;margin-left:2px">${item.seriesName}</span>
              <span style="float:right;margin-left:20px;font-size:14px;color:#666;font-weight:900">${item.value}%</span>
              </div>`
            })
            return (
              html
            )
        }
      }
      return tooltip
    }
  },
  methods: {
    getChartId(){
      return this.chartId
    },

    getChart(name){
      const that = this;
      const {
        statisticalPlacer,
        provinceCodeList,
        cityCodeList,
        areaCodeList,
        opTime
      } = this.queryForm

      var params = {
        statisticalPlacer,
        startTime: opTime&&opTime[0]?opTime[0]:'',
        endTime: opTime&&opTime[1]?opTime[1]:'',
        provinceCodeList:provinceCodeList&&provinceCodeList.length ? '['+provinceCodeList.join(',')+']':'',
        cityCodeList:cityCodeList&&cityCodeList.length ? '['+cityCodeList.join(',')+']':'',
        areaCodeList:areaCodeList&&areaCodeList.length ? '['+areaCodeList.join(',')+']':'',
      };

      this.asyncHandler(params).then((res) => {
        if (res.code == 0) {
          if (res.result) {
            var abscissa = res.result.abscissa||[];
            var datum = res.result.datum||[];
            const myChart = echarts.init(document.getElementById(this.chartId));
            var option = {
              tooltip: this.addSymbol?this.serviceTooltip:{trigger: 'axis',},
              legend: {
                data: [this.titleInfo]
              },
              toolbox: {
                show: true,
                feature: {
                  magicType: {            //动态类型切换
                    show: true,           //是否显示该工具
                    type: ['line', 'bar'], //启用的动态类型
                    title:{
                        line:'切换为折线图',
                        bar:'切换为柱状图',
                    },
                    emphasis: {
                      iconStyle: {
                        color: '#4184D5',
                        textFill: '#4184D5'
                      }
                    }
                  },
                  saveAsImage: {          //保存为图片
                    show: true,            //是否显示该工具
                    title:'保存',
                    emphasis: {
                      iconStyle: {
                        color: '#4184D5',
                        textFill: '#4184D5'
                      }
                    }
                  }
                }
              },
              xAxis: {
                type: 'category',
                boundaryGap: false,
                data: abscissa
              },
              yAxis: {
                type: 'value',
                // axisLabel: {
                //     formatter: '{value} ￥'
                // }
              },
              series: [
                {
                  name: this.titleInfo,
                  type: 'line',
                  data: datum,
                  color:this.chartColor,
                  markPoint: {
                    data: [
                      {type: 'max', name: '最大值'},
                      {type: 'min', name: '最小值'}
                    ],
                    label: {
                      normal:this.addSymbol? {
                        formatter:function(param) {
                          return  (param.data.value)+'%';
                        }
                      }:{
                        formatter:function(param) {
                          return  (param.data.value);
                        }
                      }
                    }
                  },
                  smooth: true,
                  showSymbol: false,
                  areaStyle: {
                    color: {
                      type: 'linear',
                      x: 0,
                      y: 0,
                      x2: 0,
                      y2: 1,
                      colorStops: [{
                        offset: 0, color: 'rgb(227, 237, 249)' // 0% 处的颜色
                      }, {
                      offset: 1, color: 'white' // 100% 处的颜色
                    }],
                      global: false // 缺省为 false
               }
                  },
                  markLine: {
                    data: [
                      {type: 'average', name: '平均值'}
                    ]
                  }
                }
              ]
            };
            option && myChart.setOption(option);
          } else {
            this.$message({
              message: '暂无数据',
              type: 'error',
            });
          }
        } else {
          this.$message({
            message: res.msg,
            type: 'error',
          });
        }
      }).catch(() => {});
    },
  }
}
</script>

<style scoped lang="scss">
.search-info {
  padding: 10px 20px;
  font-weight: 500;
  .sign {
    display: inline-table;
    width: 3px;
    height: 13px;
    background: linear-gradient(360deg, #1d69c4 1%, #8bbdfc 99%);
    border-radius: 2px;
    margin-right: 8px;
    text-align: center;
  }
  .searchMsg {
    line-height: 32px;
    font-weight: 700;
  }
}
.span-tip {
  display: inline-block;
  width: 12px;
  height: 12px;
  font-size: 10px;
  border: 1px solid #999999;
  color: #999999;
  text-align: center;
  line-height: 12px;
  border-radius: 50%;
  margin-left: 5px;
}
#quantity,
#purchase,
#orderNum,
#drugstoreNum,
#drugstoreNumNew,
#shopCouponAmount,
#collageNetPurchase,
#collageDrugstoreNum,
#deliveryRate,
#refundRate,
#returnRete,
#responseRate,
#invoiceAfterSale,
#quaAfterSale,
#kfResponseTime {
  width: 100%;
  height: 408px;
}
</style>
