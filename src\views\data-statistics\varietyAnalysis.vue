<template>
  <div class="varietyAnalysis">
    <template v-if="!showProductInfo">
      <div class="title_line">筛选条件</div>
      <SearchForm
        v-if="showForm"
        ref="searchForm"
        :model="formModel"
        :form-items="formItems"
        :hasOpenBtn="false"
        @submit="handleFormSubmit"
        @reset="handleFormReset"
      >
        <template slot="form-item-sold">
          <el-radio-group v-model="formModel.isSold" size="small" class="form-item-sold" :disabled="formModel.statisticalPlacer !== 1">
            <el-radio-button :label="1">有销量</el-radio-button>
            <el-radio-button :label="0">无销量</el-radio-button>
          </el-radio-group>
        </template>
        <template slot="form-item-time">
          <el-select style="min-width: 120px;width: 120px" v-model="formModel.statisticalPlacer"
                     @change="timeTypeChange"
          >
            <el-option
              v-for="item in timeTypeOptions"
              :disabled="formModel.isSold === 0 && item.value !== 1"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
          <el-date-picker
            v-if="formModel.statisticalPlacer===0 || formModel.statisticalPlacer=== 5"
            v-model="formModel.time"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="yyyy-MM-dd"
            :picker-options="pickerOptions"
          >
          </el-date-picker>
          <el-date-picker
            v-if="formModel.statisticalPlacer===1"
            v-model="formModel.time"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="yyyy-MM-dd"
            :picker-options="pickerOptions1"
          >
          </el-date-picker>
          <el-date-picker
            v-if="formModel.statisticalPlacer===2"
            v-model="formModel.time"
            type="week"
            format="yyyy 第 WW 周"
            placeholder="选择周"
            :picker-options="pickerOptions2"
          >
          </el-date-picker>
          <el-date-picker
            v-if="formModel.statisticalPlacer===3"
            v-model="formModel.time"
            type="monthrange"
            placeholder="选择月"
            :picker-options="pickerOptions3"
          >
          </el-date-picker>
        </template>
      </SearchForm>
      <div class="operations">
        <el-button type="primary" plain @click="exportList" size="small">导出列表数据</el-button>
        <el-button type="primary" plain @click="exportDetail" size="small">导出明细数据</el-button>
      </div>
      <xyyTable
        :key="isSold"
        ref="withdrawalRecord"
        v-loading="tableLoading"
        :data="tableConfig.data"
        :col="isSold === 1 ? tableConfig.col : tableConfig.col1"
        :list-query="tableConfig.pagConfig"
        @get-data="queryList"
      >
        <template slot="productName">
          <el-table-column label="商品信息" width="280">
            <template slot="header" slot-scope="scope">
              商品信息
            </template>
            <template slot-scope="{row}">
              <div style="text-align: left;padding:5px 0 5px 15px">       
                <div>{{ row.productName }}</div>
                <div>{{ row.manufacturer }}</div>
                <div>{{ row.spec }}</div>
                <div>商品编码: {{ row.barcode }}</div>
                <div>ERP编码: {{ row.productCode }}</div>
              </div>
            </template>
          </el-table-column>
        </template>

        <template slot="availableQty">
          <el-table-column label="库存">
            <template slot="header">
              库存
              <el-tooltip class="item" content="当前商品可售库存" placement="top">
                <i class="el-icon-warning-outline"></i>
              </el-tooltip>
            </template>
            <template slot-scope="{row}">
              <div>{{ row.availableQty }}</div>
            </template>
          </el-table-column>
        </template>
        <template slot="productCt">
          <el-table-column label="销量">
            <template slot="header">
              销量
              <el-tooltip class="item" content="统计时间范围内订单中商品的采购数量 减去 订单退款成功的商品采购数量" placement="top">
                <i class="el-icon-warning-outline"></i>
              </el-tooltip>
            </template>
            <template slot-scope="{row}">
              <div>{{ row.productCt }}</div>
            </template>
          </el-table-column>
        </template>
        <template slot="payAmount">
          <el-table-column label="销售额">
            <template slot="header">
              销售额
              <el-tooltip class="item" content="统计时间范围内订单中商品的原总金额 减去 订单退款成功的原总金额" placement="top">
                <i class="el-icon-warning-outline"></i>
              </el-tooltip>
            </template>
            <template slot-scope="{row}">
              <div>{{ row.payAmount }}</div>
            </template>
          </el-table-column>
        </template>
        <template slot="realPayAmount">
          <el-table-column label="实付总金额">
            <template slot="header">
              实付总金额
              <el-tooltip class="item" content="统计时间范围内订单中商品的实付金额 减去 订单退款成功的商品金额" placement="top">
                <i class="el-icon-warning-outline"></i>
              </el-tooltip>
            </template>
            <template slot-scope="{row}">
              <div>{{ row.realPayAmount }}</div>
            </template>
          </el-table-column>
        </template>
        <template slot="clickCt">
          <el-table-column label="点击量">
            <template slot="header">
              点击量
              <el-tooltip class="item" content="客户点击商品信息，进入商品详情页次数" placement="top">
                <i class="el-icon-warning-outline"></i>
              </el-tooltip>
            </template>
            <template slot-scope="{row}">
              <div>{{ row.clickCt }}</div>
            </template>
          </el-table-column>
        </template>
        <template slot="visitorCt">
          <el-table-column label="访客量">
            <template slot="header">
              访客量
              <el-tooltip class="item" content="浏览商品的客户数" placement="top">
                <i class="el-icon-warning-outline"></i>
              </el-tooltip>
            </template>
            <template slot-scope="{row}">
              <div>{{ row.visitorCt }}</div>
            </template>
          </el-table-column>
        </template>
        <template slot="shoppingCartUserCt">
          <el-table-column label="加购药店数">
            <template slot="header">
              加购药店数
              <el-tooltip class="item" content="有加购行为的客户数" placement="top">
                <i class="el-icon-warning-outline"></i>
              </el-tooltip>
            </template>
            <template slot-scope="{row}">
              <div>{{ row.shoppingCartUserCt }}</div>
            </template>
          </el-table-column>
        </template>
        <template slot="userCt">
          <el-table-column label="采购药店数">
            <template slot="header">
              采购药店数
              <el-tooltip class="item" content="有采购行为的客户数" placement="top">
                <i class="el-icon-warning-outline"></i>
              </el-tooltip>
            </template>
            <template slot-scope="{row}">
              <div>{{ row.userCt }}</div>
            </template>
          </el-table-column>
        </template>
        <template slot="orderCt">
          <el-table-column label="采购订单数">
            <template slot="header">
              采购订单数
              <el-tooltip class="item" content="采购订单数（不含退款订单）" placement="top">
                <i class="el-icon-warning-outline"></i>
              </el-tooltip>
            </template>
            <template slot-scope="{row}">
              <div>{{ row.orderCt }}</div>
            </template>
          </el-table-column>
        </template>
        
        <template slot="suggestPrice">
          <el-table-column label="建议价格" width="150">
            <template slot="header">
              建议价格
              <el-tooltip class="item" content="近期销量较好的同品价格" placement="top">
                <i class="el-icon-warning-outline"></i>
              </el-tooltip>
            </template>
            <template slot-scope="{row}">
              <div v-if="row.suggestPriceMin == row.suggestPriceMax">{{ row.suggestPriceMax }}</div>
              <div v-else>{{( row.suggestPriceMin || '') + ' - ' + ( row.suggestPriceMax || '')}}</div>
            </template>
          </el-table-column>
        </template>
        
        <!-- <template slot="payAmount">
          <el-table-column label="销量" width="150">
            <template slot="header" slot-scope="scope">
              销售额
              <el-tooltip class="item" content="所选时间范围内商品的采购金额（去除这段时间已取消和已退款完成的该商品的采购金额）" placement="top">
                <i class="el-icon-warning-outline"></i>
              </el-tooltip>
            </template>
            <template slot-scope="{row}">
              <div>{{ row.payAmount }}</div>
            </template>
          </el-table-column>
        </template> -->
        <!-- <template slot="orderCt">
          <el-table-column label="销量" width="150">
            <template slot="header" slot-scope="scope">
              订单数量
              <el-tooltip class="item" content="所选时间范围内包含该商品的订单数量（去除这段时间已取消和已退款完成的包含该商品的订单）" placement="top">
                <i class="el-icon-warning-outline"></i>
              </el-tooltip>
            </template>
            <template slot-scope="{row}">
              <div>{{ row.orderCt }}</div>
            </template>
          </el-table-column>
        </template> -->
        <!-- <template slot="userCt">
          <el-table-column label="销量" width="150">
            <template slot="header" slot-scope="scope">
              药店数量
              <el-tooltip class="item" content="所选时间范围内采购该商品的药店数量（去除这段时间已取消和已退款完成的包含该商品的订单对应的药店数量）" placement="top">
                <i class="el-icon-warning-outline"></i>
              </el-tooltip>
            </template>
            <template slot-scope="{row}">
              <div>{{ row.userCt }}</div>
            </template>
          </el-table-column>
        </template> -->
        <template slot="operation">
          <el-table-column label="操作" fixed="right" width="190">
            <template slot-scope="{row}">
              <el-button type="text" @click="goViewDetail(row)">商品明细</el-button>
              <el-button type="text" @click="viewDetail(row)" v-if="isSold == 1 && statisticalPlacer !== 5">查看更多</el-button>
            </template>
          </el-table-column>
        </template>
      </xyyTable>
    </template>
    <commodityDataAnalysis
      v-if="showProductInfo"
      :showProductInfo.sync="showProductInfo"
      :productInfo=productInfo
    />

    <export-tip
      :change-export="changeExport"
      @handleExoprClose="handleExoprClose"
      @handleChangeExport="handleChangeExport"
    />
  </div>
</template>

<script>
import SearchForm from '@/components/searchForm';
import exportTip from '@/views/other/components/exportTip';
import commodityDataAnalysis from './components/commodityDataAnalysis';
import {actionTracking} from "@/track/eventTracking";
import {ExposureTool, VNodeExposureTool} from "@/utils/exposureTools"

import { selectPage, apiExportList ,apiSelectMainPage ,apiExportDetail } from '@/api/data-statistics/varietyAnalysis';

export default {
  name: 'varietyAnalysis',
  components: {
    SearchForm,
    exportTip,
    commodityDataAnalysis
  },
  data() {
    return {
      showForm: true,
      isSold: 1,
      statisticalPlacer:5,
      formModel: {
        barcode: '',
        productCode: '',
        isSold: 1,
        statisticalPlacer: 5,
        time: ''
      },
      formItems: [
        {
          label: '商品编码',
          prop: 'barcode',
          component: 'el-input',
          colSpan: 4,
          attrs: {
            placeholder: '请输入完整的商品编码'
          }
        },
        {
          label: '商品ERP编码',
          prop: 'productCode',
          component: 'el-input',
          colSpan: 5,
          attrs: {
            placeholder: '请输入完整的商品ERP编码'
          }
        },
        // {
        //   label: '是否有销量',
        //   prop: 'isSold',
        //   component: 'el-select',
        //   colSpan: 4,
        //   attrs: {
        //     options: [
        //       {
        //         label: '有销量',
        //         value: 1
        //       },
        //       {
        //         label: '无销量',
        //         value: 0
        //       }
        //     ]
        //   }
        // },
        {
          label: '',
          slotName: 'sold',
          colSpan: 3,
        },
        {
          label: '时间',
          slotName: 'time',
          colSpan: 9
        }
      ],
      timeTypeOptions: [
        {
          label: '今日',
          value: 5
        },
        {
          label: '近七天',
          value: 0
        },
        {
          label: '按天统计',
          value: 1
        },
        {
          label: '按周统计',
          value: 2
        },
        {
          label: '按月统计',
          value: 3
        }
      ],
      pickerOptions:{
        disabledDate: time => {
         return true
        }
      },
      pickerOptions1: {
        disabledDate: time => {
          const day_1 = new Date().getTime() - 24 * 3600 * 1000;
          const day_31 = new Date().getTime() - 24 * 3600 * 1000 * 32;
          return time.getTime() < day_31 || time.getTime() > day_1;
        }
      },
      pickerOptions2: {
        firstDayOfWeek: 1,
        disabledDate: time => {
          const day_1 = new Date().getTime() - 24 * 3600 * 1000;
          const day_180 = new Date().getTime() - 24 * 3600 * 1000 * 180;
          return time.getTime() < day_180 || time.getTime() > day_1;
        }
      },
      pickerOptions3: {
        disabledDate: time => {
          const day_1 = new Date().getTime() - 24 * 3600 * 1000;
          const day_365 = new Date().getTime() - 24 * 3600 * 1000 * 365;
          return time.getTime() < day_365 || time.getTime() > day_1;
        }
      },
      tableLoading: false,
      tableConfig: {
        data: [],
        col: [
          {
            name: '商品信息',
            index: 'productName',
            slot: 'productName'
          },
          {
            name: '库存',
            index: 'availableQty',
            slot: 'availableQty',
          },
          {
            name: '销量',
            index: 'productCt',
            slot: 'productCt',
          },
          {
            name: '销售额',
            index: 'payAmount',
            slot: 'payAmount',
          },
          {
            name: '实付总金额',
            index: 'realPayAmount',
            slot: 'realPayAmount',
          },
          {
            name: '点击量',
            index: 'clickCt',
            slot:'clickCt'
          },
          {
            name: '访客量',
            index: 'visitorCt',
            slot:'visitorCt'
          },
          {
            name: '加购药店数',
            index: 'shoppingCartUserCt',
            slot: 'shoppingCartUserCt',
          },
          {
            name: '采购药店数',
            index: 'userCt',
            slot: 'userCt',
          },
          {
            name: '采购订单数',
            index: 'orderCt',
            slot: 'orderCt',
          },
          {
            name: '操作',
            index: 'operation',
            slot: 'operation'
          }
        ],
        col1: [
          {
            name: '商品信息',
            index: 'productName',
            slot: 'productName'
          },
          {
            name: '库存',
            index: 'availableQty',
            slot: 'availableQty',
          },
          {
            name: '建议价格',
            index: 'suggestPrice',
            slot: 'suggestPrice'
          },
          {
            name: '点击量',
            index: 'clickCt',
            slot: 'clickCt'
          },
          {
            name: '访客量',
            index: 'visitorCt',
            slot: 'visitorCt'
          },
          {
            name: '加购药店数',
            index: 'shoppingCartUserCt',
            slot: 'shoppingCartUserCt'
          },
          {
            name: '操作',
            index: 'operation',
            slot: 'operation'
          }
        ],
        pagConfig: {
          pageSize: 10,
          page: 1,
          total: 0
        }
      },
      changeExport: false,
      showProductInfo: false,
      productInfo: {}
    };
  },
  created() {
    this.initialFormModelData = { ...this.formModel };
    this.timeTypeChange(this.formModel.statisticalPlacer);
    this.queryList();
  },

  mounted() {
    // setTimeout(() => {
    //     document.querySelector('.form-item-sold').previousElementSibling.style='background: #fff !important;border: 0 !important;'
    //   }, 1);
    actionTracking('variety_analysis_click', { })
  },

  methods: {
    getParams() {
      const params = { ...this.formModel };
      if (params.statisticalPlacer === 2) {
        params.startTime = window.dayjs(params.time)
          .startOf('week')
          .format('YYYY-MM-DD');
        params.endTime = window.dayjs(params.time)
          .endOf('week')
          .format('YYYY-MM-DD');
      } else if (params.statisticalPlacer === 3) { // 按月统计
        params.startTime = window.dayjs(params.time[0])
          .startOf('month')
          .format('YYYY-MM-DD');
        params.endTime = window.dayjs(params.time[1])
          .endOf('month')
          .format('YYYY-MM-DD');
      } else if ((params.statisticalPlacer === 0 || params.statisticalPlacer === 1 || params.statisticalPlacer === 5) && Array.isArray(params.time)) {
        params.startTime = params.time[0] ? window.dayjs(params.time[0])
          .format('YYYY-MM-DD') : '';
        params.endTime = params.time[1] ? window.dayjs(params.time[1])
          .format('YYYY-MM-DD') : '';
      }
      if (params.endTime && window.dayjs()
        .unix() < window.dayjs(params.endTime)
        .unix()) {
        //结束时间大于了当前时间
        params.endTime = window.dayjs()
          .subtract(1, 'day')
          .format('YYYY-MM-DD');
      }
      delete params.time;
      return params;
    },
    async queryList(listQuery) {
      let params = this.getParams();
      this.isSold=params.isSold;
      this.statisticalPlacer = params.statisticalPlacer;
      if (!params) return;
      this.tableLoading = true;
      if (listQuery) {
        const {
          pageSize,
          page
        } = listQuery;
        this.tableConfig.pagConfig.pageSize = pageSize;
        this.tableConfig.pagConfig.page = page;
      }
      const {
        pageSize,
        page
      } = this.tableConfig.pagConfig;
      params.pageNum = page;
      params.pageSize = pageSize;
      console.log(JSON.stringify(params));
      try {
        const res = await apiSelectMainPage(params);
        if (res && res.code === 0) {
          this.tableConfig.data = res.result.list || [];
          this.tableConfig.pagConfig.total = res.result.total;
        } else {
          this.$message.error(res.message || '请求异常');
        }
      } catch (e) {
        console.log(e);
      }
      this.tableLoading = false;
    },
    handleFormSubmit() {
      this.tableConfig.pagConfig.page = 1
      this.tableConfig.data = [];
      this.queryList();
    },
    handleFormReset() {
      this.formModel = { ...this.initialFormModelData };
      this.timeTypeChange(this.formModel.statisticalPlacer);
      this.queryList();
    },
    timeTypeChange(val) {
      console.log(val);
      this.showForm = false;
      if (val === 0) {
        this.formModel.time = [window.dayjs()
          .subtract(7, 'day')
          .format('YYYY-MM-DD'), window.dayjs()
          .subtract(1, 'day')
          .format('YYYY-MM-DD')];
      } else if (val === 1) {
        this.formModel.time = [window.dayjs()
          .subtract(1, 'day')
          .format('YYYY-MM-DD'), window.dayjs()
          .subtract(1, 'day')
          .format('YYYY-MM-DD')];
      } else if (val === 2) {
        this.formModel.time = window.dayjs()
          .startOf('week')
          .format('YYYY-MM-DD');
      } else if (val === 3) {
        this.formModel.time = [window.dayjs()
          .format('YYYY-MM'), window.dayjs()
          .format('YYYY-MM')]
      }else if (val === 5) {
        this.formModel.time = [window.dayjs()
          .format('YYYY-MM-DD'), window.dayjs()
          .format('YYYY-MM-DD')];
      }
      console.log(this.formModel.time);
      actionTracking('variety_analysis_statistical_method_click',
        {
          time : new Date().getTime(),
          org_id : this.orgId,
          variety_analysis_statistical_method : {
            0 : '7day',
            1 : 'day',
            2 : 'week',
            3 : 'month'
          }[val]
        })
      this.$nextTick(() => {
        this.showForm = true;
      });
    },
    async exportList() {
      actionTracking('variety_analysis_export', {
          time : new Date().getTime(),
          org_id : this.orgId
      });
      const res = await apiExportList(this.getParams());
      if (res && res.code === 0) {
        this.changeExport = true;
      } else {
        this.$message.error(res.msg || '导出失败');
      }
    },
    async exportDetail() {
      // actionTracking('variety_analysis_export', {
      //     time : new Date().getTime(),
      //     org_id : this.orgId
      // });
      const res = await apiExportDetail(this.getParams());
      if (res && res.code === 0) {
        this.changeExport = true;
      } else {
        this.$message.error(res.msg || '导出失败');
      }
    },
    
    handleChangeExport(info) {
      this.changeExport = false;
      if (info === 'go') {
        const path = '/downloadList';
        window.openTab(path);
      }
    },
    handleExoprClose() {
      this.changeExport = false;
    },
    viewDetail(row) {
      console.log(row);
      const {
        barcode,
        productCode,
        productName,
        spec,
        manufacturer,
        skuId
      } = row;
      this.productInfo = {
        barcode,
        productCode,
        productName,
        spec,
        manufacturer,
        skuId
      };
      this.showProductInfo = true;

      actionTracking('variety_analysis_more_click', {
        time: new Date().getTime(),
        org_id: this.orgId
      })

    },
    goViewDetail(row) {
      const {
        skuId
      } = row;

      const params = this.getParams();

      this.$router.push({
        path: '/varietyAnalysis/detail',
        query: {
          startTime:params.startTime,
          endTime:params.endTime,
          time:new Date(this.formModel.time).getTime(),
          // barcode:params.barcode,
          productCode:params.productCode,
          originalSkuId:row.skuId,
          isSold:this.isSold,
          productName:row.productName,
          spec:row.spec,
          manufacturer:row.manufacturer,
          statisticalPlacer:this.statisticalPlacer
        }
      })
    }
  }
};
</script>

<style scoped lang="scss">
.varietyAnalysis {
  padding: 16px 16px;
  background: #fff;

  ::v-deep   .el-input-group--prepend {
    height: 32px;
    line-height: 32px;

    .el-date-editor .el-range-separator {
      line-height: 29px;
    }

    .el-date-editor .el-range__icon {
      line-height: 24px;
    }
  }


 
  .operations {
    margin-bottom: 12px;
  }
 
  ::v-deep   .el-table tr td .cell{
    height: auto  !important;
    line-height: 20px !important;
  }
}
.searchForm ::v-deep   .el-date-editor .el-range-input {
  font-size: 14px;
}
</style>
<style>
  div[colindex="2"] .el-input-group__prepend{
    background: #fff !important;
    border:0 !important;
  }
</style>