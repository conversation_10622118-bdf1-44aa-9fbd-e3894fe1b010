import request from '../../utils/request';


export const qualificationSearch = (params) => {
  return request.post('/popFirstSaleQualification/query', params)
}
export const firstSaleQualificationStatistics = () => {
  return request.get('/popFirstSaleQualification/statistics');
}
export const uploadFDFS = (file) => {
  const forms = new FormData();
  forms.append('file', file);
  return request({
    url: '/uploadFile/uploadFDFS',
    method: 'post',
    data: forms,
    headers: { 'Content-Type': 'multipart/form-data' },
    // onUploadProgress: (progressEvent) => {
    //   const num = (progressEvent.loaded / progressEvent.total) * 100; // 百分比
    //   params.onProgress({ percent: num }); // 进度条
    // },
  });
}
export const firstSaleQualificationDownFile = (params) => {
  // window.open(`/popFirstSaleQualification/downZipUrl?barCode=${params.barCode}&sealStatus=${params.sealStatus}`)
  return request.get('/popFirstSaleQualification/downZipUrlV2', {
    params: params
  });
}

//首营资质导出
export const popFirstSaleQualificationExport = (params) => {
  return request.get('/popFirstSaleQualification/async/export', {
    params: params
  })
}
//是否开通电子签章
export const platformServiceAgreementGetSealStatus = () => {
  return request.get('/platformServiceAgreement/signaturesStatus')
}
export const popFirstSaleQualificationAddFile = (params) => {
  return request.post('/popFirstSaleQualification/addFile', params)
}
export function drugReportExport (params) {
  return request.post('/pop/drug/report/export', params)
}

export const firstSaleQualificationMultipartUpload = (params) => {
  return request.post('/popFirstSaleQualification/addBatchFile', params)
}

//查询药检报告
export const drugReportQuery = (params) => {
  return request.post('/pop/drug/report/query', params)
}
//药检报告统计信息查询
export const drugReportStatistics = () => {
  return request.post('/pop/drug/report/statistics')
}
//药检报告批量上传
export const drugReportBatchUpload = (params) => {
  return request.post('/pop/drug/report/batch/upload', params)
}
//药检报告上传编辑
export const reportBatchUpload = (params) => {
  return request.post('/pop/upload/edit', params)
}
//药检报告选择商品添加批号
export const addBatchCodeByGood = (params) => {
  return request.post('/pop/upload/batchCode', params)
}
//药检报告批量添加批号
export const addBatchCodeByFile = (params) => {
  return request.post('/pop/upload/batch/batchCode', params)
}
export const drugReportDownZipUrl = (params) => {
  window.open(`/pop/drug/report/downZipUrl?barCode=${params.barcode}&sealStatus=${params.sealStatus}&batchCode=${params.batchCode}`)
}
export const templateFile = () => {
  return request.get('/pop/upload/templateFile', {
    responseType: 'blob'
  });
}
