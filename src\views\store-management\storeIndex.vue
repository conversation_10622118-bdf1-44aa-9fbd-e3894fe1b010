<template>
  <div class="main-box">
    <div class="list-box">
      <div v-show="nextTip == 1">
        <floorSetList @goNext="goNext" />
      </div>
      <div v-show="nextTip == 2">
        <configurationGoodsList
          :goods-obj="goodsObj"
          @goPrev="goPrev"
        />
      </div>
    </div>
  </div>
</template>

<script>
import floorSetList from './modules/floorSetList';
import configurationGoodsList from './modules/configurationGoodsList';

export default {
  name: 'StoreIndex',
  components: {
    floorSetList,
    configurationGoodsList,
  },
  data() {
    return {
      nextTip: 1,
      // 基础信息
      goodsObj: {
        floorName: '',
        floorDescribe: '',
        areaName: '',
      },
      detailData: {},
    };
  },
  methods: {
    // 配置商品
    goNext(from) {
      if (from.from == 'goodsList') {
        this.nextTip = 2;
        this.detailData = from.data;
        this.goodsObj.floorName = this.detailData.floorName;
        this.goodsObj.floorDescribe = this.detailData.floorDescribe;
        this.goodsObj.areaName = this.detailData.areaName;
        this.goodsObj.floorId = this.detailData.floorId;
      }
    },
    // 返回楼层
    goPrev(from) {
      if (from.from == 'floorSet') {
        this.nextTip = 1;
      }
    },
  },
};
</script>
<style lang="scss">
.main-box{
  padding: 8px;
}
.delete-dialog-customClass {
  position: relative;
  top: -30%;
  left: 25%;
  transform: translate(-60%, 30%);

  .el-message-box__message {
    padding-bottom: 30px;
    color: #333333;
    font-size: 14px;
    max-height: 400px;
    overflow-y: auto;
    word-break: break-all;
  }
}
</style>
<style lang="scss" scoped>
@import '../../assets/css/market';
</style>
