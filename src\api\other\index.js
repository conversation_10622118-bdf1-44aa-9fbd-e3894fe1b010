import request from '@/utils/request';

/**
 * 下载管理：列表
 * @param {params}查询参数
 */
export function getDownloadFileList (params) {
    return request({
      url: '/order/v2/queryDownloadFileList',
      method: 'get',
      params: params
    });
  }

  /**
   * 下载管理：下载
   * @param {params}查询参数
   */
  export function getDownload (params) {
    return request({
      url: '/workOrder/export/download/' + params,
      method: 'get'
    });
  }
/*退货须知*/
export function queryReturnNotice (params) {
  return request({
    url: '/popNotice/queryReturnNotice',
    method: 'get',
    params
  });
}
/*店铺公告*/
export function queryShopNotice (params) {
  return request({
    url: '/popNotice/queryShopNotice',
    method: 'get',
    params
  });
}
/*公告保存*/
export function shopNoticeSave (params) {
  return request({
    url: '/popNotice/shopNoticeSave',
    method: 'post',
    data: params
  });
}
/* 退货保存 */
export function returnNoticeSave (params) {
  return request({
    url: '/popNotice/ReturnNoticeSave',
    method: 'post',
    data: params,
  });
}

// 店铺公告改变 https://yapi.int.ybm100.com/project/891/interface/api/cat_8076
/* 店铺公告-物流公司 */
export function getDeliveryNames(params) {
  return request({
    url: '/popNotice/deliveryNames',
    method: 'get',
    params,
  });
}

/* 区域列表 */
export function getLevelList(params) {
  return request({
    url: '/area/levelList',
    method: 'get',
    params,
  });
}
