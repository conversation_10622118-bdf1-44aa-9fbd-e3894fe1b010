<template>
    <div class="wholesale-page">
        <h4>批购包邮单品销售类型</h4>
        <div class="form-item">
            <p><span>*</span>上下架时间：</p>
            <div class="form-item-content">
                <el-date-picker
                    v-model="form.shelvesTime"
                    type="datetimerange"
                    range-separator="至"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期">
                </el-date-picker>
            </div>
        </div>
        <div class="form-item">
            <p>申请方式：</p>
            <div class="form-item-content">
                <div class="form-item-list">
                    <el-radio v-model="applyType" label="1">单个申请</el-radio>
                    <div class="sim-input">
                        <el-input v-model="form.shopCode" placeholder="输入普通商品编码"></el-input>
                    </div>
                    <el-button type="primary" @click="singleApply" :disabled="applyType === '2'">创建</el-button>
                </div>
                <div class="form-item-list-direc">
                    <div class="form-item-title">
                        <el-radio v-model="applyType" label="2">批量申请</el-radio>
                        <el-upload
                            ref="upload"
                            action=""
                            class="upload-box"
                            accept=".xls,.xlsx"
                            :on-change="uploadChange"
                            :on-exceed="fileBeyond"
                            :before-remove="remove"
                            :file-list="fileList"
                            :limit="1"
                            :auto-upload="false"
                        >
                            <el-button
                            type="primary"
                            size="small"
                            >
                            选择文件
                            </el-button>
                            <p
                                v-if="!batchFile"
                                style="padding: 10px 0 0 0;margin:0;"
                            >
                                未选择任何文件
                            </p>
                        </el-upload>
                    </div>
                    <div class="form-item-tips">
                        <p>1.请上传编辑好的xlsx文件</p>
                        <p>2.业务商圈、供货对象和黑白名单相同的可批量提交，否则请分批提交</p>
                        <p>3.模板文件<span @click="downloadImportTemplate">下载</span></p>
                    </div>
                </div>
            </div>
        </div>
        <div class="form-item">
            <p>供货信息配置方式：</p>
            <div class="form-item-content">
                <div class="form-item-list" style="padding-top: 10px;">
                    <el-radio v-model="radio" label="1">复用原品销售范围</el-radio>
                    <el-radio v-model="radio" label="2">配置业务商圈、供货对象、黑白名单</el-radio>
                    <el-radio v-model="radio" label="3">人群</el-radio>
                </div>
                <div class="form-item-tips">
                    <p>1.选"复刻原品销售范围"，商品创建成功后，商品和原因商圈、供货对象、黑白名单相同；</p>
                    <p>2.选"配置业务商圈、供货对象和黑白名单"，按照页面所选信息生效。</p>
                </div>
            </div>
        </div>
        <div class="form-button">
            <el-button>取消</el-button>
            <el-button type="primary" :disabled="applyType === '1'">提交</el-button>
        </div>
    </div>
</template>
<script>
export default {
    data() {
        return {
            fileList: [],
            applyType: "2",
            batchFile: null,
            batchType: "",
            radio: "2",
            form: {
                shelvesTime: [new Date(), new Date(2099, 11, 31, 23, 59, 59)],
                shopCode: "",
            }
        }
    },
    methods: {
        uploadChange(file) {
            if (file.status === 'ready') {
                const extension = file.name.split('.')[1] === 'xls';
                const extension2 = file.name.split('.')[1] === 'xlsx';
                // file.size / 1024 / 1024 < 3
                const isLt2M = file.size / 1024 / 1024 < 3;
                if (!extension && !extension2) {
                    this.fileList = [];
                    this.$message.warning('上传模板只能是 xls、xlsx格式!');
                    return;
                }
                if (!isLt2M) {
                    this.fileList = [];
                    this.$message.warning('文件过大，最大支持3M!');
                    return;
                }
                this.batchFile = file;
            }
        },
        remove() {
            this.batchFile = '';
            this.fileList = [];
        },
        // 文件超出限制
        fileBeyond() {
            this.$message({
                message: '每次最多上传1个文件',
                type: 'warning',
            });
        },
        downloadImportTemplate() {
            let url = '';
            if (this.batchType === 'add') {
                url = `${process.env.VUE_APP_BASE_API}/groupFollowHeart/downloadImportBlackListSkuTemplate`;
            } else {
                url = `${process.env.VUE_APP_BASE_API}/groupFollowHeart/downloadDeleteBlackListSkuTemplate`;
            }
            const a = document.createElement('a');
            a.href = url;
            a.click();
        },
        singleApply() {
            if (!this.form.shopCode) {
                this.$message.error("商品编码为必填");
                return;
            }
            this.$router.push("/singleApply")
        }
    }
}
</script>
<style lang="scss" scoped>
.wholesale-page {
    padding: 20px;
    box-sizing: border-box;
    >h4 {
        font-size: 20px;
        margin: 0;
        margin-bottom: 20px;
    }
    .form-item {
        display: flex;
        margin-bottom: 20px;
        >p {
            width: 150px;
            display: flex;
            margin: 0;
            justify-content: flex-end;
            margin-right: 10px;
            line-height: 34px;
            span {
                color: red;
            }
        }
        .form-item-content {
            flex: 1;
            display: flex;
            justify-content: center;
            flex-direction: column;
            .form-item-list {
                display: flex;
                align-items: center;
                margin-bottom: 20px;
                .sim-input {
                    width: 250px;
                    margin-right: 20px;
                }
            }
            .form-item-list-direc {
                display: flex;
                flex-direction: column;
                justify-content: center;
                .form-item-title {
                    display: flex;
                    align-items: center;
                }
                .form-item-tips {
                    margin-top: 10px;
                    display: flex;
                    flex-direction: column;
                    p {
                        margin: 0;
                        margin-bottom: 5px;
                        span {
                            text-decoration:underline;
                            color: #4183d5;
                            cursor: pointer;
                            margin-left: 10px;
                        }
                    }
                }
            }
        }
    }
    .form-button {
        padding-left: 150px;
        box-sizing: border-box;
        .el-button {
            width: 140px;
            margin-right: 40px;
        }
    }
}
</style>