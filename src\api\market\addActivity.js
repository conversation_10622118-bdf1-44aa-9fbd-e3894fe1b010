import request from '../index';

export function peopleDataPost(data) {
  return request.post('/insight/create', data);
}

export function peopleAreaList(data) {
  return request.get('/insight/areaAndMerchatType', data);
}

export function addActivityCouponList(data) {
  return request.get('/insight/list', data)
}

export function getBranchList() {
  return request.get('/areaManagement/getBranchList');
}

export function checkBeforeDel(data) {
  return request.get('/insight/checkBeforeDel', data);
}

export function checkDel(data) {
  return request.get('/insight/del', data);
}

export function getDetail(data) {
  return request.get('/insight/detail', data);
}

export function getProvinceAndMerchantType() {
  return request.get('/insight/default/areaAndMerchatType');
}

export function getProvinceRegion(data) {
  return request.get('/insight/getProvinceAreas', data);
}


export function getImportMerchantList(data) {
  return request.post('/insight/importMerchant/list', data);
}

export function getMerchantList(data) {
  return request.post('/insight/merchant/list', data);
}

export function deleteImportMerchantList(data) {
  return request.post('/insight/importMerchant/remove', data);
}
