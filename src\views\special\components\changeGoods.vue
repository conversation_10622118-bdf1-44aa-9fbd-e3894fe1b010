<template>
  <div class="choose">
    <el-dialog
      title="选择商品"
      :visible.sync="dialogFormVisible"
      width="900px"
    >
      <div>
        <div style="padding: 15px 20px">
          <el-row :gutter="20">
            <el-col :span="6">
              <el-input v-model="listQuery.barCode" placeholder="请输入内容" size="small">
                <template slot="prepend"> 商品编号 </template>
              </el-input>
            </el-col>
            <el-col :span="6">
              <el-input v-model="listQuery.productName" placeholder="请输入内容" size="small">
                <template slot="prepend"> 商品名称 </template>
              </el-input>
            </el-col>
            <el-col :span="6">
              <el-input v-model="listQuery.code" placeholder="请输入内容" size="small">
                <template slot="prepend"> 条码 </template>
              </el-input>
            </el-col>
            <el-col :span="6" style="text-align: right">
              <el-button size="small" @click="resetList"> 重置 </el-button>
              <el-button type="primary" size="small" @click="getList"> 查询 </el-button>
            </el-col>
          </el-row>
        </div>
        <div style="padding: 0 20px">
          <el-table
            ref="goodsTable"
            :data="goodsData"
            stripe
            border
            tooltip-effect="dark"
            style="width: 100%"
          >
            <el-table-column type="selection" width="45" />
            <el-table-column label="商品编号" prop="barcode" width="120"></el-table-column>
            <el-table-column label="商品名称" prop="productName" width="160"></el-table-column>
            <el-table-column label="条码" prop="code" width="140"></el-table-column>
            <el-table-column label="规格" prop="spec" width="120"></el-table-column>
            <el-table-column label="单体采购价" prop="fob"></el-table-column>
            <el-table-column label="连锁采购价" prop="guidePrice"></el-table-column>
            <el-table-column label="可售库存" prop="availableQty"></el-table-column>
            <el-table-column label="状态">
              <template slot-scope="scope">
                <span v-if="scope.row.status == 1">销售中</span>
                <span v-if="scope.row.status == 2">已售罄</span>
                <span v-if="scope.row.status == 6">待上架</span>
                <span v-if="scope.row.status == 7">已录入</span>
                <span v-if="scope.row.status == 8">待审核</span>
                <span v-if="scope.row.status == 9">审核未通过</span>
              </template>
            </el-table-column>
            <el-table-column width="120" label="是否近效期">
              <template slot-scope="scope">
                <span v-if="scope.row.status == 1">是</span>
                <span v-else>否</span>
              </template>
            </el-table-column>
            <el-table-column label="创建时间" width="160">
              <template slot-scope="scope">
                <span>{{ scope.row.createTime | formatDate }}</span>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div class="page-box">
          <Pagination
            v-show="goodsListTotal > 0"
            :total="goodsListTotal"
            :page.sync="listQuery.pageNum"
            :limit.sync="listQuery.pageSize"
            @pagination="getList(1)"
          />
        </div>
      </div>
      <div
        slot="footer"
        class="dialog-footer"
      >
        <el-button
          size="small"
          @click="dialogFormVisible = false"
        >
          取 消
        </el-button>
        <el-button
          type="primary"
          size="small"
          @click="sendData"
        >
          确 定
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>

import { getProductGoodsList } from '../../../api/activity/special'
import Pagination from '../../../components/Pagination/index.vue'
export default {
  name: 'changeGoods',
  data() {
    return {
      dialogFormVisible: false,
      goodsData: [],
      goodsListTotal: 0,
      listQuery: {
        pageNum: 1,
        pageSize: 10,
        code: '',
        productName: '',
        barCode: ''
      }
    };
  },
  components: { Pagination },
  methods: {
    getChooseData() {},
    getList(from, data) {
      let that = this
      const params = {...this.listQuery}
      getProductGoodsList(params).then( (res) => {
        !from ? this.dialogFormVisible = true : ''
        if(res.code == 0) {
          console.log('选择商品数据', res);
          const { list, total } = {...res.result}
          this.goodsData = list
          this.goodsListTotal = total
          // setTimeout(function (){
          //   console.log(data,'00000')
          //   data.forEach(row => {
          //     console.log(row,'row')
          //     that.$refs.goodsTable.toggleRowSelection(row,true)
          //   })
          // },1000)
        }else {
          this.$message.error(res.msg)
        }
      })
    },
    sendData() {
      const _selectData = this.$refs.goodsTable.selection
      console.log(_selectData);
      this.$emit('setChooseGoods', _selectData)
      this.dialogFormVisible = false
    },
    resetList() {
      this.listQuery = {
        pageNum: 1,
        pageSize: 10,
        code: '',
        productName: '',
        barCode: ''
      }
      this.getList();
    }
  },
};
</script>

<style scoped lang="scss">
.choose {
  ::v-deep  .el-button--primary {
    background: #4183d5;
  }
  ::v-deep  .el-dialog__body {
    padding: 0 0 10px;
  }
  ::v-deep  .el-dialog__header {
    padding: 10px 16px;
    background: #f9f9f9;
  }
  ::v-deep  .el-dialog__headerbtn {
    top: 13px;
  }
}
</style>
