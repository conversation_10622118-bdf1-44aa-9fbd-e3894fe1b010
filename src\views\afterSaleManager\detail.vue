<!-- 售后详情页 -->
<template>
	<div v-loading="loading" style="min-height:500px;">
		<common-header v-if="!loading" title="售后详情" :showFold="false">
			<template slot="header-right">
				<el-button size="small" @click="goBack">返回</el-button>
			</template>
			<process :processList="data.auditProcessList" style="margin-bottom:20px;"></process>
			<div class="detail-content">
				<div style="border:solid 1px #dedede;border-radius:5px;padding:0 10px;">
					<common-header class="card-box" title="售后信息" :showFold="false" :shouHeightLine="false">
						<p class="item-left">
							<span>客户名称：</span>
							<span :title="data.afterSaleInfo.merchantName">{{ data.afterSaleInfo.merchantName }}</span>
						</p>
						<p class="item-left">
							<span>售后类型：</span>
							<span v-if="data.afterSaleInfo.afterSalesDetailInfo" :title="data.afterSaleInfo.afterSalesDetailInfo.afterSalesTypeName">{{ data.afterSaleInfo.afterSalesDetailInfo.afterSalesTypeName }}</span>
						</p>
						<p class="item-left">
							<span>售后原因：</span>
							<span :title="data.afterSaleInfo.reason">{{ data.afterSaleInfo.reason || '-' }}</span>
						</p>
						<p class="item-left">
							<span>售后金额：</span>
							<span :title="data.afterSaleInfo.amount">{{ data.afterSaleInfo.amount || '-' }}</span>
						</p>
						<p class="item-left">
							<span>客户备注：</span>
							<span :title="data.afterSaleInfo.remark">{{ data.afterSaleInfo.remark }}</span>
						</p>
						<p class="item-left" style="justify-content: flex-start;margin:0px;" slot="header-right">
							<el-button size="mini" icon="el-icon-service" @click="openService(data.afterSaleInfo.merchantName)">
								联系客户
							</el-button>
							<el-button size="mini" v-loading="buyer.loading" @click="showYBM(data.afterSaleInfo.merchantId)">药帮忙业务员</el-button>
						</p>
					</common-header>
					<common-header class="card-box" style="border-top:solid 1px #dedede;" title="明细" :showFold="false" :shouHeightLine="false">
						<div  v-if="data.afterSaleInfo.afterSalesDetailInfo">
							<p class="item-left">
								<span style="width:20px;"></span>
								<span>{{ data.afterSaleInfo.afterSalesDetailInfo.afterSalesTypeName }}</span>
							</p>
							<p class="item-left" v-if="data.afterSaleInfo.afterSalesDetailInfo.specialInvoiceTitle">
								<span style="width:20px;"></span>
								<span>{{ data.afterSaleInfo.afterSalesDetailInfo.specialInvoiceTitle }}</span>
							</p>
							<div>
								<p v-for="(item, i) in data.afterSaleInfo.afterSalesDetailInfo.itemList" :key="i" class="item-left">
									<span style="width:120px;">{{ item.itemTitle }}：</span>
									<span :title="item.itemValue">{{ item.itemValue }}</span>
									<span v-if="item.itemValue" @click="copy(item.itemTitle, item.itemValue)">复制</span>
								</p>
							</div>
							<div v-if="data.afterSaleInfo.afterSalesDetailInfo.specialInvoiceInfo" >
								<p>
									{{ `客户专票信息(${ data.afterSaleInfo.afterSalesDetailInfo.specialInvoiceInfo.isElectronicInvoice ? '接受' : '不接受'}电子专票)` }}</p>
								<p class="item-left">
									<span>公司名称：</span>
									<span>{{ data.afterSaleInfo.afterSalesDetailInfo.specialInvoiceInfo.companyName }}</span>
									<span v-if="data.afterSaleInfo.afterSalesDetailInfo.specialInvoiceInfo.companyName" @click="copy('公司名称', data.afterSaleInfo.afterSalesDetailInfo.specialInvoiceInfo.companyName)">复制</span>
								</p>
								<p class="item-left">
									<span>纳税人识别号：</span>
									<span>{{ data.afterSaleInfo.afterSalesDetailInfo.specialInvoiceInfo.enterpriseRegistrationNo }}</span>
									<span v-if="data.afterSaleInfo.afterSalesDetailInfo.specialInvoiceInfo.enterpriseRegistrationNo" @click="copy('纳税人识别号', data.afterSaleInfo.afterSalesDetailInfo.specialInvoiceInfo.enterpriseRegistrationNo)">复制</span>
								</p>
								<p class="item-left">
									<span>地址：</span>
									<span>{{ data.afterSaleInfo.afterSalesDetailInfo.specialInvoiceInfo.registerAddress }}</span>
									<span v-if="data.afterSaleInfo.afterSalesDetailInfo.specialInvoiceInfo.registerAddress" @click="copy('地址', data.afterSaleInfo.afterSalesDetailInfo.specialInvoiceInfo.registerAddress)">复制</span>
								</p>
								<p class="item-left">
									<span>电话：</span>
									<span>{{ data.afterSaleInfo.afterSalesDetailInfo.specialInvoiceInfo.phone }}</span>
									<span v-if="data.afterSaleInfo.afterSalesDetailInfo.specialInvoiceInfo.phone" @click="copy('电话', data.afterSaleInfo.afterSalesDetailInfo.specialInvoiceInfo.phone)">复制</span>
								</p>
								<p class="item-left">
									<span>开户银行：</span>
									<span>{{ data.afterSaleInfo.afterSalesDetailInfo.specialInvoiceInfo.bankName }}</span>
									<span v-if="data.afterSaleInfo.afterSalesDetailInfo.specialInvoiceInfo.bankName" @click="copy('开户银行', data.afterSaleInfo.afterSalesDetailInfo.specialInvoiceInfo.bankName)">复制</span>
								</p>
								<p class="item-left">
									<span>银行账号：</span>
									<span>{{ data.afterSaleInfo.afterSalesDetailInfo.specialInvoiceInfo.acct }}</span>
									<span v-if="data.afterSaleInfo.afterSalesDetailInfo.specialInvoiceInfo.acct" @click="copy('银行账号', data.afterSaleInfo.afterSalesDetailInfo.specialInvoiceInfo.acct)">复制</span>
								</p>
							</div>
						</div>
					</common-header>
				</div>
				<div style="border:solid 1px #dedede;border-radius:5px;padding:0 10px;">
					<common-header class="card-box" title="请选择处理方式" :showFold="false" :shouHeightLine="false">
						<p v-if="data.afterSaleInfo.auditProcessState == 6" class="warning">
							<i class="el-icon-alarm-clock" style="margin-right:5px"></i>
							<span style="font-size: 14px;color:black">剩余处理时间：</span>
              <span style="font-size: 18px;">{{ dateLine }}</span>
						</p>
						<p class="close" v-if="data.afterSaleInfo.auditProcessState == 2">
							<i class="el-icon-warning-outline" style="margin-right:5px"></i>
							<span style="fontSize:14px;">客户已取消售后申请</span>
						</p>
						<p class="close" v-if="data.afterSaleInfo.auditProcessState == 8">
							<i class="el-icon-warning-outline" style="margin-right:5px"></i>
							<span style="fontSize:14px;">售后超时未处理，系统自动关闭。</span>
						</p>
						<p class="refund" v-if="data.afterSaleInfo.auditProcessState == 5">
							<i class="el-icon-circle-check" style="margin-right:5px"></i>
							<span style="fontSize:14px;">您已补发给客户，请及时提醒客户查收货物！</span>
						</p>
						<p class="refund" v-if="data.afterSaleInfo.auditProcessState == 3">
							<i class="el-icon-circle-check" style="margin-right:5px"></i>
							<span style="fontSize:14px;">当前售后已处理完成</span>
						</p>
						<p class="refund" v-if="data.afterSaleInfo.auditProcessState == 4">
							<i class="el-icon-circle-check" style="margin-right:5px"></i>
							<span style="fontSize:14px;">已退回售后申请，请及时与客户沟通并安抚客户！</span>
						</p>
						<p class="refund" v-if="data.afterSaleInfo.auditProcessState == 7">
							<i class="el-icon-circle-check" style="margin-right:5px"></i>
							<span style="fontSize:14px;">客户已退回发票，收到客户发票后，请及时处理!</span>
						</p>
						<!-- <p>剩余处理时间：</p> -->
						<div v-if="data.afterSaleInfo.auditProcessState == 1 && data.afterSaleInfo.afterSalesType == 1">
							<p>
								<span style="color:red;">1、请优先和客户沟通确认后，再选择处理方案;</span>
							</p>
							<p>
								<span>2、如己邮寄纸质发票，需客户退回发票再处理的请选择【要求退回发票】;</span>
							</p>
							<p>
								<span>3、如直接补寄纸质发票，请选择【售后已处理】，井添加相关凭证;</span>
							</p>
							<p>
								<span>4、如己上传电子发票，请选择【售后已处理】，并添加相关凭证;</span>
							</p>
							<p>
								<span>5、若您认为客户是无理售后，您可以【退回售后申请】，谨慎选择;</span>
							</p>
						</div>
						<div v-if="data.afterSaleInfo.auditProcessState == 6">
							<p>
								<span>1、等待客户退回发票，您可以通过在线客服或电话提醒客户！</span>
							</p>
							<p>
								<span>2、若客户超时未寄回，当前售后单将会被自动关闭。</span>
							</p>
							<p>
								<span>3、若已收客户寄回的发票，可点击“客户已退回”进行下一步处理</span>
							</p>
						</div>
						<div v-if="data.afterSaleInfo.auditProcessState == 1 && data.afterSaleInfo.afterSalesType == 2">
							<p>
								<span style="color:red;">1、请优先和客户沟通确认后，再选择处理方案；</span>
							</p>
							<p>
								<span>2、如需补寄纸质资质或已向客户发送电子版资质，请选择【售后已处理】，并添加相关凭证；</span>
							</p>
							<p>
								<span>3、若您认为客户是无理售后，您可以【退回售后申请】，谨慎选择；</span>
							</p>
						</div>
						<p class="item-left" style="justify-content: flex-start;">
							<el-button v-if="[1, 7].includes(data.afterSaleInfo.auditProcessState)" type="primary" size="small" @click="dialog.status = true; dialog.title = '售后已处理'">售后已处理</el-button>
							<el-button v-if="[1, 7, 10].includes(data.afterSaleInfo.auditProcessState)" type="primary" size="small" @click="dialog.status = true; dialog.title = '退回售后申请'">退回售后申请</el-button>
							<el-button v-if="[1].includes(data.afterSaleInfo.auditProcessState) && data.afterSaleInfo.afterSalesType == 1" type="primary" size="small" @click="showRefund">要求退回发票</el-button>
							<el-button v-if="[6].includes(data.afterSaleInfo.auditProcessState)" type="primary" size="small" @click="dialog.status = true; dialog.title = '货物退回确认'">客户已退回</el-button>
							<el-button v-if="[7, 10].includes(data.afterSaleInfo.auditProcessState)" type="primary" size="small" @click="dialog.status = true; dialog.title = '另外补发'">另外补发</el-button>
							<!-- v-if="[7, 10].includes(data.afterSaleInfo.auditProcessState)" -->
						</p>
					</common-header>
					<common-header class="card-box" title="协商历史" style="border-top:solid 1px #dedede;" :showFold="false" :shouHeightLine="false">
						<div style="max-height:500px;overflow:auto;">
							<div  v-for="(item, i) in data.auditRecords" :key="i" :showFold="true" class="list">
								<div style="fontSize:12px;">
									<p>{{item.createTime.split(' ')[0]}}</p>
									<p>{{item.createTime.split(' ')[1]}}</p>
								</div>
								<div style="overflow:auto;">
									<p  class="item-left" style="padding-left:25px;gap:5px;align-items:center;margin-top:0;">
										<img :src="item.logo" alt="" width="27" height="27">
										<span style="fontWeight:500;fontSize:16px;">{{item.chatter}}</span>
									</p>
									<!-- <p v-if="item.chatter" class="item-left">
										<span>客户名称：</span>
										<span>{{item.chatter}}</span>
									</p> -->
									<p v-if="item.operator" class="item-left">
										<span>操作账号：</span>
										<span :title="item.operator">{{item.operator}}</span>
									</p>
									<p v-if="item.afterSalesTypeName" class="item-left">
										<span>售后类型：</span>
										<span :title="item.afterSalesTypeName">{{item.afterSalesTypeName}}</span>
									</p>
									<p v-if="item.auditProcessStateName" class="item-left">
										<span>处理状态：</span>
										<span :title="item.auditProcessStateName">{{item.auditProcessStateName}}</span>
									</p>
									<!-- <p v-if="item.createTime" class="item-left">
										<span>创建时间：</span>
										<span>{{item.createTime}}</span>
									</p> -->
									<p v-if="item.evidences" class="item-left">
										<span>凭证：</span>
										<span>
											<img-list style="width:600px;" :maxShowLength="9" :value="item.evidences" :maxCol="4"></img-list>
										</span>
									</p>
									<el-collapse :value="['1','2','3']" v-if="item.reissueCredential || item.sellerAddressInfo || item.specialInvoiceInfo" style="margin: 0 20px;">
										<el-collapse-item v-if="item.reissueCredential" title="资质信息" name="1" disabled>
											<div>
												<p v-if="item.reissueCredential.corpCredential" class="item-left">
													<span>企业资质：</span>
													<span :title="item.reissueCredential.corpCredential">{{item.reissueCredential.corpCredential}}</span>
												</p>
												<p v-if="item.reissueCredential.drugSupervisionReport" class="item-left">
													<span>商品药检报告：</span>
													<span :title="item.reissueCredential.drugSupervisionReport">{{item.reissueCredential.drugSupervisionReport}}</span>
												</p>
												<p v-if="item.reissueCredential.productCredential" class="item-left">
													<span>商品首营资料：</span>
													<span :title="item.reissueCredential.productCredential">{{item.reissueCredential.productCredential}}</span>
												</p>
											</div>
										</el-collapse-item>
										<el-collapse-item v-if="item.sellerAddressInfo" title="收货信息" name="2" disabled>
											<div>
												<p class="item-left">
													<span>收件人：</span>
													<span :title="item.sellerAddressInfo.recipient">{{item.sellerAddressInfo.recipient}}</span>
												</p>
												<p class="item-left">
													<span>收货地址：</span>
													<span :title="item.sellerAddressInfo.deliveryAddress">{{item.sellerAddressInfo.deliveryAddress}}</span>
												</p>
												<p class="item-left">
													<span>收货人电话：</span>
													<span :title="item.sellerAddressInfo.receivingPhone">{{item.sellerAddressInfo.receivingPhone}}</span>
												</p>
												<p class="item-left">
													<span>快递说明：</span>
													<span :title="item.sellerAddressInfo.expressRemarks">{{item.sellerAddressInfo.expressRemarks}}</span>
												</p>
											</div>
										</el-collapse-item>
										<el-collapse-item v-if="item.specialInvoiceInfo" title="专票信息" name="3" disabled>
											<div>
												<p class="item-left">
													<span>公司名称：</span>
													<span :title="item.specialInvoiceInfo.companyName">{{item.specialInvoiceInfo.companyName}}</span>
												</p>
												<p class="item-left">
													<span>纳税人识别号：</span>
													<span :title="item.specialInvoiceInfo.enterpriseRegistrationNo">{{item.specialInvoiceInfo.enterpriseRegistrationNo}}</span>
												</p>
												<p class="item-left">
													<span>注册地址：</span>
													<span :title="item.specialInvoiceInfo.registerAddress">{{item.specialInvoiceInfo.registerAddress}}</span>
												</p>
												<p class="item-left">
													<span>电话：</span>
													<span :title="item.specialInvoiceInfo.phone">{{item.specialInvoiceInfo.phone}}</span>
												</p>
												<p class="item-left">
													<span>银行名称：</span>
													<span :title="item.specialInvoiceInfo.bankName">{{item.specialInvoiceInfo.bankName}}</span>
												</p>
												<p class="item-left">
													<span>银行账号：</span>
													<span :title="item.specialInvoiceInfo.acct">{{item.specialInvoiceInfo.acct}}</span>
												</p>
												<p class="item-left">
													<span>接受电子专票：</span>
													<span>{{item.specialInvoiceInfo.isElectronicInvoice ? '接受' : '不接受'}}</span>
												</p>
											</div>
										</el-collapse-item>
									</el-collapse>
									<p v-if="item.expressName" class="item-left">
										<span>物流公司：</span>
										<span :title="item.expressName">{{item.expressName}}</span>
									</p>
									<p v-if="item.expressNo" class="item-left">
										<span>运单号：</span>
										<span :title="item.expressNo">{{item.expressNo}}</span>
									</p>
									<p v-if="item.deliveryAddress" class="item-left">
										<span>收货地址：</span>
										<span :title="item.deliveryAddress">{{item.deliveryAddress}}</span>
									</p>
									<p v-if="item.remarks" class="item-left">
										<span>补充说明：</span>
										<span :title="item.remarks">{{item.remarks}}</span>
									</p>
									<p v-if="item.incorrectInvoiceInfo" class="item-left">
										<span>错误信息：</span>
										<span :title="item.incorrectInvoiceInfo">{{item.incorrectInvoiceInfo}}</span>
									</p>
								</div>
							</div>
						</div>
					</common-header>
				</div>
			</div>
		</common-header>
		<el-dialog :visible.sync="dialog.status" :title="dialog.title" width="620px" @close="reset();">
			<p v-if="dialog.title == '要求退回发票'" style="color:red;">请确认退货信息。在店铺管理-退货地址管理页配置好退货地址，此处可自动获取！</p>
			<p v-if="dialog.title == '货物退回确认'" style="color:red;">请及时与客户沟通，确认客户已发货</p>
      <p v-if="dialog.title == '退回售后申请'" style="color:red;">温馨提示：如果客户选错了原因，请引导客户选择正确原因的售后；若已经处理了售后，请上传处理的截图给客户</p>
			<el-form ref="form" label-width="120px" :rules="rules" :model="dialog.form" v-loading="dialog.loading">
				<el-row>
					<el-col v-if="dialog.title == '另外补发'">
						<el-form-item label="快递公司:" prop="expressName" ref="expressName" :rules="rules.expressName">
							<el-select v-model="dialog.form.expressName" size="small">
								<el-option v-for="item in address" :key="item.logisticsCompanyCode" :label="item.logisticsCompanyName" :value="item.logisticsCompanyName"></el-option>
							</el-select>
						</el-form-item>
					</el-col>
					<el-col v-if="dialog.title == '另外补发'">
						<el-form-item label="快递单号:" prop="expressNo" ref="expressNo">
							<el-input :maxlength="100" v-model="dialog.form.expressNo" size="small" @input="expressNo"></el-input>
						</el-form-item>
					</el-col>
					<el-col v-if="dialog.title == '要求退回发票'">
						<el-form-item label="收货人姓名:" prop="recipient" ref="recipient">
							<el-input :maxlength="100" v-model="dialog.form.recipient" size="small"></el-input>
						</el-form-item>
					</el-col>
					<el-col v-if="dialog.title == '要求退回发票'">
						<el-form-item label="收货人手机号:" prop="receivingPhone">
							<el-input :maxlength="100" v-model="dialog.form.receivingPhone" size="small"></el-input>
						</el-form-item>
					</el-col>
					<el-col v-if="dialog.title == '要求退回发票'">
						<el-form-item label="收货地址:" prop="deliveryAddress">
							<el-input :maxlength="100" v-model="dialog.form.deliveryAddress" size="small"></el-input>
						</el-form-item>
					</el-col>
					<el-col v-if="dialog.title == '要求退回发票'">
						<el-form-item label="快递说明:">
							<el-input :maxlength="100" v-model="dialog.form.expressRemarks" size="small"></el-input>
						</el-form-item>
					</el-col>
					<el-col>
						<el-form-item label="回复内容:" :prop="['退回售后申请','售后已处理'].includes(dialog.title) ? 'sellerRemark' : ''">
							<el-input type="textarea" :maxlength="200" v-model="dialog.form.sellerRemark" :autosize="{ minRows: 2, maxRows: 4}" show-word-limit></el-input>
							<p style="margin:0;color:red;">内容将直接回复给客户，请礼貌用语，限200字。</p>
						</el-form-item>
					</el-col>
					<el-col v-if="dialog.title != '货物退回确认'">
						<el-form-item label="凭证:">
							<img-upload v-model="dialog.form.evidences" :btnStyle="{width:'200px', height: '90px'}" :upload="upload" :allowMaxFileCount="9" :allowMaxFileSize="5" msg="最多上传9张, 且大小不超过5MB"></img-upload>
						</el-form-item>
					</el-col>
				</el-row>
			</el-form>
			<p style="text-align:end;margin-bottom:0;">
				<el-button @click="dialog.status = false; reset();" size="small">取消</el-button>
				<el-button @click="commit" type="primary" size="small">确定</el-button>
			</p>
		</el-dialog>
		<el-dialog :visible="buyer.status" title="药帮忙业务员" width="450px" @close="buyer.status = false;">
			<div style="padding:0 15px;">
				<p class="item-left" style="fontSize:16px !important;margin: 10px 0">
					<span style="width:100px">姓名：</span>
					<span>{{ buyer.name }}</span>
				</p>
				<p class="item-left" style="fontSize:16px !important;margin: 10px 0">
					<span style="width:100px">电话：</span>
					<span>{{ buyer.phone }}</span>
				</p>
				<p style="text-align:end;margin-bottom:0;">
					<el-button @click="buyer.status = false;buyer.name='';buyer.phone=''" type="primary" size="small">确定</el-button>
				</p>
			</div>
		</el-dialog>
	</div>
</template>

<script>
import commonHeader from "./components/common-header.vue"
import { queryBDInfoByMerchantId } from '../../api/order/index'
import { queryMsgToken } from '@/api/home';
import { getDetail, uploadImage, verify, queryReturnAddress, queryLogistics } from "../../api/afterSaleManager/index"
import imgUpload from './components/img-upload.vue'
import imgList from './components/imgList.vue'
import process from "./components/process.vue"
export default {
	name: 'afterSaleManagerDetail',
	components: {
		commonHeader,
		process,
		imgUpload,
		imgList
	},
	data() {
		return {
			data: {
			},
			afterSalesNo: '',
			loading: true,
			dateLine1: '',
			time: {
				d: 0,
				h: 0,
				m: 0,
				s: 0
			},
			address: [],
			dialog: {
				title: '',
				status: false,
				form: {
					recipient: '',   //收件人姓名
					expressRemarks: '', //快递说明
					receivingPhone: '', //收货人电话
					deliveryAddress: '',  //收货地址
					sellerRemark: '',
					expressName: '',   //快递公司
					expressNo: '',     //快递单号
					evidences: []
				},
				loading: false
			},
			buyer: {
				name: '',
				phone: '',
				status: false,
				loading: false
			},
			rules: {
				recipient: [{required: true, message: '请输入收货人姓名', trigger: 'blur'}],
				receivingPhone: [{required: true, message: '请输入收货人手机号', trigger: 'blur'}],
				sellerRemark: [{required: true, message: '请输入回复内容', trigger: 'blur'}],
				expressName: [{required: true, message: '请输入快递公司', trigger: 'change'}],
				expressNo: [{required: true, message: '请输入快递', trigger: 'blur'}],
				deliveryAddress: [{required: true, message: '请输入收货地址', trigger: 'blur'}]
			},
			activeNames: [],
      Address: []
		}
	},
	computed: {
		dateLine() {
		return `${this.time.d < 10 ? '0' + this.time.d : this.time.d}天
				${this.time.h < 10 ? '0' + this.time.h : this.time.h}小时
				${this.time.m < 10 ? '0' + this.time.m : this.time.m}分钟
				${this.time.s < 10 ? '0' + this.time.s : this.time.s}秒`
		}
	},
	async activated() {
		await this.init();
	},
	async mounted() {
		queryLogistics().then(res => {
			if (res.code == 0) {
				this.address = res.data;
			}
		})
	},
	methods: {
		expressNo(val) {
			this.dialog.form.expressNo = val.replace(/[^a-z|A-Z|0-9|\-]/g,'');
		},
		showRefund() {
			if (this.dialog.loading) return ;
			this.dialog.loading = true
			queryReturnAddress(this.data.afterSaleInfo.provinceCode).then(res => {
				if (res.code == '0') {
				this.dialog.form.recipient = res.result.contactor;
				this.dialog.form.expressRemarks = res.result.remark;
				this.dialog.form.receivingPhone = res.result.contactorMobile;
				this.dialog.form.deliveryAddress = res.result.address;
				}
			}).finally(() => {
				this.dialog.status = true; 
				this.dialog.title = '要求退回发票'
				this.dialog.loading = false;
			})
		},
		goBack() {
			let path = this.$route.fullPath
			path = decodeURI(path)
			window.closeTab(path, true)
			setTimeout(() => {
				window.openTab('/afterSaleManager')
			},0)
		},
		getSubType(subType) {
			switch (subType) {
				case 3:
					return '申请专票';
				case 1:
					return '无票';
				case 2:
					return '错票';
			}
			return ''
		},
		async openService(key) {
			// console.log(5555555555, key)
			const res = await queryMsgToken();
			if (res && res.code === 0) {
				const { token, domainName } = res.data;
				const str = `&orgId=${key}`;
				const userId = `&userId=${res.data.userId}`;
				console.log(domainName + token + str+userId);
				window.open(domainName + token + str+userId);
			} else {
				this.$message.error(res.message);
			}
		},
		copy(title,value) {
			navigator.clipboard.writeText(value);
			this.$message.success(`已复制${title}`)
		},
		upload(file) {
			return new Promise((resolve, reject) => {
				uploadImage(file).then(res => {
					if (res.success) {
						resolve(res.data);
						this.$message.success("上传成功")
						return
					}
					if (res.fail) {
						resolve(false)
						this.$message.error("上传失败")
						return
					}
				}).catch(err => {
					resolve(false);
				})
			})
		},
		commit() {
			console.log(this.dialog.form);
			if (this.dialog.loading) return;
			this.dialog.loading = true;
			this.$refs.form.validate().then(res => {
				const { sellerRemark, evidences } = this.dialog.form
				const list = evidences.map(item => {
					const arr = item.split('/');
					arr.shift();
					arr.shift();
					arr.shift();
					return '/' + arr.join('/');
				})
				let form = {};
				switch(this.dialog.title) {
					case '售后已处理': {
						form = {
							sellerRemark,
							auditProcessState: 3,
							evidences: list
						}
						break;
					}
					case '退回售后申请': {
						form = {
							sellerRemark,
							auditProcessState: 4,
							evidences: list
						}
						break;
					}
					case '要求退回发票': {
						form = Object.assign({}, this.dialog.form);
						delete form.expressName;
						form.auditProcessState = 6;
						form.evidences = list;
						break;
					}
					case '货物退回确认': {
						form = {
							sellerRemark,
							auditProcessState: 10,
						};
						break;
					}
					case '另外补发': {
						form = {
							auditProcessState: 5,
							expressName: this.dialog.form.expressName,
							expressNo: this.dialog.form.expressNo,
							evidences: list
						}
					}
				}
				verify({
					afterSalesNo: this.afterSalesNo,
					...form
				}).then(res => {
					if (res.success) {
						this.$message.success("处理成功")
						this.dialog.status = false;
						this.init();
						this.reset();
					}
					if (res.fail) {
						this.$message.error(res.message);
					}
				}).finally(() => {
					this.dialog.loading = false;
				})
			}).catch(err => {
				this.dialog.loading = false;
			})
		},
		reset() {
			for (const key in this.dialog.form) {
				this.dialog.form[key] = '';
			}
			this.dialog.form.evidences = [];
			this.$refs.form.resetFields();
		},
		async init() {
			this.afterSalesNo = this.$route.query.afterSalesNo;
			if (this.afterSalesNo) {
				try {
					const res = await getDetail({ afterSalesNo: this.afterSalesNo })
					if (res.fail) {
						this.$message.error("查询失败")
						this.goBack();
					}
					if (res.success) {
						if (res.data.afterSaleInfo && res.data.afterSaleInfo.afterSalesDetailInfo && res.data.afterSaleInfo.afterSalesDetailInfo.itemList) {
							res.data.afterSaleInfo.afterSalesDetailInfo.itemList = res.data.afterSaleInfo.afterSalesDetailInfo.itemList.map(item => {
								const arr = item.split('：')
								return {
									itemTitle: arr[0],
									itemValue: arr[1]
								}
							});
						}
						this.data = res.data
						this.dateLine1 = new Date(res.data.afterSaleInfo.createTime).getTime() + 60*60*24*1000*7
            			this.timer();
					}
					this.loading = false;
				} catch {
					this.loading = false;
					this.$message.error("服务器异常")
					this.goBack();
				}
			} else {
				this.$message.error("无售后单号")
				this.goBack();
			}
			this.loading = false;
		},
		showYBM(id) {
			if (this.buyer.loading) return
			this.buyer.loading = true
			queryBDInfoByMerchantId({merchantId: id}).then(res => {
				if (res.code == 0) {
					this.buyer.name = res.result.name;
					this.buyer.phone = res.result.phone;
					this.buyer.status = true
				} else {
					this.$message.error(res.msg);
				}
			}).finally(() => {
				this.buyer.loading = false;
			})
		},
		timer() {
			const t = setTimeout(() => {
				clearTimeout(t)
				const current = this.dateLine1 - Date.now();
				if (current < 0) {
				this.time = {
					d: 0,
					h: 0,
					m: 0,
					y: 0
				}
				} else {
				const c = current / 1000
					this.time.d = parseInt((c / 60 / 60 / 24)).toFixed(0),
					this.time.h = parseInt((c / 60 / 60 % 24)).toFixed(0),
					this.time.m = parseInt((c / 60 % 60)).toFixed(0),
					this.time.s = parseInt((c % 60)).toFixed(0)
					this.timer();
				}
			}, 1000)
		},
	}
}
</script>

<style lang="scss" scoped>
.item-left {
	width: 100%;
	display: flex;
	margin: 10px 0;
	align-items: flex-start;
	> span:first-child {
		flex-shrink: 0;
		width: 100px;
		text-align: end;
		color:#8d8d8d;
	}
	> span:nth-child(2) {
		flex-grow: 1;
		display: -webkit-box;//对象作为弹性伸缩盒子模型显示
        -webkit-box-orient: vertical;//设置或检索伸缩盒对象的子元素的排列方式
        -webkit-line-clamp: 100;//溢出省略的界限
		overflow: hidden;
		text-overflow: ellipsis;
		text-align: start !important;
	}
	> span:nth-child(3) {
		flex-shrink: 0;
		margin: 0 5px;
		cursor: pointer;
		color: rgb(0, 134, 211);
	}
}
.detail-content {
	display:flex;
	padding: 5px;
	gap: 10px;
	overflow: auto;
	> div:first-child {
		flex: 0.4;
		min-width: 500px;
		flex-shrink: 0;
	}
	> div:last-child {
		flex: 0.6;
		min-width: 700px;
	}
	.card-box {
		border-radius: 7px;
		margin-bottom: 10px;
	}
}
.refund {
	padding: 0 12px;
	height: 40px;
	display: flex;
	align-items: center;
	font-size: 20px;
	border-radius: 5px;
	background: #ebf6ff;
	border: solid 1px #c4d8e7;
	color:#0087d4
}
.close {
	padding: 0 12px;
	height: 40px;
	display: flex;
	align-items: center;
	font-size: 20px;
	border-radius: 5px;
	background: #ffffeb;
	border: solid 1px #e7e6c4;
	color:#d4bf00
}
.warning {
	padding: 0 12px;
	height: 40px;
	display: flex;
	align-items: center;
	font-size: 20px;
	border-radius: 5px;
	background: #ffebeb;
	border: solid 1px rgb(231, 196, 196);
	color:#d40000;
}
::v-deep   .el-input__count {
	line-height: normal !important;
}
::v-deep   .con {
	color: red;
	justify-content: start;
	align-items: center !important;
	.prefix {
		font-size: 14px;
		color: black;
	}
}
.card-box .list {
	display: flex;
	justify-content: start;
	margin-bottom: 10px;
	> div:last-child {
		flex-grow: 1;
	}
	> div:first-child {
		position: relative;
		padding: 0 0 0 20px;
		p {
			margin: 0;
			font-size: 14px;
			color: rgb(173, 173, 173);
			transition: all 0.2s;
		}
	}
	> div:first-child::before {
		content: '';
		position: absolute;
		width: 2px;
		height: calc(100% - 19px);
		top: 13px;
		left: 5px;
		background: rgb(173, 173, 173);
		transition: all 0.2s;
	}
	> div:first-child::after {
		content: '';
		position: absolute;
		width: 10px;
		height: 10px;
		top: 13px;
		left: 5px;
		transform: translateX(-45%);
		background: rgb(173, 173, 173);
		border-radius: 50%;
		transition: all 0.2s;
	}
}
.card-box .list:hover >div:first-child p {
	color: #0087d4;
}
.card-box .list:hover >div:first-child::before,
.card-box .list:hover >div:first-child::after {
	background: #0087d4;
}
</style>

