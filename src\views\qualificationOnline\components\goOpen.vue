<template>
  <div>
    <p v-if="status != 1" style="font-weight: 500;color: red;padding-left: 20px;margin: 5px 0;">
      <span>您未开通电子签服务，请先开通电子签认证，再上传首营/药检，如有疑问可联系您的商家运营处理。</span>
      <el-button size="small" type="primary" @click="go">去开通</el-button>
    </p>
    <p v-if="erpStatus == 0" style="font-weight: 500;color: red;padding-left: 20px;margin: 5px 0;">
      您的ERP对接暂未对接商品批号信息，将导致新批号出库后客户无法及时下载药检报告，请在技术对接群联系商家运营对接处理。
    </p>
  </div>
</template>
<script>
import { platformServiceAgreementGetSealStatus } from '../../../api/qualificationOnline/index'
export default {
  props: {
    // 1: 首营，2：药检
    from: ''
  },
  data() {
    return {
      status: 0, //1: 已开通，0：未开通
      erpStatus: 0, //0: 失败  1:成功
    }
  },
  activated() {
    console.log(this);
    this.erpStatus = this.$store.state.app.shopConfig.erpConnStatus;
    platformServiceAgreementGetSealStatus().then(res => {
      if (res.code === 0) {
        this.status = res.result.status;
      }
    })
  },
  methods: {
    go() {
      window.openTab('/companyOpenAccount/platformServeAgreement')
    }
  }
}
</script>
<style scoped>
</style>
