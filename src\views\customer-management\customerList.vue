<template>
  <div class="customerWrap">
    <div class="Fsearch">
      <el-row type="flex" align="middle" justify="space-between" class="my-row">
        <el-row type="flex" align="middle">
          <span class="sign" />
          <div class="searchMsg">客户列表</div>
        </el-row>
      </el-row>
    </div>
    <div>
      <div v-if="countCustomerQualification > 0" class="div-info">
        <p>
          <span class="status-span">
            <i style="background: #dcdfe6" />客户信息有变更
          </span>
          <el-tooltip effect="dark" placement="top">
            <template #content>客户信息发生变更，请您仔细核对ERP中的客户资料，并及时更新</template>
            <i class="el-icon-warning-outline"></i>
          </el-tooltip>
        </p>
        <p class="refundCountBox">
          <span class="refundCount">{{ countCustomerQualification }}</span>
          <el-button
            type="primary"
            size="mini"
            class="seeCount"
            @click="searchCustomerQualification"
          >{{ isCountCustomerQualification ? '筛选' : '取消' }}</el-button>
        </p>
      </div>
    </div>
    <el-row :gutter="24" style="padding: 0 8px" class="searchMy">
      <el-form ref="form" :model="ruleForm" size="small">
        <el-row :span="24">
          <el-col :span="6">
            <el-form-item prop="merchantId">
              <el-input v-model="ruleForm.merchantId" placeholder="请输入">
                <template slot="prepend">药店ID</template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item prop="sellerUserId">
              <el-input v-model="ruleForm.sellerUserId" placeholder="请输入">
                <template slot="prepend">ERP编码</template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item prop="merchantName">
              <el-input v-model="ruleForm.merchantName" placeholder="请输入">
                <template slot="prepend">药店名称</template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item prop="mobile">
              <el-input v-model="ruleForm.mobile" placeholder="请输入">
                <template slot="prepend">电话</template>
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :span="24">
          <el-col :span="6">
            <el-form-item prop="businessTypeId">
              <div class="flex">
              <span class="search-title">药店类型</span>
              <el-select v-model="ruleForm.businessTypeId" size="small" placeholder="全部">
                <el-option
                  v-for="item in businessTypeList"
                  :key="item.businessTypeId"
                  :label="item.businessTypeName"
                  :value="item.businessTypeId"
                />
              </el-select>
            </div>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item prop="provId">
              <div class="flex">
              <span class="search-title">省份</span>
              <el-select
                v-model="ruleForm.provId"
                size="small"
                placeholder="全部"
                @change="getProvince('getCity', $event)"
              >
                <el-option
                  v-for="item in proviceList"
                  :key="item.regionCode"
                  :label="item.regionName"
                  :value="item.regionCode"
                />
              </el-select>
            </div>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item prop="cityId">
              <div class="flex">
              <span class="search-title">城市</span>
              <el-select
                v-model="ruleForm.cityId"
                size="small"
                placeholder="全部"
                @change="getProvince('getArea', $event)"
              >
                <el-option
                  v-for="item in cityList"
                  :key="item.regionCode"
                  :label="item.regionName"
                  :value="item.regionCode"
                />
              </el-select>
            </div>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item prop="areaId">
              <div class="flex">
              <span class="search-title">区县</span>
              <el-select v-model="ruleForm.areaId" size="small" placeholder="全部">
                <el-option
                  v-for="item in areaList"
                  :key="item.regionCode"
                  :label="item.regionName"
                  :value="item.regionCode"
                />
              </el-select>
            </div>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :span="24">
          <el-col :span="12">
            <el-form-item>
              <span class="search-title">首次下单时间</span>
              <div style="display: table-cell; line-height: 24px">
                <el-date-picker
                  v-model="orderStartTime"
                  type="datetimerange"
                  format="yyyy-MM-dd HH:mm:ss"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  :default-time="['00:00:00', '23:59:59']"
                  @change="orderStartTimeChange"
                />
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item>
              <span class="search-title">最后下单时间</span>
              <div style="display: table-cell; line-height: 24px">
                <el-date-picker
                  v-model="time"
                  type="datetimerange"
                  format="yyyy-MM-dd HH:mm:ss"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  :default-time="['00:00:00', '23:59:59']"
                  @change="dateOnChange"
                />
              </div>
            </el-form-item>
          </el-col>

        </el-row>
        <el-row :span="24">
          <el-col :span="6">
            <el-form-item prop="accountStatus">
              <div class="flex">
              <span class="search-title">开户状态</span>
              <el-select v-model="ruleForm.accountStatus" size="small" placeholder="全部">
                <el-option label="全部" :value="''" />
                <el-option label="未开户" :value="0" />
                <el-option label="已开户" :value="1" />
              </el-select>
            </div>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :span="24" style="text-align: right;padding-bottom: 15px">
          <el-col :span="24">
            <el-button type="primary" size="small" @click="toSerachForm()">查询</el-button>
            <el-button size="small" @click="resetForm">重置</el-button>
          </el-col>
        </el-row>
      </el-form>
    </el-row>
    <el-row>
      <el-button v-permission="['customer_list_export']" type="primary" size="small" @click="exportExcel">导出</el-button>
      <el-button v-permission="['customer_list_batchDownloadQualification']" type="primary" size="small" @click="downLoad('batch')">批量下载资质</el-button>
      <el-button
        type="primary"
        size="small"
        @click="handleBatchUpdateErpCode"
      >批量修改客户ERP编码</el-button>
    </el-row>
    <el-row style="marginTop: 20px">
      <!-- <xyy-table
        :data="tableData"
        :list-query="listQuery"
        :col="columns"
        :operation="operation"
        :has-selection="true"
        @get-data="getDataList"
        @operation-click="operationClick"
        @selectionCallback="selectRows"
      />-->
      <el-table
        stripe
        :data="tableData"
        border
        :header-cell-style="{ background: '#f9f9f9' }"
        @selection-change="selectRows"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="merchantId" label="药店ID" width="150" />
        <el-table-column prop="sellerUserId" label="ERP编码" width="150" />
        <!-- <el-table-column prop="merchantName" label="药店名称" width="150" /> -->
        <el-table-column label="药店名称" prop="merchantName" width="150">
          <template slot-scope="scope">
            <div>{{ scope.row.merchantName }}</div>
            <div
              v-if="scope.row.qualificationChanged && scope.row.qualificationChanged === 1"
              class="informationChanges"
              @click="btnClick(scope.row)"
            >信息有变更</div>
          </template>
        </el-table-column>
        <el-table-column prop="address" label="药店地址" width="150" />
        <el-table-column prop="prov" label="省市" width="150">
          <template slot-scope="scope">
            <span>{{ scope.row.prov }}{{ scope.row.city }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="contactor" label="联系人" width="150" />
        <el-table-column prop="mobile" label="电话" width="150" />
        <el-table-column prop="businessTypeName" label="药店类型" width="150" />
        <el-table-column prop="accountStatusDesc" label="开户状态" width="150" />
        <el-table-column prop="firstOrderCreatetime" label="首次下单时间" width="160" />
        <el-table-column prop="lastOrderCreatetime" label="最后下单时间" width="160" />
        <el-table-column label="操作" width="120" fixed="right">
          <template slot-scope="scope">
            <div class="editText">
              <div v-permission="['customer_list_openAccount']" v-if="scope.row.accountStatus === 0" @click="operationClick(1, scope.row)">开户</div>
              <div v-permission="['customer_list_downloadQualification']" @click="operationClick(2, scope.row)">下载资质</div>
              <div @click="operationClick(3, scope.row)">查看资质</div>
              <div
                v-permission="['customer_list_editErpCode']"
                v-if="scope.row.accountStatus === 1"
                @click="operationClick(4, scope.row)"
              >编辑ERP编码</div>
              <div @click="operationClick(5, scope.row)">开票信息</div>
              <div @click="operationClick(6, scope.row)">操作日志</div>
              <div @click="operationClick(7, scope.row)">查看信息变更记录</div>
            </div>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination-container">
        <div class="pag-text">
          共 {{ listQuery.total }} 条数据，每页{{ listQuery.pageSize }}条，共{{
          listQuery.total > 0 ? Math.ceil(listQuery.total / listQuery.pageSize) : 0
          }}页
        </div>
        <el-pagination
          :page-sizes="[10, 20, 30, 50]"
          prev-text="上一页"
          next-text="下一页"
          layout="sizes, prev, pager, next, jumper"
          :current-page="listQuery.pageNum"
          :total="listQuery.total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-row>
    <el-dialog
      :title="btnType === 4 ? '编辑ERP编码' : '开户提示'"
      :visible="tipVisible"
      @close="cancelDialog"
    >
      <p v-if="btnType === 4 ">客户名称：{{ editRow.merchantName }}</p>
      <p v-else>开户前请确认客户提交的资料已经满足您要求的开户条件！</p>
      <p>
        客户ERP编码：
        <el-input v-model="erpCode" style="width: 200px" />
      </p>
      <div v-if="singTableData.length > 0">
        <p style="color: #ff2121">*系统匹配到多个ERP客户信息，请选择：</p>
        <el-table
          ref="singleTable"
          :data="singTableData"
          highlight-current-row
          style="width: 100%"
          border
          @current-change="handleSingCurrentChange"
        >
          <el-table-column type="index" width="50" />
          <el-table-column property="customerCode" label="ERP客户编码" />
          <el-table-column property="customerName" label="ERP客户名称" />
        </el-table>
      </div>
      <span slot="footer">
        <el-button @click="cancelDialog">取 消</el-button>
        <el-button type="primary" @click="confirmDialog">确 定</el-button>
      </span>
    </el-dialog>
    <invoice-info
      v-if="dialogVisible"
      :dialog-visible="dialogVisible"
      :merchant-id="editRow.merchantId"
      @cancelDialog="cancelDialog"
    />
    <custLog
      v-if="custLogDialogVisible"
      ref="custLog"
      :name="editRow.merchantName"
      :merchant-id="editRow.merchantId"
      :sku-log-dialog-visible.sync="custLogDialogVisible"
    />
    <export-tip
      :change-export="changeExport"
      @handleExoprClose="handleExoprClose"
      @handleChangeExport="handleChangeExport"
    />
    <informationChanges-log
      v-if="informationChangesLogVisible"
      :dialog-visible="informationChangesLogVisible"
      :merchant-id="editRow.merchantId"
      :all-list="isAllList"
      @cancelDialog="cancelDialog"
      @gitList="getDataList"
    />
    <BatchModifyProduct
      v-if="batchModifyVisible"
      :batch-modify-product-visible.sync="batchModifyVisible"
      :type="5"
      @refreshTable="getDataList"
    />
  </div>
</template>
<script>
import { getList, getBusinessType, getProvince, downloadBatch, editErpCode, exportExcelList, apiCountCustomerQualificationChanged } from '@/api/customer-management/index';
import custLog from 'components/customer/custLog';
import exportTip from '@/views/other/components/exportTip';
import { getListByName } from '@/api/order';
import informationChangesLog from '@/views/order/components/informationChangesLog.vue';
import { actionTracking } from '@/track/eventTracking';
import invoiceInfo from './invoiceInfo';
import BatchModifyProduct from '../product/components/batchModifyProduct';
import { downloadZip } from '@/utils/download'

export default {
  name: 'CustomerList',
  components: { invoiceInfo, custLog, exportTip, informationChangesLog, BatchModifyProduct },
  data() {
    return {
      batchModifyVisible: false,
      orderStartTime: [new Date(), new Date()],
      time: '',
      dialogVisible: false,
      erpCode: '',
      currentRow: '',
      tipVisible: false,
      businessTypeList: [],
      proviceList: [],
      cityList: [],
      areaList: [],
      selectedRows: [],
      editRow: {},
      tableData: [],
      btnType: null,
      listQuery: {
        pageSize: 10,
        pageNum: 1,
        total: 0,
      },
      ruleForm: {
        merchantId: '',
        businessTypeId: '',
        accountStatus: '',
        firstOrderStartTime: '',
        firstOrderEndTime: '',
        startTime: '',
        endTime: '',
        provId: '',
        cityId: '',
        areaId: '',
        sellerUserId: '',
        merchantName: '',
        mobile: '',
        qualificationChanged: null,
      },
      custLogDialogVisible: false,
      changeExport: false,
      singTableData: [],
      countCustomerQualification: 0,
      isCountCustomerQualification: true,
      informationChangesLogVisible: false,
      isAllList: false,
    };
  },
  created() {
    this.getBusinessType();
    this.getProvince('getProv');
  },
  mounted() {
    if (Number(this.$route.query.homeEnter) !== 1) {
      this.getDataList();
    }
  },
  activated() {
    this.activate();
  },
  methods: {
    handleBatchUpdateErpCode() {
      this.batchModifyVisible = true;
    },
    // 查询客户资质有变更的客户数量
    countCustomerQualificationChanged() {
      apiCountCustomerQualificationChanged().then((res) => {
        this.countCustomerQualification = (res || {}).data || 0;
        if (this.countCustomerQualification > 0 && !this.isCountCustomerQualification) {
          this.ruleForm.qualificationChanged = 1;
        } else {
          this.ruleForm.qualificationChanged = null;
        }
      });
    },
    getErpList() {
      getListByName({ merchantName: this.editRow.merchantName }).then((res) => {
        console.log(res);
        if (res.code === 0) {
          this.singTableData = res.data?res.data:[];
        } else {
          this.singTableData = [];
        }
      });
    },
    handleSingCurrentChange(val) {
      if (val.customerId === this.currentRow) {
        this.$refs.singleTable.setCurrentRow();
        this.currentRow = '';
        this.erpCode = '';
      } else {
        this.currentRow = val.customerId;
        this.erpCode = val.customerCode;
      }
    },
    activate() {
      const { query } = this.$route;
      if (query && Object.keys(query).length > 0) {
        this.resetForm('clearParams');
        Object.keys(query).map((key) => {
          if (key === 'orderStartTime') {
            this.orderStartTime = query[key];
          } else {
            this.$set(this.ruleForm, key, query[key]);
          }
        });
        this.orderStartTimeChange(this.orderStartTime);
        this.$nextTick(() => {
          this.getDataList();
        });
      }
    },
    // 操作选项
    operationClick(type, row) {
      this.btnType = type;
      this.editRow = row;
      if (type === 1 || type === 4) { // 开户/编辑erp编码
        this.tipVisible = true;
        this.erpCode = type === 4 ? row.sellerUserId : '';
        this.singTableData = [];
        if (type === 4 && this.erpCode) {
          return
        } else {
          this.getErpList();
        }
      } else if (type === 2) { // 下载资质
        this.downLoad(row);
      } else if (type === 3) { // 查看资质
        this.$router.push(`/customerQualification?merchantId=${row.merchantId}`);
        const local = { ...this.listQuery, ...this.ruleForm };
        this.util.setLocal('localQuery', local);
      } else if (type === 5) { // 开票信息
        this.dialogVisible = true;
      } else if (type === 6) {
        this.custLogDialogVisible = true
      } else if (type === 7) {
        actionTracking('customer_information_change_log', {})
        // 信息有变更
        this.isAllList = true;
        this.informationChangesLogVisible = true
      }
    },
    // 下载资质
    downLoad(data) {
      let merchantIds = [];
      if (data === 'batch') {
        if (this.selectedRows.length === 0) {
          this.$message.error('请选择需要下载的资质');
          return;
        }
        merchantIds = this.selectedRows.map(item => String(item.merchantId));
      } else {
        merchantIds.push(data.merchantId);
      }
      downloadBatch(merchantIds).then(async (res) => {
        if(res.code == 0 && res.result) {
          const arr = [];
          for(let i in res.result) {
            let obj = {};
            obj.name = i;
            obj.value = res.result[i];
            arr.push(obj);
          }
          downloadZip(arr, "导出文件",2); //传2 下载时按文件夹区分
        } else {
          this.$message.error(res.msg || '下载失败！请稍后再试！');
        }
      }).catch(() => {});
    },
    downLoadFile(res, batch) {
      const blob = new Blob([res]); // 接受文档流
      if ('msSaveOrOpenBlob' in navigator) {
        // IE下的导出
        window.navigator.msSaveOrOpenBlob(blob, `${batch ? '导出文件' : (this.editRow || {}).merchantName}.zip`); // 设置导出的文件名
      } else {
        // 非ie下的导出
        const a = document.createElement('a');
        const url = window.URL.createObjectURL(blob);
        const filename = `${batch ? '导出文件' : (this.editRow || {}).merchantName}.zip`; // 设置导出的文件名
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
      }
    },
    handleChangeExport(info) {
      this.changeExport = false;
      if (info === 'go') {
        const path = '/downloadList'
        window.openTab(path)
        //that.$router.push({ path: '/downloadList' });
      }
    },
    handleExoprClose() {
      this.changeExport = false;
    },
    // 导出
    exportExcel() {
      //window.open(`/orgUserRelation/exportExcel${this.getParams({ ...this.ruleForm })}`);
      exportExcelList(this.ruleForm).then((res) => {
        if (res.code !== 0) {
          this.$message.error(res.message);
          return;
        }
        this.changeExport = true;
      })
    },
    // 获取get请求参数
    getParams(params) {
      let queryStr = '?';
      Object.keys(params).forEach((key) => {
        queryStr += `${key}=${params[key]}&`;
      });
      queryStr = queryStr.substr(0, queryStr.length - 1);
      return queryStr;
    },
    // 关闭弹框
    cancelDialog() {
      this.dialogVisible = false;
      this.tipVisible = false;
      this.informationChangesLogVisible = false;
      this.erpCode = '';
    },
    // 开户/编辑erp
    confirmDialog() {
      editErpCode({
        id: this.editRow.id || null,
        sellerUserId: this.erpCode,
      }).then((res) => {
        if (res.code === 0) {
          this.tipVisible = false;
          this.erpCode = '';
          this.$message.success('保存成功');
          this.getDataList();
        } else {
          this.$message.error(res.msg);
        }
      }).catch((err) => {
        this.erpCode = '';
        this.$message.error(err);
      });
    },
    // 选中回调
    selectRows(rows) {
      this.selectedRows = rows;
    },
    // 获取药店类型
    getBusinessType() {
      getBusinessType().then((res) => {
        (res.data || []).unshift({
          businessTypeName: '全部',
          businessTypeId: '',
        });
        this.businessTypeList = res.data || [];
      });
    },
    // 省市区
    getProvince(type, e, from) {
      const local = this.util.getLocal('localQuery');
      const pms = { parentCode: e || null };
      getProvince(pms).then((res) => {
        (res.data || []).unshift({
          regionName: '全部',
          regionCode: '',
        });
        if (type === 'getProv') {
          this.proviceList = res.data || [];
          if (!local && !from) {
            this.cityList = [];
            this.areaList = [];
            this.ruleForm.cityId = '';
            this.ruleForm.areaId = '';
          }
        } else if (type === 'getCity') {
          this.cityList = res.data || [];
          if (!local && !from) {
            this.areaList = [];
            this.ruleForm.cityId = '';
            this.ruleForm.areaId = '';
          }
        } else if (type === 'getArea') {
          this.areaList = res.data || [];
          if (!local && !from) {
            this.ruleForm.areaId = '';
          }
        }
      });
    },
    handleCurrentChange(val) {
      this.util.clearLoacl('localQuery');
      this.listQuery.pageNum = val;
      this.getDataList();
    },
    handleSizeChange(sizi) {
      this.util.clearLoacl('localQuery');
      this.listQuery.pageSize = sizi;
      this.getDataList();
    },
    getDataList() {
      // 查询客户资质有变更的客户数量
      this.countCustomerQualificationChanged();
      let queryInfo = {};
      const localInfo = this.util.getLocal('localQuery');
      if (localInfo) {
        queryInfo = localInfo;
        this.time = [localInfo.startTime, localInfo.endTime];
        this.orderStartTime = [localInfo.firstOrderStartTime, localInfo.firstOrderEndTime];
        if (localInfo.provId) {
          this.getProvince('getCity', localInfo.provId, 1);
        }
        if (localInfo.cityId) {
          this.getProvince('getArea', localInfo.cityId, 1);
        }
        this.listQuery.pageNum = localInfo.pageNum;
        this.listQuery.pageSize = localInfo.pageSize;
        this.ruleForm = { ...queryInfo };
      } else {
        delete this.ruleForm.pageNum;
        delete this.ruleForm.pageSize;
        queryInfo = { ...this.listQuery, ...this.ruleForm };
      }
      getList(queryInfo)
        .then((res) => {
          if (res.code === 0) {
            const { list, total, pageNum, pageSize } = res.data;
            this.tableData = list || [];
            this.listQuery = {
              ...this.listQuery,
              total,
              pageNum,
              pageSize,
            };
          } else {
            this.tableData = [];
          }
        })
        .catch((err) => {
          this.$message.error(err);
        });
    },
    orderStartTimeChange(val) {
      if (val === null) {
        this.ruleForm.firstOrderStartTime = '';
        this.ruleForm.firstOrderEndTime = '';
        val = '';
        this.orderStartTime = '';
      } else if (typeof val[0] === 'string') {
        this.ruleForm.firstOrderStartTime = val[0];
        this.ruleForm.firstOrderEndTime = val[1];
      }
    },
    dateOnChange(val) {
      if (val === null) {
        this.ruleForm.startTime = '';
        this.ruleForm.endTime = '';
        val = '';
        this.time = '';
      } else if (typeof val[0] === 'string') {
        // this.ruleForm.startTime = new Date(val[0]).getTime();
        // this.ruleForm.endTime = new Date(val[1]).getTime();
        this.ruleForm.startTime = val[0];
        this.ruleForm.endTime = val[1];
      }
    },
    toSerachForm() {
      this.util.clearLoacl('localQuery');
      this.getDataList();
    },
    // 查询客户资质有变更的客户数量
    searchCustomerQualification() {
      this.util.clearLoacl('localQuery');
      if (this.countCustomerQualification > 0 && this.isCountCustomerQualification) {
        this.ruleForm.qualificationChanged = 1;
      } else {
        this.ruleForm.qualificationChanged = null;
      }
      this.isCountCustomerQualification = !this.isCountCustomerQualification;
      this.getDataList();
    },
    btnClick(row) {
      this.editRow = row || {};
      // 信息有变更
      this.isAllList = false;
      this.informationChangesLogVisible = true;
    },
    resetForm(str) {
      this.util.clearLoacl('localQuery');
      this.$refs.form.resetFields();
      this.isCountCustomerQualification = true;
      this.time = '';
      this.orderStartTime = '';
      this.cityList = [];
      this.areaList = [];
      this.ruleForm = {
        merchantId: '',
        businessTypeId: '',
        accountStatus: '',
        startTime: '',
        endTime: '',
        provId: '',
        cityId: '',
        areaId: '',
        sellerUserId: '',
        merchantName: '',
        mobile: '',
        qualificationChanged: null,
      };
      this.listQuery = {
        pageSize: 10,
        pageNum: 1,
        total: 0,
      };
      if (str !== 'clearParams') {
        this.getDataList();
        this.getProvince('getProv');
      }
    },
  },
};
</script>
<style lang="scss" scoped>
::v-deep  .el-range-editor.el-input__inner{
  width: 100%;
}
.flex{
  display: flex;
  .el-select{
    flex: 1;
  }
}
::v-deep  .el-form-item__label {
  padding: 0;
}
::v-deep .el-input--small .el-input__inner{
  height: 30px !important;
  line-height: 32px !important;
}
::v-deep  .pagination-container {
  margin: 15px 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.customerWrap {
  padding: 20px 24px;
  .customerTitle {
    font-size: 20px;
    font-weight: 500;
  }
}
.editText {
  color: #4183d5;
  div {
    cursor: pointer;
  }
}
.informationChanges {
  display: inline-block;
  font-size: 12px;
  padding: 0 5px 0 5px;
  text-align: center;
  background: #fff1f0;
  border: 1px solid #ffa39e;
  border-radius: 3px;
  color: #ff4d4f;
  cursor: pointer;
}
.div-info:hover {
  border: 1px solid #4183d5;
  color: #4183d5;
  background-color: #fff;
  .status-span i {
    background: #4183d5 !important;
  }
}
.div-info {
  display: inline-block;
  padding: 5px 10px 10px 10px;
  border-radius: 2px;
  font-size: 14px;
  font-family: PingFangSC, PingFangSC-Regular;
  font-weight: 400;
  border: 1px solid #dcdfe6;
  margin: 0 10px 10px 0;
  p {
    margin: 0;
    padding-top: 5px;
    font-size: 14px;
    font-family: PingFangSC, PingFangSC-Medium;
    font-weight: 500;
    color: #333333;
  }
  .status-span {
    padding-left: 14px;
    position: relative;
    i {
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
      left: 4px;
      display: inline-block;
      vertical-align: middle;
      width: 5px;
      height: 5px;
      border-radius: 50%;
    }
  }
  .refundCountBox {
    .refundCount {
      font-size: 20px;
      font-weight: 500;
      color: #ff3945;
      font-family: PingFangSC, PingFangSC-Medium;
    }
    .seeCount {
      float: right;
      color: #ffffff;
    }
  }
}
.Fsearch {
  padding: 0 0 10px;
  font-weight: 500;
  .searchMsg {
    font-weight: 700;
    width: 200px;
  }
  .sign {
    display: inline-table;
    width: 3px;
    height: 13px;
    background: linear-gradient(360deg, #1d69c4 1%, #8bbdfc 99%);
    border-radius: 2px;
    margin-right: 8px;
  }
}
.searchMy ::v-deep  .el-form-item__label {
  margin-left: 20px;
  padding: 0;
}
.searchMy ::v-deep  .el-input__inner {
  border-radius: 0 4px 4px 0;
}
.searchMy ::v-deep  .el-date-editor {
  width: 100%;
}
.search-title {
  display: table-cell;
  padding: 0 10px;
  text-align: center;
  border: 1px solid #dcdfe6;
  height: 30px;
  line-height: 30px;
  vertical-align: middle;
  border-right: none;
  border-radius: 4px 0 0 4px;
  color: #333333;
  white-space: nowrap;
}
.searchMy ::v-deep  .el-select {
  display: table-cell;
  width: 100%;
}
.searchMy ::v-deep  .el-form-item__content {
  width: 100%;
}
.searchMy ::v-deep  .el-form-item--small.el-form-item {
  margin-bottom: 10px;
  width: 32%;
}
.searchMy ::v-deep  .el-form-item--small .el-form-item__content {
  line-height: 30px;
  width: 100%;
}
.searchMy ::v-deep  .el-input-group__prepend {
  background: none;
  color: #333333;
  padding: 0 10px;
}
.Fsearch {
  // padding: 0px 20px 10px;
  font-weight: 500;
  .searchMsg {
    font-weight: 700;
    width: 200px;
  }
  .sign {
    display: inline-table;
    width: 3px;
    height: 13px;
    background: linear-gradient(360deg, #1d69c4 1%, #8bbdfc 99%);
    border-radius: 2px;
    margin-right: 8px;
  }
}
.searchMy ::v-deep  .el-form-item--small.el-form-item {
  margin-bottom: 10px;
  width: 97%;
}
::v-deep  .el-table {
  .el-checkbox__inner {
    border: 1px solid #000000;
  }
}
</style>
