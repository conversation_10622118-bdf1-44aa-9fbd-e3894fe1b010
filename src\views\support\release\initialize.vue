<template>
  <div
    class="divBox"
    v-infinite-scroll="loadMore"
    :infinite-scroll-disabled="
      goodsList.length >= listQuery.total ||
      goodsList.length >= 500 ||
      loading ||
      $route.path != '/support/initialize'
    "
    :infinite-scroll-distance="300"
    :infinite-scroll-immediate="false"
  >
    <div class="nameTitle">发布商品</div>
    <div class="contentBox">
      <div class="title">查询商品</div>
      <div class="topTip">注：您可以通过查询，从标准库中选择商品</div>
      <SearchForm
        ref="searchForm"
        :model="formModel"
        :form-items="formItems"
        :has-open-btn="false"
        @submit="handleFormSubmit"
        @reset="handleFormReset"
      ></SearchForm>
    </div>
    <div>
      <div v-if="checkedName" class="selectBox">
        您当前选择的是：
        <span>{{ checkedName }}</span>
      </div>
      <div>
        <div v-if="goodsList && goodsList.length">
          <ChooseList
            :prop-data="goodsList"
            :is-submit="isSubmit"
            @chooseGoods="chooseGoods"
            @getSubmit="submitState"
          ></ChooseList>
        </div>
        <div v-else class="noData">
          <p class="img-box">
            <!-- <img src="@/assets/image/marketing/noneImg.png" alt /> -->
          </p>
          <p v-if="isShow">没有找到匹配的商品</p>
        </div>
      </div>
    </div>
    <div class="page-box" v-if="false">
      <Pagination
        v-show="listQuery.total > 0"
        :total="listQuery.total"
        :page.sync="listQuery.pageNum"
        :limit.sync="listQuery.pageSize"
        @pagination="getList"
      />
    </div>

    <div class="bottomBtn">
      <div class="contentBox" v-if="listQuery.total > 0 || isShow">
        <div class="title">自建商品</div>
        <div class="topTip">
          注：未在标准库中找到商品？您可以选择“一级分类“自建商品
        </div>
        <div>
          <div style="display: flex">
            <el-tooltip effect="dark" placement="top">
              <template #content>
                1、本字段为系统分类，不同分类维护的属性和业务流程不同。
                <br />2、通过本字段区分药品、非药、中药和赠品并同步给其他系统。
                <br />3、药品和医疗器械不可作为赠品。
              </template>
              <i class="el-icon-warning-outline span-tip"></i>
            </el-tooltip>
            <span class="search-title">一级分类</span>
            <el-select
              v-model="selectLevelCategory"
              placeholder="Select"
              size="small"
              :disabled="checkedName || isDisable ? true : false"
              @change="handleSelectLevelCategory"
            >
              <el-option
                v-for="item in levelData"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              ></el-option>
            </el-select>
          </div>
        </div>
      </div>
      <div>
        <el-button
          type="primary"
          size="small"
          :disabled="selectLevelCategory || checkedName ? false : true"
          @click="goDetails"
          >现在发布商品</el-button
        >
      </div>
    </div>
  </div>
</template>

<script>
import {
  meProduceList,
  levelCategory,
  apiValidProtectSkuAndForbiddenName
} from '@/api/product'
import SearchForm from '@/components/searchForm'
import Pagination from '../../../components/Pagination/index.vue'
import ChooseList from '../components/chooseList'
import { searchItem } from '../config'

export default {
  name: 'initialize',
  components: { SearchForm, Pagination, ChooseList },
  data() {
    return {
      formModel: {
        code: '',
        productName: '',
        manufacturer: '',
        approvalNumber: ''
      },
      listQuery: {
        pageSize: 50,
        pageNum: 1,
        total: 0
      },
      selectLevelCategory: '',
      formItems: searchItem.formItemsIn,
      checkedName: '',
      goodsList: [],
      levelData: [],
      itemData: {},
      isSubmit: false,
      isDisable: false,
      isShow: false,
      loading: false
    }
  },
  created() {
    this.getLevelList()
  },
  activated() {
    this.activate()
    this.getLevelList()
  },
  methods: {
    activate() {
      const query = this.$route.query
      if (query && Object.keys(query).length > 0) {
        if (Object.prototype.hasOwnProperty.call(query, 'from')) {
          if (query.from === 'toInitialize') {
            Object.assign(this.$data, this.$options.data())
          }
        }
      }
    },
    getLevelList() {
      levelCategory().then((res) => {
        if (res.code === 0) {
          this.levelData = res.data ? res.data : []
          this.levelData.unshift({ id: '', name: '请选择' })
        } else {
          this.levelData = []
        }
      })
    },
    submitState() {
      this.isSubmit = false
    },
    handleFormSubmit() {
      this.checkedName = ''
      this.selectLevelCategory = ''
      this.itemData = {}
      this.isSubmit = true
      this.isDisable = false
      this.listQuery = {
        pageNum: 1,
        pageSize: 50,
        total: 0
      }
      this.goodsList = []
      this.getList()
    },
    handleFormReset(obj) {
      this.formModel = obj
    },
    loadingFun() {
      const loading = this.$loading({
        lock: true,
        text: 'Loading',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      return loading
    },
    getList() {
      const params = { ...this.formModel }
      const { pageSize, pageNum } = this.listQuery
      params.pageSize = pageSize
      params.pageNum = pageNum
      const load = this.loadingFun()
      this.loading = true
      meProduceList(params).then((res) => {
        load.close()
        this.loading = false
        if (res.code === 0) {
          //去重！！！！！！！！！！！！
          this.goodsList.push(...(res.data.list ? res.data.list : []))
          this.listQuery.total = res.data.total ? res.data.total : 0
          this.isShow = true
        } else {
          this.$message.error(res.message)
        }
      })
    },
    chooseGoods(data) {
      this.checkedName = data.showName
        ? (data.brand ? data.brand : '') +
          ' ' +
          data.showName +
          '/' +
          data.spec +
          '/' +
          data.manufacturer
        : ''
      this.selectLevelCategory = data.firstCategory ? data.firstCategory : ''
      this.itemData = data ? data : {}
      this.isDisable = false
      if (data.showName && data.specialVarieties) {
        this.checkedName = ''
        this.selectLevelCategory = ''
        this.isDisable = true
        this.$message.error('商品为国家管控的特殊品种，无法创建')
      }
      if (data.showName) {
        this.getValidForbiddenName(data.showName, data.standardProductId)
      }
    },
    getValidForbiddenName(showName, standardProductId) {
      const that = this
      const params = {
        productName: showName,
        standId: standardProductId
      }
      apiValidProtectSkuAndForbiddenName(params).then((res) => {
        if (res.code === 0) {
          const validForbiddenName = res.data ? true : false
          if (validForbiddenName) {
            that.checkedName = ''
            that.selectLevelCategory = ''
            that.isDisable = true
            that.$message.error(res.message)
          }
        } else {
          that.$message.error(res.message)
        }
      })
    },
    goDetails() {
      if (this.selectLevelCategory) {
        const path = '/support/detailsEdit'
        const obj = {
          standardProductId: this.itemData.standardProductId || '',
          erpFirstCategoryId: this.selectLevelCategory,
          isEdit: 2,
          from: 'initialize'
        }
        window.openTab(path, obj)
      } else {
        this.$message.error('查询参数异常')
      }
    },

    handleSelectLevelCategory(val) {
      if (val === 100006) {
        this.selectLevelCategory = ''
      }
    },

    // 加载更多
    loadMore() {
      this.listQuery.pageNum++
      this.getList()
    }
  }
}
</script>

<style scoped lang="less">
.divBox {
  padding-bottom: 52px;
  position: relative;
  .bottomBtn {
    text-align: center;
    line-height: 59px;
    width: calc(100%);
    height: 200px;
    background-color: #fff;
    // position: fixed;
    position: sticky;
    bottom: 0;
    z-index: 1;
    button {
      line-height: normal;
    }
    .contentBox {
      height: 140px;
      .title {
        margin-bottom: 18px;
      }
      .topTip {
        margin-bottom: 0px;
      }
    }
  }
  .nameTitle {
    padding: 15px 15px 0;
    font-size: 20px;
    font-weight: bold;
    text-align: left;
    color: #333333;
  }
  .selectBox {
    padding: 0 20px 20px;
    font-size: 13px;
    font-weight: 800;
    text-align: left;
    color: #333333;
    span {
      color: #ff9500;
    }
  }
}
.contentBox {
  //height: 100%;
  padding: 16px 16px;
  background: #fff;
  .search-title {
    display: table-cell;
    padding: 0 10px;
    text-align: center;
    border: 1px solid #dcdfe6;
    height: 32px;
    line-height: 32px;
    vertical-align: middle;
    border-right: none;
    border-radius: 4px 0 0 4px;
    color: #333333;
    white-space: nowrap;
    margin-top: 15px;
  }
  .span-tip {
    display: flex;
    justify-content: center;
    align-items: center;
  }
  ::v-deep  .el-select {
    display: table-cell;
    width: 165px;
  }
  ::v-deep  .el-input__inner {
    border-radius: 0 4px 4px 0;
  }
  .title {
    font-weight: 500;
    text-align: left;
    color: #000000;
    line-height: 14px;
    margin-bottom: 24px;
  }

  .title:before {
    content: '';
    display: inline-block;
    width: 3px;
    height: 13px;
    background: linear-gradient(360deg, #1d69c4 1%, #8bbdfc 99%);
    border-radius: 2px;
    margin-right: 8px;
    vertical-align: middle;
  }
  .topTip {
    height: 36px;
    background: rgba(255, 180, 0, 0.05);
    border-radius: 4px;
    margin-bottom: 15px;
    padding-left: 5px;
    font-size: 12px;
    font-family: PingFangSC, PingFangSC-Regular;
    font-weight: 400;
    text-align: left;
    color: #ff9500;
    line-height: 36px;
  }
  .searchForm {
    overflow: hidden;
  }

  .el-form-item {
    vertical-align: middle;
    margin-right: 16px;
    margin-bottom: 12px;
    border: 1px solid #dcdfe6;
    border-radius: 4px;

    ::v-deep   .el-form-item__label {
      font-size: 12px;
      width: 95px;
      height: 33px;
      line-height: 33px;
      border-right: 1px solid #dcdfe6;
    }

    ::v-deep   .el-form-item__content {
      width: 120px;

      .el-input__inner {
        border: none;
        font-size: 12px;
      }
    }
  }

  ::v-deep   .formItemTime .el-form-item__content {
    width: 354px;
  }

  .operation {
    margin-bottom: 12px;
  }

  .tips {
    opacity: 1;
    background: #fafafa;
    border-radius: 4px;
    padding: 8px 16px;
    margin-bottom: 10px;

    div {
      font-size: 14px;
      font-family: PingFangSC, PingFangSC-Regular;
      font-weight: 400;
      text-align: left;
      color: #333333;
      line-height: 24px;

      .el-button {
        padding: 0;
        line-height: 20px;
        border: none;
      }
    }
  }
}
.noData {
  width: 100%;
  height: 100%;
  min-height: 60px;
  text-align: center;
  display: flex;
  justify-content: center;
  align-items: center;
}
::v-deep   .el-input-group__prepend {
  background: none;
  color: #333333;
  padding: 0 10px;
}
</style>
