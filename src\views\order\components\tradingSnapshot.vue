<template>
  <div>
    <el-dialog title="交易快照" :visible="dialogVisible" width="50%" @close="closeDialog">
      <div class="con-box">
        <p>商品图片</p>
        <div class="img-box">
          <el-image
            :src="snapshotInfo.imageUrl"
            :preview-src-list="[snapshotInfo.imageUrl]"
            @click.prevent
          >
            <div slot="error" class="image-slot">
              <el-image src="https://oss-ec.ybm100.com/ybm/product/defaultPhoto.jpg" />
            </div>
          </el-image>
        </div>
        <p>详情图片</p>
        <div class="infoImgList">
          <img
            v-for="(items,indexs) in snapshotInfo.imagesList"
            :key="indexs"
            class="img"
            :src="(items || '')"
            @click="toShowBig(snapshotInfo.imagesList,indexs)"
          />
        </div>
        <p>商品名称：{{snapshotInfo.productName}}</p>
        <p>APP展示名称：{{snapshotInfo.showName}}</p>
        <p>处方类型：{{snapshotInfo.drugClassificationText}}</p>
        <p>规格：{{snapshotInfo.spec}}</p>
        <p>单位：{{snapshotInfo.productUnit}}</p>
        <p>中包装：{{snapshotInfo.mediumPackageNum}}({{ {0: '不可拆零', 1: '可拆零'}[snapshotInfo.isSplit] }})</p>
        <p>批准文号：{{snapshotInfo.approvalNumber}}</p>
        <p>生产厂家：{{snapshotInfo.manufacturer}}</p>
        <p v-if="snapshotInfo.nearEffectiveFlag">商品状态：{{ {0: '正常',1: '临期', 2: '近效期'}[snapshotInfo.nearEffectiveFlag] }}</p>
        <p>近远效期：{{snapshotInfo.nearEffect}}/{{snapshotInfo.farEffect}}</p>
        <p>有效期/保质期：{{snapshotInfo.shelfLife}}</p>
        <p>存储条件：{{snapshotInfo.storageCondition}}</p>
        <p v-if="snapshotInfo.tracingCode==1">追溯码：有</p>
      </div>
    </el-dialog>
    <el-image-viewer
      v-if="showViewer"
      :url-list="srcArr"
      :on-close="closeViewer"
      :on-switch="onSwitch"
    />
  </div>
</template>
<script>
import { invoiceInfo , apiConfig } from '@/api/customer-management/index';
import { apiGetOrderSnapshot } from '@/api/order/index';
import ElImageViewer from 'element-ui/packages/image/src/image-viewer';
export default {
  name: 'TradingSnapshot',
  components: { ElImageViewer },
  props: {
    dialogVisible: {
      type: Boolean,
      default: false,
    },
    skuId: {
      type: Number,
      default: null,
    },
    orderNo: {
      type: String,
      default: '',
    },
  },
  data() {
    return { 
      snapshotInfo: {},
      srcArr: [],
      showViewer: false,
      bigImgUrlPrefix:'',
      bigDescImgUrlPrefix:'',
    };
  },
  created() {
    apiConfig().then((res) => {
      if (res.data) {
        this.bigImgUrlPrefix = res.data.bigImgUrlPrefix // 商品大图地址前缀
        this.bigDescImgUrlPrefix = res.data.bigDescImgUrlPrefix // 详情大图地址前缀
      }
    });
  },
  mounted() {
    this.getInfo();
  },
  methods: {
    closeDialog() {
      this.$emit('cancelDialog');
    },
    getInfo() {
      const that = this;
      const params = {
        skuId:this.skuId,
        orderNo:this.orderNo
      }
      apiGetOrderSnapshot(params).then((res) => {
        if (res.code === 0) {
          // this.snapshotInfo = res.result || {};
          let snapshotInfo = res.result || {};
          if(snapshotInfo.imageUrl){
            snapshotInfo.imageUrl = `${that.bigImgUrlPrefix}${snapshotInfo.imageUrl}`
          }
          if(snapshotInfo.imagesList){
            snapshotInfo.imagesList = snapshotInfo.imagesList.map((item) => {
              return `${that.bigImgUrlPrefix}${item}`
            });
          }
          that.snapshotInfo = snapshotInfo;
        }else{
          that.$message.error(res.msg)
        }
      });
    },
    toShowBig(imgList, indexs) {
      if ((imgList || []).length > 0) {
        this.showViewer = true;
        this.srcArr = imgList || [];
        this.srcArr = this.getPrivewImages(indexs);
      } else {
        this.$message.warning('当前资质无相应图片');
      }
    },
    getPrivewImages(index) {
      const tempImgList = [...this.srcArr];// 所有图片地址
      if (index == 0) return tempImgList;
      // 调整图片顺序，把当前图片放在第一位
      const start = tempImgList.splice(index);
      const remain = tempImgList.splice(0, index);
      return start.concat(remain);// 将当前图片调整成点击缩略图的那张图片
    },
    closeViewer() {
      this.showViewer = false;
      this.actImgIndex = 1;
    },
    onSwitch(index) {
      this.actImgIndex = index + 1;
    },
  },
};
</script>
<style lang="scss" scoped>
.infoImgList {
  img {
    margin-right: 10px;
  }
}
.img-box {
  height: 100px;
  width: 100px;
  ::v-deep   .el-image {
    max-height: 100px;
  }
}
.img {
  width: 100px;
  height: 100px;
}
.con-box {
  padding: 10px 10px 15px;
  h4,
  p {
    margin: 0;
    padding: 0;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  p {
    padding-top: 5px;
    font-size: 12px;
    color: #666666;
  }
}
::v-deep  .el-image-viewer__wrapper{
  z-index: 9999 !important;
}
</style>
