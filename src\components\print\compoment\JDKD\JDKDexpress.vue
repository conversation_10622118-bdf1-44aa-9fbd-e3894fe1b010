<template>
  <div
    style="width: 94mm;margin: 0 auto;border: 1px solid #000;font-family:黑体;box-sizing: border-box;font-size: 12px;font-weight: bold;overflow: hidden"
  >
    <div
      style="height: 13mm;border-bottom: 1px solid #000;text-align: left;background-color: #fff;overflow: hidden;position: relative;"
    >
      <div style="width:80%;height:12.5mm;margin-top:0.25mm;overflow: hidden;border-right: none">
        <!--        <div><img src="https://files.test.ybm100.com/G1/M00/1E/E2/Cgoz02HC3FqARb4uAAAHmUddVbQ352.png" alt=""></div>-->
      </div>
      <div
        v-if="config.proName"
        style="width: 18mm;height: 5mm;line-height: 5mm;border: 1px solid #000;font-size: 12px;text-align: center;position: absolute;top: 4mm;right: 1mm"
      >{{ config.proName }}
      </div>
    </div>
    <div
      style="height: 13mm;border-bottom: 1px solid #000;text-align: left;background-color: #fff;overflow: hidden;position: relative;"
    >
      <div
        style="width: 15%;height: 100%;border-right: 1px solid #000;overflow: hidden;text-align: center;line-height: 13mm"
      >始发地
      </div>
      <div style="width: 35%;height: 100%;border-right: 1px solid #000;overflow: hidden;position: absolute;top: 0;left: 15%">
        <div style="width: 100%;height: 50%;padding-left: 5px;line-height: 6.5mm">
          {{ jDKDFace.originalDmsName }}
        </div>
        <div style="width: 100%;border-bottom: 1px solid #000"></div>
        <div style="width: 100%;height: 50%;padding-left: 5px;line-height: 6.5mm">
          {{ jDKDFace.originalCrossCode }}-{{ jDKDFace.originalTabletrolleyCode }}
        </div>
      </div>
      <div
        style="width: 15%;height: 100%;border-right: 1px solid #000;overflow: hidden;position: absolute;top: 0;left: 50%;text-align: center;line-height: 13mm"
      >目的地
      </div>
      <div style="width: 35%;height: 100%;overflow: hidden;position: absolute;top: 0;left: 65%">
        <div style="width: 100%;height: 50%;padding-left: 5px;line-height: 6.5mm">
          {{ jDKDFace.destinationDmsName }}
        </div>
        <div style="width: 100%;border-bottom: 1px solid #000"></div>
        <div style="width: 100%;height: 50%;padding-left: 5px;line-height: 6.5mm">
          {{ jDKDFace.destinationCrossCode }}-{{ jDKDFace.destinationTabletrolleyCode }}
        </div>
      </div>
    </div>
    <div class="page-row three"
         style="height: 7mm;border-bottom: 1px solid #000;text-align: left;background-color: #fff;overflow: hidden;position: relative;"
    >
      <div class="cell_1"
           style="width: 15%;height: 100%;border-right: 1px solid #000;overflow: hidden"
      ></div>
      <div class="cell_5"
           style="width: 50%;height: 100%;border-right: 1px solid #000;position: absolute;top: 0;left: 15%;text-align: center;line-height: 7mm"
      >{{ jDKDFace.printSiteName }}
      </div>
      <div class="cell_4"></div>
    </div>
    <div class="page-row four"
         style="height: 18mm;border-bottom: 1px solid #000;text-align: left;background-color: #fff;overflow: hidden;position: relative;"
    >
      <div class="cell_1"
           style="width: 15%;height: 100%;border-right: 1px solid #000;overflow: hidden;text-align: center;line-height: 18mm"
      >收件信息
      </div>
      <div style="width: 85%;height: 100%;overflow: hidden;position: absolute;left: 15%;top: 0">
        {{ config.contactor }} {{ config.merchantName }} {{config.merchantErpCode}}<br>
        {{ config.mobile }}<br>
        {{ config.takeAeliveryAddress }}
      </div>
    </div>
    <div class="page-row five"
         style="height: 5mm;border-bottom: 1px solid #000;text-align: left;background-color: #fff;overflow: hidden;position: relative;"
    >
      <div class="cell_12" style="width: 100%;height: 100%;text-align: center;line-height: 5mm">
        <span>第{{ config.printCount }}次打印</span><span style="padding-left: 10px"
      > 打印时间：{{ formatDate(config.printTime) }} </span></div>
    </div>
    <div class="page-row five"
         style="width:100%;height: 8mm;border-bottom: 1px solid #000;text-align: left;background-color: #fff;overflow: hidden;position: relative;"
    >
      <div style="width: 25%;height: 8mm;">
        <!--        <div><img src="https://oss-ec.ybm100.com/ybm/popDeliverLog/jingdong_logo.png" alt=""></div>-->
      </div>
      <div class="cell_12" style="width: 100%;height: 100%;position: absolute;top: 0;left: 40%;line-height: 8mm;">
        <span style="font-size: 14px;font-weight: bold">运单号：{{ config.waybillNo }}</span></div>
    </div>
    <div class="page-row six"
         style="height: 15mm;border-bottom: 1px solid #000;text-align: left;background-color: #fff;overflow: hidden;position: relative;"
    >
      <div class="cell_5"
           style="width:50%;height:100%;margin-top:0.25mm;overflow: hidden;border-right: 1px solid #000;"
      >
        <!--        <div><img src="https://files.test.ybm100.com/G1/M00/1E/E2/Cgoz02HC3FqARb4uAAAHmUddVbQ352.png" alt=""></div>-->
      </div>
      <div class="cell_5" style="width: 50%;height: 100%;position: absolute;left: 50%;top: 0">
        <div style="width: 100%;height: 100%;overflow: hidden;word-break: break-all"><span style="padding: 5px;"
        >备注：{{ config.remark }}</span></div>
      </div>
    </div>
    <div class="page-row seven"
         style="height: 5mm;border-bottom: 1px solid #000;text-align: left;background-color: #fff;overflow: hidden;position: relative;"
    >
      <div class="cell_25"
           style="width: 25%;height: 100%;border-right: 1px solid #000;"
      ></div>
      <div class="cell_25"
           style="width: 25%;height: 100%;border-right: 1px solid #000;position: absolute;left: 25%;top: 0"
      ></div>
      <div class="cell_25"
           style="width: 25%;height: 100%;border-right: 1px solid #000;position: absolute;left: 50%;top: 0"
      ></div>
      <div class="cell_25" style="width: 25%;height: 100%;"
      ></div>
    </div>
    <div class="page-row eight"
         style="height: 20mm;overflow: hidden;text-align: left;background-color: #fff;position: relative;"
    >
      <div class="cell_5"
           style="width:50%;height:100%;margin-top:0.25mm;overflow: hidden;border-right: 1px solid #000;"
      >
        <div style="height: 100%;overflow: hidden;">寄方信息：
          {{ config.consignor }}
          {{ config.deliveryMobile }}
          {{ config.mailRegionName }}
          {{ config.mailAddress }}
        </div>
      </div>
      <div class="cell_4" style="width: 50%;height: 100%;position: absolute;top: 0;left: 50%">
        <div
          style="width: 100%;height: 50%;box-sizing:border-box;padding:5px;word-break: break-all;"
        >
          订单号：{{ config.orderNo }}
        </div>
        <div style="width: 100%;border-bottom: 1px solid #000"></div>
        <div style="width: 100%;height: 50%;padding-left: 5px;padding-top: 3mm">
          始发城市：{{ jDKDFace.sendCity }}
        </div>
      </div>
      <div style="position: absolute;bottom: 0;right: 0;color: red">www.jd-ex.com</div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'JDexpress',
  props: {
    config: {
      type: Object,
      default: () => {
        return {};
      }
    }
  },
  data() {
    return {
      jDKDFace: {}
    };
  },
  created() {
    console.log(this.config);
    this.jDKDFace = { ...this.config.jDKDFace || {} };
  }
};
</script>

<style scoped>
@page {
  size: 100mm 113mm;
  margin: 0;
  padding: 0;
}
</style>
