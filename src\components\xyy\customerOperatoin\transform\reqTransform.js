import { merchantJoinTypes } from '../constant';
/**
 * created by <PERSON><PERSON><PERSON><PERSON><PERSON> in 2021/3/19
 */

function getCrowdDetail(data) {
  const realData = JSON.parse(JSON.stringify(data));
  return realData;
}

function getCrowdList(data) {
  const realData = JSON.parse(JSON.stringify(data));
  delete realData.groupName;
  realData.tagName = data.groupName;
  return realData;
}

function getProvincesAndMerchantTypes() {

}

function getProvinceRegions(data) {
  return data;
}
function uploadCrowdCustomer(data) {
  return data;
}

// 创建人群
function createCrowd(data) {
  const realData = {
    tagName: data.groupName,
    merchantTag: data.merchantTag,
    merchantTypes: data.merchantTypes,
    merchantJoinType: data.merchantJoinType,
    areaCodes: [],
    isNewMan: data.isNewMan,
    tagIds:data.tagIds
  };
  if(data.tagIds.length <= 0){
    delete realData.tagIds
  }
  if (data.merchantJoinType !== merchantJoinTypes.exclude && data.merchantJoinType !== merchantJoinTypes.include) {
    delete realData.merchantJoinType;
  }
  if (data.conditionAreas && data.conditionAreas.length > 0) {
    data.conditionAreas.forEach((item) => {
      realData.areaCodes.push({
        areaCode: item.areaCode,
        level: item.areaLevel,
      });
    });
  }
  if(data.merchantJoinType === 1 && data.tagIds.length <= 0 && data.conditionAreas.length <= 0 && data.merchantTypes.length <= 0){
    delete realData.isNewMan
  }
  return realData;
}

function getImportMerchantList(data) {
  const realData = JSON.parse(JSON.stringify(data));
  return realData;
}

function getMerchantList(data) {
  const realData = JSON.parse(JSON.stringify(data));
  return realData;
}


function deleteImportMerchantList(data) {
  return data;
}

export default {
  getCrowdDetail,
  getCrowdList,
  getProvincesAndMerchantTypes,
  getProvinceRegions,
  uploadCrowdCustomer,
  getImportMerchantList,
  getMerchantList,
  createCrowd,
  deleteImportMerchantList,
};
