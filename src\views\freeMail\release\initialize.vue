<template>
  <div
    class="divBox"
    v-infinite-scroll="loadMore"
    :infinite-scroll-disabled="
      goodsList.length >= listQuery.total ||
      goodsList.length >= 500 ||
      loading ||
      $route.path != '/product/initialize'
    "
    :infinite-scroll-distance="300"
    :infinite-scroll-immediate="false"
  >
    <div class="nameTitle">发布商品</div>
    <div class="contentBox">
      <div class="title">查询商品</div>
      <div class="topTip">注：您可以通过查询，从标准库中选择商品</div>
      <SearchForm
        ref="searchForm"
        :model="formModel"
        :form-items="formItems"
        :has-open-btn="false"
        @submit="handleFormSubmit"
        @reset="handleFormReset"
      ></SearchForm>
    </div>
    <div>
      <div v-if="checkedName" class="selectBox">
        您当前选择的是：
        <span>{{ checkedName }}</span>
      </div>
      <div>
        <div v-if="goodsList && goodsList.length">
          <ChooseList
            :prop-data="goodsList"
            :is-submit="isSubmit"
            @chooseGoods="chooseGoods"
            @getSubmit="submitState"
          ></ChooseList>
        </div>
        <div v-else class="noData">
          <p class="img-box">
            <!-- <img src="@/assets/image/marketing/noneImg.png" alt /> -->
          </p>
          <p v-if="isShow">没有找到匹配的商品</p>
        </div>
      </div>
    </div>
    <div class="page-box" v-if="false">
      <Pagination
        v-show="listQuery.total > 0"
        :total="listQuery.total"
        :page.sync="listQuery.pageNum"
        :limit.sync="listQuery.pageSize"
        @pagination="getList"
      />
    </div>

    <div class="bottomBtn">
      <div class="contentBox">
        <div class="title">自建商品</div>
        <div class="topTip">
          注：未在标准库中找到商品？您可以选择“商品大类”自建商品
        </div>
        <div>
          <div style="display: flex">
            <el-tooltip effect="dark" placement="top">
              <template #content>
                1、本字段为系统分类，不同分类维护的属性和业务流程不同。
                <br />2、通过本字段区分药品、非药、中药和赠品并同步给其他系统。
                <br />3、药品和医疗器械不可作为赠品。
              </template>
              <i class="el-icon-warning-outline span-tip"></i>
            </el-tooltip>
            <span class="search-title">商品大类</span>
            <el-select
              v-model="spuCategoryCode"
              placeholder="请选择商品大类"
              size="small"
              :disabled="checkedName || isDisable ? true : false"
              clearable
              @change="handleSpuCategoryCode"
            >
              <el-option label="普通药品" :value="1"/>
              <el-option label="中药" :value="2"/>
              <el-option label="医疗器械" :value="3"/>
              <el-option label="非药" :value="4"/>
            </el-select>
            <span v-if="spuCategoryCode == 4" class="search-title" style="margin-left: 15px;">非药细项</span>
            <el-select
              v-if="spuCategoryCode == 4"
              v-model="nonDrugCategoryCode"
              placeholder="请选择非药细项"
              size="small"
              :disabled="(checkedName || isDisable) ? true : false"
              clearable
            >
            <el-option label="个人护理品" :value="100001"/>
              <el-option label="日用百货" :value="100007"/>
              <el-option label="普通食品" :value="100008"/>
              <el-option label="保健食品" :value="100003"/>
            </el-select>
          </div>
        </div>
      </div>
      <div>
        <el-button
          type="primary"
          size="small"
          :disabled="spuCategoryCode || checkedName ? false : true"
          @click="goDetails"
          >现在发布商品</el-button
        >
      </div>
    </div>
  </div>
</template>

<script>
import {
  meProduceList,
  levelCategory,
  apiValidProtectSkuAndForbiddenName
} from '@/api/product'
import SearchForm from '@/components/searchForm'
import Pagination from '../../../components/Pagination/index.vue'
import ChooseList from '../components/chooseList'
import { searchItem, erpFirstCategoryId, Deduplication } from '../config'

export default {
  name: 'initialize',
  components: { SearchForm, Pagination, ChooseList },
  data() {
    return {
      isInit: false,
      formModel: {
        code: '',
        productName: '',
        manufacturer: '',
        approvalNumber: '',
        spuCategory: '', // 商品大类
        spec: '', //规格
        brand: '', //品牌
        originPlace: '', //场地
      },
      listQuery: {
        pageSize: 50,
        pageNum: 1,
        total: 0
      },
      spuCategoryCode: '',
      spuCategoryCodeName: '',
      nonDrugCategoryCode: '',
      formItems: searchItem.formItemsIn,
      checkedName: '',
      goodsList: [],
      levelData: [],
      itemData: {},
      isSubmit: false,
      isDisable: false,
      isShow: false,
      loading: false
    }
  },
  created() {
    this.activate()
    this.getLevelList()
    sessionStorage.removeItem('pipeiProduct')
  },
  activated() {
    this.activate()
    this.getLevelList()
  },
  methods: {
    activate() {
      const query = this.$route.query
      if (query && Object.keys(query).length > 0) {
        if (Object.prototype.hasOwnProperty.call(query, 'from')) {
          if (query.from === 'toInitialize') {
            Object.assign(this.$data, this.$options.data())
          }
        }
      }
    },
    getLevelList() {
      levelCategory().then((res) => {
        if (res.code === 0) {
          this.levelData = res.data ? res.data : []
          this.levelData.unshift({ id: '', name: '请选择' })
        } else {
          this.levelData = []
        }
      })
    },
    submitState() {
      this.isSubmit = false
    },
    handleFormSubmit() {
      this.checkedName = ''
      this.spuCategoryCode = ''
      this.itemData = {}
      this.isSubmit = true
      this.isDisable = false
      this.listQuery = {
        pageNum: 1,
        pageSize: 50,
        total: 0
      }
      this.goodsList = []
      this.getList()
    },
    handleFormReset(obj) {
      this.formModel = obj
    },
    loadingFun() {
      const loading = this.$loading({
        lock: true,
        text: 'Loading',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      return loading
    },
    getList() {
      const params = { ...this.formModel }
      const { pageSize, pageNum } = this.listQuery
      params.pageSize = pageSize
      params.pageNum = pageNum
      const load = this.loadingFun()
      this.loading = true
      meProduceList(params).then((res) => {
        load.close()
        this.loading = false
        if (res.code === 0) {
          //去重！！！！！！！！！！！！！！！
          this.goodsList.push(...(res.data.list ? res.data.list : []))
          console.log(this.goodsList);
          this.goodsList = Deduplication(this.goodsList);
          console.log(this.goodsList);
          this.listQuery.total = res.data.total ? res.data.total : 0
          this.isShow = true
        } else {
          this.$message.error(res.message)
        }
      })
    },
    chooseGoods(data) {
      this.isInit = data.isInit
      this.checkedName = data.showName
        ? (data.brand ? data.brand : '') +
          ' ' +
          data.showName +
          '/' +
          data.spec +
          '/' +
          data.manufacturer
        : ''
        console.log(data);
      this.spuCategoryCode = data.spuCategory ? data.spuCategory : '';
      this.nonDrugCategoryCode = data.firstCategory ? data.firstCategory : '';
      this.selectLevelCategoryName = data.spuCategoryCodeName
        ? data.firstCategoryName
        : ''
      this.itemData = data ? data : {}
      this.isDisable = false
      if (data.showName && data.specialVarieties) {
        this.checkedName = ''
        this.spuCategoryCode = ''
        this.selectLevelCategoryName = ''
        this.isDisable = true
        this.$message.error('商品为国家管控的特殊品种，无法创建')
      }
      if (data.showName) {
        this.getValidForbiddenName(data.showName, data.standardProductId)
      }
    },
    getValidForbiddenName(showName, standardProductId) {
      const that = this
      const params = {
        productName: showName,
        standId: standardProductId
      }
      apiValidProtectSkuAndForbiddenName(params).then((res) => {
        if (res.code === 0) {
          const validForbiddenName = res.data ? true : false
          if (validForbiddenName) {
            that.checkedName = ''
            that.spuCategoryCode = ''
            that.selectLevelCategoryName = ''
            that.isDisable = true
            that.$message.error(res.message)
          }
        } else {
          that.$message.error(res.message)
        }
      })
    },
    goDetails() {
      if (this.spuCategoryCode == 4 && !this.nonDrugCategoryCode) {
        this.$message.error('请选择非药细项')
        return
      }
      if (this.spuCategoryCode) {
        const path = '/product/detailsEdit'
        const key = this.spuCategoryCode === 4 ? `${this.spuCategoryCode}${this.nonDrugCategoryCode}` : `${this.spuCategoryCode}`
        let id = erpFirstCategoryId[key];
        let obj = {
          standardProductId: this.itemData.standardProductId || '',
          spuCategoryCode: this.spuCategoryCode,
          nonDrugCategoryCode: this.nonDrugCategoryCode,
          erpFirstCategoryId: id,
          isEdit: 2,
          from: Object.keys(this.itemData).length > 0 ? 'initialize' : 'create',
          prop: true,
          firstCategoryName: this.selectLevelCategoryName,
          isInit: this.isInit
        }
        const errorkeys = Object.keys(obj).filter(key => {
          return obj[key] === '' || obj[key] === undefined;
        })
        errorkeys.forEach(key => {
          delete obj[key]
        })
        // window.openTab(path, obj)
        // window.closeTab(this.$route.fullPath, true);
        sessionStorage.removeItem('createProduct')
        sessionStorage.setItem('createProduct', 'true')
        window.closeTab('/putProduct', true);
				setTimeout(() => {
					console.log('obj:', obj)
					window.openTab(path, obj)
				}, 0)
        // this.$router.go(0)
      } else {
        this.$message.error('请选择商品大类')
      }
    },

    handleSpuCategoryCode(val) {
      console.log(val)
      /* for (let index = 0; index < this.levelData.length; index++) {
        const element = this.levelData[index]
        if (element.id === val) {
          this.selectLevelCategoryName = element.name
        }
      }
      if (val === 100006) {
        this.spuCategoryCode = ''
        this.selectLevelCategoryName = ''
      } */
    },

    // 加载更多
    loadMore() {
      this.listQuery.pageNum++
      this.getList()
    }
  }
}
</script>

<style scoped lang="less">
.divBox {
  padding-bottom: 52px;
  position: relative;
  .bottomBtn {
    text-align: center;
    line-height: 59px;
    width: calc(100%);
    height: 200px;
    background-color: #fff;
    // position: fixed;
    position: sticky;
    bottom: 0;
    z-index: 1;
    button {
      line-height: normal;
    }
    .contentBox {
      height: 140px;
      .title {
        margin-bottom: 18px;
      }
      .topTip {
        margin-bottom: 0px;
      }
    }
  }
  .nameTitle {
    padding: 15px 15px 0;
    font-size: 20px;
    font-weight: bold;
    text-align: left;
    color: #333333;
  }
  .selectBox {
    padding: 0 20px 20px;
    font-size: 13px;
    font-weight: 800;
    text-align: left;
    color: #333333;
    span {
      color: #4183d5;
    }
  }
}
.contentBox {
  //height: 100%;
  padding: 16px 16px;
  background: #fff;
  .search-title {
    display: table-cell;
    padding: 0 10px;
    text-align: center;
    border: 1px solid #dcdfe6;
    height: 32px;
    line-height: 32px;
    vertical-align: middle;
    border-right: none;
    border-radius: 4px 0 0 4px;
    color: #333333;
    white-space: nowrap;
    margin-top: 15px;
  }
  .span-tip {
    display: flex;
    justify-content: center;
    align-items: center;
  }
  ::v-deep  .el-select {
    display: table-cell;
    width: 165px;
  }
  ::v-deep  .el-input__inner {
    border-radius: 0 4px 4px 0;
  }
  .title {
    font-weight: 500;
    text-align: left;
    color: #000000;
    line-height: 14px;
    margin-bottom: 24px;
  }

  .title:before {
    content: '';
    display: inline-block;
    width: 3px;
    height: 13px;
    background: linear-gradient(360deg, #1d69c4 1%, #8bbdfc 99%);
    border-radius: 2px;
    margin-right: 8px;
    vertical-align: middle;
  }
  .topTip {
    height: 36px;
    background: rgba(255, 180, 0, 0.05);
    border-radius: 4px;
    margin-bottom: 15px;
    padding-left: 5px;
    font-size: 12px;
    font-family: PingFangSC, PingFangSC-Regular;
    font-weight: 400;
    text-align: left;
    color: #ff9500;
    line-height: 36px;
  }
  .searchForm {
    overflow: hidden;
  }

  .el-form-item {
    vertical-align: middle;
    margin-right: 16px;
    margin-bottom: 12px;
    border: 1px solid #dcdfe6;
    border-radius: 4px;

    ::v-deep   .el-form-item__label {
      font-size: 12px;
      width: 95px;
      height: 33px;
      line-height: 33px;
      border-right: 1px solid #dcdfe6;
    }

    ::v-deep   .el-form-item__content {
      width: 120px;

      .el-input__inner {
        border: none;
        font-size: 12px;
      }
    }
  }

  ::v-deep   .formItemTime .el-form-item__content {
    width: 354px;
  }

  .operation {
    margin-bottom: 12px;
  }

  .tips {
    opacity: 1;
    background: #fafafa;
    border-radius: 4px;
    padding: 8px 16px;
    margin-bottom: 10px;

    div {
      font-size: 14px;
      font-family: PingFangSC, PingFangSC-Regular;
      font-weight: 400;
      text-align: left;
      color: #333333;
      line-height: 24px;

      .el-button {
        padding: 0;
        line-height: 20px;
        border: none;
      }
    }
  }
}
.noData {
  width: 100%;
  height: 100%;
  min-height: 60px;
  text-align: center;
  display: flex;
  justify-content: center;
  align-items: center;
}
::v-deep   .el-input-group__prepend {
  background: none;
  color: #333333;
  padding: 0 10px;
}
</style>
