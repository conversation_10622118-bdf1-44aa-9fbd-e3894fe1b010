<template>
  <div class="boxDiv">
    <el-row>
      <el-form
        :model="otherInformationVo"
        :rules="otherInformationVoRules"
        ref="otherInformationVo"
        label-width="130px"
        class="demo-ruleForm"
      >
        <el-row>
          <el-col :span="24">
            <el-form-item label="副标题:" prop="subtitle">
              <el-input
                v-model="otherInformationVo.subtitle"
                placeholder="请输入"
                type="textarea"
                :autosize="{ minRows: 2, maxRows: 4}"
                :disabled="disabled"
                maxlength="100"
                show-word-limit
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-if="erpFirstCategoryId !== '100004' && erpFirstCategoryId !== '100010' && erpFirstCategoryId !== '262683'">
          <el-col :span="24">
            <el-form-item prop="component" :rules="rulesConfig">
              <template slot="label">
                <el-tooltip v-if="otherInformationVo.isInstrument" effect="dark" placement="top">
                  <template #content>请按照医疗器械注册证或备案凭证或包装上载明的对应信息录入</template>
                  <i class="el-icon-warning-outline"></i>
                </el-tooltip>
                <span>{{otherInformationVo.isInstrument ? '结构及组成' : '成分'}}:</span>
              </template>
              <el-input
                v-model="otherInformationVo.component"
                placeholder="请输入"
                type="textarea"
                :autosize="{ minRows: 2, maxRows: 4}"
                :disabled="disabled"
                maxlength="200"
                show-word-limit
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24">
            <el-form-item :label="otherInformationVo.isInstrument ? '适用范围:' : '适应症/功能主治:'" prop="indication">
              <el-input
                v-model="otherInformationVo.indication"
                placeholder="请输入"
                type="textarea"
                :autosize="{ minRows: 2, maxRows: 4}"
                :disabled="disabled"
                maxlength="200"
                show-word-limit
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-if="!otherInformationVo.isInstrument">
          <el-col :span="24">
            <el-form-item label="用法与用量:" prop="usageAndDosage">
              <el-input
                v-model="otherInformationVo.usageAndDosage"
                placeholder="请输入"
                type="textarea"
                :autosize="{ minRows: 2, maxRows: 4}"
                :disabled="disabled"
                maxlength="200"
                show-word-limit
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-if="!otherInformationVo.isInstrument">
          <el-col :span="24">
            <el-form-item label="注意事项:" prop="considerations">
              <el-input
                v-model="otherInformationVo.considerations"
                placeholder="请输入"
                type="textarea"
                :autosize="{ minRows: 2, maxRows: 4}"
                :disabled="disabled"
                maxlength="200"
                show-word-limit
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-if="erpFirstCategoryId !== '100004' && erpFirstCategoryId !== '100010' && erpFirstCategoryId !== '262683'">
          <el-col :span="24">
            <el-form-item :label="otherInformationVo.isInstrument ? '禁忌症:' : '禁忌:'" prop="abstain">
              <el-input
                v-model="otherInformationVo.abstain"
                placeholder="请输入"
                type="textarea"
                :autosize="{ minRows: 2, maxRows: 4}"
                :disabled="disabled"
                maxlength="200"
                show-word-limit
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-if="!otherInformationVo.isInstrument && (erpFirstCategoryId !== '100004' && erpFirstCategoryId !== '100010' && erpFirstCategoryId !== '262683')">
          <el-col :span="24">
            <el-form-item label="不良反应:" prop="untowardEffect">
              <el-input
                v-model="otherInformationVo.untowardEffect"
                placeholder="请输入"
                type="textarea"
                :autosize="{ minRows: 2, maxRows: 4}"
                :disabled="disabled"
                maxlength="200"
                show-word-limit
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-if="!otherInformationVo.isInstrument && (erpFirstCategoryId !== '100004' && erpFirstCategoryId !== '100010' && erpFirstCategoryId !== '262683')">
          <el-col :span="24">
            <el-form-item label="药物相互作用:" prop="interaction">
              <el-input
                v-model="otherInformationVo.interaction"
                placeholder="请输入"
                type="textarea"
                :autosize="{ minRows: 2, maxRows: 4}"
                :disabled="disabled"
                maxlength="200"
                show-word-limit
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="备注:" prop="bz">
              <el-input
                v-model="otherInformationVo.bz"
                placeholder="请输入"
                type="textarea"
                :autosize="{ minRows: 2, maxRows: 4}"
                :disabled="disabled"
                maxlength="200"
                show-word-limit
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-row>
  </div>
</template>

<script>
export default {
  name: 'OtherInformation',
  props: {
    basicData: {
      type: Object,
      default() {
        return {};
      },
    },
    formModel: {
      type: Object,
      default() {
        return {};
      },
    },
  },
  data() {
    return {
      erpFirstCategoryId: '', // 一级分类
      disabled: false,
      provinceList: [],
      propsData: {},
      otherInformationVoRules: {},
      otherInformationVo: {
        isInstrument: false,
        subtitle: '', // 副标题
        component: '', // 成分
        indication: '', // 适应症/功能主治
        usageAndDosage: '', // 用法与用量
        considerations: '', // 注意事项
        abstain: '', // 禁忌
        untowardEffect: '', // 不良反应
        interaction: '', // 药物相互作用
        bz: '', // 备注
      },
      businessEndTimeDisabled: false,
    };
  },
  computed: {
    rulesConfig() {
      if (this.otherInformationVo.isInstrument && !this.otherInformationVo.componentCanEmpty) {
        return [{required: true, message: '结构及组成必填', trigger: 'blur'}]; 
      }
    }
  },
  watch: {
    basicData: {
      immediate: true,
      handler(newVale) {
        this.$nextTick(() => {
          this.propsData = JSON.parse(JSON.stringify(newVale));
          this.initData();
        });
      },
    },
    formModel: {
      handler(newVal) {
        var base = JSON.parse(JSON.stringify(newVal));
        this.disabled = base.isEdit == 1;
      },
      immediate: true,
      deep: true,
    },
  },
  mounted() {

  },
  methods: {
    initData() {
      const that = this;
      this.otherInformationVo.subtitle = this.propsData.subtitle;
      this.otherInformationVo.component = this.propsData.component;
      this.otherInformationVo.indication = this.propsData.indication;
      this.otherInformationVo.usageAndDosage = this.propsData.usageAndDosage;
      this.otherInformationVo.considerations = this.propsData.considerations;
      this.otherInformationVo.abstain = this.propsData.abstain;
      this.otherInformationVo.untowardEffect = this.propsData.untowardEffect;
      this.otherInformationVo.interaction = this.propsData.interaction;
      this.otherInformationVo.bz = this.propsData.bz;
      this.otherInformationVo.isInstrument = this.propsData.isInstrument;
      this.otherInformationVo.componentCanEmpty = this.propsData.componentCanEmpty; // 结构及组成是否可以为空,true可以为空
      this.erpFirstCategoryId = this.propsData.erpFirstCategoryId;
    },
  }
};
</script>

<style scoped lang="scss">
.el-button {
  padding: 8px 20px;
}
.el-button.is-circle {
  padding: 7px;
  border: none;
}
.boxDiv {
  width: 100%;
  height: 100%;
  overflow-y: auto;
  padding: 0 0 0 10px;

  ::v-deep   .el-form {
    width: 100%;

    .el-select {
      margin-right: 14px;
    }

    .el-form-item__label {
      font-size: 12px;
      line-height: 30px;
    }

    .el-form-item__content {
      line-height: 30px;
    }

    .el-input__inner {
      line-height: 30px;
      height: 30px;
      font-size: 12px;
    }

    .el-input__icon {
      line-height: 30px;
    }
  }

  ::v-deep   .el-table__body .el-form-item {
    padding: 20px 0;
  }

  .addrForm .el-form-item {
    display: inline-block;
  }

  .avatar-uploader ::v-deep   .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
  }

  .avatar-uploader ::v-deep   .el-upload:hover {
    border-color: #409eff;
  }

  .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 64px;
    height: 64px;
    line-height: 64px;
    text-align: center;
  }

  .avatar {
    width: 64px;
    height: 64px;
    display: block;
  }
}

.boxDiv::-webkit-scrollbar {
  width: 0 !important;
}
</style>
