<template>
  <div class="listContainer">
    <div :class="{'listContainer-title':!isDialog,'dialog-title':isDialog}">{{ title }}</div>
    <div class="listContainer-content">
      <div v-for="item in list" :key="item.id" :class="{'item':true,'item-center':isDialog,  'item-disabled':item.sort&&isDialog}"  @click="toDetailPageOrAddItem(item)">
        <div :class="{ 'item-text': isDialog }"  >
         <div  class="moduleName">
          <span :class="{'moduleName-select':item.sort&&isDialog}">{{ item.moduleName }}</span>
          <el-tooltip effect="dark" placement="top" v-if="![22, 23].includes(item.id)&&!isDialog" >
            <div slot="content"><span v-html="getTipContent(item.id)"></span></div>
            <i class="iconfot el-icon-warning-outline"></i>
            <!-- <span v-if="!isDialog" class="span-tip">!</span> -->
            <!-- <img src="@/assets/image/home/<USER>" alt=""> -->
          </el-tooltip>
         </div>

          <div style="color: #333333; font-weight: bold;margin-top:12px;" v-if="!isDialog && !isTadayData">
            {{ getNum(item.id) }}
          </div>
          <div style="color: #333333; font-weight: bold;margin-top:12px" v-if="!isDialog && isTadayData">
            {{ getNumTaday(item.id) }}
          </div>
        </div>
        <template v-if="isDialog">
          <img class="item-img" src="@/assets/image/home/<USER>" v-if="!item.sort" alt="" @click="toDetailPageOrAddItem(item)">
          <img class="item-img2" src="@/assets/image/home/<USER>" alt="" v-if="item.sort&&item.msg" />
          <img class="item-img2" src="@/assets/image/home/<USER>" alt="" v-else-if="item.msg" />
        </template>
      </div>
    </div>
  </div>
</template>

<script>
import { actionTracking } from '@/track/eventTracking';
export default {
  props: {
    type: {
      default: 1
    },
    title: {
      default: ''
    },
    list: {
      default: []
    },
    isDialog: {
      default: false
    },
    todoConfig: {
      type: Object,
      default: () => ({})
    },
    isTadayData: {
      default: false
    },
    storeData: {
      default: () => ({})
    }
  },
  methods: {
    toDetailPageOrAddItem(item) {
      if(this.isDialog){
        this.addItem(item)
        return
      }
      switch (item.id) {
        case 1:
          this.jumpPage('/orderList', 2)
          this.transactionClickItem('no_approved')
          break
        case 2:
          this.jumpPage('/orderList', 6)
          this.transactionClickItem('failed_ERP')
          break
        case 3:
          this.jumpPage('/orderList', 1, 'evidenceToExamine')
          this.transactionClickItem('wire_transfer')
          break
        case 4:
          this.jumpPage('/orderList', 3)
          this.transactionClickItem('no_shipped')
          break
        case 5:
          this.jumpPage('/orderList', 4)
          this.transactionClickItem('48h_no_shipped')
          break
        case 6:
          this.jumpPage('/orderList', 9)
          break
        case 7:
          this.jumpPage('/orderList', 8)
          this.transactionClickItem('illegal_track')
          break
        case 8:
          this.jumpPage('/orderList', 'nonInvoiceOrderCount')
          this.transactionClickItem('no_invoice')
          break
        case 9:
          this.jumpPage('/productList', 1)
          this.commidityClickItem('near_term')
          break
        case 10:
          this.jumpPage('/productList', 2)
          this.commidityClickItem('no_stock')
          break
        case 11:
          this.jumpPage('/productList', 3)
          this.commidityClickItem('outdate')
          break
        case 12:
          this.jumpPage('/productList', 4)
          this.commidityClickItem('0_price')
          break
        case 13:
          this.jumpPage('/productList', 5)
          this.commidityClickItem('prepare_sale')
          break
        case 14:
          this.jumpPage('/productList', 6)
          this.commidityClickItem('fail_to_pass')
          break
        case 15:
          this.jumpPage('/productList', 8)
          this.commidityClickItem('incorrect_information')
          break
        case 16:
          this.jumpPage('/productList', 9)
          break;
        case 17:
          this.jumpPage('/productList', 10)
          this.commidityClickItem('low_price')
          break;
        case 19:
          this.jumpPage('/afterSaleList', 0)
          this.transactionClickItem('no_approved_refund')
          break;
        case 20:
          this.jumpPage('/afterSaleManager', 'no_afterSale')
          this.transactionClickItem('no_afterSale')
          break;
        case 21:
          this.jumpPage('/urgeDelivery')
          this.commidityClickItem('processed_delivery')
          break;
        case 22:
          this.jumpPage('/qualificationManage')
          this.commidityClickItem('qualificationApplyUnUploadCount')
          break;
        case 23:
          this.jumpPage('/drugTestResultManage')
          this.commidityClickItem('drugReportApplyUnUploadCount')
          break;
        case 24:
          this.openFn('newOrderCount')
          break
        case 25:
          this.openFn('amountPayable')
          break
        case 26:
          this.openFn('refundOrderCount')
          break
        case 27:
          this.openFn('refundTotalMoney')
          break
        case 28:
          this.openFn('newCustomerCount')
          break
        case 29:
          this.openFn('sellingInStockSkuCount')
          break
        case 34:
          this.jumpPage('/afterSaleManager')
          break
      }
    },
    transactionClickItem(itemName) {
      actionTracking('agency_task_transaction_click', {
        agency_task_transaction: itemName
      })
    },
    commidityClickItem(itemName) {
      actionTracking('agency_task_commodity_click', {
        agency_task_commodity: itemName
      })
    },
    openFn(str) {
      console.log(str)
      const obj = {}
      let path = ''
      const YMD = this.formatDate(new Date().getTime(), 'YMD')
      const time = [YMD + ' 00:00:00', YMD + ' 23:59:59']
      let trackarg = ''
      switch (str) {
        case 'newOrderCount':
          //今日新订单
          path = '/orderList'
          obj.time = time
          obj.status = 10
          trackarg = 'new_orders'
          break
        case 'amountPayable':
          //今日订单实付
          obj.time = time
          obj.status = 10
          path = '/orderList'
          trackarg = 'new_orders_payment'
          break
        case 'refundOrderCount':
          //今日退款单
          obj.startCreateTime = new Date(time[0]).getTime()
          obj.endCreateTime = new Date(time[1]).getTime()
          obj.status = '0'
          path = '/afterSaleList'
          trackarg = 'refund_order'
          break
        case 'refundTotalMoney':
          //今日已退款金额
          obj.status = '0'
          path = '/afterSaleList'
          trackarg = 'refund_order_payment'
          break
        case 'newCustomerCount':
          //今日新增客户
          path = '/customerList'
          obj.orderStartTime = time
          trackarg = 'new_customers'
          break
        case 'sellingInStockSkuCount':
          //在售有货商品
          path = '/productList'
          obj.status = 1
          obj.stockStatus = '1'
          trackarg = 'goods_on_sale'
          break
      }

      actionTracking('today_store_data_click', {
        today_store_data: trackarg
      })
      obj.homeEnter = 1
      window.openTab(path, obj)
    },
    jumpPage(path, params, from) {
      const obj = {}
      // const YMD = this.formatDate(new Date().getTime(), 'YMD')
      // const strList = YMD.split('-')
      const s = dayjs().valueOf()
      const time = [
        dayjs().subtract(3, 'month').format('YYYY-MM-DD') + ' 00:00:00',
        dayjs().format('YYYY-MM-DD') + ' 23:59:59'
      ]
      if (path === '/orderList') {
        switch (params) {
          case 1:
            // obj.time = time
            if (from) {
              obj.status = '10'
              obj.evidenceToExamine = true
            } else {
              obj.status = '1'
              obj.openAccountStatus = '0'
            }
            break
          case 2:
            obj.time = time
            obj.status = '1'
            break
          case 3:
            obj.time = time
            obj.status = '7'
            break
          case 4:
            obj.status = '1'
            obj.statusList = '[1,7,32,33]'
            break
          case 5:
            obj.startCreateTime = time[0]
            obj.endCreateTime = time[1]
            path = process.env.VUE_APP_BASE_API + '/afterSales/index'
            obj.auditStateListJson = '[0]'
            obj.branchCode = 'XS000000'
            break
          case 6:
            obj.time = time
            obj.status = '1'
            obj.orderSyncStatus = '2'
            break
          case 7:
            obj.logisticsTrackFail = true
            break
          case 8:
            obj.logisticsTrackIllegal = true
            break
          case 9:
            obj.partialShipment = true
            break
          case 'nonInvoiceOrderCount':
            obj.invoiceState = 0
            break
        }
      }
      if (path === '/productList') {
        switch (params) {
          case 1:
            obj.status = ''
            obj.nearTerm = true
            break
          case 2:
            obj.status = 1
            obj.stockStatus = '0'
            break
          case 3:
            obj.status = ''
            obj.expiredOffShelf = true
            break
          case 4:
            obj.zeroPrice = true
            break
          case 5:
            obj.status = 6
            break
          case 6:
            obj.status = 9
            break
          case 7:
            obj.authOffShelf = true
            break
          case 8:
            obj.needRepair = true
            break
          case 9:
            obj.autoSaleWithStock = true
            break
          case 10:
            obj.lowPrice = true
        }
      }
      if (path === '/afterSaleList') {
        obj.auditState = '0'
        obj.auditStateListJson = '[0]'
      }
      obj.homeEnter = 1
      window.openTab(path, obj)
    },
    getNumTaday(id) {
      switch (id) {
        case 24:
          return this.storeData.newOrderCount
        case 25:
          return this.storeData.amountPayable
        case 26:
          return this.storeData.refundOrderCount
        case 27:
          return this.storeData.refundTotalMoney
        case 28:
          return this.storeData.newCustomerCount
        case 29:
          return this.storeData.sellingInStockSkuCount
      }
    },
    getNum(id) {
      const result = {
        0: '',
        1: 'waitExamineOrderCount',
        2: 'pullFailedOrderCount',
        3: 'evidenceToExamine',
        4: 'waitDeliveryOrderCount',
        5: 'timeoutOrderCount',
        6: 'partialShipmentOrderCount',
        7: 'logisticsTrackIllegalCount',
        8: 'nonInvoiceOrderCount',
        9: 'nearEffectSkuCount',
        10: 'sellStockOutSkuCount',
        11: 'expireAutoOutMarketSkuCount',
        12: 'priceOSkuCount',
        13: 'waitPutAwaySkuCount',
        14: 'noPassSkuCount',
        15: 'errorInfoCount',
        16: 'autoSaleWithStockCount',
        17: 'lowPriceCount',
        18: '18',
        19: 'waitExamineRefundOrderCount',
        20: 'afterSalesCount',
        21: 'pendingShipmentReminderQuantity',
        22: 'qualificationApplyUnUploadCount',
        23: 'drugReportApplyUnUploadCount',
        34: "waitAfterSalesOrderCount"
      };
      if (result[id] in this.todoConfig) return this.todoConfig[result[id]]
      else return -1
    },
    addItem(item) {
      if(item.sort)
      return
      this.$emit('addItem', { id: item.id, type: this.type })
    },
    getTipContent(id) {
      const result = {
        1: '订单需进行审核。若已完成ERP订单对接，无需手工审核。<br />请关注订单下发失败原因',
        2: '下推ERP失败的订单数量，请及时查看并处理。',
        3: '订单需进行电汇审核，请及时核款确认。',
        4: '订单需进行发货。若已完成ERP出库单对接，无需手工发货。<br />请在订单支付后48小时内发货完成',
        5: '订单支付成功后超48小时未发货，请尽快发货',
        6: '订单有商品部分发货，请及时关注处理',
        7: '订单发货快递的揽收或签收时间&lt订单支付时间，请检查快递公司或运单号是否填写错误，如有问题及时修正。订单异常物流轨迹可能会影响后期结算，请及时关注处理',
        8: '订单未上传电子发票，请尽快上传',
        9: '商品近效期至距离当前时间&lt;=90天。<br />若近效期至距离当前时间&lt;30天将被自动下架，请及时关注',
        10: '当前已售罄的在售商品，请及时补货或下架',
        11: '商品近效期至距离当前时间&lt;30天，已被平台自动下架<br />请及时关注',
        12: '商品售价为0，请检查并设置单价。<br />售价为0的商品将不允许上架',
        13: '当前可上架的商品，请关注并及时上架',
        14: '平台审核未通过，请关注驳回原因并及时处理',
        15: '商品信息有误，请及时修正。',
        16: '48h内由售罄变为销售中的商品个数，请及时检查价格',
        17: '商品单体价/连锁价/拼团价低于近15天成交价过多，请检查价格',
        18: '商品信息有误，请及时修正。',
        19: '退款单需进行审核',
        20: '请及时处理售后单据',
        21: '催发货待处理、催发货申诉失败、催发货申诉成功状态的单子',
        24: '今日支付的有效订单数量，统计订单状态包含“审核中、出库中、配送中、已完成',
        25: '今日支付的有效订单实付金额(含运费，不含优惠)-今日支付订单的实退金额，统计订单状态包含“审核中、出库中、配送中、已完成',
        26: '今日产生的所有退款单数量',
        27: '今日退款成功的所有退款金额（含运费）',
        28: '今日产生店铺首单的客户',
        29: '当前在售且有库存的商品',
        34: '发票/资质售后，请尽快处理'
      };
      return result[id]
    },

  }
}
</script>

<style scoped>
.listContainer {
  margin-top: 10px;
}
.listContainer-title {
  font-weight: bold;
  font-size: 16px;
  margin-bottom: 10px;
}
.listContainer-content {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 12px;
}
.listContainer .el-button {
  margin: 5px 0 0 5px;
}
.listContainer .el-button span {
  color: blue;
}
.listContainer .el-button .iconfont {
  cursor: pointer;
}
.moduleName{
  display: flex;
  align-items: center;
}
.moduleName span{
  color:#111111
}

.moduleName img{
  margin-left:5px;
}
.span-tip {
  display: inline-block;
  width: 12px;
  height: 12px;
  font-size: 10px;
  border: 1px solid #999999;
  color: #999999;
  text-align: center;
  line-height: 12px;
  border-radius: 50%;
  margin-left: 4px;
  transform: rotate(180deg);
}
.item {
  border: 1px solid #e9e9e9;
  background-color: #f5f5f5;
  border-radius: 4px;
  padding: 8px 2px 8px 10px;
  position: relative;
  cursor: pointer;
  display: flex;

}

.item-center{
  display: flex;
  justify-content: center;
  padding: 5px 2px 5px 10px;
}
.item i{
  transform: rotate(180deg);
  margin-left:3px;
}
.item-disabled{
  cursor: not-allowed;
}
.num {
  margin-top: 8px;
  font-weight: bold;
}
.item-img {
  position: absolute;
  top: 0;
  right: 0;
  transform: translate(50%, -50%);
  height:17px;
  cursor:pointer;
}
.item-img2 {
  position: absolute;
  top: 0;
  left: 0;
  height:14px;

}
.moduleName-select{
  color:#777777 !important;
}
.dialog-title{
  font-size: 14px;
  margin-bottom:16px;
}
</style>
