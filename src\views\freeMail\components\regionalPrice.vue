<template>
<div>
    <el-dialog 
        title="查看区域价" 
        :visible="regionalPriceVisible"
        @close="closeDialog" 
        width="70%">
        <el-row>
            <el-col :span="4" style="text-align: right">
                商品编码：
            </el-col>
            <el-col :offset="1" :span="19">{{productData.barcode}}</el-col>
        </el-row>
        <el-row>
            <el-col :span="4" style="text-align: right">
                单体采购价：
            </el-col>
            <el-col :offset="1" :span="19">{{productData.fob}}</el-col>
        </el-row>
        <el-row>
            <el-col :span="4" style="text-align: right">
                连锁采购价：
            </el-col>
            <el-col :offset="1" :span="19">{{productData.chainPrice}}</el-col>
        </el-row>

        <el-row>
            <el-col :span="4" style="text-align: right">
                分区域价：
            </el-col>
            <el-col :offset="1" :span="19">
                <el-row>
                    <el-col :md="24" :lg="15" style="display: flax">
                            <span>地域：</span>
                            <el-select
                                v-model.trim="provinceCodeList"
                                placeholder="全部"
                                clearable
                                multiple
                                style="width: 30%"
                                collapse-tags
                                @change="getProvince('getCity', $event)"
                            >
                                <el-option
                                v-for="item in proviceList"
                                :key="item.regionCode"
                                :label="item.regionName"
                                :value="item.regionCode"
                                />
                            </el-select>
                            <el-select
                                v-model.trim="cityCodeList"
                                placeholder="全部"
                                clearable
                                multiple
                                collapse-tags
                                style="width: 30%"
                                @change="getProvince('getArea', $event)"
                            >
                                <el-option
                                v-for="item in cityList"
                                :key="item.regionCode"
                                :label="item.regionName"
                                :value="item.regionCode"
                                />
                            </el-select> 
                            <el-select
                                v-model.trim="areaCodeList"
                                placeholder="全部"
                                clearable
                                multiple
                                style="width: 30%"
                                collapse-tags
                            >
                                <el-option
                                v-for="item in areaList"
                                :key="item.regionCode"
                                :label="item.regionName"
                                :value="item.regionCode"
                                />
                            </el-select>
                    </el-col>
                    <el-col :md="24" :lg="9" style="display: flex;">
                        <span>客户类型：</span>
                         <el-select 
                            v-model="customerType"
                            style="width: 50%" 
                            multiple
                            clearable
                            collapse-tags 
                            placeholder="全部">
                            <el-option 
                                v-for="(item,index) in customerOption"
                                :key="index"
                                :label="item.name"
                                :value="item.id">
                            </el-option>
                         </el-select>
                    </el-col>
                </el-row>
                <el-row style="text-align:right;margin: 3vh 5vw 0 0">
                    <el-button @click="resetOptions">
                        重置
                    </el-button>
                    <el-button type="primary" @click="getRegionalPriceList">
                        查询
                    </el-button>
                </el-row>
            </el-col>
        </el-row>
        <el-row>
            <el-table
                ref="areaPriceTable"
                height="250"
                border
                :data="filterTableData"
                style="width: 85%;margin: 10px auto;">
                 <el-table-column
                    prop="groupName"
                    label="用户组名称">
                    <template slot-scope="{ row }">
                        <div>{{row.groupName}}</div>
                    </template>
                </el-table-column>
                <el-table-column
                    prop="price"
                    label="区域价格">
                    <template slot-scope="{ row }">
                        <div style="color: red">{{row.price}}</div>
                    </template>
                </el-table-column>
                <el-table-column
                    prop="areaNames"
                    label="地域">
                    <template slot-scope="{ row }">
                        <div>{{row.areaNames}}</div>
                    </template>
                </el-table-column>
                <el-table-column
                    prop="customerTypes"
                    label="客户类型">
                    <template slot-scope="{ row }">
                        <div>{{row.customerTypes}}</div>
                    </template>
                </el-table-column>
            </el-table>
        </el-row>
        <div slot="footer">
            <el-button type="primary" @click="closeDialog">
                确定
            </el-button>
        </div>
        
    </el-dialog>
</div>
</template>

<script>
import { getRegionList } from '@/api/data-statistics/index'
import { findUserTypes } from '@/api/product';
export default {
name:'regionalPrice',
props: {
    regionalPriceVisible: {
        type: Boolean,
        default: false
    },
    productData: {
        type: Object,
        default: () => ({})
    }
},
watch: {
    productData: {
        handler(newVal) {
            this.tableData = newVal.skuAreaPriceVos
            this.getRegionalPriceList()
        }
    }
},
data() {
    return {
        regionalPriceVisible: false,
        customerType:[],
        // 存放省份的code
        provinceCodeList:[],
        cityCodeList:[],
        areaCodeList:[],
        // 存放省份的code和label
        proviceList: [],
        cityList: [],
        areaList: [],
        customerOption: [],
        tableData: [],
        filterTableData: [],
    }
},
created() {
    this.getProvince('getProv');
    this.findUserTypes();
},
methods: {
    closeDialog() {
        this.$emit('colse')
    },
    resetOptions(type) {
        this.customerType = []
        this.cityList = []
        this.areaList = []
        this.provinceCodeList = []
        this.cityCodeList = []
        this.areaCodeList = []
        this.getRegionalPriceList()
    },
    getRegionalPriceList() {
        this.filterTableData = this.tableData.filter(row => {
            const provinceMatch  = this.provinceCodeList.length === 0 || this.isIncludesTypeMatch(row.allAreaCodes,this.provinceCodeList)
            const cityMatch  = this.cityCodeList.length === 0 || this.isIncludesTypeMatch(row.allAreaCodes,this.cityCodeList)
            const areaMatch  = this.areaCodeList.length === 0 || this.isIncludesTypeMatch(row.allAreaCodes,this.areaCodeList)
            const customerTypeMatch  = this.customerType.length === 0 || this.isIncludesTypeMatch(row.customerTypeIds,this.customerType)
            let flag = true
            if(this.areaCodeList.length > 0) {
                flag = areaMatch
            }else if(this.cityCodeList.length > 0) {
                flag = cityMatch
            }else if(this.provinceCodeList.length > 0) {
                flag = provinceMatch
            }
            return flag && customerTypeMatch 
        })
    },
    isIncludesTypeMatch(rowTypes, selectedTypes) {
        if(!rowTypes || rowTypes.length === 0) return false  
        return selectedTypes.some(selectedType => rowTypes.includes(selectedType));  
    },
    handleRegionChange(value) {
        console.log('选中的地域:', value);  
    },
    getProvince(type, e) {
      let code = e&&e.length?'['+e.join(',')+']':'';
      const pms = { parentCode: code|| null };
      getRegionList(pms).then((res) => {
        // (res.data || []).unshift({
        //   regionName: '全部',
        //   regionCode: '',
        // });
        if (type === 'getProv') {
          this.proviceList = res.data || [];
        } else if (type === 'getCity') {
          if(this.provinceCodeList == "") return this.cityList = []
          this.cityList = res.data || [];
        } else if (type === 'getArea') {
          if(this.cityCodeList == "") return this.areaList = []
          this.areaList = res.data || [];
        }
      });
    },
    findUserTypes() {
      findUserTypes().then((res) => {
        if (res.code == 0) {
          this.customerOption = res.data;
        } else {
          this.$message({
            message: res.message,
            type: 'warning',
          });
        }
      });
    },
    redrawTable() {
        this.$nextTick(() => {
            this.$refs.areaPriceTable.doLayout()
        })
    }
}
}
</script>

<style scoped lang="scss">
.el-row {
    line-height: 35px;
}
::v-deep   .el-dialog {
  border-radius: 5px;
  overflow: hidden;
}
::v-deep   .el-dialog__header {
  padding: 20px;
  background-color: #f3f3f3;
}
::v-deep   .el-dialog__header span {
  font-size: 14px;
  line-height: normal;
}
::v-deep   .el-dialog__header button {
  right: 15px;
  top: 15px;
}
::v-deep   .el-dialog__body {
  max-height: 500px;
  overflow: auto;
  padding: 5px 15px;
  border-bottom: solid 1px #e4eaf1;
}
::v-deep   .el-dialog__footer {
  padding: 15px;
}
</style>