<!--created by <PERSON><PERSON><PERSON><PERSON><PERSON> in 2021/3/12
  checkbox group 方便选择处理
-->
<template>
  <div>
    <el-checkbox
      v-model="checkAll"
      :indeterminate="isIndeterminate"
      @change="handleCheckAllChange"
    >
      全选
    </el-checkbox>
    <el-checkbox-group
      v-model="selectKeys"
      style="margin-top: 5px;"
    >
      <el-checkbox
        v-for="item in dataArray"
        :key="item[keyProp]"
        :label="item[keyProp]"
        @change="handleCheckedChange(item, $event)"
      >
        {{ item[nameProp] }}
      </el-checkbox>
    </el-checkbox-group>
  </div>
</template>

<script>
export default {
  name: 'XyyCheckboxGroup',
  props: {
    nameProp: {
      type: String,
      default: 'name',
    },
    keyProp: {
      // eslint-disable-next-line no-bitwise,vue/require-prop-type-constructor
      type: String | Number,
      default: 'key',
    },
    value: {
      type: Array,
      default: () => [],
    },
    dataArray: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      // 是否选中全部
      checkAll: false,
      isIndeterminate: false,
      checkedArray: [],
      selectKeys: [],
    };
  },
  mounted() {
    this.$nextTick(() => {
      this.dataArray.forEach((item) => {
        if (this.value.indexOf(item[this.keyProp]) >= 0) {
          this.checkedArray.push(item);
        }
      });
      this.selectKeys = this.value;
      this.refactorCheckedAll();
    });
  },
  methods: {
    refactorCheckedAll() {
      const checkedCount = this.checkedArray.length;
      this.checkAll = checkedCount === this.dataArray.length;
      this.isIndeterminate = checkedCount > 0 && checkedCount < this.dataArray.length;
    },
    handleCheckAllChange(val) {
      if (val) {
        this.checkedArray = [].concat(this.dataArray);
      } else {
        this.selectKeys = [];
        this.checkedArray = [];
      }
      this.isIndeterminate = true;
      this.handleConfirm();
    },
    handleConfirm() {
      this.refactorCheckedAll();
      const selectedKeys = this.checkedArray.map(item => item[this.keyProp]);
      this.selectKeys = selectedKeys;
      this.$emit('change', this.checkedArray);
      this.$emit('input', selectedKeys);
    },
    handleCheckedChange(item, checked) {
      if (checked) {
        this.checkedArray.push(item);
      } else {
        const self = this;
        const index = this.checkedArray.findIndex(value => item[self.keyProp] === value[self.keyProp]);
        if (index >= 0) {
          this.checkedArray.splice(index, 1);
        }
      }
      this.refactorCheckedAll();
      this.handleConfirm();
    },
  },
};
</script>

<style scoped></style>
