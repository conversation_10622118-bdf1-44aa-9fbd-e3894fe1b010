<template>
    <div class="nextDayDelivery" v-loading="pageLoading">
        <div class="content content-v1">
            请详细阅读《次日达规则》<el-button type="primary" size="small" @click="openView">点击查阅</el-button>
            <el-button class="util-Button-info" type="primary" size="small"
                @click="custLogDialogVisible = true">操作日志</el-button>
            <el-button class="util-Button" type="primary" size="small" @click="save" v-if="isEdit">保存</el-button>
            <el-button class="util-Button" type="primary" size="small" @click="toEdit" v-else>编辑</el-button>

        </div>
        <div class="content content-v2">
            <span class=""> 是否开启次日达服务&nbsp;&nbsp;</span>
            <el-switch :disabled="!isEdit" v-model="formValue.nextdayEnabled">
            </el-switch>
        </div>
        <div style="display: flex;">
            <div style="width: 60%;">
                <div class="content content-v2">
                    <div class="title">时间</div>
                    <el-button style="margin-top: 10px;" type="primary" size="small" :disabled="!isEdit"
                        @click="openDialogTime">时间配置</el-button>
                </div>
                <div class="content content-v3">
                    <div v-for="item in formValue.shopNextdayTimeConfList" style="padding-bottom: 6px;">
                        {{ { 0: '周一', 1: '周二', 2: '周三', 3: '周四', 4: '周五', 5: '周六', 6: '周日' }[item.timeDay - 1] }}：<span
                            v-if="item.endPayHour && item.endPayMinute && item.arriveHour && item.arriveMinute">{{
        item.endPayHour }}:{{ item.endPayMinute }}前付款，预计明天{{ item.arriveHour }}:{{ item.arriveMinute
                            }}前送达</span>
                    </div>

                </div>
                <div class="content content-v4">
                    <div class="title">地区设置</div>
                    <div style="margin-top: 10px;">区域：</div>
                    <el-form ref="ruleForm" :model="formValue" :disabled="!isEdit">
                        <el-tree style="margin-top: 4px;height: 300px;overflow-y: scroll;" ref="region" :key="elTreeKey"
                            :render-content="renderContent" node-key="areaCode" show-checkbox
                            :default-expanded-keys="expanded" :default-checked-keys="formValue.areaCodes"
                            :data="saleableAreaData" @node-expand="handleExpand" @check-change="checkChange"
                            :props="props"></el-tree>
                    </el-form>

                </div>
            </div>
            <div style="width: 40%;">
                <div class="showImg">
                    示例图：<img src="./img/zstp.png" style="width: 250px;" alt="">
                </div>
            </div>

        </div>
        <div style="margin: 10px 0;">已选：</div>
        <div><el-tag style="margin: 5px;" v-for="item in formValue.areaNames">{{ item == '全选' ? '全国' : item }}</el-tag>
        </div>
        <!-- 查阅 -->
        <el-dialog title="商业次日达规则" :visible.sync="dialogVisible" width="620px">
            <div>
                pop次日达
                <br>
                <br>
                商业自主配置以下次日达信息 <br>
                1、 次日达地区：可支持省份/城市/区县/乡镇 维度配置。 <br>

                2、 次日达时间：周一至周五，0:00-16:00支付订单支持次日达。或每天0:00-16:00支付订单支持次日达，00:00-17:00也可以。 最早不能早于16:00截单。 <br>
                <br>

                权益： <br>

                1、 次日达覆盖地区客户搜索，第三位为次日达商业商品固定展示位。 <br>

                2、 前端商品列表以及商详页均展示次日达标签 <br>

                3、 搜索有次日达标签，前端搜索权重会增加 <br>
                <br>

                要求： <br>
                1、 近4个月本省订单≥90单，且次日达订单率≥85%。 <br>

                <!-- 2、 店铺星级为三星、四星、五星 <br> -->

                2、 店铺次日达开通后，次日达订单率需≥90%，若上周次日达订单率＜90%则取消次日达权益，待数据整改完毕再开通次日达权益。 <br>

                3、 次日达原则为预计达，默认不赔付，如产生售后纠纷至平台介入解决，则按照订单金额*5%最低1元最高5元的规则赔付客户，如超时发货按照超时发货规则处理。 <br>
            </div>
            <span slot="footer" class="dialog-footer">
                <el-button type="primary" @click="closeView">已阅读并同意</el-button>
            </span>
        </el-dialog>
        <!-- 时间设置弹窗 -->
        <el-dialog title="时间设置" :visible.sync="dialogVisibleTime" width="700px" :before-close="handleCloseTime">
            <div v-for="item in untilTimes" class="time-item" style="text-align: center;">

                {{ { 0: '周一', 1: '周二', 2: '周三', 3: '周四', 4: '周五', 5: '周六', 6: '周日' }[item.timeDay - 1] }}：
                <el-select v-model="item.endPayHour" size="small" style="width: 80px;" clearable placeholder="时">
                    <el-option v-for="op in options.hourLimit" :value="op" :label="op"></el-option>
                </el-select>&nbsp;&nbsp;:&nbsp;
                <el-select v-model="item.endPayMinute" size="small" style="width: 80px;" clearable placeholder="分">
                    <el-option v-for="op in options.minute" :value="op" :label="op"></el-option>
                </el-select>
                &nbsp;前付款，预计明天&nbsp;
                <el-select disabled v-model="item.arriveHour" size="small" style="width: 80px;" clearable
                    placeholder="时">
                    <el-option v-for="op in options.hour" :value="op" :label="op"></el-option>
                </el-select>&nbsp;&nbsp;:&nbsp;
                <el-select disabled v-model="item.arriveMinute" size="small" style="width: 80px;" clearable
                    placeholder="分">
                    <el-option v-for="op in options.minute" :value="op" :label="op"></el-option>
                </el-select>
                &nbsp;前送达
            </div>
            <span slot="footer" class="dialog-footer">
                <el-button @click="handleCloseTime" size="small">取消</el-button>
                <el-button type="primary" @click="saveTime" size="small">确定</el-button>
            </span>
        </el-dialog>
        <!-- 操作日志 -->
        <custLog v-if="custLogDialogVisible" ref="custLog" :name="editRow.merchantName"
            :sku-log-dialog-visible.sync="custLogDialogVisible" />
    </div>
</template>

<script>
// import { areasCodes } from '@/api/storeManagement/storeAreaSalesControl';
import custLog from './components/custLog.vue';
import { getQuerySaleableArea } from '@/api/businessCircle'
import { nextDaySave, nextDayQueryConf, nextdayAgree , timeConfig} from '@/api/storeManagement';
export default {
    components: {
        custLog
    },
    data() {
        return {
            elTreeKey: 0,
            editRow: {
                merchantName: '操作日志',
            },
            custLogDialogVisible: false,
            props: { emitPath: false, multiple: true, value: "areaCode", label: "areaName", children: "children" },
            formValue: {

                nextdayEnabled: false,
                areaCodes: [],
                areaNames: [],
                shopNextdayTimeConfList: this.getTimesInit()
            },
            expanded: [0],
            saleableAreaData: [],
            dialogVisible: false,
            isEdit: false,
            dialogVisibleTime: false,
            options: {
                hourLimit:[], //Array.from({ length: 8 }, (_, i) => (i + 15).toString().padStart(2, '0')),
                hour:Array.from({ length: 24 }, (_, i) => (i).toString().padStart(2, '0')),
                minute: []//['00', '15', '30', '45']
            },
            untilTimes: this.getTimesInit(),
            pageLoading: false

        }
    },
    async created() {
        timeConfig().then(res => {
            if (res.code == 0) {
                this.options.hourLimit = res.result.endPay.houre; // 小时限制
                this.options.minute = res.result.endPay.minute; // 分钟限制
            }
        })
        this.pageLoading = true;
        try {
            await Promise.all([

                this.getAreasCodes(),
                this.initData(),
            ]);
            // 所有异步操作完成后执行的逻辑
        } catch (error) {
            console.log(error);
        } finally {
            this.pageLoading = false;
            //去回显地区
            this.$nextTick(() => {
                this.checkChange()
            })

        }

    },
    methods: {
        //关闭阅读弹窗
        closeView() {
            nextdayAgree()
            this.dialogVisible = false
        },
        async initData() {
            let res = await nextDayQueryConf()
            if (res.code == 0) {
                if (res.result.timeConfList) {
                    this.formValue.nextdayEnabled = res.result.nextdayEnabled==1?true:false
                    this.formValue.areaCodes = (res.result.areaConfList||[]).map(item => item.areaCode)
                    this.$refs.region.setCheckedKeys(this.formValue.areaCodes); // 手动设置默认选中
                    console.log(this.formValue.areaCodes, "lwq")
                    //时间
                    res.result.timeConfList.forEach(item => {
                        if (item.endPayHour === -1) {
                            item.endPayHour = ''
                        }
                        if (item.endPayMinute === -1) {
                            item.endPayMinute = ''
                        }
                        if (item.endPayHour !== '') {
                            item.endPayHour = item.endPayHour.toString().padStart(2, '0')
                        }
                        if (item.endPayMinute !== '') {
                            item.endPayMinute = item.endPayMinute.toString().padStart(2, '0')
                        }
                        if (item.arriveHour !== '') {
                            item.arriveHour = item.arriveHour.toString().padStart(2, '0')
                        }
                        if (item.arriveMinute !== '') {
                            item.arriveMinute = item.arriveMinute.toString().padStart(2, '0')
                        }
                    })
                    this.formValue.shopNextdayTimeConfList = res.result.timeConfList
                    console.log(this.formValue.shopNextdayTimeConfList, "lwq")
                } else {
                    //第一次进入
                    this.isEdit = true
                }
            }
        },
        getTimesInit() {
            let arr = []
            Array.from({ length: 7 }, (_, i) => {
                arr.push({
                    timeDay: i + 1,
                    name: { 0: '周一', 1: '周二', 2: '周三', 3: '周四', 4: '周五', 5: '周六', 6: '周日' }[i],
                    endPayHour: '',
                    endPayMinute: '',
                    arriveHour: "23",
                    arriveMinute: "59"
                })
            })
            return arr;
        },
        // 打开时间弹窗
        openDialogTime() {
            this.dialogVisibleTime = true
            this.untilTimes = JSON.parse(JSON.stringify(this.formValue.shopNextdayTimeConfList))
        },
        //时间弹窗关闭
        handleCloseTime() {
            this.dialogVisibleTime = false;
        },
        //打开
        openView() {
            this.dialogVisible = true;
        },
        async getAreasCodes() {
            const res = await getQuerySaleableArea({});
            let arr = [{ areaName: '全选', areaCode: 0, children: res.data }]
            this.saleableAreaData = arr;

        },
        renderContent(h, { node, data, store }) {//树节点的内容区的渲染 Function
            let classname = "";
            // 由于项目中有三级菜单也有四级级菜单，就要在此做出判断
            if (node.level === 4) {
                classname = "foo2";
            }
            if (node.level === 3 && node.childNodes.length === 0) {
                classname = "foo2";
            }
            return h(
                "p",
                {
                    class: classname
                },
                node.label
            );
        },
        handleExpand() {//节点被展开时触发的事件
            this.$nextTick(() => {
                const levelName = document.getElementsByClassName('foo2'); // levelname是上面的最底层节点的名字
                for (let i = 0; i < levelName.length; i++) {
                    levelName[i].parentNode.style.cssFloat = 'left'; // 最底层的节点，包括多选框和名字都让他左浮动
                    levelName[i].parentNode.style.styleFloat = 'left';
                }
            });
        },
        getSimpleCheckedNodes(store) {
            // 定义数组
            const checkedNodes = []
            const checkedName = []
            // 判断是否为全选，若为全选状态返回被全选的节点，不为全选状态正常返回被选中的节点
            const traverse = function (node) {
                const childNodes = node.root ? node.root.childNodes : node.childNodes

                childNodes.forEach(child => {
                    if (child.checked) {
                        // console.log(child)
                        checkedNodes.push({ id: child.data.id, areaLevel: child.level - 1, areaCode: child.data.areaCode, areaName: child.data.areaName })
                    }
                    if (child.indeterminate) {
                        traverse(child)
                    }
                })
            }
            traverse(store)
            return checkedNodes
        },
        checkChange() {
            let areaInfos = this.getSelectCode()
            this.formValue.areaNames = areaInfos.map(item => item.areaName)
        },
        save() {

            let params = JSON.parse(JSON.stringify(this.formValue))
            params.shopNextdayAreaConfList = this.getSelectCode()
            params.nextdayEnabled = params.nextdayEnabled ? 1 : 0
         
            params.shopNextdayTimeConfList.forEach(item => {
                if(item.endPayMinute === ''||item.endPayHour=== ''){
                    item.endPayMinute = -1
                    item.endPayHour = -1
                }
            })
            //校验
            let isNext=true;
            let errMsg='';
            let sum=0;
            params.shopNextdayTimeConfList.forEach((item,index)=>{
                
                if(item.endPayMinute==-1&&item.endPayHour==-1){
                    sum++
                }
            })
            if(sum==7){
                errMsg='时间不能为空，请配置时间'
                isNext=false;
                
            }
            if(!params.shopNextdayAreaConfList||params.shopNextdayAreaConfList.length==0){
                errMsg='地区不能为空，请配置地区'
                isNext=false;
                
            }
            if((!params.shopNextdayAreaConfList||params.shopNextdayAreaConfList.length==0)&&sum==7){
                errMsg='时间地区不能为空，请配置'
                isNext=false;
                
            }
            if(!isNext){
                this.$message({
                    type:'error',
                    message: errMsg
                });
                return
            }
            delete params.areaCodes
            delete params.areaNames
            this.pageLoading = true;
            nextDaySave(params).then(res => {
                if (res.code == 0) {
                    this.$message({
                        type: 'success',
                        message: '保存成功'
                    });
                    this.initData() // 保存成功后重新获取数据，更新v
                    this.isEdit = false;
                } else {
                    this.$message({
                        type: 'error',
                        message: res.msg
                    });
                }
            }).finally(e => {
                this.pageLoading = false;
            })
            // console.log(this.getSelectCode())
        },
        getSelectCode() {
            let areaInfos = this.getSimpleCheckedNodes(this.$refs.region.store)
            // console.log(areaInfos)
            // return areaInfos
            if(areaInfos.some(i=>i.areaCode==0)){
                return this.saleableAreaData[0].children.map((item)=>{
                    return {
                        areaCode:item.areaCode,
                        areaLevel:item.areaLevel,
                        areaName:item.areaName
                    }
                })
            }else{
                return areaInfos
            }
        },
        saveTime() {

            // 校验完整
            try{
                this.untilTimes.forEach((item,index)=>{
                if(!((item.endPayHour&&item.endPayMinute)||(!item.endPayHour&&!item.endPayMinute))){
                  this.$message.error(`${{ 0: '周一', 1: '周二', 2: '周三', 3: '周四', 4: '周五', 5: '周六', 6: '周日' }[item.timeDay - 1]}时间未填写完整`)
                  throw  Error();
                }
            })
            }catch(err){
              return
            }
            this.dialogVisibleTime = false;
            this.formValue.shopNextdayTimeConfList = JSON.parse(JSON.stringify(this.untilTimes))

        },
        //编辑
        toEdit() {
            this.isEdit = true;
        },

    }

}
</script>

<style lang="scss" scoped>
.nextDayDelivery {
    ::v-deep .el-dialog__body {
        padding: 10px 20px;
    }

    padding: 10px 15px;

    .util-Button {
        position: absolute;
        right: 30px;
        top: 10px;
    }

    .util-Button-info {
        position: absolute;
        right: 100px;
        top: 10px;
    }

    .content {
    position: relative;
        padding-bottom: 15px;

        .title {
            font-size: 16px;
            font-weight: 500;
        }
    }

    .content-v1 {
        color: red;
    }

    .showImg {
        display: flex;
        align-items: flex-start;
        padding-left: 30px;

    }

    .time-item {
        margin: 10px 0;
    }
}
</style>