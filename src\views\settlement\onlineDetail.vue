<template>
  <div class="settlement-detail">
    <el-button
      type="primary"
      @click="toBack"
    >
      返回
    </el-button>
    <h4>账单信息</h4>
    <el-form :inline="true">
      <el-row>
        <el-col :span="8">
          <el-form-item label="账单号">
            {{ info.billNo }}
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="账单状态">
            {{ info.billPaymentStatus | formatStatus }}
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="打款状态">
            {{ info.remitStatus | formatRemitStatus }}
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="8">
          <el-form-item label="生成时间">
            {{ info.billCreateTime | formatDate }}
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="入账时间">
            {{ info.billPaymentTime | formatDate }}
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="打款时间">
            {{ info.remitTime | formatDate }}
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="8">
          <el-form-item label="应结算金额（元）">
            {{
              info.statementTotalMoney || info.statementTotalMoney === 0
                ? info.statementTotalMoney.toFixed(2)
                : ''
            }}
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="佣金金额（元）">
            {{
              info.hireMoney || info.hireMoney === 0 ? info.hireMoney.toFixed(2) : ''
            }}
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="佣金结算方式">
            {{
              info.settlementType === 1 ? '非月结' : '月结'
            }}
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="账单类型">
            {{
              { 1: '在线支付', 3: '电汇平台', 4: '电汇商业' }[info.payType]
            }}
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="应缴纳佣金（元）">
            {{
              info.deductedCommission || info.deductedCommission === 0 ? info.deductedCommission.toFixed(2) : ''
            }}
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="实际需缴纳佣金（元）">
            {{
              info.actualCommissionMoney || info.actualCommissionMoney === 0 ? info.actualCommissionMoney.toFixed(2) : ''
            }}
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="支付通道">
            {{
              info.paymentChannel === 1 ? '直连支付' : info.paymentChannel === 3 ? '平安支付' : ''
            }}
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <h4>账单明细</h4>
    <xyy-table
      :data="list"
      :list-query="listQuery"
      :col="col"
      @get-data="getList"
    />
  </div>
</template>

<script>
import { listBillDetail, queryBillByBillNo } from '../../api/settlement/bill';

export default {
  name: 'SettlementOnlineDetail',
  filters: {
    formatDate(date) {
      return date ? new Date(date + 8 * 3600 * 1000).toJSON().substr(0, 19).replace('T', ' ') : '';
    },
    formatStatus(val) {
      return val === 1 ? '已入账' : '未入账';
    },
    formatRemitStatus(val) {
      return val === 1 ? '已打款' : '未打款';
    },
  },
  data() {
    return {
      list: [],
      listQuery: {
        pageSize: 10,
        page: 1,
        total: 0,
      },
      col: [
        {
          index: 'businessNo',
          name: '单据号',
          width: 200,
        },
		{
			index: 'popOrderId',
			name: '订单ID',
			width: 200
		},
        {
          index: 'settlementType',
          name: '佣金结算方式',
          width: 150,
          formatter: (row, col, cell) => {
            if (cell === 1) {
              return '非月结';
            }
            if (cell === 2) {
              return '月结';
            }
            return '';
          },
        },
        {
          index: 'businessType',
          name: '单据类型',
          formatter: (row, col, cell) => {
            if (cell === 1) {
              return '订单';
            }
            if (cell === 2) {
              return '退款单';
            }
            if (cell === 3) {
              return '调账单';
            }
          },
        },
        {
          index: 'merchantErpCode',
          name: '客户ERP编码',
          width: 150,
        },
        {
          index: 'merchantName',
          name: '客户名称',
          width: 150,
        },
        {
          index: 'productMoney',
          name: '商品金额（元）',
          width: 150,
          formatter: (row, col, cell) => (cell || cell === 0 ? cell.toFixed(2) : ''),
        },
        {
          index: 'freightAmount',
          name: '运费金额（元）',
          width: 150,
          formatter: (row, col, cell) => (cell || cell === 0 ? cell.toFixed(2) : ''),
        },
        {
          index: 'totalMoney',
          name: '单据总额含运费（元）',
          width: 200,
          formatter: (row, col, cell) => (cell || cell === 0 ? cell.toFixed(2) : ''),
        },
        {
          index: 'shopTotalDiscount',
          name: '店铺总优惠（元）',
          width: 150,
          formatter: (row, col, cell) => (cell || cell === 0 ? cell.toFixed(2) : ''),
        },
        {
          index: 'platformTotalDiscount',
          name: '平台总优惠（元）',
          width: 150,
          formatter: (row, col, cell) => (cell || cell === 0 ? cell.toFixed(2) : ''),
        },
        {
          index: 'money',
          name: '实付金额（元）',
          width: 150,
          formatter: (row, col, cell) => (cell || cell === 0 ? cell.toFixed(2) : ''),
        },
        {
          index: 'hireMoney',
          name: '佣金金额（元）',
          width: 150,
          formatter: (row, col, cell) => (cell || cell === 0 ? cell.toFixed(2) : ''),
          renderHeader: (h, { column }) => {
            return h(
              'div', [
                h('span', column.label),
                h('el-tooltip', {
                  props: {
                    content: '单据中每个商品的佣金金额=（商品的实付金额+商品分摊的平台优惠金额）*下单时刻商品的佣金比例',
                    placement: 'right',
                  },
                }, [
                  h('i', { class: 'el-icon-warning-outline' }),
                ]),
              ],
            );
          },
        },
        {
          index: 'statementTotalMoney',
          name: '应结算金额（元）',
          width: 150,
          formatter: (row, col, cell) => (cell || cell === 0 ? cell.toFixed(2) : ''),
          renderHeader: (h, { column }) => {
            return h(
              'div', [
                h('span', column.label),
                h('el-tooltip', {
                  props: {
                    content: 'a、当单据佣金结算方式为“非月结”且支付方式为“在线支付、线下转账电汇平台“，应结算金额=单据实付金额-（佣金金额-平台总优惠）\nb、当单据佣金结算方式为“月结”且支付方式为“在线支付、线下转账电汇平台“，应结算金额=单据实付金额\nc、当单据佣金结算方式为“月结”且支付方式为“线下转账电汇商业“，应结算金额=0',
                    placement: 'right',
                  },
                }, [
                  h('i', { class: 'el-icon-warning-outline' }),
                ]),
              ],
            );
          },
        },
        {
          index: 'deductedCommission',
          name: '应缴纳佣金（元）',
          width: 150,
          formatter: (row, col, cell) => (cell || cell === 0 ? cell.toFixed(2) : ''),
          renderHeader: (h, { column }) => {
            return h(
              'div', [
                h('span', column.label),
                h('el-tooltip', {
                  props: {
                    content: '结算单明细商品佣金金额求和',
                    placement: 'right',
                  },
                }, [
                  h('i', { class: 'el-icon-warning-outline' }),
                ]),
              ],
            );
          },
        },
        {
          index: 'commissionDiscount',
          name: '佣金折扣',
          width: 200,
        },
        {
          index: 'discountReason',
          name: '折扣原因',
          width: 200,
        },
        {
          index: 'actualCommissionMoney',
          name: '实际需缴纳佣金',
          width: 200,
          renderHeader: (h, { column }) => {
            return h(
              'div', [
                h('span', column.label),
                h('el-tooltip', {
                  props: {
                    content: '实际需缴纳佣金=应缴纳佣金',
                    placement: 'right',
                  },
                }, [
                  h('i', { class: 'el-icon-warning-outline' }),
                ]),
              ],
            );
          },
        },
        {
          index: 'commissionDiscountMoney',
          name: '佣金优惠',
          width: 200,
          renderHeader: (h, { column }) => {
            return h(
              'div', [
                h('span', column.label),
                h('el-tooltip', {
                  props: {
                    content: '结算单明细商品佣金优惠求和',
                    placement: 'right',
                  },
                }, [
                  h('i', { class: 'el-icon-warning-outline' }),
                ]),
              ],
            );
          },
        },
        {
          index: 'orderPayTime',
          name: '支付时间',
          width: 200,
          formatter: (row, col, cell) => (cell ? new Date(cell + 8 * 3600 * 1000).toJSON().substr(0, 19).replace('T', ' ') : ''),
        },
        {
          index: 'paymentChannel',
          name: '结算方式',
          width: 150,
          formatter: (row, col, cell) => {
            if (cell === 3) {
              return '平安支付'
            } else if (cell === 1) {
              return '直连支付'
            }
          }
        }
      ],
      info: {
        billNo: '',
        billPaymentStatus: '',
        remitStatus: '',
        billCreateTime: '',
        billPaymentTime: '',
        remitTime: '',
        statementTotalMoney: '',
        hireMoney: '',
        settlementType: '',
        payType: '',
        actualCommissionMoney: '',
        deductedCommission: '',
        paymentChannel: ''
      },
    };
  },
  created() {
    if (this.$route.query.billNo) {
      this.listQuery.billNo = this.$route.query.billNo;
      this.getList(this.listQuery);
    }
  },
  activated() {
    if (this.$route.query.billNo) {
      this.listQuery.billNo = this.$route.query.billNo;
      this.getList(this.listQuery);
    }
  },
  methods: {
    /**
     * 获取账单明细列表
     */
    getList(listQuery) {
      const loading = this.$loading({
        lock: true,
        text: 'Loading',
        spinner: 'el-icon-loading',
        background: 'rgba(255,255,255, 0.8)',
      });
      this.getInfo();
      const { page, pageSize } = listQuery;
      listBillDetail({
        pageNum: page,
        pageSize,
        billNo: this.listQuery.billNo,
      })
        .then((res) => {
          loading.close();
          if (res.success) {
            const { list, total, pageNum } = res.data;
            this.list = list;
            this.listQuery = {
              ...this.listQuery,
              total,
              page: pageNum,
            };
          } else {
            this.$message.error({ message: res.errorMsg || res.msg, customClass: 'center-msg' });
          }
        })
        .catch(() => {
          loading.close();
        });
    },
    /**
     * 获取账单明细数据
     */
    getInfo() {
      queryBillByBillNo({ billNo: this.listQuery.billNo })
        .then((res) => {
          if (res.success) {
            Object.keys(this.info).forEach((key) => {
              this.info[key] = res.data[key];
            }, this);
          } else {
            this.$message.error({ message: res.errorMsg || res.msg, customClass: 'center-msg' });
          }
        })
        .catch(() => {});
    },
    toBack() {
      this.$router.push('/bill');
    },
  },
};
</script>

<style>
  .el-tooltip__popper {
    white-space: pre-line;
  }
</style>
<style lang="scss" scoped>
.settlement-detail {
  padding: 15px;
  position: relative;
  .el-button {
    padding: 0 12px;
    line-height: 30px;
    position: absolute;
    right: 15px;
  }
  h4 {
    margin: 0 0 15px 0;
    font-size: 14px;
    font-family: PingFangSC, PingFangSC-Medium;
    font-weight: 500;
    color: #333333;
    &:first-child {
      margin-top: 0;
    }
    &:before {
      content: '';
      background-image: linear-gradient(0deg, #1d69c4 0%, #8bbdfc 99%);
      width: 3px;
      height: 13px;
      float: left;
      margin: 3px 7px 3px 0;
    }
  }
  .el-form {
    width: 95%;
    padding: 0 10px;
    .el-form-item {
      margin-bottom: 5px;
      font-size: 12px;
      font-family: PingFangSC, PingFangSC-Regular;
      font-weight: 400;
      color: #333333;
      ::v-deep  .el-form-item__label {
        height: 30px;
        line-height: 30px;
      }
      ::v-deep  .el-form-item__content {
        height: 30px;
        line-height: 30px;
      }
    }
  }
}
</style>
