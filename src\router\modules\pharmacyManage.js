import layout from '@/layout';

const pharmacyManage = {
  path: '/customerManage',
  name: 'customerManage',
  component: layout,
  meta: {
    title: '药店管理',
    icon: 'el-icon-s-custom',
  },
  children: [
    {
      path: `${process.env.VUE_APP_BASE_API}/openClientAccount/index`,
      name: 'openClientAccountIndex',
      meta: { title: '开户管理' },
    },
    {
      path: '/customerList',
      name: 'customerList',
      component: () => import('@/views/customer-management/customerList.vue'),
      meta: { title: '客户列表' },
    },
    {
      path: '/customerOperation',
      name: 'customerOperation',
      component: () => import('@/views/marketing/customerOperation.vue'),
      meta: { title: '客户运营' },
    },
    {
      path: '/controlUserGroup',
      name: 'controlUserGroup',
      component: () => import('@/views/control-goods/controlUserGroup.vue'),
      meta: { title: '控销药店组' },
    },
    {
      path: '/priceUserGroup',
      name: 'priceUserGroup',
      component: () => import('@/views/control-goods/priceUserGroup.vue'),
      meta: { title: '分区域定价' },
    },
    {
      path: `${process.env.VUE_APP_BASE_API}/postmail/index`,
      name: 'postmailIndex',
      meta: { title: '邮寄查询' },
    },
    {
      path: '/customerQualification',
      name: 'customerQualification',
      component: () => import('@/views/customer-management/customerQualification.vue'),
      hidden: true
    },
    {
      path: '/factoryReport',
      name: 'factoryReport',
      component: () => import('@/views/customer-management/factoryReport.vue'),
      meta: { title: '厂家查价举报' },
      hidden: true
    }
  ]
}
export default pharmacyManage
