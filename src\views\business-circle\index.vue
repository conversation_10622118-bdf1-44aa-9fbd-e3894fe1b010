<template>
  <div class="storeVoucher">
    <div class="sticky-tabs">
        <el-tabs v-model="activeName" @tab-click="handleClick">
            <el-tab-pane label="店铺商圈设置" name="businessCircle"/>
            <el-tab-pane label="店铺商圈控销" name="storeAreaSalesControl"/>
        </el-tabs>
    </div>
    <transition name="fade" mode="out-in">
        <keep-alive>
            <component :is="currentComponent"></component>
        </keep-alive>
    </transition>
  </div>
</template>

<script>
import businessCircle from "@/views/business-circle/businessCircle.vue"
import storeAreaSalesControl from "@/views/store-management/storeAreaSalesControl/index.vue"
export default {
    name: "shopCircleManage",
    components: {
        businessCircle,
        storeAreaSalesControl
    },
    data() {
        return {
            activeName: "businessCircle",
            currentComponent: businessCircle,
        }
    },
    created() {
        if(this.$route.query.to) {
            this.selectComponents(this.$route.query.to)
        }
    },
    methods: {
        handleClick(tab, event) {
            this.$router.replace({
                path: 'shopBusinessManage',
                query: { to: tab.name },
            });
            this.selectComponents(tab.name)
        },
        selectComponents(target) {
            if(target) {
                this.activeName = target
            }
            switch (target) {
                case "businessCircle":
                    this.currentComponent = businessCircle
                    break;
                case "storeAreaSalesControl":
                    this.currentComponent = storeAreaSalesControl
                    break;
                default:
                    break;
            }
        }
    },
}
</script>

<style>
.storeVoucher {
    margin-top: 10px;
    padding-left: 10px;
}
.sticky-tabs {
  position: sticky;
  top: 0;
  z-index: 1000;
  background: #fff;
  padding: 10px 0;
}
  /* 定义过渡动画 */
.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-to,
.fade-leave-from {
  opacity: 1;
}
</style>