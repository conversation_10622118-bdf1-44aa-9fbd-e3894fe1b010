<template>
  <div class="preview-box">
    <el-row :gutter="10">
      <el-col :span="8">
        <slides :imgs="imgs" />
      </el-col>
      <el-col :span="16">
        <introduction :info="info" />
      </el-col>
    </el-row>
    <el-tabs type="border-card">
      <el-tab-pane label="商品详情">
        <detail :detail="detail" />
      </el-tab-pane>
      <el-tab-pane
        label="关于药帮忙"
        :disabled="true"
      />
    </el-tabs>
  </div>
</template>

<script>
import slides from './components/slideshow';
import introduction from './components/introduction';
import detail from './components/detail';
import { getProductDetail } from '../../api/product';

export default {
  name: 'productPreview',
  components: {
    slides,
    introduction,
    detail,
  },
  props: {
    barcodeProp: {
      type: String,
      default() {
        return '';
      }
    }
  },
  data() {
    return {
      barcode: '',
      imgs: [],
      info: {
        showName: '', // 名称
        fob: '', // 含税价
        suggestPrice: '', // 建议零售价
        grossMargin: '', // 毛利率
        spec: '', // 规格
        dosageForm: '', // 剂型
        nearEffect: '', // 有效期 近至
        farEffect: '', // 有效期 远至
        pieceLoading: '', // 件装量
        approvalNumber: '', // 批准文号
        mediumPackageNum: '', // 中包装
        manufacturer: '', // 生产厂家
        productUnit: '', // 单位
        isSplit: '', // 是否可拆零 1 是 0 否
      },
      detail: {
        skuCategory: '', // 商品分类
        manufacturer: '', // 生产厂家
        approvalNumber: '', // 批准文号
        term: '', // 有效期
        spec: '', // 规格
        storageCondition: '', // 存储条件
        mediumPackageNum: '', // 中包装
        pieceLoading: '', // 件装量
        indication: '', // 适应症/功能主治
        usageAndDosage: '', // 用法与用量
        untowardEffect: '', // 不良反应
        productUnit: '', // 单位
        isSplit: '', // 是否可拆零 1 是 0 否
        imgs: [], // 图片集合
      },
    };
  },
  created() {
    if (this.$route.query.barcode || this.barcodeProp) {
      this.barcode = this.$route.query.barcode || this.barcodeProp;
      this.initProduct();
    }
  },
  methods: {
    initProduct() {
      getProductDetail({ barcode: this.barcode })
        .then((res) => {
          if (res.code === 0) {
            const { data } = res;
            this.imgs.push(`${data.productImgBaseUrl}${data.imageUrl}`);
            if (data.imagesList && data.imagesList.length) {
              data.imagesList.forEach((url) => {
                this.imgs.push(`${data.productImgBaseUrl}${url}`);
              }, this);
            }
            Object.keys(this.info).forEach((key) => {
              if (Object.prototype.hasOwnProperty.call(data, key)) {
                this.info[key] = data[key];
              }
            }, this);

            Object.keys(this.detail).forEach((key) => {
              if (Object.prototype.hasOwnProperty.call(data, key)) {
                this.detail[key] = data[key];
              }
            }, this);
            if (data.skuInstructionImageList && data.skuInstructionImageList.length) {
              data.skuInstructionImageList.forEach((obj) => {
                this.detail.imgs.push(`${data.productDescImgBaseUrl}${obj.instrutionImageUrl}`);
              }, this);
            }
          } else {
            this.$message.error(res.message);
          }
        })
        .catch(() => {});
    },
  },
};
</script>

<style lang="scss" scoped>
.preview-box {
  padding: 5px;
  box-sizing: border-box;
  .el-tabs {
    margin-top: 100px;
    &.el-tabs--border-card {
      box-shadow: none;
      ::v-deep   .el-tabs__header .el-tabs__item.is-active {
        color: #00c675;
      }
    }
  }
}
</style>
