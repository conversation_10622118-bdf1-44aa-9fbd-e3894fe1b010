<script>
import { mapState } from 'vuex';
export default {
  name: 'MenuItem',
  functional: true,
  props: {
    icon: {
      type: String,
      default: ''
    },
    title: {
      type: String,
      default: ''
    },
    badge: {
      type: String,
      default: ''
    },
    count: {
      default: ''
    },
    showRedDotNum: {
      default: ''
    },
    isGrayUser:{
      default: false
    }
  },
  render(h, context) {
    const {icon, title, badge,count,showRedDotNum} = context.props
    const vnodes = []
    if (icon) {
      if (icon.includes('el-icon')) {
        // if(context.props.isGrayUser){
        //   vnodes.push(<span class="menu-round" ></span>)
        // }else{

          vnodes.push(<i class={[icon, 'sub-el-icon']}/>)
        // }
      } else {
        vnodes.push(<svg-icon icon-class={icon}/>)
      }
    }
    if (title) {
      vnodes.push(<span slot='title'>{(title)}</span>)
    }
    if(showRedDotNum && count > 0) {
        vnodes.push(<span slot='title' class="count">{(count)}</span>)
    }
    if (badge) {
      vnodes.push(<span class={['menuBadge']}>{(badge)}</span>)
    }
    return vnodes
  }
}
</script>

<style scoped>
.sub-el-icon {
  color: currentColor;
  width: 1em;
  height: 1em;
}

.menuBadge {
  float: right;
  margin-top: 16px;
  margin-right: -6px;
  font-size: 12px;
  color: #fff;
  line-height: 19px;
  padding: 0 4px;
  border-radius: 9px;
  background-color: red;
  z-index: 1001;
}
.count {
	background: #ff4d4f;
	border-radius: 11px;
	color: #fff;
	font-size: 12px;
	margin-left: 2px;
	padding: 0 8px;
  margin-left: 3px;
}
.menu-round{
display: inline-block;
width: 12px;
height: 12px;
border-radius: 12px;
background-color: #DCDCDC;
margin-right: 4px;
}
</style>
