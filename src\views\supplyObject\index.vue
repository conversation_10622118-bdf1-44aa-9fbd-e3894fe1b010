<template>
    <div class="storeVoucher">
      <div class="sticky-tabs">
          <el-tabs v-model="activeName" @tab-click="handleClick">
              <el-tab-pane label="供货对象设置" name="supplyObjectPage"/>
              <el-tab-pane label="店铺控销药店名单" name="blacklistStoreControlledSales"/>
              <el-tab-pane label="商品控销药店名称查询" name="goodsControlledSales"/>
          </el-tabs>
      </div>
      <transition name="fade" mode="out-in">
        <keep-alive>
            <component :is="currentComponent"></component>
        </keep-alive>
      </transition>
    </div>
  </template>
  
  <script>
  import supplyObjectPage from "@/views/supplyObject/supplyObjectPage"
  import blacklistStoreControlledSales from "@/views/store-management/blacklistStoreControlledSales/index.vue"
  import goodsControlledSales from "@/views/supplyObject/goodsControlledSales.vue"
  export default {
      name: "shopCustomerManage",
      components: {
        supplyObjectPage,
        blacklistStoreControlledSales,
        goodsControlledSales
      },
      data() {
          return {
              activeName: "supplyObjectPage",
              currentComponent: supplyObjectPage,
          }
      },
      created() {
        if(this.$route.query.to) {
            this.selectComponents(this.$route.query.to)
        }
      },
      methods: {
          handleClick(tab, event) {
              this.$router.replace({
                  path: 'shopCustomerManage',
                  query: { to: tab.name },
              });
              this.selectComponents(tab.name)
          },
          selectComponents(target) {
              if(target) {
                  this.activeName = target
              }
              switch (target) {
                  case "supplyObjectPage":
                      this.currentComponent = supplyObjectPage
                      break;
                  case "blacklistStoreControlledSales":
                      this.currentComponent = blacklistStoreControlledSales
                      break;
                  case "goodsControlledSales":
                      this.currentComponent = goodsControlledSales
                      break;
                  default:
                      break;
              }
          }
      },
  }
  </script>
  
  <style>
  .storeVoucher {
      margin-top: 10px;
      padding-left: 10px;
  }
  .sticky-tabs {
    position: sticky;
    top: 0;
    z-index: 1000;
    background: #fff;
    padding: 10px 0;
  }
  /* 定义过渡动画 */
.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-to,
.fade-leave-from {
  opacity: 1;
}
  </style>