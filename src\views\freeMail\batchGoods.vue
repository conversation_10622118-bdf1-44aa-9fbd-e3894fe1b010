<template>
  <div>
    <div class="line-div">
      <div class="padding20">
        <div class="serch">
          <el-row type="flex" align="middle">
            <span class="sign" />
            <div>批量发布商品（待发布商品）</div>
          </el-row>
        </div>
        <div>
          <el-row>
            <el-col :span="12">
              <div class="div-info">
                <p>
                  <span class="release-goods-green">{{ successNum }}</span
                  >个商品可一键发布
                  <span
                    class="release-goods-search"
                    type="text"
                    @click="searchFn(0)"
                    >立即搜索</span
                  >
                </p>
                <!-- 修改文本 31行 -->
                <p>
                  <span class="release-goods-info">
                    <span style="color: #ff2121">{{
                      differentStandardCount
                    }}</span>
                    个商品通用名称、商品名称、规格、包装单位、批准文号、厂家、产地与标准库ID不一致，发布后将使用标准库信息（处方类型、剂型、存储条件、生产许可证或备案凭证编号默认使用标品信息发布）
                  </span>
                  <span
                    class="release-goods-search"
                    type="text"
                    @click="searchFn(2)"
                    >立即搜索</span
                  >
                </p>
                <p>
                  <span class="release-goods-info">
                    <span style="color: #ff2121">{{
                      differentStandardCodeCount
                    }}</span>
                    个商品69码与标准库ID不一致
                  </span>
                  <span
                    class="release-goods-search"
                    type="text"
                    @click="searchFn(1)"
                    >立即搜索</span
                  >
                </p>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="div-info">
                <p>
                  <span class="release-goods-red">{{ noImgNum }}</span
                  >个商品没有商品图片
                  <span
                    class="release-goods-search"
                    type="text"
                    @click="searchNoImg"
                    >搜索无图商品</span
                  >
                </p>
                <p>
                  <span class="release-goods-info"
                    >商品发布后将导致在首页、推荐等重要位置流量受限，立即搜索无图商品，批量上传商品图片</span
                  >
                </p>
              </div>
            </el-col>
          </el-row>
          <div class="div-info">
            <p>
              <span class="release-goods-red">{{ errorNum }}</span
              >个商品存在问题
              <span class="release-goods-search">
                <span v-if="!showMore" @click="showMore = true">
                  展开
                  <i class="el-icon-arrow-down" />
                </span>
                <span v-else @click="showMore = false">
                  收起
                  <i class="el-icon-arrow-up" />
                </span>
              </span>
            </p>
            <p>
              <span class="release-goods-info"
                >其中包括匹配失败、匹配结果多个、商品信息不全/单位不规范、商品经营范围不在企业经营范围内、暂无销售权限</span
              >
            </p>
            <div class="tip-box" v-show="showMore">
              <p class="tip">
                <span style="color: #ff2121">{{ matchMoreCount }}</span
                >个商品匹配结果为多个，点击“点击选择”，选择符合的商品，匹配ID后点击一键上架
              </p>
              <p class="tip">
                <span style="color: #ff2121">{{ lossInfoCount }}</span
                >个商品信息不完整/单位不规范，点击“去补充信息”进入商品编辑页面，填写完整提交后，商品创建成功
              </p>
              <p class="tip">
                <span style="color: #ff2121">{{ outOfBusinessCount }}</span
                >个商品经营范围不在企业经营范围内和暂无销售权限的无法创建，如有疑问可联系您的专属销售
              </p>
            </div>
          </div>
        </div>
        <!-- <div class="div-text">
          <p>
            <span class="status-span">
              <i style="background: #00b377" />
            </span>
            <span style="color:#ff2121">{{successNum}}</span>个商品可一键发布(上架前，请仔细核对商品的69码、名称、规格、包装单位、批准文号、厂家与匹配ID的信息一致)
          </p>
          <div class="tip-box">
            <p class="tip">
              <span style="color:#ff2121">{{differentStandardCount}}</span>个商品匹配ID后，商品通用名称、商品的名称、规格、包装单位、批准文号、厂家与标准库ID不一致，发布后将使用标准库信息，
              <el-button type="text" @click="searchFn(2)">立即搜索</el-button>
            </p>
            <p class="tip">
              <span style="color:#ff2121">{{differentStandardCodeCount}}</span>个商品69码与标准库ID不一致
              <el-button type="text" @click="searchFn(1)">立即搜索</el-button>
              <span>，是否使用标准库字段进行</span>
              <el-button type="text" @click="cover">覆盖</el-button>
            </p>
          </div>
          <p>
            <span class="status-span">
              <i style="background: #ff2121" />
            </span>
            <span style="color:#ff2121">{{errorNum}}</span>个商品存在问题，其中包括匹配失败、匹配结果多个、商品信息不全/单位不规范、商品经营范围不在企业经营范围内、暂无销售权限
          </p>
          <div class="tip-box">
            <p class="tip">
              <span style="color:#ff2121">{{matchMoreCount}}</span>个商品匹配结果为多个，点击“点击选择”，选择符合的商品，匹配ID后点击一键上架
            </p>
            <p class="tip">
              <span style="color:#ff2121">{{lossInfoCount}}</span>个商品信息不完整/单位不规范，点击“去补充信息”进入商品编辑页面，填写完整提交后，商品创建成功
            </p>
            <p class="tip">
              <span style="color:#ff2121">{{outOfBusinessCount}}</span>个商品经营范围不在企业经营范围内和暂无销售权限的无法创建，如有疑问可联系您的专属销售
            </p>
          </div>
          <p v-if="noImgNum > 0" style="padding-top: 0; padding-bottom: 15px">
            <span class="status-span" style="color: #ff2121">
              <i style="background: #ff2121" />
              {{ noImgNum }}
            </span>
            个商品当前还没有商品图片，商品发布后将导致在首页、推荐等重要位置流量受限，立即
            <el-button type="text" @click="searchNoImg">搜索无图商品</el-button>，批量上传商品图片吧！
          </p>
        </div>-->
        <div style="margin-top: 16px">
          <el-row :gutter="20">
            <el-col :span="6">
              <el-input
                v-model="listQuery.productName"
                placeholder="请输入内容"
                size="small"
              >
                <template slot="prepend">商品名称</template>
              </el-input>
            </el-col>
            <el-col :span="6">
              <el-input
                v-model="listQuery.erpCode"
                placeholder="请输入内容"
                size="small"
              >
                <template slot="prepend">ERP编码</template>
              </el-input>
            </el-col>
            <el-col :span="6">
              <el-input
                v-model="listQuery.manufacturer"
                placeholder="请输入内容"
                size="small"
              >
                <template slot="prepend">生产厂家</template>
              </el-input>
            </el-col>
            <el-col :span="6">
              <el-input
                v-model="listQuery.code"
                placeholder="请输入内容"
                size="small"
              >
                <template slot="prepend">条码</template>
              </el-input>
            </el-col>
          </el-row>
          <el-row :gutter="20" style="margin-top: 12px" class="my-search-row">
            <el-col :span="6">
              <el-input
                v-model="listQuery.approvalNumber"
                placeholder="请输入内容"
                size="small"
              >
                <template slot="prepend">批准文号</template>
              </el-input>
            </el-col>
            <el-col :span="6">
              <div style="display: flex">
                <span class="search-title">库存</span>
                <el-select
                  v-model="listQuery.hasStock"
                  placeholder="请选择"
                  size="small"
                >
                  <el-option label="全部" :value="-1" />
                  <el-option label="有" :value="1" />
                  <el-option label="无" :value="2" />
                </el-select>
              </div>
            </el-col>
            <el-col :span="6">
              <div style="display: flex">
                <span class="search-title">商品来源</span>
                <el-select
                  v-model="listQuery.source"
                  placeholder="请选择"
                  size="small"
                >
                  <el-option label="全部" :value="-1" />
                  <el-option label="erp对接" :value="1" />
                  <el-option label="excel上传" :value="2" />
                </el-select>
              </div>
            </el-col>
            <el-col :span="6">
              <div style="display: flex">
                <span class="search-title">是否有图</span>
                <el-select
                  v-model="listQuery.hasImg"
                  placeholder="请选择"
                  size="small"
                >
                  <el-option label="全部" :value="-1" />
                  <el-option label="有" :value="1" />
                  <el-option label="无" :value="2" />
                </el-select>
              </div>
            </el-col>
          </el-row>
          <el-row :gutter="20" style="margin-top: 12px" class="my-search-row">
            <el-col :span="6">
              <div style="display: flex">
                <span class="search-title">商品匹配结果</span>
                <el-select
                  v-model="listQuery.canPublish"
                  placeholder="请选择"
                  size="small"
                >
                  <el-option
                    v-for="item in canPublishList"
                    :key="item.code"
                    :label="item.name"
                    :value="item.code"
                  />
                </el-select>
              </div>
            </el-col>
            <el-col :span="6">
              <el-input
                v-model="listQuery.barcode"
                placeholder="输入完整S开头商品编码"
                size="small"
              >
                <template slot="prepend">商品编码</template>
              </el-input>
            </el-col>
            <el-col :span="6">
              <el-input
                v-model="listQuery.spec"
                placeholder="请输入内容"
                size="small"
              >
                <template slot="prepend">商品规格</template>
              </el-input>
            </el-col>
            <!-- <el-col :span="6">
              <div style="display: flex">
                <span class="search-title">商品分类</span>
                <el-cascader
                  class="dis_cascader"
                  ref="productCategoryLevel"
                  v-model="listQuery.categoryId"
                  :options="productCategoryLevelOptions"
                  :props="{ label: 'name', value: 'id', checkStrictly: true }"
                  :show-all-levels="false"
                  clearable
                  @change="handleCascaderVal"
                />
              </div>
            </el-col> -->
          </el-row>
          <el-row>
            <el-col :span="24" style="text-align: right; padding-top: 10px">
              <el-button size="small" @click="resetList">重置</el-button>
              <el-button type="primary" size="small" @click="searchList"
                >查询</el-button
              >
            </el-col>
          </el-row>
        </div>
      </div>
    </div>
    <div class="padding20 con-box">
      <div class="btn-box">
        <el-button type="primary" size="small" @click="goodsPush"
          >一键发布</el-button
        >
        <el-button
          type="primary"
          size="small"
          :disabled="isReset"
          @click="resetDataList"
          >重新匹配</el-button
        >
        <!--        <el-button type="primary" plain size="small" @click="showGoods">批量导入商品</el-button>-->
        <el-dropdown
          style="padding: 0 10px"
          size="small"
          trigger="click"
          @command="showGoods"
        >
          <el-button type="primary" plain size="small">
            批量导入商品
            <i class="el-icon-arrow-down el-icon--right" />
          </el-button>
          <el-dropdown-menu slot="dropdown">
            <!-- <el-dropdown-item command="a">批量匹配标准库</el-dropdown-item> -->
            <el-dropdown-item command="b">批量创建商品</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
        <el-button type="primary" plain size="small" @click="showGoodsImg"
          >批量上传商品图片</el-button
        >
        <el-button
          type="primary"
          plain
          size="small"
          style="margin-left: 10px"
          @click="exportGoods"
          :disabled="tableData.length < 1"
          >导出商品</el-button
        >
        <el-button
          type="primary"
          plain
          size="small"
          style="margin-left: 10px"
          @click="seeGoodsHistory(1)"
          >商品导入记录</el-button
        >
        <el-button type="primary" plain size="small" @click="deleteListImg"
          >删除</el-button
        >
        <p class="tip">
          注：该列表内的商品包含“ERP对接抓取的商品”和“Excel批量上传的商品”，如您未对接ERP则可点击“批量导入商品”按钮上传商品
        </p>
      </div>
      <el-tabs v-model="activeType" type="card" @tab-click="changeTab">
        <el-tab-pane
          :label="`未发布(${countObj.unPublishedCount || 0})`"
          name="0"
        />
        <el-tab-pane
          :label="`已发布(${countObj.publishedCount || 0})`"
          name="1"
        />
      </el-tabs>
      <div class="tab_all" v-if="activeType != '1'">
        <el-tabs
          v-model="filterType"
          type="card"
          @tab-click="handleFilterChangeTab"
        >
          <el-tab-pane :label="'全部'" name="0" />
          <el-tab-pane
            :label="`一键发布-精确匹配(${
              countObj.unPublishedAccurateMatchCount || 0
            })`"
            name="1"
          />
          <el-tab-pane
            :label="`一键发布-模糊匹配(${
              countObj.unPublishedFuzzyMatchCount || 0
            })`"
            name="2"
          />
          <el-tab-pane
            :label="`已删除(${countObj.unPublishedNonValidCount || 0})`"
            name="3"
          />
        </el-tabs>
        <el-button
          style="margin-left: 10px"
          v-if="filterType == 3"
          type="primary"
          plain
          size="small"
          @click="handleRecoverDelete"
          >恢复</el-button
        >
      </div>
      <div>
        <el-table
          ref="multipleTable"
          :data="tableData"
          stripe
          border
          tooltip-effect="dark"
          style="width: 100%"
          @selection-change="handleSelectionChange"
        >
          <el-table-column
            type="selection"
            width="45"
            v-if="activeType === '0'"
            :key="Math.random()"
          />
          <el-table-column
            label="匹配状态"
            v-if="activeType === '0'"
            :key="Math.random()"
          >
            <template slot-scope="scope">
              <span v-if="scope.row.canPublishStatus === 1" class="status-span">
                <i style="background: #00b377" /> 正常
              </span>
              <span v-else class="status-span">
                <i style="background: #ff2121" /> 异常
              </span>
            </template>
          </el-table-column>
          <el-table-column label="商品来源">
            <template slot-scope="scope">
              <span>{{ scope.row.source === 1 ? 'ERP' : '批量导入' }}</span>
            </template>
          </el-table-column>
          <el-table-column
            prop="barcode"
            label="商品编码"
            v-if="activeType === '1'"
            :key="Math.random()"
          />
          <el-table-column prop="erpCode" label="ERP编码" />
          <el-table-column prop="productName" label="商品名称">
            <template slot-scope="{ row }">
              <div>{{ row.productNameStandard }}</div>
              <div
                :class="
                  filterType != '1' &&
                  row.productName &&
                  row.productNameStandard &&
                  removeSpecialCharactersAndToLower(row.productName) !==
                    removeSpecialCharactersAndToLower(row.productNameStandard)
                    ? 'atypismStr'
                    : ''
                "
              >
                {{ row.source === 1 ? '商家ERP：' : '商家：'
                }}{{ row.productName }}
              </div>
            </template>
          </el-table-column>

          <el-table-column prop="commonName" label="通用名称">
            <template slot-scope="{ row }">
              <div>{{ row.commonNameStandard }}</div>
              <div
                :class="
                  filterType != '1' &&
                  row.commonName &&
                  row.commonNameStandard &&
                  removeSpecialCharactersAndToLower(row.commonName) !==
                    removeSpecialCharactersAndToLower(row.commonNameStandard)
                    ? 'atypismStr'
                    : ''
                "
              >
                {{ row.source === 1 ? '商家ERP：' : '商家：'
                }}{{ row.commonName }}
              </div>
            </template>
          </el-table-column>

          <el-table-column prop="code" label="条码">
            <template slot-scope="{ row }">
              <template v-if="row.code && row.codeStandard">
                <template v-if="row.code !== row.codeStandard">
                  <el-radio-group
                    v-model="row.codeSelected"
                    @change="selectChangeCode(row)"
                    :disabled="activeType === '1' || filterType == 3"
                  >
                    <el-radio :label="row.codeStandard">{{
                      row.codeStandard
                    }}</el-radio>
                    <el-radio :label="row.code">
                      <span v-if="row.source === 1" :class="filterType != '1' ?  'atypismStr' : ''"
                        >商家ERP：{{ row.code }}</span
                      >
                      <span v-else :class="filterType != '1' ? 'atypismStr' : ''"
                        >商家：{{ row.code }}</span
                      >
                    </el-radio>
                  </el-radio-group>
                  <!-- <div>{{ row.codeStandard }}</div> -->
                  <!-- <div class="atypismStr">商家ERP：{{row.code}}</div> -->
                </template>
                <template v-else>
                  <div>{{ row.code }}</div>
                </template>
              </template>
              <template v-else>
                <div>{{ row.code || row.codeStandard }}</div>
              </template>
            </template>
          </el-table-column>

          <el-table-column prop="approvalNumber" label="批准文号">
            <template slot-scope="{ row }">
              <div>{{ row.approvalNumberStandard }}</div>
              <div
                :class="
                  filterType != '1' &&
                  row.approvalNumber &&
                  row.approvalNumberStandard &&
                  removeSpecialCharactersAndToLower(row.approvalNumber) !==
                    removeSpecialCharactersAndToLower(
                      row.approvalNumberStandard
                    )
                    ? 'atypismStr'
                    : ''
                "
              >
                {{ row.source === 1 ? '商家ERP：' : '商家：'
                }}{{ row.approvalNumber }}
              </div>
            </template>
          </el-table-column>

          <el-table-column prop="manufacturer" label="生产厂家">
            <template slot-scope="{ row }">
              <div>{{ row.manufacturerStandard }}</div>
              <div
                :class="
                  filterType != '1' &&
                  row.manufacturer &&
                  row.manufacturerStandard &&
                  removeSpecialCharactersAndToLower(row.manufacturer) !==
                    removeSpecialCharactersAndToLower(row.manufacturerStandard)
                    ? 'atypismStr'
                    : ''
                "
              >
                {{ row.source === 1 ? '商家ERP：' : '商家：'
                }}{{ row.manufacturer }}
              </div>
              <div v-if="row.entrustedManufacturer">受托厂家：{{row.entrustedManufacturer}}</div>
              <div v-if="row.entrustedManufacturerAddress"> 受托生产厂家地址：{{row.entrustedManufacturerAddress}}</div>
            </template>
          </el-table-column>

          <el-table-column prop="producer" label="产地">
            <template slot-scope="{ row }">
              <div v-if="row.producerStandard">
                {{ row.producerStandard }}
              </div>
              <div v-if="row.producer">
                <div
                  :class="
                    filterType != '1' &&
                    row.producerStandard &&
                    row.producer &&
                    removeSpecialCharactersAndToLower(row.producerStandard) !=
                      removeSpecialCharactersAndToLower(row.producer)
                      ? 'atypismStr'
                      : ''
                  "
                >
                  {{ row.source === 1 ? '商家ERP：' : '商家：'
                  }}{{ row.producer }}
                </div>
              </div>
            </template>
          </el-table-column>

          <el-table-column prop="spec" label="规格">
            <template slot-scope="{ row }">
              <div>{{ row.specStandard }}</div>
              <div
                :class="
                  filterType != '1' &&
                  row.spec &&
                  row.specStandard &&
                  !compareStrings(
                    removeSpecialCharactersAndToLower(row.spec),
                    removeSpecialCharactersAndToLower(row.specStandard)
                  )
                    ? 'atypismStr fw'
                    : ''
                "
              >
                {{ row.source === 1 ? '商家ERP：' : '商家：' }}{{ row.spec }}
              </div>
            </template>
          </el-table-column>

          <el-table-column prop="productUnit" label="单位">
            <template slot-scope="{ row }">
              <div v-show="!Boolean(row.productUnitOK)" :class="filterType != '1' ? 'atypismStr' : ''">
                单位不规范
              </div>
              <template v-if="row.productUnit && row.productUnitStandard">
                <template v-if="row.productUnit !== row.productUnitStandard">
                  <div>
                    {{ row.productUnitStandard }}
                    <i
                      v-if="activeType === '0' && filterType != 3"
                      class="el-icon-edit-outline"
                      style="color: #4184d5; font-size: 16px"
                      @click="unitModifyBtn(row)"
                    ></i>
                  </div>
                  <div v-if="row.source === 1" :class="filterType != '1' ? 'atypismStr' : ''">
                    商家ERP：{{ row.productUnit }}
                  </div>
                  <div v-else :class="filterType != '1' ? 'atypismStr' : ''">
                    商家：{{ row.productUnit }}
                  </div>
                </template>
                <template v-else>
                  <div>
                    {{ row.productUnit }}
                    <i
                      v-if="activeType === '0' && filterType != 3"
                      class="el-icon-edit-outline"
                      style="color: #4184d5; font-size: 16px"
                      @click="unitModifyBtn(row)"
                    ></i>
                  </div>
                </template>
              </template>
              <template v-else>
                <div>
                  {{ row.productUnit || row.productUnitStandard }}
                  <i
                    v-if="activeType === '0'"
                    class="el-icon-edit-outline"
                    style="color: #4184d5; font-size: 16px"
                    @click="unitModifyBtn(row)"
                  ></i>
                </div>
              </template>
            </template>
          </el-table-column>
          <el-table-column label="价格">
            <template slot-scope="scope">
              <p>单体采购价：{{ scope.row.fob }}</p>
              <p>连锁采购价：{{ scope.row.chainPrice }}</p>
              <p>底价：{{ scope.row.basePrice }}</p>
            </template>
          </el-table-column>
          <el-table-column prop="vailableQyt" label="库存" />
          <el-table-column label="匹配ID">
            <template slot-scope="scope">
              <span
                v-if="scope.row.matchResult === 1"
                class="colorB"
                @click="chooseGoods(scope.row, scope.row.matchedStandardIds)"
              >
                {{
                  scope.row.standardProductId ? scope.row.standardProductId : ''
                }}
                <i
                  v-if="activeType === '0' && filterType != 3"
                  class="el-icon-edit-outline"
                  style="color: #4184d5; font-size: 16px"
                  @click="chooseGoods(scope.row, scope.row.matchedStandardIds)"
                ></i>
              </span>
              <span
                v-else-if="scope.row.matchResult === 4"
                class="colorB"
                @click="chooseGoods(scope.row)"
                >点击选择</span
              >
              <div v-else-if="scope.row.matchResult === 2">商品超经营范围</div>
              <div v-else-if="scope.row.matchResult === 6">没有销售区域”</div>
              <!-- <span v-else-if="scope.row.matchResult === 2">商品经营范围不在企业经营范围内</span> -->
              <span v-else-if="scope.row.matchResult === 5">暂无销售权限</span>
              <span v-else-if="scope.row.matchResult === 7"
                >暂无经营范围,生产企业限销自产品种</span
              >
              <span v-else-if="scope.row.matchResult === 3"> 匹配失败 </span>
              <div
                v-if="scope.row.matchedStandardIds && scope.row.matchedStandardIds.length > 0 && scope.row.matchResult === 5"
                class="colorB"
                @click="chooseGoods(scope.row)"
                >点击选择</div>
              <p
                v-if="scope.row.showManuallyBindSku && filterType != 3"
                style="color: #4183d5; cursor: pointer"
                @click="handleManualBindingSku(scope.row)"
              >
                手动绑定标品
              </p>
              <div>
                  <span v-if="scope.row.disableType" style="color: #ff0000;"">{{scope.row.disableTypeName + ':' + scope.row.disableNote}}</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="是否有图">
            <template slot-scope="scope">
              <!--              <span>{{ scope.row.hasImg ? '有' : '无' }}</span>-->
              <el-image
                style="width: 80px; height: 73px"
                v-if="scope.row.hasImg"
                :src="scope.row.fullImageUrl"
                :preview-src-list="[scope.row.fullImageUrl]"
                @click.prevent
              />
            </template>
          </el-table-column>
          <el-table-column
            prop="skuFrom"
            label="是否使用商品库数据"
            v-if="activeType === '1'"
            :key="Math.random()"
          />
          <el-table-column
            label="操作"
            fixed="right"
            v-if="activeType === '0' && filterType != 3"
            :key="Math.random()"
          >
            <template slot-scope="scope">
              <span
                v-if="scope.row.valueOk"
                class="colorB"
                @click="
                  goGoodsDetail(
                    'created',
                    scope.row.businessSpuCategoryCode,
                    scope.row.businessNonDrugCategoryCode,
                    scope.row.id,
                    scope.row.standardProductId
                  )
                "
                >重新创建</span
              >
              <span
                v-else-if="
                  (scope.row.matchResult === 1 && !scope.row.valueOk) ||
                  scope.row.matchResult === 3 ||
                  getCheckProcess(scope.row)
                "
                class="colorB"
                @click="
                  goGoodsDetail(
                    'edit',
                    scope.row.businessSpuCategoryCode,
                    scope.row.businessNonDrugCategoryCode,
                    scope.row.id,
                    scope.row.standardProductId
                  )
                "
                >补充商品信息</span
              >
              <span v-else>/</span>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="page-box">
        <Pagination
          v-show="totalList > 0"
          :total="totalList"
          :page.sync="listQuery.pageNum"
          :limit.sync="listQuery.pageSize"
          @pagination="getList"
          :pageSizes="
            shopConfig.shopPatternCode === 'ybm'
              ? [10, 20, 50, 100]
              : [10, 20, 50, 100, 500]
          "
        />
      </div>
    </div>
    <el-dialog
      :title="templateFrom === 'a' ? '批量匹配标准库' : '批量上传商品'"
      :visible.sync="dialogVisible"
    >
      <div>
        <el-upload
          ref="upload"
          action
          class="upload-box"
          accept=".xls, .xlsx"
          :on-change="handlePreview"
          :on-remove="handleRemove"
          :file-list="fileList"
          :auto-upload="false"
          style="display: inline-block"
        >
          <el-button type="primary" plain size="small">导入excel文件</el-button>
        </el-upload>
        <el-button
          size="small"
          @click="downLoadExcelMode"
          style="margin-left: 10px"
          >下载模板</el-button
        >
        <p>
          提示：
          <!--          <a :href="excelTemplate" download="上传模板.xls">下载模板</a>-->
        </p>
        <p>1、仅支持上传xlsx、xls文件，大小不超过3M,商品数量不超过3000</p>
        <p>2、必须严格按照模板内容填入商品信息，否则可能会出现导入异常</p>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button size="small" @click="dialogVisible = false">取 消</el-button>
        <el-button
          type="primary"
          size="small"
          :disabled="isContent"
          @click="submitUpload(1)"
          >确 定</el-button
        >
      </span>
    </el-dialog>
    <el-dialog
      title="商品导入记录"
      :visible.sync="dialogVisibleGood"
      width="70%"
    >
      <p style="margin-top: 0; color: #979797">
        *记录批量匹配标准库的历史导入记录，支持导出匹配失败的商品信息
      </p>
      <div class="div-dialog">
        <div>
          <el-table
            ref="goodsTable"
            :data="goodsData"
            stripe
            border
            tooltip-effect="dark"
            style="width: 100%"
          >
            <el-table-column label="导入时间" prop="createTime">
              <template slot-scope="scope">
                <span>{{ scope.row.createTime | formatDate }}</span>
              </template>
            </el-table-column>
            <el-table-column label="导入结果">
              <template slot-scope="scope">
                <span v-if="scope.row.status === 1">匹配中</span>
                <span v-else-if="scope.row.status === 2" style="color: #52c41a"
                  >匹配成功</span
                >
                <span v-else-if="scope.row.status === 3">
                  <a
                    style="color: #ff2121"
                    :href="scope.row.url"
                    :download="scope.row.fileName"
                    >匹配失败商品信息</a
                  >
                </span>
                <span v-else>生成文件异常</span>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div class="page-box">
          <Pagination
            v-show="goodsListTotal > 0"
            :total="goodsListTotal"
            :page.sync="goodsList.pageNum"
            :limit.sync="goodsList.pageSize"
            @pagination="seeGoodsHistory"
          />
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button size="small" @click="dialogVisibleGood = false"
          >取 消</el-button
        >
      </span>
    </el-dialog>
    <el-dialog
      title="批量上传商品图片"
      :visible.sync="dialogVisibleImg"
      width="80%"
    >
      <div class="div-dialog">
        <el-upload
          ref="upload"
          action
          class="upload-box-img"
          accept=".zip, .rar"
          :on-change="handlePreview"
          :on-remove="handleRemove"
          :file-list="fileList"
          :auto-upload="false"
        >
          <el-button type="primary" plain size="small">导入压缩文件</el-button>
        </el-upload>
        <el-button type="primary" plain size="small" @click="downloadSample"
          >下载示例</el-button
        >
        <p style="padding-top: 15px">提示：</p>
        <p>1.上传步骤</p>
        <p>
          （1）每个商品的图片和详情图片放在一个文件夹中，文件名为商品的erp编码且在系统中已经存在
        </p>
        <p>（2）新创建一个文件夹，例如，新建文件夹命名为“商品图片”</p>
        <p>（3）把每个商品的文件夹都放到“商品图片”这个文件夹内</p>
        <p>（4）压缩“商品图片”文件夹，上传压缩包</p>
        <p>2.格式要求</p>
        <p>
          （1）商品图片宽高保持1:1，宽高需大于等于800px，小于等于1500px，大小不超过2M。商品详情图片宽度大于等于800px，小于等于1500px，高小于等于1500px，大小不超过2M。格式支持jpeg、png、jpg、gif
        </p>
        <p>
          （2）文件夹内商品图片命名为“商品图片-1”（最多上传5张，分别标明1、2、3、4、5），详情图片的命名为“详情-1”（最多上传5张，分别标明1、2、3、4、5）
        </p>
        <p>（3）仅支持zip格式的压缩包，大小最大不多于50mb，商品最多100个</p>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button size="small" @click="dialogVisibleImg = false"
          >取 消</el-button
        >
        <el-button
          type="primary"
          size="small"
          :disabled="isContent"
          @click="submitUpload(2)"
          >确 定</el-button
        >
      </span>
    </el-dialog>
    <el-dialog
      title="选择商品分类"
      :visible.sync="dialogVisibleShop"
      width="50%"
    >
      <div class="div-dialog">
        <el-form
          ref="shopClass"
          :model="formClass"
          size="small"
          label-position="right"
          :rules="classRules"
        >
          <el-form-item label="商品大类" prop="spuCategoryCode" label-width="100px">
            <el-select
              v-model="formClass.spuCategoryCode"
              clearable
              placeholder="请选择商品大类"
            >
              <el-option label="普通药品" :value="1"/>
              <el-option label="中药" :value="2"/>
              <el-option label="医疗器械" :value="3"/>
              <el-option label="非药" :value="4"/>
            </el-select>
          </el-form-item>
          <el-form-item v-if="formClass.spuCategoryCode == 4" label="非药细项" :prop="formClass.spuCategoryCode == 4 ? 'nonDrugCategoryCode' : ''" label-width="100px">
            <el-select
              v-model="formClass.nonDrugCategoryCode"
              placeholder="请选择非药细项"
              clearable
            >
              <el-option label="个人护理品" :value="100001"/>
              <el-option label="日用百货" :value="100007"/>
              <el-option label="普通食品" :value="100008"/>
              <el-option label="保健食品" :value="100003"/>
            </el-select>
          </el-form-item>
          <!-- <el-form-item label="三级分类">
            <el-select
              v-model="formClass.third"
              placeholder="请选择商品分类"
              @change="chooseClass($event, 'third')"
            >
              <el-option
                v-for="(item, index) in thirdList"
                :key="index"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="四级分类">
            <el-select v-model="formClass.four" placeholder="请选择商品分类">
              <el-option
                v-for="(item, index) in fourList"
                :key="index"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
          </el-form-item> -->
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button size="small" @click="dialogVisibleShop = false"
          >取 消</el-button
        >
        <el-button
          type="primary"
          size="small"
          @click="submitClass()"
          :disabled="formClass.spuCategoryCode === 5"
          >确 定</el-button
        >
      </span>
    </el-dialog>
    <chooseList ref="chooseList" @resetList="getList" />
    <manualBindingSkuDialog ref="manualBindingSkuDialog" @resetList="getList" />

    <ModifyUnitDialog
      ref="modifyUnitDialog"
      v-if="modifyUnitDialogVisible"
      :modifyUnitDialog.sync="modifyUnitDialogVisible"
      :unitListOptions="unitsStrList"
      :productData="modifyUnitDialogData"
      @modifyConfirm="searchList"
    />

    <export-tip
      :change-export="changeExport"
      @handleExoprClose="handleExoprClose"
      @handleChangeExport="handleChangeExport"
    />
    <el-dialog
      title="信息"
      width="400px"
      :visible.sync="visible"
      :modal-append-to-body="false"
      :close-on-click-modal="false"
    >
      提交成功，可在"商品列表"-
      <span>"待上架"</span>查看。
      <el-button
        style="color: #4184d5"
        size="small"
        type="text"
        @click="toProduct"
        >点击查看</el-button
      >

      <span slot="footer">
        <el-button
          size="medium"
          style="margin-left: 20px"
          type="primary"
          @click="onCancel"
          >关闭</el-button
        >
      </span>
    </el-dialog>
    <!-- <el-dialog title="选择商品" :visible.sync="dialogFormVisible" width="80%">
      <div class="div-dialog">
        <div v-if="checkedName" class="selectBox">
          您当前选择的是：
          <span>{{checkedName}}</span>
        </div>
        <p class="goods-tip">*以下列表中，请选择商品信息一致的商品(规格数字一样描述不一样的，可视为一致)，如果无匹配的商品，请从新增上架途径单独上架</p>
        <chooseListNew :prop-data="goodsListNew" @chooseGoods="chooseGoodsNew"></chooseListNew>
      </div>
    </el-dialog>-->
    <ResultTips
      v-if="showTips"
      :data-count="differentStandardCount"
      @handleClose="handleClose"
      @tipsSearch="searchFn"
    />
  </div>
</template>

<script>
import exportTip from '@/views/other/components/exportTip'
import { mapState } from 'vuex'
import {
  getRecoverValid,
  getMatchResultType,
  getQueryErpSku,
  getQueryProductList,
  getQueryErpSkuCount,
  queryErpSkuCount,
  uploadFile,
  getExport,
  pushListGoods,
  getMatchCount,
  getResetDataList,
  getShopClassList,
  isResetStatus,
  getGoodsExportHistory,
  deleteListGoods,
  resetCode,
  getUnitsList,
  apiChangeCode,
  apiDownloadTemplate,
  categoryTree
} from '../../api/product'
import Pagination from '../../components/Pagination/index.vue'
import chooseList from './components/chooseGoodsList'
import manualBindingSkuDialog from './components/manualBindingSkuDialog'
import ModifyUnitDialog from './components/modifyUnitDialog'
import chooseListNew from './components/chooseList'
import ResultTips from './components/resultTips'
import { erpFirstCategoryId } from './config'

export default {
  name: 'BatchGoods',
  components: {
    Pagination,
    chooseList,
    ModifyUnitDialog,
    exportTip,
    chooseListNew,
    ResultTips,
    manualBindingSkuDialog
  },
  filters: {
    formatDate(value) {
      const date = new Date(value)
      const y = date.getFullYear()
      let MM = date.getMonth() + 1
      MM = MM < 10 ? `0${MM}` : MM
      let d = date.getDate()
      d = d < 10 ? `0${d}` : d
      let h = date.getHours()
      h = h < 10 ? `0${h}` : h
      let m = date.getMinutes()
      m = m < 10 ? `0${m}` : m
      let s = date.getSeconds()
      s = s < 10 ? `0${s}` : s
      return `${y}-${MM}-${d} ${h}:${m}:${s}`
    }
  },
  data() {
    return {
      searchNum: '',
      filterType: '0',
      recoverValidParams: [],
      successNum: 0, // 可一键发布数
      errorNum: 0, // 存在问题数
      noImgNum: 0, // 无图商品数
      differentStandardCount: 0, // 与标准库信息不一至数（商品名）
      differentStandardCodeCount: 0, // 与标准库信息不一至数（69码）
      matchMoreCount: 0, // 匹配到多个的商品数
      lossInfoCount: 0, // 信息不完整商品数
      outOfBusinessCount: 0, // 超经营范围商品数
      listQuery: {
        pageSize: 10,
        pageNum: 1,
        productName: '',
        erpCode: '',
        barcode: '',
        manufacturer: '',
        code: '',
        hasStock: -1,
        source: -1,
        hasImg: -1,
        canPublish: -1,
        approvalNumber: '',
        infoEqualsStandard: 0,
        spec: '',
        publishMatchStatus: '',
        valid: ''
      },
      dialogVisible: false,
      dialogVisibleImg: false,
      dialogVisibleShop: false,
      dialogVisibleGood: false,
      totalList: 0,
      canPublishList: [],
      tableData: [],
      fileList: [],
      uploadFileData: '',
      isReset: false,
      isContent: false,
      formClass: {
        spuCategoryCode: '',
        nonDrugCategoryCode: '',
        erpId: '',
        standardProductId: ''
        /* third: '',
        four: '',
        erpId: '',
        standardProductId: '' */
      },
      classAllList: {},
      firstList: [],
      secondList: [],
      thirdList: [],
      fourList: [],
      classRules: {
        spuCategoryCode: [
          { required: true, message: '请选择商品大类', trigger: 'change' }
        ],
        nonDrugCategoryCode: [
          { required: true, message: '请选择非药细项', trigger: 'change' }
        ],
      },
      excelTemplate: '',
      templateFrom: '',
      goodsData: [],
      goodsList: {
        pageNum: 1,
        pageSize: 10
      },
      goodsListTotal: 0,
      initiallistQuery: {},
      unitsStrList: [],
      modifyUnitDialogVisible: false,
      modifyUnitDialogData: {},
      changeExport: false,
      showMore: false,
      checkedName: '',
      itemData: {},
      dialogFormVisible: false,
      visible: false,
      isSupplement: false,
      activeType: '0', // 0-未发布，1-已发布
      showTips: false, // 展示提醒弹框
      countObj: {}, // tab页签数量
      productCategoryLevelOptions: []
    }
  },
  computed: { ...mapState('app', ['shopConfig']) },
  created() {
    // this.getCategoryTree()
    this.initiallistQuery = JSON.parse(JSON.stringify(this.listQuery))
    const that = this
    getMatchResultType().then((res) => {
      if (res.code === 0) {
        that.canPublishList = res.data
      } else {
        that.canPublishList = []
      }
    })
    isResetStatus().then((res) => {
      if (res.code === 0) {
        that.isReset = res.data
      }
    })
    this.getUnitsList()
    this.getList()
  },
  activated() {
    this.activate()
    this.getMatchCount()
    // this.getQueryErpSkuCount()
  },
  methods: {
    // 恢复已删除 传数据id
    handleRecoverDelete() {
      if (!this.recoverValidParams.length) {
        this.$message({
          type: 'warning',
          message: '请选择一条商品数据',
          offset: 60
        })
        return
      }
      getRecoverValid({
        ids: this.recoverValidParams.join(',')
      })
        .then((res) => {
          if (res.code == 0 && res.data == '恢复成功') {
            this.$message({
              type: 'success',
              message: res.data,
              offset: 60
            })
            this.getList()
          }
        })
        .finally(() => {
          this.recoverValidParams = []
        })
    },
    packageConvertToStandard(str) {
      let result = str // 存储转换后的结果
      let packageUnits = [
        's',
        'S',
        't',
        'T',
        '片',
        '粒',
        'w',
        'W',
        '丸',
        '枚',
        '袋',
        'd',
        '包',
        '瓶',
        '盒',
        '小盒',
        '罐',
        '掀',
        '喷',
        'IU',
        'iu',
        '国际单位'
      ] // 支持的包装单位
      let standardUnit = '个' // 标准单位
      for (let i = 0; i < packageUnits.length; i++) {
        let unit = packageUnits[i]
        let regex = new RegExp('\\d+(\\.\\d+)?' + unit, 'g') // 匹配数字+单位的正则表达式
        let matchArr = result.match(regex) // 查找匹配的字符串
        // console.log(99999999, matchArr);
        if (matchArr && matchArr.length > 0) {
          let oldValue = unit // 获取对应的单位
          let newValue = standardUnit // 转换为标准单位
          let newStr = matchArr[0].replace(oldValue, newValue) // 拼接处理后的字符串
          const reg = new RegExp(matchArr[0], 'g')
          // console.log(55555555, reg);
          result = result.replace(reg, newStr) // 将原字符串中的旧字符串替换为新字符串
        }
      }
      return result
    },
    unitConvertToNum(str) {
      let result = str // 存储转换后的结果
      let weightUnits = [
        '克',
        'G',
        'g',
        'kg',
        'KG',
        '千克',
        '公斤',
        '斤',
        '吨',
        '毫克',
        'mg',
        'MG',
        '微克',
        'μg',
        'ug'
      ] // 支持的重量单位
      let weightValue = [
        1, 1, 1, 1000, 1000, 1000, 1000, 500, 1000000, 0.001, 0.001, 0.001,
        0.000001, 0.000001, 0.000001
      ] // 单位对应的转换量，单位为g
      let volumeUnits = ['毫升', 'ML', 'Ml', 'mL', 'ml', '升', 'L', 'l'] // 支持的体积单位
      let volumeValue = [1, 1, 1, 1, 1, 1000, 1000, 1000] // 单位对应的转换量，单位为ml
      let lengthUnits = [
        '厘米',
        'CM',
        'Cm',
        'cM',
        'cm',
        '米',
        'M',
        'm',
        '毫米',
        'mm',
        'MM',
        'mM',
        'Mm',
        '分米',
        'dm',
        'DM',
        'Dm',
        'dM'
      ] // 支持的长度单位
      let lengthValue = [
        1, 1, 1, 1, 1, 100, 100, 100, 0.1, 0.1, 0.1, 0.1, 0.1, 10, 10, 10, 10,
        10
      ] // 单位对应的转换量，单位为cm

      for (let i = 0; i < weightUnits.length; i++) {
        // 处理重量单位
        let unit = weightUnits[i]
        let regex = new RegExp('\\d+(\\.\\d+)?' + unit, 'g') // 匹配数字+单位的正则表达式
        let matchArr = str.match(regex) // 查找匹配的字符串
        if (matchArr && matchArr.length > 0) {
          for (let j = 0; j < matchArr.length; j++) {
            let oldStr = matchArr[j]
            let num = parseFloat(oldStr.match(/\d+(?:\.\d+)?/)[0]) // 提取数字
            let oldValue = weightValue[weightUnits.indexOf(unit)] // 获取对应的转换量
            // console.log(9999999999, num, oldValue)
            let newValue = num * oldValue // 转换后的数值，单位为g
            let newStr = newValue + 'g' // 拼接处理后的字符串
            result = result.replace(oldStr, newStr) // 将原字符串中的旧字符串替换为新字符串
          }
        }
      }
      for (let i = 0; i < volumeUnits.length; i++) {
        // 处理体积单位
        let unit = volumeUnits[i]
        let regex = new RegExp('\\d+(\\.\\d+)?' + unit, 'g') // 匹配数字+单位的正则表达式
        let matchArr = result.match(regex) // 查找匹配的字符串
        if (matchArr && matchArr.length > 0) {
          for (let j = 0; j < matchArr.length; j++) {
            let oldStr = matchArr[j]
            // console.log(2222222, oldStr);
            let num = parseFloat(oldStr.match(/\d+(?:\.\d+)?/)[0]) // 提取数字
            let oldValue = volumeValue[volumeUnits.indexOf(unit)] // 获取对应的转换量
            let newValue = num * oldValue // 转换后的数值，单位为ml
            let newStr = newValue + 'ml' // 拼接处理后的字符串
            // console.log(9999999999, newStr)
            result = result.replace(oldStr, newStr) // 将原字符串中的旧字符串替换为新字符串
          }
        }
      }
      for (let i = 0; i < lengthUnits.length; i++) {
        // 处理长度单位
        let unit = lengthUnits[i]
        let regex = new RegExp('\\d+(\\.\\d+)?' + unit, 'g') // 匹配数字+单位的正则表达式
        let matchArr = result.match(regex) // 查找匹配的字符串
        // console.log(777777, result, unit, result[result.indexOf(unit) + 1]);
        if (
          matchArr &&
          matchArr.length > 0 &&
          result[result.indexOf(unit) + 1] !== unit
        ) {
          for (let j = 0; j < matchArr.length; j++) {
            let oldStr = matchArr[j]
            let num = parseFloat(oldStr.match(/\d+(?:\.\d+)?/)[0]) // 提取数字
            let oldValue = lengthValue[lengthUnits.indexOf(unit)] // 获取对应的转换量
            let newValue = num * oldValue // 转换后的数值，单位为cm
            let newStr = newValue + 'cm' // 拼接处理后的字符串
            result = result.replace(oldStr, newStr) // 将原字符串中的旧字符串替换为新字符串
          }
        }
      }
      return result
    },
    compareStrings(str, str1) {
      // console.log(66666666, str, str1);
      let newStr = this.unitConvertToNum(str) // 将字符串str中的单位转换为统一单位
      let newStr1 = this.unitConvertToNum(str1)
      // console.log(1212121212, newStr, newStr1);
      newStr = this.packageConvertToStandard(newStr) // 将字符串str中的包装单位转换为标准单位
      newStr1 = this.packageConvertToStandard(newStr1)
      return newStr === newStr1
    },
    removeSpecialCharactersAndToLower(str) {
      if (!str) return str
      // 定义需要去除的字符集合
      const specialCharactersRegex =
        /[\s（）()"'：:；，\-+\/\\&|？#^*￥$@。~,{}\[\]【】]/g
      // 去除特殊字符
      const withoutSpecialCharacters = str.replace(specialCharactersRegex, '')
      // 将所有英文字母转换为小写
      const lowerCaseString = withoutSpecialCharacters.toLowerCase()
      // 返回结果
      return lowerCaseString
    },
    handleCascaderVal(value) {
      console.log('value', value[0])
      this.listQuery.businessFirstCategory = value[0] || ''
      this.listQuery.businessSecondCategory = value[1] || ''
    },
    deleteChildren(obj) {
      for (let i = 0; i < obj.length; i++) {
        const children = obj[i].children
        if (children) {
          for (let j = 0; j < children.length; j++) {
            if (children[j].children) {
              delete children[j].children
            } else {
              this.deleteChildren([children[j]])
            }
          }
        }
      }
      return obj
    },
    async getCategoryTree() {
      try {
        const res = await categoryTree()
        if (res.code === 0) {
          const a = this.deleteChildren(res.data.children)
          console.log('a', a)
          this.productCategoryLevelOptions = a
        }
      } catch (e) {
        console.log(e)
      }
    },
    handleManualBindingSku(row) {
      this.$refs.manualBindingSkuDialog.handleParamsSearch(
        row.id,
        row.approvalNumber,
        row.commonName,
        row
      )
    },
    activate() {
      const query = this.$route.query
      if (query && Object.keys(query).length > 0) {
        this.visible = false
        if (Object.prototype.hasOwnProperty.call(query, 'from')) {
          if (query.from === 'toBatchGoods') {
            Object.assign(this.$data, this.$options.data())
            this.initiallistQuery = JSON.parse(JSON.stringify(this.listQuery))
          }
        }
      }
      const that = this
      getMatchResultType().then((res) => {
        if (res.code === 0) {
          that.canPublishList = res.data
        } else {
          that.canPublishList = []
        }
      })
      isResetStatus().then((res) => {
        if (res.code === 0) {
          that.isReset = res.data
        }
      })
      this.getUnitsList()
      this.getMatchCount()
      this.getList()
    },
    handleClose() {
      this.showTips = false
    },
    chooseGoodsNew(data) {
      this.checkedName = data.showName
        ? data.showName + '/' + data.spec + '/' + data.manufacturer
        : ''
      this.itemData = data ? data : {}
    },
    getChooseData(ids, chooseId) {
      this.dialogFormVisible = true
      if (chooseId) {
        this.chooseId = chooseId
      }
      this.sendIds = ids
      this.sendSkuId = ''
      this.chooseIndex = ''
      getMathList({ id: ids })
        .then((res) => {
          if (res.code === 0) {
            // this.goodsDetail = res.data;
          } else {
            this.$message({
              type: 'error',
              message: res.message,
              offset: 60
            })
            // this.goodsDetail = [];
          }
        })
        .catch(() => {
          // this.goodsDetail = [];
        })
    },
    async getUnitsList() {
      const res = await getUnitsList({})
      if (res && res.code === 0) {
        this.unitsStrList = res.data
      }
    },
    getMatchCount() {
      getMatchCount().then((res) => {
        if (res.code === 0) {
          if (res.data) {
            console.log(res)
            const {
              okCount,
              errorCount,
              noPicCount,
              differentStandardCount,
              differentStandardCodeCount,
              matchMoreCount,
              lossInfoCount,
              outOfBusinessCount
            } = res.data
            this.successNum = okCount || 0
            this.errorNum = errorCount || 0
            this.noImgNum = noPicCount || 0
            this.differentStandardCount = differentStandardCount || 0
            this.differentStandardCodeCount = differentStandardCodeCount || 0
            this.matchMoreCount = matchMoreCount || 0
            this.lossInfoCount = lossInfoCount || 0
            this.outOfBusinessCount = outOfBusinessCount || 0
            if (this.differentStandardCount > 0) {
              this.showTips = true
            }
          }
        }
      })
    },
    selectInit(row) {
      if (row.canPublishStatus === 1) {
        return true
      }
      return false
    },
    getQueryErpSkuCount() {
      const params = {
        ...this.listQuery,
        publish: this.activeType
      }
      if (this.searchNum != 2) {
        delete params.publishMatchStatus
        delete params.valid
      }
      queryErpSkuCount(params).then((res) => {
        this.countObj = {
          unPublishedCount: res.data.unPublishedCount || 0,
          publishedCount: res.data.publishedCount || 0,
          unPublishedNonValidCount: res.data.unPublishedNonValidCount || 0,
          unPublishedAccurateMatchCount:
            res.data.unPublishedAccurateMatchCount || 0,
          unPublishedFuzzyMatchCount: res.data.unPublishedFuzzyMatchCount || 0
        }
      })
    },
    handleFilterChangeTab() {
      this.listQuery.canPublish = -1
      switch (this.filterType) {
        case '0':
          this.listQuery.spec = ''
          this.listQuery.publishMatchStatus = ''
          this.listQuery.valid = ''
          break
        case '1':
          this.listQuery.publishMatchStatus = 1
          this.listQuery.valid = 1
          break
        case '2':
          this.listQuery.publishMatchStatus = 0
          this.listQuery.valid = 1
          break
        case '3':
          this.listQuery.valid = 0
          delete this.listQuery.publishMatchStatus
          break
      }
      this.getList()
    },
    changeTab() {
      if (this.activeType == 1) {
        this.listQuery.spec = ''
        this.listQuery.publishMatchStatus = ''
        this.listQuery.valid = ''
      }
      this.getList()
    },
    getList(from, type) {
      from && from === 1 ? (this.listQuery.hasImg = 2) : ''
      const params = {
        ...this.listQuery,
        publish: type || this.activeType
      }
      // const selected = this.$refs.productCategoryLevel.getCheckedNodes(true)[0]
      // if (selected && selected.data) {
      //   params.categoryLevel = selected.data.level
      //   params.categoryId = selected.data.id
      // }
      const load = this.loadingFun()
      getQueryErpSku(params).then((res) => {
        console.log('ressssssssss', res)
        load.close()
        if (res.code === 0) {
          if (res.data.list && res.data.list.length > 0) {
            this.tableData = res.data.list
            console.log('this.tableData:', this.tableData)
            this.totalList = res.data.total
            // if (this.tableData.every(item => item.disableType != 0 && item.disableType != null)) {
            //   let val = 5
            //   this.listQuery.canPublish = val
            // }
          } else {
            this.tableData = []
            this.totalList = 0
          }
          // 未发布无数据且已发布有数据则展示已发布，否则展示未发布的数据
          // if (this.activeType === '0' && this.totalList === 0) {
          //   this.getList(from, '1');
          // } else if (type === '1' && this.totalList > 0) {
          //   this.activeType = '1';
          // }
        } else {
          this.tableData = []
          this.totalList = 0
        }
      })
      this.getQueryErpSkuCount()
    },
    resetList() {
      this.filterType = '0'
      this.clearList()
      this.getList()
    },
    searchList() {
      this.listQuery.infoEqualsStandard = 0
      this.getList()
    },
    handleSelectionChange(arr) {
      for (let index = 0; index < arr.length; index++) {
        const element = arr[index]
        let ck = this.checkDatas(element)
        if (ck === false) {
          return
        }
      }

      if (this.filterType == 3 && arr.length) {
        arr.map((item) => this.recoverValidParams.push(item.id))
      }
    },

    getCheckProcess(element) {
      let result = this.checkDatas(element, false) === false
      return result
    },

    // 检查弹窗
    checkDatas(element, needAlert) {
      if (
        this.checkProductName(
          element.productName,
          'ERP编号：'+element.erpCode+'的商品名称不完整，请先去补充商品信息',
          needAlert
        ) === false
      ) {
        return false
      }
      if (
        this.checkProductName(
          element.commonName,
          'ERP编号：'+element.erpCode+'的通用名称不完整，请先去补充商品信息',
          needAlert
        ) === false
      ) {
        return false
      }
      if (
        this.checkProductName(
          element.manufacturer,
          'ERP编号：'+element.erpCode+'的生产厂家不完整，请先去补充商品信息',
          needAlert
        ) === false
      ) {
        return false
      }
      if (
        this.checkProductName(
          element.spec,
          'ERP编号：'+element.erpCode+'的规格不完整，请先去补充商品信息',
          needAlert
        ) === false
      ) {
        return false
      }
      return true
    },

    //检查名字是否只有*或/或-或.
    checkProductName(productName, warningMSG, needAlert = true) {
      if (!productName) {
        return true
      }
      var pattern = /^[*/\-.]+$/
      if (pattern.test(productName)) {
        if (needAlert) {
          this.$message.warning(warningMSG)
          this.$refs.multipleTable.clearSelection()
        }
        return false
      }
      return true
    },

    chooseGoods(row, chooseid) {
      if (this.activeType === '1' || this.filterType == 3) {
        return
      }
      console.log('row, chooseid:', row)
      this.$refs.chooseList.getChooseData(row, chooseid)
    },
    goGoodsDetail(str, spuCategoryCode, nonDrugCategoryCode, erpId, standardProductId) {
      this.formClass = {
        spuCategoryCode: '',
        nonDrugCategoryCode: '',
        erpId: '',
        standardProductId: ''
      }
      this.isSupplement = false
      const key = spuCategoryCode === 4 ? `${spuCategoryCode}${nonDrugCategoryCode}` : `${spuCategoryCode}`
      const first = erpFirstCategoryId[key]
      if (str === 'created') {
        this.formClass.erpId = erpId
        this.isSupplement = false
        this.getShopClass(spuCategoryCode, nonDrugCategoryCode)
        sessionStorage.removeItem('createProduct')
        sessionStorage.setItem('createProduct', 'true')
      } else if (!first) {
        this.formClass.erpId = erpId
        this.formClass.standardProductId = standardProductId
        this.isSupplement = true
        this.getShopClass()
      } else {
        // const url = `/product/toNewAddPage?erpFirstCategoryId=${first}&erpSecondCategoryId=${second}&erpThirdCategoryId=${third}&erpFourthCategoryId=${four}&erpSkuId=${erpId}`
        // const path = process.env.VUE_APP_BASE_API + '/product/toNewAddPage'
        // window.openTab(path, {
        //   erpFirstCategoryId: first,
        //   erpSecondCategoryId: second,
        //   erpThirdCategoryId: third,
        //   erpFourthCategoryId: four,
        //   erpSkuId: erpId
        // }, '补充商品详情')

        if (first) {
          const path = '/product/detailsEdit'
          const obj = {
            erpFirstCategoryId: first,
            erpSecondCategoryId: '',
            erpThirdCategoryId: '',
            erpFourthCategoryId: '',
            erpSkuId: erpId,
            spuCategoryCode: spuCategoryCode,
            nonDrugCategoryCode: nonDrugCategoryCode,
            standardProductId: standardProductId,
            isEdit: 2,
            from: 'supplement'
          }
          window.openTab(path, obj)
        } else {
          this.$message.error('查询参数异常')
        }
      }
    },
    getShopClass(spuCategoryCode, nonDrugCategoryCode) {
      getShopClassList().then((res) => {
        if (res.code === 0) {
          this.classAllList = res.data
          this.firstList = res.data.root
          this.dialogVisibleShop = true
          if (spuCategoryCode) {
            this.formClass.spuCategoryCode = Number(spuCategoryCode)
          }
          if (nonDrugCategoryCode) {
            this.formClass.nonDrugCategoryCode = Number(nonDrugCategoryCode)
          }
        } else {
          // this.$message.warning(res.message)
          this.$message({
            type: 'warning',
            message: res.message,
            offset: 60
          })
        }
      })
    },
    chooseClass(value, listStr) {
      switch (listStr) {
        case 'first':
          this.secondList = []
          this.formClass.second = ''
          this.thirdList = []
          this.formClass.third = ''
          this.fourList = []
          this.formClass.four = ''
          this.secondList = this.classAllList[value]
          break
        case 'second':
          this.thirdList = []
          this.formClass.third = ''
          this.fourList = []
          this.formClass.four = ''
          this.thirdList = this.classAllList[value]
          break
        case 'third':
          this.fourList = []
          this.formClass.four = ''
          this.fourList = this.classAllList[value]
          break
        default:
          break
      }
    },
    submitClass() {
      this.$refs.shopClass.validate((valid) => {
        if (valid) {
          // const url = `/product/toNewAddPage?erpFirstCategoryId=${this.formClass.first}&erpSecondCategoryId=${this.formClass.second}&erpThirdCategoryId=${this.formClass.third}&erpFourthCategoryId=${this.formClass.four}&erpSkuId=${this.formClass.erpId}`
          // const path = process.env.VUE_APP_BASE_API + '/product/toNewAddPage'
          // window.openTab(path, {
          //   erpFirstCategoryId: this.formClass.first,
          //   erpSecondCategoryId: this.formClass.second,
          //   erpThirdCategoryId: this.formClass.third,
          //   erpFourthCategoryId: this.formClass.four,
          //   erpSkuId: this.formClass.erpId,
          //   fromErp: true,
          // }, '补充商品详情')
          this.dialogVisibleShop = false;
          const key = this.formClass.spuCategoryCode === 4 ? `${this.formClass.spuCategoryCode}${this.formClass.nonDrugCategoryCode}` : `${this.formClass.spuCategoryCode}`
          const first = erpFirstCategoryId[key]
          const path = '/product/detailsEdit'
          const obj = {
            erpFirstCategoryId: first,
            spuCategoryCode: this.formClass.spuCategoryCode,
            nonDrugCategoryCode: this.formClass.nonDrugCategoryCode,
            erpSkuId: this.formClass.erpId,
            standardProductId: this.formClass.standardProductId,
            isEdit: 2,
            from: this.isSupplement ? 'supplement' : 'create',
            prop: true
          }
          window.openTab(path, obj)
        }
      })
    },
    showGoods(from) {
      this.templateFrom = from
      // const env = process.env.NODE_ENV
      // let fileName = '';
      // if (from === 'a') {
      //   this.excelTemplate = 'https://upload.ybm100.com/pop/temp/%E6%89%B9%E9%87%8F%E5%8C%B9%E9%85%8D%E6%A0%87%E5%87%86%E5%BA%93%E5%95%86%E5%93%81%E6%A8%A1%E7%89%88.xlsx'
      //   if (env === 'development' || env === 'test') {
      //     this.excelTemplate = 'https://upload.test.ybm100.com/pop/temp/%E6%89%B9%E9%87%8F%E5%8C%B9%E9%85%8D%E6%A0%87%E5%87%86%E5%BA%93%E5%95%86%E5%93%81%E6%A8%A1%E7%89%88.xlsx'
      //   }
      //   fileName = '批量匹配标准库商品模版.xlsx';
      // } else {
      //   this.excelTemplate = 'https://upload.ybm100.com/pop/temp/%E6%89%B9%E9%87%8F%E5%88%9B%E5%BB%BA%E5%95%86%E5%93%81%E6%A8%A1%E7%89%88.xlsx'
      //   if (env === 'development' || env === 'test') {
      //     this.excelTemplate = 'https://upload.test.ybm100.com/pop/temp/%E6%89%B9%E9%87%8F%E5%88%9B%E5%BB%BA%E5%95%86%E5%93%81%E6%A8%A1%E7%89%88.xlsx'
      //   }
      //   fileName = '批量创建商品模版.xlsx';
      // }
      // const params = {
      //   fileName: fileName,
      //   priceType: this.shopConfig.priceType,
      // };
      // apiDownloadTemplate(params).then((res) => {
      //   this.util.exportExcel(res, '导出文件.xls');
      //   if (res.code === 0) {
      //     this.excelTemplate = res.data;
      //   }
      // })
      this.dialogVisible = true
      this.isContent = true
      this.uploadFileData = ''
      this.fileList = []
    },
    downLoadExcelMode() {
      // window.open(this.excelTemplate)
      let fileName = ''
      if (this.templateFrom === 'a') {
        fileName = '批量匹配标准库商品模版.xlsx'
      } else {
        fileName = '批量创建商品模版.xlsx'
      }
      const params = {
        fileName: fileName,
        priceType: this.shopConfig.priceType
      }
      apiDownloadTemplate(params)
        .then((res) => {
          if (res.code && res.code !== 0) {
            this.$message.error(res.message || '请求异常')
          } else {
            this.util.exportExcel(res, fileName)
          }
        })
        .catch((err) => {
          console.log(err)
        })
    },
    showGoodsImg() {
      this.dialogVisibleImg = true
      this.isContent = true
      this.uploadFileData = ''
      this.fileList = []
    },
    handlePreview(file, fileList) {
      if (file.status === 'ready') {
        // 已上传文件列表如果存在 2 条记录，移除第一条，实现替换效果
        if (fileList.length === 2) {
          fileList.shift()
        }
      }
      this.uploadFileData = file
      this.isContent = false
    },
    handleRemove() {
      this.uploadFileData = ''
      this.isContent = true
      this.fileList = []
    },
    /* 重新匹配 */
    resetDataList() {
      const load = this.loadingFun()
      getResetDataList().then((res) => {
        load.close()
        if (res.code === 0) {
          this.getMatchCount()
          this.getList()
        } else {
          // this.$message.error(res.message)
          this.$message({
            type: 'error',
            message: res.message,
            offset: 60
          })
        }
      })
    },
    /* 上传文件提交 */
    submitUpload(from) {
      if (this.uploadFileData !== '') {
        const load = this.loadingFun()
        uploadFile(from, this.templateFrom, this.uploadFileData).then((res) => {
          load.close()
          if (res.code === 0) {
            let content = ''
            if (from === 1) {
              this.dialogVisible = false
              if (this.templateFrom === 'b') {
                if (res.data.error > 0) {
                  content = `<p>${res.data.success}个商品发布成功，商品状态更新为待审核，${res.data.error}个商品导入失败，原因见错误文件<br><a style="color: #ff0021" href="${res.data.errorFileUrl}" download="${res.data.errorFileName}">${res.data.errorFileName}</a></p>`
                } else {
                  content = `<p>${res.data.success}个商品发布成功，商品状态更新为待审核，${res.data.error}个商品导入失败</p>`
                }
                this.$confirm(content, '上传文件反馈', {
                  customClass: 'my-confirm',
                  dangerouslyUseHTMLString: true,
                  showCancelButton: false
                }).then(() => {
                  this.getList()
                })
              } else if (res.data.success > 0) {
                let con = ''
                if (res.data.error > 0) {
                  con = `<p><span style="color: #ff2121">${res.data.error}个商品导入失败，原因见错误文件:</span><br><a style="color: #ff0021" href="${res.data.errorFileUrl}" download="${res.data.errorFileName}">${res.data.errorFileName}</a></p><p><span style="color: #00c675">${res.data.success}个商品导入成功</span>，标准库匹配预计时长3-5分钟，请到商品导入记录查询结果。
匹配完成后，请刷新批量发布商品页面</p>`
                } else {
                  con = `<p><span style="color: #00c675">${res.data.success}个商品导入成功</span>，标准库匹配预计时长3-5分钟，请到商品导入记录查询结果。匹配完成后，请刷新批量发布商品页面</p>`
                }
                this.$confirm(con, '上传文件反馈', {
                  customClass: 'my-confirm',
                  confirmButtonText: '打开商品导入记录',
                  cancelButtonText: '关闭',
                  dangerouslyUseHTMLString: true
                })
                  .then(() => {
                    this.seeGoodsHistory(1)
                  })
                  .catch(() => {})
              } else {
                this.$confirm(
                  `<p><span style="color: #ff2121">${res.data.error}个商品导入失败，原因见错误文件:</span><br><a style="color: #ff0021" href="${res.data.errorFileUrl}" download="${res.data.errorFileName}">${res.data.errorFileName}</a></p>`,
                  '上传文件反馈',
                  {
                    customClass: 'my-confirm',
                    confirmButtonText: '打开商品导入记录',
                    cancelButtonText: '关闭',
                    showConfirmButton: false,
                    dangerouslyUseHTMLString: true
                  }
                ).then(() => {})
              }
            } else {
              this.dialogVisibleImg = false
              if (res.data.error > 0) {
                content = `<p>成功上传${res.data.success}个商品的图片，失败${res.data.error}个，原因见错误文件<br><a style="color: #ff0021" href="${res.data.errorFileUrl}" download="${res.data.errorFileName}">${res.data.errorFileName}</a></p>`
              } else {
                content = `<p>成功上传${res.data.success}个商品的图片，失败${res.data.error}个</p>`
              }
              this.$confirm(content, '上传文件反馈', {
                customClass: 'my-confirm',
                dangerouslyUseHTMLString: true,
                showCancelButton: false
              }).then(() => {
                this.getList()
              })
            }
          } else {
            // this.$message.error('上传失败')
            this.$message({
              type: 'error',
              message: res.message,
              offset: 60
            })
          }
        })
      }
    },
    /* 导出商品 */
    exportGoods() {
      let idStr = ''
      const sendAry = this.$refs.multipleTable.selection
      if (sendAry.length > 0) {
        sendAry.forEach((item) => {
          idStr += `${item.id},`
        })
      }
      const params = JSON.parse(JSON.stringify(this.listQuery))
      params.ids = idStr
      params.publish = this.activeType
      delete params.pageNum
      delete params.pageSize
      console.log('params', params)
      getExport(params)
        .then((res) => {
          console.log('res', res)
          // const blob = new Blob([res]) // 接受文档流
          // if ('msSaveOrOpenBlob' in navigator) {
          //   // IE下的导出
          //   window.navigator.msSaveOrOpenBlob(blob, '导出文件.xlsx') // 设置导出的文件名
          // } else {
          //   // 非ie下的导出
          //   const a = document.createElement('a')

          //   const url = window.URL.createObjectURL(blob)
          //   const filename = '导出文件.xlsx' // 设置导出的文件名
          //   a.href = url
          //   a.download = filename
          //   document.body.appendChild(a)
          //   a.click()
          //   window.URL.revokeObjectURL(url)
          //   document.body.removeChild(a)
          // }
          if (res.code !== 0) {
            this.$message.error(res.message)
            return
          }
          this.changeExport = true
        })
        .catch(() => {})
    },
    handleChangeExport(info) {
      this.changeExport = false
      if (info === 'go') {
        const path = '/downloadList'
        window.openTab(path)
        // that.$router.push({ path: '/downloadList' });
      }
    },
    handleExoprClose() {
      this.changeExport = false
    },
    /* 商品导出记录 */
    seeGoodsHistory(from) {
      this.dialogVisibleGood = true
      if (from === 1) {
        this.goodsList.pageNum = 1
        this.goodsList.pageSize = 10
      }
      const params = JSON.parse(JSON.stringify(this.goodsList))
      getGoodsExportHistory(params).then((res) => {
        if (res.code === 0) {
          this.goodsListTotal = res.data.total
          this.goodsData = res.data.list
        }
      })
    },
    /* 一键发布 */
    goodsPush() {
      if (this.filterType == 3) {
        this.$message({
          type: 'warning',
          message: '当前状态下不允许一键发布',
          offset: 60
        })
        return
      }
      const sendAry = this.$refs.multipleTable.selection
      if (sendAry.length > 0) {
        const aty = []
        const hasImgAry = []
        const errorAry = []
        const disaccord = []
        sendAry.forEach((item) => {
          aty.push(item.id)
          item.hasImg ? '' : hasImgAry.push(item.id)
          item.canPublishStatus !== 1 ? errorAry.push(item.id) : ''
          // if ((item.productName && item.productNameStandard && item.productName !== item.productNameStandard) || (item.code && item.codeStandard && item.code !== item.codeStandard) || (item.approvalNumber && item.approvalNumberStandard && item.approvalNumber !== item.approvalNumberStandard) || (item.manufacturer && item.manufacturerStandard && item.manufacturer !== item.manufacturerStandard) || (item.spec && item.specStandard && item.spec !== item.specStandard) || (item.productUnit && item.productUnitStandard && item.productUnit !== item.productUnitStandard)) {
          //   disaccord.push(item.id)
          // }
          if (
            Number(item.infoEqualsStandard) === 2 ||
            Number(item.infoEqualsStandard) === 3
          ) {
            disaccord.push(item.id)
          }
        })
        if (errorAry.length > 0) {
          this.$confirm(
            '只允许发布“匹配状态”为“正常”的商品，请重新勾选待发布的商品',
            '商品发布提醒',
            {
              customClass: 'my-confirm',
              confirmButtonText: '确定',
              showCancelButton: false
            }
          )
            .then(() => {})
            .catch(() => {})
          return
        }
        const str = []
        if (disaccord.length > 0) {
          str.push(
            `<p>${disaccord.length}个商品匹配ID后，商品的名称、规格、包装单位、批准文号、厂家与标准库ID不一致，发布后将使用标准库信息。</p>`
          )
        }
        if (hasImgAry.length > 0) {
          str.push(
            `<p>${hasImgAry.length}个待发布商品没有图片，无图商品发布后将导致在首页、推荐等重要位置流量受限，确认发布吗？</p>`
          )
        }
        if (str.length > 0) {
          this.$confirm(str.join(' '), '商品发布提醒', {
            customClass: 'my-confirm',
            dangerouslyUseHTMLString: true,
            showClose: false,
            closeOnClickModal: false,
            confirmButtonText: '暂不发布',
            cancelButtonText: '已了解，确认发布'
          }).catch(() => {
            this.pushProductFn(aty)
          })
        } else {
          this.pushProductFn(aty)
        }
      } else {
        // this.$message.warning('请先勾选要发布的商品')
        this.$message({
          type: 'warning',
          message: '请先勾选要发布的商品',
          offset: 60
        })
      }
    },
    pushProductFn(idList) {
      const load = this.loadingFun()
      pushListGoods(idList)
        .then((res) => {
          load.close()
          if (res.code === 0) {
            this.$message({
              type: 'success',
              message: '发布成功',
              offset: 60
            })
            this.getList()
            this.visible = true
          } else {
            load.close()
            this.$message({
              type: 'error',
              message: res.message,
              offset: 60
            })
          }
        })
        .catch((error) => {
          load.close()
          this.$message({
            type: 'error',
            message: error,
            offset: 60
          })
        })
    },
    /* 删除商品 */
    deleteListImg() {
      if (this.filterType == 3) {
        this.$message({
          type: 'warning',
          message: '当前状态下不允许删除',
          offset: 60
        })
        return
      }
      const sendAry = this.$refs.multipleTable.selection
      if (sendAry.length > 0) {
        const load = this.loadingFun()
        // let idStr = ''
        let idStr = []
        sendAry.forEach((item) => {
          // idStr += `${item.id},`
          idStr.push(item.id)
        })
        idStr = idStr.join(',')
        deleteListGoods(idStr)
          .then((res) => {
            load.close()
            if (res.code === 0) {
              this.$message({
                type: 'success',
                message: '删除成功',
                offset: 60
              })
              this.getList()
            } else {
              this.$message({
                type: 'error',
                message: res.message,
                offset: 60
              })
              // this.$message.error(res.message)
            }
          })
          .catch((error) => {
            // this.$message.error(error)
            this.$message({
              type: 'error',
              message: error,
              offset: 60
            })
          })
      } else {
        // this.$message.warning('请先勾选要发布的商品')
        this.$message({
          type: 'warning',
          message: '请先勾选要删除的商品',
          offset: 60
        })
      }
    },
    clearList() {
      // this.listQuery = {
      //   pageSize: 10,
      //   pageNum: 1,
      //   productName: '',
      //   erpCode: '',
      //   manufacturer: '',
      //   code: '',
      //   hasStock: -1,
      //   source: -1,
      //   hasImg: -1,
      //   canPublish: -1
      // }
      this.listQuery = JSON.parse(JSON.stringify(this.initiallistQuery))
    },
    loadingFun() {
      const loading = this.$loading({
        lock: true,
        text: 'Loading',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      return loading
    },
    searchFn(value) {
      this.clearList()
      this.listQuery.canPublish = 1
      this.listQuery.infoEqualsStandard = value || 0
      if (value == 2) {
        this.listQuery.publishMatchStatus = 0
        this.listQuery.valid = 1
        this.searchNum = value
      } else {
        this.searchNum = ''
      }
      this.getList()
    },
    searchNoImg() {
      this.clearList()
      this.listQuery.hasImg = 2
      this.getList()
    },
    async cover() {
      const res = await resetCode()
      if (res && res.code === 0) {
        // this.$message.success(res.data.message)
        this.$alert(res.message, { type: 'success' })
      } else {
        // this.$message.warning(res.data.message)
        this.$alert(res.message, { type: 'error' })
      }
    },
    unitModifyBtn(row) {
      this.modifyUnitDialogData = row || {}
      this.modifyUnitDialogVisible = true
    },
    selectChangeCode(row) {
      const params = {
        id: row.id,
        code: row.codeSelected
      }
      apiChangeCode(params).then((res) => {
        if (res.code === 0) {
          this.$message.success('修改成功')
        } else {
          this.$message.error(res.message)
        }
      })
    },
    toProduct() {
      const batchPath = this.$route.fullPath
      let obj = {}
      obj.status = 6
      const path = '/productList'
      window.openTab(path, obj)
      setTimeout(() => {
        // const path = '/batchGoods'
        this.visible = false
        window.closeTab(batchPath)
        window.closeTab('/batchGoods')
      }, 100)
    },
    onCancel() {
      this.visible = false
    },
    // 下载示例
    downloadSample() {
      const env = process.env.NODE_ENV
      let excelTemplate = 'https://upload.ybm100.com/pop/temp/商品图片.zip'
      if (env === 'development' || env === 'test') {
        excelTemplate = 'https://upload.test.ybm100.com/pop/temp/商品图片.zip'
      }
      window.open(excelTemplate)
    }
  }
}
</script>

<style scoped lang="scss">
::v-deep   .dis_cascader {
  width: 205px;
  height: 32px;
  .el-input {
    width: 205px;
    height: 32px;
    position: relative;
    .el-input__inner {
      height: 32px;
      line-height: 32px;
      position: absolute;
      top: 0;
    }
  }
}
.serch {
  font-weight: bold;
  .sign {
    display: inline-table;
    width: 3px;
    height: 13px;
    background: linear-gradient(360deg, #1d69c4 1%, #8bbdfc 99%);
    border-radius: 2px;
    margin-right: 8px;
  }
}
.page-box {
  text-align: right;
  padding-top: 15px;
}
.line-div {
  border-bottom: 1px solid #efefef;
  .search-title {
    display: table-cell;
    padding: 0 10px;
    text-align: center;
    border: 1px solid #dcdfe6;
    height: 30px;
    line-height: 30px;
    vertical-align: middle;
    border-right: none;
    border-radius: 4px 0 0 4px;
    color: #333333;
    white-space: nowrap;
  }
  .my-search-row ::v-deep  .el-input__inner {
    border-radius: 0 4px 4px 0;
  }
  .my-search-row ::v-deep  .el-select {
    display: table-cell;
  }
  ::v-deep  .el-input-group__prepend {
    background: none;
    color: #333333;
    padding: 0 10px;
  }
  .div-text {
    p {
      margin: 0;
      padding-top: 10px;
    }
    .tip-box {
      padding-left: 26px;
      .tip {
        font-size: 12px;
        color: #666666;
        margin: 0;
        padding: 3px 0;
        .el-button {
          font-size: 12px;
          line-height: 16px;
          padding: 0;
        }
      }
    }
  }
  .div-info {
    padding: 10px;
    background: #f9f9f9;
    border-radius: 2px;
    font-size: 14px;
    font-family: PingFangSC, PingFangSC-Regular;
    font-weight: 400;
    color: #666666;
    margin: 15px 10px 0 0;
    min-height: 98px;
    p {
      margin: 0;
      padding-top: 5px;
      font-size: 14px;
      font-family: PingFangSC, PingFangSC-Medium;
      font-weight: 500;
      color: #333333;
    }
    .tip-box {
      .tip {
        font-size: 12px;
        color: #363636;
        margin: 0;
        padding: 3px 0;
        .el-button {
          font-size: 12px;
          line-height: 16px;
          padding: 0;
        }
      }
    }
    .release-goods-green {
      font-size: 20px;
      font-weight: 500;
      color: #0fb559;
      font-family: PingFangSC, PingFangSC-Medium;
    }
    .release-goods-red {
      font-size: 20px;
      font-weight: 500;
      color: #ff3945;
      font-family: PingFangSC, PingFangSC-Medium;
    }
    .release-goods-search {
      position: relative;
      padding-left: 10px;
      margin-right: 10px;
      font-size: 13px;
      font-weight: 400;
      color: #4184d5;
      cursor: pointer;
      &::before {
        content: '';
        width: 1px;
        height: 100%;
        position: absolute;
        left: 0;
        top: 0;
        background-color: #dfdfdf;
        transform: scaleY(0.6);
      }
    }
    .release-goods-info {
      font-size: 12px;
      font-weight: 400;
      color: #666666;
    }
  }
}
::v-deep  .el-button--primary {
  background: #4183d5;
}
::v-deep  .el-button--primary.is-disabled {
  background-color: #a0c1ea !important;
}
::v-deep  .el-button--primary.is-plain {
  color: #4183d5;
  background: #ffffff;
  border-color: #4183d5;
}
.status-span {
  padding-left: 26px;
  position: relative;
  i {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    left: 16px;
    display: inline-block;
    vertical-align: middle;
    width: 5px;
    height: 5px;
    border-radius: 50%;
  }
}
.con-box {
  ::v-deep  .el-table th > .cell {
    color: #333333;
    font-size: 14px;
  }
  .colorB {
    color: #4183d5 !important;
    cursor: pointer;
    a {
      text-decoration: none;
      color: #4183d5 !important;
    }
  }

  body {
    padding: 10px 15px;
  }
  .padding20 {
    padding: 15px 20px;
  }
  .div-dialog p {
    margin: 0;
    padding: 0;
  }
  .upload-box {
    display: inline-block;
    margin-left: 10px;
  }
  .btn-box {
    position: sticky;
    top: 0; /* 子元素吸顶位置 */
    z-index: 666;
    background-color: #fff; /* 子元素背景颜色 */
    padding: 5px 0px;
    .tip {
      .goods-tip {
        color: #ff8e00;
        font-size: 12px;
        color: #999999;
        padding: 8px 0;
        margin: 0;
      }
    }
  }
  ::v-deep  .el-message-box--center .el-message-box__header {
    padding-top: 0;
  }
  ::v-deep  .el-dialog__body {
    padding: 10px 15px;
  }
  .padding20 {
    padding: 15px 20px;
  }
  .div-dialog p {
    margin: 0;
    padding: 0;
  }
  .upload-box-img {
    display: inline-block;
    margin-right: 10px;
  }
  .goods-tip {
    color: #ff8e00;
    font-size: 12px;
    padding: 8px 0;
    margin: 0;
  }
  ::v-deep  .el-message-box--center .el-message-box__header {
    padding-top: 0;
  }

  .atypismStr {
    color: #fe2828;
  }

  ::v-deep   .el-radio__label {
    width: 100% !important;
    text-overflow: ellipsis;
    white-space: normal;
    line-height: 18px;
  }
  ::v-deep  .el-table {
    .el-checkbox__inner {
      border: 1px solid #000000;
    }
  }
}
.tab_all {
  display: flex;
  align-items: center;
  margin: 15px 0px;
}
::v-deep  .tab_all .el-tabs__header {
  margin: 0;
}
.fw {
  font-weight: bold;
}
</style>
