<template>
  <div class="settlement-box">
    <div
      v-if="isHandle"
      class="commission-state"
    >
      {{ handleTip }}<span
        class="activeTo"
        @click="gotoCommission"
      >去处理></span>
    </div>
    <div
      v-if="tipOpeningAccount"
      class="tip-opening-account"
    >
      为保证平台业务合规性，提升商户资金安全性，请您在
      <span style="color:#ff2121;text-decoration:underline;">
        {{ deadlineDatetime }}
      </span> 前尽快完成“企业开户”。逾期未完成企业开户，平台将限制您的余额提现功能，企业开户成功后将恢复提现功能。详细开户流程可参考首页的企业开户操作说明。
      <span
        class="activeTo"
        @click="toCompanyOpenAccount"
      >去企业开户></span>
    </div>
    <el-row
      :gutter="20"
      class="price-box"
    >
      <el-col
        :span="8"
      >
        可提现金额(元)
        <span style="color: red;">{{
          info.totalCanCashAdvanceAmount || info.totalCanCashAdvanceAmount === 0
            ? info.totalCanCashAdvanceAmount.toFixed(2)
            : ''
        }}</span>
      </el-col>
      <el-col
        :span="8"
      >
        提现中金额（元）
        <span>{{
          info.totalAmountInCashAdvance || info.totalAmountInCashAdvance === 0
            ? info.totalAmountInCashAdvance.toFixed(2)
            : ''
        }}</span>
      </el-col>
      <el-col
        :span="8"
      >
        已提现金额（元）
        <span>{{
          info.totalAlreadyCashAdvanceAmount || info.totalAlreadyCashAdvanceAmount === 0
            ? info.totalAlreadyCashAdvanceAmount.toFixed(2)
            : ''
        }}</span>
      </el-col>
    </el-row>
    <div class="btn-box">
      <el-button
        v-permission="['bill_withdraw']"
        type="primary"
        @click="toApplyWithdraw(true)"
      >
        申请提现
      </el-button>
      <el-button
        plain
        @click="toWithdraw"
      >
        查看提现记录
      </el-button>
      <!-- <EditAccount></EditAccount> -->
    </div>
    <p>
      账单规则：<br>
      1、账单会根据结算单据的支付方式及佣金结算方式分为以下几类：<br>非月结商家：在线支付账单、线下转账(电汇平台)账单<br>月结商家：在线支付账单、线下转账(电汇平台)账单、线下转账(电汇商业)账单<br>
      {{ '2、若账单的“应结算金额”>=0则将账单状态记为“已入账”，若账单的“应结算金额”<0则将账单状态记为“未入账”。未入账的账单会同后面生成的账单应结算金额求和，若求和金额为正数，则所有账单变为“已入账”' }}<br>
      3、“已入账”且“分润成功”的账单应结算金额将记入“可提现金额”中。若账单分润失败，请及时联系平台运营处理
    </p>
    <div class="searchMy">
      <el-form
        ref="listQuery"
        :model="listQuery"
        :inline="true"
        size="small"
      >
        <el-form-item
          prop="billNo"
        >
          <el-input
            v-model="listQuery.billNo"
            placeholder="请输入"
          >
            <template slot="prepend">
              账单号
            </template>
          </el-input>
        </el-form-item>
        <el-form-item
          prop="payTypes"
        >
          <span
            class="search-title"
          >账单类型</span>
          <el-select
            v-model="listQuery.payTypes"
            placeholder="请选择"
            multiple
          >
            <el-option
              label="全部"
              value=""
            />
            <el-option
              label="在线支付"
              :value="1"
            />
            <el-option
              label="电汇平台"
              :value="3"
            />
            <el-option
              label="电汇商业"
              :value="4"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          prop="billPaymentStatus"
        >
          <span
            class="search-title"
          >账单状态</span>
          <el-select
            v-model="listQuery.billPaymentStatus"
            placeholder="请选择"
          >
            <el-option
              label="全部"
              value=""
            />
            <el-option
              label="已入账"
              :value="1"
            />
            <el-option
              label="未入账"
              :value="0"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          prop="billShareStatus"
        >
          <span
            class="search-title"
          >分润状态</span>
          <el-select
            v-model="listQuery.billShareStatus"
            placeholder="请选择"
          >
            <el-option
              label="全部"
              value=""
            />
            <el-option
              label="未分润"
              :value="1"
            />
            <el-option
              label="分润成功"
              :value="2"
            />
            <el-option
              label="分润失败"
              :value="3"
            />
            <el-option
              label="分润中"
              :value="4"
            />
            <el-option
              label="无需分润"
              :value="0"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          prop="createTime"
        >
          <span
            class="search-title"
          >生成时间</span>
          <div style="display: table-cell; line-height: 24px">
            <el-date-picker
              v-model="listQuery.createTime"
              type="datetimerange"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              :default-time="['00:00:00', '23:59:59']"
              @change="handleChangeCreateTime"
            />
          </div>
        </el-form-item>
        <el-form-item
          prop="paymentTime"
        >
          <span
            class="search-title"
          >入账时间</span>
          <div style="display: table-cell; line-height: 24px">
            <el-date-picker
              v-model="listQuery.paymentTime"
              type="datetimerange"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              :default-time="['00:00:00', '23:59:59']"
              @change="handleChangePaymentTime"
            />
          </div>
        </el-form-item>
        <el-form-item
          prop="businessNo"
        >
          <el-input
            v-model="listQuery.businessNo"
            placeholder="请输入"
          >
            <template slot="prepend">
              单据号
            </template>
          </el-input>
        </el-form-item>
        <el-form-item
          prop="remitStatus"
        >
          <span
            class="search-title"
          >打款状态</span>
          <el-select
            v-model="listQuery.remitStatus"
            placeholder="请选择"
          >
            <el-option
              label="全部"
              value=""
            />
            <el-option
              label="已打款"
              :value="1"
            />
            <el-option
              label="未打款"
              :value="0"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          prop="settlementType"
        >
          <span
            class="search-title"
          >佣金结算方式</span>
          <el-select
            v-model="listQuery.settlementType"
            placeholder="请选择"
          >
            <el-option
              label="全部"
              value=""
            />
            <el-option
              label="月结"
              :value="2"
            />
            <el-option
              label="非月结"
              :value="1"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          prop="deducted"
        >
          <span
            class="search-title"
          >补贴冲抵佣金</span>
          <el-select
            v-model="listQuery.deducted"
            placeholder="请选择"
          >
            <el-option
              label="全部"
              value=""
            />
            <el-option
              label="是"
              :value="1"
            />
            <el-option
              label="否"
              :value="0"
            />
          </el-select>
        </el-form-item>
        <el-form-item prop="paymentChannel">
          <span
            class="search-title"
          >支付通道</span>
          <el-select
            v-model="listQuery.paymentChannel"
            placeholder="请选择"
          >
            <el-option
              label="全部"
              value=""
            />
            <el-option
              label="直连支付"
              :value="1"
            />
            <el-option
              label="平安支付"
              :value="3"
            />
            <el-option
              label="富民支付"
              :value="2"
            />
            <el-option
              label="其他支付方式"
              :value="0"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          class="btn-item"
          style="width: 100%;text-align: right;padding-right: 20px"
        >
          <el-button
            v-permission="['bill_exportBillProductDetail']"
            @click="handleExportBillList('product')"
          >
            导出账单商品明细
          </el-button>
          <el-button
            v-permission="['bill_exportBillDetail']"
            @click="handleExportBillList('detail')"
          >
            导出账单明细
          </el-button>
          <el-button
            v-permission="['bill_exportBill']"
            @click="handleExportBillList('bill')"
          >
            导出账单
          </el-button>
          <el-button
            type="primary"
            @click="reset"
          >
            重置
          </el-button>
          <el-button
            type="primary"
            @click="getList(listQuery, true)"
          >
            查询
          </el-button>
        </el-form-item>
      </el-form>
    </div>
    <div style="margin-top: 10px;">
      <xyy-table
        :data="list"
        :list-query="listQuery"
        :col="col"
        @get-data="getList"
      >
        <template
          slot="attachmentUrl"
          slot-scope="{col}"
        >
          <el-table-column
            :key="col.index"
            :label="col.name"
            :width="col.width"
            fixed="right"
          >
            <template slot-scope="{row}">
              <el-button
                size="small"
                type="primary"
                @click="handleGoDetail(row.billNo)"
              >
                查看明细
              </el-button>
            </template>
          </el-table-column>
        </template>
      </xyy-table>
    </div>
    <el-row
      :gutter="20"
      class="price-box mb15 new-price"
    >
      <el-col :span="6">
        <el-tooltip
          class="item"
          content="当前查询结果所有单据，佣金金额求和"
          placement="left"
        >
          <i class="el-icon-warning-outline" />
        </el-tooltip>
        佣金金额合计（元）
        <span>{{
          hireMoneyTotal || 0
        }}</span>
      </el-col>
      <el-col :span="6">
        <el-tooltip
          class="item"
          content="当前查询结果所有单据，应结算金额求和"
          placement="left"
        >
          <i class="el-icon-warning-outline" />
        </el-tooltip>
        应结算金额合计(元)
        <span>{{
          statementTotalMoneyTotal || 0
        }}</span>
      </el-col>
      <el-col :span="6">
        <el-tooltip
          class="item"
          content="当前查询结果所有单据，应缴纳佣金求和"
          placement="left"
        >
          <i class="el-icon-warning-outline" />
        </el-tooltip>
        应缴纳佣金合计(元)
        <span>{{ deductedCommissionTotal || 0 }}</span>
      </el-col>

      <el-col :span="6">
        <el-tooltip
          class="item"
          content="当前查询结果所有单据，实际需缴纳佣金求和"
          placement="left"
        >
          <i class="el-icon-warning-outline" />
        </el-tooltip>
        实际需缴纳佣金合计(元)
        <span>{{
          actualCommissionMoneyTotal || 0
        }}</span>
      </el-col>
      <el-col :span="6">
        <el-tooltip
          class="item"
          content="当前查询结果所有单据，佣金优惠求和"
          placement="left"
        >
          <i class="el-icon-warning-outline" />
        </el-tooltip>
        佣金优惠合计（元）
        <span>{{
          commissionDiscountMoneyTotal || 0
        }}</span>
      </el-col>
    </el-row>
    <export-tip
      :change-export="changeExport"
      @handleExoprClose="handleExoprClose"
      @handleChangeExport="handleChangeExport"
    />
    <ApplyWithdrawalDialog
      v-if="applyWithdrawalDialogVisible"
      :apply-withdrawal-dialog-visible.sync="applyWithdrawalDialogVisible"
      :info="fuMinInfo"
    />
    <el-dialog
      title="企业开户提醒通知"
      :visible.sync="tipOpeningAccountDialogVisible"
      :before-close="handleClose"
      width="40%"
    >
      <div>
        为保证平台业务合规性，提升商户资金安全性，请您在
        <span style="color:#ff2121;">{{ deadlineDatetime }}</span> 前尽快完成“企业开户”。逾期未完成企业开户，平台将限制您的余额提现功能，企业开户成功后将恢复提现功能。
        <br>详细开户流程可参考首页的<span style="font-weight: bold;">企业开户操作说明</span>
      </div>
      <span
        slot="footer"
        class="dialog-footer"
      >
        <el-button
          v-if="isShowWithdrawal"
          size="small"
          @click="toApplyWithdraw(false)"
        >继续提现</el-button>
        <el-button
          type="primary"
          size="small"
          @click="toCompanyOpenAccount"
        >去企业开户</el-button>
      </span>
    </el-dialog>
    <el-dialog
      :visible.sync="withdrawStatus"
      custom-class="withdraw-dialog"
      :close-on-click-modal="false"
      title="申请提现"
    >
      <div class="withdrawAmount">
        提现金额<span style="color: #ff2121;margin-left: 20px;font-size: 16px">{{ withdraw.applyAmount }}元</span>
      </div>
      <div style="color: #ff2121;font-size: 12px;padding: 0;margin-bottom: 5px">
        *请填写企业收款账号及开户行
      </div>
      <el-form
        ref="withdraw"
        :model="withdraw"
        :rules="rules"
        label-width="80px"
        label-position="top"
      >
        <el-form-item
          label="账户名称："
          prop="accountName"
        >
          <el-input
            v-model.trim="withdraw.accountName"
            disabled
          />
        </el-form-item>
        <el-form-item
          label="账户号："
          prop="accountNum"
        >
          <el-input
            v-model="withdraw.accountNum"
            placeholder="请输入企业收款账号"
            maxlength="30"
          />
        </el-form-item>
        <el-form-item
          label="开户行："
          prop="accountBank"
        >
          <el-input
            v-model.trim="withdraw.accountBank"
            placeholder="请输入开户行及支行名称"
            maxlength="50"
          />
        </el-form-item>
      </el-form>
      <div>
        <div>温馨提示：</div>
        <div>
          1、满{{ withdraw.lowestAmount }}元可发起提现，单笔提现金额需小于{{
            (withdraw.highestAmount / 10000).toFixed(0)
          }}万；
        </div>
        <div>2、每个自然日限提现{{ withdraw.number }}次；</div>
        <div> 3、周一至周五，当天提现，下一个工作日到账，节假日顺延；</div>
      </div>
      <div slot="footer">
        <el-button @click="withdrawStatus = false">
          取消
        </el-button>
        <el-button
          type="primary"
          :disabled="withdrawDisabled"
          @click="applyWithdraw"
        >
          确定
        </el-button>
      </div>
    </el-dialog>
    <!--待商业付款、待平台审核、审核未通过且已逾期的佣金缴纳记录-->
    <el-dialog
      :visible.sync="deadlineTip"
      custom-class="deadlineTip-dialog"
      top="0"
      title="提示"
    >
      <p>
        您有已逾期的佣金缴纳记录，系统已限制您的申请提现功能。佣金缴纳记录处理完成后系统将自动恢复您的申请提现功能，请及时处理
      </p>
      <div slot="footer">
        <el-button @click="deadlineTip = false">
          取消
        </el-button>
        <el-button
          type="primary"
          @click="gotoCommission"
        >
          去处理
        </el-button>
      </div>
    </el-dialog>
    
    <el-dialog title="" :visible.sync="myDialogVisible" width="40%" @closed="myDialogVisible = false">
      <p>{{ myText }}</p>
      <div slot="footer">
        <el-button @click="myDialogVisible = false">我知道了</el-button>
        <el-button type="primary" @click="goCharge">去充值保证金</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import exportTip from '@/views/other/components/exportTip';
import ApplyWithdrawalDialog from './components/applyWithdrawalDialog';
import { queryBillStatistic, listBill, exportBillList, exportBillDetailList, exportBillProductList } from '../../api/settlement/bill';
import {
  getPriceInfo,
  getAccountInfo,
  getWithdrawTimes,
  getSellerStatus,
  apiQueryOpenAccountTips,
  sendWithdraw,
} from '../../api/settlement/online';
import EditAccount from './components/editAccount.vue';

export default {
  name: 'Bill',
  components: { exportTip, ApplyWithdrawalDialog, EditAccount },
  data() {
    return {
      myDialogVisible: false,
      myText: '',
      rules: {
        // accountName: [
        //   {
        //     required: true,
        //     message: '请输入账户名',
        //     trigger: ['blur', 'change']
        //   }
        // ],
        accountNum: [
          {
            required: true,
            message: '请输入企业收款账号',
            trigger: ['blur', 'change'],
          },
          { validator: this.validateNum, trigger: ['change', 'blur'] },
        ],
        accountBank: [
          {
            required: true,
            message: '请输入开户行及支行名称',
            trigger: ['blur', 'change'],
          },
        ],
        applyAmount: [
          {
            required: true,
            message: '请输入提现金额',
            trigger: ['blur', 'change'],
          },
          {
            validator: this.validateApplyAmount,
            trigger: ['change', 'blur'],
          },
        ],
      },
      withdraw: {
        accountName: '',
        accountNum: '',
        accountBank: '',
        applyAmount: '',
        highestAmount: '',
        lowestAmount: '',
        number: '',
        onlineCanCashAmount: '',
        offlineCanCashAmount: '',
        tips: '',
      },
      fuMinInfo: {},
      applyWithdrawalDialogVisible: false,
      withdrawStatus: false, // 提现弹框
      withdrawDisabled: false, // 是否可提现
      deadlineTip: false, // 逾期提示弹框
      isShowWithdrawal: false,
      tipOpeningAccountDialogVisible: false,
      deadlineDatetime: '',
      tipOpeningAccount: false,
      isOverdue: '', // 逾期标志，1：逾期  2：不逾期
      handleTip: '',
      isHandle: false,
      changeExport: false,
      list: [],
      hireMoneyTotal: 0,
      statementTotalMoneyTotal: 0,
      actualCommissionMoneyTotal: 0,
      commissionDiscountMoneyTotal: 0,
      deductedCommissionTotal: 0,
      listQuery: {
        pageSize: 10,
        page: 1,
        total: 0,
        paymentChannel: '',
        billNo: '',
        billPaymentStatus: '',
        remitStatus: '',
        payTypes: '',
        settlementType: '',
        businessNo: '',
        startCreateTime: '',
        endCreateTime: '',
        createTime: [],
        startBillPaymentTime: '',
        endBillPaymentTime: '',
        paymentTime: [],
        billShareStatus: '',
        deducted: '',
      },
      info: {
        totalCanCashAdvanceAmount: '', // 可提现金额
        totalAmountInCashAdvance: '', // 提现中金额
        totalAlreadyCashAdvanceAmount: '', // 已提现金额
      },
      col: [
        {
          index: 'billNo',
          name: '账单号',
          width: 200,
        },
        {
          index: 'settlementType',
          name: '佣金结算方式',
          width: 150,
          formatter: (row, col, cell) => {
            if (cell === 1) {
              return '非月结';
            }
            if (cell === 2) {
              return '月结';
            }
            return '';
          },
        },
        {
          index: 'productMoney',
          name: '商品金额（元）',
          width: 150,
          formatter: (row, col, cell) => (cell || cell === 0 ? cell.toFixed(2) : ''),
        },
        {
          index: 'freightAmount',
          name: '运费金额（元）',
          width: 150,
          formatter: (row, col, cell) => (cell || cell === 0 ? cell.toFixed(2) : ''),
        },
        {
          index: 'totalMoney',
          name: '单据金额含运费（元）',
          width: 150,
          formatter: (row, col, cell) => (cell || cell === 0 ? cell.toFixed(2) : ''),
        },
        {
          index: 'shopTotalDiscount',
          name: '店铺总优惠（元）',
          width: 150,
          formatter: (row, col, cell) => (cell || cell === 0 ? cell.toFixed(2) : ''),
        },
        {
          index: 'platformTotalDiscount',
          name: '平台总优惠（元）',
          width: 150,
          formatter: (row, col, cell) => (cell || cell === 0 ? cell.toFixed(2) : ''),
        },
        {
          index: 'money',
          name: '单据实付金额（元）',
          width: 150,
          formatter: (row, col, cell) => (cell || cell === 0 ? cell.toFixed(2) : ''),
        },
        {
          index: 'hireMoney',
          name: '佣金金额（元）',
          width: 150,
          formatter: (row, col, cell) => (cell || cell === 0 ? cell.toFixed(2) : ''),
          renderHeader: (h, { column }) => {
            return h(
              'div', [
                h('span', column.label),
                h('el-tooltip', {
                  props: {
                    content: '账单的佣金金额=账单中所有单据的佣金金额求和',
                    placement: 'right',
                  },
                }, [
                  h('i', { class: 'el-icon-warning-outline' }),
                ]),
              ],
            );
          },
        },
        {
          index: 'statementTotalMoney',
          name: '应结算金额（元）',
          width: 150,
          formatter: (row, col, cell) => (cell || cell === 0 ? cell.toFixed(2) : ''),
          renderHeader: (h, { column }) => {
            return h(
              'div', [
                h('span', column.label),
                h('el-tooltip', {
                  props: {
                    content: '账单的应结算金额=账单中所有单据的应结算金额求和',
                    placement: 'right',
                  },
                }, [
                  h('i', { class: 'el-icon-warning-outline' }),
                ]),
              ],
            );
          },
        },
        {
          index: 'deductedCommission',
          name: '应缴纳佣金（元）',
          width: 200,
          renderHeader: (h, { column }) => {
            return h(
              'div', [
                h('span', column.label),
                h('el-tooltip', {
                  props: {
                    content: '账单的应缴纳佣金=账单中所有单据的应缴纳佣金求和。单据平台补贴金额冲抵佣金金额后，对应账单商业应给平台缴纳的佣金金额',
                    placement: 'right',
                  },
                }, [
                  h('i', { class: 'el-icon-warning-outline' }),
                ]),
              ],
            );
          },
        },
        {
          index: 'actualCommissionMoney',
          name: '实际需缴纳佣金（元）',
          width: 200,
          renderHeader: (h, { column }) => {
            return h(
              'div', [
                h('span', column.label),
                h('el-tooltip', {
                  props: {
                    content: '账单的实际需缴纳佣金=账单中所有单据的实际需缴纳佣金求和。单据享受佣金折扣政策优惠后，对应账单商业实际需给平台缴纳的佣金金额',
                    placement: 'right',
                  },
                }, [
                  h('i', { class: 'el-icon-warning-outline' }),
                ]),
              ],
            );
          },
        },
        {
          index: 'commissionDiscountMoney',
          name: '佣金优惠（元）',
          width: 200,
          renderHeader: (h, { column }) => {
            return h(
              'div', [
                h('span', column.label),
                h('el-tooltip', {
                  props: {
                    content: '账单的佣金优惠=账单中所有单据的佣金优惠求和。单据因享受佣金折扣政策而产生的佣金优惠',
                    placement: 'right',
                  },
                }, [
                  h('i', { class: 'el-icon-warning-outline' }),
                ]),
              ],
            );
          },
        },
        {
          index: 'billPaymentStatus',
          name: '账单状态',
          formatter: (row, col, cell) => (cell === 1 ? '已入账' : '未入账'),
        },
        {
          index: 'billShareStatus',
          name: '分润状态',
          formatter: (row, col, cell) => {
            if (cell === 0) {
              return '无需分润';
            }
            if (cell === 1) {
              return '未分润';
            }
            if (cell === 2) {
              return '分润成功';
            }
            if (cell === 3) {
              return '分润失败';
            }
            if (cell === 4) {
              return '分润中';
            }
            return '';
          },
        },
        {
          index: 'remitStatus',
          name: '打款状态',
          formatter: (row, col, cell) => (cell === 1 ? '已打款' : '未打款'),
        },
        {
          index: 'billCreateTime',
          name: '生成时间',
          width: 200,
          formatter: (row, col, cell) => (cell ? new Date(cell + 8 * 3600 * 1000).toJSON().substr(0, 19).replace('T', ' ') : ''),
        },
        {
          index: 'billPaymentTime',
          name: '入账时间',
          width: 200,
          formatter: (row, col, cell) => (cell ? new Date(cell + 8 * 3600 * 1000).toJSON().substr(0, 19).replace('T', ' ') : ''),
        },
        {
          index: 'remitTime',
          name: '打款时间',
          width: 200,
          formatter: (row, col, cell) => (cell ? new Date(cell + 8 * 3600 * 1000).toJSON().substr(0, 19).replace('T', ' ') : ''),
        },
        {
          index: 'payType',
          name: '账单类型',
          width: 150,
          formatter: (row, col, cell) => {
            if (cell === 1) {
              return '在线支付';
            }
            if (cell === 3) {
              return '电汇平台';
            }
            if (cell === 4) {
              return '电汇商业';
            }
            return '';
          },
        },
        {
          index: 'deducted',
          name: '补贴冲抵佣金',
          width: 150,
          formatter: (row, col, cell) => {
            if (cell === 1) {
              return '是';
            }
            if (cell === 0) {
              return '否';
            }
            return '';
          },
        },
        {
          index: 'paymentChannel',
          name: '支付通道',
          width: 150,
          formatter: (row, col, cell) => {
            if (cell === 3) {
              return '平安支付'
            } else if (cell === 1) {
              return '直连支付'
            }
          }
        },
        {
          index: 'attachmentUrl',
          name: '操作',
          width: 150,
          slot: true,
        },
      ],
    };
  },
  created() {
    this.queryBillStatistic();
    this.getPriceInfo();
    this.getList(this.listQuery, true);
    this.getSellerStatus();
    this.queryOpenAccountTips();
  },
  methods: {
    goCharge(){
      window.openTab('/marginAccount')
      this.myDialogVisible = false
    },
    /**
     * 格式化日期
     */
    formatDate(date) {
      return Number(date)
        ? new Date(date + 8 * 3600 * 1000).toJSON().substr(0, 19).replace('T', ' ')
        : '';
    },
    handleChangePaymentTime(val) {
      if (val) {
        this.listQuery.startBillPaymentTime = this.formatDate(val[0].getTime());
        this.listQuery.endBillPaymentTime = this.formatDate(val[1].getTime());
      } else {
        this.listQuery.startBillPaymentTime = '';
        this.listQuery.endBillPaymentTime = '';
      }
    },
    handleChangeCreateTime(val) {
      if (val) {
        this.listQuery.startCreateTime = this.formatDate(val[0].getTime());
        this.listQuery.endCreateTime = this.formatDate(val[1].getTime());
      } else {
        this.listQuery.startCreateTime = '';
        this.listQuery.endCreateTime = '';
      }
    },
    /**
     * 跳转到提现明细
     */
    toWithdraw() {
      window.openTab('/withdrawalRecord');
      // window.parent.toWithdraw()
      // parent.$('#mainFrameTabs').bTabsAdd('private', '提现明细', '/accountStatement/accountStatementDetail')
    },
    handleClose() {
      this.tipOpeningAccountDialogVisible = false;
      // this.getFirstPageStatistics();
    },
    /**
     * 去处理-跳转企业开户页面
     */
    toCompanyOpenAccount() {
      this.handleClose();
      window.openTab('/companyOpenAccount');
    },
    /**
     * 去处理-跳转佣金缴纳记录页面
     */
    gotoCommission() {
      this.$router.push('/commissionRecord');
    },
    // 获取商家状态
    queryOpenAccountTips() {
      apiQueryOpenAccountTips()
        .then((res) => {
          if (res.code === 0) {
            if (res.result.showFlag) {
              const { showFlag, deadlineDatetime, currentDateTime } = res.result;
              this.tipOpeningAccount = showFlag;
              this.deadlineDatetime = deadlineDatetime;
              // 如果showFlag是true的话，deadlineDatetime有值，比较当前时间currentDateTime<deadlineDatetime，显示继续提现
              if (showFlag && deadlineDatetime && currentDateTime) {
                this.isShowWithdrawal = new Date().getTime(currentDateTime) < new Date(deadlineDatetime).getTime();
              }
            }
          } else {
            this.$message.error({ message: res.errorMsg || res.msg, customClass: 'center-msg' });
          }
        })
        .catch(() => {});
    },
    // 获取商家状态
    getSellerStatus() {
      getSellerStatus()
        .then((res) => {
          if (res.code === '200') {
            if (res.data.showFlag) {
              this.isHandle = true;
              this.handleTip = res.data.content;
              this.isOverdue = res.data.overdue;
            }
          } else {
            this.$message.error({ message: res.errorMsg || res.msg, customClass: 'center-msg' });
          }
        })
        .catch(() => {});
    },
    async toApplyWithdraw(isTipOpeningAccount) {
      if (this.tipOpeningAccount && isTipOpeningAccount) {
        this.tipOpeningAccountDialogVisible = true;
        return false;
      }
      // 待商业付款、待平台审核、审核未通过且已逾期的佣金缴纳记录
      if (this.isOverdue === 1) {
        this.tipOpeningAccountDialogVisible = false;
        this.deadlineTip = true;
        return false;
      }
      const res = await getAccountInfo();
      if (res && Number(res.code) === 200) {
          // -直连支付
          /* this.withdrawStatus = true;
          Object.keys(this.withdraw).forEach((key) => {
            if (res.data[key] || res.data[key] === 0) {
              this.withdraw[key] = res.data[key];
              // this.withdraw.applyAmount = ''
            }
          }, this);
          getWithdrawTimes()
            .then((_res) => {
              if (_res.code === '200') {
                if (_res.data >= this.withdraw.number) {
                  this.$message.warning({
                    message: `每天最多提现${this.withdraw.number}次`,
                    customClass: 'center-msg',
                  });
                  this.withdrawDisabled = true;
                } else {
                  this.withdrawDisabled = false;
                }
              } else {
                this.$message.error({
                  message: _res.errorMsg || _res.msg,
                  customClass: 'center-msg',
                });
              }
            })
            .catch(() => {
            }); */
        if(res.data.limitTips){
          this.myText = res.data.limitTips;
          this.myDialogVisible = true;
          return
        }
        if (Number(res.data.paymentChannel) === 2 || Number(res.data.paymentChannel) === 3 || Number(res.data.paymentChannel) === 1) {

          this.applyWithdrawalDialogVisible = true;
          this.fuMinInfo = res.data;
        } else {
          this.$message.error('未查询到支付渠道');
        }
      } else {
        this.$message.error(res.msg || '查询账户信息失败');
      }
    },
    validateNum(rules, val, cb) {
      if (!/^\d+$/.test(val)) {
        cb('请输入数字');
      } else {
        cb();
      }
    },
    validateApplyAmount(rules, val, cb) {
      const reg = /^(([1-9]{1}\d*)|([0]{1}))(\.(\d){0,2})?$/;
      if (!reg.test(val)) {
        cb('请输入数字，小数点后可保留两位');
      } else if (
        (this.withdraw.lowestAmount || this.withdraw.lowestAmount === 0)
        && val < this.withdraw.lowestAmount
      ) {
        cb('不能低于最小提现金额');
      } else if (
        (this.info.canCashAdvanceAmount || this.info.canCashAdvanceAmount === 0)
        && val > this.info.canCashAdvanceAmount
      ) {
        cb('提现金额不能大于可提现金额');
      } else if (
        (this.withdraw.highestAmount || this.withdraw.highestAmount === 0)
        && val > this.withdraw.highestAmount
      ) {
        cb('不能超过单笔最大提现金额');
      } else {
        cb();
      }
    },
    /**
     * 提现
     */
    applyWithdraw() {
      this.$refs.withdraw.validate((valid) => {
        if (valid) {
          this.applyWithdrawFn(this.withdraw);
        }
      });
    },
    applyWithdrawFn(data) {
      if (!data) return false;
      const { applyAmount, onlineCanCashAmount, offlineCanCashAmount, directOnlineCanCashAmount } = data;
      console.log(data);
      const params = { applyAmount, onlineApplyAmount: onlineCanCashAmount, offlineApplyAmount: offlineCanCashAmount, directOnlineApplyAmount: directOnlineCanCashAmount };
      sendWithdraw(params)
        .then((res) => {
          if (res.code === '200') {
            // 刷新提现金额
            this.getPriceInfo();
            // 刷新账单
            this.getList(this.listQuery, true);
            this.$message.success({ message: '提现成功', customClass: 'center-msg' });
            this.withdrawStatus = false;
            this.applyWithdrawalDialogVisible = false;
          } else {
            this.$message.error({ message: res.errorMsg || res.msg, customClass: 'center-msg' });
          }
        })
        .catch(() => {
        });
    },
    handleGoDetail(billNo) {
      this.$router.push(`/settlementOnlineDetail?billNo=${billNo}`);
    },
    getList(listQuery, reset) {
      const loading = this.$loading({
        lock: true,
        text: 'Loading',
        spinner: 'el-icon-loading',
        background: 'rgba(255,255,255, 0.8)',
      });
      const { page, pageSize } = listQuery;
      this.listQuery.pageSize = pageSize;
      this.listQuery.pageNum = reset ? 1 : page;
      this.listQuery.page = reset ? 1 : page;
      const params = JSON.parse(JSON.stringify(this.listQuery));
      params.createTime = null;
      params.paymentTime = null;
      if (Array.isArray(params.payTypes)) {
        params.payTypes = params.payTypes.join(',');
      }
      this.queryBillStatistic();
      listBill(params).then((res) => {
        loading.close();
        if (res.success) {
          const { data } = res;
          this.list = data.list || [];
          this.listQuery = {
            ...this.listQuery,
            total: data.total,
            page: data.pageNum,
          };
        } else {
          this.$message.error(res.msg || '请求失败');
        }
      });
    },
    reset() {
      this.$refs.listQuery.resetFields();
      this.listQuery.startBillPaymentTime = '';
      this.listQuery.endBillPaymentTime = '';
      this.listQuery.startCreateTime = '';
      this.listQuery.endCreateTime = '';
      this.getList(this.listQuery, true);
    },
    handleChangeExport(info) {
      this.changeExport = false;
      if (info === 'go') {
        const path = '/downloadList';
        window.openTab(path);
        // that.$router.push({ path: '/downloadList' });
      }
    },
    handleExoprClose() {
      this.changeExport = false;
    },
    async handleExportBillList(type) {
      let res = '';
	  const listQuery = {
		...this.listQuery
	  }
	  listQuery.payTypes = listQuery.payTypes.join(',');
      if (type === 'bill') {
        res = await exportBillList(listQuery);
      }
      if (type === 'detail') {
        res = await exportBillDetailList(listQuery);
      }
      if (type === 'product') {
        res = await exportBillProductList(listQuery);
      }
      if (res && res.code !== 0) {
        this.$message.error(res.message);
        return false;
      }
      this.changeExport = true;
    },
    getPriceInfo() {
      getPriceInfo().then((res) => {
        if (res.code === '200') {
          Object.keys(this.info).forEach((key) => {
            this.info[key] = res.data[key];
          }, this);
        } else {
          this.$message.error({ message: res.errorMsg || res.msg, customClass: 'center-msg' });
        }
      });
    },
    queryBillStatistic() {
      const params = JSON.parse(JSON.stringify(this.listQuery));
      params.createTime = null;
      params.paymentTime = null;
      if (Array.isArray(params.payTypes)) {
        params.payTypes = params.payTypes.join(',');
      }
      queryBillStatistic(params).then((res) => {
        if (res.success) {
          const { data } = res;
          Object.keys(data).forEach((item) => {
            this[item] = data[item];
          });
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.settlement-box {
  padding: 15px;
  .commission-state {
    display: flex;
    align-items: center;
    padding: 0 20px;
    font-size: 14px;
    color: #ff2121;
    height: 50px;
    background: #fffbf1;
    .activeTo {
      color: #4183d5;
      cursor: pointer;
    }
  }
  .tip-opening-account{
    align-items: center;
    padding: 0 20px;
    font-size: 14px;
    color: #ff2121;
    height: 50px;
    background: #fffbf1;
    .activeTo {
      color: #4183d5;
      cursor: pointer;
    }
  }
  .price-box {
    font-size: 16px;
    font-family: PingFangSC, PingFangSC-Semibold;
    font-weight: 600;
    color: #303133;
    line-height: 40px;
    overflow: hidden;
    &.mb15 {
      margin-bottom: 15px;
    }
    span {
      font-size: 28px;
    }
    .el-button {
      padding: 0 12px;
      line-height: 30px;
      &.is-plain {
        color: #4183d5;
        border-color: #4183d5;
      }
    }
  }
  .new-price {
    padding-top: 10px;
    font-size: 16px;
    font-family: PingFangSC, PingFangSC-Semibold;
    font-weight: 600;
    color: #303133;
    line-height: 40px;
    overflow: hidden;
    &.mb15 {
      margin-bottom: 15px;
    }
    span {
      font-size: 16px;
    }
  }
  .btn-box {
    margin-top: 15px;
    .el-button {
      padding: 0 12px;
      line-height: 30px;
      &.is-plain {
        color: #4183d5;
        border-color: #4183d5;
      }
    }
  }
  > p {
    padding: 8px;
    background: #f9f9f9;
    border-radius: 2px;
    font-size: 14px;
    font-family: PingFangSC, PingFangSC-Regular;
    font-weight: 400;
    color: #666666;
    margin: 15px 0 25px;
  }

  .el-tabs {
    .el-tab-pane {
      padding: 0 20px;
      .el-form {
        overflow: hidden;
        &.settlement-form {
          ::v-deep  .el-form-item__content {
            .el-select {
              width: 246px;
            }
          }
        }
      }
      .btn-item {
        float: right;
      }
    }
  }
}
.deadlineTip-dialog {
  width: 418px;
  top: 50%;
  transform: translateY(-50%);
  .el-dialog__header {
    padding: 0 20px;
    line-height: 40px;
    .el-dialog__headerbtn {
      top: 13px;
    }
  }
  .el-dialog__body {
    padding: 10px 20px;
    line-height: 24px;
  }
  .el-button {
    padding: 0 20px;
    line-height: 28px;
    height: 30px;
  }
}
.el-dialog.withdraw-dialog {
  width: 600px;
  //top: 50%;
  //transform: translateY(-50%);
  .el-dialog__header {
    padding: 0 20px;
    line-height: 50px;
    .el-dialog__headerbtn {
      top: 17px;
    }
  }
  .el-dialog__body {
    box-sizing: border-box;
    padding: 10px 20px;
    //max-height: 400px;
    overflow-y: auto;

    .withdrawAmount{
      margin-bottom: 12px;
    }

    .el-form {
      overflow: hidden;
      padding-bottom: 22px;
      .el-form-item {
        margin-bottom: 12px;
        .el-form-item__label {
          height: 30px;
          line-height: 30px;
          text-align: left;
          font-size: 12px;
        }

        .el-form-item__content {
          height: 30px;
          line-height: 30px;
          padding-right: 200px;
          width: 100%;

          .el-input__inner {
            height: 30px;
            line-height: 30px;
          }
          .el-form-item__error{
            width: 200px;
            top: 15%;
            left: 200px;
          }
        }
        &.with-btn {
          .el-input {
            width: calc(100% - 80px);
          }
          .el-button {
            float: right;
            padding: 0;
            &::after {
              content: '';
              clear: both;
            }
          }
        }
      }
    }
    > div {
      padding: 4px;
      border-radius: 2px;
      font-size: 12px;
      line-height: 17px;
      font-family: PingFangSC, PingFangSC-Regular;
      font-weight: 400;
      color: #666666;
      margin-top: 0;
      div{
        margin-bottom: 8px;
      }
    }
  }
  .el-button {
    padding: 0 20px;
    line-height: 30px;
    //height: 30px;
  }
}
.searchMy ::v-deep  .el-form-item__label {
  margin-left: 20px;
  padding: 0;
}
.searchMy ::v-deep  .el-input__inner {
  border-radius: 0 4px 4px 0;
}
.searchMy ::v-deep  .el-date-editor{
  width: 100%;
}
.search-title {
  display: table-cell;
  padding: 0 10px;
  text-align: center;
  border: 1px solid #dcdfe6;
  height: 30px;
  line-height: 30px;
  vertical-align: middle;
  border-right: none;
  border-radius: 4px 0 0 4px;
  color: #333333;
  white-space: nowrap;
}
.searchMy ::v-deep  .el-select {
  display: table-cell;
  width: 100%;
}
.searchMy ::v-deep  .el-form-item__content{
  width: 100%;
}
.searchMy ::v-deep  .el-form-item--small.el-form-item{
  margin-bottom: 10px;
  width: 32%;
}
.searchMy ::v-deep  .el-form-item--small .el-form-item__content{
  line-height: 30px;
  width: 100%;
}
.searchMy ::v-deep  .el-input-group__prepend {
  background: none;
  color: #333333;
  padding: 0 10px;
}
.Fsearch {
  padding: 0px 20px 10px;
  font-weight: 500;
  .searchMsg {
    font-weight: 700;
    width: 200px;
  }
  .sign {
    display: inline-table;
    width: 3px;
    height: 13px;
    background: linear-gradient(360deg, #1d69c4 1%, #8bbdfc 99%);
    border-radius: 2px;
    margin-right: 8px;
  }
}
.el-dialog.withdraw-dialog .el-dialog__body .el-form .el-form-item .el-form-item__content{
  width: 100%;
}
</style>
