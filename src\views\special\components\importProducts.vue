<template>
  <el-dialog
    title="导入商品"
    :visible="true"
    width="40%"
    :before-close="cancelDialog"
  >
    <div style="display: flex; padding-bottom: 10px; align-items: flex-start">
      <el-upload
        action="xxx"
        ref="excludeImport"
        :http-request="uploadFile"
        :before-remove="removeImportData"
        :show-file-list="true"
        :limit="1"
        accept=".xls, .xlsx, .XLS, .XLSX"
      >
        <el-button type="primary" size="small" style="margin-right: 10px">
          选择文件
        </el-button>
      </el-upload>
      <el-button size="small" @click="downloadTemp">导入商品模板下载</el-button>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button @click="cancelDialog">取 消</el-button>
      <el-button type="primary" @click="confirm">提 交</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { importProduct } from '../../../api/activity/special';

export default {
  props: {
    cancelDialog: {
      type: Function,
      default: () => {},
    },
    setChooseGoods: {
      type: Function,
      default: () => {},
    },
  },
  data() {
    return { fileFormData: {} };
  },
  methods: {
    uploadFile(params) {
      const { file } = params;
      if (file) {
        this.fileFormData.excelFile = file;
      } else {
        this.$message.warning('请选择上传文件!');
      }
    },
    removeImportData() {
      this.fileFormData = {};
    },
    downloadTemp() {
      window.open(`${process.env.VUE_APP_BASE_API}/promo/promotion/templateDownloadOfSku`);
    },
    confirm() {
      if (!this.fileFormData.excelFile) {
        this.$message.warning('请选择要上传的文件');
        return;
      }
      const loading = this.$loading({
        lock: true,
        text: 'Loading',
        spinner: 'el-icon-loading',
        background: 'rgba(255,255,255, 0.8)',
      });
      importProduct(this.fileFormData)
        .then((res) => {
          loading.close();
          if (res.code === 1000) {
            let con = '';
            const specialProductImportResult = res.data.specialProductImportResult || {};
            if (specialProductImportResult.failureNum > 0) {
              con = `<p>商品导入成功${specialProductImportResult.successNum}条，失败${specialProductImportResult.failureNum}条，失败原因请下载错误文件：<br><a style="color: #ff0021" href="${specialProductImportResult.failureExcelFileDownloadUrl}" download="导入商品错误文件下载">导入商品错误文件下载</a></p>`;
            } else {
              con = `<p>商品导入成功${specialProductImportResult.successNum}条，失败${specialProductImportResult.failureNum}条</p>`;
            }
            this.$confirm(con, '提示', {
              confirmButtonText: '确定',
              dangerouslyUseHTMLString: true,
              cancelButtonText: '取消',
            }).then(() => {
              this.cancelDialog();
              setTimeout(() => {
                this.setChooseGoods(specialProductImportResult.specialProductImportRowDTOS);
              }, 500);
            });
          } else {
            this.$message.error(res.msg);
          }
        })
        .catch((error) => {
          console.log(error);
          loading.close();
          this.$message({
            message: '请求失败',
            type: 'error',
          });
        });
    },
  },
  created() {},
};

</script>

<style lang="scss" scoped></style>
