<template>
	<el-date-picker
	size="small"
	v-model="time"
	:disabled="disabled"
	type="datetimerange"
	format="yyyy-MM-dd HH:mm:ss"
	value-format="yyyy-MM-dd HH:mm:ss"
	range-separator="至"
	start-placeholder="开始日期"
	end-placeholder="结束日期"
	:default-time="['00:00:00', '23:59:59']"
	style="width: 100%;"
	@change="dateOnChange"/>
</template>

<script>
export default {
	props: {
		value: {
			default: () => {},
			type: Object
		},
		startProp: {
			default: 'startTime',
			type: String
		},
		endProp: {
			default: 'endTime',
			type: String
		},
		disabled: {
			default: false,
			type: Boolean
		}
	},
	watch: {
		value: {
			immediate: true,
			deep: true,
			handler(newVal) {
				this.valueChange(newVal);
			}
		}
	},
	data() {
		return {
			time: ['', '']
		}
	},
	methods: {
		dateOnChange(val) {
			const res = {...this.value};
			if (val === null) {
				res[this.startProp] = '';
				res[this.endProp] = '';
			} else if (typeof val[0] === 'string') {
				res[this.startProp] = new Date(val[0]).getTime();
				res[this.endProp] = new Date(val[1]).getTime();
			}
			this.$emit('input', res);
		},
		valueChange(newVal) {
			this.time = []
			this.time[0] = newVal[this.startProp] != '' ? new Date(newVal[this.startProp]) : ''
			this.time[1] = newVal[this.endProp] != '' ? new Date(newVal[this.endProp]) : ''
		}
	}
}
</script>

<style>

</style>