<template>
  <div>
    <el-form
      ref="corData"
      :model="mergeData"
      size="small"
      label-position="right"
      :rules="corRules"
      label-width="80px"
    >
      <el-form-item
        v-for="(item, index) in propsData"
        :key="index"
        label=""
      >
        <div class="cor-list-box">
          <div
            class="list-name"
            style="display: flex;"
          >
            <i
              v-if="item.isNeed"
              class="red-i"
            >*</i>{{ item.name }}
            <div
              v-if="item.expireState === 1 || item.expireState === 2"
              class="expireState"
              :class="{ orange: item.expireState === 1}"
            >
              {{ item.expireState === 1 ? '即将过期' : '已过期' }}
            </div>
          </div>
          <div class="list-remark">
            {{ item.remark }}
          </div>
          <div
            v-if="(mergeData[item.id].remarks && qualStateStr === 6) || (isHistory && qualStateStr === 2)"
            class="list-remark"
            style="color: red"
          >
            {{ mergeData[item.id].remarks ? '驳回原因：' + mergeData[item.id].remarks : '' }}
          </div>
          <el-form-item :prop="item.isNeed ? item.id + 'imgList' : ''">
            <el-upload
              :ref="item.id"
              action=""
              :class="{ hide: !isDetail || mergeData[item.id].isUpload }"
              :http-request="
                (file) => {
                  return uploadImg(file, index, item.id)
                }
              "
              list-type="picture-card"
              :file-list="mergeData[item.id].urlVal"
              :on-preview="handlePictureCardPreview"
              :on-remove="
                (file) => {
                  return handleRemove(file, mergeData[item.id].urlVal, item.id)
                }
              "
              :on-change="
                (file) => {
                  return fileChange(file, item.id)
                }
              "
              :limit="item.maxImg"
              :before-upload="beforeAvatarUpload"
              :disabled="isDetail ? false : true"
              accept=".jpg,.jpeg,.png,.JPG,.JPEG"
              class="avatar-uploader"
            >
              <i class="el-icon-plus" />
              <div
                v-if="isDetail"
                slot="tip"
                class="el-upload__tip"
              >
                请上传jpg、png文件 大小不超过 1MB
              </div>
            </el-upload>
            <el-input
              v-show="false"
              v-model.trim="mergeData[item.id + 'imgList']"
            />
          </el-form-item>
          <el-form-item
            v-if="item.personNameShow"
            :label="item.personNameShowName"
            :prop="item.legalPersonName ? item.id + 'name' : ''"
            class="width60"
          >
            <el-input
              v-if="isDetail"
              v-model.trim="mergeData[item.id].nameVal"
              type="text"
              placeholder="请填写企业名称"
              @change="setValue($event, item.id + 'name', item.id)"
            />
            <span v-else>{{ mergeData[item.id].nameVal }}</span>
          </el-form-item>
          <el-form-item
            v-if="item.needCodeShow"
            :label="item.needCodeShowName"
            :prop="item.isNeedCode ? item.id + 'code' : ''"
            class="width60"
          >
            <el-input
              v-if="isDetail"
              v-model.trim="mergeData[item.id].codeVal"
              type="text"
              :placeholder="`请填写${item.needCodeShowName}`"
              @change="setValue($event, item.id + 'code', item.id)"
            />
            <span v-else>{{ mergeData[item.id].codeVal }}</span>
          </el-form-item>
          <el-form-item
            v-if="item.needBankShow"
            :label="item.needBankShowName"
            :prop="item.isNeedBank ? item.id + 'bank' : ''"
            class="width60"
          >
            <el-input
              v-if="isDetail"
              v-model.trim="mergeData[item.id].bankName"
              type="text"
              :placeholder="`请填写${item.needBankShowName}`"
              @change="setValue($event, item.id + 'bank', item.id)"
            />
            <span v-else>{{ mergeData[item.id].bankName }}</span>
          </el-form-item>
          <el-form-item
            v-if="item.needSubBankShow"
            :label="item.needSubBankShowName"
            :prop="item.isNeedSubBank ? item.id + 'subBank' : ''"
            class="width60"
          >
            <el-input
              v-if="isDetail"
              v-model.trim="mergeData[item.id].subBankName"
              type="text"
              :placeholder="`请填写${item.needSubBankShowName}`"
              @change="setValue($event, item.id + 'subBank', item.id)"
            />
            <span v-else>{{ mergeData[item.id].subBankName }}</span>
          </el-form-item>
          <el-form-item
            v-if="item.needTimeEndShow"
            :label="item.needTimeShowName"
            :prop="item.isNeedTime ? item.id + 'time' : ''"
            class="width70"
          >
            <div class="width25">
              <el-col :span="isDetail ? 8 : 6">
                <el-date-picker
                  v-if="isDetail"
                  :picker-options="{ disabledDate: time => time.getTime() > (new Date().getTime()) }"
                  v-model.trim="mergeData[item.id].startDateVal"
                  type="date"
                  placeholder="开始时间"
                  value-format="timestamp"
                  style="width: 100%"
                  @change="
                    changeDataTime(
                      mergeData[item.id].startDateVal,
                      mergeData[item.id].endDateVal,
                      item.id
                    )
                  "
                />
                <span v-else>{{ mergeData[item.id].startDateVal | formatDate }}</span>
              </el-col>
              <el-col
                class="line"
                :span="2"
                style="text-align: center"
              >
                至
              </el-col>
              <el-col :span="isDetail ? 8 : 6">
                <el-date-picker
                  v-if="isDetail"
                  v-model.trim="mergeData[item.id].endDateVal"
                  :class="{ corComponentOnColor: item.expireState === 1 && onColor, corComponentOnColorRed: item.expireState === 2 && onColor }"
                  :picker-options="options"
                  type="date"
                  placeholder="结束时间"
                  value-format="timestamp"
                  :disabled="mergeData[item.id].longTermVal ? true : false"
                  style="width: 100%;"
                  @change="
                    onColor = false;
                    changeDataTime(
                      mergeData[item.id].startDateVal,
                      mergeData[item.id].endDateVal,
                      item.id
                    )
                  "
                />
                <span v-else>{{ mergeData[item.id].endDateVal | formatDate }}</span>
              </el-col>
              <el-col
                v-if="item.withLongTime"
                class="line"
                :span="4"
                style="text-align: center"
              >
                <el-checkbox
                  v-if="isDetail"
                  :checked="mergeData[item.id].longTermVal ? true : false"
                  @change="
                    setLongTime(
                      mergeData[item.id].longTermVal,
                      mergeData[item.id].startDateVal,
                      item.id
                    )
                  "
                >
                  长期有效
                </el-checkbox>
                <span v-else>{{ mergeData[item.id].longTermVal ? '长期有效' : '' }}</span>
              </el-col>
              <el-input
                v-show="false"
                v-model.trim="mergeData[item.id + 'time']"
              />
            </div>
          </el-form-item>
        </div>
      </el-form-item>
    </el-form>
    <!-- <el-dialog :visible.sync="dialogVisible">
      <img
        width="100%"
        :src="dialogImageUrl"
        alt=""
      >
    </el-dialog> -->
    <el-image-viewer
      v-if="dialogVisible"
      :url-list="[dialogImageUrl]"
      :on-close="closeImg"
      append-to-body
      :z-index="100000"
    />
  </div>
</template>

<script>
import { uploadFile } from '@/api/qual';
import ElImageViewer from 'element-ui/packages/image/src/image-viewer';

export default {
  name: 'CorComponent',
  components: { ElImageViewer },
  filters: {
    formatDate(value) {
      if (value > 0) {
        const date = new Date(value);
        const y = date.getFullYear();
        let MM = date.getMonth() + 1;
        MM = MM < 10 ? `0${MM}` : MM;
        let d = date.getDate();
        d = d < 10 ? `0${d}` : d;
        return `${y}-${MM}-${d}`;
      }
      return '';
    },
  },
  props: {
    /* 标准资质信息 */
    companyQualData: {
      type: Object,
      default: () => {},
    },
    /* 保存后信息 */
    corporationQualifications: {
      type: Array,
      default: () => [],
    },
    hostName: {
      type: String,
      default: 'http://t-upload.ybm100.com',
    },
    isDetail: {
      type: Boolean,
      default: true,
    },
    qualStateStr: {
      type: Number,
      default: null,
    },
    isHistory: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      onColor: true,
      options: { disabledDate: time => time.getTime() < Date.now() },
      mergeData: {},
      corData: [],
      propsData: [],
      dialogImageUrl: '',
      dialogVisible: false,
      corRules: {},
    };
  },
  watch: {
    companyQualData: {
      immediate: true,
      handler(newVale) {
        this.$nextTick(() => {
          this.propsData = newVale.details ? JSON.parse(JSON.stringify(newVale.details)) : [];
          this.initData();
        });
      },
    },
    corporationQualifications: {
      immediate: true,
      handler(newVale) {
        this.$nextTick(() => {
          this.corData = newVale;
        });
      },
    },
  },
  created() {},
  methods: {
    closeImg() {
      this.dialogVisible = false;
    },
    setLongTime(val, start, idStr) {
      this.mergeData[idStr].longTermVal = !this.mergeData[idStr].longTermVal;
      if (start) {
        this.mergeData[idStr].endDateVal = '';
        this.$set(this.mergeData, `${idStr}time`, start);
      }
    },
    changeDataTime(start, end, idStr) {
      if (start && end && start > end) {
        end = '';
        this.$message.warning('结束时间不能小于开始时间');
        this.mergeData[idStr].endDateVal = '';
      } else if (start && end && !this.mergeData[idStr].longTermVal) {
        this.$set(this.mergeData, `${idStr}time`, start);
      } else if (start && this.mergeData[idStr].longTermVal) {
        this.$set(this.mergeData, `${idStr}time`, start);
      }
    },
    initData() {
      const that = this;
      this.mergeData = {};
      this.propsData.forEach((item, index) => {
        const nam = item.id;
        item.nameVal = '';
        item.bankName = '';
        item.subBankName = '';
        item.codeVal = '';
        item.startDateVal = '';
        item.endDateVal = '';
        item.longTermVal = null;
        item.state = '';
        item.remarks = '';
        item.urlVal = [];
        item.isUpload = false;
        that.corRules[`${item.id}name`] = [
          { required: true, message: '请输入名称', trigger: 'blur' },
          { min: 1, max: 50, message: '长度在 1 到 50 个字符', trigger: 'blur' },
        ];
        that.corRules[`${item.id}bank`] = [
          { required: true, message: '请输入企业对公账户开户银行', trigger: 'blur' },
          { min: 1, max: 300, message: '长度在 1 到 300 个字符', trigger: 'blur' },
        ];
        that.corRules[`${item.id}subBank`] = [
          { required: true, message: '请输入企业对公账户开户支行', trigger: 'blur' },
          { min: 1, max: 50, message: '长度在 1 到 50 个字符', trigger: 'blur' },
        ];
        that.corRules[`${item.id}code`] = [
          { required: true, message: '请输入证件号', trigger: 'blur' },
          { min: 1, max: 50, message: '长度在 1 到 50 个字符', trigger: 'blur' },
        ];
        that.corRules[`${item.id}imgList`] = [
          { required: true, message: '请上传基础图片', trigger: 'change' },
        ];
        that.corRules[`${item.id}time`] = [
          { required: true, message: '请选择有效期', trigger: 'change' },
        ];
        for (let i = 0; i < that.corData.length; i++) {
          const ite = that.corData[i];
          if (item.id === ite.qualificationsDetailId) {
            item.expireState = ite.expireState;
            item.nameVal = ite.legalPersonName;
            item.bankName = ite.bankName;
            item.subBankName = ite.subBankName;
            item.codeVal = ite.code;
            item.startDateVal = ite.startDate > 0 ? ite.startDate : '';
            item.endDateVal = ite.endDate > 0 ? ite.endDate : '';
            item.longTermVal = !!ite.longTerm;
            item.state = ite.state;
            item.remarks = ite.remarks;
            item.urlVal = [];
            const urlAry = ite.url.split(',');
            urlAry.forEach((itAry, indAry) => {
              if (itAry) {
                item.urlVal.push({
                  name: itAry.substring(itAry.length - 6),
                  url: itAry,
                  uid: index + indAry,
                });
              }
            });
            item.urlVal.length === ite.maxImg ? (item.isUpload = true) : (item.isUpload = false);
            that.$set(that.mergeData, `${nam}name`, ite.legalPersonName);
            that.$set(that.mergeData, `${nam}bank`, ite.bankName);
            that.$set(that.mergeData, `${nam}subBank`, ite.subBankName);
            that.$set(that.mergeData, `${nam}code`, ite.code);
            that.$set(that.mergeData, `${nam}imgList`, ite.url);
            that.$set(that.mergeData, `${nam}time`, ite.startDate > 0 ? ite.startDate : '');
          }
        }
        that.$set(that.mergeData, nam, JSON.parse(JSON.stringify(item)));
        that.$emit('setHeight');
      });
      // console.log(*********, this.propsData);
    },
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.url;
      this.dialogVisible = true;
    },
    beforeAvatarUpload(file) {
      const isLt1M = file.size / 1024 / 1024 < 1;
      if (!isLt1M) {
        this.$message.error('上传文件大小不能超过 1MB!');
      }
      return isLt1M;
    },
    uploadImg(file, index, idStr) {
      const that = this;
      uploadFile(file)
        .then((res) => {
          if (res.code === '200') {
            file.onSuccess();
            that.mergeData[idStr].urlVal.push({
              name: file.file.name,
              url: `${that.hostName}/${res.data}`,
              uid: file.file.uid,
            });
            that.$set(that.mergeData, `${idStr}imgList`, res.data);
            that.mergeData[idStr].urlVal.length === that.mergeData[idStr].maxImg
              ? (that.mergeData[idStr].isUpload = true)
              : (that.mergeData[idStr].isUpload = false);
          } else {
            file.onError();
            that.$message.error('图片上传失败');
          }
        })
        .catch(() => {
          file.onError();
        });
    },
    handleRemove(file, fileList, idStr) {
      fileList.forEach((item, index) => {
        if (item.uid === file.uid) {
          fileList.splice(index, 1);
        }
      });
      if (fileList.length < 1) {
        this.$set(this.mergeData, `${idStr}imgList`, '');
      }
      this.mergeData[idStr].isUpload = false;
    },
    fileChange(file) {
      // this.$refs[idStr][0].clearFiles()
      // this.$set(this.mergeData[idStr], 'urlVal', this.mergeData[idStr].urlVal)
    },
    setValue(val, env) {
      this.$set(this.mergeData, env, val);
    },
    processData() {
      const that = this;
      const sendData = [];
      const mergeDataAry = Object.keys(this.mergeData);
      mergeDataAry.forEach((itemd) => {
        const data = that.mergeData[itemd];
        if (data && data.id) {
          if (
            data.legalPersonName
            || (data.isNeedCode && data.codeVal)
            || (data.isNeedTime && data.startDateVal && data.startDateVal > 0)
            || data.urlVal
            || (!data.longTermVal && data.endDateVal && data.endDateVal > 0)
          ) {
            const obj = {};
            obj.qualificationsDetailId = data.id;
            obj.longTerm = data.longTermVal ? 1 : 0;
            obj.name = data.name;
            obj.legalPersonName = data.nameVal;
            obj.bankName = data.bankName;
            obj.subBankName = data.subBankName;
            obj.code = data.codeVal;
            obj.startDate = data.startDateVal > 0 ? data.startDateVal : '';
            obj.endDate = data.endDateVal > 0 ? data.endDateVal : '';
            obj.url = '';
            if (data.urlVal.length > 0) {
              data.urlVal.forEach((item) => {
                obj.url += `${item.url},`;
              });
            } else {
              obj.url = '';
            }
            if (data.longTermVal) {
              obj.endDate = '';
            }
            if (
              (obj.code && data.isNeedCode)
              || (obj.startDate && data.isNeedTime)
              || (obj.endDate && !data.longTermVal)
              || obj.url
            ) {
              sendData.push(obj);
            }
          }
        }
      });
      return sendData;
    },
    checkBankVal(data) {
      if (data) {
        const name = data.legalPersonName.substring(0, 5);
        const basicName = localStorage.getItem("basicName") || "";
        console.log(name, basicName, '校验企业名称');
        if (basicName && (name == basicName.substring(0, 5))) {
          return true;
        } else {
          return false;
        }
      }
    },
    submitForm(f) {
      if (f) {
        const sendData = this.processData();
        this.$emit('formCor', sendData, 1);
      } else {
        this.$refs.corData.validate((valid, rule) => {
          if (valid) {
            const sendData = this.processData();
            console.log(sendData, 'sendDatasendData');
            const bsData = sendData.filter(item => item.name === "企业对公账户信息");
            if (bsData) {
              if (!this.checkBankVal(bsData[0])) {
                this.$message.warning({
                  message: "“对公账户名称”需填写企业名称，请修改后重新提交",
                  type: 'warning',
                  offset: '60',
                });
                return;
              };
            }
            this.$emit('formCor', sendData);
          } else {
            const validAry = rule[Object.keys(rule)[0]];
            const msgName = validAry[0].message;
            // const refs = validAry[0].field
            this.$message.warning({
              message: msgName,
              type: 'warning',
              offset: '60',
            });
            this.$emit('formCor', false);
          }
        });
      }
    },
  },
};
</script>

<style lang="scss">
.corComponentOnColor {
  .el-input__inner {
    border-color: orange;
  }
}
.corComponentOnColorRed {
  .el-input__inner {
    border-color: red;
  }
}
</style>
<style scoped lang="scss">
.cor-list-box {
  .expireState {
    padding: 0 3px;
    color: red;
    border: 1px solid red;
    line-height: 26px;
    height: 26px;
    border-radius: 3px;
    margin-left: 5px;
  }
  .orange {
    color: orange;
    border-color: orange;
  }
  .list-remark {
    color: #666666;
    font-size: 12px;
  }
  .red-i {
    color: red;
    padding-right: 5px;
  }
  ::v-deep   .hide .el-upload--picture-card {
    display: none;
  }
}
.width60 {
  width: 60%;
}
.width70 {
  width: 70%;
}
.avatar-uploader ::v-deep  .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  background: rgba(0, 0, 0, 0.02);
  width: 126px;
  height: 126px;
  line-height: 126px;
}
.avatar-uploader ::v-deep  .el-upload:hover {
  border-color: #409eff;
}
.avatar-uploader ::v-deep  .el-upload__tip {
  margin-top: 0;
  color: #999999;
  font-size: 12px;
}
.avatar-uploader-icon {
  font-size: 28px;
  color: #666666;
  width: 126px;
  height: 126px;
  line-height: 126px;
  text-align: center;
}
.avatar {
  width: 126px;
  height: 126px;
  display: block;
}
.avatar-uploader ::v-deep  .el-upload-list--picture-card .el-upload-list__item {
  width: 126px;
  height: 126px;
}
</style>
