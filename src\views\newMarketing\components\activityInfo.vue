<template>
  <div>
    <div class="Fsearch">
      <p class="activityNav">
        <span>已选择的主题：</span>
        <span class="activitySubmit">{{ activityInfo.actName }}</span>
      </p>
      <div v-if="activityInfo.actName">
        <p class="activityNav">
          <span>仅指定商品可参与：</span>
          <span style="margin:0 20px 0 6px;color: #4183d5;cursor: pointer;" @click="download()"
            v-if="activityInfo.skuLimitType && Number(activityInfo.skuLimitType.code) === 1">
            <i class="el-icon-download"></i><a @click="download(1)">下载指定商品</a>
          </span>
          <span style="margin:0 20px 0 6px;color: #4183d5;cursor: pointer;" @click="download()"
            v-else-if="activityInfo.skuLimitType && Number(activityInfo.skuLimitType.code) === 2">
            <i class="el-icon-download"></i><a @click="download(1)">下载指定商品</a>
          </span>
          <span v-else>全部商品</span>

        </p>
        <div v-for="(item, index) in customerGroupVO.contentBundleDescriptions" :key="index">
          <p v-for="(one, index1) in item" :key="index1">{{ one }}</p>
        </div>
        <p v-if="customerGroupVO.specifyUserDescription">
          {{ customerGroupVO.specifyUserDescription }}
        </p>
        <p v-if="customerGroupVO.id">人群ID：{{ customerGroupVO.id }}</p>
        <div class="tipsBox">
          <div class="icon">
            <p><i class="el-icon-warning-outline" style=""></i></p>
          </div>
          <div class="font">
            <p style="color: #3c93ee;" v-for="(item, i) in activityInfo.description.split('<br/>')" :key="i">
              {{ item }}
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  data() {
    return {}
  },
  props: {
    activityInfo: {
      type: Object,
      default: function () {
        return {}
      }
    },
    customerGroupVO: {
      type: Object,
      default: function () {
        return {}
      }
    }
  },
  methods: {
    download() {
      let url = `${process.env.VUE_APP_BASE_API}/report/groupbuying/apply/canReportSku?frameReportId=${this.activityInfo.frameReportId}`
      this.$message.success('下载成功')
      window.open(url);
    }
  }
}
</script>
<style scoped lang="scss">
.Fsearch {
  padding-right: 0px;
}

.activityNav {
  margin-left: -15px;
  display: flex;
  align-items: center;

  .activitySubmit {
    margin-left: 40px;
    height: 32px;
    line-height: 32px;
    width: calc(100% - 50px - 90px);
    background: #F9F9F9;
    display: inline-block;
    border: 1px solid #E0E0E0;
    border-radius: 4px;
    padding: 0 20px;
  }
}

.tipsBox {
  margin-left: -15px;
  border-radius: 4px;
  background: #FFF9F0;
  border: 1px solid #FFE6C0;
  color: #B0610D;
  display: flex;
  justify-content: flex-start;

  .el-icon-warning-outline {
    margin: 0 10px;
    display: inline-block;
    height: 19px;
    line-height: 19px;
    padding-top: 2px;
  }
}
</style>