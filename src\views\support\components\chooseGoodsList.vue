<template>
  <div class="choose">
    <el-dialog title="选择商品" :visible.sync="dialogFormVisible" width="80%">
      <div class="choose-info">
        <div class="padding-box">
          <div class="goods-detail">
            <h3>{{ sendSku.commonName }}/{{ sendSku.spec }}</h3>
            <p>
              <span v-if="sendSku.approvalNumber">批准文号: {{ sendSku.approvalNumber }}</span>
              <span v-if="sendSku.code">条码: {{ sendSku.code }}</span>
              <span v-if="sendSku.manufacturer">生产厂家: {{ sendSku.manufacturer }}</span>
            </p>
          </div>
          <p class="goods-tip">*以下列表中，请选择商品信息一致的商品(规格数字一样描述不一样的，可视为一致)，如果无匹配的商品，请从新增上架途径单独上架</p>
        </div>
        <ul class="goods-ul">
          <li
            v-for="(item, index) in goodsDetail"
            :key="index"
            :class="[
              index === chooseIndex ? 'goods-li choose-li' : 'goods-li',
              item.result === 1 ? '' : 'disabled-li'
            ]"
            @click="chooseList(item, index)"
          >
            <div class="img-box">
              <!-- @click.stop="lookImg(item.imageUrl)" -->
              <!-- <img :src="item.imageUrl" alt /> -->
              <el-image :src="item.imageUrl" :preview-src-list="[item.imageUrl]" @click.prevent>
                <div slot="error" class="image-slot">
                  <el-image src="https://oss-ec.ybm100.com/ybm/product/defaultPhoto.jpg" />
                </div>
              </el-image>
            </div>
            <!-- <div class="con-box">
              <h4>{{ item.showName }}</h4>
              <p>ID：{{ item.standardProductId }}</p>
              <p>条码：{{ item.code }}</p>
              <p>规格：{{ item.spec }}</p>
              <p>{{ item.approvalNumber }}</p>
              <p>{{ item.manufacturer }}</p>
            </div>-->
            <div class="con-box">
              <h4 v-if="item.showName">{{item.brand}} {{ item.showName }}</h4>
              <p v-if="item.standardProductId">ID：{{ item.standardProductId }}</p>
              <p v-if="item.spec">规格：{{ item.spec }}</p>
              <el-tooltip
                class="item"
                effect="dark"
                :content="item.manufacturer+(item.entrustedManufacturer?'('+item.entrustedManufacturer+')':'')"
                placement="top-start"
              >
                <p>
                  <span v-if="item.manufacturer">生产厂家：{{ item.manufacturer }}</span>
                  <span v-if="item.entrustedManufacturer">({{ item.entrustedManufacturer }})</span>
                </p>
              </el-tooltip>
              <el-tooltip
                class="item"
                effect="dark"
                :content="item.approvalNumber"
                placement="top-start"
              >
                <p v-if="item.approvalNumber">批准文号：{{ item.approvalNumber }}</p>
              </el-tooltip>
              <p v-if="item.code">条码：{{ item.code }}</p>
              <p v-if="item.drugClassification">处方类型：{{ item.drugClassification }}</p>
              <p v-if="item.storageCondition">存储条件：{{ item.storageCondition }}</p>
              <p v-if="item.term">有效期：{{ item.term }}</p>
              <p v-if="item.productUnit">包装单位：{{ item.productUnit }}</p>
              <p v-if="item.size">码数：{{ item.size }}</p>
              <p v-if="item.colour">颜色：{{ item.colour }}</p>
              <p v-if="item.flavor">口味：{{ item.flavor }}</p>
            </div>
            <p class="tip-box">
              <i class="el-icon-check" />
            </p>
            <p class="text-tip">
              {{
              item.result === 2
              ? '商品经营范围不在企业经营范围内'
              : item.result === 5
              ? '暂无销售权限'
              : ''
              }}
            </p>
          </li>
        </ul>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button size="small" @click="dialogFormVisible = false">取 消</el-button>
        <el-button type="primary" size="small" @click="sendData">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getMathList, sendMathReSet } from '../../../api/product';

export default {
  name: 'ChooseGoodsList',
  data() {
    return {
      dialogFormVisible: false,
      goodsDetail: [],
      chooseId: '',
      chooseIndex: '',
      sendIds: '',
      sendSkuId: '',
      sendRes: '',
      goods: {},
      sendSku: {},
    };
  },
  methods: {
    getChooseData(row, chooseId) {
      if (chooseId) {
        this.chooseId = chooseId;
      }
      if (row) {
        this.sendSku = row;
      }
      this.sendIds = row.id;
      this.sendSkuId = '';
      this.chooseIndex = '';
      getMathList({ id: row.id })
        .then((res) => {
          if (res.code === 0) {
            this.dialogFormVisible = true;
            this.goodsDetail = res.data || [];
            if (this.goodsDetail) {
              this.goodsDetail.forEach((item, index) => {
                if (row.standardProductId == item.standardProductId) {
                  this.sendSkuId = item.standardProductId;
                  this.chooseIndex = index;
                  this.goods = item;
                }
              });
            }
          } else {
            // this.$message.error(res.message)
            this.$message({
              type: 'error',
              message: res.message,
              offset: 60,
            });
            this.goodsDetail = [];
          }
        })
        .catch(() => {
          this.goodsDetail = [];
        });
    },
    chooseList(data, index) {
      if (data.result === 1) {
        this.sendSkuId = data.standardProductId;
        this.chooseIndex = index;
        this.goods = data;
      }
    },
    sendData() {
      if (this.sendIds && this.sendSkuId) {
        sendMathReSet({ id: this.sendIds, standardProductId: Number(this.sendSkuId) }).then(
          (res) => {
            if (res.code === 0) {
              // this.$message.success('选取成功')
              this.$message({
                type: 'success',
                message: '选取成功',
                offset: 60,
              });
              this.dialogFormVisible = false;
              this.$emit('resetList');
            } else {
              // this.$message.error(res.message)
              this.$message({
                type: 'error',
                message: res.message,
                offset: 60,
              });
            }
          },
        );
      } else {
        this.$message({
          type: 'warning',
          message: '请选择商品',
          offset: 60,
        });
      }
    },
  },
};
</script>

<style scoped lang="scss">
.choose {
  ::v-deep  .el-button--primary {
    background: #4183d5;
  }
  ::v-deep  .el-dialog__body {
    padding: 0 0 10px;
    height: 520px;
    overflow-y: scroll;
  }
  ::v-deep  .el-dialog__header {
    padding: 10px 16px;
    background: #f9f9f9;
  }
  ::v-deep  .el-dialog__headerbtn {
    top: 13px;
  }
  ul,
  li {
    list-style: none;
    margin: 0;
  }
  // .choose-info {
  //   max-height: 430px;
  //   overflow-y: scroll;
  // }
  .goods-detail {
    background: #fafafa;
    padding: 15px;
    margin-top: 10px;
    h3,
    p {
      padding: 0;
      margin: 0;
    }
    p {
      padding-top: 5px;
      font-size: 12px;
      color: #666666;
      span {
        margin-right: 10px;
      }
    }
  }
  .goods-tip {
    color: #ff8e00;
    font-size: 12px;
    padding: 8px 0;
    margin: 0;
  }
  .padding-box {
    padding: 0 15px;
  }
  .goods-ul {
    box-sizing: border-box;
    display: flex;
    justify-content: flex-start;
    flex-wrap: wrap;
    padding: 0 10px 0 10px;
    .goods-li {
      width: 19%;
      height: auto;
      border: 1px solid #efefef;
      margin: 10px 10px 0px 0;
      display: inline-block;
      overflow: hidden;
      position: relative;
      background: #f9f9f9;
      border-radius: 4px;
      .img-box {
        padding: 10px 0 10px 0;
        text-align: center;
        display: flex;
        justify-content: center;
        align-items: center;
        background: #ffffff;
        height: 235px;
        width: 100%;
        ::v-deep   .el-image {
          max-height: 235px;
        }
      }
      .con-box {
        padding: 10px 10px 15px;
        h4,
        p {
          margin: 0;
          padding: 0;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
        p {
          padding-top: 5px;
          font-size: 12px;
          color: #666666;
        }
      }
      .tip-box {
        display: none;
        width: 20px;
        height: 20px;
        background: #4183d5;
        border-radius: 50%;
        color: #ffffff;
        position: absolute;
        top: 5px;
        right: 10px;
        margin: 0;
        text-align: center;
      }
      .text-tip {
        display: none;
        width: 100%;
        height: 24px;
        background: #eeeeee;
        font-size: 12px;
        color: #666666;
        text-align: center;
        position: absolute;
        top: 0;
        left: 0;
        margin: 0;
        line-height: 24px;
      }
    }
    li.choose-li {
      border-color: #4183d5;
      .tip-box {
        display: block;
      }
    }
    li.none-right {
      margin-right: 0;
    }
    li.disabled-li {
      .text-tip {
        display: block;
      }
    }
  }
}
</style>
