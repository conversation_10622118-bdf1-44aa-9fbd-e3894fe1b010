<template>
  <div class="searchForm">
    <div class="formBox" :style="formHeight">
      <template v-for="(list, index) in colList">
        <el-row :gutter="20" :key="index" :elIndex="index">
          <el-col
            :span="item.colSpan || 24 / spanNum"
            v-for="(item, colIndex) in list"
            :key="colIndex"
            :colIndex="colIndex"
          >
            <template v-if="item.slotName" >
              <div class="el-input-group el-input-group--prepend" v-if="!item.isHidden">
                <div class="el-input-group__prepend">{{ item.label }}</div>
                <slot :name="'form-item-' + item.slotName" />
              </div>
            </template>
            <template v-else>
              <template v-if="item.component !== 'el-input'">
                <div class="el-input-group el-input-group--prepend">
                  <div class="el-input-group__prepend">{{ item.label }}</div>
                  <component
                    v-bind:is="item.component"
                    v-model="$attrs.model[item.prop]"
                    v-bind="item.attrs"
                    class="comSelect"
                    :style="{ width: item.width || undefined }"
                  >
                    <el-option
                      v-if="item.component === 'el-select'"
                      v-for="op in item.attrs.options"
                      :key="op.value"
                      :label="op.label"
                      :value="op.value"
                    />
                  </component>
                </div>
              </template>
              <template v-else>
                <component
                  v-bind:is="item.component"
                  v-model="$attrs.model[item.prop]"
                  v-bind="item.attrs"
                  :style="{ width: item.width || undefined }"
                >
                  <template slot="prepend">{{ item.label }}</template>
                </component>
              </template>
            </template>
          </el-col>
        </el-row>
      </template>
    </div>
    <div class="operation" v-if="hasOperation">
      <el-button
        v-if="hasOpenBtn"
        type="text"
        @click="openFormFlag = !openFormFlag"
        size="small"
        :icon="openFormFlag ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"
        >{{ openFormFlag ? '收起' : '展开' }}
      </el-button>
      <el-button type="primary" @click="submit" size="small">查询</el-button>
      <el-button @click="reset" size="small">重置</el-button>
    </div>
  </div>
</template>

<script>
export default {
  name: 'Index',
  props: {
    formItems: {
      require: true,
      type: Array,
      default: () => {
        return []
      }
    },
    spanNum: {
      type: Number,
      default: 4
    },
    hasOperation: {
      type: Boolean,
      default: true
    },
    hasOpenBtn: {
      type: Boolean,
      default: true
    },
    isOpen: {
      type: Boolean,
      default: false
    }
  },
  watch: {
    openFormFlag(value) {
      if (value) {
        this.formHeight = 'height:auto'
      } else {
        this.formHeight = 'height:84px'
      }
    }
  },
  computed: {
    colList() {
      const colList = []
      for (let i = 0; i < this.formItems.length; i += this.spanNum) {
        colList.push(this.formItems.slice(i, i + this.spanNum))
      }
      return colList
    }
  },
  data() {
    return {
      openFormFlag: false,
      formHeight: 'height:84px',
      initialFormModel: {}
    }
  },
  created() {
    if (!this.hasOpenBtn || this.isOpen) {
      this.openFormFlag = true
    }
    this.initialFormModel = JSON.parse(JSON.stringify(this.$attrs.model))
  },
  methods: {
    submit() {
      console.log('this.$attrs', this.$attrs)
      this.$emit('submit', this.$attrs.model)
    },
    reset() {
      this.$emit('reset', JSON.parse(JSON.stringify(this.initialFormModel)))
    },
    getInitialFormModel() {
      return JSON.parse(JSON.stringify(this.initialFormModel))
    }
  }
}
</script>

<style scoped lang="scss">
.searchForm {
  .formBox {
    overflow: hidden;
  }

  ::v-deep   .el-input-group__prepend {
    box-sizing: border-box;
    height: 30px;
    line-height: 30px;
    font-size: 12px;
    padding: 0 7px;
  }

  ::v-deep   .el-input {
    height: 32px;
    line-height: 32px;
    max-width: 300px;
  }

  ::v-deep   .el-input__inner {
    vertical-align: top;
    font-size: 12px;
    line-height: 32px;
    height: 32px;
  }

  ::v-deep   .el-input__inner.el-range-editor {
    //line-height: 28px;
    //height: 28px;
  }

  .el-row .el-col {
    padding-bottom: 12px;
  }

  ::v-deep   .el-input.is-focus .el-input__inner {
    border: 1px solid #dcdfe6;
  }

  ::v-deep   .el-select {
    display: table-cell;
    line-height: 30px;
    height: 30px;

    ::v-deep   .el-input__icon {
      line-height: 30px;
      height: 30px;
    }
  }

  ::v-deep   .el-cascader {
    display: table-cell;
    height: 32px;
    line-height: 30px;

    ::v-deep   .el-input__inner {
      border-top-left-radius: 0;
      border-bottom-left-radius: 0;
    }
  }

  ::v-deep   .el-input__icon {
    line-height: 30px;
  }

  ::v-deep   .el-date-editor {
    padding: 0 10px;
    width: 100%;
    .el-range__icon{
      width: 8%;
    }
    /* .el-range-input{
      font-size: 5px;
    } */
    ::v-deep   .el-input__icon {
      width: 6%;
      height: 30px;
      line-height: 30px;
    }

    ::v-deep   .el-range-input {
      width: 40%;
      height: 30px;
    }

    ::v-deep   .el-range-separator {
      height: 30px;
      line-height: 30px;
      padding: 0;
      width: 8%;
    }
  }

  .operation {
    text-align: end;
  }
}
</style>
