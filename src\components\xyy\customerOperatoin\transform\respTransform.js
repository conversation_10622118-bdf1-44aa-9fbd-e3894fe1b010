/**
 * created by <PERSON><PERSON><PERSON><PERSON><PERSON> in 2021/3/19
 */

function getCrowdDetail(res) {
  return res;
}
function getCrowdList(res) {
  return res;
}

function createCrowd(res) {
  return res;
}

// 省份和客户类型选
function provincesAndMerchantTypes(res) {
  const result = JSON.parse(JSON.stringify(res));
  delete result.data;
  const { data } = res;
  if (data) {
    const resultData = {
      allMerchantTypes: [],
      region: [],
      userLabels:[]
    };
    const { allMerchantTypes, branchs,userLabels } = data;
    if (allMerchantTypes && allMerchantTypes.length > 0) {
      allMerchantTypes.forEach((item) => {
        resultData.allMerchantTypes.push({
          id: item.key,
          value: item.value,
        });
      });
    }
    if (branchs && branchs.length > 0) {
      branchs.forEach((item) => {
        resultData.region.push({
          areaCode: item.areaCode,
          areaName: item.areaName,
          level: item.level,
        });
      });
    }
    if(userLabels && userLabels.length > 0){
      resultData.userLabels = userLabels
    }
    result.data = resultData;
  }
  return result;
}
// 市区转换
function provinceRegions(res) {
  const result = JSON.parse(JSON.stringify(res));
  delete result.data;
  const { data } = res;
  if (data) {
    result.data = [];
    const { twoChildAreasDto } = data;
    if (twoChildAreasDto) {
      const { twoChildAreas } = twoChildAreasDto;
      if (twoChildAreas && twoChildAreas.length > 0) {
        twoChildAreas.forEach((item) => {
          const itemObj = {
            areaCode: item.areaCode,
            areaName: item.areaName,
            level: item.level,
            childRegion: [],
          };
          if (item.childList && item.childList.length > 0) {
            item.childList.forEach((child) => {
              itemObj.childRegion.push({
                areaCode: child.areaCode,
                areaName: child.areaName,
                level: child.level,
              });
            });
          }
          result.data.push(itemObj);
        });
      }
    }
  }
  return result;
}
function uploadCrowdCustomer(res) {
  return res;
}
function getImportMerchantList(res) {
  return res;
}

function getMerchantList(res) {
  return res;
}

function deleteImportMerchantList(res) {
  return res;
}

export default {
  getCrowdDetail,
  getCrowdList,
  provincesAndMerchantTypes,
  provinceRegions,
  createCrowd,
  uploadCrowdCustomer,
  getImportMerchantList,
  getMerchantList,
  deleteImportMerchantList,
};
