import request from '@/utils/request';

// 省域接口查询
export function branchsList(params) {
  return request({
    url: '/branch/list/v2/new',
    method: 'get',
    data: params,
  });
}
export function branchsListQuery(params) {
  return request({
    url: '/branch/list',
    method: 'get',
    data: params,
  });
}

// 模板查询列表
export function freightTemplateList(params) {
  return request({
    url: '/shipping/v1/list/',
    method: 'post',
    data: params,
  });
}

// 新增模板
export function addShipping(params) {
  return request({
    url: '/shipping/v1/operation/',
    method: 'post',
    data: params,
  });
}

// 查询模板详情
export function searchTemplateData(params) {
  return request({
    url: `/shipping/v1/detail?tempId=${params.tempId}`,
    method: 'get',
    data: params,
  });
}

// 删除运费模板
export function deleteFreightTemplate(params) {
  return request({
    url: '/shipping/v1/delete',
    method: 'get',
    params,
  });
}

export function getAreaCode(params) {
  return request({
    url: '/shipping/getAreaCode',
    method: 'get',
    params,
  });
}

// 运费模版区域列表
export function apiQuerySaleableArea(data) {
  return request({
    url: '/busArea/querySaleableArea',
    method: 'post',
    data,
  });
}