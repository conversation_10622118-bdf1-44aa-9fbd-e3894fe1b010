<template>
  <div class="error-edit-page">
    <div class="divBox">
      <div class="bottom-info">
        <div class="topBox">
          <span>商品纠错</span>
          <div>
            <el-button size="small" @click="onCancel()">取消</el-button>
            <el-button
              size="small"
              type="primary"
              @click="onSubmit"
              :disabled="formType === 'detail'"
              >提交</el-button
            >
          </div>
        </div>
        <div style="margin-left: 20%">
          <el-form
            class="fix-item-width"
            size="small"
            label-position="right"
            :model="formData"
            ref="form"
            :rules="rules"
          >
            <el-form-item
              prop="correctType"
              label="选择纠错类型"
              label-width="200px"
            >
              <el-radio-group v-model="formData.correctType">
                <el-radio :label="1" :disabled="formType === 'detail'"
                  >商品基础信息</el-radio
                >
                <el-radio :label="2" :disabled="formType === 'detail'"
                  >商品图片及详情图纠错</el-radio
                >
              </el-radio-group>
            </el-form-item>

            <el-form-item
              prop="applicationField"
              label="选择纠错信息"
              label-width="200px"
            >
              <el-checkbox
                v-model="formData.applicationField"
                v-for="item in cateGoryOptions"
                :key="item.name"
                :label="item.code.toString()"
                :disabled="formType === 'detail'"
                >{{ item.name }}</el-checkbox
              >
            </el-form-item>

            <el-form-item
              prop="productName"
              :label="
                this.cateGoryParams.firstCategory.toString() === '100005'
                  ? '请填写正确的医疗器械名称'
                  : '请填写正确的商品名称'
              "
              label-width="200px"
              v-if="getNameNeedHide('productName')"
            >
              <el-input
                v-model="formData.productName"
                style="width: 300px"
                :disabled="formType === 'detail'"
              ></el-input>
            </el-form-item>

            <el-form-item
              v-if="getBrandNeedHide('brand')"
              prop="brand"
              label="请填写正确的品牌"
              label-width="200px"
            >
              <el-input
                v-model="formData.brand"
                style="width: 300px"
                :disabled="formType === 'detail'"
              ></el-input>
            </el-form-item>

            <el-form-item
              v-if="getaApprovalNumber('approvalNumber')"
              prop="approvalNumber"
              :label="
                this.cateGoryParams.firstCategory.toString() === '100005'
                  ? '请填写正确的医疗器械注册证或备案凭证编号'
                  : '请填写正确的批准文号'
              "
              label-width="200px"
            >
              <el-input
                v-model="formData.approvalNumber"
                style="width: 300px"
                :disabled="formType === 'detail'"
              ></el-input>
            </el-form-item>

            <el-form-item
              v-if="getscxkzhbapzbhNeedHide('manufacturingLicenseNo')"
              prop="manufacturingLicenseNo"
              :label="
                this.cateGoryParams.firstCategory === 100005
                  ? '请填写正确的医疗器械注册证或备案凭证编号'
                  : '请填写正确的生产许可证或备案凭证编号'
              "
              label-width="200px"
            >
              <el-input
                v-model="formData.manufacturingLicenseNo"
                style="width: 300px"
                :disabled="formType === 'detail'"
              ></el-input>
            </el-form-item>

            <el-form-item
              v-if="getSccjNeedHide('manufacturer')"
              prop="manufacturer"
              label="请填写正确的生产厂家"
              label-width="200px"
            >
              <el-input
                v-model="formData.manufacturer"
                style="width: 300px"
                :disabled="formType === 'detail'"
              ></el-input>
            </el-form-item>
            <el-form-item
              v-if="getSpecNeedHide('spec')"
              prop="spec"
              label="请填写正确的规格"
              label-width="200px"
            >
              <el-input
                v-model="formData.spec"
                :disabled="formType === 'detail'"
                style="width: 300px"
              ></el-input>
            </el-form-item>
            <el-form-item
              v-if="getCodeNeedHide('code')"
              prop="code"
              label="请填写正确的商品条码"
              label-width="200px"
            >
              <el-input
                v-model="formData.code"
                style="width: 300px"
                :disabled="formType === 'detail'"
              ></el-input>
            </el-form-item>
            <el-form-item
              v-if="getOtherNameNeedHide('aliasName')"
              prop="aliasName"
              label="请填写正确的别名"
              label-width="200px"
            >
              <el-input
                v-model="formData.aliasName"
                style="width: 300px"
                :disabled="formType === 'detail'"
              ></el-input>
            </el-form-item>

            <el-form-item
              v-if="getCDNeedHide('producer')"
              prop="producer"
              label="请填写正确的产地"
              label-width="200px"
            >
              <el-input
                v-model="formData.producer"
                style="width: 300px"
                :disabled="formType === 'detail'"
              ></el-input>
            </el-form-item>
			<el-form-item
              v-if="getFilingAuthor('filingsAuthor')"
              prop="filingsAuthor"
              label="请填写正确的备案人信息"
              label-width="200px"
            >
              <el-input
                v-model="formData.filingsAuthor"
                style="width: 300px"
                :disabled="formType === 'detail'"
              ></el-input>
            </el-form-item>
			<el-form-item
              v-if="getProductionAddress('productionAddress')"
              prop="productionAddress"
              label="请填写正确的生产厂家地址"
              label-width="200px"
            >
              <el-input
                v-model="formData.productionAddress"
                style="width: 300px"
                :disabled="formType === 'detail'"
              ></el-input>
            </el-form-item>
			<el-form-item
              v-if="getMarketAuthorAddress('marketAuthorAddress')"
              prop="marketAuthorAddress"
              label="请填写正确的上市许可持有人地址"
              label-width="200px"
            >
              <el-input
                v-model="formData.marketAuthorAddress"
                style="width: 300px"
                :disabled="formType === 'detail'"
              ></el-input>
            </el-form-item>
			<el-form-item
              v-if="getMarketAuthor('marketAuthor')"
              prop="marketAuthor"
              label="请填写正确的上市许可持有人"
              label-width="200px"
            >
              <el-input
                v-model="formData.marketAuthor"
                style="width: 300px"
                :disabled="formType === 'detail'"
              ></el-input>
            </el-form-item>
            <el-form-item
              prop="applicationReason"
              label="申请理由"
              label-width="200px"
            >
              <el-input
                v-model="formData.applicationReason"
                style="width: 300px"
                :disabled="formType === 'detail'"
                maxlength="100"
                show-word-limit
              ></el-input>
            </el-form-item>

            <!-- 凭证器械 -->
            <el-form-item
              v-if="
                formData.correctType === 1 &&
                this.cateGoryParams.firstCategory.toString() === '100005'
              "
              prop="voucherImage"
              label="凭证"
              label-width="200px"
            >
              <uploadModuleTwo
                :imagesPermision="getImageModulePermissionWithqx()"
                ref="voucherImage"
                :mainType="'voucherImage'"
                :formType="formType"
                @imgback="imgback"
                @initIMG="initIMG"
              ></uploadModuleTwo>
            </el-form-item>

            <!-- 凭证非器械 -->
            <el-form-item
              v-if="
                formData.correctType === 1 &&
                this.cateGoryParams.firstCategory.toString() !== '100005'
              "
              prop="voucherImage"
              label="凭证"
              label-width="200px"
            >
              <uploadModule
                :imagesPermision="getImageModulePermission()"
                ref="voucherImage"
                :mainType="'voucherImage'"
                :formType="formType"
                @imgback="imgback"
                @initIMG="initIMG"
              ></uploadModule>
            </el-form-item>

            <!-- 请上传正确的商品图片 -->
            <el-form-item
              v-if="formData.correctType !== 1 && getGoodsImageNeedHide()"
              prop="packageImage"
              label="请上传正确的商品图片"
              label-width="200px"
            >
              <uploadModule
                :imagesPermision="getImageModulePermission()"
                ref="packageImage"
                :mainType="'packageImage'"
                :formType="formType"
                @initIMG="initIMG"
                @imgback="imgback"
              ></uploadModule>
            </el-form-item>
            <!-- 请上传正确的商品图片 -->
            <el-form-item
              v-if="formData.correctType !== 1 && getGoodsDetailImageNeedHide()"
              prop="instructionImage"
              label="请上传正确的商品详情图"
              label-width="200px"
            >
              <uploadModule
                :imagesPermision="getImageModulePermission()"
                ref="instructionImage"
                :mainType="'instructionImage'"
                :formType="formType"
                @imgback="imgback"
                @initIMG="initIMG"
                imgType=""
              ></uploadModule>
            </el-form-item>
          </el-form>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { deepClone } from '@/utils/util.js'
import uploadModule from '../components/uploadmodule.vue'
import uploadModuleTwo from '../components/uploadmoduleTwo.vue'
import {
  correctionCategory,
  correctionSubmit,
  correctionDetail
} from '@/api/product'
export default {
  name: 'errorEdit',
  components: {
    uploadModule,
    uploadModuleTwo
  },
  data() {
    return {
      firstChange: true,
      oldData: undefined,
      formData: {
        correctType: 1,
        applicationField: [],
        productName: '',
        brand: '',
        applicationReason: '',
        approvalNumber: '',
        manufacturer: '',
        spec: '',
        code: '',
        aliasName: '',
        producer: '',
		productionAddress: '',
		marketAuthorAddress: '',
		marketAuthor: '',
		filingsAuthor: '',
        manufacturingLicenseNo: '',
        voucherImage: undefined,
        packageImage: undefined,
        instructionImage: undefined
      },
      formType: undefined,
      pageDetail: undefined,

      bigImgUrlPrefix: '',
      cateGoryParams: {
        firstCategory: '',
        correctType: ''
      },
      cateGoryOptions: [],
      rules: {
        applicationField: [
          { required: true, message: '请选择纠错信息', trigger: 'blur' }
        ],
        productName: [
          { required: true, message: '请输入商品名称', trigger: 'blur' }
        ],
        brand: [{ required: true, message: '请输入品牌', trigger: 'blur' }],
        applicationReason: [
          { required: true, message: '请输入申请理由', trigger: 'blur' }
        ],
        approvalNumber: [
          { required: true, message: '请输入批准文号', trigger: 'blur' }
        ],
        manufacturer: [
          { required: true, message: '请输入生产厂家', trigger: 'blur' }
        ],
        spec: [{ required: true, message: '请输入规格', trigger: 'blur' }],
        code: [{ required: true, message: '请输入商品编码', trigger: 'blur' }],
        producer: [{ required: true, message: '请输入产地', trigger: 'blur' }],
        aliasName: [{ required: true, message: '请输入别名', trigger: 'blur' }],
		filingsAuthor: [{ required: true, message: '请输入备案人信息', trigger: 'blur' }],
		marketAuthorAddress: [{ required: true, message: '请输入上市许可持有人地址', trigger: 'blur' }],
		marketAuthor: [{ required: true, message: '请输入上市许可持有人', trigger: 'blur' }],
		productionAddress: [{ required: true, message: '请输入生产厂家地址', trigger: 'blur' }],
        manufacturingLicenseNo: [
          {
            required: true,
            message: '请输入医疗器械注册证或备案凭证编号',
            trigger: 'blur'
          }
        ]
      }
    }
  },

  watch: {
    'formData.correctType': {
      handler() {
        if (this.formType !== 'detail' && this.firstChange === false) {
          this.formData = Object.assign(this.formData, {
            applicationField: [],
            productName: '',
            brand: '',
            applicationReason: '',
            approvalNumber: '',
            manufacturer: '',
            spec: '',
            code: '',
            aliasName: '',
            producer: '',
			productionAddress: '',
			marketAuthorAddress: '',
			marketAuthor: '',
			filingsAuthor: '',
            manufacturingLicenseNo: '',
            voucherImage: undefined,
            packageImage: undefined,
            instructionImage: undefined
          })
        }
        this.firstChange = false

        this.apicorrectionCategory()
      },
      deep: true
    }
  },

  activated() {
    this.init()
  },

  methods: {
    init() {
      this.formData = {
        correctType: 1,
        applicationField: [],
        productName: '',
        brand: '',
        applicationReason: '',
        approvalNumber: '',
        manufacturer: '',
        spec: '',
        code: '',
        aliasName: '',
        producer: '',
		productionAddress: '',
		marketAuthorAddress: '',
		filingsAuthor: '',
		marketAuthor: '',
        manufacturingLicenseNo: '',
        voucherImage: undefined,
        packageImage: undefined,
        instructionImage: undefined
      }
      this.oldData = undefined
      this.formType = undefined
      this.pageDetail = undefined

      this.bigImgUrlPrefix = ''
      this.cateGoryParams = {
        firstCategory: '',
        correctType: ''
      }
      this.cateGoryOptions = []

      this.loopSetInitImage();

      this.formType = this.$route.query.type
      if (this.$route.query.from === 'errorEditList') {
        this.apicorrectionDetail(this.$route.query.id)
      } else {
        this.cateGoryParams.firstCategory = this.$route.query.firstCategory
        this.apicorrectionCategory()
      }
    },
    checkOldAndNew(oldData, formData) {
      const eque = _.isEqual(oldData, formData)
      return eque
    },
    onSubmit() {
      if (this.formType !== 'detail') {
        const ret = this.checkOldAndNew(this.oldData, this.formData)
        if (ret) {
          this.$message.warning('您没有任何修改,无法提交')
          return
        }
      }

      this.$refs.form.validate((valid) => {
        if (valid) {
          if (this.visiteFormData()) {
            let params = this.makeParamFormData()
            if (params === false) {
              return false
            }
            this.apiCorrectionSubmit(params)
            return true
          }
          return false
        }
        return false
      })
    },
    getImageModulePermission() {
      let arr = []
      if (this.getBrandNeedHide()) {
        arr.push(0)
      }
      if (this.getPzwhNeedHide()) {
        arr.push(1)
      }
      if (this.getSpecNeedHide()) {
        arr.push(2)
      }
      return arr
    },

    getImageModulePermissionWithqx() {
      let arr = []
      if (this.getBrandNeedHide()) {
        arr.push(0)
      }
      if (this.getSccjNeedHide()) {
        arr.push(1)
      }
      if (this.getSpecNeedHide()) {
        arr.push(2)
      }
      if (this.getylqxzczhbapzbhNeedHide()) {
        arr.push(3)
      }
      if (this.getscxkzhbapzbhNeedHide()) {
        arr.push(4)
      }
      return arr
    },

    initIMG(key, data) {
      this.oldData[key] = deepClone(data)
      this.formData[key] = data
    },
    onCancel() {
      const path = this.$route.fullPath
      window.closeTab(path)
    },
    imgback(type, fileData) {
      this.formData[type] = fileData
    },
    async apicorrectionCategory() {
      this.cateGoryParams.correctType = this.formData.correctType
      let params = Object.assign({}, this.cateGoryParams)
      let res = await correctionCategory(params)
      this.cateGoryOptions = res.data
    },
    applicationFieldChange(value) {},

    checkEmpty(noempty, key) {
      if (noempty === false) {
        this.formData[key] = undefined
      }
    },
    getaApprovalNumber(key) {
      const result =
        !this.getylqxzczhbapzbhNeedHide(key) && !this.getPzwhNeedHide(key)
      if (!this.getylqxzczhbapzbhNeedHide(key) && !this.getPzwhNeedHide(key)) {
        this.checkEmpty(result, key)
      }
      return this.getylqxzczhbapzbhNeedHide(key) || this.getPzwhNeedHide(key)
    },
	//生产厂家地址
	getProductionAddress(key) {
		const fkarr = ['109', '210', '310', '408']
		const result = this.checkHasCode(fkarr)
		this.checkEmpty(result, key)
		return result
	},
	//marketAuthor
	getMarketAuthor(key) {
		const fkarr = ['111']
		const result = this.checkHasCode(fkarr)
		this.checkEmpty(result, key)
		return result
	},
	//生产厂家地址
	getMarketAuthorAddress(key) {
		const fkarr = ['110']
		const result = this.checkHasCode(fkarr)
		this.checkEmpty(result, key)
		return result
	},
	//备案人
	getFilingAuthor(key) {
		const fkarr = ['501']
		const result = this.checkHasCode(fkarr)
		this.checkEmpty(result, key)
		return result
	},
    //商品名称是否显示
    getNameNeedHide(key) {
      const fkarr = ['101', '201', '301', '401']
      const result = this.checkHasCode(fkarr)
      this.checkEmpty(result, key)
      return result
    },
    //商品品牌是否显示
    getBrandNeedHide(key) {
      const fkarr = ['102', '202', '302', '402']
      const result = this.checkHasCode(fkarr)
      this.checkEmpty(result, key)
      return result
    },
    //批准文号是否显示
    getPzwhNeedHide(key) {
      const fkarr = ['103']
      const result = this.checkHasCode(fkarr)
      return result
    },
    //生产厂家是否显示
    getSccjNeedHide(key) {
      const fkarr = ['104', '205', '305', '403']
      const result = this.checkHasCode(fkarr)
      this.checkEmpty(result, key)
      return result
    },
    //商品规格是否显示
    getSpecNeedHide(key) {
      const fkarr = ['105', '206', '306', '404']
      const result = this.checkHasCode(fkarr)
      this.checkEmpty(result, key)
      return result
    },
    //商品条码是否显示
    getCodeNeedHide(key) {
      const fkarr = ['106', '207', '307', '405']
      const result = this.checkHasCode(fkarr)
      this.checkEmpty(result, key)
      return result
    },
    //医疗器械注册证或备案凭证编号是否显示
    getylqxzczhbapzbhNeedHide(key) {
      const fkarr = ['203']
      const result = this.checkHasCode(fkarr)
      return result
    },
    //生产许可证或备案凭证编号是否显示
    getscxkzhbapzbhNeedHide(key) {
      const fkarr = ['204']
      const result = this.checkHasCode(fkarr)
      this.checkEmpty(result, key)
      return result
    },
    //别名是否显示
    getOtherNameNeedHide(key) {
      const fkarr = ['303']
      const result = this.checkHasCode(fkarr)
      this.checkEmpty(result, key)
      return result
    },
    //产地是否显示
    getCDNeedHide(key) {
      const fkarr = ['304']
      const result = this.checkHasCode(fkarr)
      this.checkEmpty(result, key)
      return result
    },
    //商品图片是否显示
    getGoodsImageNeedHide(key) {
      const fkarr = ['107', '208', '308', '406']
      const result = this.checkHasCode(fkarr)
      // this.checkEmpty(result, key)
      return result
    },
    //商品详情图片是否显示
    getGoodsDetailImageNeedHide(key) {
      const fkarr = ['108', '209', '309', '407']
      const result = this.checkHasCode(fkarr)
      // this.checkEmpty(result, key)
      return result
    },

    //检测是否需要展示
    checkHasCode(arr) {
      for (
        let index = 0;
        index < this.formData.applicationField.length;
        index++
      ) {
        const element = this.formData.applicationField[index]
        const obj = arr.find((item) => {
          return item === element
        })
        if (obj) {
          return true
        }
      }
      return false
    },

    //提交
    apiCorrectionSubmit(params) {
      correctionSubmit(params).then((res) => {
        if (res.code !== 0) {
          this.$message.error(res.message)
          return
        }
        this.$message.success('提交成功')
        this.onCancel()
      })
    },

    //-------------------------------------提交校检--------------------------------------

    visiteFormData() {
      if (this.formData.correctType === 1) {
        let voucherImageresult =
          this.cateGoryParams.firstCategory.toString() === '100005'
            ? this.getIMGVisiteResultWithqx('voucherImage')
            : this.getIMGVisiteResult('voucherImage')
        if (voucherImageresult) {
          this.$message.warning('请先将上传凭证必选图片')
          return fasle
        }
      } else {
        let packageImageresult = this.getIMGVisiteResult('packageImage')
        if (packageImageresult) {
          this.$message.warning('请先将上传商品必选图片')
          return fasle
        }
        let instructionImageresult = this.getIMGVisiteResult('instructionImage')
        if (instructionImageresult) {
          this.$message.warning('请先将上传商品详情必选图片')
          return fasle
        }
      }
      return true
    },

    getIMGVisiteResult(type) {
      let fileObj = this.formData[type]
      let brandR = fileObj && fileObj.brandImage.length > 0 ? true : false
      let pzwhR = fileObj && fileObj.pzwhImage.length > 0 ? true : false
      let specR = fileObj && fileObj.specImage.length > 0 ? true : false
      const permissions = this.getImageModulePermission()
      if (brandR === false && permissions.includes(0)) {
        return true
      }
      if (pzwhR === false && permissions.includes(1)) {
        return true
      }
      if (specR === false && permissions.includes(2)) {
        return true
      }

      return false
    },

    getIMGVisiteResultWithqx(type) {
      let fileObj = this.formData[type]
      let brandR = fileObj && fileObj.brandImage.length > 0 ? true : false
      let sccjR = fileObj && fileObj.sccjImage.length > 0 ? true : false
      let specR = fileObj && fileObj.specImage.length > 0 ? true : false
      let pzwhR = fileObj && fileObj.pzwhImage.length > 0 ? true : false
      let scxkR = fileObj && fileObj.scxkImage.length > 0 ? true : false

      const permissions = this.getImageModulePermissionWithqx()
      if (brandR === false && permissions.includes(0)) {
        return true
      }
      if (sccjR === false && permissions.includes(1)) {
        return true
      }
      if (specR === false && permissions.includes(2)) {
        return true
      }
      if (pzwhR === false && permissions.includes(3)) {
        return true
      }
      if (scxkR === false && permissions.includes(4)) {
        return true
      }

      return false
    },

    makeParamFormData() {
      let params = {}
      for (let index = 0; index < Object.keys(this.formData).length; index++) {
        const key = Object.keys(this.formData)[index]
        if (
          this.formData[key] &&
          key !== 'voucherImage' &&
          key !== 'packageImage' &&
          key !== 'instructionImage' &&
          key !== 'applicationField'
        ) {
          params[key] = this.formData[key]
        }
      }
      params['applicationField'] = this.formData.applicationField.join(',')
      if (this.formData.correctType === 1) {
        if (this.cateGoryParams.firstCategory.toString() === '100005') {
          params['voucherImage'] = this.getImageUrlsWithqx('voucherImage')
        } else {
          params['voucherImage'] = this.getImageUrls('voucherImage')
        }
      } else {
        params['packageImage'] = this.getImageUrls('packageImage')
        params['instructionImage'] = this.getImageUrls('instructionImage')

        if(params['packageImage'] === ',,,' && this.getGoodsImageNeedHide()){
          this.$message.warning('请先上传商品图片')
            return false
        }
        if(params['instructionImage'] === ',,,' && this.getGoodsDetailImageNeedHide()){
          this.$message.warning('请先上传详情图片')
            return false
        }
      }

      params['barcode'] = this.$route.query.barCode
      params['firstCategory'] = this.cateGoryParams.firstCategory
      return params
    },

    //获取图片的url
    getImageUrls(type) {
      let imgObj = ''
      let fileObj = this.formData[type]
      let brandurl =
        fileObj && fileObj.brandImage.length > 0
          ? fileObj.brandImage[0].url
          : ''
      imgObj += brandurl + ','
      let pzwhurl =
        fileObj && fileObj.pzwhImage.length > 0 ? fileObj.pzwhImage[0].url : ''
      imgObj += pzwhurl + ','
      let specurl =
        fileObj && fileObj.specImage.length > 0 ? fileObj.specImage[0].url : ''
      imgObj += specurl + ','

      if (fileObj && fileObj.changeFileImage.length > 0) {
        let urls = fileObj.changeFileImage.map((item) => {
          return item.url
        })
        imgObj += urls
      }
      return imgObj
    },
    getImageUrlsWithqx(type) {
      let imgObj = ''
      let fileObj = this.formData[type]
      //品牌
      let brandurl =
        fileObj && fileObj.brandImage.length > 0
          ? fileObj.brandImage[0].url
          : ''
      imgObj += brandurl + ','
      //生产厂家
      let sccjurl =
        fileObj && fileObj.sccjImage.length > 0 ? fileObj.sccjImage[0].url : ''
      imgObj += sccjurl + ','

      //规格
      let specurl =
        fileObj && fileObj.specImage.length > 0 ? fileObj.specImage[0].url : ''
      imgObj += specurl + ','

      //医疗器械注册证或备案凭证
      let pzwhurl =
        fileObj && fileObj.pzwhImage.length > 0 ? fileObj.pzwhImage[0].url : ''
      imgObj += pzwhurl + ','

      //生产许可证或备案凭证
      let scxkurl =
        fileObj && fileObj.scxkImage.length > 0 ? fileObj.scxkImage[0].url : ''
      imgObj += scxkurl + ','

      //其他
      if (fileObj && fileObj.changeFileImage.length > 0) {
        let urls = fileObj.changeFileImage.map((item) => {
          return item.url
        })
        imgObj += urls
      }
      return imgObj
    },
    apicorrectionDetail(id) {
      correctionDetail({ id: id }).then((res) => {
        if (res.code === 0) {
          this.pageDetail = res.data
          this.cateGoryParams.firstCategory = res.data.firstCategory
          this.setDetail(res.data)
          this.apicorrectionCategory()
        } else {
          this.$message.error(res.message)
        }
      })
    },
    setDetail(data) {
      let keys = Object.keys(this.formData)
      for (let index = 0; index < keys.length; index++) {
        const key = keys[index]
        if (key === 'applicationField') {
          data[key] = data[key].split(',')
        }
        if (data[key]) {
          this.formData[key] = data[key]
        }
      }
      if (data['applicationField']) {
        this.formData.applicationField = data['applicationField']
      }
      if (this.formType !== 'detail') {
        this.oldData = JSON.parse(JSON.stringify(this.formData))
      }

      this.loopSetTimeOut(data)
    },

    loopSetInitImage(){
      setTimeout(() => {
        if (this.$refs.voucherImage){
          this.$refs.voucherImage.setImages();
        }else if(this.$refs.packageImage && this.$refs.instructionImage){
          this.$refs.packageImage.setImages();
          this.$refs.instructionImage.setImages();
        } else {
          this.loopSetInitImage()
        }
      }, 500)
    },

    loopSetTimeOut(data) {
      setTimeout(() => {
        if (
          this.$refs.voucherImage ||
          this.$refs.packageImage ||
          this.$refs.instructionImage
        ) {
          this.setImage(data)
        } else {
          this.loopSetTimeOut(data)
        }
      }, 500)
    },

    setImage(data) {
      let keys = ['voucherImage', 'packageImage', 'instructionImage']
      for (let index = 0; index < keys.length; index++) {
        const key = keys[index]
        if (data[key]) {
          this.$refs[key].setImages(data[key])
        }
      }
    }
  }
}
</script>

<style scoped>
.error-edit-page {
}

.error-edit-page /deep/ .el-form-item__content {
  width: 700px;
}
</style>

<style lang="scss" scoped>
.divBox {
  width: 100%;
  height: 100%;
  position: relative;
  padding-top: 105px;
  .bottom-info {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 105px;
    background: #fff;
    // display: flex;
    justify-content: space-between;
    align-items: center;
    z-index: 100;
    .topBox {
      padding: 16px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      span {
        font-size: 20px;
        font-weight: 500;
        text-align: left;
        color: #333333;
      }
    }
    .tabBox {
      border-bottom: 1px solid #efefef;
      display: flex;
      align-items: center;
      div:first-child {
        margin-left: 16px;
        border-left: 1px solid #efefef;
      }
      div {
        height: 40px;
        padding: 0 18px;
        font-size: 14px;
        line-height: 40px;
        font-weight: 400;
        text-align: left;
        color: rgba(0, 0, 0, 0.65);
        text-decoration: none;
        border-right: 1px solid #efefef;
        border-top: 1px solid #efefef;
        background: rgba(239, 239, 239, 0.3);
        cursor: pointer;
      }
      div.active {
        border-bottom: none;
        background: #ffffff;
        opacity: 1;
        color: #1890ff;
      }
    }
  }

  .conBox {
    width: 100%;
    height: 100%;
    overflow-y: auto;
  }

  .conBox::-webkit-scrollbar {
    width: 0 !important;
  }

  .contentBox {
    //height: 100%;
    padding: 16px 16px;
    background: #fff;

    .title {
      font-weight: 500;
      text-align: left;
      color: #000000;
      line-height: 14px;
      margin-bottom: 24px;
    }

    .title:before {
      content: '';
      display: inline-block;
      width: 3px;
      height: 13px;
      background: linear-gradient(360deg, #1d69c4 1%, #8bbdfc 99%);
      border-radius: 2px;
      margin-right: 8px;
      vertical-align: middle;
    }
  }
}

.tip-warning-text {
  margin-top: 20px;
  width: 64px;
  line-height: 1.5;
}

.row-line {
  display: flex;
  flex-direction: row;
}

.imgItem-l10 {
  margin-left: 30px;
}
</style>