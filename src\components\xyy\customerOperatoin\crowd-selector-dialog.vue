<!--created by <PERSON><PERSON><PERSON><PERSON><PERSON> in 2021/3/16
人群选择弹窗
-->
<template>
  <el-dialog
    v-el-drag-dialog
    :title="title"
    :visible="visible"
    width="90%"
    :destroy-on-close="true"
    :before-close="handleDialogClose"
    :append-to-body="true"
    @open="open"
  >
    <crowd-selector
      :selected.sync="innerSelected"
      :show-factory-report="false"
      :select-able="true"
      :operate-able="true"
      @selectChange="selectChange"
    />
    <span slot="footer">
      <el-button
        size="medium"
        @click="cancel"
      >取消</el-button>
      <el-button
        size="medium"
        style="margin-left: 20px;"
        type="primary"
        @click="confirm"
      >确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import elDragDialog from '@/components/directive/el-dragDialog';
import CrowdSelector from './crowd-selector';
// 拖拽指令
export default {
  name: 'CrowdSelectorDialog',
  components: { CrowdSelector },
  directives: { elDragDialog },
  model: {
    prop: 'dialogVisible',
    event: 'onDialogChange',
  },
  props: {
    // eslint-disable-next-line no-bitwise,vue/require-prop-type-constructor
    selected: Number | String,
    title: {
      type: String,
      default() {
        return '选择人群';
      },
    },
    dialogVisible: {
      type: Boolean,
      default() {
        return false;
      },
    },
  },
  data() {
    return {
      innerSelected: '',
      visible: false,
      selectItem: undefined,
    };
  },
  mounted() {
    this.$nextTick(() => {
      this.visible = true;
      this.innerSelected = this.selected;
    });
  },
  methods: {
    selectChange(val) {
      this.selectItem = val;
    },
    open() {},
    cancel() {
      this.handleDialogClose();
    },
    confirm() {
      if (!this.innerSelected) {
        this.$message.warning('请选择人群');
        return;
      }
      if (this.selectItem) {
        this.$emit('onSelect', this.selectItem);
        this.$emit('update:selected', this.innerSelected);
      }
      this.$emit('confirm');
      this.handleDialogClose();
    },
    handleDialogClose() {
      this.visible = false;
      this.$emit('onDialogChange', false);
    },
  },
};
</script>

<style lang="scss" scoped></style>
