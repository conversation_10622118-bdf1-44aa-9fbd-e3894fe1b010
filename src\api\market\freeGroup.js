import request from '../index';

// https://yapi.int.ybm100.com/project/65/interface/api/cat_8584

/**
 * 随心拼活动详情
 * @param {参数} params
 */
export function selectDetailByShopCode(params) {
  return request.get('/groupFollowHeart/selectDetailByShopCode', params);
}

/**
 * 操作记录
 * @param {参数} params
 */
export function selectOperationLog(params) {
  return request.get('/groupFollowHeart/selectOperationLog', params);
}

/**
 * 添加置顶商品
 * @param {参数} params
 */
export function getCsuByBarcode(params) {
  return request.get('/groupFollowHeart/getCsuByBarcode', params);
}

/**
 * 单个添加黑名单商品
 * @param {参数} params
 */
export function addBlackListSku(params) {
  return request.get('/groupFollowHeart/addBlackListSku', params);
}

/**
 * 选择黑名单商品
 * @param {参数} params
 */
export function selectSku(params) {
  return request.get('/groupFollowHeart/selectSku', params);
}

/**
 * 删除黑名单商品
 * @param {参数} params
 */
export function deleteBlackListSku(params) {
  return request.get('/groupFollowHeart/deleteBlackListSku', params);
}

/**
 * 获取转换系数范围和最大数量
 * @param {参数} params
 */
export function getDefaultDataFromApollo(params) {
  return request.get('/groupFollowHeart/getDefaultDataFromApollo', params);
}

/**
 * 新增/修改
 * @param {参数} params
 */
export function addOrUpdate(params) {
  return request.post('/groupFollowHeart/addOrUpdate', params);
}

/**
 * 黑名单列表
 * @param {参数} params
 */
export function selectBlackList(params) {
  return request.post('/groupFollowHeart/selectBlackList', params);
}

/**
 * 批量导入黑名单商品
 * @param {参数} params
 */
export function importBlackListSku(params) {
  return request.post('/groupFollowHeart/importBlackListSku', params);
}

/**
 * excel批量删除黑名单商品
 * @param {参数} params
 */
export function deleteBlackListSkuByExcel(params) {
  return request.post('/groupFollowHeart/deleteBlackListSkuByExcel', params);
}

/**
 * 导出黑名单商品
 * @param {参数} params
 */
export function exportBlackListSku(params) {
  return request.post('/groupFollowHeart/exportBlackListSku', params);
}
