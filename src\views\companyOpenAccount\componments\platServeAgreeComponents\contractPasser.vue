<template>
    <div class="open-elc-sign-page">
        <div class="qual-state">
            <div class="qual-title">
                {{ title }}
            </div>
            <div class="header-btn">
                <el-button size="small" @click="returnPage">返回平台服务协议</el-button>
            </div>
        </div>
        <div class="elc-sign-iframe">
            <iframe :src="signUrl" frameborder="0" style="width: 100%; height: 100%; border: 0px;"></iframe>
        </div>
    </div>
</template>

<script>
export default {
    data() {
        return {
            signUrl: '',
            title: ''
        }
    },
    methods: {
        returnPage() {
            this.$router.push({path: '/companyOpenAccount/platformServeAgreement'})
        }
    },
    mounted() {
        let query = this.$route.query;
        if (query && query.signUrl) {
            this.signUrl = query.signUrl;
            this.title = query.title;
        }
    },
}
</script>

<style lang="scss" scoped>
.open-elc-sign-page {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    .qual-state {
        .qual-title {
            font-family: PingFangSC-Medium;
            font-weight: 500;
            font-size: 20px;
            color: #333333;
        }
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0 20px;
        height: 50px;
    }
    .elc-sign-iframe {
        flex: 1;
    }
}
</style>