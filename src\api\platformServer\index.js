import request from '@/utils/request';
import { param } from 'jquery';

/**
 * 查询电子签状态
 * @param {params}查询参数
 */
export function getSignaturesStatus (params) {
  return request({
    url: '/platformServiceAgreement/signaturesStatus',
    method: 'get',
    params: params
  });
}

/* 点击开通电子签 */
export function openSignatures (params) {
  return request({
    url: '/platformServiceAgreement/passSignatures',
    method: 'get',
    data: params,
  });
}

/* 查询商家电子签协议列表 */
export function getSignaturesList (params) {
  console.log(params, "1231231231")
  return request({
    url: '/platformServiceAgreement/signaturesList',
    method: 'get',
    params: params,
  });
}
/* 获取免签验证连接 */
export function getFreeSign (params) {
  return request({
    url: '/platformServiceAgreement/freeSign',
    method: 'get',
    params: params,
  });
}
/* 获取资质印章验证连接 */
export function getVisaFreeSign() {
  return request({
    url: '/platformServiceAgreement/visaFreeSign',
    method: 'get',
  });
}
/* 平台服务协议查询商家信息 */
export function platServerQueryBusiness (params) {
  return request({
    url: '/platformServiceAgreement/getSignTaskOrgMessage',
    method: 'get',
    params: params,
  });
}

/* 去签署 */
export function goSign (params) {
  return request({
    url: '/platformServiceAgreement/goSign',
    method: 'get',
    params: params,
  });
}

/* 平台服务协议提交创建任务模板 */
export function submitAgreement (params) {
  return request({
    url: '/platformServiceAgreement/submitAgreement',
    method: 'post',
    data: params,
  });
}
/* 获取查看账号数据 */
export function getAccountInfo (params) {
  return request({
    url: '/platformServiceAgreement/getFddMemberNumber',
    method: 'get',
    params: params,
  });
}
/* 法大大开通协议链接 */
export function getFadadaOpenUrl (params) {
  return request({
    url: '/platformServiceAgreement/collectFreeSign',
    method: 'get',
    params: params,
  });
}