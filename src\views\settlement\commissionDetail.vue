<template>
  <div class="settlement-detail">
    <el-button class="reBtn" type="primary" @click="toBack">返回</el-button>
    <h4>应缴纳佣金记录</h4>
    <el-form :inline="true">
      <el-row>
        <el-col :span="8">
          <el-form-item label="商户编号：">{{ info.orgId }}</el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="商户名称：">{{ info.orgName }}</el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="生成时间：">{{ info.createTime | formatDate }}</el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="8">
          <el-form-item label="应收佣金金额：">{{
            info.deductedCommission || info.deductedCommission === 0 ? info.deductedCommission.toFixed(2) : ''
          }}</el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="确认收款时间：">{{ info.paymentTime | formatDate }}</el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="是否逾期：">{{
            info.overdue === 2 ? '未逾期' : '已逾期'
          }}</el-form-item>
        </el-col>
      </el-row>
      <el-row>
      <!--  <el-col :span="8">
          <el-form-item label="佣金优惠折扣：">{{
            info.commissionDiscount || info.commissionDiscount === 0 ? info.commissionDiscount.toFixed(2) : ''
          }}</el-form-item>
        </el-col> -->
        <el-col :span="8">
          <el-form-item label="佣金优惠金额：">{{
            info.discountHireMoney || info.discountHireMoney === 0 ? info.discountHireMoney.toFixed(2) : ''
          }}</el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="实际需缴纳佣金金额：">{{
            info.actualHireMoney || info.actualHireMoney === 0 ? info.actualHireMoney.toFixed(2) : ''
          }}</el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="8">
          <el-form-item label="最后付款时间：">{{ info.paymentTerm | formatDate }}</el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="状态：">{{ info.state | formatInvoice }}</el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="付款凭证/发票：">
            <span v-if="['ofd', 'pdf'].includes(checkFileExtension(info.paymentCertificate))" class="pdfTextSty" @click="viewAnnexUrl(info)">{{ info.invoiceFilename || '测试.pdf' }}</span>
            <el-button
              v-else
              style="padding: 0"
              type="text"
              @click="handlePictureCardPreview(info.paymentCertificate)"
              >点击查看</el-button
            >
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label="佣金收取月份：">{{ info.hireMonths }}</el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label="备注/原因：">{{ info.remarks }}</el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <h4>账单明细</h4>
    <xyy-table :data="list" :list-query="listQuery" :col="col" @get-data="getList"></xyy-table>
    <!--付款凭证预览-->
    <el-dialog :visible.sync="dialogVisible" title="付款凭证" custom-class="img-dialog">
      <img width="100%" :src="dialogImageUrl" alt="" />
    </el-dialog>
  </div>
</template>

<script>
import { getCommissionDetailList, getCommissionDetailInfo } from '../../api/settlement/commission'
import { showPdf } from '@/api/settlement/marketingServiceQuota/index'

export default {
  name: 'commissionDetail',
  data() {
    return {
      dialogImageUrl: '', // 付款凭证预览
      dialogVisible: false, // 付款凭证弹框
      list: [],
      listQuery: {
        pageSize: 10,
        page: 1,
        total: 0
      },
      col: [
        {
          index: 'billNo',
          name: '账单号',
          width: 200
        },
        {
          index: 'businessNo',
          name: '单据号',
          width: 200
        },
		{
			index: 'popOrderId',
			name: '订单ID',
			width: 200
		},
        {
          index: 'payTypeDesc',
          name: '支付方式',
          width: 200
        },
        {
          index: 'settlementTypeDesc',
          name: '佣金结算方式',
          width: 200
        },
        {
          index: 'businessTypeDesc',
          name: '单据类型',
          width: 200
        },
        {
          index: 'merchantName',
          name: '客户名称',
          width: 200
        },
        {
          index: 'productMoney',
          name: '商品金额（元）',
          width: 150,
          formatter: (row, col, cell) => (cell || cell === 0 ? cell.toFixed(2) : '')
        },
        {
          index: 'freightAmount',
          name: '运费金额（元）',
          width: 150,
          formatter: (row, col, cell) => (cell || cell === 0 ? cell.toFixed(2) : '')
        },
        {
          index: 'totalMoney',
          name: '单据总额含运费（元）',
          width: 200,
          formatter: (row, col, cell) => (cell || cell === 0 ? cell.toFixed(2) : '')
        },
        {
          index: 'shopTotalDiscount',
          name: '店铺总优惠（元）',
          width: 150,
          formatter: (row, col, cell) => (cell || cell === 0 ? cell.toFixed(2) : '')
        },
        {
          index: 'platformTotalDiscount',
          name: '平台总优惠（元）',
          width: 150,
          formatter: (row, col, cell) => (cell || cell === 0 ? cell.toFixed(2) : '')
        },
        {
          index: 'money',
          name: '单据实付金额（元）',
          width: 150,
          formatter: (row, col, cell) => (cell || cell === 0 ? cell.toFixed(2) : '')
        },
        {
          index: 'productActualMoney',
          name: '商品实付金额（元）',
          width: 150,
          formatter: (row, col, cell) => (cell || cell === 0 ? cell.toFixed(2) : '')
        },
        {
          index: 'hireMoney',
          name: '佣金金额（元）',
          width: 150,
          formatter: (row, col, cell) => (cell || cell === 0 ? cell.toFixed(2) : ''),
          renderHeader: (h, { column }) => {
            return h(
              'div', [
                h('span', column.label),
                h('el-tooltip', {
                  props: {
                    content: '单据中每个商品的佣金金额=（商品的实付金额+商品分摊的平台优惠金额）*下单时刻商品的佣金比例\n单据的佣金金额=单据中所有商品的佣金金额求和',
                    placement: 'right',
                  },
                }, [
                  h('i', { class: 'el-icon-warning-outline' }),
                ]),
              ],
            );
          },
        },
        {
          index: 'statementTotalMoney',
          name: '应结算金额（元）',
          width: 150,
          formatter: (row, col, cell) => (cell || cell === 0 ? cell.toFixed(2) : ''),
          renderHeader: (h, { column }) => {
            return h(
              'div', [
                h('span', column.label),
                h('el-tooltip', {
                  props: {
                    content: 'a、当单据佣金结算方式为“非月结”且支付方式为“在线支付、线下转账电汇平台“，应结算金额=单据实付金额-（佣金金额-平台总优惠）\nb、当单据佣金结算方式为“月结”且支付方式为“在线支付、线下转账电汇平台“，应结算金额=单据实付金额\nc、当单据佣金结算方式为“月结”且支付方式为“线下转账电汇商业“，应结算金额=0',
                    placement: 'right',
                  },
                }, [
                  h('i', { class: 'el-icon-warning-outline' }),
                ]),
              ],
            );
          },
        },
        {
          index: 'deductedCommission',
          name: '应缴纳佣金（元）',
          width: 150,
          formatter: (row, col, cell) => (cell || cell === 0 ? cell.toFixed(2) : ''),
          renderHeader: (h, { column }) => {
            return h(
              'div', [
                h('span', column.label),
                h('el-tooltip', {
                  props: {
                    content: '补贴冲抵佣金商业：应缴纳佣金=佣金金额*佣金折扣-平台总优惠\n补贴不冲抵佣金商业：应缴纳佣金=佣金金额*佣金折扣',
                    placement: 'right',
                  },
                }, [
                  h('i', { class: 'el-icon-warning-outline' }),
                ]),
              ],
            );
          },
        },
        {
          index: 'commissionDiscount',
          name: '佣金折扣',
          width: 150,
          formatter: (row, col, cell) => (cell || cell === 0 ? cell.toFixed(2) : '')
        },
        {
          index: 'discountReason',
          name: '折扣原因',
          width: 150
        },
        {
          index: 'actualCommissionMoney',
          name: '实际需缴纳佣金',
          width: 150,
          formatter: (row, col, cell) => (cell || cell === 0 ? cell.toFixed(2) : ''),
          renderHeader: (h, { column }) => {
            return h(
              'div', [
                h('span', column.label),
                h('el-tooltip', {
                  props: {
                    content: '实际需缴纳佣金=应缴纳佣金',
                    placement: 'right',
                  },
                }, [
                  h('i', { class: 'el-icon-warning-outline' }),
                ]),
              ],
            );
          },
        },
        {
          index: 'commissionDiscountMoney',
          name: '佣金优惠',
          width: 150,
          formatter: (row, col, cell) => (cell || cell === 0 ? cell.toFixed(2) : ''),
          renderHeader: (h, { column }) => {
            return h(
              'div', [
                h('span', column.label),
                h('el-tooltip', {
                  props: {
                    content: '佣金优惠=佣金金额-佣金金额*佣金折扣。单据因享受佣金折扣政策的优惠金额',
                    placement: 'right',
                  },
                }, [
                  h('i', { class: 'el-icon-warning-outline' }),
                ]),
              ],
            );
          },
        },
        {
          index: 'orderPayTime',
          name: '支付时间',
          width: 200,
          formatter: (row, col, cell) =>
            cell ? new Date(cell + 8 * 3600 * 1000).toJSON()
              .substr(0, 19)
              .replace('T', ' ') : ''
        },
        {
          index: 'billCreateTime',
          name: '账单生成时间',
          width: 200,
          formatter: (row, col, cell) =>
            cell ? new Date(cell + 8 * 3600 * 1000).toJSON().substr(0, 19).replace('T', ' ') : ''
        },
        // {
        //   index: 'billPaymentTime',
        //   name: '入账时间',
        //   width: 200,
        //   formatter: (row, col, cell) =>
        //     cell ? new Date(cell + 8 * 3600 * 1000).toJSON().substr(0, 19).replace('T', ' ') : ''
        // }
      ],
      info: {
        orgId: '', // 商户编码
        orgName: '', // 商户名称
        createTime: '', // 生成时间
        hireMoney: '', // 应收佣金金额
        paymentTime: '', // 确认收款时间
        overdue: '', // 是否逾期，1：逾期  2：不逾期
        paymentTerm: '', // 最后付款时间
        state: '', // 状态， 1：待商业付款 2-待平台审核 3：审核通过 4：审核未通过 5：待结转 6:已结转
        paymentCertificate: '', // 付款凭证图片
        hireMonths: '', // 佣金收取月份
        remarks: '', // 备注原因
        commissionDiscount: '', // 佣金折扣
        discountHireMoney: '', // 优惠佣金金额
        actualHireMoney: '', // 实际需缴纳佣金金额
        deductedCommission: '', // 应收佣金金额
        invoiceFilename: '', // 发票文件名
      },
      routerObj: ''
    }
  },
  methods: {
    checkFileExtension(val = ''){
      let fileType = val?.substring(val?.lastIndexOf('.') + 1);
      fileType = fileType?.toLocaleLowerCase();
      return fileType
    }, // 判断文件类型
    viewAnnexUrl(val) {
      var val = {url: val.paymentCertificate, name: val.invoiceFilename};
      let {url, name} = val
      let temp = this.checkFileExtension(url)
      if(temp == 'pdf'){
        window.open(url, '_blank')
      }else if (temp == 'ofd') {
        showPdf({ pdfUrl: url }).then(res=>{
          let pdfContent = res;
          const blob = new Blob([pdfContent], { type: "application/pdf"});
          const url = window.URL.createObjectURL(blob);
          const a = document.createElement("a");
          a.style.display = "none";
          a.href = url;
          a.download = name;
          document.body.appendChild(a);
          a.click();
          window.URL.revokeObjectURL(url);
        })
      }
    }, // 点击预览pdf&下载ofd
    /** 预览 */
    handlePictureCardPreview(url) {
      this.dialogImageUrl = url
      this.dialogVisible = true
    },
    /**
     * 获取明细列表
     */
    getList() {
      this.routerObj = this.$route.query
      this.listQuery.hireNo = this.routerObj.hireNo
      const loading = this.$loading({
        lock: true,
        text: 'Loading',
        spinner: 'el-icon-loading',
        background: 'rgba(255,255,255, 0.8)'
      })
      this.getInfo()
      const { page, pageSize } = this.listQuery
      getCommissionDetailList({
        pageNum: page,
        pageSize,
        hireNo: this.listQuery.hireNo
      })
        .then((res) => {
          loading.close()
          if (res.code === '200') {
            const { list, total, pageNum } = res.data
            this.list = list || []
            this.listQuery = {
              ...this.listQuery,
              total,
              page: pageNum
            }
          } else {
            this.$message.error({ message: res.errorMsg || res.msg, customClass: 'center-msg' })
          }
        })
        .catch(() => {
          loading.close()
        })
    },
    getInfo() {
      getCommissionDetailInfo({
        hireNo: this.listQuery.hireNo
      })
        .then((res) => {
          if (res.code === '200') {
            Object.keys(this.info).forEach((key) => {
              this.info[key] = res.data[key]
            }, this)
          } else {
            this.$message.error({ message: res.errorMsg || res.msg, customClass: 'center-msg' })
          }
        })
        .catch(() => {})
    },
    toBack() {
      this.$router.go(-1);
    }
  },
  filters: {
    formatDate(date) {
      return date ? new Date(date + 8 * 3600 * 1000).toJSON().substr(0, 19).replace('T', ' ') : ''
    },
    formatInvoice(val) {
      if (val === 1) {
        return '待商业付款'
      }
      if (val === 2) {
        return '待平台审核'
      }
      if (val === 3) {
        return '已完成'
      }
      if (val === 4) {
        return '审核未通过'
      }
      if (val === 5) {
        return '待结转'
      }
      if (val === 6) {
        return '已结转'
      }
      return ''
    }
  },
  created() {
    if (this.$route.query.hireNo) {
      this.getList()
    }
  },
  activated() {
    this.getList();
  }
}
</script>

<style>
  .el-tooltip__popper {
    white-space: pre-line;
  }
</style>
<style lang="scss" scoped>
.settlement-detail {
  padding: 15px;
  position: relative;
  .reBtn.el-button {
    padding: 0 12px;
    line-height: 30px;
    position: absolute;
    right: 15px;
  }
  h4 {
    margin: 0 0 15px 0;
    font-size: 14px;
    font-family: PingFangSC, PingFangSC-Medium;
    font-weight: 500;
    color: #333333;
    &:first-child {
      margin-top: 0;
    }
    &:before {
      content: '';
      background-image: linear-gradient(0deg, #1d69c4 0%, #8bbdfc 99%);
      width: 3px;
      height: 13px;
      float: left;
      margin: 3px 7px 3px 0;
    }
  }
  .el-form {
    width: 95%;
    padding: 0 10px;
    .el-form-item {
      margin-bottom: 5px;
      font-size: 12px;
      font-family: PingFangSC, PingFangSC-Regular;
      font-weight: 400;
      color: #333333;
      ::v-deep  .el-form-item__label {
        height: 30px;
        line-height: 30px;
      }
      ::v-deep  .el-form-item__content {
        height: 30px;
        line-height: 30px;
      }
    }
  }
}
</style>
<style lang="scss">
.el-dialog.img-dialog {
  width: 500px;
  .el-dialog__header {
    padding: 0 20px;
    line-height: 50px;
    .el-dialog__headerbtn {
      top: 17px;
    }
  }
  .el-dialog__body {
    padding: 10px 20px;
  }
}
.pdfTextSty{
  color: #52a7cb;
  cursor: pointer;
}
</style>
