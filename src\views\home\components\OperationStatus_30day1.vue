<template>
  <div v-if="showChart" class="OperationStatus_30day">
    <el-row type="flex" align="middle" justify="space-between">
      <div class="task_title">近30天经营情况</div>
      <el-button ref="more_button" v-permission="['dataStatistics_salesReport']" class="btn-info" size="small" @click="toSalesReports">查看更多</el-button>
    </el-row>
    <template v-for="(item, index) in lineChartList">
      <line-chart
        ref="lineChart"
        :titleInfo="item.titleInfo"
        :chartId="item.chartId"
        :tooltip="item.tooltip"
        :chartColor="item.chartColor"
        :asyncHandler="item.asyncHandler"
        :queryForm="form"
      />
    </template>
  </div>
</template>

<script>
import {
  apiQuantity,
  apiPurchase,
  apiCreateShop
  } from '@/api/data-statistics/index'
import lineChart from '@/views/data-statistics/components/lineChart';
import {mapState} from 'vuex'
import {actionTracking} from "@/track/eventTracking";
import {VNodeExposureTool} from "@/utils/exposureTools";

let exposureTool = null;

export default {
  name: "OperationStatus_30day",
   components: {lineChart},
  data() {
    return {
      form: {
        statisticalPlacer: 1,
        provinceCodeList:[],
        cityCodeList:[],
        areaCodeList:[],
        opTime:[]
      },
      lineChartList:[],
      isCreateShop:false
    }
  },
  computed:{
    ...mapState('app',['shopConfig']),
    showChart : function (){
      let show = this.isCreateShop && this.shopConfig.indexShow;
      if(show){
        this.$nextTick(()=>{
          exposureTool.begin([...this.$refs.lineChart, this.$refs.more_button])
        })
      }
      return show;
    }
  },
  created() {
    this.getNowTimeDate();
  },
  mounted() {
    this.getList();
    let that = this;
    this.$nextTick(()=>{
      exposureTool = new VNodeExposureTool(document.querySelector(".home"), (item)=> {
        if(!item.getChartId){
          //没ID的引用就是按钮
          actionTracking('last_30days_more_exposure', {})
        } else if (item.getChartId() === 'quantity'){
          actionTracking('last_30days_total_money_exposure', {})
        } else if(item.getChartId() === 'purchase'){
          actionTracking('last_30days_money_exposure', {})
        }
      });
      // exposureTool.begin([...this.$refs.lineChart, this.$refs.more_button])
    })
  },

  methods: {
    getList(from) {
      this.lineChartList =[];
      this.getCreateShop();
      this.getQuantity();
      this.getPurchase();
    },
    getCreateShop(){
      const that = this;
      apiCreateShop().then(res => {
        if (res.code !== 0) {
          this.$message.error(res.message);
          return;
        }
        this.isCreateShop = true;
      });
    },
    getQuantity() {
      let quantity = {
        titleInfo:'采购总金额',
        chartId:'quantity',
        chartColor:'#4184D5',
        tooltip:'每天支付成功的订单总额（含运费，含优惠）<br/>统计订单状态：待审核、出库中、配送中、已完成、已退款',
        asyncHandler:apiQuantity
      }
      this.lineChartList.push(quantity);
    },
    getPurchase(){
      let purchase = {
        titleInfo:'净采购金额',
        chartId:'purchase',
        chartColor:'#FF982C',
        tooltip:'每天支付成功的订单实付总额（含运费，不含优惠）-每天退款成功的退款总额（含运费，不含优惠）<br/>统计订单状态：待审核、出库中、配送中、已完成、已退款<br/>统计退款单状态：退款成功',
        asyncHandler:apiPurchase
      }
      this.lineChartList.push(purchase);
    },
    toSalesReports(){
      const path = '/salesReports'
      window.openTab(path)
      actionTracking('last_30days_more_click', {})
    },
    // 设置初始时间
    getNowTimeDate() {
      const time = new Date(8 * 3600 * 1000 + +new Date() - 24 * 3600 * 1000 )
        .toJSON()
        .substr(0, 10);

      const cc = new Date().getTime();
      var halfYear = 30 * 24 * 3600 * 1000;
      var pastResult = cc - halfYear;
      var pastDate = new Date(pastResult),
        pastYear = pastDate.getFullYear(),
        pastMonth =
          pastDate.getMonth() + 1 < 10
            ? '0' + (pastDate.getMonth() + 1)
            : pastDate.getMonth() + 1,
        pastDay =
          pastDate.getDate() < 10
            ? '0' + pastDate.getDate()
            : pastDate.getDate();

      const oldTime = pastYear + '-' + pastMonth + '-' + pastDay;
      this.form.opTime = [oldTime, time];
    },
  }
}
</script>

<style scoped lang="scss">
.OperationStatus_30day {
  width: 100%;
  background: #fff;
  padding: 16px;
  margin-top: 16px;

  .btn-info {
    padding: 9px 15px;
    font-size: 12px;
    border-radius: 3px;
    border: 1px solid #4183d5;
    color: #4183d5;
    background: #fff;

    &:hover,
    &:focus {
      background: #4183d5;
      border-color: #4183d5;
      color: #ffffff;
    }
  }
}
</style>
