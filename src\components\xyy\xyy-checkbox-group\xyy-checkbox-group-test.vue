<!--created by <PERSON><PERSON><PERSON><PERSON><PERSON> in 2021/3/12-->
<template>
  <xyy-checkbox-group
    v-model="selected"
    key-prop="id"
    name-prop="name"
    :data-array="dataArray"
  />
</template>

<script>
import XyyCheckboxGroup from './index';

export default {
  name: 'XyyCheckboxGroupTest',
  components: { XyyCheckboxGroup },
  data() {
    return {
      selected: [1, 4],
      dataArray: [
        { id: 1, name: 'id-1' },
        { id: 2, name: 'id-2' },
        { id: 3, name: 'id-3' },
        { id: 4, name: 'id-4' },
      ],
    };
  },
};
</script>

<style scoped></style>
