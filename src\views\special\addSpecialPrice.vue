<template>
  <div class="padding20">
    <div class="serch">
      <el-row type="flex" align="middle" style="justify-content: space-between;">
        <div>
          <span class="sign" />
          <span>基本信息</span>
        </div>
        <el-button type="primary" size="small" @click="$router.go(-1)">返回</el-button>
      </el-row>
    </div>
    <div>
      <el-form
        ref="basic"
        label-width="100px"
        size="small"
        label-position="right"
        :rules="basicRules"
        :model="basic"
        style="width: 500px"
      >
        <el-form-item label="活动名称" prop="title" ref="title">
          <el-input
            v-model.trim="basic.title"
            type="text"
            maxlength="80"
            placeholder="请输入"
          />
        </el-form-item>
        <el-form-item label="活动时间" prop="startTime" ref="startTime">
          <!--          <el-date-picker-->
          <!--            style="width: 100%;"-->
          <!--            v-model="basic.timeList"-->
          <!--            type="datetimerange"-->
          <!--            range-separator="至"-->
          <!--            start-placeholder="开始日期"-->
          <!--            value-format="timestamp"-->
          <!--            end-placeholder="结束日期">-->
          <!--          </el-date-picker>-->
          <el-date-picker
            v-model="basic.startTime"
            :picker-options="pickerOptions"
            style="width: 47%"
            type="datetime"
            :disabled="isDisabled"
            value-format="timestamp"
            placeholder="选择开始日期"
            @change="dateTimeFocus"
          />
          <span style="padding: 0 5px">至</span>
          <el-date-picker
            v-model="basic.endTime"
            :picker-options="pickerOptions"
            style="width: 47%"
            type="datetime"
            value-format="timestamp"
            placeholder="选择结束日期"
          />
        </el-form-item>
        <el-form-item label="预热时间" prop="preheatTime" ref="preheatTime">
          <el-date-picker
            v-model="basic.preheatTime"
            :picker-options="pickerOptions"
            style="width: 100%"
            type="datetime"
            :disabled="isDisabled"
            value-format="timestamp"
            placeholder="选择日期"
          />
        </el-form-item>
        <el-form-item label="活动介绍">
          <el-input
            v-model.trim="basic.introduction"
            type="textarea"
            :autosize="{ minRows: 2, maxRows: 4}"
            placeholder="请输入内容"
          />
        </el-form-item>
      </el-form>
    </div>
    <div class="serch">
      <el-row type="flex" align="middle">
        <span class="sign" />
        <div>特价配置</div>
      </el-row>
    </div>
    <div>
      <div class="list-box">
        <div class="text">人群范围：</div>
        <div>
          <p>
            <el-radio v-model="radio" label="1">指定人群参与</el-radio>
            <el-button
              v-if="radio == 1 "
              size="small"
              type="primary"
              plain
              @click="dialogVisible = true"
            >选择人群</el-button>
            <span style="padding-left: 10px">{{peopleIdStr}}</span>
          </p>
          <!-- <p style="padding-top: 10px">
            <el-radio v-model="radio" label="2" @change="clearPeople">全部人群参与</el-radio>
          </p> -->
        </div>
      </div>
      <div class="list-box">
        <div class="text">参与商品：</div>
        <div>
          <el-button size="small" type="primary" plain @click="addGoods">添加商品</el-button>
          <!-- <el-upload
            class="upload-demo"
            action="/promo/promotion/importProduct"
            :on-success="handleSuccess"
            :multiple="false"
            name="bindFile"
            :show-file-list="false"
          >
            <el-button size="small" type="primary" plain>导入商品</el-button>
          </el-upload> -->
          <el-button size="small" type="primary" plain @click="showImportProducts = true">导入商品</el-button>
          <!-- <el-button size="small" type="primary" plain @click="downloadExcel">模板下载</el-button> -->
          <el-button size="small" type="primary" plain @click="deleteGoods">删除商品</el-button>
          <div style="display: inline-block;padding-left: 10px" v-if="shopListData.length>0">
            <el-button plain size="small" @click="batchSetting('all')">批量设置商品总限购数</el-button>
            <el-button plain size="small" @click="batchSetting('one')">批量设置商品个人限购数</el-button>
            <el-button plain size="small" @click="batchSetting('is')">批量设置是否可超出限购</el-button>
            <el-input
              v-model="quickSearchStr"
              style="width: 200px;margin-left: 10px;"
              size="small"
              placeholder="sku编码/商品编码/商品名称/ERP编码/生产厂家"
              prefix-icon="el-icon-search"
              @change="handleQuickSearch"
            />
          </div>
        </div>
      </div>
      <div>
        <el-table
          ref="shopTable"
          :data="(quickSearchList ? quickSearchList : shopListData)"
          stripe
          border
          tooltip-effect="dark"
          style="width: 100%"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="45" />
          <el-table-column type="index" width="50" />
          <el-table-column prop="barcode" label="编码信息">
            <template slot-scope="{ row }">
              <p>{{ row.skuId }}</p>
              <p>{{ row.barcode }}</p>
              <p>{{ row.erpCode }}</p>
            </template>
          </el-table-column>
          <el-table-column prop="showName" label="商品信息">
            <template slot-scope="{ row }">
              <p>{{ row.showName }}</p>
              <p>{{ row.spec }}</p>
              <p>{{ row.manufacturer }}</p>
              <div v-if="row.imageUrls">
                <!-- <div
                  v-for="(item, index) in row.imageUrls"
                  :key="index"
                >
                  <el-image
                    style="width: 80px;height: 73px;margin-bottom: 4px"
                    :src="item"
                    :preview-src-list="row.imageUrls"
                    @click.prevent
                  />
                </div> -->
                <div v-if="row.imageUrls[0]">
                  <el-image
                    style="width: 80px;height: 73px;margin-bottom: 4px"
                    :src="row.imageUrls[0]"
                    :preview-src-list="[row.imageUrls[0]]"
                    @click.prevent
                  />
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="spec" label="价格">
            <template slot-scope="{ row }">
              <p>单体采购价：{{ row.fob }}</p>
              <p>连锁采购价：{{ row.guidePrice }}</p>
              <p>底价：{{ row.basePrice }}</p>
              <el-button
                  v-if="row.haveAreaPrice === 1"
                  type="text"
                  size="medium"
                  @click="viewAreaPrice(row)"
                  >查看区域价格</el-button
                >
            </template>
          </el-table-column>
          <el-table-column width="150">
            <template slot="header" slot-scope="scope">
              <span>特价价格</span>
              <el-tooltip effect="dark" placement="top">
                <template
                  #content
                >特惠价格需小于连锁采购价和单体采购价最大值，针对活动价格大于原价的对应活动不生效。
                <br>示例：单体采购价10，连锁采购价8元，特惠价格可设置9.5元，针对连锁客户特价活动不生效。</template>
                <i class="el-icon-warning-outline"></i>
              </el-tooltip>
            </template>
            <template slot-scope="scope">
              <el-input
                v-model="scope.row.skuPrice"
                style="width: 80px;"
                size="small"
                type="number"
                @focus="clearTip(1, scope.row)"
                @blur="changePrice(1, scope.$index, scope.row)"
              />
              <p v-if="scope.row.skuPriceTip" class="errorTip">{{scope.row.skuPriceTip}}</p>
            </template>
          </el-table-column>
          <el-table-column prop="availableQty" label="库存" width="120" />
          <el-table-column
            label="限购"
            :render-header="renderHeader"
          >
            <template slot-scope="scope">
              <div style="margin-bottom: 10px;">
                总限购
                <el-input
                  v-model="scope.row.totalQty"
                  style="width: 80px;"
                  size="small"
                  type="number"
                  @focus="clearTip(2, scope.row)"
                  @blur="changePrice(2, scope.$index, scope.row)"
                />
                <p v-if="scope.row.totalQtyTip" class="errorTip">{{scope.row.totalQtyTip}}</p>
              </div>
              <div>
                单店限购
                <el-input
                  v-model="scope.row.personalQty"
                  style="width: 80px;"
                  size="small"
                  type="number"
                  @focus="clearTip(3, scope.row)"
                  @blur="changePrice(3, scope.$index, scope.row)"
                />
                <p v-if="scope.row.personalQtyTip" class="errorTip">{{scope.row.personalQtyTip}}</p>
              </div>
              <div>
                是否可超出限购数量
                <el-radio v-model="scope.row.isOver" :label="1">是</el-radio>
                <el-radio v-model="scope.row.isOver" :label="0">否</el-radio>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div style="padding-top: 20px">
        <el-button size="small" type="primary" @click="operateCreate" :disabled="isSend">保存</el-button>
        <el-button size="small" type="primary" plain @click="$router.go(-1)">取消</el-button>
      </div>
      <changeGoods ref="changeGoods" @setChooseGoods="setChooseGoods"></changeGoods>
      <crowd-selector-dialog
        ref="changePeople"
        v-if="dialogVisible"
        v-model="dialogVisible"
        :selected="this.customerGroupId"
        @onSelect="sendPeopleData"
      ></crowd-selector-dialog>
    </div>
    <div class="chooses">
      <el-dialog title="批量设置是否可超出限购" :visible.sync="isDialog" width="422px">
        <div class="conten-box">
          <div>是否可超出限购数量:</div>
          <div style="margin-left: 10px">
            <el-radio v-model="isInputValue" :label="1">是</el-radio>
            <el-radio v-model="isInputValue" :label="0">否</el-radio>
          </div>
        </div>
        <div slot="footer" class="dialog-footer">
          <el-button size="small" @click="isDialog = false">取 消</el-button>
          <el-button type="primary" size="small" @click="batchSetting('isAc')">确 定</el-button>
        </div>
      </el-dialog>
    </div>
    <importProducts
      v-if="showImportProducts"
      :cancel-dialog="cancelDialog"
      :set-choose-goods="setChooseGoods"
    />
    <viewPrice
    v-if="viewingInventoryLogsVisible"
    :inventory-log-dialog-visible.sync="viewingInventoryLogsVisible"
    :goods-message="goodsMessage"
    >
    </viewPrice>
  </div>

</template>

<script>
import viewPrice from './components/viewPrice.vue';
import changeGoods from './components/changeGoods'
// import changePeople from './components/changePeople'
import CrowdSelectorDialog from '../../components/xyy/customerOperatoin/crowd-selector-dialog.vue'
import {operateGoods, getGoodsDetail, offlineGoods} from '../../api/activity/special'
import importProducts from './components/importProducts.vue';

export default {
  name: "specialPriceOperate",
  components: { changeGoods, CrowdSelectorDialog, importProducts, viewPrice },

  data () {
    let checkTime = (rule, value, callback) => {
      if(value === ''){
        callback(new Error('请选择预热时间'))
      }else {
        if(value < new Date().getTime() && this.businessStatus != 3){
          callback(new Error('预热时间不得小于当前时间'))
        }
        if(this.basic.timeList[0] && value > this.basic.timeList[0]){
          callback(new Error('预热时间必须早于活动开始时间'))
        }else {
          callback()
        }
      }
    }
    let checkName = (rule, value, callback) => {
      if(value === ''){
        callback(new Error('请输入活动名称'))
      }else if(this.nameErrorMsg) {
        callback(new Error(this.nameErrorMsg))
      }else {
        callback()
      }
    }
    let checkStartTime = (rule, value, callback) => {
      if(!value){
        if(this.basic.endTime && !this.basic.startTime){
          this.basic.endTime = ''
          callback(new Error('请先选择开始时间'))
        }
        callback(new Error('请选择活动时间'))
      }else {
        if(!this.basic.startTime){
          callback(new Error('请选择活动开始时间'))
        }else if(!this.basic.endTime){
          callback(new Error('请选择活动结束时间'))
        }
        if(this.basic.endTime && this.basic.endTime < new Date().getTime() && this.businessStatus != 3){
          callback(new Error('活动结束时间不得小于当前时间'))
        }
        if(this.basic.startTime && this.basic.endTime && (this.basic.startTime == this.basic.endTime)){
          callback(new Error('活动开始时间不得等于结束时间'))
        }
        if(this.basic.preheatTime && this.basic.startTime < this.basic.preheatTime){
          callback(new Error('活动开始时间必须晚于预热时间'))
        }else {
          callback()
        }
      }
    }
    return {
      goodsMessage:{},
      viewingInventoryLogsVisible: false,
      quickSearchList: null,
      quickSearchStr: '',
      basicRules: {
        title: [
          { required: true, validator: checkName, trigger: 'blur' },
          { max: 50, message: '活动名称限制50', trigger: 'blur' },
        ],
        startTime: [
          { validator: checkStartTime, required: true, trigger: 'change' },
        ],
        // preheatTime: [
        //   {required: true, validator: checkTime, trigger: 'change' }
        // ]
      },
      basic: {
        promotionId: '',
        title: '',
        startTime: '',
        endTime: '',
        preheatTime: '',
        introduction: '',
        timeList: [],
      },
      showImportProducts: false,
      nameErrorMsg: '',
      radio: '1',
      shopListData: [],
      shopListAllData: [],
      peopleIdStr: '',
      customerGroupId: '',
      isDialog: false,
      isInputValue: null,
      isDisabled: false,
      dialogVisible: false,
      isSend: false,
      businessStatus: '',
      routerObj: '',
      minDate: '',
      pickerOptions: {
        onPick: ({ maxDate, minDate }) => {
          this.minDate = minDate;
          this.maxDate = maxDate;
        },
        disabledDate: (time) => {
          if (this.minDate) {
            return (
              time.getTime() < Date.now() - 24 * 3600 * 1000
            );
          }
          return time.getTime() < Date.now() - 24 * 3600 * 1000 || time.getTime() > Date.now() + 90 * 24 * 3600 * 1000;
        },
      },
    }
  },
  computed: {
    expireTimeOption() {
      const _this = this;
      return {
        disabledDate(date) {
          return date < _this.basic.startTime;
        },
      }
    }
  },
  created() {
    if (this.$route.query.promotionId) {
      this.queryInfo();
    }
  },
  activated() {
    if (this.$route.query.promotionId) {
      this.queryInfo();
    }
    // if (this.routerObj && JSON.stringify(this.routerObj) !== JSON.stringify(this.$route.query)) {
    //   console.log('334----------',this.routerObj, this.$route.query)
    //   this.queryInfo()
    // }
  },
  methods: {
    viewAreaPrice(row) {
      this.goodsMessage = row
      console.log(row);
      this.viewingInventoryLogsVisible=true

    },
    handleQuickSearch(val) {
      if (val === '') {
        this.quickSearchList = this.shopListData;
      } else {
        const arr = [];
        this.shopListData.forEach((item) => {
          if (`${item.skuId}` === val || item.barcode === val || item.showName === val || item.erpCode === val || item.manufacturer === val) {
            arr.push(item);
          }
        });
        this.quickSearchList = arr;
      }
    },
    renderHeader(h, { column }) {
      return h(
        'div', [
          h('span', column.label),
          h('el-tooltip', {
            props: {
              content: '超出单店限购数量将按照原价购买',
              placement: 'right',
            },
          }, [
            h('i', { class: 'el-icon-warning-outline' }),
          ]),
        ],
      );
    },
    dateTimeFocus(time) {
      if (this.basic.startTime) {
        this.basic.preheatTime = this.basic.startTime;
      }
    },
    queryInfo() {
      if (this.$route.query && this.$route.query.promotionId) {
        this.routerObj = this.$route.query
        getGoodsDetail({promotionId:this.routerObj.promotionId}).then( res => {
          if (res.code == 0) {
            const data = res.result
            this.businessStatus = data.businessStatus
            data.businessStatus == 3 ? this.isDisabled = true: ''
            this.basic.title = data.title
            this.basic.preheatTime = data.preheatTime
            this.basic.introduction = data.introduction
            this.basic.promotionId = data.promotionId
            this.basic.startTime = data.startTime
            this.basic.endTime = data.endTime
            //this.basic.timeList = [data.startTime, data.endTime]
            if (data.customerGroupId) {
              this.radio = '1'
              this.customerGroupId = data.customerGroupId
              this.peopleIdStr = data.customerGroupDTO.groupName
            } else {
              this.radio = '2'
            }
            this.shopListData = data.promotionSkuExtendDTOList
          } else {

          }
        })
      }
    },
  /**/
    downloadExcel(){
      window.open('https://upload.ybm100.com/ybm/file/skuexportmodule.xlsx')
    },
    /*删除商品*/
    deleteGoods() {
      const allList = this.$refs.shopTable.selection
      if(allList.length <=0){
        this.$message.warning({message: '请先勾选要操作的数据', offset: 100})
        return
      }
      this.$confirm('确认要删除已选商品吗?', '温馨提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        let deleateAry = []
        for( let j=0; j<allList.length; j++){
          const item = allList[j]
          deleateAry.push(item.barcode)
        }
        for (let k= 0; k < this.shopListData.length; k++) {
          if (deleateAry.indexOf(this.shopListData[k].barcode) > -1) {
            this.shopListData.splice(k, 1);
            k--;
          }
        }
      }).catch(() => {});
    },
    /*列表勾选*/
    handleSelectionChange(){},
    /*创建特价活动*/
    operateCreate() {
      let that = this
      this.$refs.basic.validate( (valid) => {
        if(valid) {
          console.log(this.shopListData,'this.shopListData')
          if(this.radio == 1 && !this.customerGroupId){
            this.$message.warning({message:'请先添加人群范围', offset:100})
            return
          }
          if(this.shopListData.length <= 0){
            this.$message.warning({message:'请先添加商品', offset:100})
            return
          }
          for( let i=0; i<this.shopListData.length; i++){
            const item = this.shopListData[i]
            if(!item.skuPrice || item.skuPrice <= 0){
              item.skuPriceTip = '请填写商品特价'
              that.shopListData = that.shopListData.concat([])
              return
            }
          }
          let {title, preheatTime, introduction, timeList, promotionId, startTime, endTime} = this.basic
          let sendData = {};
          sendData.title = title
          sendData.startTime = startTime
          if(this.businessStatus == 3 && endTime < new Date().getTime()){
            sendData.endTime = new Date().getTime()
          }else{
            sendData.endTime = endTime
          }
          sendData.preheatTime = preheatTime
          sendData.introduction = introduction
          sendData.promotionId = promotionId
          sendData.customerGroupId = this.customerGroupId
          sendData.promotionSkuExtendDTOList = []
          this.shopListData.forEach((item) => {
            let obj = {}
            obj.promotionId = promotionId
            obj.barcode = item.barcode
            obj.totalQty = item.totalQty
            obj.personalQty = item.personalQty
            obj.skuId = item.skuId?item.skuId:item.id
            obj.skuName = item.showName
            obj.showName = item.showName
            obj.skuPrice = item.skuPrice
            obj.sort = ''
            obj.isDisplay = 1
            obj.isOver = item.isOver
            obj.introduction = item.introduction
            obj.fob = item.fob
            obj.guidePrice = item.guidePrice
            sendData.promotionSkuExtendDTOList.push(obj)
          })
          const specialPromotionDTO = JSON.stringify(sendData)
          this.isSend = true
          operateGoods(specialPromotionDTO).then( res => {
            this.isSend = false
            if (res.code == 0) {
              this.$message.success({
                message: '保存成功', offset: 100, onClose: () => {
                  this.$router.push({path: '/specialPrice', query: {refresh: true}})
                }
              })
            }else {
              this.$message.error({message:res.msg, offset:100})
            }
          })
        }
      })
    },
    /*添加商品*/
    addGoods() {
      if(this.shopListData.length < 500) {
        this.$refs.changeGoods.getList('', this.shopListData)
      }else{
        this.$message.warning('商品数量最大值500')
      }
    },
    clearPeople(val) {
      this.peopleIdStr = ''
      this.customerGroupId = ''
    },
    /*添加人群*/
    sendPeopleData(value) {
      this.peopleIdStr = value.tagName
      this.customerGroupId = value.id
    },
    /*添加商品*/
    setChooseGoods(list) {
      if (list.length) {
        list.forEach((item) => {
          if (!item.skuId && item.id) {
            item.skuId = item.id;
          }
        });
      }
      this.shopListData = this.shopListData.concat(list)
      let arr = this.shopListData
      const hash = {};
      arr = arr.reduce((acc, curr) => {
        hash[curr.barcode] ? '' : hash[curr.barcode] = true && acc.push(curr);
        return acc;
      }, []);
      console.log(arr);
      for (let k = 0; k<arr.length; k++){
        const s = arr[k]
        // if(!s.totalQty){
        //   s.totalQty = 999999
        // }
        if(s.isOver == undefined || s.isOver == null || s.isOver === ''){
          s.isOver = 0
        }
      }
      this.shopListData = JSON.parse(JSON.stringify(arr))
    },
    // /*批量添加商品*/
    // handleSuccess(response) {
    //   if(response.code == 0) {
    //     if((Number(response.result.length) + Number(this.shopListData.length)) > 500){
    //       const num = 500 - Number(this.shopListData.length)
    //       this.$message.error({message: '导入商品数量超过500，还可以导入'+num+'个商品',offset:100})
    //     }else {
    //       this.setChooseGoods(response.result)
    //     }
    //   }else {
    //     this.$message.error({message:response.msg,offset:100})
    //   }
    // },
    /*特价效验*/
    changePrice(from, index, value) {
      // const min = Math.min.apply(Math,[Number(value.fob), Number(value.guidePrice)]);
      // if( value.skuPrice !== '' && value.guidePrice && (Number(value.skuPrice) > min || Number(value.skuPrice) == min) && from == 1){
      //   value.skuPriceTip = '特价价格需小于单体采购价和连锁采购价两者最低价格'
      //   value.skuPrice = ''
      // }
      const max = Math.max.apply(Math,[Number(value.fob), Number(value.guidePrice)]);
      if( value.skuPrice !== '' && value.guidePrice && (Number(value.skuPrice) > max || Number(value.skuPrice) == max) && from == 1){
        value.skuPriceTip = '特惠价格需小于连锁采购价和单体采购价最大值'
        value.skuPrice = ''
      }else if( value.totalQty !== '' && Number(value.totalQty) > Number(value.availableQty) && from == 2){
        // value.totalQtyTip = '限购总数不能超过商品实时库存'
        // value.totalQty = ''
      }
      // else if(value.totalQty !== '' && value.personalQty !== '' && Number(value.personalQty) > Number(value.totalQty) && from == 3) {
      //   value.personalQtyTip = '个人限购总数不能超过限购总数量'
      //   value.personalQty = ''
      // }
    },
    clearTip(from, value) {
      switch (from) {
        case 1:
          value.skuPriceTip = ''
              break
        case 2:
          value.totalQtyTip = ''
              break
        case 3:
          value.personalQtyTip = ''
              break
      }
      this.shopListData = this.shopListData.concat([])
    },
    /*批量操作*/
    batchSetting(from) {
      const allList = this.$refs.shopTable.selection
      if(allList.length <=0){
        this.$message.warning({message: '请先勾选要操作的数据', offset: 100})
        return
      }
      if(from == 'all' || from == 'one') {
        const titleStr = from == 'all' ? '商品总限购数:' : '商品个人限购数:'
        const conStr = from == 'all' ? '批量设置商品总限购数:' : '批量设置商品个人限购数:'
        this.$prompt(titleStr, conStr, {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          customClass: 'myPrompt',
          inputPattern: /(^[0-9]\d*$)/,
          inputErrorMessage: '请输入整数',
          beforeClose: (action, instance, done) => {
            const barcodeAry = []
            if(action == 'confirm'){
              let str = '';
              for( let j=0; j<allList.length; j++){
                const item = allList[j]
                barcodeAry.push(item.barcode)
                if(from == 'all'){
                  if(Number(instance.inputValue) > Number(item.availableQty)){
                    str += '商品编号'+ (j+1) +';'
                  }
                }else {
                  if(item.totalQty !== '' && instance.inputValue !== '' && Number(instance.inputValue) > Number(item.totalQty)){
                    str += '商品编号'+ (j+1) +';'
                  }
                }
              }
              if(str){
                if(from == 'all'){
                  this.$message.warning({message: '限购总数不能超过商品实时库存'+ str, offset: 100})
                }else{
                  // this.$message.warning({message: '个人限购总数不能超过限购总数量'+ str, offset: 100})
                }
              }else{
                this.shopListData.forEach((item) => {
                  if(barcodeAry.indexOf(item.barcode) > -1){
                    if(from == 'all'){
                      item.totalQty = instance.inputValue
                    }else {
                      item.personalQty = instance.inputValue
                    }
                  }
                })
                this.shopListData = this.shopListData.concat([])
                done()
              }
            }else{
              done()
            }
          }
        }).then(() => {}).catch(() => {})
      }else if(from == 'is') {
        this.isDialog = true
      }else {
        let barcodeArys = []
        for( let j=0; j<allList.length; j++){
          const item = allList[j]
          barcodeArys.push(item.barcode)
        }
        this.shopListData.forEach((item) => {
          if(barcodeArys.indexOf(item.barcode) > -1){
            item.isOver = this.isInputValue
          }
        })
        this.isDialog = false
        this.shopListData = this.shopListData.concat([])
      }
    },
    cancelDialog() {
      this.showImportProducts = false;
    },
  }
}
</script>

<style scoped lang="scss">
.serch {
  font-weight: bold;
  .sign {
    display: inline-table;
    width: 3px;
    height: 13px;
    background: linear-gradient(360deg, #1d69c4 1%, #8bbdfc 99%);
    border-radius: 2px;
    margin-right: 8px;
  }
}
.padding20 {
  padding: 15px 20px;
}
.list-box {
  display: flex;
  padding-left: 20px;
  margin: 10px;
  .text {
    font-size: 14px;
    font-weight: bold;
  }
  p {
    padding: 0;
    margin: 0;
  }
}
.errorTip {
  padding: 0;
  margin: 0;
  padding-top: 5px;
  color: #ff2121;
}
.upload-demo {
  display: inline-block;
  margin: 0 10px;
}
.chooses {
  ::v-deep  .el-button--primary {
    background: #4183d5;
  }
  ::v-deep  .el-dialog__body {
    padding: 0 0 10px;
  }
  ::v-deep  .el-dialog__header {
    padding: 10px 16px;
    background: #f9f9f9;
  }
  ::v-deep  .el-dialog__headerbtn {
    top: 13px;
  }
  .conten-box {
    padding: 15px 20px;
    display: flex;
  }
}

</style>
