<template>
  <div>
    <div class="serch">
      <el-row
        type="flex"
        align="middle"
      >
        <span class="sign" />
        <div>店铺公告</div>
      </el-row>
    </div>
    <div
      class="content"
    >
      <el-form
        ref="form"
        :model="formData"
        size="small"
      >
        <el-form-item
          label="快递类型:"
          prop="express"
        >
          <label slot="label"><span style="color:red">*</span>&nbsp;&nbsp;快递类型:</label>
          <div style="display: flex;">
            <el-tag
              v-for="(item) in formData.expressType"
              :key="item"
              closable
              disabled
              @close="handleDeleteExpress(item)"
            >
              {{ item }}
            </el-tag>
            <el-button
              type="primary"
              :disabled="disabled"
              @click="dialogVisible = true"
            >
              添加配送方式
            </el-button>
            <div style="margin-left: 30px;">
              备注:
              <el-input
                v-model="formData.expressRemarks"
                type="textarea"
                placeholder="例如：指定快递联系客服"
                style="width: 250px;"
                :rows="1"
                maxlength="10"
                show-word-limit
                :disabled="disabled"
              />
            </div>
          </div>
        </el-form-item>
        <el-form-item
          label="发货省份:"
          prop="express"
        >
          <label slot="label"><span style="color:red">*</span>&nbsp;&nbsp;发货省份:</label>
          <span style="margin-right: 5px;">省份</span>
          <el-select
            v-model="formData.deliveryProvinceCode"
            placeholder="请选择"
            :disabled="disabled"
            @change="handleChangeProvince"
          >
            <el-option
              v-for="item in deliveryProvinceOptions"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
          <span style="margin-left: 20px;margin-right: 5px;">城市</span>
          <el-select
            v-model="formData.deliveryCityCode"
            placeholder="请选择"
            :disabled="disabled"
          >
            <el-option
              v-for="item in deliveryCityOptions"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          label="订单处理时间:"
        >
          <el-input
            v-model="formData.orderHandleTime"
            type="textarea"
            placeholder="例如：工作日早八点半 晚5点半 非工作日不处理"
            style="width: 500px"
            maxlength="50"
            show-word-limit
            :rows="3"
            :disabled="disabled"
          />
        </el-form-item>
        <el-form-item
          label="发货时间:"
        >
          <el-input
            v-model="formData.deliveryHandleTime"
            type="textarea"
            placeholder="例如：每天15点前的订单当天发，15点后的订单第二天发；周六日节假日正常发货"
            style="width: 500px"
            maxlength="50"
            show-word-limit
            :rows="3"
            :disabled="disabled"
          />
        </el-form-item>
        <el-form-item
          label="其他:"
        >
          <quillEditor
            :form-data="formData"
            :from-editor="1"
            :disabled="disabled"
          />
          <el-button
            type="primary"
            style="margin-left: 20px"
            @click="handleEdit"
          >
            {{ disabled ? '编辑' : '保存' }}
          </el-button>
        </el-form-item>
        <el-form-item
          label="示意图:"
        >
          <img
            src="@/assets/image/product/gonggao.png"
            alt=""
          >
        </el-form-item>
      </el-form>
    </div>
    <el-dialog
      title="添加配送方式"
      :visible="dialogVisible"
      @close="dialogVisible = false"
    >
      <el-form
        size="small"
      >
        <el-form-item
          label="快递类型:"
        >
          <el-select
            v-model="deliveryCode"
            placeholder="请选择"
            filterable
          >
            <el-option
              v-for="item in deliveryOptions.filter((item) => {
                return !formData.expressType.includes(item.id)
              })"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <span slot="footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button
          type="primary"
          @click="handleAddExpressType"
        >确定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { queryShopNotice, getDeliveryNames, getLevelList, shopNoticeSave } from '@/api/other/index';
import quillEditor from './component/myQuillEditor';

export default {
  name: 'Notice',
  components: { quillEditor },
  data() {
    return {
      dialogVisible: false,
      disabled: true,
      deliveryCode: '',
      deliveryOptions: [],
      deliveryProvinceOptions: [],
      deliveryCityOptions: [],
      formData: {
        id: null,
        expressType: [],
        deliveryProvinceCode: '',
        deliveryCityCode: '',
        expressRemarks: '',
        content: '',
        orderHandleTime: '',
        deliveryHandleTime: '',
      },
    };
  },
  created() {
    getDeliveryNames().then((res) => {
      if (res.code === 0) {
        this.deliveryOptions = res.data;
      }
    });
    queryShopNotice().then((res) => {
      if (res.code === 0) {
        if (res.result) {
          // Object.keys(this.formData).forEach((item) => {
          //   this.formData[item] = res.result[item];
          // });
          this.formData = { ...this.formData, ...res.result };
          this.formData.deliveryProvinceCode = `${this.formData.deliveryProvinceCode || ''}`;
          this.formData.deliveryCityCode = `${this.formData.deliveryCityCode || ''}`;
          if (this.formData.expressType) {
            this.formData.expressType = this.formData.expressType.split(',');
          } else {
            this.formData.expressType = [];
          }
        }
        this.getLevelList();
        if (this.formData.deliveryProvinceCode) {
          this.getLevelList(this.formData.deliveryProvinceCode);
        }
        // this.$set(this.formData, this.formData);
      } else {
        this.$message.error(res.msg);
      }
    });
  },
  methods: {
    handleDeleteExpress(tag) {
      if (this.disabled) return false;
      this.formData.expressType.splice(this.formData.expressType.indexOf(tag), 1);
    },
    handleChangeProvince(val) {
      this.formData.deliveryCityCode = '';
      this.getLevelList(val);
    },
    getLevelList(id) {
      const params = { parentId: id || '' };
      getLevelList(params).then((res) => {
        if (res.code === 0) {
          if (!id) {
            this.deliveryProvinceOptions = res.data;
          } else {
            this.deliveryCityOptions = res.data;
          }
        }
      });
    },
    handleAddExpressType() {
      if (this.deliveryCode === '') {
        this.$message.warning('请选择快递类型');
        return false;
      }
      if (this.formData.expressType.length === 3) {
        this.$message.warning('最多可选择3个快递类型');
        return false;
      }
      this.formData.expressType.push(this.deliveryCode);
      this.dialogVisible = false;
      this.deliveryCode = '';
    },
    handleEdit() {
      if (this.disabled) {
        this.disabled = false;
        return false;
      }
      if (!this.formData.expressType.length) {
        this.$message.warning('请选择快递类型');
        return false;
      }
      if (this.formData.deliveryProvinceCode === '') {
        this.$message.warning('请选择省份');
        return false;
      }
      if (this.formData.deliveryCityCode === '') {
        this.$message.warning('请选择城市');
        return false;
      }
      const params = { ...this.formData };
      params.expressType = [...params.expressType].join(',');
      shopNoticeSave(params).then((res) => {
        if (res.code === 0) {
          this.$message.success(res.msg || '保存成功');
          this.disabled = true;
        } else {
          this.$message.error(res.msg || '保存失败');
        }
      });
    },
  },
};
</script>

<style scoped lang="scss">
::v-deep   .el-form-item__label {
  width: 100px;
}
.el-tag {
  margin-right: 10px;
}
.serch {
  font-weight: bold;
  padding: 15px 20px;
  .sign {
    display: inline-table;
    width: 3px;
    height: 13px;
    background: linear-gradient(360deg, #1d69c4 1%, #8bbdfc 99%);
    border-radius: 2px;
    margin-right: 8px;
  }
}
.content {
  padding: 15px;
  color: #606266;
}
.expressOne {
  padding: 5px;

}
.boxDiv{
  display: flex;
  justify-content: left;
  p{
    margin: 0;
    padding: 0;
    padding-top: 20px;
  }
  .imgBox{
    width: 260px;
    border: 1px solid rgba(0,0,0,0.15);
    border-radius: 4px;
    padding: 10px;
    img{
      width: 100%;
    }
  }
}
</style>
