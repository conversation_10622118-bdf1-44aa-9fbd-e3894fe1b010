<template>
  <div class="">
    <el-dialog
      title="添加优惠券"
      :close-on-click-modal="false"
      :visible="dialogVisible"
      width="1226px"
      @close="closeDialog"
    >
      <el-row class="condition">
        <el-form
          ref="ruleForm"
          :inline="true"
          :model="ruleForm"
          :rules="rules"
          size="small"
        >
          <el-form-item prop="name">
            <span
              slot="label"
              style="
                padding: 5px;
                position: relative;
                top: 1px;
                right: -1px;
              "
            >优惠券名称</span>
            <el-input v-model.trim="ruleForm.name" />
          </el-form-item>
          <el-form-item prop="name">
            <span
              slot="label"
              style="
                padding: 5px;
                position: relative;
                top: 1px;
                right: -1px;
              "
            >优惠券ID</span>
            <el-input v-model.trim="ruleForm.id" />
          </el-form-item>
          <el-form-item prop="name">
            <span
              slot="label"
              style="
                padding: 5px;
                position: relative;
                top: 1px;
                right: -1px;
              "
            >券状态</span>
            <el-select
              v-model.trim="ruleForm.couponStatus"
              placeholder="请选择"
            >
              <el-option
                v-for="item in statusOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item class="searchBtn">
            <el-button
              type="primary"
              @click="couponList('ruleForm')"
            >
              查询
            </el-button>
          </el-form-item>
        </el-form>
        <el-divider />
      </el-row>
      <el-row class="actlist">
        <el-table
          border
          height="400"
          stripe
          :data="tableData"
          :header-cell-style="{ background: '#f9f9f9' }"
          @selection-change="handleSelectionChange"
          :row-key='getRowKey'
        >
          <el-table-column
            type="selection"
            :reserve-selection="true"
            :selectable="selectable"
            width="55">
          </el-table-column>
<!--          <el-table-column-->
<!--            width="150"-->
<!--            align="center"-->
<!--          >-->
<!--            <template slot-scope="scope">-->
<!--              &lt;!&ndash; 1可选择，2已领完不可选，3已过期不可选 &ndash;&gt;-->
<!--              <el-radio-group-->
<!--                v-if="scope.row.chooseCouponLogicalStatus == '1'"-->
<!--                v-model="templateRadio"-->
<!--                @change="getTemplateRow(scope.$index, scope.row)"-->
<!--              >-->
<!--                <el-radio-->
<!--                  ref="selTable"-->
<!--                  :label="scope.row.id"-->
<!--                  style="margin-left: 10px"-->
<!--                >-->
<!--                  <i />-->
<!--                </el-radio>-->
<!--              </el-radio-group>-->
<!--              <span v-else>{{ scope.row.chooseCouponLogicalStatusName }}</span>-->
<!--            </template>-->
<!--          </el-table-column>-->
          <el-table-column
            prop="name"
            label="优惠券名称"
            width="150"
          />
          <el-table-column
            prop="id"
            label="券ID"
            width="150"
          />
          <el-table-column
            label="使用时间"
            width="150"
          >
            <template slot-scope="scope">
              <div>
                <div v-if="scope.row.startTime">
                  {{ scope.row.startTime | handleTime }} ~ {{ scope.row.endTime | handleTime }}
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column
            prop="typeName"
            label="券优惠方式"
            width="150"
          />
          <el-table-column
            prop="minMoneyToEnable"
            label="门槛&金额"
            width="150"
          >
            <template slot-scope="{row}">
              <div v-if="Number(row.type)===2">
                满{{ row.minMoneyToEnable }}元，打{{ row.discount }}折
                <span v-if="row.maxMoneyInVoucher">，最高减{{ row.maxMoneyInVoucher }}元</span>
              </div>
              <div v-else>
                <span v-if="Number(row.reduceType)===1"
                >每满{{ row.minMoneyToEnable }}元，减{{ row.moneyInVoucher }}元，最高减{{ row.discount }}元</span>
                <span v-else>满{{ row.minMoneyToEnable }}元减{{ row.moneyInVoucher }}元</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column
            prop="description"
            label="适用商品"
            width="150"
          />
          <el-table-column
            prop="createTime"
            width="180"
            label="创建时间"
          >
            <template slot-scope="{row}">
              <div>{{ formatDate(row.createTime) }}</div>
            </template>
          </el-table-column>
<!--          <el-table-column-->
<!--            prop="remainStock"-->
<!--            label="剩余张数"-->
<!--          />-->
        </el-table>
        <div
          v-if="page.totalPage != 0"
          class="pagination-container"
        >
          <div class="pag-text">
            共 {{ page.totalCount }} 条数据，每页{{ 10 }}条，共{{
              Math.ceil(page.totalCount / 10)
            }}页
          </div>
          <el-pagination
            background
            :page-sizes="[10]"
            prev-text="上一页"
            next-text="下一页"
            layout="sizes, prev, pager, next, jumper"
            :total="page.totalCount"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </el-row>
      <span
        slot="footer"
        class="dialog-footer"
      >
        <el-button @click="closeDialog">取 消</el-button>
        <el-button
          type="primary"
          @click="handleQuery"
        >确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { couponList } from '@/api/activity/index';

export default {
  name: 'AddCoupon',
  filters: {
    handleTime(time) {
      return time ? new Date(time + 8 * 3600 * 1000).toJSON().substr(0, 19).replace('T', ' ') : '';
    },
  },
  props: {
    dialogVisible: {
      type: Boolean,
      default: false,
    },
    templateId: {
      type: Number,
      default: null,
    },
  },
  data() {
    return {
      selRow: null,
      templateSelection: {},
      templateRadio: false,
      ruleForm: {
        name: '',
        id: '',
        couponStatus: ''
      },
      rules: { name: [] },
      tableData: [],
      page: {
        pageNum: 1,
        pageSize: 10,
        totalPage: null,
        totalCount: null,
      },
      statusOptions: [
        {
          label: '全部',
          value: '',
        },
        {
          label: '未开始',
          value: 0,
        },
        {
          label: '进行中',
          value: 1,
        }
      ],
    };
  },
  watch: {},
  mounted() {
    this.couponList();
  },
  methods: {
    getRowKey(row){
      return row.id
    },
    selectable(row){
      if (Number(row.chooseCouponLogicalStatus)===1){
        return true
      }
      return false
    },
    couponList() {
      const query = {
        name: this.ruleForm.name,
        id: this.ruleForm.id,
        couponStatus: this.ruleForm.couponStatus,
        pageNum: this.page.pageNum,
        pageSize: this.page.pageSize
      };
      couponList(query).then((res) => {
        if (res.success) {
          const { list, pageNo, totalCount, totalPage } = res.data;
          this.tableData = list;
          this.page.pageNum = pageNo;
          this.page.totalPage = totalPage;
          this.page.totalCount = totalCount;
          if (this.templateId) {
            this.templateRadio = this.templateId;
          }
        }
      });
    },
    closeDialog() {
      this.ruleForm.name = '';
      this.ruleForm.id = '';
      this.ruleForm.couponStatus = ''
      this.$emit('cancelSel');
    },
    handleSelectionChange(row) {
      this.templateSelection = row;
    },
    handleQuery() {
      this.$emit('querySel', this.templateSelection);
    },
    resetForm(formName) {
      this.$refs[formName].resetFields();
    },
    handleCurrentChange(val) {
      this.page.pageNum = val;
      this.couponList();
    },
     handleSizeChange(sizi){
      this.page.pageSize = sizi;
      this.couponList();
    },
  },
};
</script>

<style lang="scss" scoped>
::v-deep  .el-form-item__label {
  padding: 0;
}
::v-deep  .el-input__inner {
  border-radius: 0;
}
::v-deep  .el-dialog__body {
  padding: 0 20px;
}

::v-deep  .el-dialog__wrapper .el-dialog__header {
  background: #f9f9f9;
}

::v-deep  .el-dialog__wrapper .el-dialog__title {
  font-size: 16px;
  font-weight: 500;
  color: #333333;
}

::v-deep  .el-divider--horizontal {
  width: 104%;
  margin: 0 0 0 -20px;
}

.condition {
  form {
    padding: 20px 20px 0;
  }
}

.actlist {
  padding: 20px 20px;

  .serch {
    padding: 20px 0;
    font-weight: 500;
  }
}

.el-pagination {
  margin-top: 10px;
  text-align: right;
}

::v-deep  .el-pagination .el-pagination__total {
  float: left;
}
</style>
