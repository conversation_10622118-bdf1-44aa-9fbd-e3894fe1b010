<template>
  <el-dialog
      title="操作日志"
      :visible="dialogVisible"
      width="600px"
      :before-close="handleClose">
    <div class="titlediv">{{name}}</div>
    <el-table
        v-loading="loading"
        :data="tableConfig.data"
        border
        height="400"
        style="width: 100%">
      <el-table-column
        prop="operationStr"
        label="操作项"
        width="200"      
      >
        <template slot-scope="scope">
          <div>
            <div v-if="scope.row.operationType === 1">
              {{scope.row.operationStr || ''}}
            </div>
            <div v-if="scope.row.operationType === 2">
               <div>{{scope.row.operationStr || ''}}</div>
               <div>{{'旧erp编码：'+scope.row.erpOldCode || ''}}</div>
               <div>{{'新erp编码：'+scope.row.erpNewCode || ''}}</div>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column
          prop="ctime"
          label="操作时间"
          width="200"
      >
        <template slot-scope="scope">
          <div>
            {{formatDate(scope.row.ctime,'YMD HMS') }}
          </div>
        </template>
      </el-table-column>
      <el-table-column
          prop="operator"
          width="200"
          label="操作人"/>
    </el-table>
  </el-dialog>
</template>

<script>
import {getSkulog} from '@/api/customer-management'

export default {
  name: "operationLog",
  props: {
    merchantId: {
      type: Number,
      default: ''
    },
    name: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      dialogVisible: true,
      loading: true,
      tableConfig: {
        data: []
      },
    }
  },
  mounted() {
    this.getSkulog()
  },
  methods: {
    async getSkulog() {
      try {
        const res = await getSkulog({merchantId: this.merchantId})
        if (res.code === 0) {
          this.tableConfig.data = res.data
        } else {
          // this.$message.error(res.msg)
          this.$alert(res.msg, {type: 'error'})
        }
      } catch (e) {
        console.log(e)
      }
      this.loading = false
    },
    handleClose() {
      this.$emit('update:skuLogDialogVisible', false)
    }
  }
}
</script>

<style scoped lang="scss">
::v-deep   .el-table thead th {
  background: #f9f9f9;
  border: none;

  .cell {
    font-family: PingFangSC, PingFangSC-Regular;
    font-weight: 400;
    text-align: left;
    color: rgba(51, 51, 51, 0.85);
    line-height: 22px;
  }
}

::v-deep   .el-table__body-wrapper {
  font-size: 12px;
  color: #666666;
}
::v-deep  .el-dialog__body{
  padding-top: 10px;
}
.titlediv{
  padding-bottom: 10px;
  font-size: 16px;
  font-weight: bold;
}
</style>
