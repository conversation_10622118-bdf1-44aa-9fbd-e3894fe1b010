export const searchItemFbp = {
    formItems : [
      {
        label: '商品编码',
        prop: 'barcode',
        component: 'el-input',
        attrs: {
          placeholder: '请输入'
        }
      },
      {
        label: '商品名称',
        prop: 'showName',
        component: 'el-input',
        attrs: {
          placeholder: '请输入'
        }
      },
      {
        label: '生产厂家',
        prop: 'manufacturer',
        component: 'el-input',
        attrs: {
          placeholder: '请输入'
        }
      },
      {
        label: '商品ERP编码',
        prop: 'erpCode',
        component: 'el-input',
        attrs: {
          placeholder: '请输入'
        }
      },
      {
        label: '商品条码',
        prop: 'code',
        component: 'el-input',
        attrs: {
          placeholder: '请输入'
        }
      },
      {
        label: '批准文号',
        prop: 'approvalNumber',
        component: 'el-input',
        attrs: {
          placeholder: '请输入'
        }
      },
      {
        label: '商品状态',
        prop: 'status',
        component: 'el-select',
        slotName: 'status'
      },
      {
        label: '库存ERP同步',
        prop: 'stockSyncErp',
        component: 'el-select',
        attrs: {
          options: [
            {
              label: '全部',
              value: ''
            },
            {
              label: '是',
              value: 1
            },
            {
              label: '否',
              value: 0
            },
          ]
        }
      },
      {
        label: '商品分类',
        prop: 'categoryId',
        component: 'el-cascader',
        slotName: 'category'
      },
      {
        label: '商品特性',
        prop: 'characteristic',
        component: 'el-select',
        attrs: {
          options: [
            {
              label: '全部',
              value: 0
            },
            {
              label: '特长药',
              value: 1
            },
            {
              label: '专供',
              value: 2
            },
            {
              label: '独家代理',
              value: 3
            },
            {
              label: '特许专供',
              value: 4
            }
          ]
        }
      },
      {
        label: '商品类型',
        prop: 'saleType',
        component: 'el-select',
        attrs: {
          options: [
            {
              label: '全部',
              value: ''
            },
            {
              label: '药品',
              value: 1
            },
            {
              label: '非药品',
              value: 2
            }
          ]
        }
      },
      {
        label: '价格ERP同步',
        prop: 'priceSyncErp',
        component: 'el-select',
        attrs: {
          options: [
            {
              label: '全部',
              value: ''
            },
            {
              label: '是',
              value: 1
            },
            {
              label: '否',
              value: 0
            }
          ]
        }
      },
      {
        label: '商圈名称',
        prop: 'busName',
        component: 'el-input',
        attrs: {
          placeholder: '请输入'
        }
      },
      {
        label: '是否有主图',
        prop: 'havePic',
        component: 'el-select',
        attrs: {
          options: [
            {
              label: '全部',
              value: ''
            },
            {
              label: '有图',
              value: 1
            },
            {
              label: '无图',
              value: 2
            }
          ]
        }
      },
      {
        label: '库存状态',
        prop: 'stockStatus',
        component: 'el-select',
        attrs: {
          options: [
            {
              label: '全部',
              value: ''
            },
            {
              label: '无库存',
              value: '0'
            },
            {
              label: '有库存',
              value: '1'
            }
          ]
        }
      },
      {
        label: 'sku编码',
        prop: 'csuid',
        component: 'el-input',
        attrs: { placeholder: '请输入完整的sku编码' },
      },
      {
        label: '创建时间',
        prop: 'createTime',
        component: 'el-date-picker',
        // colSpan: 12,
        attrs: {
          type: 'daterange',
          startPlaceholder: '开始日期',
          endPlaceholder: '结束日期',
          rangeSeparator: '至',
          valueFormat: 'yyyy-MM-dd',
        },
      },
      // pop 012 自营1234
      {
        label: '商品来源',
        prop: 'activityType',
        component: 'el-select',
        attrs: {
          options: [
            {
              label: '全部',
              value: '',
            },
            {
              label: '普通商品',
              value: 0,
            },
            {
              label: '拼团商品',
              value: 1,
            },
            {
              label: '赠品',
              value: 2,
            },
          ],
        },
      },
      {
        label: '活动ID',
        prop: 'activityId',
        component: 'el-input',
        attrs: { placeholder: '请输入' },
      },
      {
        label: '原商品编码',
        prop: 'originalBarcode',
        component: 'el-input',
        colSpan: 6,
        attrs: {
          placeholder: '请输入完整原商品编码'
        }
      },{
        label: '是否有追溯码',
        prop: 'tracingCode',
        component: 'el-select',
        slotName: 'tracingCode',
        isHidden: false,
        // attrs: {
        //   options: [
        //     {
        //       label: '全部',
        //       value: null
        //     },
        //     {
        //       label: '是',
        //       value: 1
        //     },
        //     {
        //       label: '否',
        //       value: 0
        //     },
        //     {
        //       label: '未录入',
        //       value: 2
        //     }
        //   ]
        // }
      },
      {
        label: '商品规格',
        prop: 'spec',
        component: 'el-input',
        colSpan: 6,
        attrs: {
          placeholder: '请输入'
        }
      },
      {
        label: '是否已关联标品',
        prop: 'tiedMeProduct',
        component: 'el-select',
        colSpan: 6,
        attrs: {
          placeholder: '请选择',
          options:[
            {value:"", label:"全部"},
            {value:"1", label:"是"},
            {value:"0", label:"否"}
          ]
        }
      },
    ],
    formItemsIn: [
      {
        label: '条码',
        prop: 'code',
        component: 'el-input',
        attrs: {
          placeholder: '请输入完整的条码',
          onkeyup: "value=value.replace(/[^\\d]/g,'')",
        },
      },
      {
        label: '商品搜索',
        prop: 'productName',
        component: 'el-input',
        attrs: {
          placeholder: '请输入商品名称/助记码'
        }
      },
      {
        label: '生产厂家',
        prop: 'manufacturer',
        component: 'el-input',
        attrs: {
          placeholder: '请输入'
        }
      },
      {
        label: '批准文号',
        prop: 'approvalNumber',
        component: 'el-input',
        attrs: {
          placeholder: '请输入完整的批准文号'
        }
      },
    ]
}
