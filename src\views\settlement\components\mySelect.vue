<template>
  <!-- 	<el-autocomplete v-model="data" :fetch-suggestions="query" @select="selected" :size="size" @clear="clear" @blur="data = temp" :placeholder="temp" clearable>
		<template slot="append">

		</template>
	</el-autocomplete> -->
  <div style="display: flex">
    <!-- {{ list }} -->
    <el-input class="input-with-select" :size="size">
      <template slot="prepend">
        <span>{{ label }}</span>
      </template>
      <template slot="append">
        <el-select
          placeholder="请选择"
          style="width: 200px; margin-left: 0px"
          v-model="value1"
          @input="change"
          :multiple="multiple"
          collapse-tags
          :size="size"
          clearable
        >
          <el-option
            v-for="item in list"
            :key="item[valueProp]"
            :value="item[valueProp]"
            :label="item[labelProp]"
          ></el-option>
        </el-select>
      </template>
    </el-input>
  </div>
</template>

<script>
export default {
  props: {
    value: '',
    size: {
      default: 'small',
      type: String
    },
    label: {
      default: '',
      type: String
    },
    list: {
      default: () => [],
      type: Array
    },
    multiple: {
      default: false,
      type: Boolean
    },
    valueProp: {
      default: 'value',
      type: String
    },
    labelProp: {
      default: 'label',
      type: String
    }
  },
  mounted() {
    console.log(this.list)
  },
  // data() {
  //   return {
  //     value1: this.value
  //   }
  // },
  computed: {
    value1: {
      // getter
      get() {
        return this.value
      },
      // setter
      set(newValue) {
        // 注意：我们这里使用的是解构赋值语法
        this.$emit('input', newValue)
      }
    }
  },
  methods: {
    change(val) {
      // this.$emit('input', val)
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .input-with-select {
  width: 0px !important;
  input {
    padding: 0 !important;
    border-right: none;
  }
  > input {
    width: 0px !important;
  }
}
::v-deep .input-with-select .el-input-group__append {
    background: white;
  }
</style>
