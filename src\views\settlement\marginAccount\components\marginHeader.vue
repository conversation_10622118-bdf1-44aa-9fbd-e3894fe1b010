<template>
  <div>
    <!-- 中间插入的数据和充值保证金等按钮 -->
    <marginInsert ref="marginInsert"></marginInsert>
    <div>
        <el-form label-position="right" label-width="10px" style="margin-top: 10px">
          <el-row type="flex" style="flex-wrap: wrap;">
            <el-col :xs="24" :md="8" :xl="8">
              <el-form-item>
                <span class="search-title">变动日期</span>
                <div class="left-input">
                  <el-date-picker
                    size="small"
                    v-model="form.changeTime"
                    type="daterange"
                    format="yyyy-MM-dd"
                    value-format="timestamp"
                    range-separator="至"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                    style="width: 100%"
                  />
                </div>
              </el-form-item>
            </el-col>

            <el-col :xs="24" :md="6" :xl="6">
              <el-form-item>
                <el-input
                  size="small"
                  v-model="form.orderNo"
                  clearable
                  placeholder="请输入订单号"
                >
                  <template slot="prepend">订单号</template>
                </el-input>
              </el-form-item>
            </el-col>

            <div style="display: flex;">
              <el-form-item>
                <my-select
                  label="类型"
                  :list="selectData.accountTypeList"
                  labelProp="name"
                  valueProp="code"
                  :multiple="false"
                  :value="form.comprehensiveType"
                  @input="(val) => (form.comprehensiveType = val)"
                ></my-select>
              </el-form-item>
              <div style="margin-left: 10px">
                <el-button size="small" type="primary" @click="search">查询</el-button>
                <el-button size="small" @click="resetForm">重置</el-button>
                <!-- <el-button size="small" type="primary"> 导出 </el-button> -->
              </div>
            </div>
          </el-row>

        </el-form>
        <el-button class="import-template" size="small" type="primary" @click="handleExport">
          导出明细
        </el-button>        
    </div>
    <export-tip
      :change-export="changeExport"
      @handleExoprClose="handleExoprClose"
      @handleChangeExport="handleChangeExport"
    />
  </div>
</template>

<script>
import { getMarginChangeType, exportFundFlow } from '@/api/settlement/marginAccount/index'
import commonHeader from '../../components/common-header.vue'
import mySelect from '../../components/mySelect.vue'
import marginInsert from './marginInsert.vue'
import exportTip from '@/views/other/components/exportTip'
// import { exportAfterSaleList } from '@/api/afterSaleManager/index'

export default {
  components: {
    commonHeader,
    mySelect,
    marginInsert,
    exportTip
  },
  created() {
    let {orderNo} = this.$route.query
    this.form.orderNo = orderNo
    this.search()
  },
  activated() {
    let {orderNo} = this.$route.query
    this.form.orderNo = orderNo
    this.search()
  },
  mounted() {
    // 调用接口获取省份列表赋值给selectData.provinceList
    // 开始的时候就调用search方法渲染一次表格数据
    getMarginChangeType().then((res) => {
      if (res.code == 0) {
        this.selectData.accountTypeList = res.result
        this.$parent.setChangeType(this.selectData.accountTypeList)
      }
    })
    this.search()
  },
  data() {
    return {
      changeExport: false, // 导出功能测试
      form: {
        orderNo: null, // 商户
        comprehensiveType: null, // 类型
        changeTime: null // 变动时间
      },
      selectData: {
        accountTypeList: [], // 审核状态列表
      } // 下拉框列表条件
    }
  },
  methods: {
    // openDialog(value) {
    //   this.$refs.newDialog.openDialog(value);
    // }, // 打开新增弹框
    search() {
      //   console.log(this.form);
      //   this.loadTableData();
      this.$emit('search', this.form)
      this.$refs.marginInsert.loadData()
    }, // 查询逻辑
    resetForm() {
      this.form = {
        orderNo: null, // 商户
        comprehensiveType: null, // 类型
        changeTime: null // 变动时间
      }
    },

    handleChangeExport(info) {
      const that = this;
      this.changeExport = false;
      if (info === "go") {
        that.$router.push({ path: "/downloadList" });
      }
    },
    handleExoprClose() {
      this.changeExport = false;
    },
    handleExport() {
      let subData = {
        fundPropertyStatus: 1,
        orderNo: this.form.orderNo || '',
        comprehensiveType: this.form.comprehensiveType || '',
        changeStartTime: this.form.changeTime?.[0] || '',
        changeEndTime: this.form.changeTime?.[1] || '',
      }
      exportFundFlow(subData).then((res) => {
        if (res.success) {
          this.changeExport = true;
        } else {
          this.$message.error("导出失败！");
        }
      });
    },
  }
}
</script>

<style lang="scss" scoped>
.import-template {
  // display: flex;
  // align-items: center;
  margin: -10px 0 10px 10px;
}
</style>
