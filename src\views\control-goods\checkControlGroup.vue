<template>
  <div class="controlGroupWrpper">
    <div class="headerBox">
      <part-title title="查看控销组" />
      <el-button type="primary" size="small" @click="goBack">返回</el-button>
    </div>
    
    <el-tabs v-model="activeName" type="card" @tab-click="handleClick">
      <el-tab-pane label="控销药店" name="merchantType">
        <ControlMerchant v-if="activeName === 'merchantType'" />
      </el-tab-pane>
      <el-tab-pane label="控销商品" name="skuType">
        <ControlSku v-if="activeName === 'skuType'" />
      </el-tab-pane>
  </el-tabs>
  </div>
</template>
<script>
import Pagination from '@/components/Pagination';
import partTitle from '@/components/part-title/index';
import ControlMerchant from './modules/controlMerchant';
import ControlSku from './modules/controlSku';
import { merchantPage } from '@/api/goods/controlGoods.js';

export default {
  name: 'CheckControlGroup',
  components: {
    Pagination,
    partTitle,
    ControlMerchant,
    ControlSku,
  },
  data() {
    return {
      activeName: 'merchantType',
    }
  },
  created() {
    // this.getInfo();    
  },
  activated() {
    this.activeName = this.$route.query.activeName || 'merchantType';
  },
  methods: {
    handleClick(tab, event) {
      console.log(tab, event);
    },
    goBack() {
      this.$router.go(-1);
    }
  }
}

</script>
<style lang="scss" scoped>
.controlGroupWrpper {
  padding: 20px;
  .headerBox {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
}
</style>