import request from '@/utils/request'
import util from '../../utils/util'
//所有药店查询
export function apiAllBuyers(params) {
  return request({
    url: '/shopBlackBuyer/allBuyers',
    method: 'get',
    params: params
  })
}

//药店黑名单列表
export function apiShopBlackBuyerList(params) {
  return request({
    url: '/shopBlackBuyer/list',
    method: 'get',
    params: params
  })
}

//移除黑名单
export function apiRemoveBuyer(id) {
  return request({
    url: '/shopBlackBuyer/removeBuyer/' + id + '',
    method: 'post'
  })
}

//单个新增黑名单
export function apiAddBlackBuyer(data) {
  return request({
    url: '/shopBlackBuyer/addBlackBuyer',
    method: 'post',
    data
  })
}

//批量加入黑名单
export function apiBatchAddBlackBuyer(data) {
  return request({
    url: '/shopBlackBuyer/batchAddBlackBuyer',
    method: 'post',
    data
  })
}

// 获取导入模板
export function apiDownloadTemplate(params) {
  return request.get('/uploadFile/downloadTemplate?fileName=' + params, { responseType: 'blob' })
}

//导出
export function exportData(params) {
  return request({
    url: '/shopBlackBuyer/export',
    method: 'get',
    params
  })
}
//批量删除黑名单模板
export function apiDownloadTemplateNormal(params) {
  request.get('/uploadFile/downloadTemplate?fileName=' + params, { responseType: 'blob' }).then(res => {
    util.exportExcel(res, params);
  })
}
//批量删除黑名单
export function apiBatchDeleteBlackBuyers(file) {
  const fileForm = new FormData();
  fileForm.append('file', file);
  return request({
    url: '/shopBlackBuyer/importDeleteBlackBuyers',
    method: 'post',
    data: fileForm
  })
}
//删除控销商品组
export function apiDeleteControlUserGroup(groupId) {
  const data = new FormData();
  data.append('groupId', groupId);
  return request({
    url: `/skuMerchantGroup/deleteByGroupId`,
    method: 'post',
    data: data
  })
}
//商品控销药店名称
export function apiGetShopName(data) {
  return request({
    url: '/skuMerchantGroup/merchantProductPage',
    method: 'post',
    data
  }) 
}
