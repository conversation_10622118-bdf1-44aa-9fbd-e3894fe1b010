<template>
  <div class="main-box">
    <div class="explain-search">
      <div class="con-title" style="padding-left: 0">
        <span class="line"></span><span>全部流程</span>
      </div>
      <steps style="padding-bottom: 5px;border-bottom: 1px solid #f0f2f5;" :active="0"></steps>
      <div class="con-title" style="padding-left: 0;margin: 20px 0">
        <span class="line"></span>
        <span>设置控销信息</span>
      </div>
      <el-form size="small" :inline="true" ref="searchData" :model="searchData" :rules="rules">
        <!-- <el-form-item class="my-label" style="padding-left: 20px;">
          <span slot="label"><i class="red-span">*</i>控销区域</span>
          <el-select
            v-model.trim="searchData.provinceCode"
            placeholder="请选择"
            style="width: 150px"
            @change="saleChange"
          >
            <el-option
              v-for="(item, index) in sellerAry"
              :key="index"
              :label="item.prov"
              :value="item.provId"
            ></el-option>
          </el-select>
        </el-form-item> -->
        <el-form-item
          class="my-label"
          style="display: block;padding-left: 20px;"
          prop="time"
          label="生效时间:"
        >
            <!-- <span slot="label"><i class="red-span" style="color: #F5222D;margin-right: 5px">*</i>生效时间</span> -->
            <el-date-picker
                size="small"
                popper-class="install-contr-cell-class"
                v-model.trim="searchData.time"
                type="datetime"
                value-format="yyyy-MM-dd HH:mm:ss"
                placeholder="选择日期时间"
                @change="timeChange"
                prefix-icon="el-icon-date"
            ></el-date-picker>
          <!-- <el-date-picker
            v-model.trim="timeAry"
            type="datetimerange"
            format="yyyy-MM-dd HH:mm:ss"
            :default-time="['00:00:00', '23:59:59']"
            value-format="yyyy-MM-dd HH:mm:ss"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            @change="timeChange"
            prefix-icon="el-icon-date"
          >
          </el-date-picker> -->
        </el-form-item>
        <el-form-item class="my-label" label="状态：" style="display: block;padding-left: 52px;">
          <el-radio-group v-model="searchData.status">
            <el-radio :label="1">启用</el-radio>
            <el-radio :label="0">停用</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item class="my-label controlSaleName" style="display: block;padding-left: 30px;" label="控销名称：">
          <el-input type="textarea" v-model="searchData.controlSaleName" maxlength="50" show-word-limit></el-input>
        </el-form-item>
        <el-form-item
          class="search-btn"
          style="display: block;text-align: right;"
        >
          <el-button size="small" @click="returnList">取消</el-button>
          <el-button
            size="small"
            type="info"
            @click="toSeleProd"
            :class="'status-'+ (isNext?0:1)"
            :disabled="isNext"
            >下一步</el-button
          >
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>
<style>
@import '../../../assets/css/changeElement.scss';
</style>
<script>
// import { getProvinceList } from '@/api/goods/newGood.js'
import steps from './../components/steps';
export default {
  name: 'controlBasic',
   components: {
    steps,
  },
  props: {
    basicEdit: {
      type: Object,
      default: () => {}
    },
    isEditData: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      searchData: {
        provinceCode: '',
        time: '',
        status: 1,
        controlSaleName: ''
      },
      isNext: true,
      btnStatus: 0,
      sellerAry: [],
      isSale: true,
      isTime: false,
      rules: {
        time: [
          {  required: true, message: '请设置生效时间', trigger: 'change' }
        ],
      }
    }
  },
  watch: {
    basicEdit: {
      deep: true,
      handler: function (newVal) {
        this.searchData.time = this.basicEdit.time;
        this.searchData.status = this.basicEdit.status;
        this.searchData.controlSaleName = this.basicEdit.controlSaleName;
        this.searchData = JSON.parse(JSON.stringify(this.basicEdit));
        this.isNext = !this.isEditData;
        // getProvinceList().then((res) => {
        //   if (res.code == 0) {
        //     this.sellerAry = res.data
        //     this.timeAry = [this.basicEdit.beginTime, this.basicEdit.endTime]
        //     this.searchData = JSON.parse(JSON.stringify(this.basicEdit))
        //     this.isNext = !this.isEditData
        //   } else {
        //     this.sellerAry = []
        //   }
        // })
      }
    }
  },
  created() {
      this.searchData.time = this.basicEdit.time;
      this.searchData.status = this.basicEdit.status
      this.searchData.controlSaleName = this.basicEdit.controlSaleName;
      this.searchData = JSON.parse(JSON.stringify(this.basicEdit))
      this.isNext = !this.isEditData
    // getProvinceList().then((res) => {
    //   if (res.code == 0) {
    //     this.sellerAry = res.data
    //     this.timeAry = [this.basicEdit.beginTime, this.basicEdit.endTime]
    //     this.searchData = JSON.parse(JSON.stringify(this.basicEdit))
    //     this.isNext = !this.isEditData
    //   } else {
    //     this.sellerAry = []
    //   }
    // })
  },
  methods: {
    toSeleProd() {
      let data = {}
      data.provinceCode = this.searchData.provinceCode
      data.time = this.searchData.time
      data.status = this.searchData.status
      data.controlSaleName = this.searchData.controlSaleName;
      this.$emit('goNext', { from: 'basic', data: data })
    },
    returnList() {
      this.$emit('goPrev', { from: 'basic' })
    },
    // saleChange(val) {
    //   if (val) {
    //     this.isSale = true
    //     if (this.isTime) {
    //       this.isNext = false
    //     }
    //   }
    // },
    timeChange() {
      if (this.searchData.time) {
        // this.isTime = true
        // if (this.isSale) {
        this.isNext = false
        // }
      }else{
        this.isNext = true
      }
    },
      // 当前时间
    getNowTimeDate() {
      const time = new Date(8 * 3600 * 1000 + +new Date())
        .toJSON()
        .substr(0, 10);
       this.searchData.time = time;
    },
  }
}
</script>

<style lang="scss" scoped>
// @import '../../../assets/css/market';
.con-inner {
  padding-top: 15px;
  padding-left: 23px;
  margin-right: 17px;
  padding-bottom: 10px;
  border-bottom: 1px solid #efefef;
  div {
    display: inline-block;
  }
  .img {
    width: 92px;
    height: 92px;
    img {
      width: 100%;
      height: 100%;
    }
  }
  .text {
    padding-left: 20px;
    vertical-align: top;
    h3 {
      font-size: 14px;
      color: #000000;
      padding: 0;
      margin: 0;
    }
    p {
      padding: 0;
      margin: 0;
      font-size: 12px;
      color: #333333;
      padding-top: 10px;
    }
  }
  .btn {
    float: right;
    padding-top: 26px;
    button {
      width: 100px;
      height: 30px;
      text-align: center;
      line-height: 30px;
      padding: 0;
      background: rgba(65, 131, 213, 1);
      border-color: rgba(65, 131, 213, 1);
      border-radius: 4px;
      font-size: 14px;
    }
    a {
      color: #ffffff;
      text-decoration: none;
    }
    .router-link-active {
      color: #ffffff;
      text-decoration: none;
    }
  }
}
.pag-info {
  width: 500px;
}
::v-deep   .my-label .el-form-item__label {
  border: 1px solid #ffffff;
  border-radius: 4px 0px 0px 4px;
  padding: 0 9px 0 9px;
  height: 30px;
  vertical-align: top;
  border-right: 0;
  font-size: 12px;
  line-height: 28px;
}
::v-deep   .my-label .el-input__inner {
  border-radius: 4px 4px 4px 4px;
}

::v-deep   .el-range-editor--small.el-input__inner {
  height: 30px;
}

::v-deep   .el-input--small .el-input__inner {
    height: 30px;
    line-height: 30px;
    font-size: 12px;
}

::v-deep   .controlSaleName .el-textarea__inner{
  height: 150px;
}
 .status-0 {
    border-color: #afafbf;
    background: #afafbf;
  }
  .status-1 {
    border-color: #4183D5;
    background: #4183D5;
  }
</style>
