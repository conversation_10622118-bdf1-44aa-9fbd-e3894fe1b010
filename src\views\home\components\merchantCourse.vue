<!-- 首页公告列表 -->
<template>
  <div class="courseListContainer">
    <div class="courseListTitle">
      <div>商家课堂</div>
      <div>
        <el-link :underline="false" style="font-size: 16px; font-weight: bold" @click="moreCourse"
          >更多<i class="el-icon-arrow-right"></i
        ></el-link>
      </div>
    </div>

    <div class="courseList" v-if="merchantCourseList.length>0">
      <div class="item" v-for="item in merchantCourseList" :key="item.id">
        <img :src="item.imageUrl" alt="" class="course-img" />
        <div style="padding: 0px">
          <div class="course-desc" @click="toCourseDetail(item)">
            {{ item.title }}
          </div>
          <div style="font-size: 12px; color: #777777; margin-top: 12px">商家课堂</div>
        </div>
      </div>

    </div>
    <el-empty description="暂无数据~" v-else></el-empty>
  </div>
</template>

<script>

import {queryCourseBanner} from '@/api/home'
export default {

  data() {
    return {
      merchantCourseList: [],
    }
  },
  computed: {},
  watch: {},
  methods: {
    moreCourse() {
      this.$router.push('/merchantCourseList')
      setTimeout(() => {
        this.$bus.$emit('tomerchantcourse',false)
      })
    },
    toCourseDetail(item){
      this.$router.push('/merchantCourseList')
      setTimeout(() => {
        this.$bus.$emit('tomerchantcourse',true)
        this.$bus.$emit('tocourseDetail',item)
      }, 200);





    }
  },
  created() {
    queryCourseBanner().then(res=>{

      if(res.code===0){

        this.merchantCourseList = res.result
      }else if(res.code===1){
        this.$message.error(res.msg)
      }
    })
  },
  mounted() {}
}
</script>
<style lang="scss" scoped>
.courseListContainer {
  background-color: #fff;
  padding: 16px;
  margin-bottom: 16px;

  .courseListTitle {
    display: flex;
    justify-content: space-between;
    font-weight: bold;
    font-size: 16px;
    margin-bottom: 16px;
  }

  .courseList {
    & > .item {
      display: flex;
      // align-items: center;
      margin-top: 16px;
    }
  }
  .course-desc {
    overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  font-size: 14px;
  color:#111111;
  // margin-top: 5px;
  cursor: pointer;
  }
  .course-img {

    margin-right: 10px;
    border: 1px solid #eee;
    border-radius: 4px;
    object-fit: contain;
    width:90px;
    height:60px;
  }
}
</style>
