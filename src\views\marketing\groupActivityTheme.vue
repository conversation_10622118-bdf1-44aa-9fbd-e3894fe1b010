<template>
  <div class="contentWrapper">
    <el-row type="flex" align="middle">
      <div class="titleBox">
        <div class="searchMsg">
          <span class="sign" />
          拼团活动主题:{{activityInfo.topicTitle}}
        </div>
        <!-- <el-button size="small" @click="$router.push({ path: '/collageActivity' })">返 回</el-button> -->
      </div>
    </el-row>
    <el-form label-width="150px" class="activity-info">
      <el-form-item label="活动说明：">
        <div style="color: #3c93ee;" v-for="(item, i) in activityInfo.description.split('<br/>')" :key="i">
          {{ item }}
        </div>
      </el-form-item>
      <el-form-item label="拼团报名时间：">
        {{
          timeRangeFormat(
            activityInfo.registrationStime,
            activityInfo.registrationEtime
          )
        }}
        <span class="time-info el-icon-warning-outline">报名时间范围内可提报拼团活动，截止后不可再上报新的活动商品</span>
      </el-form-item>
      <el-form-item v-if='!activityInfo.isAutoAudit' label="拼团编辑截止时间：">
        {{ timeRangeFormat(activityInfo.auditStime, activityInfo.auditEtime) }}
        <span class="time-info el-icon-warning-outline">时间范围内可修改拼团信息，截止后不允许修改</span>
      </el-form-item>
      <el-form-item label="拼团活动时间：">
        <template v-if='Number(activityInfo.segmentType)===1'>
          {{ timeRangeFormat(activityInfo.validStime, activityInfo.validEtime) }}
        </template>
        <template v-else>
          <div>{{ timeRangeFormat(activityInfo.validStime, activityInfo.validEtime,'YMD') }}</div>
          <div v-for='(item,index) in activityInfo.multiSegmentDTOList' :key='index'>
            {{ cycleTimeFormat(item) }}
          </div>
        </template>
      </el-form-item>
      <el-form-item label="参与活动商品范围：" v-if="activityInfo.skuLimitType && Number(activityInfo.skuLimitType.code) === 1">
        <a @click="download(1)">下载可报名商品</a>
      </el-form-item>
      <el-form-item label="参与活动商品范围：" v-else-if="activityInfo.skuLimitType && Number(activityInfo.skuLimitType.code) === 2">
        <a @click="download(1)">下载可报名商品</a>
      </el-form-item>
      <el-form-item label="参与活动商品范围：" v-else>全部商品</el-form-item>
      <!-- <el-form-item label="批量导入：">
        <div style="display: flex; padding-bottom: 10px; align-items: center;">
          <el-upload action="xxx" ref="excludeImport" :http-request="uploadFile" :show-file-list="true" :limit="1" :auto-upload="false" :on-change="handleChange" :on-remove="handleRemove" :file-list="fileList" :before-upload="beforeImportData" accept=".xls, .xlsx, .XLS, .XLSX">
            <el-button size="small" style="margin-right: 10px;">选择文件</el-button>
          </el-upload>
          <a @click="download()">下载拼团批量导入模板</a>
        </div>
      </el-form-item> -->

      <el-form-item label="已提报数量：" v-if="activityInfo.shopReportNum !== null || activityInfo.shopReportLimit !== null"><span style="color: red">{{ activityInfo.shopReportNum }}{{ activityInfo.shopReportLimit !== null ? '/' : ''}}{{ activityInfo.shopReportLimit }}</span></el-form-item>
      <el-form-item label="是否管控个人限购类型：" v-if="activityInfo.personalLimitTypes && activityInfo.personalLimitTypes != 0">
        <span>{{ activityInfo.personalLimitMsg }}</span>
      </el-form-item>
      <el-form-item label="平台限制销售范围及供货对象：" v-if="customerGroupVO">
        <p style="color: red">提报到本拼团主题的活动，最终售卖范围是平台限制区域和原品销售范围取交集。示例：平台限制仅售卖湖北、单体；店铺已选范围为全国，则最终取交集后售卖范围为“湖北单体”</p>
        <div
          v-for="(item, index) in customerGroupVO.contentBundleDescriptions"
          :key="index"
        >
          <p v-for="(one, index1) in item" :key="index1">{{ one }}</p>
        </div>
        <p v-if="customerGroupVO.specifyUserDescription">
          {{ customerGroupVO.specifyUserDescription }}
        </p>
        <p>人群ID：{{ customerGroupVO.id }}</p>
      </el-form-item>

      <el-form-item label="拼团活动申请方式：" prop="applyType">
        <el-radio-group v-model="batchApplyInfo.applyType">
          <el-radio :label="1">
            单个申请
            <el-input
              v-model="batchApplyInfo.tempBarcode"
              size="small"
              style="margin: 0 10px 0 20px;width: 200px;"
              placeholder="请输入普通商品编码"
            />
            <el-button size="small" type="primary" :disabled="batchApplyInfo.applyType === 2" @click="createApply">创建</el-button>
          </el-radio>
          <el-radio :label="2" style="display:flex; margin-top: 10px;">
            <div style="display:flex;align-items:flex-start;">
              <span style="margin:0 20px 0 6px;">批量申请</span>
              <el-upload action="xxx" ref="excludeImport" :http-request="uploadFile" :show-file-list="true" :limit="1" :auto-upload="false" :on-change="handleChange" :on-remove="handleRemove" :file-list="fileList" :before-upload="beforeImportData" accept=".xls, .xlsx, .XLS, .XLSX">
                <el-button size="small" type="primary" :disabled="batchApplyInfo.applyType == 1" style="margin-right: 10px;">选择文件</el-button>
              </el-upload>
            </div>
          </el-radio>
          <div class="tipBox">
            <p>1、请上传编辑好的xlsx文件</p>
            <p>2、人群ID或业务商圈、供货对象和黑白名单相同的拼团活动可批量提交，否则请分批提交</p>
            <p>3、模板文件 <a @click="download()">下载</a></p>
          </div>
        </el-radio-group>
      </el-form-item>
      <span v-if="batchApplyInfo.applyType === 2">
        <!-- 供货方式配置信息 -->
        <SupplyTypeConfig :dis="false" :baseCustomerGroupName="activityInfo.limitCustomerGroupName" :isGroupActivityTheme="isGroupActivityTheme" :baseCustomerGroupId="activityInfo.limitCustomerGroupId" :isedit="activityInfo.isedit"  ref="supplyTypeInfo" />
    </span>

    </el-form>
    <el-row v-if="batchApplyInfo.applyType === 2">
      <el-button size="small" @click="cancelPage">取 消</el-button>
      <el-button type="primary" class="xyy-blue" size="small" :loading="btnLoading" @click="determineDialog()">提交</el-button>
      <el-button
          style="margin-left: 10px"
          type="primary"
          size="small"
          @click="toCollageActivityNew"
        >
          拼团上架（新）
        </el-button>
    </el-row>
  </div>
</template>

<script>
import {
  getTimeStr,
  uploadGoodsCustomer,
  getSkuByBarcode,
  checkImportGroupProduct,
} from '@/api/market/collageActivity';
import SupplyTypeConfig from './components/supplyTypeConfig.vue';

export default {
  components: { SupplyTypeConfig },
  props: {},
  data() {
    return {
      customerGroupVO: null,
      activityInfo: {
        registrationStime: '',
        registrationEtime: '',
        auditStime: '',
        auditEtime: '',
        validStime: '',
        validEtime: '',
        description: '',
        isAutoAudit: 0, // 是否自动审核 1是自动审核 0是不自动审核
        preheatStime: '',
        limitCustomerGroupId: '',
      },
      frameReportId: '',
      groupBuyIngApplyImportProductVos: [],
      importLoading: false,
      btnLoading: false,
      weekObj: {
        1: '周一',
        2: '周二',
        3: '周三',
        4: '周四',
        5: '周五',
        6: '周六',
        7: '周日'
      },
      fileList: [],
      batchApplyInfo: {
        tempBarcode: '',
        applyType: 2, // 拼团活动申请方式
      },
      isGroupActivityTheme:1,
    }
  },
  computed: {},
  watch: {},
  // created() {
  //   const { query } = this.$route
  //   this.frameReportId = query.frameReportId ? query.frameReportId : '';
  //   getTimeStr({ frameReportId: this.frameReportId }).then((res) => {
  //     if (res.code === 1000) {
  //       this.activityInfo = { ...res.data.activityReportBaseResDTO };
  //     } else {
  //       this.$message.error(res.msg)
  //     }
  //   });
  //   this.initData();
  // },
  activated() {
    const { query } = this.$route
    this.frameReportId = query.frameReportId ? query.frameReportId : '';
    this.isGroupActivityTheme = 1;
    getTimeStr({ frameReportId: this.frameReportId }).then((res) => {
      if (res.code === 1000) {
        this.activityInfo = { ...res.data.activityReportBaseResDTO };
        this.customerGroupVO = res.data.customerGroupVO;
        this.isGroupActivityTheme = 2
      } else {
        this.$message.error(res.msg);
      }
    });
    this.initData();
  },
  methods: {
    toCollageActivityNew(){
      window.openTab('/collageActivityNew', {})
    },
    initData() {
      this.batchApplyInfo.applyType = 2;
      this.batchApplyInfo.tempBarcode = '';
      this.fileList = [];
    },
    timeRangeFormat(start, end, type) {
      start = this.formatDate(start, type)
      end = this.formatDate(end, type)
      return start && end ? `${start} 至 ${end}` : ''
    },
    cycleTimeFormat(obj) {
      if (obj) {
        return `${this.weekObj[obj.stime.cycleNum]} ${
          Number(obj.stime.hour) < 10 ? '0' + obj.stime.hour : obj.stime.hour
        }:${
          Number(obj.stime.minute) < 10
            ? '0' + obj.stime.minute
            : obj.stime.minute
        } 至 ${this.weekObj[obj.etime.cycleNum]} ${
          Number(obj.etime.hour) < 10 ? '0' + obj.etime.hour : obj.etime.hour
        }:${
          Number(obj.etime.minute) < 10
            ? '0' + obj.etime.minute
            : obj.etime.minute
        }`
      }
    },
    handleChange(file) {
      if (file.status === 'ready') {
        this.fileList = [file]
      }
    },
    handleRemove() {
      this.fileList = [];
    },
    uploadFile(file) {
      let supplyInfo = this.$refs['supplyTypeInfo'].getAllSupplyInfo();
      const fileFormData = {};
      fileFormData.baseId = this.frameReportId;
      fileFormData.file = file;
      fileFormData.isCopySaleArea = supplyInfo.isCopySaleArea;
      if (supplyInfo.isCopySaleArea === 3) {
        Object.keys(supplyInfo).forEach((key) => {
          fileFormData[key] = supplyInfo[key];
        });
        // delete fileFormData.customerGroupId;
        if (supplyInfo.isCopyControlUser === 2) {
          fileFormData.controlUserTypes = supplyInfo.controlUserTypes.join();
        } else {
          fileFormData.controlUserTypes = '';
        }
        fileFormData.customerGroupId = this.activityInfo.limitCustomerGroupId || supplyInfo.customerGroupId;
        if(!this.activityInfo.limitCustomerGroupId && !supplyInfo.customerGroupId){
          delete fileFormData.customerGroupId;
        }
      } else if (supplyInfo.isCopySaleArea === 2 && (this.activityInfo.limitCustomerGroupId || supplyInfo.customerGroupId)) {
        fileFormData.customerGroupId = this.activityInfo.limitCustomerGroupId || supplyInfo.customerGroupId;
        fileFormData.isCopySaleArea = 2;
      }else if(supplyInfo.isCopySaleArea === 1){
        fileFormData.customerGroupId = this.activityInfo.limitCustomerGroupId || supplyInfo.customerGroupId;
        fileFormData.isCopySaleArea = 1;
        if(!this.activityInfo.limitCustomerGroupId && !supplyInfo.customerGroupId){
          delete fileFormData.customerGroupId;
        }
      }

      this.importLoading = true;

      this.checkRepeat(fileFormData);
    },
    checkRepeat(params) {
      checkImportGroupProduct(params).then((res) => {
        if (res.code === 1000) {
          this.btnLoading = false;
          const { existExcelFileDownloadUrl } = res.data.groupBuyingBatchCreatesResult || {};
          if (existExcelFileDownloadUrl) {
            const h = this.$createElement;
            const baseUrl = process.env.VUE_APP_BASE_API;
            this.$confirm('提示', {
              title: '提示',
              message: h('div', [
                h(
                  'span',
                  null,
                  '以下导入商品在未结束的拼团活动中，存在'
                ),
                h(
                  'span',
                  { style: 'color: red;' },
                  '业务商圈、供货对象、拼团价格、起拼数量一致的拼团活动，上架后将会降低APP商品搜索顺序'
                ),
                h(
                  'span',
                  null,
                  '，请确认是否继续上架？'
                ),
                h('a', {
                  style: 'color: #4183d5;',
                  attrs: {
                    href: existExcelFileDownloadUrl,
                    download: '下载重复性文件',
                  }
                }, '下载重复性文件'),
              ]),
              confirmButtonText: '确定',
              cancelButtonText: '取消'
            })
              .then(() => {
                this.confirmSubmit(params);
              })
              .catch(() => {

              })
          } else {
            this.confirmSubmit(params);
          }
        } else {
          this.btnLoading = false;
          this.$message({
            showClose: true,
            message: res.msg,
            type: 'error',
            duration: 2000
          })
        }
      }).catch(() => {
        this.btnLoading = false;
      })
    },
    confirmSubmit(params) {
      const loading = this.$loading({
        lock: true,
        text: 'Loading',
        spinner: 'el-icon-loading',
        background: 'rgba(255,255,255, 0.8)'
      })
      uploadGoodsCustomer(params)
        .then((res) => {
          loading.close()
          if (res.code === 1000) {
            const excelRowNumLimitFailureMsg =
              ((res.data || {}).groupBuyingBatchCreatesResult || {})
                .excelRowNumLimitFailureMsg || ''
            if (excelRowNumLimitFailureMsg) {
              this.$message.error(excelRowNumLimitFailureMsg)
            } else {
              let con = ''
              if (
                res.data.groupBuyingBatchCreatesResult &&
                res.data.groupBuyingBatchCreatesResult.failureNum > 0
              ) {
                con = `<p>${res.data.groupBuyingBatchCreatesResult.successNum}个商品发布成功，${res.data.groupBuyingBatchCreatesResult.failureNum}个商品导入失败，原因见错误文件<br><a style="color: #ff0021" href="${res.data.groupBuyingBatchCreatesResult.failureExcelFileDownloadUrl}" download="批量导入拼团活动">批量导入拼团活动</a></p>`
              } else {
                con = `<p>${res.data.groupBuyingBatchCreatesResult.successNum}个商品发布成功，${res.data.groupBuyingBatchCreatesResult.failureNum}个商品导入失败</p>`
              }
              this.groupBuyIngApplyImportProductVos = res.data
                .groupBuyingBatchCreatesResult
                ? res.data.groupBuyingBatchCreatesResult
                    .groupBuyIngApplyImportProductVos
                : []
              this.$confirm(con, '提示', {
                confirmButtonText: '确定',
                dangerouslyUseHTMLString: true,
                cancelButtonText: '取消'
              })
                .then(() => {
                  this.handleRemove()
                  const path = this.$route.fullPath;
                  window.closeTab(path);
                  window.closeTab('/groupActivityTheme');
                  this.$router.push({
                    path: '/collageActivity',
                    query: { refresh: true }
                  })
                })
                .catch((err) => {
                  this.handleRemove()
                })
            }
          } else {
            this.handleRemove()
            this.$message({
              showClose: true,
              message: res.msg,
              type: 'error',
              duration: 2000
            })
          }
        })
        .catch((error) => {
          loading.close();
          this.$message({
            message: '请求失败',
            type: 'error',
          });
        })
        .finally(() => {
          this.importLoading = false;
          this.btnLoading = false;
          if (this.uploadRef) {
            this.$refs[this.uploadRef].clearFiles();
          }
        });
    },
    beforeImportData(uploadInfo) {
      const fileName = uploadInfo.name;
      const fileType = fileName.substring(fileName.lastIndexOf('.') + 1);
      if (fileType !== 'xlsx' && fileType !== 'xls') {
        this.$message.warning('选择的文件类型不对');
        return false;
      }
      return true;
    },
    download(from) {
      let url = '';
      if (from === 1) {
        url = `${process.env.VUE_APP_BASE_API}/report/groupbuying/apply/canReportSku?frameReportId=${this.frameReportId}`
      } else {
        url = `${process.env.VUE_APP_BASE_API}/report/groupbuying/apply/downloadTemplateForImport`
      }
      window.open(url);
    },
    determineDialog() {
      if (this.btnLoading) {
        this.$message.warning('有在执行中的任务，请稍后！');
        return false;
      }
      let supplyInfo = this.$refs['supplyTypeInfo'].getAllSupplyInfo();
      console.log(supplyInfo,'lwq5');
      if (this.fileList.length > 0) {
        this.btnLoading = true;
        this.fileList.forEach((obj) => {
          this.uploadFile(obj.raw);
        });
      } else {
        this.$message.warning('请选择上传文件!');
      }
    },
    createApply() {
      if (this.batchApplyInfo.tempBarcode === '') {
        this.$message.error('请填写普通商品编码');
        return;
      }
      getSkuByBarcode({
        baseId: this.frameReportId,
        barcode: this.batchApplyInfo.tempBarcode,
      }).then((res) => {
        if (res.code === 1000) {
          window.sessionStorage.removeItem("singleCreateInfo") // 商业反馈重新选择新的商品创建拼团活动，进入编辑页面还是上一次所选的商品信息。先删再存看看
          sessionStorage.setItem('singleCreateInfo', JSON.stringify({
            ...res.data.csu,
            baseId: this.frameReportId,
            auditTime: [this.activityInfo.auditStime, this.activityInfo.auditEtime],
            activityTime: [this.activityInfo.validStime, this.activityInfo.validEtime],
            preheatTime: this.activityInfo.preheatStime,
            topicTitle: this.activityInfo.topicTitle,
            registrationTime: [this.activityInfo.registrationStime, this.activityInfo.registrationEtime],
            customerGroupVO: this.customerGroupVO,
            baseCustomerGroupId: this.activityInfo.limitCustomerGroupId,
            isedit:this.activityInfo.isedit,
          }));
          console.log(this.activityInfo.isedit,'jtt');
          
          const path = '/editCollageActivity';
          const obj = {
            fromType: 'singleCreate'
          }
          sessionStorage.setItem('collageActType', 'singleCreate');
          sessionStorage.removeItem('createCollageActivity')
          sessionStorage.setItem('createCollageActivity', 'true')
          window.openTab(path, obj);
          // this.$router.push('/editCollageActivity?fromType=singleCreate');
        } else {
          this.btnLoading = false;
          this.$message.error(res.msg);
        }
      }).catch((err) => {
        this.btnLoading = false;
        this.$message.error(err.msg || '系统错误');
      });
      // this.$router.push({ path: '/editCollageActivity', query: { frameReportId: '' } });
    },
    cancelPage() {
      const path = this.$route.fullPath;
      window.closeTab(path);
      window.closeTab('/groupActivityTheme');
    },

  }
}
</script>

<style lang="scss" scoped>
.contentWrapper {
  padding: 16px;
  background: #fff;
  margin-bottom: 20px;
  .titleBox {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  .searchMsg {
    font-weight: 700;
    width: 100%;
    font-size: 16px;
  }
  .sign {
    display: inline-table;
    width: 3px;
    height: 13px;
    background: linear-gradient(360deg, #1d69c4 1%, #8bbdfc 99%);
    border-radius: 2px;
    margin-right: 8px;
  }
  .el-form {
    &.activity-info {
      margin-top: 20px;
      .el-form-item {
        margin-bottom: 10px;
        ::v-deep  .el-form-item__label {
          line-height: 30px;
        }
        ::v-deep  .el-form-item__content {
          line-height: 30px;
          color: #606266;
          a {
            color: #409eff;
            cursor: pointer;
          }
        }
      }
      .time-info {
        font-size: 14px;
        font-family: PingFangSC, PingFangSC-Regular;
        font-weight: 400;
        text-align: left;
        color: #ff3024;
      }
      .tipBox {
        color: #909399;
        font-size: 12px;
        p {
          line-height: 18px;
        }
      }
      // .redText {
      //   color: #ff3024;
      // }
    }
  }
  // .customerType {
  //   border: 1px solid #eeeeee;
  //   border-radius: 4px;
  //   max-height: 260px;
  //   overflow-y: auto;
  //   ::v-deep  .el-checkbox {
  //     width: 14%;
  //     margin-left: 10px;
  //   }
  //   ::v-deep  .checkedall {
  //     width: 100%;
  //     padding: 10px;
  //     margin-left: 0;
  //     margin-bottom: 10px;
  //   }
  //   ::v-deep  .el-checkbox__input.is-checked + .el-checkbox__label {
  //     color: #333333;
  //   }
  // }
}
</style>
