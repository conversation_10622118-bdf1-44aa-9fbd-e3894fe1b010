<template>
  <div>
    <el-form label-width="150px" class="activity-info" :rules="groupActivityRules" :model="supplyInfo">
      <el-form-item label="供货信息配置方式" prop="isCopySaleArea">
        <el-radio-group v-model="supplyInfo.isCopySaleArea" :disabled="fromType === 'edit' || dis" @change="isCopySaleAreaChange">
          <el-radio :label="1" style="display: block;margin-top: 10px">{{isFour?'复制原商品销售范围':'复制原活动销售范围'}}</el-radio>
          <el-radio :label="2" style="display: block;margin-top: 10px">人群</el-radio>
          <el-radio :label="3" style="display: block;margin-top: 10px">配置业务商圈、供货对象和黑白名单</el-radio>
        </el-radio-group>
        <el-form-item v-if="supplyInfo.isCopySaleArea === 2" label-width="none" label="指定人群：" :key="supplyInfo.isCopySaleArea">
            <el-button size="small" type="primary" plain :disabled="isedit === false || iseditNew == 1"        
              @click="dialogVisible = true">选择人群</el-button>
            <span style="padding-left: 10px" v-if="baseCustomerGroupId || peopleInfo.id">
              已选：人群ID：{{ peopleInfo.id || baseCustomerGroupId }} ；人群名称：{{ peopleInfo.name || baseCustomerGroupName }}
            </span>
            <el-button v-if="peopleInfo.id || baseCustomerGroupId" type="text" @click="seeCrowd(peopleInfo.id || baseCustomerGroupId)">查看详情</el-button>
        </el-form-item>
        <el-form-item v-else-if="supplyInfo.isCopySaleArea === 1" label-width="none" label="指定人群：" :key="supplyInfo.isCopySaleArea">
            <el-button size="small" type="primary" plain  :disabled="isedit === false || iseditNew == 1"     
              @click="dialogVisible = true">选择人群</el-button>
            <span style="padding-left: 10px" v-if="baseCustomerGroupId || peopleInfo.id">
               <span v-if="isedit === false">平台指定人群：</span>已选：人群ID：{{ peopleInfo.id || baseCustomerGroupId }} ；人群名称：{{ peopleInfo.name || baseCustomerGroupName }}
            </span>
            <el-button v-if="peopleInfo.id || baseCustomerGroupId" type="text" @click="seeCrowd(peopleInfo.id || baseCustomerGroupId)">查看详情</el-button>
            <el-button
              size="small"
              type="primary"
              plain
              v-if="isedit === true && peopleInfo.id"
              @click="clear"
            >清空</el-button>
        </el-form-item>
        <div style="border: 1px solid rgb(238,238,238);padding: 10px;margin-top: 5px;border-radius: 5px;" v-if="supplyInfo.isCopySaleArea === 3">
          <el-form-item
            v-if="supplyInfo.isCopySaleArea === 3 && supplyInfo.controlRosterType != 2"
            label="业务商圈" label-width="80px">
            <el-button size="small" type="primary" 
              @click="selectBusinessCircle">选择商圈</el-button>
            <br>
            <span style="padding-left: 10px" v-if="businessCircleInfo.busAreaName">
              当前已选：{{ businessCircleInfo.busAreaName }}
              <el-button v-if="businessCircleInfo.busAreaName" type="text" class="btn-info"
                @click="checkDetails">查看详情</el-button>
              <el-button type="text" v-if="isShow" class="btn-info" @click="addBusinessDistrict">新建商圈</el-button>
            </span>
          </el-form-item>
      
          <el-form-item label="供货对象" prop="controlUserTypes" label-width="none" style="margin-top: 5px;"
            v-if="supplyInfo.isCopySaleArea === 3 && supplyInfo.controlRosterType != 2">
            <div class="customerType">
              <el-checkbox :indeterminate="isIndeterminate" v-model="checkAll" 
                @change="handleCheckAllChange" class="checkedall">全选</el-checkbox>
              <el-checkbox-group v-model="supplyInfo.controlUserTypes"
                @change="handleCheckedTypesChange">
                <el-checkbox v-for="(item, index) in customerTypes" v-if="Array.isArray(customerTypes)" :label="item.id" style="margin-Bottom:10px;"
                  :key="index">{{ item.name }}</el-checkbox>
              </el-checkbox-group>
            </div>
         
          </el-form-item>
       
          <el-form-item v-if="supplyInfo.isCopySaleArea === 3" label=" 黑白名单"
            prop="controlRosterType" label-width="75px" style="margin-top: 6px;" >
            <el-radio-group v-model="supplyInfo.controlRosterType" >
              <el-radio :label="1" @click.native.prevent="clickBlackNameList">黑名单控销组</el-radio>
              <el-radio :label="2" v-if="!disabledWhite"  @click.native.prevent="clickWhiteNameList">白名单控销组</el-radio>
              <el-radio :label="2" v-else :disabled="disabledWhite" >白名单控销组</el-radio>
            </el-radio-group>
            <div class="div-info">
              <el-button type="primary" v-if="supplyInfo.controlRosterType === 1 || supplyInfo.controlRosterType === 2"
                size="small" @click="toSelectControlGroups">选择控销组</el-button>
              <span style="padding-left: 10px" v-if="supplyInfo.controlGroupName && supplyInfo.controlRosterType">
                <span>已选“{{ supplyInfo.controlGroupName }}”</span>
                <el-button type="text" class="btn-info" @click="seeMerchantDetail">查看药店明细</el-button>
              </span>
            </div>
      
          </el-form-item>
         
        </div>
        <el-form-item v-if="supplyInfo.isCopySaleArea === 3" label-width="none" label="指定人群：" :key="supplyInfo.isCopySaleArea">
            <el-button size="small" type="primary" plain  :disabled="isedit === false || iseditNew == 1"     
              @click="dialogVisible = true">选择人群</el-button>
            <span style="padding-left: 10px" v-if="baseCustomerGroupId || peopleInfo.id">
               <span v-if="isedit === false">平台指定人群：</span>已选：人群ID：{{ peopleInfo.id || baseCustomerGroupId }} ；人群名称：{{ peopleInfo.name || baseCustomerGroupName }}
            </span>
            <el-button v-if="peopleInfo.id || baseCustomerGroupId" type="text" @click="seeCrowd(peopleInfo.id || baseCustomerGroupId)">查看详情</el-button>
            <el-button
              size="small"
              type="primary"
              plain
              v-if="isedit === true && peopleInfo.id"
              @click="clear"
            >清空</el-button>
        </el-form-item>
        <el-form-item>
            <div class="tipBox">
              <!-- <p>1、选“复制原品销售范围”，活动商品创建成功后，活动商品和原商品商圈、供货对象、黑白名单相同，活动投放范围和原品一致。</p>
              <p>2、选“人群”，活动投放范围按照人群进行生效。</p>
              <p>3、选“配置业务商圈、供货对象和黑白名单”，按照页面所选信息生效活动。</p> -->
              <p>1、选“复制原品销售范围”，活动商品创建成功后，活动商品和原商品商圈、供货对象、黑白名单相同，最终售卖范围是人群和原品销售范围取交集。示例：人群限制仅售卖湖北、单体；店铺已选范围为全国，则最终取交集后售卖范围为“湖北单体“</p>
              <p>2、选“人群”，活动投放范围按照人群进行生效。</p>
              <p>3、选“配置业务商圈、供货对象和黑白名单”，最终售卖范围是人群限制区域和原品销售范围取交集。示例：人群限制仅售卖湖北、单体；店铺已选范围为全国，则最终取交集后售卖范围为“湖北单体“</p>
            </div>
          </el-form-item>

      </el-form-item>

     
    </el-form>
    <!-- 选择人群 -->
    <crowd-selector-dialog ref="changePeople" @onSelect="sendPeopleData" v-if="dialogVisible" v-model="dialogVisible"
      :selected="peopleInfo.id"></crowd-selector-dialog>
    <!-- 查看客户信息 -->
    <CustomerInfoLog v-if="showCustomer" :market-customer-group-id="peopleInfo.id || baseCustomerGroupId" @cancelModal="cancelDialog" />
    <!-- 选择商圈 -->
    <select-business-circle v-if="isSelectBusinessCircle" :selected.sync="businessCircleInfo.busAreaId"
      fromComp="groupActivityTheme" @onDialogChange="onDialogChange"
      @selectChange="selectChange"></select-business-circle>
    <!-- 查看商圈 -->
    <view-business-circle :row="{ id: businessCircleInfo.busAreaId }" v-if="viewBusinessDialog"
      @onDialogChange="onDialogChange"></view-business-circle>
    <!-- 查看药店明细 -->
    <ListOfControlGroups v-if="showMerchantInfo" :control-group-id="supplyInfo.controlGroupId"
      @cancelDialog="cancelDialog" />
    <!-- 选择控销组 -->
    <SelectControlGroups v-if="showSelectControlGroups" :show-select-control-groups.sync="showSelectControlGroups"
      :selected-id="supplyInfo.controlGroupId" @selectGroupChange="selectGroupChange" />
  </div>
</template>
<script>
import CrowdSelectorDialog from '@/components/xyy/customerOperatoin/crowd-selector-dialog.vue'
import selectBusinessCircle from "@/views/product/components/selectBusinessCircle";
import viewBusinessCircle from '@/views/business-circle/components/viewBusinessCircle';
import ListOfControlGroups from '@/views/product/components/listOfControlGroups';
import SelectControlGroups from '@/views/product/components/selectControlGroups';
import { findUserTypes } from '@/api/product';
import CustomerInfoLog from '@/components/customer/customerInfoLog.vue';
import { isArray } from 'lodash';

export default {
  components: { CrowdSelectorDialog, selectBusinessCircle, viewBusinessCircle, SelectControlGroups, ListOfControlGroups, CustomerInfoLog },
  props: {
    isShow:{
      type:Boolean,
      default:true
    },
    disabledWhite:Boolean,
    dis: Boolean,
    frameActReportSaleScopeDTO: Object,
    disabled: Boolean,
    subOtherDisabled: Boolean,
    baseCustomerGroupId: Number,
    baseCustomerGroupName: String,
    isShopUpdate: {
      type: Boolean,
      default: false
    },
    isShopLoad: {
      type: Boolean,
      default: false
    },
    isedit:{
      type: Boolean,
      default: true,
    },
    isFour:{
      type: Boolean,
      default: false,
    },
    iseditNew:Number,
  },
  data() {
    return {
      isSelectBusinessCircle: false,
      viewBusinessDialog: false,
      businessCircleInfo: { // 商圈信息
        busAreaId: '',
        busAreaName: '',
      },
      dialogVisible: false,
      peopleInfo: { // 人群信息
        name: '',
        id: '',
      },
      showCustomer: false,
      showMerchantInfo: false, // 查看药店明细
      showSelectControlGroups: false, // 选择控销组
      isIndeterminate: false,
      checkAll: false,
      customerTypes: [],
      supplyInfo: {
        isCopyBusArea: 2,
        controlRosterType: 0,
        isCopySaleArea: 1,
        controlGroupId: '',
        controlGroupName: '',
        controlUserTypes: [],
        isCopyControlRoster: 2,
        isCopyControlUser: 2,
        controlUserTypeList:[],
      },
      isCopySaleArea:1,
      peopleInfoList:[
        {
          name: '',
          id: '',
        },
        {
          name: '',
          id: '',
        },
        {
          name: '',
          id: '',
        },
      ],
      fromType: '',
      groupActivityRules: {
        controlUserTypes: [
          { type: 'array', required: true, message: '请至少选择一个供货对象', trigger: 'change' }
        ]
      }
    };
  },
  watch: {
    iseditNew:{
      handler(val){
        console.log('zzz',this.iseditNew);
      }
    },
    isedit:{
      handler(val){
        console.log(val,'tjy');
        
      }
    },
    frameActReportSaleScopeDTO: { // 深度监听，可监听到对象、数组的变化
      handler(val) {
        if (!this.isShopUpdate) {
          if (Object.keys(val).length) {
            // this.frameActReportSaleScopeDTO = val;
            this.initData();
            this.rest();
          } else {
            this.supplyInfo = {
              isCopyBusArea: 2,
              controlRosterType: 0,
              isCopySaleArea: 1,
              controlGroupId: '',
              controlGroupName: '',
              controlUserTypes: [],
              isCopyControlRoster: 2,
              isCopyControlUser: 2,
              controlUserTypeList:[]
            };
            this.isCopySaleArea = 1;
            this.businessCircleInfo = { // 商圈信息
              busAreaId: '',
              busAreaName: '',
            };
            this.peopleInfo = { // 人群信息
              name: '',
              id: '',
            };
          }
        }
      },
      deep: true, // true 深度监听
    },
  },
  activated() {
    this.rest();
    // this.findUserTypes();
    this.fromType = this.$route.query.fromType;
  },
  created() {
    this.findUserTypes();
    this.fromType = this.$route.query.fromType;
  },
  mounted() {
    this.rest();
    if (this.isShopLoad) {
      this.initData();
    }
  },
  methods: {
    /**清空选项 */
    rest(){
      this.peopleInfoList = [
        {
          name: '',
          id: '',
        },
        {
          name: '',
          id: '',
        },
        {
          name: '',
          id: '',
        },
      ]
    },
    /**清空人群数据 */
    clear(){
      this.peopleInfo = { // 人群信息
        name: '',
        id: '',
      }
      this.peopleInfoList[this.isCopySaleArea - 1] = {
        name: '',
        id: '',
      }
    },
    isCopySaleAreaChange(){
      if(this.isedit !== false){
        this.peopleInfoList[this.isCopySaleArea -1] = {...this.peopleInfo};
        this.peopleInfo = {...this.peopleInfoList[ this.supplyInfo.isCopySaleArea - 1]};
      }
      console.log(this.peopleInfoList,this.peopleInfo,'lwq4',this.isCopySaleArea,this.supplyInfo.isCopySaleArea,this.peopleInfo);
      this.isCopySaleArea = this.supplyInfo.isCopySaleArea;
      console.log(this.isedit,'zzz');
      
      // if(!this.baseCustomerGroupId){
        
      // }
    },
    initData() {
      Object.keys(this.supplyInfo).forEach((key) => {
        this.supplyInfo[key] = this.frameActReportSaleScopeDTO[key];
      });
      if (!isArray(this.frameActReportSaleScopeDTO.controlUserTypes)) {
        this.supplyInfo.controlUserTypes = []
      }
      console.log(this.frameActReportSaleScopeDTO.controlUserTypes)
      if (this.frameActReportSaleScopeDTO.controlUserTypes) {
        this.supplyInfo.controlUserTypes = this.frameActReportSaleScopeDTO.controlUserTypes.split(',').map(i => Number(i));
      }
      console.log(this.supplyInfo,'lwq111')
      this.businessCircleInfo = { // 商圈信息
        busAreaId: this.frameActReportSaleScopeDTO.busAreaId || '',
        busAreaName: this.frameActReportSaleScopeDTO.busAreaName || '',
      };
      this.peopleInfo = {
        id: this.frameActReportSaleScopeDTO.customerGroupId,
        name: this.frameActReportSaleScopeDTO.customerGroupName,
      };
      this.isCopySaleArea = this.frameActReportSaleScopeDTO.isCopySaleArea;
      if(this.frameActReportSaleScopeDTO.customerGroupCanModify === false){
        this.isedit = false;
        this.peopleInfoList.forEach(item=>{
          item.id = this.frameActReportSaleScopeDTO.customerGroupId;
          item.name = this.frameActReportSaleScopeDTO.customerGroupName;
        })
      }else{
        this.peopleInfoList[this.isCopySaleArea - 1] = {
          id : this.frameActReportSaleScopeDTO.customerGroupId,
          name : this.frameActReportSaleScopeDTO.customerGroupName,
        }
        this.isedit = true;
      }
      console.log('lwq3',this.frameActReportSaleScopeDTO);
    },
    // 添加人群*
    sendPeopleData(value) {
      this.peopleInfo = {
        name: value.tagName,
        id: value.id,
      };
    },
    seeCrowd() {
      console.log(this.baseCustomerGroupId , this.peopleInfo, 'zzm');
      
      this.showCustomer = true;
    },
    // 选择商圈
    selectBusinessCircle() {
      this.isSelectBusinessCircle = true;
    },
    // 查看商圈详情
    checkDetails() {
      this.viewBusinessDialog = true;
    },
    // 新建商圈
    addBusinessDistrict() {
      this.$emit("supplyDialogClose");
      if(this.$store.state.permission.menuGray == 1) {
        window.openTab('/shopBusinessManage?to=businessCircle');
      }else {
        window.openTab('/businessCircle');
      }
    },
    onDialogChange() {
      this.isSelectBusinessCircle = false;
      this.viewBusinessDialog = false;
    },
    selectChange(row) {
      if (row) {
        this.businessCircleInfo.busAreaId = row.id;
        this.businessCircleInfo.busAreaName = row.busAreaName;
      }
    },
    // 获取用户类型
    findUserTypes() {
      findUserTypes().then((res) => {
        if (res.code == 0) {
          this.customerTypes = res.data;
        } else {
          this.$message({
            message: res.message,
            type: 'warning',
          });
        }
      });
    },
    // 供货对象
    handleCheckAllChange(val) {
      const checkAllId = this.customerTypes.map((item => item.id));
      this.supplyInfo.controlUserTypes = val ? checkAllId : [];
      this.isIndeterminate = false;
    },
    handleCheckedTypesChange(value) {
      
      this.supplyInfo.controlUserTypeList=[]
      this.supplyInfo.controlUserTypes.forEach(item=>{
        console.log(item)
       let data=this.customerTypes.find(res=>{
          return res.id==item
         })
    
         this.supplyInfo.controlUserTypeList.push(data.name)
      })
      const checkedCount = value.length;
      this.checkAll = checkedCount === this.customerTypes.length;
      this.isIndeterminate = checkedCount > 0 && checkedCount < this.customerTypes.length;
    },
    // 点击黑名单
    clickBlackNameList(e) {
      if (this.disabled) {
        return;
      }
      this.supplyInfo.controlGroupId = '';
      this.supplyInfo.controlGroupName = '';
      if (this.supplyInfo.controlRosterType === 1) {
        this.supplyInfo.controlRosterType = 0;
      } else {
        this.supplyInfo.controlRosterType = 1;
        
      }
    },
    clickWhiteNameList(e) {
     
      if (this.supplyInfo.controlRosterType == 2) {
        this.supplyInfo.controlRosterType = 0;
      } else {
        this.$confirm('选择白名单控销组后，仅白名单控销组内的客户可以购买此商品，确定使用白名单控销组吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
        })
          .then(() => {
            this.supplyInfo.controlGroupId = '';
            this.supplyInfo.controlGroupName = '';
            this.supplyInfo.controlRosterType = 2;
            this.supplyInfo.isCopyBusArea = 2;
            this.businessCircleInfo = { // 商圈信息
              busAreaId: '',
              busAreaName: '',
            };
            this.supplyInfo.isCopyControlUser = 2;
            this.supplyInfo.controlUserTypes = [];
          })
          .catch(() => { });
      }
    },
    seeMerchantDetail() {
      this.showMerchantInfo = true;
    },
    selectGroupChange(row) {
      this.supplyInfo.controlGroupId = row.id; // 用户组id
      this.supplyInfo.controlGroupName = row.name;
    },
    cancelDialog() {
      this.showMerchantInfo = false;
      this.showSelectControlGroups = false;
      this.showCustomer = false;
    },
    toSelectControlGroups() {
      this.showSelectControlGroups = true;
    },
    getAllSupplyInfo() {
      console.log(this.supplyInfo)
      let params = {
        ...this.supplyInfo,
        customerGroupId: this.peopleInfo.id || this.baseCustomerGroupId || '',
        customerGroupName:this.peopleInfo.name || this.baseCustomerGroupName || "",
        busAreaName:this.businessCircleInfo.busAreaName,
        busAreaId: this.businessCircleInfo.busAreaId,
        
      }
      // alert(this.supplyInfo.isCopySaleArea)
      console.log(this.businessCircleInfo, '???o')
      if(this.supplyInfo.isCopySaleArea ==1 ) {
        params.busAreaId="";
           params.controlUserTypes = [];
           params.controlGroupId=""
           params.controlGroupName=""
           params.isCopyBusArea=""
           params.isCopyControlUser=""
           params.isCopyControlRoster = ""
           params.controlRosterType=""
          //  params.customerGroupId="";
           params.scopeType="-1"
      }
      if (this.supplyInfo.isCopySaleArea ==2) {
           params.busAreaId="";
           params.controlUserTypes = [];
           params.controlGroupId=""
           params.controlGroupName=""
           params.isCopyBusArea=""
           params.isCopyControlUser=""
           params.isCopyControlRoster = ""
           params.controlRosterType=""
           params.scopeType="1"
      }
      if (this.supplyInfo.isCopySaleArea == 3) {
        // params.customerGroupId="";
        params.scopeType="2"
      }
      // params.controlUserTypeList=params.controlUserTypes
      params.controlUserTypes=params.controlUserTypes.join(",")
      
      let data=this.frameActReportSaleScopeDTO?JSON.parse(JSON.stringify(this.frameActReportSaleScopeDTO)):{}
      return Object.assign(data, params);
    },
  },
};

</script>
<style lang="scss" scoped>
.tipBox {
  color: #909399;
  font-size: 12px;

  p {
    line-height: 18px;
  }
}

.redText {
  color: #ff3024;
}

.customerType {
  border: 1px solid #eeeeee;
  border-radius: 4px;
  max-height: 260px;
  overflow-y: auto;
  padding: 0 30px;
  margin-right: 20px;

  ::v-deep  .el-checkbox {
    width: 14%;
    margin-left: 10px;
  }

  ::v-deep  .checkedall {
    width: 100%;
    padding: 10px;
    margin-left: 0;
    margin-bottom: 10px;
  }

  ::v-deep  .el-checkbox__input.is-checked+.el-checkbox__label {
    color: #333333;
  }
}

.optionsMsgArea {
  margin-top: -10px;
  margin-left: 150px;
  border: 1px solid #E0E0E0;
  border-radius: 4px;

  .el-form-item {
    margin: 15px 0 0 15px;
  }

  .el-form-item__label {
    padding: 0;
  }
}
</style>
