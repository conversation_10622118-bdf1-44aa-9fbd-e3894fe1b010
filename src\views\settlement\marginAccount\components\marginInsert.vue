<template>
  <div>
    <div class="text-father">
      <!-- 当前余额： 当前余额比例： 保证金金额：  -->
      <div class="left-text">
        <span>
          <span>当前余额</span><br />
          <span style="font-size: 22px">{{ pageData.actualReceivedTotalAmount || 0 }}</span>元
        </span>
        <el-divider direction="vertical"></el-divider>
        <span>
          <span>当前余额比例</span><br />
          <span style="font-size: 22px">{{ pageData.balanceRatio || 0 }}%</span>
        </span>
        <el-divider direction="vertical"></el-divider>
        <span>
          <span>保证金金额</span><br />
          <span style="font-size: 22px">{{ pageData.bondAmount || 0 }}</span>元
        </span>
      </div>
      <!-- 充值记录和充值保证金按钮 -->
      <div class="left-right">
        <div>
          <el-button size="small" type="primary" @click="openDialog('rechargeDialog')">
            充值保证金
          </el-button>
          <el-button size="small" @click="openDialog('recordDialog')">充值记录</el-button>
        </div>
        <p style="color: red; font-size: 14px">
          1.根据您与平台签订的协议，当出现赔付需要，系统会优先扣除您的保证金赔付给用户。
          2.保证金不足{{ limitRatio }}%时，会限制您提现。
        </p>
      </div>
        
    </div>
    <!-- 充值保证金的弹框 -->
    <rechargeDialog ref="rechargeDialog"></rechargeDialog>
    <!-- 充值记录的弹框 -->
    <recordDialog ref="recordDialog"></recordDialog>
  </div>
</template>

<script>
import { getAccountInfo, getWithdrawalLimitRatio } from '@/api/settlement/marginAccount/index'

import rechargeDialog from './rechargeDialog.vue'
import recordDialog from './recordDialog.vue'
export default {
  components: {
    rechargeDialog,
    recordDialog
  },
  data() {
    return {
      pageData: {
        actualReceivedTotalAmount: null,
        balanceRatio: null,
        bondAmount: null
      },
      limitRatio: null
    }
  },
  mounted() {
    // this.loadData()
  },
  methods: {
    openDialog(dialogName) {
      this.$refs[dialogName].openDialog('buy')
    },
    loadData() {
      getAccountInfo({ fundPropertyStatus: 1 }).then((res) => {
        if( res.code == 0){
          this.pageData = res.data
        }
      })
      getWithdrawalLimitRatio().then((res) => {
        if(res.code == 0){
          this.limitRatio = res.result
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.text-father{
  width: 100%; 
  // padding: 10px; 
  display: flex;
  border-bottom: 10px solid #f3f3f3;
}
.left-text{
  width: 40%;
  display: flex;
  justify-content: space-around;
  align-items: center;
  // border: 1px solid red;
  :first-child{
    color: #777777;
  }
  :last-child{
    color: #4184D5;
  }
}
.left-right{
  width: 60%;
  // border: 1px solid red;
  padding: 20px 20px 0 20px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  // align-items: center;
}
</style>
