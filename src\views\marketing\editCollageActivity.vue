<template xmlns="http://www.w3.org/1999/html">
  <div>
    <div class="serch">
      <div class="Fsearch">
        <el-row
          type="flex"
          align="middle"
          justify="space-between"
          class="my-row"
        >
          <!-- <el-row
            type="flex"
            align="middle"
          >
            <span class="sign" />
            <div class="searchMsg">
              编辑拼团申请
            </div>
          </el-row> -->
          <!-- <el-button
            type="primary"
            size="small"
            @click="$router.go(-1)"
          >
            返回
          </el-button> -->
        </el-row>
      </div>
      <div class="fromBlock">
        <el-form
          ref="ruleForm"
          :model="ruleForm"
          :rules="rules"
          size="small"
          label-width="170px"
        >
          <el-form-item label="拼团主题" v-if="collageActType === 'singleCreate'">
            <el-input disabled v-model="ruleForm.topicTitle" style="width: 200px;" />
          </el-form-item>
          <el-form-item label="拼团报名时间" prop="registrationTime" v-if="collageActType === 'singleCreate'">
            <el-date-picker
              v-model.trim="ruleForm.registrationTime"
              type="datetimerange"
              format="yyyy-MM-dd HH:mm:ss"
              range-separator="至"
              value-format="timestamp"
              :default-time="['00:00:00', '23:59:59']"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              disabled
            />
            <span style="color: #ff2121"> 时间范围内可修改拼团信息，截止后不允许修改</span>
          </el-form-item>
          <el-form-item label="拼团编辑截止时间" prop="auditTime" v-else>
            <el-date-picker
              v-model.trim="ruleForm.auditTime"
              type="datetimerange"
              format="yyyy-MM-dd HH:mm:ss"
              range-separator="至"
              value-format="timestamp"
              :default-time="['00:00:00', '23:59:59']"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              disabled
            />
            <span style="color: #ff2121"> 时间范围内可修改拼团信息，截止后不允许修改</span>
          </el-form-item>
          <el-form-item label="拼团活动时间" prop="activityTime">
            <el-date-picker
              v-model.trim="ruleForm.activityTime"
              type="datetimerange"
              format="yyyy-MM-dd HH:mm:ss"
              range-separator="至"
              value-format="timestamp"
              :default-time="['00:00:00', '23:59:59']"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              disabled
            />
          </el-form-item>
          <el-form-item label="拼团预热时间" prop="preheatTime">
            <el-date-picker
              v-model.trim="ruleForm.preheatTime"
              type="datetime"
              format="yyyy-MM-dd HH:mm:ss"
              placeholder="选择日期"
              disabled
            />
          </el-form-item>
          <el-form-item label="商品信息">
            <div>商品编码：{{ editCollageItem.barcode }}</div>
            <div>商品ERP编码：{{ editCollageItem.productCode }}</div>
            <div>商品名称：{{ editCollageItem.productName }}</div>
            <div>规格：{{ editCollageItem.spec }}</div>
            <div>生产厂家：{{ editCollageItem.manufacturer }}</div>
            <!-- <div>药帮忙价格：{{ editCollageItem.fob }}</div> -->
            <!-- <div>单体采购价：{{ editCollageItem.fob }}</div> -->
            <div>连锁采购价：{{ editCollageItem.guidePrice }}</div>
          </el-form-item>
          <!-- <el-form-item label="是否复制原品销售范围" prop="isCopyCsuModel">
            <div>
              <div class="mt16 ml150">
                <el-radio
                  v-model="ruleForm.radioCheck"
                  :label="true"
                  class="radio-class"
                >
                  <span />
                </el-radio>
                <span class="radio-span">是</span>
              </div>
              <div class="mt16">
                <el-radio
                  v-model="ruleForm.radioCheck"
                  :label="false"
                  class="radio-class"
                >
                  <span />
                </el-radio>
                <span class="radio-span">否</span>
                <el-button
                  v-show="!ruleForm.radioCheck"
                  type="primary"
                  plain
                  size="small"
                  @click="addPeopel"
                >
                  选择人群
                </el-button>
                <span
                  v-if="!ruleForm.radioCheck && ruleForm.customerGroupName"
                  style="margin-left: 10px"
                >
                  已选人群：{{ ruleForm.customerGroupName }}
                </span>
              </div>
            </div>
          </el-form-item> -->
          <el-form-item label="拼团价格" prop="groupPrice">
            <el-input :disabled="disabled && otherRuleDisabled" v-model="ruleForm.groupPrice" placeholder="请输入" style="width: 200px" />
            <span style="color: #ff2121"> 每家参团药店采购价格</span>
          </el-form-item>
          <el-form-item label="起拼数量" prop="groupNum">
            <el-input :disabled="disabled && otherRuleDisabled" v-model="ruleForm.groupNum" placeholder="请输入" style="width: 200px" />
            <span style="color: #ff2121"> 每家参团药店单次最低采购数量</span>
          </el-form-item>

          <el-form-item
            v-if="judgeIsApplyStepPrice"
            label="是否设置阶梯价"
            prop="stepPriceStatus"
          >
            <el-radio-group
              v-model="ruleForm.stepPriceStatus"
              style="margin-right: 10px"
              :disabled="(disabled && otherRuleDisabled) || pintuanDisabled"
              @change="handleChangeItemStepPriceStatus"
            >
              <el-radio :label="2">否</el-radio>
              <el-radio :label="1">是</el-radio>
            </el-radio-group>
            <el-tooltip
              placement="bottom"
            >
              <div slot="content">
                1.阶梯价最多支持3个阶梯。<br />
                2. 设置阶梯价时 最大阶梯起拼数量≤活动个人限购数量≤活动总限购数量，不满足则提交失败。<br />
                3.商品列表中展示的起拼数量为默认起拼数量，一个拼团活动中最多可支持4个拼团价格。<br />
                4.药店买满起始促销数量后，成团价自动改为对应阶梯价；<br />
                起拼数量＜促销起始数量1＜促销起始数量2＜促销起始数量3≤活动个人限购数量，拼团价格＞阶梯价1＞阶梯价2＞阶梯价3，不符合则提交失败；
              </div>
              <i class="el-icon-warning-outline" />
            </el-tooltip>
          </el-form-item>
		      <el-form-item
            v-if="judgeIsApplyStepPrice && ruleForm.stepPriceStatus === 1"
            label=""
          >
            <div
              v-for="(item, index) in ruleForm.activityReportGroupLevelPriceDTOList"
              :key="index"
              style="display: flex; align-items: center;margin-bottom: 10px;align-items: center;"
            >
              <span style="margin-right: 10px;">促销起始数量{{ index + 1 }}</span>
              <el-input
                v-model.number="item.startQty"
                :precision="0"
                :disabled="disabled && pintuanDisabled && otherRuleDisabled"
                placeholder="请输入"
                style="width: 200px"
              />
              <span style="margin: 0 10px;">阶梯价{{ index + 1 }}</span>
              <el-input-number
                v-model="item.discountPrice"
                :precision="2"
                :disabled="disabled && pintuanDisabled && otherRuleDisabled"
                placeholder="请输入"
                style="width: 200px"
              />
              <div>
                <i
                  v-if="index === 0"
                  style="font-size: 30px; cursor: pointer;margin-left: 10px;"
                  class="el-icon-circle-plus-outline"
                  @click="handleSetGroupLevelPriceDTOList('add', index)"
                />
                <i
                  v-if="index !== 0"
                  style="font-size: 30px; cursor: pointer;margin-left: 10px;"
                  class="el-icon-remove-outline"
                  @click="handleSetGroupLevelPriceDTOList('reduce', index)"
                />
              </div>
            </div>
          </el-form-item>
          <el-form-item label="单个药店是否限购">
              <el-switch :disabled="(disabled && otherRuleDisabled) || statusList.status1" v-model="swtich.value1" active-color="#4183d5" inactive-color="#b1b1b1" active-text="限制" inactive-text="不限制" @change="ruleForm.personalLimitType = ''; ruleForm.personalLimitNum = ''" style="vertical-align: top;transform: translateY(7px);">
              </el-switch>
              <div style="display: inline-block;margin-left: 10px;">
                <span style="color: rgb(255, 33, 33);">
                  单个药店在设定的周期内可购买的最大采购数量，需为正整数。限购数量需≥起拼数量;
                  <br>
                  系统会从修改那一刻重新计算限购数量。
                </span>
              </div>
          </el-form-item>
          <el-form-item label="单个药店限购类型" prop="personalLimitType" v-if="swtich.value1">
            <el-select v-model="ruleForm.personalLimitType" :disabled="(disabled && otherRuleDisabled) || statusList.status2" placeholder="请选择">
              <el-option v-for="item in swtich.personalLimitTypeList" :key="item.code" :label="item.value" :value="item.code"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item v-if="swtich.value1" label="单个药店参团数量上限" prop="personalLimitNum">
            <el-input :disabled="(disabled && otherRuleDisabled) || statusList.status3" v-model="ruleForm.personalLimitNum" placeholder="请输入正整数" style="width: 200px" @input="valueChange(ruleForm.personalLimitNum, 'personalLimitNum')"/>
            <span style="color: #ff2121"> 每家参团药店活动期间最高采购数量</span>
          </el-form-item>
          <el-form-item label="是否限制活动总限购">
              <el-switch :disabled="(disabled && totalLimitNumDisabled) || statusList.status4" v-model="swtich.value2" @change="ruleForm.totalLimitNum = ''" active-color="#4183d5" inactive-color="#b1b1b1" active-text="限制" inactive-text="不限制">
              </el-switch>
          </el-form-item>
          <el-form-item label="拼团活动采购上限" prop="totalLimitNum" v-if="swtich.value2">
            <el-input :disabled="(disabled && totalLimitNumDisabled) || statusList.status5" v-model="ruleForm.totalLimitNum" placeholder="请输入" style="width: 200px" />
            <span style="color: #ff2121"> 所有参团药店采购总数量不能大于该数值</span>
          </el-form-item>
          <el-form-item label="设置虚拟供应商" prop="isVirtualShop">
            <el-radio :disabled="editCollageItem.status == 5" v-model="ruleForm.isVirtualShop" label="1">是</el-radio>
            <el-radio :disabled="editCollageItem.status == 5" v-model="ruleForm.isVirtualShop" label="2">否</el-radio>
            <span style="color: #ff2121;display: inline-block;width: 80%;vertical-align: top">
              • 选“是”，则该拼团活动支付前都只显示虚拟供应商信息，支付成功后才显示真实供应商信息<br>
              • 选“否”，则正常显示供应商信息
            </span>
          </el-form-item>

          <el-form-item label="平台限制销售范围及供货对象：" v-if="ruleForm.customerGroupVO">
            <p style="color: red">提报到本拼团主题的活动，最终售卖范围是平台限制区域和原品销售范围取交集。示例：平台限制仅售卖湖北、单体；店铺已选范围为全国，则最终取交集后售卖范围为“湖北单体”</p>
            <div
              v-for="(item, index) in ruleForm.customerGroupVO.contentBundleDescriptions"
              :key="index"
            >
              <p v-for="(one, index1) in item" :key="index1">{{ one }}</p>
            </div>
            <p v-if="ruleForm.customerGroupVO.specifyUserDescription">
              {{ ruleForm.customerGroupVO.specifyUserDescription }}
            </p>
            <p>人群ID：{{ ruleForm.customerGroupVO.id }}</p>
          </el-form-item>

          <el-form-item label="在途库存" prop="onTheWayStock">
            <el-input :disabled="disabled" v-model="ruleForm.onTheWayStock" placeholder="请输入" style="width: 200px" onkeyup="this.value=this.value.replace(/[^\d]/g,'')"/>
          </el-form-item>
          <!-- 供货方式配置信息 -->
          <SupplyTypeConfig ref="supplyTypeInfo" :baseCustomerGroupName="(collageActType === 'singleCreate' && ruleForm.customerGroupVO) ? ruleForm.customerGroupVO.tagName :  collageActType === 'singleCreate' ? ruleForm.customerGroupName:''" :isPT="this.$route.query.fromType == 'edit'" :baseCustomerGroupId="collageActType === 'singleCreate' ? ruleForm.baseCustomerGroupId: ''" :isedit="ruleForm.isedit" :disabled="disabled" :subOtherDisabled="subOtherDisabled" :sale-scope-dto="saleScopeDTO" />
          <el-form-item class="searchBtn">
            <el-button type="primary" :loading="submitLoading" @click="submitForm('ruleForm')">
              提交
            </el-button>
            <el-button @click="resetForm('ruleForm')"> 取消</el-button>
            <el-button
              style="margin-left: 10px"
              type="primary"
              size="small"
              @click="toCollageActivityNew"
            >
            拼团上架（新）
          </el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>
    <crowd-selector-dialog
      v-if="dialogVisible"
      v-model="dialogVisible"
      :selected="ruleForm.customerGroupId"
      @onSelect="onSelect"
    />
  </div>
</template>

<script>
import { saveActivity, addApply, getIsApplyStepPrice, saveReportCheck,getSupportPersonalLimitType,updateCheck } from '@/api/market/collageActivity';
import CrowdSelectorDialog from '../../components/xyy/customerOperatoin/crowd-selector-dialog.vue';
import SupplyTypeConfig from './components/supplyTypeConfig.vue';

export default {
  name: 'EditCollageActivity',
  components: { CrowdSelectorDialog, SupplyTypeConfig },
  data() {
    return {
      swtich: {
        whereFrom:null,
        value1: false,
        value2: false,
        personalLimitTypeList: [{
          code: "1", value: "活动期间限购"
        }, {
          code: "2", value: "每天（每天00:00至24:00）"
        }, {
          code: "3", value: "单笔订单限购"
        }, {
          code: "4", value: "每周（周一00:00至周日24:00）"
          }, {
          code: "5", value: "每月（每月1号00:00至每月最后一天24:00）"
        }]
      },
      warningRateStatusCount: null,
      totalLimitNumDisabled: false,
      subOtherDisabled: true,
      otherRuleDisabled: true,
      judgeIsApplyStepPrice: false,
      disabled: false,
      submitLoading: false,
      editCollageItem: {},
      isLoading: false,
      addCoupon: false,
      dialogVisible: false,
      addInitiatorDTOItem: { name: '', amount: 0 },
      ruleForm: {
        activityReportGroupLevelPriceDTOList: [],
        stepPriceStatus: -1,
        isCopyCsuModel: 1,
        // isCopySaleArea: 1,
        personalLimitType: '0',
        groupPrice: '',
        groupNum: '',
        customerGroupId: undefined,
        customerGroupName: '',
        totalLimitNum: '',
        personalLimitNum: '',
        preheatTime: '',
        activityTime: [],
        auditTime: [],
        registrationTime: [],
        isVirtualShop: '',
        onTheWayStock: '',
        topicTitle: '',
        baseCustomerGroupId: '',
        customerGroupVO: null,
        customerGroupName:'',
        // radioCheck: true,
      },
      statusList: {
        status1: false,   //是否不允许修改单个药店限购
        status2: false,  //是否不允许修改限购类型
        status3: false,  //是否不允许修改限购数量
        status4: false,  //是否不允许修改拼团活动限购
        status5: false,  //是否不允许修改拼团活动总数量
      },
      selItem: [],
      rules: Object.freeze({
        groupPrice: [{ required: true, message: '请填写拼团价格',trigger: 'blur' }],
        groupNum: [
          { required: true, message: '请填写起拼数量',trigger: 'blur' },
          { pattern: /^[1-9]\d*$/,message: '请维护正整数', trigger: ['blur', 'change'] }
        ],
        totalLimitNum: [
          { required: false, message: '请填写拼团活动采购上限',trigger: 'blur' },
          { pattern: /^[1-9]\d*$/,message: '请维护正整数', trigger: ['blur', 'change'] }
        ],
        personalLimitNum: [{ required: false, message: '请填写单个药店参团数量上限',trigger: 'blur' }],
      }),
      templateId: null,
      tableLoading: false,
      selectedList: [],
      saleScopeDTO: {},
      collageActType: '',
      isHidden: false,
      pintuanDisabled: false,
      temp: {
        id: 'xxx',
        barcode: 'xxx'
      }
    };
  },
  created() {
    // this.getIsApplyStepPrice();
    // if (this.$route.query.fromType === 'singleCreate') {
    //   this.singleCreateInit();
    //   sessionStorage.setItem('editCollageItem', '');
    // } else {
    //   this.initData();
    //   sessionStorage.setItem('singleCreateInfo', '');
    // }
    /* getSupportPersonalLimitType().then(res => {
      if (res.code == 1000) {
        this.swtich.personalLimitTypeList = res.data.personalLimitTypeList;
      }
    }) */
  },
  activated() {
    /* const data = sessionStorage.getItem('editCollageItem') ? JSON.parse(sessionStorage.getItem('editCollageItem')) : null; */
    let data = '';
    let id = ''
    if (this.$route.query.fromType === 'singleCreate') {
      data = sessionStorage.getItem('singleCreateInfo') ? JSON.parse(sessionStorage.getItem('singleCreateInfo')) : null;
      id = data.baseId;
    } else {
      
      data = sessionStorage.getItem('editCollageItem') ? JSON.parse(sessionStorage.getItem('editCollageItem')) : null
      if(this.$route.query.isCollageActTypeTb){
        id = data.baseId
        
      }else{
        id = data.frameReportId
      }
     
    }
    console.log(data);
    if (this.$route.query.fromType === 'singleCreate') {
      // 如果是拼团活动单个创建，不再缓存直接重新加载
      // 如果是单纯从其它页面回来而不是跳转过来，照常缓存信息
      let data = sessionStorage.getItem('createCollageActivity')
        ? JSON.parse(sessionStorage.getItem('createCollageActivity'))
        : null
      if (data) {
        this.loadNew()
        sessionStorage.removeItem('createCollageActivity')
      }
    }
    else if (!data || this.temp.id != id || this.temp.barcode != data.barcode) {
      this.loadNew()
    }else if(String(this.whereFrom) !== this.$route.query.isCollageActTypeTb){
      console.log(this.whereFrom,this.$route.query.isCollageActTypeTb)
      this.loadNew()
    }
    this.whereFrom = Boolean(this.$route.query.isCollageActTypeTb)
    this.temp.id = id;
    this.temp.barcode = data ? data.barcode : 'xxx';
  },
  deactivated() {

  },
  methods: {
    toCollageActivityNew(){
      window.openTab('/collageActivityNew', {})
    },
    loadNew(){
      try{
        if(!this.$route.query.isCollageActTypeTb){
            sessionStorage.setItem('collageActTypeTb', '');  
          }else{
            sessionStorage.setItem('collageActTypeTb', 'collageActTypeTb');  
          }
      }catch(e){
        
      }
      this.swtich.value1 = false;
      this.swtich.value2 = false;
      this.statusList.status1 = false;
      this.statusList.status2 = false;
      this.statusList.status3 = false;
      this.statusList.status4 = false;
      this.statusList.status5 = false;
      this.collageActType = sessionStorage.getItem('collageActType');
      this.getIsApplyStepPrice();
      if (this.collageActType === 'singleCreate') {
        this.singleCreateInit();
        sessionStorage.setItem('editCollageItem', '');
        sessionStorage.setItem('collageActTypeTb', '');     
      } else {
        this.initData();
        sessionStorage.setItem('singleCreateInfo', '');
        this.isHidden = false;
      }
      // alert(this.$route.query.isCollageActTypeTb)
     
    }, // 加载数据
	valueChange(value, key) {
		const arr = value.toString().match(/^[0-9]*[1-9][0-9]*$/);
      	this.ruleForm[key] = arr ? arr[0] : null;
	},
    getIsApplyStepPrice() {
      getIsApplyStepPrice().then((res) => {
        if (res.success) {
          const { data } = res;
          this.judgeIsApplyStepPrice = (data || {}).judgeIsApplyStepPrice;
        }
      });
    },
    handleSetGroupLevelPriceDTOList(type, index) {
      if (this.disabled && this.otherRuleDisabled) return;
      if (type === 'add') {
        if (
          this.ruleForm.activityReportGroupLevelPriceDTOList.length === 3
        ) {
          this.$message.warning('最多添加3条数据');
          return;
        }
        const obj = { ...this.addInitiatorDTOItem };
        this.ruleForm.activityReportGroupLevelPriceDTOList.push(obj);
        return;
      }
      this.ruleForm.activityReportGroupLevelPriceDTOList.splice(index, 1);
    },
    handleChangeItemStepPriceStatus(val) {
      if (val === 2) {
        this.ruleForm.activityReportGroupLevelPriceDTOList = null;
      } else if (
        this.ruleForm.activityReportGroupLevelPriceDTOList === null || (this.ruleForm.activityReportGroupLevelPriceDTOList || []).length === 0
      ) {
        const arr = [];
        const obj = { ...this.addInitiatorDTOItem };
        arr[0] = obj;
        this.ruleForm.activityReportGroupLevelPriceDTOList = arr;
      }
    },
    singleCreateInit() {
      try {
        this.$nextTick(() => {
          this.ruleForm.auditTime = [];
          this.ruleForm.activityTime = [];
          this.ruleForm.preheatTime = '';
          this.editCollageItem.baseId = '';
          this.ruleForm.registrationTime = [];
          this.ruleForm.topicTitle = '';
          this.ruleForm.stepPriceStatus = -1;
          this.ruleForm.activityReportGroupLevelPriceDTOList = [];
          let info = sessionStorage.getItem('singleCreateInfo') ? JSON.parse(sessionStorage.getItem('singleCreateInfo')) : {};
          Object.keys(this.ruleForm).forEach((key) => {
            this.ruleForm[key] = info[key] || '';
          }, this);
          this.ruleForm.isedit = info.isedit;
          console.log('jtt',this.ruleForm);
          
          this.ruleForm.auditTime = info.auditTime || [];
          this.ruleForm.activityTime = info.activityTime || [];
          this.ruleForm.preheatTime = info.preheatTime;
          this.editCollageItem.baseId = info.baseId;
          this.ruleForm.registrationTime = info.registrationTime || [];
          this.ruleForm.topicTitle = info.topicTitle;
          this.ruleForm.stepPriceStatus = -1;
          this.ruleForm.activityReportGroupLevelPriceDTOList = [];


          this.editCollageItem.barcode = info.barcode;
          this.editCollageItem.productCode = info.productCode;
          this.editCollageItem.productName = info.productName;
          this.editCollageItem.spec = info.spec;
          this.editCollageItem.manufacturer = info.manufacturer;
          this.editCollageItem.fob = info.fob;
          this.editCollageItem.guidePrice = info.guidePrice;
          this.saleScopeDTO = {};
          this.disabled = false;
        });
      } catch (err) {
        console.log(55555555555, err);
      }
    },
    initData() {
      this.disabled = false;
      this.editCollageItem = sessionStorage.getItem('editCollageItem') ? JSON.parse(sessionStorage.getItem('editCollageItem')) : {};
      //判断是否有平台
      if (this.editCollageItem.activityReportGroupAmountDtos && this.editCollageItem.activityReportGroupAmountDtos.some(item => item.name == '平台')) {
        //有则判断当前编辑的活动状态  3 未启动 不可切换是否限购和限购类型     4进行中
        this.pintuanDisabled = true;
        if (this.editCollageItem.status == 3 || this.editCollageItem.status == 4) {
          this.statusList.status1 = true;
          this.statusList.status2 = true;
          this.statusList.status4 = true;
          this.statusList.status5 = true;
        }
      } else {
        if (this.editCollageItem.status == 4) {
          /* this.statusList.status3 = true; */
          /* this.statusList.status4 = true; */
          /* this.statusList.status5 = true; */
        }
      }
      Object.keys(this.ruleForm).forEach((key) => {
        this.ruleForm[key] = this.editCollageItem[key];
      }, this);
      if (!isNaN(Number(this.ruleForm.personalLimitType)) && Number(this.ruleForm.personalLimitType) > 0) {
        this.swtich.value1 = true
      }
      if (!isNaN(Number(this.ruleForm.totalLimitNum)) && Number(this.ruleForm.totalLimitNum) > 0) {
        this.swtich.value2 = true
      }
      this.ruleForm.personalLimitType = this.ruleForm.personalLimitType.toString();
      this.ruleForm.auditTime = [this.editCollageItem.auditStime, this.editCollageItem.auditEtime];
      this.ruleForm.activityTime = [this.editCollageItem.actStartTime, this.editCollageItem.actEndTime];
      this.ruleForm.preheatTime = this.editCollageItem.preheatTime;
      this.ruleForm.isCopyCsuModel = this.editCollageItem.isCopyCsuModel;
      // this.ruleForm.isCopySaleArea = this.editCollageItem.isCopySaleArea;
      this.ruleForm.customerGroupId = this.editCollageItem.customerGroupId;
      this.ruleForm.isVirtualShop = String(this.editCollageItem.isVirtualShop);
      this.ruleForm.stepPriceStatus = this.editCollageItem.stepPriceStatus;
      this.ruleForm.activityReportGroupLevelPriceDTOList = this.editCollageItem.activityReportGroupLevelPriceDTOList;
      // this.ruleForm.radioCheck = true;
      // if (this.editCollageItem.isCopyCsuModel === 2 || (this.editCollageItem.isCopyCsuModel === 1 && this.editCollageItem.isCopySaleArea === 2)) {
      //   this.ruleForm.radioCheck = false;
      // }
      if (this.editCollageItem.status == 1 && this.editCollageItem.registrationEtime && this.editCollageItem.registrationEtime <= new Date().getTime()) {
        this.disabled = true;
      }
      this.otherRuleDisabled = true;

      this.subOtherDisabled = true;
      this.totalLimitNumDisabled = false;
      if (this.editCollageItem.status === 3 || this.editCollageItem.status === 4) {
        this.disabled = true;
        if (this.editCollageItem.subsidyInitiatorJson) {
          this.totalLimitNumDisabled = true;
        }
        if (this.editCollageItem.activityPriceStrategyList) {
          this.totalLimitNumDisabled = true;
        }
        // if (this.editCollageItem.subsidyInitiatorJson === null || this.editCollageItem.activityPriceStrategyList === null) {
        //   this.otherRuleDisabled = false;
        // }
        this.otherRuleDisabled = false;
        if (this.editCollageItem.subsidyInitiatorJson === null && this.editCollageItem.activityPriceStrategyList === null) {
          this.subOtherDisabled = false;
        }
      }
      this.saleScopeDTO = {
        ...this.editCollageItem.saleScopeDTO,
        customerGroupName: this.editCollageItem.customerGroupName,
      };
      console.log(this);
      // this.$refs.ruleForm.clearValidate(['groupPrice', 'groupNum']);
      if(sessionStorage.getItem('collageActTypeTb')=='collageActTypeTb'){
        this.disabled = false;
        this.totalLimitNumDisabled = false;
        this.subOtherDisabled = true;
        this.otherRuleDisabled = true;
        this.statusList.status1 = false;
        this.statusList.status2 = false;
        this.statusList.status4 = false;
        this.statusList.status5 = false;
        this.pintuanDisabled=false;
      }
    },
    onSelect(selectItem) {
      this.ruleForm.customerGroupName = selectItem.tagName;
      this.ruleForm.customerGroupId = selectItem.id;
    },
    addPeopel() {
      this.dialogVisible = true;
    },
    submitForm(formName) {
      this.$refs[formName].validate(async (valid) => {
        if (valid) {
          // if (!this.ruleForm.radioCheck && !this.ruleForm.customerGroupId) {
          //   this.$message.warning('请选择人群');
          //   return;
          // }
          if ((this.ruleForm.groupPrice && isNaN(this.ruleForm.groupPrice))
              || (this.ruleForm.groupNum && isNaN(this.ruleForm.groupNum))
              || (this.ruleForm.totalLimitNum && isNaN(this.ruleForm.totalLimitNum))
              || (this.ruleForm.personalLimitNum && isNaN(this.ruleForm.personalLimitNum))
              || (this.ruleForm.onTheWayStock && isNaN(this.ruleForm.onTheWayStock))
          ) {
            this.$message.warning('请填写数字');
            return;
          }
          let supplyInfo = this.$refs['supplyTypeInfo'].getAllSupplyInfo();
          if (this.ruleForm.stepPriceStatus === 1) {
            if (
              this.ruleForm.activityReportGroupLevelPriceDTOList
            ) {
              this.ruleForm.activityReportGroupLevelPriceDTOList.forEach(
                (item, index) => {
                  item.level = index + 1;
                },
              );
            }
          }
          const { editCollageItem } = this;
		  if (this.swtich.value1 && (!this.ruleForm.personalLimitType || !this.ruleForm.personalLimitNum)) {
			this.$message.warning("请填写单个药店限购类型及参团数量上限")
			return
		  }
      // let ver=()=>{
      //       if(this.ruleForm.stepPriceStatus != 1||this.editCollageItem.stepPriceStatus != 1){
      //         return false
      //       }
      //       let next=false
      //       this.ruleForm.activityReportGroupLevelPriceDTOList.forEach((res,index)=>{
      //         if(this.editCollageItem.activityReportGroupLevelPriceDTOList[index]&&Number(res.discountPrice)>Number(this.editCollageItem.activityReportGroupLevelPriceDTOList[index].discountPrice)){
      //           next=true
      //         }
      //       })
      //       return next
      //     }
      //     if(this.editCollageItem.activityReportGroupAmountDtos.some(item => item.name == '平台')&&(Number(this.ruleForm.groupPrice)>Number(this.editCollageItem.groupPrice)||ver())){
      //       const confirmRes = await this.$confirm(
      //         `当前拼团活动存在平台补贴${this.editCollageItem.activityReportGroupAmountDtos.find(item => item.name == '平台').amount}元，调高价格后补贴将自动失效，请确认是否继续调高拼团价格？`,
      //           '提示', {
      //               confirmButtonText: '确定',
      //               cancelButtonText: '取消',
      //               type: 'warning'
      //           }
      //       ).catch(err => console.log(err))
      //       // 判断confirmRes的值
      //       if (confirmRes !== 'confirm') {
      //           return 
      //       }
      //     }
          const query = {
            frameReportId: editCollageItem.frameReportId || null,
            barcode: editCollageItem.barcode,
            version: editCollageItem.version || null,
            groupPrice: this.ruleForm.groupPrice,
            groupNum: this.ruleForm.groupNum,
            totalLimitNum: this.swtich.value2 ? this.ruleForm.totalLimitNum : '',
            personalLimitNum: this.swtich.value1 ? this.ruleForm.personalLimitNum : '',
            personalLimitType: this.swtich.value1 ? this.ruleForm.personalLimitType : '0',
            // isCopySaleArea: this.ruleForm.radioCheck ? 1 : 2,
            // customerGroupId: !this.ruleForm.radioCheck ? this.ruleForm.customerGroupId : '',
            isVirtualShop: this.ruleForm.isVirtualShop,
            onTheWayStock: this.ruleForm.onTheWayStock,
            stepPriceStatus: this.ruleForm.stepPriceStatus,
            activityReportGroupLevelPriceDTOList: this.ruleForm.activityReportGroupLevelPriceDTOList || null,
          };
          query.baseId = editCollageItem.baseId;
          query.saleScopeDTO = { isCopySaleArea: supplyInfo.isCopySaleArea };

          if (supplyInfo.isCopySaleArea === 3) {
            let { customerGroupId } = supplyInfo;
            query.saleScopeDTO = { ...supplyInfo ,customerGroupId};
            if (supplyInfo.isCopyControlUser === 2 && supplyInfo.controlRosterType != 2) {
              const controlUserTypes = supplyInfo.controlUserTypes ? supplyInfo.controlUserTypes : [];
              query.saleScopeDTO.controlUserTypes = controlUserTypes.join();
            } else {
              query.saleScopeDTO.controlUserTypes = '';
            }
          } else if (supplyInfo.isCopySaleArea === 2) {
            let { customerGroupId } = supplyInfo;
            if (this.collageActType === 'singleCreate' && this.ruleForm.baseCustomerGroupId) {
              customerGroupId = this.ruleForm.baseCustomerGroupId;
            }
            query.saleScopeDTO = {
              isCopySaleArea: 2,
              customerGroupId,
            };
          }else if(supplyInfo.isCopySaleArea === 1){
            let { customerGroupId } = supplyInfo;
            query.saleScopeDTO = {
              isCopySaleArea: 1,
              customerGroupId,
            };
          }

          if (this.collageActType === 'singleCreate'||sessionStorage.getItem('collageActTypeTb')=='collageActTypeTb') {
            this.saveReportCheck(query);
          } else {
            this.confirmSubmit(query);
          }
        } else {
          return false;
        }
        return false;
      });
    },
    saveReportCheck(query) {
      this.submitLoading = true;
      saveReportCheck(query).then((res) => {
        if (res.code === 1000) {
          this.confirmSubmitSingle(query);
        } else if (res.data.existReportIdList) {
          const h = this.$createElement;
          this.$confirm('提示', {
            title: '提示',
            message: h('div', [
              h(
                'span',
                null,
                '重复时间范围内未结束的拼团活动，'
              ),
              h(
                'span',
                { style: 'color: red;' },
                `存在业务商圈、供货对象、拼团价格、起拼数量一致的拼团活动${res.data.existReportIdList}，上架后将会降低APP商品搜索顺序`
              ),
              h(
                'span',
                null,
                '，请确认是否继续上架？'
              )
            ]),
            confirmButtonText: '确定',
            cancelButtonText: '取消'
          })
            .then(() => {
              this.confirmSubmitSingle(query);
            })
            .catch(() => {
              this.submitLoading = false;
            })
        } else {
          this.submitLoading = false;
          this.$message.warning(res.msg)
        }
      }).catch((error) => {
        this.submitLoading = false;
        this.$message.warning(error || '系统错误')
      })
    },
    confirmSubmitSingle(query) {
      addApply(query)
      .then((res) => {
        if (res.success) {
          this.$message.success('提交成功');
          sessionStorage.setItem('editCollageItem', '');
          sessionStorage.setItem('collageActTypeTb', '');     
          this.temp.id = 'xxx';
          this.temp.barcode = 'xxx';
          setTimeout(() => {
            this.submitLoading = false;
            const path = this.$route.fullPath;
            window.closeTab(path);
            window.closeTab('/editCollageActivity');
            this.$router.push({
              path: '/collageActivity',
              query: {
                refresh: true
              }
            })
            // this.$router.replace({
            //   path: '/collageActivity',
            //   query: { refresh: true },
            // });
          }, 500);
        } else if (res.msg) {
          this.$message.warning(res.msg);
          this.submitLoading = false;
        };
      }).catch(() => {
        this.submitLoading = false;
      });
    },
   async confirmSubmit(query) {
      this.submitLoading = true;
      let res=await updateCheck(query)
     this.submitLoading = false;
     if(res.code!=1000){
        const confirmRes = await this.$confirm(
              res.msg,
                '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }
            ).catch(err => console.log(err))
            // 判断confirmRes的值
            if (confirmRes !== 'confirm') {
                return 
            }
          
     }
      this.submitLoading = true;
      saveActivity(query)
      .then((res) => {
        this.submitLoading = false;
        if (res.success) {
          this.$message.success('提交成功');
          sessionStorage.setItem('editCollageItem', '');
          sessionStorage.setItem('collageActTypeTb', '');     
          this.temp.id = 'xxx';
          this.temp.barcode = 'xxx';
          setTimeout(() => {
            const path = this.$route.fullPath;
            window.closeTab(path);
            window.closeTab('/editCollageActivity');
            this.$router.push({
              path: '/collageActivity',
              query: {
                refresh: true
              }
            })
            // this.$router.replace({
            //   path: '/collageActivity',
            //   query: { refresh: true },
            // });
          }, 500);
        } else if (res.msg) this.$message.warning(res.msg);
      }).catch(() => {
        this.submitLoading = false;
      });
    },
    resetForm(formName) {
      this.$refs[formName].resetFields();
      this.temp.id = 'xxx';
      this.temp.barcode = 'xxx';
      sessionStorage.setItem('editCollageItem', '');
      sessionStorage.setItem('collageActTypeTb', '');     
      // this.$router.go(-1);
      const path = this.$route.fullPath;
      window.closeTab(path);
      window.closeTab('/editCollageActivity');
    },
  },
};
</script>

<style lang="scss" scoped>
.radio-span {
  color: rgba(51, 51, 51, 1);
  line-height: 17px;
  margin-right: 30px;
}
.radio-class {
  margin-right: 0px;
  margin-top: 8px;
}
.serch {
  .el-form-item__label {
    padding: 0 20px 0 0 !important;
  }
  padding: 20px;
  font-weight: 500;
  .sign {
    display: inline-table;
    width: 3px;
    height: 13px;
    background: linear-gradient(360deg, #1d69c4 1%, #8bbdfc 99%);
    border-radius: 2px;
    margin-right: 8px;
  }
  .fromBlock {
    margin-top: 20px;
    .couponName {
      display: inline-block;
      width: 70%;
      ::v-deep  .el-input__inner {
        border: none;
      }
    }
  }
  ::v-deep   .el-table tr td .cell {
    height: auto;
    line-height: normal;
    text-overflow: inherit;
    white-space: break-spaces;
    .el-radio-group {
      text-align: left;
    }
  }
}
</style>
