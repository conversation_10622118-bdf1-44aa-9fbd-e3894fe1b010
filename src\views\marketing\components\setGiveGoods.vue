<template>
  <div>
    <span @click="show">
      <slot>
        <el-button type="text">{{ title }}</el-button>
      </slot>
    </span>
    <el-dialog :title="title" :visible.sync="visible" width="1200px" append-to-body>
      <div style="margin-bottom: 10px;">
        <p style="margin: 7px 0;">
          1、最多可添加{{ maxCount }}个赠品 （<span style="color:#4183d5;">{{ list.length }}</span> / {{ maxCount }}）
        </p>
        <p style="margin: 7px 0;">
          2、活动创建成功，普通商品将自动生成对应的赠品
        </p>
        <p style="display: flex;justify-content: space-between;align-items: center;">
          <span v-if="type == 0" style="color:#ff5555;">3、请仔细检查赠送数量，主品满足条件，以下赠品将全部赠送</span>
          <span v-else style="color:#ff5555;">3、主品满足条件，客户可以从以下赠品池中选择指定数量或指定金额的赠品</span>
          <span v-if="!disabled">
            <multipartUpload v-if="type == 1" :type="0" :curList="list" :maxCount="maxCount" @getList="getMulitpartUploadList"></multipartUpload>
            <el-button v-if="type == 1" style="margin:0 5px;" type="primary" size="mini" @click="multipartDelete">批量删除</el-button>
            <select-product v-model="list" :maxSelectNum="maxCount" :type="type"/>
          </span>
        </p>
      </div>
      <el-table ref="table" :data="list" border  height="600px" @selection-change="handleSelectionChange" row-key="barcode">
        <el-table-column v-if="type == 1 && !disabled" type="selection" width="55" align="center" reserve-selection>
        </el-table-column>
        <el-table-column align="center" label="商品信息" width="350">
          <div slot-scope="scope" style="display: flex;align-items: center;gap: 10px;">
            <div>
              <el-image
                style="width: 70px; height: 70px;border-radius: 5px;overflow: hidden;background-color: #f3f3f3;"
                fit="cover"
                :src="scope.row.fullImageUrl"
                :preview-src-list="[scope.row.fullImageUrl]">
              </el-image>
            </div>
            <div>
              <p class="keyValue">
                <span>CSUID</span>：
                <span>{{ scope.row.csuid }}</span>
              </p>
              <p class="keyValue">
                <span>商品编码</span>：
                <span>{{ scope.row.barcode }}</span>
              </p>
              <p class="keyValue">
                <span>ERP编码</span>：
                <span>{{ scope.row.erpCode }}</span>
              </p>
              <p class="keyValue">
                <span>商品名称</span>：
                <span>{{ scope.row.showName }}</span>
              </p>
              <p class="keyValue">
                <span>规格</span>：
                <span>{{ scope.row.spec }}</span>
              </p>
              <p class="keyValue">
                <span>中包装数量</span>：
                <span>{{ scope.row.mediumPackageNum }}{{ scope.row.isSplit == 1 ?  '(可拆零)' : '(不可拆零)'}}</span>
              </p>
              <p class="keyValue">
                <span>生产厂家</span>：
                <span>{{ scope.row.manufacturer }}</span>
              </p>
            </div>
          </div>
        </el-table-column>
        <el-table-column align="center" label="价格" width="150">
          <template slot-scope="scope">
            <p class="keyValue">
              <span style="width: 45px;">单体价</span>：
              <span>{{ scope.row.fob }}</span>
            </p>
            <p class="keyValue">
              <span style="width: 45px;">连锁价</span>：
              <span>{{ scope.row.chainPrice }}</span>
            </p>
            <p class="keyValue">
              <span style="width: 45px;">活动价</span>：
              <span>0.01</span>
            </p>
          </template>
        </el-table-column>
        <el-table-column align="center" label="可售库存" width="200">
          <template slot-scope="scope">
            <p>{{ scope.row.availableQty }}</p>
            <p class="keyValue">
              <span style="width: 30px;">近至</span>：
              <span>{{ scope.row.nearEffect }}</span>
            </p>
            <p class="keyValue">
              <span style="width: 30px;">远至</span>：
              <span>{{ scope.row.farEffect }}</span>
            </p>
          </template>
        </el-table-column>
        <el-table-column align="center" label="商品来源">
          <template slot-scope="{ row }">
            {{ {1: '拼团商品', 2: '赠品', 0: '普通商品', 3:'批购包邮商品' }[row.activityType] }}
          </template>
        </el-table-column>
        <el-table-column v-if="type == 0" align="center" width="250">
          <div slot="header" style="color:red;">赠送数量</div>
          <template slot-scope="scope">
            <el-input-number v-if="temp" style="width: 140px;margin: 5px;" :step="scope.row.isSplit == 1 ? 1 : scope.row.mediumPackageNum" :disabled="disabled" v-model="scope.row.everyQty" :precision="0" :min="scope.row.isSplit == 1 ? 1 : scope.row.mediumPackageNum" @change="valueChange(scope.row, 'everyQty')" controls-position="right">
            </el-input-number>
          </template>
        </el-table-column>
        <el-table-column align="center" width="250">
          <div slot="header" style="color:red;">赠品总数量限制</div>
          <template slot-scope="scope">
            <el-radio-group v-model="scope.row.tempRadio" :disabled="disabled" @change="scope.row.giveQty = scope.row.tempRadio == 1 ? scope.row.giveQty : -1;">
              <el-radio :label="0">不限制</el-radio>
              <el-radio :label="1">限制</el-radio>
            </el-radio-group>
            <el-input-number v-if="scope.row.tempRadio == 1" :disabled="disabled && !onlyChangeTotalNum" style="width: 140px;margin: 5px;" v-model="scope.row.giveQty" :precision="0" :min="0" @change="valueChange(scope.row, 'giveQty', 'tempRadio')" controls-position="right">
            </el-input-number>
            <p v-if="scope.row.qty && scope.row.qty >= 0">当前剩余可赠数量: {{ scope.row.qty }}</p>
          </template>
        </el-table-column>
        <el-table-column v-if="type == 1" align="center" width="250">
          <div slot="header" style="color:red;">单笔订单赠送下限</div>
          <template slot-scope="scope">
            <el-radio-group v-model="scope.row.tempRadio2" :disabled="disabled" @change="scope.row.orderGiveMinQty = scope.row.tempRadio2 == 1 ? scope.row.orderGiveMinQty : -1;">
              <el-radio :label="0">不限制</el-radio>
              <el-radio :label="1">限制</el-radio>
            </el-radio-group>
            <el-input-number v-if="scope.row.tempRadio2 == 1 && temp" :step="scope.row.isSplit == 1 ? 1 : scope.row.mediumPackageNum" :disabled="disabled" style="width: 140px;margin: 5px;" v-model="scope.row.orderGiveMinQty" :precision="0" :min="0" @change="valueChange(scope.row, 'orderGiveMinQty', 'tempRadio2')" controls-position="right">
            </el-input-number>
          </template>
        </el-table-column>
        <el-table-column v-if="type == 1" align="center" width="250">
          <div slot="header" style="color:red;">单笔订单赠送上限</div>
          <template slot-scope="scope">
            <el-radio-group v-model="scope.row.tempRadio3" :disabled="disabled" @change="scope.row.orderGiveMaxQty = scope.row.tempRadio3 == 1 ? scope.row.orderGiveMaxQty : -1;">
              <el-radio :label="0">不限制</el-radio>
              <el-radio :label="1">限制</el-radio>
            </el-radio-group>
            <el-input-number v-if="scope.row.tempRadio3 == 1 && temp" :step="scope.row.isSplit == 1 ? 1 : scope.row.mediumPackageNum" :disabled="disabled" style="width: 140px;margin: 5px;" v-model="scope.row.orderGiveMaxQty" :precision="0" :min="0" @change="valueChange(scope.row, 'orderGiveMaxQty', 'tempRadio3')" controls-position="right">
            </el-input-number>
          </template>
        </el-table-column>
        <el-table-column align="center" label="操作" width="100">
          <template slot-scope="scope">
            <el-button v-if="!disabled" type="text" @click="removeProduct(scope.row)">移除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <template slot="footer">
        <el-button type="normal" size="mini" @click="visible = false">关闭</el-button>
        <el-button type="primary" size="mini" @click="confirmProduct">确认</el-button>
      </template>
    </el-dialog>
  </div>
</template>
<script>
import selectProduct from './selectProduct.vue'
import multipartUpload from './multipartUpload.vue';
import { givePoolFullGiveTemplate, fullGiveImportCount } from '../../../api/market/mzPromotion'
export default {
  components: {
    selectProduct,
    multipartUpload
  },
  props: {
    value: {
      default: () => [],
      type: Array,
    },
    title: {
      type: String,
      default: ''
    },
    type: {     //1: 赠品池    0: 赠品
      type: Number,
      default: 0
    },
    disabled: {
      type: Boolean,
      default: false
    },
    onlyChangeTotalNum: {
      type: Boolean,
      default: false
    }
  },
  mounted() {
    console.log(666);

    fullGiveImportCount({productType: this.type == 0 ? 2 : 3}).then(res => {
      if (res.code == 1000) {
        this.maxCount = res.data.importLimit;
      } else {
        this.$message.error(res.msg);
      }
    })
  },
  data() {
    return {
      temp: true,
      visible: false,
      list: [],
      maxCount: 10,
      selection: []
    }
  },
  methods: {
    show() {
      this.list = [];
      this.value.forEach(item => {
        item.tempRadio = item.giveQty == -1 ? 0 : 1;
        if (!Number.isNaN(item.orderGiveMaxQty)) {
          item.tempRadio3 = item.orderGiveMaxQty == -1 ? 0 : 1;
        }
        if (!Number.isNaN(item.orderGiveMinQty)) {
          item.tempRadio2 = item.orderGiveMinQty == -1 ? 0 : 1;
        }
        this.list.push({ ...item });
      })
      this.visible = true;
    },
    getMulitpartUploadList(rows) {

      this.list = [
        ...this.list,
        ...rows.map(item => {
          return {
            ...item,
            skuId: item.csuid,
            tempRadio: item.giveQty == -1 ? 0 : 1,
            tempRadio2: item.orderGiveMinQty == -1 ? 0 : 1,
            tempRadio3: item.orderGiveMaxQty == -1 ? 0 : 1,
          }
        })
      ];
      //this.masterSku = [...this.masterSku, ...rows.filter(item => !this.masterSku.some(val => val.id === item.id))]
    },
    valueChange(row, key, key2) {
      row[key] = (row[key] > 0 && row[key2]) || (row[key] > 0 && !key2) ? row[key] : 1
      if (key != "giveQty" && row.isSplit != 1 && row[key] % row.mediumPackageNum != 0) {
        this.$message.error("数量需为中包装数的倍数");
        this.temp = false;
        row[key] = row.mediumPackageNum;
        const timer = setTimeout(() => {
          this.temp = true;
          clearTimeout(timer)
        })

      }
    },
    confirmProduct() {
      this.$emit('input', this.list)
      this.$emit('confirmProduct', this.list)
      this.visible = false;
    },
    removeProduct(row) {
      this.list = this.list.filter(item => (item.id !== row.id) || (item.skuId !== row.skuId));
    },
    handleSelectionChange(row) {
      this.selection = row;
    },
    multipartDelete() {
      // this.list = this.list.filter(item => !this.selection.some(val => val.id == item.id))
      this.list = this.removeArrayElementsById(this.list, this.selection);
      this.$refs.table.clearSelection();
    },
    removeArrayElementsById(arrayA, arrayB) {
      const idsInB = new Set(arrayB.map(item => item.id || item.skuId));
      return arrayA.filter(item => !idsInB.has(item.id || item.skuId));
    },
    multipartUpload() {
      givePoolFullGiveTemplate()
    }
  }
}
</script>

<style scoped>
p {
  margin: 0;
}
::v-deep   .el-dialog {
  border-radius: 5px;
  overflow: hidden;
}
::v-deep   .el-dialog__header {
  padding: 15px;
  background-color: #f3f3f3;
}
::v-deep   .el-dialog__header span {
  font-size: 14px;
  line-height: normal;
}
::v-deep   .el-dialog__header button {
  right: 15px;
  top: 15px;
}
::v-deep   .el-dialog__body {
  padding: 5px 15px;
}
.keyValue {
  display: flex;
}
.keyValue span:first-child {
  width: 70px;
  flex-shrink: 0;
  text-align: justify;
  text-align-last: justify;
}
.keyValue span:last-child {
  display: -webkit-box;
  -webkit-box-orient: vertical;
	-webkit-line-clamp: 2;
  overflow: hidden;
	text-overflow: ellipsis;
}
</style>
