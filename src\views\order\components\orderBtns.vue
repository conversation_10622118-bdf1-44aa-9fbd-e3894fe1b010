<template>
  <div>
    <div class="btnBox">
      <div class="leftBtns">
        <el-button
          v-permission="['deal_order_viewShipment']"
          v-if="activeOrderStatus === '2' || activeOrderStatus === '3' || activeOrderStatus === '4' || activeOrderStatus === '91'"
          type="primary"
          size="mini"
          @click="deliverInfo"
        >查看备货信息</el-button>
        <el-button
          v-permission="['deal_order_batchImportLogistics']"
          v-if="((activeOrderStatus === '2' && shopConfig.isFbp !== 1) || activeOrderStatus === '33') &&  shopConfig.shopPatternCode !== 'ybm'"
          type="primary"
          size="mini"
          @click="importAndUpload('fastMail')"
        >批量导入快递信息</el-button>
        <el-button
          v-permission="['deal_order_uploadelEctronicInvoice']"
          v-if="((activeOrderStatus === '2' && shopConfig.isFbp !== 1) || (activeOrderStatus === '3' && shopConfig.isFbp !== 1) || activeOrderStatus === '33') && shopConfig.shopPatternCode !== 'ybm'"
          type="primary"
          size="mini"
          @click="uploadInvoice"
        >上传电子发票</el-button>
        <el-button
          v-permission="['deal_order_openAccount']"
          v-if="((activeOrderStatus === '10' || activeOrderStatus === '1' || activeOrderStatus === '7' || activeOrderStatus === 'qualificationRemindCount')&&shopConfig.isFbp!==1) && shopConfig.shopPatternCode !== 'ybm'"
          type="primary"
          size="mini"
          @click="openAccount"
        >开户</el-button>
        <el-button
          v-if="activeOrderStatus === '32' && shopConfig.shopPatternCode !== 'ybm'"
          type="primary"
          size="mini"
          @click="readyCompleteBatch"
        >批量备货完成
        <span  v-if="activeOrderStatus === '32' && shopConfig.shopPatternCode !== 'ybm'">
          <el-tooltip effect="dark" placement="top-end">
            <template #content>批量备货完成功能，默认按下单量备货。适用于无“待审核“、"待客户确认"、"退款成功”状态退款单的订单</template>
            <i class="el-icon-warning-outline"></i>
          </el-tooltip>
        </span>
        </el-button>
        <el-button
          v-if="activeOrderStatus === '32' && shopConfig.shopPatternCode !== 'ybm'"
          type="primary"
          size="mini"
          @click="readyComplete"
        >备货完成</el-button>
        <el-button
          v-permission="['deal_order_editMerchantRemark']"
          v-if="(activeOrderStatus === '10' || activeOrderStatus === '1' || activeOrderStatus === '7' || activeOrderStatus === 'qualificationRemindCount' || activeOrderStatus === '2' || activeOrderStatus === '3' || activeOrderStatus === '32' || activeOrderStatus === '33') && shopConfig.shopPatternCode !== 'ybm'"
          type="primary"
          size="mini"
          @click="editRemark"
        >编辑商家备注</el-button>
        <el-button
          v-if="activeOrderStatus === '33'"
          type="primary"
          size="mini"
          @click="stockInformation"
        >查看备货信息</el-button>
        <el-button
          v-if="activeOrderStatus === '33' && shopConfig.shopPatternCode !== 'ybm'"
          type="primary"
          size="mini"
          @click="logisticsDelivery"
        >发物流快递</el-button>
        <el-button
            v-permission="['deal_order_electronicTransferAudit']"
            v-if="activeOrderStatus === '10' && shopConfig.shopPatternCode !== 'ybm'"
            type="primary"
            size="mini"
            @click="tReview"
        >
          订单电汇审核
        </el-button>
        <!-- <el-button
           v-permission="['deal_order_shipment']"
           v-if="activeOrderStatus === '7' || || activeOrderStatus === 'qualificationRemindCount'"
           type="primary"
           size="mini"
           @click="deliverGoods"
         >发货</el-button> -->
        <el-button
          v-permission="['deal_order_audit']"
          v-if="(activeOrderStatus === '1'&&shopConfig.isFbp!==1) && shopConfig.shopPatternCode !== 'ybm'"
          type="primary"
          size="mini"
          @click="toExamine"
        >审核通过</el-button>
        <el-button
          v-permission="['deal_order_refund']"
          v-if="(activeOrderStatus === '1' || activeOrderStatus === '7' || activeOrderStatus === 'qualificationRemindCount' || activeOrderStatus === '2' || activeOrderStatus === '3' || activeOrderStatus === '32' || activeOrderStatus === '33' || activeOrderStatus === '91') && shopConfig.shopPatternCode !== 'ybm' && shopConfig.isFbp !== 1"
          type="primary"
          size="mini"
          @click="toRefund"
        >发起退款</el-button>
        <el-button
          v-permission="['deal_order_deliveryFinish']"
          v-if="activeOrderStatus === '2' && showConfirmFinishButton"
          type="primary"
          size="mini"
          @click="toSuccess"
        >批量配送完成</el-button>
        <el-button
          v-permission="['deal_order_againPushErp']"
          v-if="((activeOrderStatus === '1' || activeOrderStatus === '7' || activeOrderStatus === 'qualificationRemindCount')&&shopConfig.isFbp!==1) && shopConfig.shopPatternCode !== 'ybm'"
          type="primary"
          size="mini"
          @click="pushDownAgain"
        >重新下推ERP</el-button>
        <el-button v-if="(activeOrderStatus === '7' || activeOrderStatus === 'qualificationRemindCount' &&shopConfig.isFbp!==1) && shopConfig.shopPatternCode !== 'ybm'" type="primary" size="mini" @click="isOrder">开单完成</el-button>
        <el-button
          v-if="(activeOrderStatus === '33' || activeOrderStatus === '2' || activeOrderStatus === '3' || activeOrderStatus === '91') && shopConfig.shopPatternCode !== 'ybm'"
          type="primary"
          size="mini"
          @click="handleEditPartialShipment"
        >
          部分发货批量处理
        </el-button>
        <el-button
          v-if="(activeOrderStatus === '1' || activeOrderStatus === '7' || activeOrderStatus === 'qualificationRemindCount' || activeOrderStatus === '2' || activeOrderStatus === '3' || activeOrderStatus === '32' || activeOrderStatus === '33') && shopConfig.shopPatternCode !== 'ybm'"
          v-permission="['deal_order_refund']"
          type="primary"
          size="mini"
          @click="toBatchRefund"
        >
          批量发起退款
        </el-button>
        <!-- 暂时隐藏 v-if="shopConfig.shopPatternCode !== 'ybm'" -->
        <el-button
        v-if="shopConfig.shopPatternCode !== 'ybm'"
          v-permission="['deal_order_exportList']"
          type="primary"
          size="mini"
          @click="exportOrder('1')"
        >
          导出订单列表
        </el-button>
        <!-- 暂时隐藏 v-if="shopConfig.shopPatternCode !== 'ybm'" -->
        <el-button
        v-if="shopConfig.shopPatternCode !== 'ybm'"
          v-permission="['deal_order_exportDetail']"
          type="primary"
          size="mini"
          @click="exportOrder('2')"
        >
          导出订单明细
        </el-button>
      </div>
      <!-- <div class="rightBtns">
        <el-button
          v-permission="['deal_order_exportList']"
          type="primary"
          size="mini"
          @click="exportOrder('1')"
        >
          导出订单列表
        </el-button>
        <el-button
          v-permission="['deal_order_exportDetail']"
          type="primary"
          size="mini"
          @click="exportOrder('2')"
        >
          导出订单明细
        </el-button>
      </div> -->
    </div>
    <import-upload
      v-if="importUploadVisible"
      :import-upload-visible="importUploadVisible"
      :dialog-type="dialogType"
      :order-no="(selectRows[0] || {}).orderNo"
      @cancelDialog="cancelDialog(1)"
      @getList="$emit('refreshList')"
    />
    <editDialog
      v-if="editVisible"
      :dialog-edit-type="dialogEditType"
      :edit-visible="editVisible"
      :erp-name="merchantName"
      :merchant-status="merchantStatus"
      :merchant-id="merchantId"
      :order-no="selectRows.legnth <= 1 ? (selectRows[0] || {}).orderNo : selectRows.map((item) => item.orderNo).join(',')"
      :remark-str="selectRows.legnth <= 1 ? (selectRows[0] || {}).sellerRemark : ''"
      @closeDialog="cancelDialog(2)"
      @gitList="$emit('refreshList')"
    />
    <logInfoDialog ref="logInfoDialog" />
    <el-dialog title="温馨提示" :visible="examineVisible" width="60%" @close="cancelDialog(1)">
      <span>若已对接ERP，系统将自动审核通过并将订单下推至ERP，无需人工审核。请及时关注订单“下推ERP”状态，若下发失败请及时处理。</span>
      <br />
      <br />
      <span>确认人工审核通过吗？</span>
      <span slot="footer">
        <el-button size="mini" @click="cancelDialog(1)">取 消</el-button>
        <el-button type="primary" size="mini" @click="confirmExamine">确 定</el-button>
      </span>
    </el-dialog>
    <el-dialog title="订单电汇审核" :visible="tReviewDialog" width="60%" @close="cancelDialog(1)">
      <span style="color: red">请核对您的收款账户已收到客户打款，并确认电汇金额与订单实付金额一致！</span>
      <div>
        <p v-if="selectRows[0]">客户名称：{{ (selectRows[0].merchantInfo || {}).merchantName }}</p>
        <p v-if="selectRows[0]">订单编号：{{selectRows[0].orderNo}}</p>
        <p v-if="selectRows[0]">订单实付金额：{{selectRows[0].money}}</p>
      </div>
      <div class="demo-image__preview" v-if="evidenceUrlListUrl && evidenceUrlListUrl.length > 0">
        <el-image
          v-for="(item) in evidenceUrlListUrl"
          style="width: 100px; height: 100px;margin-right: 10px"
          :src="item"
          :preview-src-list="evidenceUrlListUrl"
        ></el-image>
      </div>
      <span slot="footer">
        <el-button size="mini" @click="cancelDialog(1)">关 闭</el-button>
        <el-button type="primary" size="mini" :loading="tReviewC" @click="confirmTReview">确认已打款</el-button>
      </span>
    </el-dialog>
    <export-tip
      :change-export="changeExport"
      @handleExoprClose="handleExoprClose"
      @handleChangeExport="handleChangeExport"
    />
    <logisticsDelivery-dialog
      v-if="logisticsDeliveryDialogVisible"
      :dialog-visible="logisticsDeliveryDialogVisible"
      :merchant-id="(selectRows[0].merchantInfo || {}).merchantId"
      :order-no="(selectRows[0] || {}).orderNo"
      :merchant-name="(selectRows[0].merchantInfo || {}).merchantName"
      @cancelDialog="cancelDialog(1)"
      @gitList="$emit('refreshList')"
    />
    <ExportDialog
      v-if='exportDialogVisible'
      :exportDialogVisible.sync='exportDialogVisible'
      :exportType="exportType"
      :tabData='tabData'
      @confirm='ExportDialogConfirm'
    />
    <el-dialog
      title="批量发起退款"
      :visible="batchRefounVis"
      width="50%"
      @close="batchRefounVis = false"
    >
      <div>
        <el-form
          ref="form"
          size="small"
          class="editDialog"
        >
          <el-form-item
            label="退款订单数量"
          >
            <span style="color: red">{{ batchRefoundOrdersInfo.orderNum }}</span>
          </el-form-item>
          <el-form-item
            label="退款金额合计"
          >
            <span style="color: red">{{ batchRefoundOrdersInfo.totalAmountStr }}</span>
          </el-form-item>
          <el-form-item
            label="退款原因"
            :rules="[
              { required: true, message: '请选择'},
            ]"
          >
            <el-select
              v-model.trim="batchRefoundOrdersInfo.refundReason"
              placeholder="请选择"
              style="width: 100%"
            >
              <el-option
                v-for="(item, index) in quickRemarkList"
                :key="index"
                :label="item.showText"
                :value="item.showText"
              />
            </el-select>
          </el-form-item>
          <el-form-item
            label="退款说明"
            :rules="[
              { required: true, message: '请填写'},
            ]"
          >
            <el-input
              v-model="batchRefoundOrdersInfo.refundExplain"
              type="textarea"
              :autosize="{ minRows: 2, maxRows: 4}"
              placeholder="请输入内容"
              maxlength="200"
              show-word-limit
            />
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer">
        <el-button size="mini" @click="batchRefounVis = false">取消</el-button>
        <el-button type="primary" size="mini" :loading="batchRefoundLoading" @click="handleBatchRefound">提交</el-button>
      </span>
    </el-dialog>
    <el-dialog
      :visible.sync="hasUrgeDeliveryVisible"
      width="30%"
      :showClose="false"
    >
      <div class="urgeDeliveryDialog">
        <div class="icon">
          <i class="el-icon-warning-outline" style="color: #e6a23c;font-size: 18px"></i>
        </div>
        <div class="text">
          {{errWaringMsg}}
          <p v-if="false">订单号：YBM12456856535656</p>
        </div>
      </div>
      <el-row>
        <el-col :offset="16" :span="4">
          <el-button type="primary" @click="hasUrgeDeliveryVisible = false">
            确认
          </el-button>
        </el-col>
      </el-row>
    </el-dialog>

    <el-dialog
      :visible.sync="hasRefundOrderVisible"
      width="30%"
      :showClose="false"
    >
      <div class="urgeDeliveryDialog">
        <div class="icon">
          <i class="el-icon-warning-outline" style="color: #e6a23c;font-size: 18px"></i>
        </div>
        <div class="text">
          {{hasRefundMsg}}
        </div>
      </div>
      <el-row>
        <el-col :offset="16" :span="4">
          <el-button type="primary" @click="hasRefundOrderVisible = false">
            确认
          </el-button>
        </el-col>
      </el-row>
    </el-dialog>
  </div>
</template>
<script>
import { getIsTrialMerchant, againIssued, updateOrderStatus,bathUpdateOrderStatusWaitDelivery,exportOrder, confirmOrderFinish, confirmReceipt, apiUpdateOrderStatusSorting, apiBatchValidDelivery, getBatchRefundOrderSummary, batchSubmitRefundApplyBySeller, listRefundReason,getIsRefund } from '@/api/order/index';
import exportTip from '@/views/other/components/exportTip';
import { mapState } from 'vuex';
import importUpload from './ImportUpload';
import editDialog from './editDialog.vue';
import logInfoDialog from './logInfoDialog.vue';
import logisticsDeliveryDialog from './logisticsDeliveryDialog.vue';
import ExportDialog from './exportDialog';

export default {
  name: 'OrderBtns',
  components: { importUpload, editDialog, logInfoDialog, exportTip, logisticsDeliveryDialog, ExportDialog },
  props: {
    activeOrderStatus: {
      type: String,
      default: '',
    },
    selectRows: {
      type: Array,
      default: () => [],
    },
    ruleForm: {
      type: Object,
      default: () => {},
    },
    logisticsCompanyList: {
      type: Array,
      default: () => [],
    },
    showConfirmFinishButton: {
      type: Boolean,
      default: false,
    },
    tabData: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      batchRefoundLoading: false,
      quickRemarkList: [],
      batchRefounVis: false,
      batchRefoundOrdersInfo: {
        orderNum: '',
        merchantNum: '',
        totalAmountStr: '',
        refundReason: '',
        refundExplain: '',
      },
      dialogType: '',
      importUploadVisible: false,
      dialogEditType: '',
      editVisible: false,
      merchantName: '',
      merchantStatus: '',
      merchantId: null,
      orderNo: null,
      examineVisible: false,
      tipHeight: document.documentElement.clientHeight / 3,
      changeExport: false,
      tReviewDialog: false,
      tReviewC: false,
      evidenceUrlListUrl: [],
      logisticsDeliveryDialogVisible: false,
      exportDialogVisible: false,
      exportType: '',
      hasUrgeDeliveryVisible: false, // 有退款时的弹窗
      hasRefundOrderVisible: false, // 订单已经退过款的弹窗
      hasRefundMsg: "", // 退款信息
      errWaringMsg: "", // 错误提示信息
    };
  },
  computed: { ...mapState('app', ['shopConfig']) },
  methods: {
    handleBatchRefound() {
      if (!this.batchRefoundOrdersInfo.refundReason) {
        this.$message.warning({ message: '请选择退款原因', offset: this.tipHeight });
        return false;
      }
      if (!this.batchRefoundOrdersInfo.refundExplain) {
        this.$message.warning({ message: '请填写退款说明', offset: this.tipHeight });
        return false;
      }
      batchSubmitRefundApplyBySeller({
        orderNos: this.selectRows.map(item => item.orderNo).join(','),
        refundReason: this.batchRefoundOrdersInfo.refundReason,
        refundExplain: this.batchRefoundOrdersInfo.refundExplain,
      }).then((res) => {
        this.batchRefounVis = false;
        if (res.code === 0) {
          const { result } = res;
          const str = `
            <div>
              <p>本次操作批量订单：<span style="color: red">${result.totalNum}笔</span></p>
              <p><span style="color: red">${result.successNum}笔发起成功：</span>商家主动发起的退款申请，需要客户在APP或PC确认同意后才能完成退款，请及时联系客户确认退款</p>
              <p><span style="color: red">${result.failureNum}笔发起失败：</p>
              <div>${result.failureItems && result.failureItems.length ? result.failureItems.map(item => `<p>订单号：${item.orderNo}，失败原因：${item.failureReason}</p>`).join('') : ''}</div>
            </div>
          `;
          this.$confirm(str, '批量发起退款结果通知', {
            dangerouslyUseHTMLString: true,
            confirmButtonText: '确定',
            cancelButtonText: '',
          }).then(() => {
            this.$emit('refreshList');
          }).catch(() => {});
        } else {
          this.$message.error(res.msg);
        }
      });
    },
    handleEditPartialShipment() {
      const pass = this.handleCheck('batch');
      if (pass) {
        this.$parent.handleEditPartialShipment(this.selectRows, 'batch');
      }
    },
    cancelDialog(from) {
      if (from === 1) {
        this.importUploadVisible = false;
        this.examineVisible = false;
        this.tReviewDialog = false;
        this.logisticsDeliveryDialogVisible = false;
      } else {
        this.dialogEditType = '';
        this.editVisible = false;
        this.merchantId = null;
        this.orderNo = null;
      }
    },
    handleCheck(type) {
      if (this.selectRows.length === 0) {
        this.$message.warning({ message: '请选择需要操作的订单', offset: this.tipHeight });
        return false;
      }
      if (this.selectRows.length > 1 && !type) {
        this.$alert('此功能不支持批量操作', '温馨提示', {
          confirmButtonText: '确定',
          callback: () => {},
        });
        // this.$message.warning('此功能不支持批量操作');
        return false;
      }
      return true;
    },
    deliverInfo() {
      const pass = this.handleCheck();
      if (pass) {
        this.$emit('setLocal');
        const query = {
          orderNo: this.selectRows[0].orderNo,
          merchantId: this.selectRows[0].merchantInfo.merchantId,
          detail: true,
          activeOrderStatus: this.activeOrderStatus,
          canEdit: true,
        };
        if (this.shopConfig.isFbp === 1) {
          delete query.canEdit;
        }
        this.$router.push({
          path: '/orderList/delivery',
          query,
        });
        // this.$refs.logInfoDialog.getListData(this.selectRows[0].orderNo);
      }
    },
    // 查看备货信息
    stockInformation() {
      // TODO 判断是否满足条件
      const pass = this.handleCheck();
      if (pass) {
        this.$emit('setLocal');
        const query = { orderNo: this.selectRows[0].orderNo, merchantId: this.selectRows[0].merchantInfo.merchantId, detail: true, stockInformation: true, canEdit: true };
        if (this.shopConfig.isFbp === 1) {
          delete query.canEdit;
        }
        this.$router.push({ path: '/orderList/delivery', query });
      }
    },
    importAndUpload(type) {
      this.dialogType = type;
      this.importUploadVisible = true;
    },
    uploadInvoice() {
      const pass = this.handleCheck();
      if (pass) {
        this.importAndUpload('invoice');
      }
    },
    openAccount() {
      const pass = this.handleCheck();
      if (pass) {
        let noOpened = true;
        if (this.selectRows.length > 1) {
          this.$alert('此功能不支持批量操作', '温馨提示', {
            confirmButtonText: '确定',
            callback: () => {},
          });
          // this.$message.warning('此功能不支持批量操作');
          return false;
        }
        this.selectRows.forEach((item) => {
          if ((item.merchantInfo || {}).merchantStatus === 1) {
            noOpened = false;
          }
        });
        if (!noOpened) {
          this.$message.warning({ message: '请选择未开户客户进行开户', offset: this.tipHeight });
        } else {
          this.editVisible = true;
          this.dialogEditType = 'editAdd';
          this.merchantName = this.selectRows[0].merchantInfo.merchantName;
          this.merchantId = this.selectRows[0].merchantInfo.merchantId;
          this.merchantStatus = this.selectRows[0].merchantInfo.merchantStatus;
        }
      }
    },
    editRemark() {
      const pass = this.handleCheck('batch');
      if (pass) {
        if (this.selectRows.length > 50) {
          this.$alert('此功能最多支持修改50条数据', '温馨提示', {
            confirmButtonText: '确定',
            callback: () => {},
          });
          return false;
        }
        this.editVisible = true;
        this.dialogEditType = 'editTip';
      }
    },
    // deliverGoods() {
    //   const pass = this.handleCheck();
    //   if (pass) {
    //     validDelivery({ orderNo: this.selectRows[0].orderNo, merchantId: this.selectRows[0].merchantInfo.merchantId }).then((res) => {
    //       if (res.code === 0) {
    //         this.$emit('setLocal');
    //         this.$router.push({ path: '/orderList/delivery', query: { orderNo: this.selectRows[0].orderNo, merchantId: this.selectRows[0].merchantInfo.merchantId } });
    //       } else {
    //         this.$alert(res.msg, '温馨提示', {
    //           confirmButtonText: '确定',
    //           callback: () => {},
    //         });
    //       }
    //     });
    //   }
    // },
    // 审核
    toExamine() {
      if (this.selectRows.length === 0) {
        this.$message.warning({ message: '请选择需要操作的订单', offset: this.tipHeight });
        return;
      }
      let pass = true;
      let hasRefund = false;
      this.selectRows.forEach((item) => {
        if ((item.merchantInfo || {}).merchantStatus === 0) {
          pass = false;
        }
        if (item.whetherRefund) {
          hasRefund = true;
        }
      });
      if (!pass) {
        this.$message.error({ message: '所选数据包含未开户客户，请先开户', offset: this.tipHeight });
      } else if (hasRefund) {
        this.$message.warning({ message: '当前订单有退款申请，请先处理退款申请', offset: this.tipHeight });
      } else {
        this.examineVisible = true;
      }
    },
    confirmExamine() {
      updateOrderStatus({ orderNos: this.selectRows.map(i => i.orderNo) }).then((res) => {
        if (res.code === 0) {
          this.$message.success({ message: '审核成功', offset: this.tipHeight });
          this.examineVisible = false;
          setTimeout(() => {
            this.$emit('refreshList');
          }, 500);
        } else {
          this.$message.error(res.message);
        }
      });
    },
    // 正在开单
    isOrder() {
      if (this.selectRows.length === 0) {
        this.$message.warning({ message: '请选择需要操作的订单', offset: this.tipHeight });
        return;
      }
      const params = {
        orderNoStr: this.selectRows.map(item => item.orderNo).join(','),
        merchantIdStr: this.selectRows.map(item => item.merchantInfo.merchantId).join(','),
      };
      apiBatchValidDelivery(params).then((res) => {
        if (res.code === 0) {
          // 判断是否满足条件，变更状态
          const str = `<p>若已对接ERP，ERP系统开单成功后会通知平台，平台订单会自动开单完成，无需人工“开单完成”。<br>确认人工开单完成吗？</p>`
          this.$confirm(str, '温馨提示', {
            dangerouslyUseHTMLString: true,
            confirmButtonText: '确定',
            cancelButtonText: '取消',
          }).then(() => {
            this.confirmOrder(params);
          }).catch(() => {});
        } else {
          this.$message.error(res.msg);
        }
      });
    },
    // 确认开单
    confirmOrder(params) {
      apiUpdateOrderStatusSorting(params).then((res) => {
        if (res.code === 0) {
          setTimeout(() => {
            this.$emit('refreshList');
          }, 500);
        } else {
          this.$message.error(res.msg);
        }
      });
    },
    // 备货完成
    readyComplete() {
      // TODO 判断是否满足条件
      const pass = this.handleCheck();
      if (pass) {
        const params = {
          orderNoStr: this.selectRows.map(item => item.orderNo).join(','),
          merchantIdStr: this.selectRows.map(item => item.merchantInfo.merchantId).join(','),
        };
        apiBatchValidDelivery(params).then((res) => {
          if (res.code === 0) {
            // 判断是否满足条件，变更状态
            this.$emit('setLocal');
            this.$router.push({ path: '/orderList/delivery', query: { orderNo: this.selectRows[0].orderNo, merchantId: this.selectRows[0].merchantInfo.merchantId, readyComplete: true } });
          } else {
            this.$message.error(res.msg);
          }
        });
      }
    },
    // 批量备货完成
    readyCompleteBatch() {
      if (this.selectRows && this.selectRows.length > 0) {
        const h = this.$createElement;
        this.$msgbox({
          title: '',
          message: h('p', null, [
            h('span', null, '批量备货默认按 '),
            h('span', { style: 'color: red' }, '下单量备货'),
            h('span', null, ',您一共勾选了'),
            h('span', { style: 'color: red' }, `${this.selectRows.length}个订单`),
            h('span', null, ',请确认?'),
          ]),
          showCancelButton: true,
          confirmButtonText: '确认备货完成',
          cancelButtonText: '取消',
        }).then(action => {
          const params = {
            orderNoList: this.selectRows.map(item => item.orderNo),
            // merchantIdStr: this.selectRows.map(item => item.merchantInfo.merchantId).join(','),
          };
          console.log(this.selectRows,'this.selectRows');
          bathUpdateOrderStatusWaitDelivery(params.orderNoList).then((res) => {
            if (res.code === 0) {
              if(res.result.failVoList.length > 0){
                this.$msgbox({
                  title: '',
                  message: h('p', null, [
                   res.result.failVoList.map(item=>h('div', null, `${item.orderNo}订单备货失败，${item.failReason}`)),
                  ]),
                  confirmButtonText: '我知道了',
                  customClass: 'customMsgboxY', // 自定义类名
                }).then(action => {
                  this.$emit('refreshList');
                })
              }else{
                this.$message.success('备货成功');
                this.$emit('refreshList');
              }
              
            } else {
              this.$message.error(res.msg);
            }
          });
        }).catch(action=>{
        })
      }
    },
    // 发物流快递
    logisticsDelivery() {
      const pass = this.handleCheck();
      if (pass) {
        this.logisticsDeliveryDialogVisible = true;
      }
    },
    toBatchRefund() {
      const pass = this.handleCheck('batch');
      if (pass) {
        if (this.selectRows.length > 50) {
          this.$alert('此功能最多支持修改50条数据', '温馨提示', {
            confirmButtonText: '确定',
            callback: () => {},
          });
          return false;
        }
        listRefundReason({ orderNo: this.selectRows[0].orderNo }).then((res) => {
          if (res.code === 0) {
            this.quickRemarkList = res.result;
          }
        });
        let orderNos = this.selectRows.map(item => item.orderNo).join(',')
        // this.isUrgeDelivery(orderNos) 查看有无催发货订单
        getBatchRefundOrderSummary({ orderNos }).then((res) => {
          if (res.code === 0) {
            Object.keys(this.batchRefoundOrdersInfo).forEach((key) => {
              this.batchRefoundOrdersInfo[key] = res.result[key];
            });
            const h = this.$createElement;
            this.$confirm('批量发起退款', {
              title: '批量发起退款',
              message: h('div', [
                h(
                  'p',
                  { style: 'color: red' },
                  '批量发起退款仅支持整单退款，请提前确认！',
                ),
                h(
                  'span',
                  '本次批量发起退款共选择了',
                ),
                h(
                  'span',
                  { style: 'color: red' },
                  `${this.batchRefoundOrdersInfo.orderNum}`,
                ),
                h(
                  'span',
                  '笔订单、涉及',
                ),
                h(
                  'span',
                  { style: 'color: red' },
                  `${this.batchRefoundOrdersInfo.merchantNum}`,
                ),
                h(
                  'span',
                  '个客户，请仔细核对是否确认退款？',
                ),
              ]),
              confirmButtonText: '确认发起',
              cancelButtonText: '取消',
            })
              .then(() => {
                this.batchRefounVis = true;
              });
          } else {
            this.hasRefundMsg = res.msg
            this.hasRefundOrderVisible = true
            // this.$message.error(res.msg);
          }
        });
      }
    },
    isUrgeDelivery(orderNo) {
      // console.log(typeof orderNo)
      this.hasUrgeDeliveryVisible = true
      return true
    },
    toRefund() {
      const pass = this.handleCheck();
      const orderNo = this.selectRows[0].orderNo
      const params = {orderNo}
      if (pass) {
        getIsRefund(params).then(res => {
          if(res.code === 0) {
            this.$emit('setLocal');
            getIsTrialMerchant().then((res) => {
              if (res.code == 0) {
                if (res.result.isTrialMerchant) this.$router.push({ path: '/orderList/refundNew', query: { orderNo: this.selectRows[0].orderNo, merchantId: this.selectRows[0].merchantInfo.merchantId, activeOrderStatus: this.activeOrderStatus } });
                else this.$router.push({ path: '/orderList/refund', query: { orderNo: this.selectRows[0].orderNo, merchantId: this.selectRows[0].merchantInfo.merchantId } });
              }
            })
          }else if(res.code === 1) {
            this.errWaringMsg = res.msg
            this.hasUrgeDeliveryVisible = true
          }else {
            this.$message.error(res.msg || res.errMsg || "服务异常")
          }
        })
      }
    },
    pushDownAgain() {
      if (this.selectRows.length === 0) {
        this.$message.warning({ message: '请选择需要操作的订单', offset: this.tipHeight });
        return;
      }
      // let pass = true;
      // this.selectRows.forEach((item) => {
      //   if (item.erpPushDownStatus !== 2) {
      //     pass = false;
      //   }
      // });
      // if (!pass) {
      //   this.$message.error({ message: '所选数据只能为下推ERP失败的订单，请重新选择', offset: this.tipHeight });
      // } else {
      //   againIssued({ orderNos: JSON.stringify(this.selectRows.map(v => v.orderNo)) }).then((res) => {
      //     if (res.code === 0) {
      //       this.$message.success({ message: '重新下推成功', offset: this.tipHeight });
      //       setTimeout(() => {
      //         // 刷新列表
      //         this.$emit('refreshList');
      //       }, 500);
      //     } else {
      //       this.$message.error(res.msg);
      //     }
      //   });
      // }
      againIssued({ orderNos: JSON.stringify(this.selectRows.map(v => v.orderNo)) }).then((res) => {
        if (res.code === 0) {
          this.$message.success({ message: '重新下推成功', offset: this.tipHeight });
          setTimeout(() => {
            // 刷新列表
            this.$emit('refreshList');
          }, 500);
        } else {
          this.$message.error(res.msg);
        }
      });
    },
    getParams(params) {
      let queryStr = '?';
      Object.keys(params).forEach((key) => {
        queryStr += `${key}=${params[key]}&`;
      });
      queryStr = queryStr.substr(0, queryStr.length - 1);
      return queryStr;
    },
    ExportDialogConfirm(status) {
      const params = {
        ...this.ruleForm,
        orderIds: this.selectRows.map(i => i.id),
      };
      const type = this.exportType;
      params.type = this.exportType;
      params.exportStatusList = JSON.stringify(status);
      params.provinceCodes = JSON.stringify(params.provinceCodes);
      delete params.status;
      if (this.selectRows.length) {
        exportOrder({ orderIds: this.selectRows.map(i => i.id).join(','), type }).then((res) => {
          if (res.code !== 0) {
            this.$message.warning(res.msg);
            return;
          }
          this.changeExport = true;
        });
      } else {
        // 导出规则：1.筛选项只有订单号 || 2.必须有下单时间且时间跨度不超过31*6=186天即6个月
        const ruleFormValue = Object.values(this.ruleForm).filter(i => i && String(i).trim());
        const timeRule = this.ruleForm.orderCreateTimeStart && this.ruleForm.orderCreateTimeEnd && (this.ruleForm.orderCreateTimeEnd - this.ruleForm.orderCreateTimeStart) < 31 * 6 * 24 * 60 * 60 * 1000;
        if ((ruleFormValue.length === 2 && this.ruleForm.orderNo) || timeRule) {
          // 可以导出
          exportOrder(params).then((res) => {
            if (res.code !== 0) {
              this.$message.warning(res.msg);
              return;
            }
            this.changeExport = true;
          });
        } else {
          this.$message.error({ message: '目前只允许导出下单时间跨度在6个自然月内的订单，请先选择下单时间', offset: this.tipHeight });
        }
      }
    },
    exportOrder(type) {
      this.exportType = type;
      this.exportDialogVisible = true;
    },
    handleChangeExport(info) {
      this.changeExport = false;
      if (info === 'go') {
        const path = '/downloadList';
        window.openTab(path);
        // that.$router.push({ path: '/downloadList' });
      }
    },
    handleExoprClose() {
      this.changeExport = false;
    },
    toSuccess() {
      if (this.selectRows.length === 0) {
        this.$message.warning({ message: '请选择需要操作的订单', offset: this.tipHeight });
        return;
      }
      let pass = 0;
      let empty = true;
      this.selectRows.forEach((item) => {
        if (!item.logisticsWayDesc) {
          empty = false;
        }
        console.log(item.logisticsWayDesc, 'item.logisticsWayDesc');
        if (item.logisticsWayDesc && this.logisticsCompanyList.indexOf(item.logisticsWayDesc) > -1) {
          pass += 1;
        }
      });
      console.log(pass, 'ppp');
      // if (pass !== this.selectRows.length || !empty) {
      //   this.$alert('只允许对商家“自配送”的订单确认配送完成，请先查看订单的配送方式', '温馨提示', {
      //     confirmButtonText: '确定',
      //     callback: () => {},
      //   });
      //   return;
      // }
      const h = this.$createElement;
      this.$confirm('温馨提示', {
        title: '温馨提示',
        message: h('div', [
          h(
            'p',
            '请核实确认勾选订单已配送完成？确认配送配送则订单状态变成已完成',
          ),
          h(
            'p',
            '以下场景，订单不允许配送完成',
          ),
          h(
            'p',
            '1、订单存在审核中退款单，请在审核退款完成后重试',
          ),
          h(
            'p',
            '2、三方物流订单，发货时间+7天内不允许配送完成。请在发货时间7天后重试',
          ),
          h(
            'p',
            '3、三方物流订单，未获取到物流轨迹。请检查物流公司和物流单号是否正确，如填写错误请修改后重试',
          ),
          h(
            'p',
            '4、三方物流订单，物流轨迹异常（运单揽收时间或签收时间<单据支付时间）。请检查物流公司和物流单号是否正确，如填写错误请修改后重试',
          ),
        ]),
        confirmButtonText: '确定',
        cancelButtonText: '取消',
      }).then(() => {
        const orderNoary = [];
        this.selectRows.map((item) => {
          orderNoary.push(item.orderNo);
        });
        confirmOrderFinish({ orderNos: orderNoary.join(',') }).then((res) => {
          if (res.code === 0) {
            this.$message.success({ message: '设置成功', offset: this.tipHeight });
            if (res.result && res.result.length) {
              this.$confirm('配送完成结果通知', {
                title: '配送完成结果通知',
                message: h('div', [
                  h(
                    'p',
                    { style: 'color: red' },
                    '以下订单执行失败，请参考下方原因处理后重新确认配送完成',
                  ),
                  h(
                    'p',
                    { style: 'word-wrap: break-word; word-break: normal;' },
                    `${res.result.join(',')}`,
                  ),
                  h(
                    'p',
                    { style: 'color: red;margin-top: 20px' },
                    '以下场景，订单不允许配送完成',
                  ),
                  h(
                    'p',
                    '1、订单存在审核中退款单，请在审核退款完成后重试',
                  ),
                  h(
                    'p',
                    '2、三方物流订单，发货时间+7天内不允许配送完成。请在发货时间7天后重试',
                  ),
                  h(
                    'p',
                    '3、三方物流订单，未获取到物流轨迹。请检查物流公司和物流单号是否正确，如填写错误请修改后重试',
                  ),
                  h(
                    'p',
                    '4、三方物流订单，物流轨迹异常（运单揽收时间或签收时间<单据支付时间）。请检查物流公司和物流单号是否正确，如填写错误请修改后重试',
                  ),
                ]),
                confirmButtonText: '确定',
              });
            } else {
              this.$message.success({ message: '配送完成成功' });
            }
            setTimeout(() => {
              // 刷新列表
              this.$emit('refreshList');
            }, 500);
          } else {
            this.$message.error(res.msg);
          }
        });
      }).catch(() => {});
    },
    // 电汇审核
    tReview() {
      if (this.selectRows.length === 0) {
        this.$message.warning({ message: '请选择需要操作的订单', offset: this.tipHeight });
        return;
      }
      if (this.selectRows.length > 1) {
        this.$alert('订单电汇审核不支持批量操作', '温馨提示', {
          confirmButtonText: '确定',
          callback: () => {},
        });
        return false;
      }
      if (this.selectRows[0].payChannel !== 8) {
        this.$alert('只允许对“电汇商业”的线下转账订单进行电汇审核', '温馨提示', {
          confirmButtonText: '确定',
          callback: () => {},
        });
        return false;
      }
      this.tReviewDialog = true;
      if (this.selectRows[0].evidenceUrlList) {
        this.evidenceUrlListUrl = [];
        this.selectRows[0].evidenceUrlList.forEach((item) => {
          this.evidenceUrlListUrl.push(item);
        });
      }
    },
    // 确认汇款
    confirmTReview() {
      this.tReviewC = true;
      confirmReceipt({ orderNo: this.selectRows[0].orderNo }).then((res) => {
        this.tReviewC = false;
        if (res.code === 0) {
          this.$message.success('保存成功');
          this.tReviewDialog = false;
          setTimeout(() => {
            // 刷新列表
            this.$emit('refreshList');
          }, 500);
        } else {
          this.$message.error(res.message);
        }
      });
    },
  },
};
</script>
<style lang="scss" scoped>
.btnBox {
  margin: 15px 0;
  display: flex;
  justify-content: space-between;
  .leftBtns ::v-deep  .el-button {
    margin-right: 10px;
    margin-left: 0px;
    margin-bottom: 10px;
  }
}
.urgeDeliveryDialog {
  display: flex;
  .icon {
    margin-right: 10px;
  }
}
</style>
<style >
.customMsgboxY{
  width: 500px !important; /* 自定义宽度 */
}
</style>
