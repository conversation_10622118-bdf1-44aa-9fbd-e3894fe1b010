import request from '@/utils/request';
// 获取药店类型
export function getBusinessTypeList(params) {
    return request({
      url: '/getBusinessType',
      method: 'get',
      params,
    });
}

// 店铺供货对象-查看
export function getSelectedBusinessType(params) {
    return request({
      url: '/shopControl/supplyCustomerType',
      method: 'get',
      params,
    });
}
export function storeSupplyObject(params) {
    return request({
      url: '/shopControl/saveSupplyCustomerType',
      method: 'post',
      data: params,
    });
}