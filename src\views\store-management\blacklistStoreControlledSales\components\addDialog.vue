<template>
  <el-dialog
    title="添加黑名单"
    :visible.sync="dialogVisible"
    width="750px"
    :before-close="handleClose"
  >
    <SearchForm
      style="margin-bottom: 15px"
      ref="searchForm"
      :model="formModel"
      :form-items="formItems"
      :hasOpenBtn="false"
      @submit="handleFormSubmit"
      @reset="handleFormReset"
    />
    <xyyTable
      ref="productListTable"
      v-loading="tableLoading"
      :data="tableConfig.data"
      :col="tableConfig.col"
      :isPagination="false"
      @get-data="queryList"
      :hasSelection="true"
      @selectionCallback="selectionCallback"
    />
    <span slot="footer" class="dialog-footer">
    <el-button @click="handleClose">取 消</el-button>
    <el-button type="primary" @click="submit" :loading="submitLoading">确 定</el-button>
  </span>
  </el-dialog>
</template>

<script>
import SearchForm from '@/components/searchForm';
import { apiAllBuyers, apiAddBlackBuyer } from '@/api/storeManagement/blacklist';

export default {
  name: 'addDialog',
  components: {
    SearchForm
  },
  data() {
    return {
      dialogVisible: true,
      formModel: {
        buyerId: '',
        buyerName: ''
      },
      formItems: [
        {
          label: '药店ID',
          prop: 'buyerId',
          component: 'el-input',
          colSpan: 8,
          attrs: {
            placeholder: '请输入'
          }
        },
        {
          label: '药店名称',
          prop: 'buyerName',
          component: 'el-input',
          colSpan: 8,
          attrs: {
            placeholder: '请输入'
          }
        }
      ],
      tableLoading: false,
      tableConfig: {
        data: [],
        col: [
          {
            index: 'buyerId',
            name: '药店ID'
          },
          {
            index: 'erpCode',
            name: 'ERP编码'
          },
          {
            index: 'buyerName',
            name: '药店名称',
            width: 240
          },
          {
            index: 'accountStatusDesc',
            name: '开户状态'
          },
          {
            index: 'address',
            name: '药店地址',
            width: 260
          }
        ]
      },
      selectData: [],
      submitLoading: false
    };
  },
  methods: {
    handleClose() {
      this.$emit('update:addDialogVisible', false);
    },
    async queryList() {
      let params = { ...this.formModel };
      let flag = false;
      if (params) {
        Object.keys(params)
          .forEach(key => {
            if (params[key]) {
              flag = true;
            }
          });
      }
      if (!flag) {
        this.$message.warning('请输入完整的药店ID或药店名称再进行查询操作！');
        return false;
      }
      if (params.buyerId) {
        const patrn = /^[0-9]*$/;
        if (!patrn.test(params.buyerId)) {
          this.$message.warning('药店ID只能是数字');
          return false;
        }
      }
      this.tableLoading = true;
      try {
        const res = await apiAllBuyers(params);
        console.log(res);
        if (res && res.code === 0) {
          this.tableConfig.data = res.data;
        }
      } catch (err) {
        console.log(err);
      }
      this.tableLoading = false;
    },
    handleFormSubmit() {
      this.queryList();
    },
    handleFormReset() {
      Object.keys(this.formModel)
        .forEach(key => {
          this.formModel[key] = '';
        });
      this.tableConfig.data = []
    },
    async submit() {
      if (this.selectData.length > 0) {
        const idList = this.selectData.map(item => {
          return item.buyerId;
        });
        this.submitLoading = true;
        try {
          const res = await apiAddBlackBuyer(idList);
          if (res && res.code === 0) {
            this.$message.success('添加成功')
            this.$parent.queryList();
          } else {
            this.$message.error(res.message || '添加失败');
          }
          this.handleClose();
        } catch (e) {
          console.log(e);
        }
        this.submitLoading = false;
      } else {
        this.$message.warning('请选择需要添加的店铺');
      }
    },
    selectionCallback(list) {
      this.selectData = list;
    }
  }
};
</script>

<style scoped>

</style>
