import JSZip from 'jszip';
import { saveAs } from 'file-saver';
function checkHttp(url) {
    let type = "https:";
    type = location.protocol;
    let curl = null;
    if (url) {
        curl = url.split("://")[1];
    }
    return type + '//' + curl;
}
function getFile(url){
    url = checkHttp(url);
	return new Promise((resolve, reject) => {
		const xhr = new XMLHttpRequest();
		xhr.open('GET', url, true);
		xhr.responseType = 'blob';
		
		xhr.onload = function() {
		  if (xhr.status === 200) {
			resolve(xhr.response);
		  } else {
			reject(`Error: ${xhr.status}`);
		  }
		};
	
		xhr.onerror = function() {
		  reject("Network Error");
		};
	
		xhr.send();
	});
};

/**
 * 
 * @param {*} data 
 * @param {*} failCount 
 */
export function downloadZip(dataList, zipName, type = 1){ // type 是否需要按文件夹区分 1：不区分 2：区分
	const zip = new JSZip();  // 创建JSZip实例用于操作Zip文件
	const promises = [];  // 用于存储下载文件的Promise数组
	if (type == 1) {
		dataList.forEach(item => {
			const promise = getFile(item.url).then(data => {
				const suffix = item.url.split(".")[item.url.split(".").length - 1];
				zip.file(item.name + '.' + suffix, data, { binary: true });  // 将文件逐个添加到Zip文件
			});
			promises.push(promise);
		})
	} else if(type == 2) {
		dataList.forEach(item => {
			item.value.forEach(res => {
				const promise = getFile(res.url).then(data => {
					const suffix = res.url.split(".")[res.url.split(".").length - 1];
					zip.folder(item.name).file(res.name + '.' + suffix, data, { binary: true });  // 将文件逐个添加到Zip文件
				});
				promises.push(promise);
			})
		})
	}
	
	 // 等待所有文件处理完成
	 Promise.all(promises).then(() => {
		zip.generateAsync({type: "blob"}).then(function (content) {
            let time = new Date() * 1;
			saveAs(content, `${zipName}.zip`);
		})
	})
}
