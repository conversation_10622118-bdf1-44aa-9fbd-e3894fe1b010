<template>
  <div>
    <div class="rightBtns">
      <span style="color: #4184d5; cursor: pointer;margin-right:10px;" @click="descriptionChange">
        退款审批流程优化说明
      </span>
      <el-button
        v-permission="['deal_refund_exportList']"
        type="primary"
        size="mini"
        @click="exportAfterSale('1')"
        >导出退款单列表</el-button
      >
      <el-button
        v-permission="['deal_refund_exportDetail']"
        type="primary"
        size="mini"
        @click="exportAfterSale('2')"
        >导出退款单明细</el-button
      >
      <!-- {{ activeAfterSaleStatus }} -->
    </div>
    <el-table
      ref="cargoTable"
      v-loading="loading"
      :data="tableData"
      :row-key="(row) => row.refundOrderNo"
      default-expand-all
      style="width: 100%"
    >
      <el-table-column type="expand" width="1">
        <template slot-scope="props">
          <div>
            <div v-if="props.row.refundRemark" class="refundRemark">
              退款备注：
              <el-tooltip :content="props.row.refundRemark" placement="top-start">
                <span
                  >{{ (props.row.refundRemark || '').substr(0, 50)
                  }}{{ (props.row.refundRemark || '').length > 50 ? '...' : '' }}</span
                >
              </el-tooltip>
            </div>
            <div v-if="props.row.showChildren" style="padding: 20px">
              <after-sale-detail
                :detail-list="props.row.detailList"
                :active-after-sale-status="activeAfterSaleStatus"
              />
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="单据编号" prop="orderNo">
        <template slot-scope="scope">
          <div>退款单编号：</div>
          <div>{{ scope.row.refundOrderNo }}</div>
          <!-- 增加订单ID展示 -->
          <div>订单ID：{{ scope.row.orderId }}</div>
          <div>订单编号：</div>
          <div class="btnText" @click="jumpOrderPage(scope.row.orderNo,formatDate(scope.row.refundCreateTime, 'YMDHMS'))">
            {{ scope.row.orderNo }}
          </div>
        </template>
      </el-table-column>
      <!-- <el-table-column label="订单ID" prop="popOrderId"></el-table-column> -->
      <el-table-column label="申请日期" prop="refundCreateTime">
        <template slot-scope="scope">
          <div>
            {{
              {
                1: '用户发起',
                4: '商家发起',
                2: '平台发起',
                5: '系统发起'
              }[scope.row.refundChannel]
            }}
          </div>
          <div>
            {{
              {
                1: '部分退款',
                2: '全部退款'
              }[scope.row.refundType]
            }}
          </div>
          <!-- 增加退款类型展示-临时 -->
          <div>
            <!-- 退款类型展示-- -->
            {{
              {
                1: '退货退款',
                2: '仅退款',
                3: '退运费',
                4: '小额打款'
              }[scope.row.refundMode]
            }}
          </div>
          <div>{{ formatDate(scope.row.refundCreateTime, 'YMDHMS') }}</div>
        </template>
      </el-table-column>
      <el-table-column label="客户名称" prop="merchantName">
        <template slot-scope="scope">
          <div>{{ scope.row.provinceName }}</div>
          <div>{{ scope.row.merchantName }}</div>
          <div>{{ scope.row.mobile }}</div>
        </template>
      </el-table-column>
      <el-table-column label="退款金额" prop="refundFee">
        <template slot-scope="scope">
          <div v-if="scope.row.refundMode==4">
            营销服务额度赔偿：<span style="color: red">{{ scope.row.indemnityMoney }}</span>
          </div>
          <div v-else>
            <div>
              {{ scope.row.payTypeDesc }}
              <span v-if="scope.row.payChannelDesc">{{ '(' + scope.row.payChannelDesc + ')' }}</span>
            </div>
            <div v-if="scope.row.payChannel === 8">
              <el-button
                v-if="scope.row.evidenceUrlList && scope.row.evidenceUrlList.length > 0"
                type="text"
                @click="viewVoucher(scope.row)"
                >查看电汇凭证</el-button
              >
            </div>
            <div v-if="activeAfterSaleStatus !== '1'" style="color: red">
              <span v-if="scope.row.refundMode==4">{{ scope.row.indemnityMoney }}</span>
              <span v-else>{{ scope.row.refundFee }}</span>
            </div>
            <div v-if="activeAfterSaleStatus === '1'" style="color: red">
              <span v-if="scope.row.refundMode==4">{{ scope.row.indemnityMoney || '' }}</span>
              <span v-else-if="scope.row.refundActualFee">{{ scope.row.refundActualFee }}</span>
            </div>
            <div v-if="scope.row.freight" style="color: red">
              {{ '(包含运费金额：' + scope.row.freight + ')' }}
            </div>
            <!-- 增加额外赔偿和营销服务额度展示-临时 -->
            <div v-if="(scope.row.refundMode==1 || scope.row.refundMode==2) && scope.row.indemnityMoney != null && scope.row.indemnityMoney > 0">
              {{
                {
                  1: '营销服务额度',
                  2: '保证金',
                }[scope.row.indemnitySource]
              }}额外赔偿：<span style="color: red">{{ scope.row.indemnityMoney }}</span>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="退款原因" prop="refundReason">
        <template slot-scope="scope">
          <div>{{ scope.row.refundReason }}</div>
        </template>
      </el-table-column>
      <el-table-column label="退款状态" prop="adminAuditStatusName">
        <template slot-scope="scope">
          <div>
            {{ scope.row.adminAuditStatusName || '--' }}
            <i
              class="el-icon-search"
              style="color: #4183d5; cursor: pointer"
              @click="btnClick(scope.row, 'checkStatus')"
            />
          </div>
          <div :style="{ color: scope.row.payStatus === 3 ? 'red' : '#606266' }">
            银行退款状态：{{ scope.row.payStatusDesc }}
          </div>
          <div v-if="scope.row.payStatus === 3" style="color: red">请联系运营处理</div>
          <div v-if="activeAfterSaleStatus !== '0'">
            <span>物流信息</span>
            <i
              class="el-icon-search"
              style="color: #4183d5; cursor: pointer"
              @click="btnClick(scope.row, 'logisticsInformation')"
            />
            <!-- 增加编辑物流的图标展示-临时 -->
            <i
              v-if="activeAfterSaleStatus == 3 && scope.row.expressNo == null"
              class="el-icon-edit"
              style="color: #92939a; cursor: pointer"
              @click="editLogistics(scope.row)"
            />
          </div>
          <!-- 增加当前状态时间展示 -->
          <div>{{ formatDate(scope.row.updateTime, 'YMDHMS') }}</div>
          <div v-if="!!scope.row.invoice" class="btnText" @click="btnClick(scope.row, 'seePdf')">
            电子发票
          </div>
        </template>
      </el-table-column>
      <el-table-column label="备注(仅内部可见)" prop="">
        <template slot-scope="scope">
				  <el-button type="text" @click="editSellerRemark(scope.row)">备注</el-button>
          <p>{{ scope.row.sellerRemark }}</p>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="100">
        <template slot-scope="scope">
          <div class="btnText">
            <template v-if="shopConfig.isFbp === 1">
              <span
                v-permission="[
                  'deal_refund_customerAudit',
                  'deal_refund_storageAudit',
                  'deal_refund_financeAudit'
                ]"
                v-if="activeAfterSaleStatus === '0' && scope.row.interceptStatus !== -1"
                @click="btnClick(scope.row, 'ToExamine')"
                >审核</span
              >
            </template>
            <template v-else>
              <span
                v-permission="[
                  'deal_refund_customerAudit',
                  'deal_refund_storageAudit',
                  'deal_refund_financeAudit'
                ]"
                v-if="
                  (activeAfterSaleStatus === '0' ||
                    activeAfterSaleStatus === '3' ||
                    (activeAfterSaleStatus === '6' &&
                      (scope.row.payType === 1 || scope.row.payChannel === 8))) &&
                  shopConfig.shopPatternCode !== 'ybm'
                "
                @click="btnClick(scope.row, 'ToExamine')"
                >审核</span
              >
            </template>
          </div>
          <div class="btnText" @click="toogleExpandCargo(scope.row)">
            商品详情
            <i :class="scope.row.showChildren ? 'el-icon-arrow-up' : 'el-icon-arrow-down'" />
          </div>
          <!-- <div v-if="scope.row.evidenceList && scope.row.evidenceList.length > 0"> -->
          <div class="refundSty">
            <span style="cursor: pointer;" @click="btnClick(scope.row, 'showRefundVoucherDis')">{{ '退款凭证' }}</span>
            <count-down v-if="scope.row.merchantAuditDeadline" :time="scope.row.merchantAuditDeadline"/>
            <!-- <i
              class="el-icon-search"
              style="color: #4183d5; cursor: pointer"
              @click="btnClick(scope.row, 'refundVoucher')"
            /> -->
          </div>
        </template>
      </el-table-column>
    </el-table>
    <refund-log ref="refundLog" />
    <refund-form-approval
      ref="refundApproval"
      :edit-row="editRow"
      :active-after-sale-status="activeAfterSaleStatus"
      :isTrialMerchant="isTrialMerchant"
      @getNewList="$emit('refreshList')"
      @toRefundVoucher="showRefundVoucherDis = true"
    />
    <export-tip
      :change-export="changeExport"
      @handleExoprClose="handleExoprClose"
      @handleChangeExport="handleChangeExport"
    />
    <logistics-information
      v-if="dialogVisible"
      :dialog-visible="dialogVisible"
      :orderRefundId="orderRefundId"
      @cancelDialog="cancelDialog"
    />
    <el-image style="width: 0; height: 0" ref="previewImg" :preview-src-list="previewList">
    </el-image>
    <pdfDialog
      v-if="pdfVisible"
      :pdf-visible="pdfVisible"
      :order-no="(editRow || {}).orderNo"
      @closeDialog="cancelDialog()"
    />
    <!-- 编辑物流信息的弹框 -->
    <logisticsDialog ref="logisticsDialog"></logisticsDialog>
    <!-- 查看退款凭证的图片预览 -->
    <!-- <el-image-viewer v-if="showViewer" :url-list="srcList" :on-close="closeViewer" /> -->
    <myImageViewer
      v-if="showViewer"
      :url-list="srcList"
      :on-close="closeViewer"
      :initialIndex="initialIndex"
    ></myImageViewer>
    <editSellerRemark ref="editSellerRemark"></editSellerRemark>
    <refundVoucher v-model="showRefundVoucherDis" :rowData="editRow" @toReview="toReview"></refundVoucher>
  </div>
</template>

<script>
import {
  getListRefundDetail,
  refundListExport,
  refundListDetailsExport
} from '@/api/afterSale/index'
import exportTip from '@/views/other/components/exportTip'
import ElImageViewer from 'element-ui/packages/image/src/image-viewer'
import { mapState } from 'vuex'
import refundLog from './refundLog.vue'
import afterSaleDetail from './afterSaleDetail.vue'
import refundFormApproval from './refundFormApproval.vue'
import logisticsInformation from './logisticsInformation'
import pdfDialog from '../../order/invoicePdf.vue'
import logisticsDialog from './logisticsDialog.vue'
import editSellerRemark from './editSellerRemark.vue'

import myImageViewer from './myImgViewr.vue'
import countDown from './countDown.vue'
import refundVoucher from './refundVoucher.vue'

export default {
  name: 'TabComponent',
  components: {
    refundLog,
    ElImageViewer,
    afterSaleDetail,
    refundFormApproval,
    exportTip,
    logisticsInformation,
    pdfDialog,
    logisticsDialog,
    myImageViewer,
    editSellerRemark,
    countDown,
    refundVoucher
  },
  computed: {
    ...mapState('app', ['shopConfig'])
  },
  props: {
    tableData: {
      type: Array,
      default: () => []
    },
    activeAfterSaleStatus: {
      type: String,
      default: null
    },
    loading: {
      type: Boolean,
      default: false
    },
    ruleForm: {
      type: Object,
      default: () => {}
    },
    isTrialMerchant: {
      type: Boolean,
      default: false
    }, // 是否为灰度商户
  },
  data() {
    return {
      editRow: {},
      showViewer: false,
      initialIndex: 0, // 图片预览初始索引
      srcList: [],
      reqFun: null,
      changeExport: false,
      dialogVisible: false,
      orderRefundId: '',
      previewList: [],
      pdfVisible: false,
      showRefundVoucherDis: false,
    }
  },
  methods: {
    /**退款审批流程优化说明 */
		descriptionChange(){
			const h = this.$createElement;
			this.$msgbox({
				title: '功能说明',
				message: h('div', null, [
				h('div', null, '1、客户发起仅退款/退货退款申请，商家需在3个工作日内审核，超3个工作日未审核的，系统自动审核为同意'),
				h('div', null, '2、在线支付的订单，客户发起仅退款申请，商家运营审核通过/超时系统自动审核通过后，直接给客户退款，不再需要商家财务审核'),
				h('div', null, '3、在线支付的订单，客户发起退货退款申请，商家仓库审核入库后，直接给客户退款，不再需要商家财务审核'),
				h('div', null, '4、在线支付的订单，商家主动发起退款，客户同意后，直接给客户退款，不再需要商家财务审核'),
				h('div', null, '5、在线支付的订单，商家主动发起退货退款，商家仓库审核入库后，直接给客户退款，不再需要商家财务审核'),
				]),
				confirmButtonText: '我知道了',
			}).then(action => {
			})
		},
    editSellerRemark(row) {
      this.$refs.editSellerRemark.openDialog(row.id)
    },
    editLogistics(row) {
      // console.log(row);
      this.$refs.logisticsDialog.openDialog(row)
    },
    openImgView(val, index) {
      this.initialIndex = index
      this.btnClick(val, 'refundVoucher')
    },
    btnClick(row, type) {
      this.editRow = row || {}
      if (type === 'checkStatus') {
        this.$refs.refundLog.getListData(this.editRow.id)
      } else if (type === 'refundVoucher') {
        this.showViewer = true
        this.srcList = []
        this.$nextTick(() => {
          const imageViewer = document.querySelector('.el-image-viewer__wrapper')
          const node = document.createElement('span')
          const textnode = document.createTextNode(
            `一共含有${row.evidenceList ? row.evidenceList.length : 0}张凭证`
          )
          node.appendChild(textnode)
          node.className = 'el-image-viewer__btn text'
          imageViewer.appendChild(node)
        })
        this.srcList = row.evidenceList
      } else if (type === 'ToExamine') {
        this.$refs.refundApproval.stateVisible = true
      } else if (type === 'logisticsInformation') {
        // 物流信息
        this.orderRefundId = row.id
        this.dialogVisible = true
      } else if (type === 'seePdf') {
        this.pdfVisible = true
      }else if (type === 'showRefundVoucherDis') {
        this.showRefundVoucherDis = true 
      }
    },
    // 关闭弹框
    cancelDialog() {
      this.orderRefundId = ''
      this.dialogVisible = false
      this.pdfVisible = false
    },
    closeViewer() {
      this.showViewer = false
    },
    toogleExpandCargo(row) {
      this.$set(row, 'showChildren', !row.showChildren)
      if (row.showChildren) {
        getListRefundDetail({ refundOrderNo: row.refundOrderNo }).then((res) => {
          if (res.code === 0) {
            this.$set(row, 'detailList', (res || {}).data.list || [])
          }
        })
      }
    },
    handleChangeExport(info) {
      this.changeExport = false
      if (info === 'go') {
        const path = '/downloadList'
        window.openTab(path)
      }
    },
    // 由订单号生成时间
    orderNoToTime(orderNo) {
      let datePart = orderNo.substring(3, 11);
      let year = datePart.substring(0, 4);
      let month = datePart.substring(4, 6);
      let day = datePart.substring(6, 8);
      let dateTimeString = `${year}-${month}-${day} 00:00:00`;
      return dateTimeString
    },
    jumpOrderPage(orderNo,createTime) {
      if(createTime) {
        let dateTimeString = this.orderNoToTime(orderNo)
        window.openTab('/orderList', { orderNo,createTime: dateTimeString })
      }else {
        window.openTab('/orderList', { orderNo })
      }
    },
    handleExoprClose() {
      this.changeExport = false
    },
    exportAfterSale(type) {
      if (type === '1') {
        this.reqFun = refundListExport
      } else if (type === '2') {
        this.reqFun = refundListDetailsExport
      }
      const timeRule =
        this.ruleForm.startCreateTime &&
        this.ruleForm.endCreateTime &&
        this.ruleForm.endCreateTime - this.ruleForm.startCreateTime < 31 * 6 * 24 * 60 * 60 * 1000
      const params = { ...this.ruleForm }
      if (timeRule) {
        this.reqFun(params)
          .then((res) => {
            if (res.code === 0) {
              this.changeExport = true
            } else {
              this.$message.error({
                message: res.message,
                offset: 100
              })
            }
          })
          .catch((error) => {
            console.log(error)
            this.$message({
              message: '导出失败',
              type: 'error'
            })
          })
      } else {
        this.$message.error({
          message: '目前只允许导出申请日期跨度在6个自然月内的订单，请先选择申请日期',
          offset: 100
        })
      }
    },
    viewVoucher(row) {
      console.log(row)
      if (Array.isArray(row.evidenceUrlList)) {
        this.previewList = row.evidenceUrlList.map((item) => {
          return process.env.VUE_APP_UPLOAD_API + item
        })
        this.$nextTick(() => {
          this.$refs.previewImg.clickHandler()
        })
      }
    },
    // 打开审核弹窗
    toReview() {
      this.$refs.refundApproval.stateVisible = true
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep  .el-table th {
  background: #f9f9f9;
}

::v-deep  .el-table__expand-column {
  pointer-events: none;
}

::v-deep  .el-table__expand-column .el-icon {
  visibility: hidden;
}

::v-deep  .el-table__expanded-cell[class*='cell'] {
  padding: 0;
}

.btnText {
  color: #4184d5;
  cursor: pointer;
}

.hasOpened {
  font-size: 12px;
  width: 52px;
  height: 22px;
  line-height: 22px;
  text-align: center;
  background: #e6f7ff;
  border: 1px solid #91d5ff;
  border-radius: 3px;
  color: #1890ff;
}

.noOpened {
  font-size: 12px;
  width: 52px;
  height: 22px;
  line-height: 22px;
  text-align: center;
  background: #fff1f0;
  border: 1px solid #ffa39e;
  border-radius: 3px;
  color: #ff4d4f;
}
.refundRemark {
  color: #ff2121;
  font-size: 16px;
  margin-top: 10px;
  padding: 5px 0;
}

// .remarkBox,
// .remarkShowBox {
//   padding: 10px 20px;
// }
::v-deep  .el-table td {
  border-bottom: none;
}

::v-deep  .el-table td.el-table__expanded-cell {
  border-bottom: 1px solid #ebeef5;
}

::v-deep  .text {
  left: 50%;
  top: 30px;
  transform: translateX(-50%);
  width: 282px;
  height: 44px;
  padding: 0 23px;
  color: #fff;
}
.rightBtns {
  margin: 10px 0;
  float: right;
}

.refundSty {
  display: flex;
  flex-wrap: wrap;
  .refund__number {
    margin-left: 5px;
    color: #1890ff;
    cursor: pointer;
  }
  .refund__number:nth-of-type(1) {
    margin-left: 0;
  }
}
</style>
