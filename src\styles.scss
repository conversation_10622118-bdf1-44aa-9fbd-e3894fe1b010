/* theme color */
$--color-primary: #4183d5;

/* icon font path, required */
$--font-path: '~element-ui/lib/theme-chalk/fonts';

@import '~element-ui/packages/theme-chalk/src/index';

/* 设置滚动条的样式 */
::-webkit-scrollbar {
  width: 10px;
  height: 10px;
}
/* 滚动槽 */
::-webkit-scrollbar-track {
  box-shadow: inset 0 0 10px #e0e2e6;
  border-radius: 10px;
}
/* 滚动条滑块 */
::-webkit-scrollbar-thumb {
  border-radius: 10px;
  background: #d0d3d8;
  box-shadow: inset 0 0 10px #e0e2e6;
}
::-webkit-scrollbar-thumb:window-inactive {
  background: #ededed;
}

.el-message.center-msg {
  top: 50% !important;
  transform: translateY(-50%) translateX(-50%);
}

.el-tooltip__popper {
  max-width: 60% !important;
}

.title_line {
  padding-left: 8px;
  font-weight: 500;
  text-align: left;
  color: #000000;
  line-height: 14px;
  margin-bottom: 24px;
  font-family: PingFangSC, PingFangSC-Medium;
  position: relative;
}

.title_line:before {
  position: absolute;
  top: 50%;
  left: 0;
  content: '';
  width: 3px;
  height: 13px;
  background: linear-gradient(360deg, #1d69c4 1%, #8bbdfc 99%);
  border-radius: 2px;
  -ms-transform: translateY(-50%);
  -moz-transform: translateY(-50%);
  -webkit-transform: translateY(-50%);
  -o-transform: translateY(-50%);
  transform: translateY(-50%);
}

th.table_required>div:before{
  content: '*';
  color: #F56C6C;
  margin-right: 4px;
}
