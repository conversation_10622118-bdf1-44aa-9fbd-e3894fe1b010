<template>
  <lGuide :start="false">
    <template slot="control" slot-scope="scope">
      <div style="">
        <p>{{ scope.title }}</p>
        <p>{{ scope.content }}</p>
      </div>
    </template>
    <CommonHeader :showFold="false" :shouHeightLine="false">
      <lGuideStep slot="title" :step="1" title="步骤1" content="lwq太强">
        <div>
          <p>厂家查价举报</p>
          <p style="font-weight: 500;font-size: 14px;">
            <span>如果你的活动商品配置了虚拟供应商在售卖期间仍被厂家下单查货，可通过此渠道反馈。平台审核通过后查价账号将从</span>
            <span style="color: red;">VIP活跃用户</span>
            <span>中剔除，为商业的上架安全保驾护航。</span>
          </p>
        </div>
      </lGuideStep>
      <lGuideStep slot="header-right" :step="2"  title="步骤2" content="lwq太强">
        <reportHistory>
          <el-button type="text">举报记录查询</el-button>
        </reportHistory>
      </lGuideStep>
      <lGuideStep :step="3"  title="步骤3" content="ffds">
        <el-form :model="form">
          <el-form-item prop="skuCode" :rules="{ required: true, message: '请输入商品编码', trigger: 'change' }">
              <div>
                <p>
                  <span style="font-weight: 600;">被查货的商品编码/CSUID</span>
                  （商品必须配置了虚拟供应商，且供货信息配置了含VIP活跃用户标签的人群）
                </p>
                商品编码：<el-input size="mini" style="width: 300px;" v-model="form.skuCode"></el-input>
              </div>
          </el-form-item>
          <el-form-item prop="end_time">
            <div>
              <p>
                <span style="font-weight: 600;">请输入厂家查价使用的具体药店</span>
                （最多添加5个药店）
              </p>
              <div>
                <addCustomer v-model="form.list" :maxCount="5"></addCustomer>
                <el-table ref="table" :data="form.list" border style="margin: 10px 0;width: 800px;" height="400" stripe>
                  <el-table-column align="center" prop="merchantId" label="药店ID" width="180"></el-table-column>
                  <el-table-column align="center" prop="erpCode" label="ERP编码" width="250"></el-table-column>
                  <el-table-column align="center" prop="merchantName" label="药店名称" width="250"></el-table-column>
                  <el-table-column align="center" label="操作">
                    <template slot-scope="scope">
                      <el-button type="text" size="mini" @click="handleDelete(scope.row)" style="color:red;">删除药店</el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </div>
          </el-form-item>
        </el-form>
        <div>
          <el-button size="mini" type="primary" @click="submitForm" style="margin-top: 20px;">提交</el-button>
          <el-button size="mini" @click="cancel">取消</el-button>
        </div>
      </lGuideStep>
    </CommonHeader>
  </lGuide>
</template>
<script>
import reportHistory from "./components/reportHistory.vue";
import CommonHeader from "../afterSaleManager/components/common-header.vue"
import { guideCreater } from '../../components/lwq-comp/guide/index'
import addCustomer from "./components/addCustomer.vue";
import LGuideStep from "../../components/lwq-comp/guide/l-guide-step.vue";
import { auditMerchantPriceInquiry } from '../../api/customer-management/index'
const { lGuide, lGuideStep } = guideCreater();
export default {
  components: {
    CommonHeader,
    addCustomer,
    lGuide,
    lGuideStep,
    reportHistory
  },
  data() {
    return {
      form: {
        list: [],
        skuCode: ''
      },
      loading: false,
      start: false
    }
  },
  mounted() {
    window.clearData['/factoryReport'] = () => {
      this.form = {
        list: [],
        skuCode: ''
      }
    }
  },
  methods: {
    submitForm() {
      if (this.form.list.length <= 0) {
        this.$message.error('请添加厂家查价客户')
        return;
      }
      const params = {
        skuCode: this.form.skuCode,
        merchants: this.form.list.map(item => {
          return {
            merchantId: item.merchantId
          }
        })
      }
      if (this.loading) return;
      this.loading = true;
      auditMerchantPriceInquiry(params).then(res => {
        if (res.code === 0) {
          this.$message.success('提交成功，平台审核通过后药店将从VIP活跃客户中剔除')
          this.form = {
            list: [],
            skuCode: ''
          }
        } else {
          this.$message.error(res.message)
        }
      }).finally(() => {
        this.loading = false;
      })

    },
    handleDelete(row) {
      this.form.list = this.form.list.filter(item => item.merchantId != row.merchantId)
    },
    cancel() {
      window.closeTab(this.$route.fullPath);
      window.openTab('/customerOperation')
    }
  }
}
</script>
<style scoped>
p {
  margin: 5px 0;
}
</style>
