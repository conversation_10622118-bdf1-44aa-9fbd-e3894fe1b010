import request from '@/utils/request';

// 获取客户列表
export function getList(params) {
  return request({
    url: '/orgUserRelation/list',
    method: 'get',
    params,
  });
}
// 药店类型
export function getBusinessType(params) {
  return request({
    url: '/getBusinessType',
    method: 'get',
    params,
  });
}

// 省市区三级联动
export function getProvince(params) {
  return request({
    url: '/region',
    method: 'get',
    params,
  });
}

// 客户资质查询
export function getQualification(params) {
  return request({
    // url: '/orgUserRelation/license',
    url: '/orgUserRelation/v2/license',
    method: 'get',
    params,
  });
}

// 客户资质详情下载
export function downloadByUrl(params) {
  return request.post('/merchant/downloadByUrlV2', params);
}

// 客户资质列表下载
export function downloadBatch(params) {
  return request.post('/orgUserRelation/downloadBatchV2', params);
}

// 开票信息查询
export function invoiceInfo(params) {
  return request({
    url: '/orgUserRelation/invoice',
    method: 'get',
    params,
  });
}

// 维护ERP编码
export function editErpCode(params) {
  return request({
    url: '/orgUserRelation/editErpCode',
    method: 'get',
    params,
  });
}

// 导出客户列表
export function exportExcelList(params) {
  return request.post('/orgUserRelation/async/exportExcel', params);
}

export function getSkulog(params) {
  return request({
    url: '/orgUserRelation/viewOperationLog',
    method: 'get',
    params,
  });
}
// 查询客户资质有变更的客户数量
export function apiCountCustomerQualificationChanged(params) {
  return request({
    url: '/orgUserRelation/countCustomerQualificationChanged',
    method: 'get',
    params,
  })
}
// 查询客户资质更新时间到当前时间段内的资质变更记录
export function apiQueryQualificationChangeListWithinTime(params) {
  return request({
    url: '/orgUserRelation/queryQualificationChangeListWithinTime',
    method: 'get',
    params,
  })
}
// 更新客户资质更新时间
export function apiUpdateQualificationTime(params) {
  return request({
    url: '/orgUserRelation/updateQualificationTime',
    method: 'post',
    params,
  })
}
// 查询全部资质
export function apiQueryQualificationChangeList(params) {
  return request({
    url: '/orgUserRelation/queryQualificationChangeList',
    method: 'post',
    params,
  });
}
/**
 * 商品改版商品配置信息
 */
export function apiConfig(params) {
  return request.get('/product/config', params);
}

// 批量修改客户ERP编码
export function cusTomerBatchUpdateErpCode(params) {
  return request.post('/orgUserRelation/batchUpdateErpCode', params);
}

/**
 * 获取可标记列表
 */
export function marketList(params) {
  return request.get('/order/exception/marketText', params);
}

// 手动添加标记
export function addMarket(data) {
  return request({
    url: '/order/exception/save',
    method: 'post',
    data,
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
    transformRequest: [function (data) {
      let ret = '';
      Object.keys(data)
        .forEach((item) => {
          ret += `${encodeURIComponent(item)}=${encodeURIComponent(data[item])}&`;
        });
      return ret;
    }],
  });
}

export function getAuditHistory(params) {
  return request({
    url: '/report/pageReportDetails',
    method: 'get',
    params,
  });
}
export function auditMerchantPriceInquiry(params) {
  return request.post('/report/reportMerchantPriceInquiry', params);
}

//查询客户
export function apiAllBuyers(params) {
  return request({
    url: '/shopBlackBuyer/allBuyersV2',
    method: 'get',
    params: params
  })
}
