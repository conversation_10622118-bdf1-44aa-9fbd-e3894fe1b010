import request from '../../index';

// https://yapi.int.ybm100.com/project/891/interface/api/cat_8188
/**
 * 获取结算单数据
 * @param {参数} params
 */
export function listSettlement(params) {
  return request.get('/sellerPopBillSettle/listSettlement', params);
}

/**
 * 结算单导出
 * @param {参数} params
 */
export function exportSettlementList(params) {
  return request.get('/sellerBillSettleExport/async/exportSettlementList', params);
}

/**
 * 结算单明细导出
 * @param {参数} params
 */
export function exportSettleDetail(params) {
  return request.get('/sellerBillSettleExport/async/exportSettleDetail', params);
}

/**
 * 结算单金额统计
 * @param {参数} params
 */
export function querySettlementStatistic(params) {
  return request.get('/sellerPopBillSettle/querySettlementStatistic', params);
}

/**
 * 账单金额统计
 * @param {参数} params
 */
export function queryBillStatistic(params) {
  return request.get('/sellerPopBill/queryBillStatistic', params);
}

/**
 * 账单列表
 * @param {参数} params
 */
export function listBill(params) {
  return request.get('/sellerPopBill/listBill', params);
}

/**
 * 账单导出
 * @param {参数} params
 */
export function exportBillList(params) {
  return request.get('/sellerBillExport/async/exportBillList', params);
}

/**
 * 账单明细导出
 * @param {参数} params
 */
export function exportBillDetailList(params) {
  return request.get('/sellerBillExport/async/exportBillDetailList', params);
}

/**
 * 账单商品导出
 * @param {参数} params
 */
export function exportBillProductList(params) {
  return request.get('/sellerBillExport/async/exportBillProductList', params);
}

/**
 * 查询账单明细列表
 * @param {参数} params
 */
export function listBillDetail(params) {
  return request.get('/sellerPopBill/listBillDetail', params);
}

/**
 * 根据帐单号查询账单
 * @param {参数} params
 */
export function queryBillByBillNo(params) {
  return request.get('/sellerPopBill/queryBillByBillNo', params);
}

export function getDetail(params) {
  return request.get('/sellerPopBillSettle/detail', params)
} // 查询结算单明细