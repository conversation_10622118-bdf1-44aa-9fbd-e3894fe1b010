<template>
  <el-dialog
    title="设置总库存"
    :visible.sync="visible"
    width="30%"
    :before-close="close"
    :close-on-click-modal="false"
  >
  <el-form :model="formData" label-width="120px" ref="form" :key="tableKey" :rules="rules">
    <el-form-item label="设置总库存" prop="availableQtyType">
      <el-radio v-model="formData.availableQtyType" :label="2" @input="handelChange">虚拟库存</el-radio>
      <el-radio v-model="formData.availableQtyType" :label="1" @input="handelChange">真实库存</el-radio>
    </el-form-item>
    <el-form-item prop="totalStock">
      <el-input v-model="formData.totalStock" placeholder="请输入" style="width: 80%;" @input="onStockInput" :disabled="formData.availableQtyType === 1"></el-input>
    </el-form-item>
  </el-form>
  <span slot="footer" class="dialog-footer">
    <el-button @click="close">取 消</el-button>
    <el-button type="primary" @click="submit">确 定</el-button>
  </span>

  </el-dialog>

</template>
<script>
import {getTotalStock,updatelStock} from '@/api/product';
export default {
  name: "setTotalStock",
  props: {


  },
  data() {
    return {
      visible: false,
      tableKey: 0,
      formData:{
        csuid:'',
        availableQtyType: 1,
        availableQty: 0,
      },
      rules: {
        // totalStock: [
        //   { required: true, message: '请输入大于0的正数', trigger: 'blur' },
        //   { pattern: /^[1-9]\d*$/, message: '请输入大于0的正数', trigger: 'blur' }
        // ]
      }

    }
  },
  mounted() {

  },
  methods: {
    //提交
    async submit() {
      this.$refs.form.validate(async (valid) => {
        if (valid) {
          try{
            // let flag = true
            // 弹窗提示并等待用户确认
            if (this.formData.availableQtyType === 1) {
              try {
                await this.$confirm('从虚拟库存切换为真实库存且已自动拉取商品库存', {
                  confirmButtonText: '确定'
                });
              } catch {
                // 用户点击取消
                return;
              }
            }
            // if(!flag) return
            const res = await updatelStock({
              csuId:this.formData.csuid,
              availableQtyType:this.formData.availableQtyType,
              availableQty:Number(this.formData.totalStock)
            })

            if(res.code === 0){
              this.$message({
                message: '设置成功',
                type: 'success'
              })
              this.close()
              this.$emit('success')
            } else {
              this.$message.error(res.message)
            }
          } catch(e){
            this.$message.error(e.message)
          } finally{
            this.tableKey ++
          }
        }
      })
    },
    //设置总库存选项
    handelChange(val) {
      this.$refs.form.clearValidate()
    if (val === 2) {
      this.rules =  {
        totalStock: [
          { required: true, message: '请输入大于0的正数', trigger: 'blur' },
          { pattern: /^[1-9]\d*$/, message: '请输入大于0的正数', trigger: 'blur' }
        ]
      }
    } else{
      this.rules =  {}

    }


    },
    onStockInput(val) {
      // 使用正则匹配正整数（不包含0）
      const reg = /^[1-9]\d*$/;
      if (val === '' || reg.test(val)) {
        this.formData.totalStock = val;
      } else {
        // 非法输入自动移除
        this.formData.totalStock = this.formData.totalStock.replace(/[^\d]/g, '');
      }
    },
    open(csuid){
      this.visible = true
      this.formData.csuid = csuid
      this.getStock()

    },

    //获取库存情况
    async getStock(){
      try{
        const res = await getTotalStock({csuid:this.formData.csuid})
        if(res.code === 0){

          this.$set(this.formData,'totalStock',res.data.availableQty || 0)
          this.$set(this.formData,'availableQtyType',res.data.availableQtyType)
          // this.formData.totalStock = res.data.availableQty || 0
          // this.formData.availableQtyType = res.data.availableQtyType
        } else {
          this.$message.error(res.message)

        }

      } catch(e){
        this.$message.error(e.message)
      } finally{
        this.tableKey ++
      }
    },


    close(){
      this.visible = false
      this.formData.csuid = ''
      this.formData.availableQtyType = 1
      this.formData.totalStock = 0
      this.tableKey ++
    },

  }
}
</script>
<style lang="scss" scoped>
::v-deep .el-form-item{
  margin-bottom: 0px;
}
::v-deep .el-input__inner{
  height:32px;
  line-height: 32px;
}
</style>
