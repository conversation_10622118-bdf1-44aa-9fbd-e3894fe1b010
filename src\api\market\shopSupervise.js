import request from '@/utils/request';

export function upLoaderShopSupervise(params) {
  return new Promise((resolve, reject) => {
    request({
      headers: { 'Content-Type': 'multipart/form-data' },
      url: '/insight/importMerchant',
      method: 'post',
      data: params,
    })
      .then((res) => {
        resolve(res);
      })
      .catch((err) => {
        reject(err);
      });
  });
}
