<template>
  <div>
    <div class="line-div">
      <div class="serch">
        <el-row type="flex" align="middle">
          <div class="titleBox">
            <div>
              <span class="sign" />
              <div>查询条件</div>
            </div>
            <el-button
              type="primary"
              size="small"
              @click="$router.go(-1)"
            >
              返回
            </el-button>
          </div>
        </el-row>
      </div>
      <div class="box-div">
        <el-row :gutter="20" class="my-search-row">
          <el-col :span="6">
            <el-input v-model="listQuery.orderNo" placeholder="请输入" size="small">
              <template slot="prepend"> 订单号 </template>
            </el-input>
          </el-col>
          <el-col :span="6">
            <el-input v-model="listQuery.merchantName" placeholder="请输入" size="small">
              <template slot="prepend"> 客户名称 </template>
            </el-input>
          </el-col>
          <el-col :span="6">
            <el-input v-model="listQuery.barCode" placeholder="请输入" size="small">
              <template slot="prepend"> 商品编码 </template>
            </el-input>
          </el-col>
          <el-col :span="6">
            <span class="search-title">订单状态</span>
            <el-select v-model="listQuery.orderStatus" placeholder="请选择" size="small">
              <el-option label="全部" value="" />
              <el-option label="待付款" :value="10" />
              <el-option label="待审核" :value="1" />
              <el-option label="待发货" :value="7" />
              <el-option label="配送中" :value="2" />
              <el-option label="已完成" :value="3" />
            </el-select>
          </el-col>
        </el-row>
        <el-row :gutter="20" style="margin-top: 12px" class="my-search-row">
          <el-col :span="12">
            <span class="search-title">创建时间</span>
            <div class="time-box">
              <el-date-picker
                v-model.trim="listQuery.time"
                size="small"
                type="datetimerange"
                format="yyyy-MM-dd HH:mm:ss"
                :default-time="['00:00:00', '23:59:59']"
                value-format="timestamp"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                prefix-icon="el-icon-date"
              />
            </div>
          </el-col>
          <el-col :span="12" style="text-align: right">
            <el-button type="primary" size="small" @click="exportList"> 导出 </el-button>
            <el-button size="small" @click="resetList"> 重置 </el-button>
            <el-button type="primary" size="small" @click="getList"> 查询 </el-button>
          </el-col>
        </el-row>
      </div>
    </div>
    <div style="padding: 15px 20px">
      <el-row class="payInfoBox">
        <el-col :span="6">已付款：共{{ payInfo.paidBuyerCount }}个药店购买{{ payInfo.paidOrderAmount }}元</el-col>
        <el-col :span="6">待付款：共{{ payInfo.unpaidBuyerCount }}个药店购买{{ payInfo.unpaidOrderAmount }}元</el-col>
      </el-row>
      <div>
        <el-table
          ref="multipleTable"
          :data="tableData"
          stripe
          border
          tooltip-effect="dark"
          style="width: 100%"
        >
          <el-table-column prop="orderNo" label="订单号">
            <template slot-scope="scope">
              {{ scope.row.orderNo }}
              <el-button
                type="text"
                @click="jumpOrderPage(scope.row.orderNo)"
              >
                查看
              </el-button>
            </template>
          </el-table-column>
          <el-table-column prop="merchantName" label="客户名称">
            <template slot-scope="scope">
              <div>{{ scope.row.merchantName }}</div>
              <div>药店编码：{{ scope.row.merchantId }}</div>
            </template>
          </el-table-column>
          <el-table-column prop="businessStatusText" label="收货信息">
            <template slot-scope="scope">
              <div>{{ scope.row.contactor }}</div>
              <div>{{ scope.row.mobile }}</div>
              <div>{{ scope.row.address }}</div>
            </template>
          </el-table-column>
          <el-table-column prop="barCode" label="购买信息">
            <template slot-scope="scope">
              <div>商品编码：{{ scope.row.barcode }}</div>
              <div>商品数量：{{ scope.row.productAmount }}</div>
              <div>实付金额：{{ scope.row.realPayAmount }}</div>
              <div>支付方式：{{ scope.row.payTypeName }}</div>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="page-box">
        <Pagination
          v-show="totalList > 0"
          :total="totalList"
          :page.sync="queryPage.pageNum"
          :limit.sync="queryPage.pageSize"
          @pagination="getList"
        />
      </div>
    </div>
  </div>
</template>

<script>
import { getInfoList, exportStatisticInfo } from '../../api/activity/special';
import Pagination from '../../components/Pagination/index.vue';

export default {
  name: 'StaticInfo',
  components: { Pagination },
  data() {
    return {
      listQuery: {
        orderNo: '',
        orderStatus: '',
        time: [],
        merchantName: '',
        barCode: '',
      },
      queryPage: {
        pageNum: 1,
        pageSize: 10,
      },
      totalList: 0,
      tableData: [],
      createdDialog: false,
      payInfo: {},
    };
  },
  created() {
    // this.getList();
  },
  activated() {
    // this.getList();
    if (this.$route.query.refresh) {
      this.resetList();
    }
  },
  methods: {
    getList() {
      this.routerObj = this.$route.query || {};
      this.promotionId = this.routerObj.promotionId;
      const params = {
        ...this.listQuery,
        ...this.queryPage,
        actId: this.promotionId,
        createStartTime: this.listQuery.time[0],
        createEndTime: this.listQuery.time[1],
      };
      const load = this.loadingFun();
      getInfoList(params).then((res) => {
        load.close();
        if (res.code === 1000) {
          const result = res.data.data || {};
          this.tableData = result.list;
          this.totalList = result.total;
          this.payInfo = {
            paidBuyerCount: result.paidBuyerCount || 0,
            paidOrderAmount: result.paidOrderAmount || 0,
            unpaidBuyerCount: result.unpaidBuyerCount || 0,
            unpaidOrderAmount: result.unpaidOrderAmount || 0,
          };
        } else {
          this.$message.error('获取列表失败');
        }
      });
    },
    resetList() {
      this.listQuery = {
        orderNo: '',
        orderStatus: '',
        time: [],
        merchantName: '',
        barCode: '',
      };
      this.queryPage = {
        pageNum: 1,
        pageSize: 10,
      };
      this.getList();
    },
    loadingFun() {
      const loading = this.$loading({
        lock: true,
        text: 'Loading',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)',
      });
      return loading;
    },
    exportList() {
      this.routerObj = this.$route.query || {};
      this.promotionId = this.routerObj.promotionId;
      const params = {
        ...this.listQuery,
        ...this.queryPage,
        actId: this.promotionId,
        createStartTime: this.listQuery.time[0],
        createEndTime: this.listQuery.time[1],
      };
      exportStatisticInfo(params).then((res) => {
        this.util.exportExcel(res, '导出文件.xls');
      }).catch((err) => {
        console.log(err);
      });
    },
    createdGoodsList() {
      this.$refs.goodsUpload.submit();
    },
    createAct() {
      this.$router.push('/specialPrice/operate');
    },
    jumpOrderPage(orderNo) {
      window.openTab('/orderList', { orderNo });
    },
  },
};
</script>

<style scoped lang="scss">
.payInfoBox{
  padding: 5px 20px;
  background: #f3d9b2;
  opacity: 0.8;
}
.serch {
  font-weight: bold;
  .sign {
    display: inline-table;
    width: 3px;
    height: 13px;
    background: linear-gradient(360deg, #1d69c4 1%, #8bbdfc 99%);
    border-radius: 2px;
    margin-right: 8px;
  }
  .titleBox {
    width: 100%;
    display: flex;
    justify-content: space-between;
    div {
      display: flex;
      align-items: center;
    }
  }
}
.line-div {
  padding: 15px 20px;
  .search-title {
    display: table-cell;
    padding: 0 10px;
    text-align: center;
    border: 1px solid #dcdfe6;
    height: 30px;
    line-height: 30px;
    vertical-align: middle;
    border-right: none;
    border-radius: 4px 0 0 4px;
    color: #333333;
    white-space: nowrap;
  }
  .my-search-row ::v-deep  .el-input__inner {
    border-radius: 0 4px 4px 0;
  }
  .my-search-row ::v-deep  .el-select {
    display: table-cell;
  }
  ::v-deep  .el-input-group__prepend {
    background: none;
    color: #333333;
    padding: 0 10px;
  }
  .box-div{
    padding-top: 15px;
  }
}
.my-search-row ::v-deep  .el-input__inner {
  border-radius: 0 4px 4px 0;
}
.my-search-row {
  .time-box{
    display: table-cell;
    width: 100%;
    .time-text{
      padding: 0 5px;
    }
  }
  .picker-time{
    width: 45%;
  }
}
.my-search-row ::v-deep  .el-select {
  display: table-cell;
  width: 100%;
}
.button-box{
  padding: 15px 0;
  display: flex;
  justify-content: space-between;
}
.page-box {
  text-align: right;
  padding-top: 15px;
}
.colorB {
  color: #4183d5 !important;
  cursor: pointer;
  font-style: normal;
  a {
    text-decoration: none;
    color: #4183d5 !important;
  }
}
.choose {
  ::v-deep  .el-button--primary {
    background: #4183d5;
  }
  ::v-deep  .el-dialog__body {
    padding: 0 0 10px;
  }
  ::v-deep  .el-dialog__header {
    padding: 10px 16px;
    background: #f9f9f9;
  }
  ::v-deep  .el-dialog__headerbtn {
    top: 13px;
  }
  .conten-box{
    padding: 15px 20px;
    display: flex;
  }
}
</style>
