<template>
  <div>
    <CommonHeader :showFold="false" :shouHeightLine="false">
      <div v-loading="loading">
        <p>
          <span>步骤一：</span>
          <span>请按照以下规则命名图片</span>
        </p>
        <div class="content">
          <p>{{ tips[from] }}</p>
          <p v-if="from == 1" style="display: flex;flex-grow: 0;gap: 15px;">
            <el-input size="small" readonly style="width: 150px;" placeholder="ERP编码"></el-input>
            <el-input size="small" readonly style="width: 150px;" placeholder="_"></el-input>
            <el-input size="small" readonly style="width: 150px;" placeholder="资质名称"></el-input>
          </p>
          <p v-if="from == 2" style="display: flex;flex-grow: 0;gap: 15px;">
            <el-input size="small" readonly style="width: 150px;" placeholder="ERP编码"></el-input>
            <el-input size="small" readonly style="width: 150px;" placeholder="_"></el-input>
            <el-input size="small" readonly style="width: 150px;" placeholder="批号"></el-input>
            <el-input size="small" readonly style="width: 150px;" placeholder="_"></el-input>
            <el-input size="small" readonly style="width: 150px;" placeholder="药检报告页数序号"></el-input>
          </p>
          <p>
            注意：
          </p>
          <p>
            1、需确保同一ERP编码下每张图片命名都不重复;
          </p>
          <p>
            2、编码/批号不能含有与间隔符相同的内容;
          </p>
        </div>
        <p>
          <span>步骤二：</span>
          <span>上传文件</span>
        </p>
        <div class="content">
          <div style="display: flex;">
            <div style="flex-shrink: 0;">
              <p>点击按钮进入文件夹，选择文件开始上传。（支持pdf和图片格式,包括jpg、png、jpeg格式）</p>
              <p>每张图片大小10K - 15MB，且建议长宽比例参考A4纸（21cm * 29.7cm）</p>
              <iFile :multiple="true" :allowType="['.png', '.jpg', '.pdf','.jpeg']" :maxCount="maxCount" v-model="fileList" :bucket="3">
                <!-- <span>请优先上传PDF，支持pdf和图片格式，包括jpg、png、jpeg格式</span> -->
              </iFile>
            </div>
            <div style="flex-grow: 1;overflow-x: auto;">
              <div style="min-width: 700px;">
                <i-img :maxCount="maxCount" :isEdit="true" v-model="fileList" :column="3"></i-img>
              </div>
            </div>
          </div>
        </div>
        <p></p>
        <div class="content" style="display: flex;justify-content: center;">
          <el-button size="small" @click="close">取消</el-button>
          <el-button type="primary" size="small" @click="submit">确定</el-button>
        </div>
      </div>
    </CommonHeader>
    <el-dialog title="提示" :visible.sync="dialog.error" append-to-body>
      <p>校验失败，存在错误数据</p>
      <p>请下载错误说明文件并修改数据，重新上传。</p>
      <template slot="footer">
        <el-button size="small" @click="dialog.error = false;">取消</el-button>
        <el-button size="small" type="primary" @click="dialog.error = false;fileList=[];$router.push('/downloadList');">前往下载中心</el-button>
      </template>
    </el-dialog>
  </div>
</template>
<script>
import CommonHeader from '../afterSaleManager/components/common-header.vue'
import iFile from './components/i-file.vue'
import iImg from './components/i-img.vue'
import { firstSaleQualificationMultipartUpload, drugReportBatchUpload } from '../../api/qualificationOnline/index'
export default {
  name: '',
  components: {
    CommonHeader,
    iFile,
    iImg
  },
  data() {
    return {
      fileList: [],
      maxCount: 100,
      from: 0,   //1: 首营资质   2: 药检报告
      loading: false,
      tips: ['','默认规则（如：123456_说明书）','默认规则（如：123456_20231109_1）'],
      dialog: {
        error: false
      }
    }
  },
  activated() {
    if (this.$route.query) {
      this.from = this.$route.query.from;
    }
  },
  created() {
    window.clearData['/qualificationMultipartUpload'] = () => {
      this.fileList = [];
    }
  },
  methods: {
    submit() {
      if (!this.fileList.length) {
        this.$message.error('未选择文件');
        return
      }
      this.loading = true;
      const req = this.from == 1 ? firstSaleQualificationMultipartUpload : drugReportBatchUpload
      req(this.fileList).then(res => {
        if (res.code === 0) {
          this.$message.success('上传成功');
          this.close();
        } else if (res.code == -1) {
          this.dialog.error = true;
          return;
        } else {
          this.$message.error(res.msg);
        }
      }).finally(() => {
        this.loading = false;
      })

    },
    // 关闭tab
    close() {
      window.closeTab(this.$route.fullPath, true);
      const path = this.from == 1 ? '/qualificationManage' : '/drugTestResultManage'
      window.openTab(path)

    }
  }
}
</script>
<style scoped>
.content {
  padding: 15px;
  background-color: rgb(248, 248, 248);
  border-radius: 5px;
}
</style>
