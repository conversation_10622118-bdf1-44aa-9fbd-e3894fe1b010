<template>
  <section class="app-main" @scroll="hanlderScroll($event)">
    <iframe v-if="showIframe" :src="iframeSrc" frameborder="0" scrolling="yes" id="homepage"
            style="width: 100%; height: 100%; border: 0px;"></iframe>
    <transition v-else name="fade-transform" mode="out-in">
      <span>
        <keep-alive>
          <router-view v-if="!$route.meta.noCache" :key="key" />
        </keep-alive>
        <router-view v-if="$route.meta.noCache" :key="key"/>
      </span>
    </transition>
  </section>
</template>

<script>
import {mapState,mapMutations} from "vuex";
import {  saveStudyRecord ,checkGrayscaleOrg} from '@/api/home'
import Vue from "vue";
export default {
  name: 'AppMain',
  computed: {
    key() {
      return this.$route.path
    },
    ...mapState('permission', ['showIframe', 'iframeSrc', 'excludeList'])
  },
  watch: {
    'excludeList'(newValue, oldValue) {
      this.excludeName = newValue
    },
  },
  data() {
    return {
      excludeName: [],
      courseProgress:0,
    }
  },
  created(){

  },
  mounted(){
    this.$bus.$on('courseProgress',(lastCourseProgress,fileType)=>{
      console.log('%c [ lastCourseProgress ]-44', 'font-size:13px; background:pink; color:#bf2c9f;', )
      setTimeout(()=>{
        var container = document.querySelector('.app-main')

        this.courseProgress = !lastCourseProgress?lastCourseProgress:container.clientHeight / container.scrollHeight * 100 + '%'

        lastCourseProgress = lastCourseProgress.substring(0, lastCourseProgress.length - 1);

        container.scrollTop = (container.scrollHeight - container.clientHeight) * lastCourseProgress / 100

      },400)



    })
    checkGrayscaleOrg().then(res=>{
      this.SET_ISGRAYUSER(res.result)
    })
    this.$bus.$on('essayAndvideo',(id,fileType,lookTime)=>{
      console.log('%c [ lookTime ]-63', 'font-size:13px; background:pink; color:#bf2c9f;', lookTime)
      console.log('我接受到了',id)
      if(fileType ==1){
        saveStudyRecord({
          'courseId': id,
          'status': lookTime == 0?0:lookTime>=2?1:2,
          'courseProgress':lookTime == 0?'0%':lookTime>=2?'100%':'50%'
        })
      }
      else{
        saveStudyRecord({
        'courseId': id,
        'status':this.courseProgress == '0%' ? 0 :this.courseProgress == '100%' ? 1 : 2,
        'courseProgress':this.courseProgress
      })
      }


    })


  },
  methods:{
    ...mapMutations('app', ['SET_ISGRAYUSER']),
      hanlderScroll(event){
      this.courseProgress =  Math.min(Math.ceil((event.target.scrollTop / (event.target.scrollHeight - event.target.clientHeight))*100), 100) +'%'
    }


  }
}
</script>

<style lang="scss" scoped>
.app-main {
  width: calc(100% - 32px);
  height: calc(100% - 10px);
  position: relative;
  overflow: auto;
  background: #fff;
  margin: 5px 16px;
  Box-shadow: 0 0 6px rgba(0, 21, 41, 0.12);
  box-sizing: border-box;
  padding-right: 16px;
}

.fixed-header + .app-main {
  padding-top: 50px;
}
</style>

<style lang="scss">
// fix css style bug in open el-dialog
.el-popup-parent--hidden {
  .fixed-header {
    padding-right: 15px;
  }
}
</style>
