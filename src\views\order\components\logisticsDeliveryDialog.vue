<template>
  <el-dialog title="发物流快递" :visible="dialogVisible" width="70%" @close="closeDialog">
    <div>
      <el-form
        :rules="logInfoRules"
        ref="logInfo"
        :model="logInfo"
        size="small"
        label-width="120px"
      >
        <el-form-item class="log-info" label="客户名称：">
          <span>{{logInfo.merchantName}}</span>
        </el-form-item>
        <el-form-item class="log-info" label="订单编号：">
          <span>{{logInfo.orderNo}}</span>
        </el-form-item>
        <el-form-item class="log-info" label="配送方式：">
          <el-radio-group v-model.trim="logInfo.deliveryType" @change="deliveryChange">
            <el-radio :label="item.key" v-for="item in deliveryTypes" :key="item.key">
              {{item.value}}
              <el-tooltip v-if="item.key===0" effect="dark" placement="top">
                <template #content>
                  如需接入平台的快递面单打印功能，请到店铺管理-快递面单打印配置页面维护相关信息
                </template>
                <i class="el-icon-warning-outline"></i>
              </el-tooltip>
            </el-radio>
            <!-- <el-radio :label="0">
              自动分配快递单号
              <el-tooltip effect="dark" placement="top">
                <template #content>
                  1、目前仅支持“顺丰”、“京东”、“中通”、“邮政”面单自动生成和打印
                  <br />2、如需接入平台的快递面单打印功能，请到店铺管理-快递面单打印配置页面维护相关信息
                </template>
                <i class="el-icon-warning-outline"></i>
              </el-tooltip>
            </el-radio>
            <el-radio :label="1">手工录入快递信息</el-radio>
            <el-radio :label="2">商家配送</el-radio>-->
          </el-radio-group>
        </el-form-item>

        <el-form-item
          v-if="logInfo.deliveryType===0||logInfo.deliveryType===1"
          class="log-info"
          label="物流公司："
          prop="logisticsCompanyCode"
        >
          <el-select v-model="logInfo.logisticsCompanyCode" placeholder="请选择物流公司" @change="logisticsChange($event)">
            <el-option
              v-for="(item,index) in deliverList"
              :key="item.logisticsCompanyCode"
              :value="item.logisticsCompanyCode"
              :label="item.logisticsCompanyName"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          v-if="logInfo.deliveryType===0"
          class="log-info"
          label="快递类型："
          prop="deliveryCarrierType"
        >
          <el-select v-model="logInfo.deliveryCarrierType" placeholder="请选择快递类型">
            <el-option
              v-for="(item,index) in businessTypes"
              :key="item.key"
              :value="item.key"
              :label="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          v-if="logInfo.deliveryType===0||logInfo.deliveryType===2"
          class="log-info"
          label="包裹数："
          prop="bagNum"
        >
          <el-input
            v-model.trim="logInfo.bagNum"
            placeholder="请输入"
            maxlength="2"
            onkeyup="value=value.replace(/[^\d]/g,'')"
            @blur="logInfo.bagNum=$event.target.value"
          ></el-input>
        </el-form-item>
        <el-form-item v-if="logInfo.deliveryType===0&&isInsuredPrice" class="log-info" label="增值服务：">
          <span>
            <span style="color:#ff2400">当前承运方所有运单均执保价服务。</span>可到店铺管理-快递面单打印配置页面进行修改
          </span>
        </el-form-item>
        <el-form-item v-if="logInfo.deliveryType===1" class="log-info" label="物流单号：">
          <div v-for="(domain, index) in logInfo.trackingNos" :key="index" class="inputDiv">
            <el-form-item
              label=""
              :rules="newTrackingNos"
              :prop="'trackingNos.'+index+''"
            >
              <el-input v-model.trim="logInfo.trackingNos[index]" maxlength="20" />
            </el-form-item>
            <span v-if="index === 0" @click="addDomain">+</span>
            <span v-else @click.prevent="removeDomain(domain)">-</span>
            <el-button v-if="(logInfo.popDeliveryTrackingDtos[index] || {}).isCanPrint" type="primary" style="margin-left: 20px;" @click="toReprint((logInfo.popDeliveryTrackingDtos[index] || {}).trackingNo, (logInfo.popDeliveryTrackingDtos[index] || {}).deliveryPlatform)">重打快递面单</el-button>
          </div>
        </el-form-item>
        <el-form-item label="收货地址：" :required="logInfo.deliveryType === 0">
<!--          <el-row>-->
<!--            <el-col :span="6">-->
<!--              <el-form-item>-->
<!--                <span class="address-span">省份</span>-->
<!--                <el-select-->
<!--                  :disabled="logInfo.deliveryType!==0"-->
<!--                  class="address-info"-->
<!--                  v-model="logInfo.provinceCode"-->
<!--                  size="small"-->
<!--                  placeholder="全部"-->
<!--                  @change="getProvince('getCity', $event)"-->
<!--                >-->
<!--                  <el-option-->
<!--                    v-for="item in proviceList"-->
<!--                    :key="item.regionCode"-->
<!--                    :label="item.regionName"-->
<!--                    :value="item.regionCode"-->
<!--                  />-->
<!--                </el-select>-->
<!--              </el-form-item>-->
<!--            </el-col>-->
<!--            <el-col :span="6">-->
<!--              <el-form-item>-->
<!--                <span class="address-span">城市</span>-->
<!--                <el-select-->
<!--                  :disabled="logInfo.deliveryType!==0"-->
<!--                  class="address-info"-->
<!--                  v-model="logInfo.cityCode"-->
<!--                  size="small"-->
<!--                  placeholder="全部"-->
<!--                  @change="getProvince('getArea', $event)"-->
<!--                >-->
<!--                  <el-option-->
<!--                    v-for="item in cityList"-->
<!--                    :key="item.regionCode"-->
<!--                    :label="item.regionName"-->
<!--                    :value="item.regionCode"-->
<!--                  />-->
<!--                </el-select>-->
<!--              </el-form-item>-->
<!--            </el-col>-->
<!--            <el-col :span="6">-->
<!--              <el-form-item>-->
<!--                <span class="address-span">区县</span>-->
<!--                <el-select-->
<!--                  :disabled="logInfo.deliveryType!==0"-->
<!--                  class="address-info"-->
<!--                  v-model="logInfo.areaCode"-->
<!--                  size="small"-->
<!--                  placeholder="全部"-->
<!--                >-->
<!--                  <el-option-->
<!--                    v-for="item in areaList"-->
<!--                    :key="item.regionCode"-->
<!--                    :label="item.regionName"-->
<!--                    :value="item.regionCode"-->
<!--                  />-->
<!--                </el-select>-->
<!--              </el-form-item>-->
<!--            </el-col>-->
<!--          </el-row>-->
<!--          <el-row>-->
<!--            <el-col :span="17">-->
              <el-form-item class="address-item" style="margin-bottom: 0">
<!--                <span class="address-span">详细地址</span>-->
                <el-input
                  :disabled="logInfo.deliveryType!==0"
                  v-model="logInfo.address"
                  maxlength="60"
                  placeholder="请输入"
                ></el-input>
              </el-form-item>
<!--            </el-col>-->
<!--          </el-row>-->
        </el-form-item>
        <el-form-item class="log-info" label="收件人：" :required="logInfo.deliveryType === 0">
          <el-input
            :disabled="logInfo.deliveryType!==0"
            v-model="logInfo.contactor"
            placeholder="请输入"
          ></el-input>
        </el-form-item>
        <el-form-item class="log-info" label="联系电话：" :required="logInfo.deliveryType === 0">
          <el-input :disabled="logInfo.deliveryType!==0" v-model="logInfo.mobile" placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item v-if="logInfo.deliveryType===0" class="log-info" label="备注信息：">
          <el-input
            :disabled="logInfo.deliveryType!==0"
            v-model="logInfo.remark"
            placeholder="请输入"
            maxlength="60"
            type="textarea"
            show-word-limit
          ></el-input>
        </el-form-item>
      </el-form>
    </div>

    <span slot="footer" class="dialog-footer">
      <el-button
        v-if="logInfo.deliveryType===0"
        size="small"
        type="primary"
        @click="handleQuery('logInfo','print')"
      >提交并打印电子面单</el-button>
      <el-button v-else size="small" type="primary" @click="handleQuery('logInfo')">提交</el-button>
    </span>
    <ExpressPrint v-if="isDeliveryType.length" v-show="false" ref="expressPrint" :expressPrintConfig="expressPrintConfig"/>
  </el-dialog>
</template>

<script>
import { apiGetDeliverGoods, apiDeliverGoods, apiUpdateDeliverGoods, getRegionList, apiPrintCallBack, repeatPrint, addPrintCount } from '@/api/order/index';
import ExpressPrint from '@/components/print/expressPrint';

export default {
  name: 'LogisticsDeliveryDialog',
  components: { ExpressPrint },
  props: {
    dialogVisible: {
      type: Boolean,
      default: false,
    },
    merchantId: {
      type: Number,
      default: null,
    },
    orderNo: {
      type: String,
      default: '',
    },
    merchantName: {
      type: String,
      default: '',
    },
    editInformation: {
      type: Boolean,
      default: false,
    }
  },
  data() {
    return {
      logInfo: {
        address:'',
        areaCode:'',
        areaName:'',
        bagNum:1,
        cityCode:'',
        cityName:'',
        contactor:'',
        deliveryType:1,
        logisticsCompanyName:'',
        logisticsCompanyCode:'',
        deliveryCarrierType:'',
        merchantId:'',
        mobile:'',
        orderNo:'',
        provinceCode:'',
        provinceName:'',
        remark:'',
        merchantName:'',
        trackingNos:[],
      },
      newTrackingNos: [
        {required: true, pattern: /[0-9a-zA-Z\-]{6,100}/, message: '运单号仅支持数字或字母数字格式，且字符长度必须在5位以上', trigger: 'blur'}
      ],
      logInfoRules: {
        logisticsCompanyCode: [
          {required: true, message: '请填写信息', trigger: 'change'}
        ],
        deliveryCarrierType: [
          {required: true, message: '请填写信息', trigger: 'change'}
        ],
        trackingNos: [
          {required: true, pattern: /[0-9a-zA-Z]{6,100}/, message: '运单号仅支持数字或字母数字格式，且字符长度必须在5位以上', trigger: 'blur'}
        ],
        bagNum: [
          {required: true, message: '请填写信息', trigger: 'blur'}
        ],
      },
      proviceList: [],
      cityList: [],
      areaList: [],
      deliveryTypes: [],
      toOrderDeliverDetailDtos: [],
      storeDeliveryTypes: [],
      deliverList: [],
      businessTypes: [],
      expressPrintConfig:{},
      deliveryTypeCopy:1,
      isInsuredPrice:false,
      logisticsCompanyCodeAutomatic:'', // 自动
      logisticsCompanyCodeManual:'', // 手动
      isDeliveryType:[],
    }
  },
  created() {
    // this.getProvince('getProv');
  },
  mounted() {
    this.getDeliverGoods();
  },
  methods: {
    // 省市区
    getProvince(type, e) {
      let code = e?'['+ e +']':'';
      const pms = { parentCode: code|| null };
      getRegionList(pms).then((res) => {
        if (type === 'getProv') {
          this.proviceList = res.data || [];
        } else if (type === 'getCity') {
          this.cityList = res.data || [];
        } else if (type === 'getArea') {
          this.areaList = res.data || [];
        }
      });
    },
    // 根据快递类型查业务类型
    logisticsChange(val){
      this.businessTypes = [];
      let isLogisticsCompanyCode = false;
      if(this.deliverList){
        this.deliverList.forEach((item,index)=>{
          // 选中值
          if(item.logisticsCompanyCode === val){
            isLogisticsCompanyCode = true;
            if(this.logInfo.deliveryType === 0){
              this.logisticsCompanyCodeAutomatic = this.logInfo.logisticsCompanyCode;
            }else if(this.logInfo.deliveryType === 1){
              this.logisticsCompanyCodeManual = this.logInfo.logisticsCompanyCode;
            }
            // 如果有子类型
            if(item.businessTypes){
              this.businessTypes = item.businessTypes;
              if(this.businessTypes && this.businessTypes.length){
                this.logInfo.deliveryCarrierType = this.businessTypes[0].key;
              }
            }
            // 如果有保价金额
            this.isInsuredPrice = item.insuredPrice && item.insuredPrice>0;
          }
        });
      }
      if(!isLogisticsCompanyCode){
        this.logInfo.logisticsCompanyCode = '';
        this.logInfo.deliveryCarrierType = '';
      }
    },
    deliveryChange(val){
      const that = this;
      if(val===0){
        this.deliveryTypeCopy = val;
        this.logInfo.logisticsCompanyCode = this.logisticsCompanyCodeAutomatic;
        this.deliverList = this.toOrderDeliverDetailDtos;
        this.logisticsChange(this.logInfo.logisticsCompanyCode);
      }else if(val===1){
        this.deliveryTypeCopy = val;
        this.logInfo.logisticsCompanyCode = this.logisticsCompanyCodeManual;
        this.deliverList = this.storeDeliveryTypes;
        this.logisticsChange(this.logInfo.logisticsCompanyCode);
      }else{
        this.deliverList = [];
        if(this.editInformation){
          this.$confirm('改为“商家配送”会将已维护的运单删除，确认切换配送方式吗？', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
          })
            .then(() => {
              this.deliveryTypeCopy = val;
            })
            .catch(() => {
              that.logInfo.deliveryType = that.deliveryTypeCopy;
              that.deliveryChange(that.logInfo.deliveryType);
              return;
            });
        }
      }
    },
    // 查询发货物流快递接口
    getDeliverGoods(){
      const that = this;
      const params = {
        orderNo: this.orderNo,
        merchantId: this.merchantId,
        type: this.editInformation ? 1 : 0
      }
      apiGetDeliverGoods(params).then((res) => {
        if(res.code === 0){
          if(res.data){
            that.logInfo = res.data;
            that.deliveryTypes = res.data.deliveryTypes||[];
            // 判断是否有自动分配，加载组件
            that.isDeliveryType = that.deliveryTypes.filter((item,index)=>{
              return item.key === 0;
            });
            // 自动分配取logisticsCompanyCode
            that.logisticsCompanyCodeAutomatic = res.data.logisticsCompanyCode;
            that.toOrderDeliverDetailDtos = res.data.toOrderDeliverDetailDtos || [];
            // 商业手动录入取businessLogisticsCompanyCode
            that.logisticsCompanyCodeManual = res.data.businessLogisticsCompanyCode;
            that.storeDeliveryTypes = res.data.storeDeliveryTypes || [];

            that.deliveryChange(that.logInfo.deliveryType);
            that.deliveryTypeCopy = that.logInfo.deliveryType;
            if(!that.logInfo.trackingNos.length){
              that.logInfo.trackingNos = [];
              that.logInfo.trackingNos.push('');
            }
            // if (that.logInfo.provinceCode) {
            //   that.getProvince('getCity', that.logInfo.provinceCode);
            // }
            // if (that.logInfo.cityCode) {
            //   that.getProvince('getArea', that.logInfo.cityCode);
            // }
          }
        } else {
          that.$message.error(res.message)
        }
      })
    },
    closeDialog() {
      this.$emit('cancelDialog');
    },
    handleQuery(formName,print) {

      // const SF = {"consignor":"张三","contactor":"李永亮11","deliveryMobile":"1760123124","isVip":1,"logisticsCompanyCode":1,"mailAddress":"白果镇易家大湾23号","mailRegionName":"湖北省黄冈市麻城市","merchantErpCode":"2","merchantName":"永亮药帮忙三十六店铺","mobile":"17810675590","orderCode":"YSD1579649447908868179","orderNo":"YBM20220818170544100018","payType":"寄付月结","printCount":1,"printTime":1665452752256,"proName":"顺丰特快","sFFace":{"abFlag":"","codingMapping":"R26","codingMappingOut":"","destRouteLabel":"027HM","destTeamCode":"","limitTypeCode":"T4","proCode":"T4","proName":"顺丰特快","sourceTransferCode":"713","twoDimensionCode":"MMM={'k1':'027','k2':'027HM','k3':'','k4':'T4','k5':'SF7444447280415','k6':'','k7':'65c37490'}"},"settlementCardNo":"0276713871","skipPrintPreview":0,"subWaybillNoList":[{"subSerialNumber":"1","subWaybillNo":"SF7444447280415"}],"takeAeliveryAddress":"湖北省十堰市郧阳区青山镇是多少(湖北省武汉市经济开发区)","takeAeliveryRegionName":"湖北省十堰市郧阳区","tmsToWms":"1","waybillNo":"SF7444447280415"}
      // const JDWL={"consignor":"张三","contactor":"李永亮11","deliveryMobile":"1760123124","isVip":1,"jDKYFace":{"consignerAddress":"湖北黄冈市麻城市白果镇白果镇易家大湾23号","destCrossAndCarCode":"1-B3","destinationCrossCode":"1","destinationDmsName":"十堰分拣中心","destinationTabletrolleyCode":"B3","origCrossAndCarCode":"3-阳逻","originalCrossCode":"3","originalDmsName":"鄂州分拣中心","originalTabletrolleyCode":"阳逻","printSiteName":"十堰郧阳营业部","sendCity":"黄冈市"},"logisticsCompanyCode":11,"mailAddress":"白果镇易家大湾23号","mailRegionName":"湖北省黄冈市麻城市","merchantErpCode":"2","merchantName":"永亮药帮忙三十六店铺","mobile":"17810675590","orderCode":"YSD1579652359250771968","orderNo":"YBM20220818170544100018","payType":"寄付月结","printCount":1,"printTime":1665453450195,"proName":"医药零担","settlementCardNo":"010K1354776","skipPrintPreview":0,"subWaybillNoList":[{"subSerialNumber":"1","subWaybillNo":"JDV008516465440-1-1-"}],"takeAeliveryAddress":"湖北省十堰市郧阳区青山镇是多少(湖北省武汉市经济开发区)","takeAeliveryRegionName":"湖北省十堰市郧阳区","tmsToWms":"1","waybillNo":"JDV008516465440"}
      // this.$refs.expressPrint.printAction(JSON.parse(JSON.stringify(JDWL)));
      // this.printSuccess();
      // return;


      this.$refs[formName].validate((valid) => {
        if (valid) {
          if (this.logInfo.deliveryType===1 && this.logInfo.trackingNos.length === 1) {
            if (this.logInfo.trackingNos[0] === ' ' || !this.logInfo.trackingNos[0].trim()) {
              this.$message.warning({ message: '请先添加物流单号'});
              return;
            }
          }
          if(this.logInfo.deliveryType===0&&(!this.logInfo.mobile||!this.logInfo.contactor)){
            this.$message.warning({ message: '请填写信息'});
            return;
          }
          this.proviceList.map((item,index)=>{
            if(this.logInfo.provinceCode===item.regionCode){
              this.logInfo.provinceName = item.regionName;
            }
          });
          this.cityList.map((item,index)=>{
            if(this.logInfo.cityCode===item.regionCode){
              this.logInfo.cityName = item.regionName;
            }
          });
          this.areaList.map((item,index)=>{
            if(this.logInfo.areaCode===item.regionCode){
              this.logInfo.areaName = item.regionName;
            }
          });
          const loading = this.$loading({
            lock: true,
            text: 'Loading',
            spinner: 'el-icon-loading',
            background: 'rgba(255,255,255, 0.8)',
          });
          // 修改
          if(this.editInformation){
            apiUpdateDeliverGoods(this.logInfo).then((res) => {
              loading.close();
              if(res.code === 0){
                if(print==='print'){
                  this.$refs.expressPrint.printAction(JSON.parse(res.data));
                  this.printSuccess();
                }else{
                  this.$message.success({ message: '提交成功'});
                  this.closeDialog();
                  this.$emit('gitList');
                }
              } else {
                this.$message.error(res.message)
              }
            })
          }else{
            delete this.logInfo.id;
            apiDeliverGoods(this.logInfo).then((res) => {
              loading.close();
              if(res.code === 0){
                if(print==='print'){
                  this.$refs.expressPrint.printAction(JSON.parse(res.data));
                  this.printSuccess();
                }else{
                  this.$message.success({ message: '提交成功'});
                  this.closeDialog();
                  this.$emit('gitList');
                }
              } else {
                this.$message.error(res.message)
              }
            })
          }
        } else {
          return false;
        }
        return false;
      });
    },
    printSuccess(type, trackingNo){
      setTimeout(() => {
        this.$confirm('是否打印成功？', '温馨提示', {
          confirmButtonText: '打印成功',
          cancelButtonText: '打印失败',
        })
          .then(() => {
            this.printCallBack(0);
            if (type === 'reprint') {
              this.toAddPrintCount(trackingNo);
            }
          })
          .catch(() => {
            this.printCallBack(1);
          });
      }, 500);
    },
    // 回调
    printCallBack(type) {
      const params = {
        orderNo:this.orderNo,
        type:type,
      }
      apiPrintCallBack(params)
        .then((res) => {
          if (res.code === 0) {
            if(type === 0){
              this.$message.success(res.data);
              this.closeDialog();
              this.$emit('gitList');
            }
          } else {
            this.$message.error(res.message);
          }
        })
        .catch(() => {});
    },
    // 重新打印快递单
    toReprint(trackingNo, deliveryPlatform) {
      const loading = this.$loading({
        lock: true,
        text: 'Loading',
        spinner: 'el-icon-loading',
        background: 'rgba(255,255,255, 0.8)',
      });
      repeatPrint({
        trackingNo,
        deliveryPlatform,
        orderNo: this.orderNo,
      }).then((res) => {
        loading.close();
        if(res.code === 0){
          this.$refs.expressPrint.printAction(JSON.parse(res.data));
          this.printSuccess('reprint', trackingNo);
        } else {
          this.$message.error(res.message)
        }
      })
    },
    toAddPrintCount(trackingNo) {
      addPrintCount({
        trackingNo,
        orderNo: this.orderNo,
      }).then((res) => {
        console.log('重打次数加1');
      })
    },
    removeDomain(data) {
      const index = this.logInfo.trackingNos.indexOf(data);
      if (index !== -1) {
        this.logInfo.trackingNos.splice(index, 1);
      }
    },
    addDomain() {
      if(this.logInfo.trackingNos && this.logInfo.trackingNos.length>=10){
        this.$message.info('最多可添加10个');
        return;
      }
      this.logInfo.trackingNos.push('');
    },
  }
}
</script>
<style scoped lang="scss">
.inputDiv {
  display: flex;
  align-items: flex-start;
  padding-bottom: 10px;
  span {
    cursor: pointer;
    margin-left: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    color: #666666;
  }
}

.address-info {
  width: 140px;
}
.address-span {
  margin-right: 10px;
}
::v-deep   .address-item {
  .address-span {
    width: 70px;
  }
  .el-form-item__content {
    display: flex;
  }
}
::v-deep   .el-form .log-info {
  width: 100%;

  .el-input {
    width: 243px;
  }

  .el-select {
    margin-right: 14px;
  }

  .el-form-item__label {
    // font-size: 12px;
    line-height: 30px;
  }

  .el-form-item__content {
    line-height: 30px;
  }

  .el-input__inner {
    line-height: 30px;
    height: 30px;
    font-size: 12px;
  }

  .el-input__icon {
    line-height: 30px;
  }
}
</style>
