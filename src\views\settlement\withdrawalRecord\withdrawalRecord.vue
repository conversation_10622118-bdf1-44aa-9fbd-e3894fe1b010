<template>
  <div class="withdrawalRecord">
    <div class="contentBox">
      <div class="title">查询条件</div>
      <!-- <div class="operations">
        <el-button type="primary" @click="goBack" size="small">返回</el-button>
      </div> -->
      <SearchForm
        ref="searchForm"
        :model="formModel"
        :form-items="formItems"
        :has-operation="false"
        :span-num="3"
      />
      <div class="search-btn">
        <el-button @click="reset" size="small">重置</el-button>
        <el-button type="primary" @click="search" size="small">查询</el-button>
      </div>
      <div class="title">提现记录</div>
      <div class="operations export">
        <el-button v-permission="['cash_exportWithdraw']" type="primary" plain @click="exportWithdrawalRecord" size="small">导出提现记录</el-button>
        <el-button v-permission="['cash_exportWithdrawDetail']" type="primary" plain @click="exportWithdrawalRecordDetail" size="small">导出提现明细</el-button>
      </div>
      <xyyTable
        ref="withdrawalRecord"
        v-loading="tableLoading"
        :data="tableConfig.data"
        :col="tableConfig.col"
        :list-query="tableConfig.pagConfig"
        @get-data="queryList"
      >
        <template slot="operation">
          <el-table-column label="操作" fixed="right" width="150">
            <template slot-scope="{row}">
              <el-button v-if="row.hasDetail" type="text" @click="openWithdrawalRecordDetail(row)">查看详情</el-button>
              <div v-else>--</div>
            </template>
          </el-table-column>
        </template>
      </xyyTable>
    </div>
    <export-tip
      :change-export="changeExport"
      @handleExoprClose="handleExoprClose"
      @handleChangeExport="handleChangeExport"
    />
  </div>
</template>

<script>
import SearchForm from '@/components/searchForm';
import exportTip from '@/views/other/components/exportTip';
import { findCashAdvanceV2, exportWithdrawalRecord, exportWithdrawalRecordDetail } from '@/api/settlement/withdrawal';

export default {
  name: 'WithdrawalRecord',
  components: { SearchForm, exportTip },
  data() {
    return {
      initialFormModel: {},
      formModel: {
        cashAdvanceNum: '',
        time: '',
        paymentTime: '',
        cashType: '',
        paymentStatus: '',
      },
      formItems: [
        {
          label: '提现单号',
          prop: 'cashAdvanceNum',
          component: 'el-input',
          colSpan: 5,
          attrs: {
            placeholder: '请输入'
          }
        },
        {
          label: '申请日期',
          prop: 'time',
          component: 'el-date-picker',
          colSpan: 9,
          attrs: {
            type: 'daterange',
            startPlaceholder: "开始日期",
            endPlaceholder: "结束日期",
            valueFormat: "yyyy-MM-dd",
          }
        },
        {
          label: '打款日期',
          prop: 'paymentTime',
          component: 'el-date-picker',
          colSpan: 9,
          attrs: {
            type: 'daterange',
            startPlaceholder: "开始日期",
            endPlaceholder: "结束日期",
            valueFormat: "yyyy-MM-dd",
          }
        },
        {
          label: '提现单类型',
          prop: 'cashType',
          component: 'el-select',
          attrs: {
            options: [
              {
                label: '全部',
                value: '',
              },
              {
                label: '在线支付',
                value: 1,
              },
              {
                label: '线下转账',
                value: 2,
              }
            ],
          },
        },
        {
          label: '打款状态',
          prop: 'paymentStatus',
          component: 'el-select',
          attrs: {
            options: [
              {
                label: '全部',
                value: '',
              },
              {
                label: '待核款',
                value: 1,
              },
              {
                label: '已打款',
                value: 2,
              },
              {
                label: '打款失败',
                value: 3,
              },
              {
                label: '退汇',
                value: 4,
              },
              {
                label: '待打款',
                value: 5,
              },
              {
                label: '打款中',
                value: 6,
              },
            ],
          },
        },
      ],
      tableLoading: false,
      tableConfig: {
        data: [],
        col: [
          {
            name: '提现单号',
            index: 'cashAdvanceNum',
            width: 200,
          },
          {
            name: '提现单类型',
            index: 'cashType',
            width: 200,
            formatter: (row, column, cellValue) => {
              if (cellValue === 1) return '在线支付';
              if (cellValue === 2) return '线下转账';
              return '';
            },
          },
          {
            name: '申请时间',
            index: 'applyTime',
            width: 180,
            formatter: (row, column, cellValue) => {
              return this.formatDate(cellValue)
            }
          },
          {
            name: '申请账号',
            index: 'monile',
            width: 120
          },
          {
            name: '申请提现金额',
            index: 'applyAmount',
            width: 120
          },
          {
            name: '提现手续费',
            index: 'fee',
            width: 120
          },
          {
            name: '实际到账金额',
            index: 'realityAmount',
            width: 120
          },
          /* {
            name: '提现账户名',
            index: 'accountName',
            width: 240
          }, */
          {
            name: '账号',
            index: 'accountNum',
            width: 220
          },
          {
            name: '开户行',
            index: 'accountBank',
            width: 220
          },
          {
            name: '开户支行',
            index: 'accountSubBank',
            width: 220
          },
          {
            name: '打款状态',
            index: 'paymentStatus',
            width: 120,
            formatter: (row, column, cellValue) => {
              if (Number(cellValue) === 1) {
                return '待核款'
              } else if (Number(cellValue) === 2) {
                return '已打款'
              } else if (Number(cellValue) === 3) {
                return '打款失败'
              } else if (Number(cellValue) === 4) {
                return '退汇'
              } else if (Number(cellValue) === 5){
                return '待打款'
              }else if(Number(cellValue) === 6){
                return '打款中'
              }else {
                return '--'
              }
            }
          },
          {
            name: '备注/原因',
            index: 'reason',
            width: 120
          },
          {
            name: '打款时间',
            index: 'paymentTime',
            width: 180,
            formatter: (row, column, cellValue) => {
              return this.formatDate(cellValue);
            },
          },
          {
            name: '操作',
            index: 'operation',
            slot: 'operation'
          }
        ],
        pagConfig: {
          pageSize: 10,
          page: 1,
          total: 0
        }
      },
      isDetailBack: false,
      changeExport: false,
    }
  },
  mounted() {
    // this.initTime()
    this.initialFormModel = JSON.parse(JSON.stringify(this.formModel));
    if (this.isDetailBack) {
      const params = this.util.getLocal('withdrawalRecordQueryParams');
      const { paymentStatus, time, pageSize, page, total } = params;
      this.formModel = { paymentStatus, time };
      this.tableConfig.pagConfig = { pageSize, page, total };
    }
    this.queryList();
  },
  methods: {
    initTime() {
      // 本月的开始时间
      const startDate = this.formatDate(new Date(new Date().getFullYear(), new Date().getMonth(), 1).getTime(), 'YMD');
      const curDay = this.formatDate(new Date().getTime(), 'YMD');
      this.formModel.time = [startDate, curDay];
    },
    search() {
      this.queryList();
    },
    reset() {
      this.formModel = this.initialFormModel;
      this.queryList();
    },
    goBack() {
      this.$router.push({ path: '/bill' });
    },
    openWithdrawalRecordDetail(row) {
      this.$router.push({ path: '/withdrawalRecordDetail', query: { cashAdvanceNum: row.cashAdvanceNum } });
    },
    getParams() {
      const params = { ...this.formModel }
      if (params.time && params.time.length > 0) {
        params.applyTimeStart = params.time[0];
        params.applyTimeEnd = params.time[1];
      } else {
        params.applyTimeStart = '';
        params.applyTimeEnd = '';
        // this.$alert('请选择申请日期', '温馨提示', {
        //   confirmButtonText: '确定'
        // });
        // return false
      }
      if (params.paymentTime && params.paymentTime.length > 0) {
        params.paymentTimeStart = params.paymentTime[0];
        params.paymentTimeEnd = params.paymentTime[1];
      } else {
        params.paymentTimeStart = '';
        params.paymentTimeEnd = '';
      }
      delete params.time;
      delete params.paymentTime;
      return params;
    },
    async queryList(listQuery) {
      let params = this.getParams()
      if (!params) return
      this.tableLoading = true
      if (listQuery) {
        const {pageSize, page} = listQuery
        this.tableConfig.pagConfig.pageSize = pageSize
        this.tableConfig.pagConfig.page = page
      }
      const {pageSize, page} = this.tableConfig.pagConfig
      params.pageNum = page
      params.pageSize = pageSize
      console.log(JSON.stringify(params))
      try {
        const res = await findCashAdvanceV2(params)
        if (res && res.code === 0) {
          this.tableConfig.data = res.data.list
          this.tableConfig.pagConfig.total = res.data.total
        } else {
          this.$message.error(res.message || '请求异常');
        }
      } catch (e) {
        console.log(e)
      }
      this.tableLoading = false
    },
    getCurrentDay() {
      const day = this.formatDate(new Date().getTime(), 'YMD')
      return day.split('-').join('')
    },
    checkExportTime() {
      let params = this.getParams()
      if (!params) return
      if (this.tableConfig.data.length === 1) { //若当前搜索结果只有一条提现记录，则不再校验申请时间。
        return true
      }
      const startM = params.applyTimeStart
      const endM = params.applyTimeEnd
      const day = (new Date(endM).getTime() - new Date(startM)) / 1000 / 60 / 60 / 24
      if (day > 186) {  //6个自然月内（186天）
        this.$alert('只允许导出6个自然月内(186天)的提现明细，请筛选申请日期查询后再次尝试导出', '温馨提示', {
          confirmButtonText: '确定'
        })
        return false
      } else {
        return true
      }
    },
    async exportWithdrawalRecord() {
      let params = this.getParams()
      if (!params || !this.checkExportTime()) return
      try {
        // const res = await exportWithdrawalRecord(params)
        // if (res.code && res.code !== 0) {
        //   this.$message.error(res.message || '请求异常');
        // } else {
        //   this.util.exportExcel(res, `导出提现记录${this.getCurrentDay()}.xlsx`)
        // }
        exportWithdrawalRecord(params).then((res) => {
          if (res.code && res.code !== 0) {
            this.$message.error(res.message);
            return
          } else {
            this.changeExport = true
          }
        });
      } catch (e) {
        console.log(e)
      }
    },
    async exportWithdrawalRecordDetail() {
      let params = this.getParams()
      if (!params || !this.checkExportTime()) return
      if (!this.checkExportTime()) return
      try {
        // const res = await exportWithdrawalRecordDetail(params)
        // if (res.code && res.code !== 0) {
        //   this.$message.error(res.message || '请求异常');
        // } else {
        //   this.util.exportExcel(res, `导出提现明细${this.getCurrentDay()}.xlsx`)
        // }
        exportWithdrawalRecordDetail(params).then((res) => {
          if (res.code && res.code !== 0) {
            this.$message.error(res.message);
            return;
          } else {
            this.changeExport = true;
          }
        });
      } catch (e) {
        console.log(e)
      }
    },
    handleChangeExport(info) {
      this.changeExport = false;
      if (info === 'go') {
        const path = '/downloadList'
        window.openTab(path)
        //that.$router.push({ path: '/downloadList' });
      }
    },
    handleExoprClose() {
      this.changeExport = false;
    }
  },
  beforeRouteEnter(to, from, next) {
    next(vm => {
      console.log('beforeRouteEnter')
      if (from.name === 'withdrawalRecordDetail') {
        vm.isDetailBack = true
      }
    })
  },
  beforeRouteLeave(to, from, next) {
    if (to.name === 'withdrawalRecordDetail') {
      const obj = {...this.formModel, ...this.tableConfig.pagConfig}
      this.util.setLocal('withdrawalRecordQueryParams', obj)
    }
    next()
  }
}
</script>

<style scoped lang="scss">
.contentBox {
  //height: 100%;
  padding: 16px 16px;
  background: #fff;
  margin-bottom: 10px;
  position: relative;

  .title {
    font-weight: 500;
    text-align: left;
    color: #000000;
    line-height: 14px;
    margin-bottom: 26px;
  }

  .title:before {
    content: '';
    display: inline-block;
    width: 3px;
    height: 13px;
    background: linear-gradient(360deg, #1d69c4 1%, #8bbdfc 99%);
    border-radius: 2px;
    margin-right: 8px;
    vertical-align: middle;
  }

  .operations {
    position: absolute;
    top: 10px;
    right: 16px;
  }

  .search-btn {
    position: absolute;
    top: 100px;
    right: 16px;
  }

  .operations.export {
    top: 140px;
  }

  .searchForm {
    overflow: hidden;
  }
}
</style>
