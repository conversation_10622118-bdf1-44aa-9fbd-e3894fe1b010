import request from '../../index'

/**
 * 获取银行账户信息
 * @param {参数} params
 */
export function getBankAccountInfo(params) {
  return request.get('/sellerCommissionSettle/bankAccountInfo', params)
}
/**
 * 获取佣金记录列表
 * @param {参数} params
 */
export function getCommissionRecordList(params) {
  return request.get('/sellerCommissionSettle/list', params)
}
/**
 * 获取待商业付款条数
 * @param {参数} params
 */
export function getStatistic(params) {
  return request.get('/sellerCommissionSettle/statistics', params)
}
/**
 * 导出应缴佣金列表
 * @param {参数} params
 */
export function exportData(params) {
  return request.get('/sellerCommissionSettle/queryExportCommissionSettleListCount', params)
}
/**
 * 佣金缴纳处理
 * @param {参数} params
 */
export function sendWithdraw(params) {
  return request.post('/sellerCommissionSettle/commissionPayProcess', params)
}
/**
 * 导出应缴佣金详情列表
 * @param {参数} params
 */
export function exportDataDetail(params) {
  return request.get('/sellerCommissionSettle/queryExportCommissionSettleBillListCount', params)
}
/**
 * 查看佣金缴费的账单明细列表
 * @param {参数} params
 */
export function getCommissionDetailList(params) {
  return request.get('/sellerCommissionSettle/commissionSettleBillList', params)
}
/**
 * 查看佣金缴纳记录详情
 * @param {参数} params
 */
export function getCommissionDetailInfo(params) {
  return request.get('/sellerCommissionSettle/commissionSettleDetail', params)
}

/**
 * 佣金缴纳记录-导出应缴纳佣金记录
 */
export function exportCommission(params) {
  return request.get('/settle/async/exportCommission', params)
}

/**
 * 佣金缴纳记录-导出应缴纳佣金明细
 */
export function exportCommissionDetail(params) {
  return request.get('/settle/async/exportCommissionDetail', params)
}