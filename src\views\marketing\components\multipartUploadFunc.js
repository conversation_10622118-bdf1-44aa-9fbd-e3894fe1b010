import { importProduct, batchOffLine } from '../../../api/market/mzPromotion'
import { apiBatchDeleteBlackBuyers } from '../../../api/storeManagement/blacklist'
/**
 * 批量导入 主品 or 赠品池
 * productTpye: 主品：1   赠品：2
 * @param { object } that   this
 * @param { 1 | 2 } productType
 * @returns { (file: File) => Promise }
 */
export const importProductByProductType = (that, productType) => {
  return (file) => {
    if (that.loading) return;
    that.loading = true;
    console.log(that.curList.map(item => item.csuid));

    importProduct({
      file: file,
      productType,
      outCsuIdList: that.curList.map(item => item.csuid)
    }).then(res => {
      if (res.code == 1000) {
        //把这个emit出去
        that.$emit('getList', res.data.batchImportResult.sucessProductList.map(item => {
          return {
            csuid: item.skuId,
            showName: item.skuName,
            chainPrice: item.guidePrice,
            ...item
          }
        }));
        //结果信息
        that.result.msgList = [
          `导入成功${res.data.batchImportResult.successNum}个商品`,
          `导入失败${res.data.batchImportResult.failureNum}个商品`
        ]
        that.result.errorUrl = res.data.batchImportResult.failureExcelUrl;
        that.result.visible = true;
      } else {
        that.$message.error(res.msg)
      }
    }).finally(() => {
      that.loading = false;
    })
  }
}


export const batchOffline = (that) => {
  return (file) => {
    if (that.loading) return;
    that.loading = true;
    batchOffLine({
      file: file
    }).then(res => {
      if (res.code == 1000) {
        //把这个emit出去
        //that.$emit('getList', res.data.batchOffLine.sucessProductList);
        //结果信息
        that.result.msgList = [
          `下线成功${res.data.batchOffLine.successNum}个活动`,
          `下线失败${res.data.batchOffLine.failureNum}个活动`
        ]
        that.result.errorUrl = res.data.batchOffLine.failureExcelFileDownloadUrl;

        that.result.visible = true;
      } else {
        that.$message.error(res.msg)
      }
    }).finally(() => {
      that.loading = false;
    })
  }

}

export const batchDeleteBlackList = (that) => {
  return (file) => {
    if (that.loading) return;
    that.loading = true;
    apiBatchDeleteBlackBuyers(file).then(res => {
      if (res.code == 0) {
        that.result.msgList = [
          `删除成功${res.data.success}个客户`,
          `删除失败${res.data.error}个客户`
        ]
        that.result.errorUrl = res.data.errorFileUrl;

        that.result.visible = true;
      } else {
        that.$message.error(res.message)
      }
    }).finally(() => {
      that.loading = false;
    })
  }
}
