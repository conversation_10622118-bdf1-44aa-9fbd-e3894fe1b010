<template>
  <!-- 表格 -->
  <div class="tableSty">
    <el-table
      :data="tableData"
      style="width: 100%; margin-bottom: 10px"
      border
      stripe
      v-loading="loading"
    >
      <el-table-column label="变动时间" prop="changeTime">
        <template slot-scope="scope">
          <div>{{ formatDate(scope.row.updateTime, 'YMDHMS') }}</div>
        </template>
      </el-table-column>
      <el-table-column label="变动类型" prop="fundChangeTypeStr">
        <!-- <template slot-scope="scope">
          <span>
            {{ accountTypeList[scope.row.fundChangeType] }}
          </span>
        </template> -->
      </el-table-column>
      <el-table-column label="原因说明" prop="description" />
      <el-table-column label="单号" prop="orderNo">
        <template slot-scope="scope">
          <div>YBM单号: {{ scope.row.orderNo }}</div>
          <div>订单ID: {{ scope.row.orderId }}</div>
        </template>
      </el-table-column>
      <el-table-column label="客户" prop="customerName">
        <template slot-scope="scope">
          <div>{{ scope.row.ERPCustomerCode }}</div>
          <div>{{ scope.row.customerName }}</div>
        </template>
      </el-table-column>
      <el-table-column label="变动金额" prop="actualReceivedAmount">
        <template slot-scope="scope">
          <span></span>
          <div v-if="scope.row.fundChangeStatus == 1 && scope.row.actualReceivedAmount != null" style="color: green">+￥{{ scope.row.actualReceivedAmount }}</div>
          <div v-if="scope.row.fundChangeStatus == 2 && scope.row.actualReceivedAmount != null" style="color: red">-￥{{ scope.row.actualReceivedAmount }}</div>
        </template>
      </el-table-column>
      <el-table-column label="余额" prop="changedBalance">
        <template slot-scope="scope">
          <span></span>
          <div v-if="scope.row.changedBalance != null">￥{{ scope.row.changedBalance }}</div>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <el-pagination
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      :current-page="pagination.pageNum"
      :page-sizes="[10, 20, 50]"
      :page-size="pagination.pageSize"
      background
      layout="->, total, sizes, prev, pager, next, jumper"
      :total="pagination.total"
    >
    </el-pagination>
  </div>
</template>

<script>
import { pageFundFlow } from '@/api/settlement/marketingServiceQuota/index'

export default {
  data() {
    return {
      form: {
        orderNo: null, // 商户
        fundChangeType: null, // 类型
        changeTime: null // 变动时间
      },
      loading: false, //表格加载中
      tableData: [], // 表格数据
      pagination: {
        pageNum: 1,
        pageSize: 10,
        total: 0
      }, // 表格分页
      accountTypeList: {}, // 变动类型
    }
  },
  methods: {
    setChangeType(val) {
      val.forEach(item => {
        this.accountTypeList[item.code] = item.name
      });
      console.log(this.accountTypeList);
    },
    search(val) {
      if (val) this.form = val
      // console.log(this.form, val)
      this.loadTableData()
    }, // 查询逻辑
    loadTableData() {
      console.log(this.form, this.pagination);
      let subData = {
        fundPropertyStatus: 2,
        orderNo: this.form.orderNo,
        fundChangeType: this.form.fundChangeType,
        changeStartTime: this.form.changeTime?.[0] || null,
        changeEndTime: this.form.changeTime?.[1] || null,
        pageNum: this.pagination.pageNum,
        pageSize: this.pagination.pageSize
      }
      this.loading = true
      // 调用接口请求数据，替换tableData
      pageFundFlow(subData).then(res => {
        if(res.code == 0) {
          this.tableData = res.data.list
          this.pagination.total = res.data.total
        }
      }).finally(() => {
        this.loading = false
      })
    }, // 加载表格数据

    handleCurrentChange(pageNum) {
      this.pagination.pageNum = pageNum
      this.search()
    }, // 页码改变的回调
    handleSizeChange(pageSize) {
      this.pagination.pageSize = pageSize
      this.search()
    } // 每页条数改变的回调
  }
}
</script>

<style scoped lang="scss">
.tableSty {
  width: 98%;
  //   border: 1px solid red;
  margin: 0 auto;
}
.selectSty {
  display: flex;
  flex-wrap: wrap;
}
.bar {
  display: none;
}
</style>
