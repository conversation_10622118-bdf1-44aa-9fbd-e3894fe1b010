<template>
    <div class="storeVoucher">
      <div class="sticky-tabs">
          <el-tabs v-model="activeName" @tab-click="handleClick">
              <el-tab-pane label="店铺楼层设置" name="storeIndex"/>
              <el-tab-pane label="店铺广告设置" name="advertisingSetList"/>
              <el-tab-pane label="图片楼层设置" name="pictureFloor"/>
          </el-tabs>
      </div>
      <transition name="fade" mode="out-in">
        <keep-alive>
            <component :is="currentComponent"></component>
        </keep-alive>
      </transition>
    </div>
  </template>
  
  <script>
  import storeIndex from "@/views/store-management/storeIndex.vue"
  import advertisingSetList from "@/views/store-management/advertisingSetList.vue"
  import pictureFloor from "@/views/store-management/pictureFloor.vue"
  export default {
      name: "shopDecoration",
      components: {
        storeIndex,
        advertisingSetList,
        pictureFloor
      },
      data() {
          return {
              activeName: "storeIndex",
              currentComponent: storeIndex,
          }
      },
      activated(){
          if(this.$route.query.to) {
              this.selectComponents(this.$route.query.to)
          }
      },
      methods: {
          handleClick(tab, event) {
              this.$router.replace({
                  path: 'shopDecoration',
                  query: { to: tab.name },
              });
              this.selectComponents(tab.name)
          },
          selectComponents(target) {
              if(target) {
                  this.activeName = target
              }
              switch (target) {
                  case "storeIndex":
                      this.currentComponent = storeIndex
                      break;
                  case "advertisingSetList":
                      this.currentComponent = advertisingSetList
                      break;
                  case "pictureFloor":
                      this.currentComponent = pictureFloor
                      break;
                  default:
                      break;
              }
          }
      },
  }
  </script>
  
  <style>
  .storeVoucher {
      margin-top: 10px;
      padding-left: 10px;
  }
  .sticky-tabs {
    position: sticky;
    top: 0;
    z-index: 1000;
    background: #fff;
    padding: 10px 0;
  }
  /* 定义过渡动画 */
.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-to,
.fade-leave-from {
  opacity: 1;
}
  </style>