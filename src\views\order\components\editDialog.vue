<template>
  <div>
    <el-dialog
      :title="dialogEditTypeStr"
      :visible="editVisible"
      width="50%"
      @close="closeDialog"
    >
      <div>
        <div v-if="dialogEditType === 'editErp' || dialogEditType === 'editAdd'">
          <!--          <p v-if="dialogEditType === 'editErp'">-->
          <!--            客户名称：{{ erpName }}-->
          <!--          </p>-->
          <p v-if="dialogEditType === 'editAdd'">
            在开户前请确认客户提交的资料已经满足您要求的开户条件！
          </p>
          <p>平台客户名称：{{ erpName }}</p>
          <p class="flexBox">
            客户ERP编码：<el-input
              v-model="erpCode"
              style="width: 60%"
              type="text"
              placeholder="请输入"
              size="small"
            />
          </p>
        </div>
        <div v-if="dialogEditType === 'editTip'">
          <el-form
            ref="form"
            size="small"
            class="editDialog"
          >
            <el-form-item
              label="快速备注语"
              style="display: flex;"
            >
              <el-select
                v-model.trim="qucikRemark"
                placeholder="请选择"
                style="width: 100%"
                @change="handleSelectQuickRemark"
              >
                <el-option
                  v-for="(item, index) in quickRemarkList"
                  :key="index"
                  :label="item.content"
                  :value="item.content"
                />
              </el-select>
              <p
                style="color:#4184d5;cursor: pointer;margin: 0 0 0 10px;flex-shrink: 0;"
                @click="handleEditQuickRemark"
              >
                编辑
              </p>
            </el-form-item>
            <el-form-item
              label="备注"
              style="display: flex;"
            >
              <el-input
                v-model="tipText"
                type="textarea"
                :autosize="{ minRows: 2, maxRows: 4}"
                placeholder="请输入内容"
                maxlength="200"
                show-word-limit
              />
            </el-form-item>
            <div>
              <el-checkbox
                v-model="customerVisible"
              >
                客户可见（勾选后，客户可在订单上看到备注内容，请仔细核查备注内容）
              </el-checkbox>
            </div>
          </el-form>
        </div>
      </div>
      <div v-if="tableData.length > 0">
        <p style="color: #ff2121">
          *系统匹配到多个ERP客户信息，请选择：
        </p>
        <el-table
          ref="singleTable"
          :data="tableData"
          highlight-current-row
          style="width: 100%"
          border
          @current-change="handleCurrentChange"
        >
          <el-table-column
            type="index"
            width="50"
          />
          <el-table-column
            property="customerCode"
            label="ERP客户编码"
          />
          <el-table-column
            property="customerName"
            label="ERP客户名称"
          />
        </el-table>
      </div>
      <span slot="footer">
        <el-button
          size="mini"
          @click="closeDialog"
        >取 消</el-button>
        <el-button
          type="primary"
          size="mini"
          @click="confirmEdit"
        >确 定</el-button>
      </span>
    </el-dialog>
    <el-dialog
      title="编辑快速备注语"
      :visible="editQucikRemarkVis"
      width="50%"
      @close="editQucikRemarkVis = false"
    >
      <div style="color: red;">
        输入框中内容限制30个字
      </div>
      <div
        v-for="(item, index) in editQucikRemarkList"
        :key="index"
        class="editQucikRemarkList"
      >
        <span>{{ `${index + 1}：` }}</span>
        <el-input
          v-model="item.content"
          placeholder="请输入内容"
          style="flex: 1;"
          maxlength="30"
        />
      </div>
      <span slot="footer">
        <el-button
          size="mini"
          @click="editQucikRemarkVis = false"
        >取 消</el-button>
        <el-button
          type="primary"
          size="mini"
          @click="handleSaveQucikRemark"
        >保 存</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { openAcount, editErpCode, addSellerRemark, getListByName, selectRemarkDict, saveOrUpdateRemark, batchAddSellerRemarks } from '@/api/order/index';

export default {
  name: 'EditDialog',
  props: {
    editVisible: {
      type: Boolean,
      default: false,
    },
    dialogEditType: {
      type: String,
      default: '',
    },
    erpName: {
      type: String,
      default: '',
    },
    merchantId: {
      type: Number,
      default: null,
    },
    orderNo: {
      type: String,
      default: null,
    },
    remarkStr: {
      type: String,
      default: '',
    },
    merchantStatus: {
      type: String || Number,
      default: null,
    },
  },
  data() {
    return {
      customerVisible: false,
      editQucikRemarkList: [],
      editQucikRemarkVis: false,
      qucikRemark: '',
      quickRemarkList: [],
      erpCode: '',
      dialogEditTypeStr: '',
      tipText: '',
      tipHeight: document.documentElement.clientHeight / 3,
      tableData: [],
      currentRow: '',
      fromStr: '',
    };
  },
  watch: {
    dialogEditType: {
      handler(newName) {
        this.fromStr = newName;
        newName === 'editErp' ? this.dialogEditTypeStr = '编辑ERP编号' : newName === 'editTip' ? this.dialogEditTypeStr = '添加备注' : this.dialogEditTypeStr = '开户提示';
        if (newName === 'editTip') {
          this.tipText = this.remarkStr || '';
          for (let i = 0; i < 10; i++) {
            this.editQucikRemarkList.push({
              content: '',
              sort: i + 1,
            });
          }
          this.selectRemarkDict();
        }
        // eslint-disable-next-line no-unused-expressions
        newName === 'editErp' || newName === 'editAdd' ? this.getErpList() : '';
      },
      immediate: true,
    },
  },
  methods: {
    handleSaveQucikRemark() {
      const arr = [];
      this.editQucikRemarkList.filter(item => item.content).forEach((item, index) => {
        arr.push({
          content: item.content,
          sort: index + 1,
        });
      });
      saveOrUpdateRemark(arr).then((res) => {
        if (res.code === 0) {
          this.$message.success(res.msg || '保存成功');
          this.selectRemarkDict();
          this.editQucikRemarkVis = false;
        }
      });
    },
    handleEditQuickRemark() {
      if (this.quickRemarkList.length) {
        const arr = [];
        this.editQucikRemarkList.forEach((item, index) => {
          arr.push({
            content: this.quickRemarkList[index] ? this.quickRemarkList[index].content : '',
            sort: index + 1,
          });
        });
        this.editQucikRemarkList = arr;
      }
      this.editQucikRemarkVis = true;
    },
    selectRemarkDict() {
      selectRemarkDict().then((res) => {
        if (res.code === 0) {
          this.quickRemarkList = res.result;
        }
      });
    },
    handleSelectQuickRemark(val) {
      this.tipText = `${this.tipText}${`${val}；`}`.substring(0, 200);
      this.qucikRemark = '';
    },
    getErpList() {
      this.tableData = [];
      if (this.fromStr === 'editErp' && this.erpCode) {
        return;
      }
      getListByName({ merchantName: this.erpName }).then((res) => {
        console.log(res);
        if (res.code === 0) {
          this.tableData = res.data ? res.data : [];
        } else {
          this.tableData = [];
        }
      });
    },
    handleCurrentChange(val) {
      if (val.customerId === this.currentRow) {
        this.$refs.singleTable.setCurrentRow();
        this.currentRow = '';
        this.erpCode = '';
      } else {
        this.currentRow = val.customerId;
        this.erpCode = val.customerCode;
      }
    },
    closeDialog() {
      this.$emit('closeDialog', false);
    },
    confirmEdit() {
      if (this.dialogEditType === 'editTip') {
        const params = {
          orderNo: this.orderNo,
          sellerRemark: this.tipText,
          customerVisible: this.customerVisible ? 1 : 0,
        };
        if (this.orderNo.includes(',')) {
          params.orderNos = params.orderNo;
          delete params.orderNo;
          batchAddSellerRemarks(params).then((res) => {
            if (res.code === 0) {
              this.$message.success({ message: '编辑成功', offset: this.tipHeight });
              this.emitFun();
            } else {
              this.$message.error({ message: res.message || res.msg, offset: this.tipHeight });
            }
          });
          return false;
        }
        addSellerRemark(params).then((res) => {
          if (res.code === 0) {
            this.$message.success({ message: '编辑成功', offset: this.tipHeight });
            this.emitFun();
          } else {
            this.$message.error({ message: res.message, offset: this.tipHeight });
          }
        });
      } else {
        const params = {
          merchantId: this.merchantId,
          erpCode: this.erpCode,
        };
        this.dialogEditType === 'editAdd'
          ? openAcount(params).then((res) => {
            if (res.code === 0) {
              this.$message.success({ message: '开户成功', offset: this.tipHeight });
              this.emitFun();
            } else {
              this.$message.error({ message: res.msg, offset: this.tipHeight });
            }
          })
          : this.erpCode
            ? editErpCode(params).then((res) => {
              if (res.code === 0) {
                this.$message.success({ message: '编辑成功', offset: this.tipHeight });
                this.emitFun();
              } else {
                this.$message.error({ message: res.message, offset: this.tipHeight });
              }
            })
            : this.$message.warning({ message: '请先填写ERP编码', offset: this.tipHeight });
      }
    },
    emitFun() {
      this.closeDialog();
      this.$emit('gitList');
    },
  },
};
</script>

<style lang="scss">
.editQucikRemarkList {
  display: flex;
  align-items: center;
  margin: 10px 0 0 0;
}
.editDialog {
  .el-form-item__content {
    flex: 1;
    display: flex;
  }
}
</style>
<style scoped lang="scss">
.flexBox{
  display: flex;
  align-items: center;
}
::v-deep  .el-dialog__body{
  padding: 10px 20px;
}
::v-deep  .el-dialog__header{
  padding: 10px 20px;
  background: #f9f9f9;
}
::v-deep  .el-dialog__headerbtn{
  top: 15px;
}
::v-deep  .el-dialog__title{
  font-size: 16px;
}
</style>
