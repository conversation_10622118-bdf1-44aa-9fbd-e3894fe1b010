<template>
  <div class="error-tip">
    <el-dialog title="推荐参团设置" :visible.sync="dialogVisible" width="700px" :before-close="handleClose">
      <el-form :model="ruleForm" label-width="180px" ref="ruleForm" :rules="rules">
        <div style="display: flex;">
          <el-form-item label="可接受降价幅度：" prop="Discount" class="is-required">
            <div style="display: flex;">
              <el-select v-model="ruleForm.discount" placeholder="">
                <el-option label="0%" :value="0"></el-option>
                <el-option label="1%" :value="1"></el-option>
                <el-option label="自定义" value="3"></el-option>
              </el-select>
              <el-form-item label="" prop="inputDiscount" v-if="ruleForm.discount == 3" label-width="0">
                <div style="display: flex;"> <el-input v-model="ruleForm.inputDiscount"
                    style="width: 130px;margin-left: 10px;" placeholder="请维护正整数"></el-input>&nbsp; % </div>
              </el-form-item>
            </div>
            <div style="color: red;font-size: 13px;margin-top: 7px;">在原活动价格基础上进行降价，拼团价格=原活动价格*(1-降价幅度)</div>
          </el-form-item>


        </div>

        <supplyTypeConfig @changeSupplyTypeEvent="changeSupplyTypeEvent" ref="supplyChild" style="margin-left: 28px;">
        </supplyTypeConfig>
        <el-form-item label="虚拟供应商：" prop="isVirtualShop" class="is-required">
          <el-radio-group v-model="ruleForm.isVirtualShop">
            <el-radio :label="-1">复制原活动</el-radio>
            <el-radio :label="1">是</el-radio>
            <el-radio :label="2">否</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="起拼数量：" prop="" class="is-required">
          <div style="display: flex;align-items: center;height: 40px;">
            <el-radio-group v-model="ruleForm.startQty">
              <el-radio :label="-1">复制原活动</el-radio>
              <el-radio label="1">
                客单价换算

              </el-radio>
            </el-radio-group>
            <span v-if="ruleForm.startQty == 1">

              <el-form-item label="" prop="inputStartQty" label-width="10px">
                <el-input v-model="ruleForm.inputStartQty" style="width: 120px;margin-left: 10px;" placeholder="请维护正整数">
                </el-input>&nbsp;
                <span style="color: black;">元</span>
              </el-form-item>
            </span>
          </div>
          <div style="color: red;font-size: 13px;margin-top: 5px;">客单价换算：则起拼数量=客单价/拼团价格</div>
        </el-form-item>

        <el-form-item label="单店限购类型：" prop="LimitType" class="is-required">
          <el-radio-group v-model="ruleForm.LimitType">
            <el-radio :label="-1">复制原活动</el-radio>
            <el-radio label="1">自定义</el-radio>
            <span>
              <el-select style="width: 250px" v-model="ruleForm.personalLimitType" v-if="ruleForm.LimitType == 1">
                <el-option v-for="item in personalLimitTypeList" :label="item.value" :value="item.code"
                  :key="item.code">{{ item.value }}</el-option>
              </el-select>
            </span>
          </el-radio-group>
        </el-form-item>

      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="UpdateOrCreateSetting">确 定</el-button>
      </span>
    </el-dialog>
    <crowd-selector-dialog v-if="dialogVisibleP" v-model="dialogVisibleP" :selected="ruleForm.customerGroupId"
      @onSelect="onSelect" />
  </div>
</template>
<script>
import CrowdSelectorDialog from '../../../components/xyy/customerOperatoin/crowd-selector-dialog.vue';
import supplyTypeConfig from "./supplyTypeConfigv2.vue"
import {
  updateSetting,
  createSetting,freshFrame
} from '@/api/market/collageActivity'

export default {
  components: {
    CrowdSelectorDialog, supplyTypeConfig
  },
  props: {
    isCreate: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      ruleForm: {
        id: "",
        discount: 1, //可接受降价幅度
        inputDiscount: '',
        scopeType: -1, //供货信息配置方式
        isVirtualShop: -1,    //虚拟供应商
        startQty: -1, // 起拼数量
        inputStartQty: "",
        LimitType: -1, //单店限购类型
        inputLimitType: '',
        personalLimitType: ""
      },
      rules: {
        inputDiscount: [{ validator: this.validateNumber, trigger: 'blur' }],
        inputStartQty: [{ validator: this.validateNumberbV2, trigger: 'blur' }]
      },
      dialogVisible: false,
      dialogVisibleP: false,
      personalLimitTypeList: [
        {
          code: 0, value: "不限购"
        }, {
          code: 1, value: "活动期间限购"
        }, {
          code: 2, value: "每天(每天00:00至24:00)"
        }, {
          code: 3, value: "单笔订单限购"
        }, {
          code: 4, value: "每周(周—O0:00至周日24:00)"
        }, {
          code: 5, value: "每月(每月1号00:00至每月最后一天24:00)"
        }],
      data: {
        id: '',
        baseId: '',
        shopCode: '',
        discount: '',
        isCopySaleArea: '',
        customerGroupId: '',
        scopeType: '',

        isCopyBusArea: '',
        busAreaId: '',
        isCopyControlUser: '',
        controlUserTypes: '',
        isCopyControlRoster: '',

        controlRosterType: '',
        controlGroupId: '',
        isVirtualShop: '',
        startQty: '',

        personalLimitType: '',
        personalQty: '',
        ctime: '',
        creator: '',
        utime: '',
        updater: '',
      }
    }
  },
  methods: {
    validateNumber(rule, value, callback) {
      if (!/^[1-9]\d*$/.test(value) || !value) {

        return callback(new Error('请维护正整数'));
      }else if(Number(value)<0||Number(value)>100){
        return callback(new Error('请维护(0-100)正整数'));
      } else {
        callback();
      }
    },
    validateNumberbV2(rule, value, callback){
      if (!/^[1-9]\d*$/.test(value) || !value) {
        return callback(new Error('请维护正整数'));
        }else {
        callback();
        }
    },
    changeSupplyTypeEvent(value) {
      
      this.ruleForm.scopeType = value
    },
  async  UpdateOrCreateSetting() {
      this.$refs.ruleForm.validate(valid => {
        if (valid) {
          if(this.ruleForm.discount===""&&!this.ruleForm.inputDiscount){
            this.$message.warning('请选择可接受降价幅度')
            return
          }
          console.log(this.$refs.supplyChild.businessCircleInfo.busAreaId,123)
          const data = {
            id: this.ruleForm.id,
            baseId: this.$parent.baseId,
            discount: this.ruleForm.discount != 3 ? this.ruleForm.discount : this.ruleForm.inputDiscount,
            // isCopySaleArea: '',
            customerGroupId: this.$refs.supplyChild.peopleInfo.id,//人群id
            scopeType:  this.$refs.supplyChild.supplyInfo.scopeType,
            isCopyBusArea: this.$refs.supplyChild.supplyInfo.isCopyBusArea,//是否复制原品商圈
            busAreaId: this.$refs.supplyChild.businessCircleInfo.busAreaId,//商圈ID
            isCopyControlUser: this.$refs.supplyChild.supplyInfo.isCopyControlUser,//是否复制原品供货对象
            controlUserTypes: this.$refs.supplyChild.supplyInfo.controlUserTypes.join(","),//供货对象内容
            isVirtualShop: this.ruleForm.isVirtualShop,
            startQty: this.ruleForm.startQty == -1 ? '-1' : this.ruleForm.inputStartQty,
            personalLimitType: this.ruleForm.LimitType == -1 ? '-1' : this.ruleForm.personalLimitType,

          }
          console.log(data,123)
          if(data.isCopyBusArea==-1){
            data.busAreaId=-1
          }
          if(data.isCopyControlUser==-1){
            data.controlUserTypes=-1
          }
          // alert(this.ruleForm.scopeType)
          if(this.$refs.supplyChild.supplyInfo.scopeType ==-1){
            data.busAreaId="";
            data.controlUserTypes = [];       
            data.isCopyBusArea=""
            data.isCopyControlUser=""
            data.customerGroupId="";
        }
        if (this.$refs.supplyChild.supplyInfo.scopeType ==1) {
          data.busAreaId="";
          data.controlUserTypes = [];
          data.isCopyBusArea=""
          data.isCopyControlUser=""
        }
        if ( this.$refs.supplyChild.supplyInfo.scopeType === 2) {
          data.customerGroupId="";
        }
          console.log(data)
          if (data.id) {
            updateSetting(data).then(async res => {
              if (res.code == 1000) {
                this.$message.success('操作成功')
                this.dialogVisible = false
                this.$parent.loading=true
                await  freshFrame({ baseId: this.$parent.baseId })
                this.$emit('getLists')
              
               
              } else {
                this.$message.error(res.msg)
              }
            })
          } else {
            delete data.id
            createSetting(data).then(async res => {
              if (res.code == 1000) {
                this.$message.success('操作成功')
                this.dialogVisible = false
                this.$parent.loading=true
                await  freshFrame({ baseId: this.$parent.baseId })
                this.$emit('getLists')
               
              } else {
                this.$message.error(res.msg)
              }
            })

          }
        }else{
          return
        }
      })

    },
    open(data) {
      //赋值
      this.ruleForm={
        id: "",
        discount: 1, //可接受降价幅度
        inputDiscount: '',
        scopeType: -1, //供货信息配置方式
        isVirtualShop: -1,    //虚拟供应商
        startQty: -1, // 起拼数量
        inputStartQty: "",
        LimitType: -1, //单店限购类型
        inputLimitType: '',
        personalLimitType: ""
      }
      this.dialogVisible = true
      this.$nextTick(() => {
        this.ruleForm.id = data.id
        this.ruleForm.discount = data.discount
        this.$refs.supplyChild.peopleInfo.id = data.customerGroupId//人群id
        this.$refs.supplyChild.supplyInfo.scopeType = data.scopeType || -1 //是否复制
        this.$refs.supplyChild.supplyInfo.isCopyBusArea = (data.busAreaId!=-1&&data.busAreaId!=null)?2:-1//是否复制原品商圈
        this.$refs.supplyChild.businessCircleInfo.busAreaId = data.busAreaId//商圈ID
        this.$refs.supplyChild.supplyInfo.isCopyControlUser =( data.controlUserTypes!=-1&&data.controlUserTypes!=null)?2:-1//是否复制原品供货对象
        this.$refs.supplyChild.supplyInfo.controlUserTypes = data.controlUserTypes?data.controlUserTypes.split(",").map(i => Number(i)):[]//供货对象内容
        this.$refs.supplyChild.peopleInfo.name = data.customerGroupName
        this.$refs.supplyChild.businessCircleInfo.busAreaName = data.busAreaName
        this.ruleForm.isVirtualShop = data.isVirtualShop || -1
        this.ruleForm.startQty = data.startQty
        this.ruleForm.personalLimitType = data.personalLimitType
        if (this.ruleForm.discount != 0 && this.ruleForm.discount != 1 && data.discount) {
          this.ruleForm.discount = "3"
          this.ruleForm.inputDiscount = Math.floor(Number(data.discount)) 
        }else{
          this.ruleForm.discount = data.discount
        }
        if (data.startQty != -1) {
          this.ruleForm.startQty = "1"
          this.ruleForm.inputStartQty = data.startQty
        }else{
         
        }
        console.log(data.personalLimitType)
        if (data.personalLimitType != -1) {
          this.ruleForm.LimitType = "1"
        }else{
         
          this.ruleForm.personalLimitType = ""
        }
      })
    },
    handleClose(done) {
      this.dialogVisible = false;
    },
    addPeople() {
      this.dialogVisibleP = true;
    },
    onSelect(selectItem) {
      this.ruleForm.customerGroupName = selectItem.tagName;
      this.ruleForm.customerGroupId = selectItem.id;
    },

  }
}

</script>
<style scoped lang="scss">
.error-tip {
  ::v-deep   .el-form-item__error {
    left: 15px;

  }
}
</style>
