<template>
  <div class="wrap">
    <div class="Fsearch">
      <el-row
        type="flex"
        align="middle"
        justify="space-between"
        class="my-row"
      >
        <el-row
          type="flex"
          align="middle"
        >
          <span class="sign" />
          <div class="searchMsg">
            查询条件
          </div>
        </el-row>
      </el-row>
    </div>
    <el-row
      :gutter="20"
      style="padding: 0 20px"
      class="searchMy"
    >
      <el-form
        ref="ruleForm"
        :inline="true"
        :model="ruleForm"
        size="small"
      >
        <el-form-item>
          <el-input
            v-model="ruleForm.tempName"
            placeholder="请输入内容"
            size="small"
          >
            <template slot="prepend">
              运费名称
            </template>
          </el-input>
        </el-form-item>
        <el-form-item prop="templateType">
          <span
            class="search-title"
          >运费类型</span>
          <el-select
            v-model="ruleForm.templateType"
            size="small"
            placeholder="全部"
          >
            <el-option
              label="全部"
              :value="''"
            />
            <el-option
              label="自定义运费"
              :value="1"
            />
            <el-option
              label="商家承担运费"
              :value="2"
            />
          </el-select>
        </el-form-item>
        <el-form-item prop="time">
          <span
            class="search-title"
          >创建时间</span>
          <div style="display: table-cell; line-height: 24px">
            <el-date-picker
              v-model="time"
              type="daterange"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              :default-time="['00:00:00', '23:59:59']"
              @change="dateOnChange"
            />
          </div>
        </el-form-item>
        <!-- <el-form-item prop="branchCode">
          <span
            class="search-title"
          >区域</span>
          <el-select
            v-model="ruleForm.branchCode"
            size="small"
            placeholder="全部"
          >
            <el-option
              v-for="(item, index) in regionasListQuery"
              :key="index"
              :label="item.branchName"
              :value="item.branchCode"
            />
          </el-select>
        </el-form-item> -->
      </el-form>
      <el-row style="text-align: right;padding-bottom: 10px">
        <div class="FsearchBtn">
          <el-button
              type="primary"
              size="small"
              @click="toSerachForm()"
          >
            查询
          </el-button>
          <el-button
              size="small"
              @click="resetForm()"
          >
            重置
          </el-button>
        </div>
      </el-row>
      <el-divider />
    </el-row>
    <el-row class="freightList">
      <div class="Fsearch">
        <el-row
          type="flex"
          align="middle"
          justify="space-between"
        >
          <el-row
            type="flex"
            align="middle"
          >
            <span class="sign" />
            <div class="searchMsg">
              运费模板基本信息
              <el-tooltip
                class="item"
                effect="dark"
                placement="top"
              >
                <template #content>
                  <span style="font-size: 14px">运费规则</span>
                  <br>1、可按照省份分别设置运费模板，同时可设置不限区域的运费模板
                  <br>2、某省未设置运费模板时，以不限区域运费模板为准；反之以省运费模板为准
                  <br>3、省、全国通用均未设置模板时，默认店铺包邮
                  <br>4、同一区域只能设置一个运费模板
                </template>
                <p class="span-tip">
                  ?
                </p>
              </el-tooltip>
            </div>
          </el-row>
          <div class="FsearchBtn">
            <el-button
              v-permission="['shop_freight_add']"
              type="primary"
              size="small"
              @click="showAddTemp"
            >
              新增运费
            </el-button>
          </div>
        </el-row>
      </div>
      <el-table
        v-loading="isLoading"
        :data="tableData"
        stripe
        border
        fit
        style="width: 100%"
        :header-cell-style="{ background: '#F9F9F9', color: '#666666' }"
      >
        <el-table-column
          prop="tempName"
          label="运费名称"
          width="150"
        />
        <!-- <el-table-column
          label="区域"
        >
          <template slot-scope="scope">
            <div>
              {{
                scope.row.areaNames|formateQ
              }}
            </div>
          </template>
        </el-table-column> -->
        <el-table-column
          width="150"
          prop="templateType"
          label="运费类型"
        >
          <template slot-scope="scope">
            <div>
              {{
                { 1: '自定义运费', 2: '商家承担运费' }[scope.row.templateType]
              }}
            </div>
          </template>
        </el-table-column>
        <el-table-column
          prop="postageFee"
          label="运费详情"
        >
          <template slot-scope="scope">
            <div style="white-space: pre-wrap">
              {{
                scope.row.templateType === 2
                  ? '商家承担运费'
                  : `1、满${scope.row.freePostageAmount}元包邮${'\n'}
                     2、不满足条件时收运费${scope.row.postageFee}元${'\n'}
                     3、低于${scope.row.lowBuyAmount}不允许下单`
              }}
            </div>
          </template>
        </el-table-column>
        <el-table-column
          prop="updateTime"
          label="操作时间"
        >
          <template slot-scope="scope">
            <div>{{ scope.row.updateTime | formatDate }}</div>
          </template>
        </el-table-column>
        <el-table-column
          fixed="right"
          label="操作"
          width="120"
        >
          <template slot-scope="scope">
            <el-button
              type="text"
              size="small"
              @click="editorClick(scope.row, 'see')"
            >
              详情
            </el-button>
            <el-button
              v-permission="['shop_freight_edit']"
              type="text"
              size="small"
              @click="editorClick(scope.row, 'edit')"
            >
              编辑
            </el-button>
            <el-button
              v-permission="['shop_freight_delete']"
              type="text"
              size="small"
              @click="deleteClick(scope.row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <div
        v-if="total != 0"
        class="pagination-container"
      >
        <div class="pag-text">
          共 {{ total }} 条数据，每页{{ ruleForm.pageSize }}条，共{{
            Math.ceil(total / ruleForm.pageSize)
          }}页
        </div>
        <el-pagination
          background
          :page-sizes="pageSizes"
          :page-size="ruleForm.pageSize"
          :current-page="ruleForm.pageNum"
          layout="sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-row>
    <freightDialog
      v-if="addDialogForm"
      :freight-dialog-type="freightDialogType"
      :freight-detail="freightDetail"
      @cancellationBtn="cancellationBtn"
      @refreshList="refreshList"
    />
  </div>
</template>
<script>
import {
  branchsList,
  branchsListQuery,
  freightTemplateList,
  searchTemplateData,
  deleteFreightTemplate,
} from '@/api/freightTemplate/index';
import freightDialog from './freightDialog.vue';

export default {
  components: { freightDialog },
  name: 'freightManagement',
  filters: {
    formatDate(value) {
      const date = new Date(value);
      const y = date.getFullYear();
      let MM = date.getMonth() + 1;
      MM = MM < 10 ? `0${MM}` : MM;
      let d = date.getDate();
      d = d < 10 ? `0${d}` : d;
      let h = date.getHours();
      h = h < 10 ? `0${h}` : h;
      let m = date.getMinutes();
      m = m < 10 ? `0${m}` : m;
      let s = date.getSeconds();
      s = s < 10 ? `0${s}` : s;
      return `${y}-${MM}-${d} ${h}:${m}:${s}`;
    },
    formateQ(value) {
      let str = '';
      if (value && value.length > 0) {
        value.map((item) => {
          str += `${item},`;
        });
        return str;
      }
      return str;
    },
  },
  data() {
    return {
      isLoading: false,
      addDialogForm: false,
      time: '',
      ruleForm: {
        tempName: '',
        templateType: '',
        tempStatus: '',
        branchCode: null,
        startTime: '',
        endTime: '',
        // 当前页
        pageNum: 1,
        // 当前每页显示多少条数据
        pageSize: 10,
      },
      total: 0,
      pageSizes: [10, 20, 30, 40],
      regionasList: [],
      // regionasListQuery: [],
      tableData: [],
      freightDetail: {},
      freightDialogType: 'add', // 运费弹框类型
    };
  },
  created() {
    // this.queryRegional();
    // this.getQueryList();
    this.getFreightData();
  },
  methods: {
    refreshList() {
      this.addDialogForm = false;
      this.getFreightData();
    },
    showAddTemp() {
      this.addDialogForm = true;
      this.freightDialogType = 'add';
    },
    getFreightData() {
      this.isLoading = true;
      const queryInfo = this.ruleForm;
      freightTemplateList(queryInfo)
        .then((res) => {
          this.isLoading = false;
          if (res.success) {
            this.tableData = res.data.list || [];
            this.total = res.data.total || 0;
          }
        })
        .catch((err) => {
          this.isLoading = false;
          this.$message.error(err);
        });
    },
    // queryRegional() {
    //   branchsList().then((res) => {
    //     this.regionasList = res.result;
    //   });
    // },
    // getQueryList() {
    //   branchsListQuery().then((res) => {
    //     this.regionasListQuery = res.result;
    //   });
    // },
    handleSizeChange(val) {
      this.ruleForm.pageSize = val;
      this.getFreightData();
    },
    handleCurrentChange(val) {
      this.ruleForm.pageNum = val;
      this.getFreightData();
    },
    cancellationBtn() {
      this.addDialogForm = false;
    },
    dateOnChange(val) {
      if (val === null) {
        this.ruleForm.startTime = '';
        this.ruleForm.endTime = '';
        val = '';
        this.time = '';
      } else if (typeof val[0] === 'string') {
        this.ruleForm.startTime = val[0];
        this.ruleForm.endTime = val[1];
      }
    },
    toSerachForm() {
      this.ruleForm.pageNum = 1;
      this.getFreightData();
    },
    resetForm() {
      this.ruleForm.tempName = '';
      this.ruleForm.templateType = '';
      this.ruleForm.tempStatus = '';
      this.ruleForm.branchCode = null;
      this.ruleForm.startTime = '';
      this.ruleForm.endTime = '';
      this.time = '';
      this.ruleForm.pageNum = 1;
      this.getFreightData();
    },
    editorClick(lineData, type) {
      const tempId = lineData.id;
      this.freightDialogType = type;
      searchTemplateData({ tempId }).then((res) => {
        this.freightDetail = res.data;
        this.addDialogForm = true;
      });
    },
    deleteClick(lineData) {
      console.log(lineData, 'sss');
      const temp = lineData.id;
      this.$confirm('是否删除运费模板?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        deleteFreightTemplate({ tempId: temp }).then((res) => {
          if (res.code === 0) {
            this.$message.success('删除运费模板成功');
            this.getFreightData();
          } else {
            this.$message.error({ message: res.message, offset: 200 });
          }
        });
      }).catch(() => {});
    },
  },
};
</script>
<style lang="scss" scoped>
.searchMy ::v-deep  .el-select {
  display: table-cell;
  width: 100%;
}
::v-deep  .el-input-group__prepend {
  background: none;
  color: #333333;
  padding: 0 10px;
}
.Fsearch {
  padding: 10px 20px;
  font-weight: 500;
  .searchMsg {
    font-weight: 700;
    width: 200px;
  }
  .sign {
    display: inline-table;
    width: 3px;
    height: 13px;
    background: linear-gradient(360deg, #1d69c4 1%, #8bbdfc 99%);
    border-radius: 2px;
    margin-right: 8px;
  }
}
.freightList {
  padding: 0 20px;
  .Fsearch {
    padding: 0px 0;
    font-weight: 700;
  }
  .pag-text {
    vertical-align: middle;
    float: left;
    font-size: 12px;
    color: #999999;
    padding-top: 8px;
  }
}
.searchMy ::v-deep  .el-form-item__label {
  margin-left: 20px;
  padding: 0;
}
.searchMy ::v-deep  .el-input__inner {
  border-radius: 0 4px 4px 0;
}
.searchMy ::v-deep  .el-date-editor{
  width: 100%;
}
.search-title {
  display: table-cell;
  padding: 0 10px;
  text-align: center;
  border: 1px solid #dcdfe6;
  height: 30px;
  line-height: 30px;
  vertical-align: middle;
  border-right: none;
  border-radius: 4px 0 0 4px;
  color: #333333;
  white-space: nowrap;
}
.searchMy ::v-deep  .el-select {
  display: table-cell;
  width: 100%;
}
.searchMy ::v-deep  .el-form-item__content{
  width: 100%;
}
.searchMy ::v-deep  .el-form-item--small.el-form-item{
  margin-bottom: 10px;
  width: 32%;
}
.searchMy ::v-deep  .el-form-item--small .el-form-item__content{
  line-height: 30px;
  width: 100%;
}
.searchMy ::v-deep  .el-input-group__prepend {
  background: none;
  color: #333333;
  padding: 0 10px;
}
.Fsearch {
  padding: 0px 20px 10px;
  font-weight: 500;
  .searchMsg {
    font-weight: 700;
    width: 200px;
  }
  .sign {
    display: inline-table;
    width: 3px;
    height: 13px;
    background: linear-gradient(360deg, #1d69c4 1%, #8bbdfc 99%);
    border-radius: 2px;
    margin-right: 8px;
  }
}
::v-deep  .el-divider--horizontal {
  margin: 0px;
}
::v-deep  .el-form-item__error {
  margin-left: 40px;
}
.searchMy ::v-deep  .el-form-item--small.el-form-item{
  width: 24%;
}
.Fsearch ::v-deep  .el-dialog{
  width: 60%;
}
.span-tip{
  display: inline-block;
  width: 20px;
  height: 20px;
  font-size: 14px;
  border: 1px solid #4183d5;
  color: #4183d5;
  text-align: center;
  line-height: 20px;
  border-radius: 50%;
  margin-left: 5px;
}
::v-deep  .pagination-container{
  margin: 15px 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
