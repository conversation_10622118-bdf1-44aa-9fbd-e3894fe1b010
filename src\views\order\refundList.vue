<template>
  <div class="divBox">
    <div class="orderTitle">
      <span>申请退款</span>
      <el-button type="primary" size="small" @click="goBack"> 返回 </el-button>
    </div>
    <div style="padding: 15px 0; border-bottom: 1px solid #dddddd; border-top: 1px solid #dddddd">
      <el-row>
        <span>注意：发起退款前请务必与客户沟通，避免引起客诉</span>
      </el-row>
      <el-row class="paddingDiv">
        <span>订单编号：{{ orderNo }}</span>
        <span style="padding-left: 30px">客户名称：{{ merchantName }}</span>
      </el-row>
    </div>
    <!-- <refundListNewVue
      v-if="pageData.oldOrNew == 1"
      :orderNo="orderNo"
      :refundData="refundData"
      :plainList="plainList"
    ></refundListNewVue> -->
    <template>
      <el-row style="padding-top: 20px">
        <el-table
          ref="multipleTable"
          :data="refundData"
          border
          style="width: 100%"
          :row-key="(row) => row.orderNo"
          @selection-change="handleSelectionChange"
          @select-all="(val) => (freightRefund.checked = val.length > 0)"
        >
          <el-table-column type="selection" width="55" :reserve-selection="true" />
          <el-table-column label="商品名称" prop="productName" />
          <el-table-column label="规格" prop="spec" />
          <el-table-column label="厂家" prop="manufacturer" width="200" />
          <el-table-column label="中包装数量" prop="mediumPackageNum" />
          <el-table-column label="是否可拆零" prop="isSplitDesc" />
          <el-table-column label="采购数量" prop="productAmount" />
          <el-table-column label="发货数量" prop="realSendNum">
            <template slot-scope="scope">
              <span :style="scope.row.realSendNum < scope.row.productAmount ? 'color:red' : ''">{{
                scope.row.realSendNum
              }}</span>
            </template>
          </el-table-column>
          <el-table-column label="可退数量" prop="canRefundAmount" />
          <el-table-column label="本次退货数量">
            <template slot-scope="scope">
              <el-input
                v-model="scope.row.thisRefundAmount"
                type="number"
                size="small"
                @blur="changeNum(scope.row)"
              />
            </template>
          </el-table-column>
        </el-table>
      </el-row>
      <el-row style="padding-top: 20px">
        <el-form ref="form" :model="form" label-width="100px">
          <el-form-item label-width="20px" v-if="freightRefund.type == 1">
            <el-checkbox v-model="freightRefund.checked">
              退运费：{{ freightRefund.amount }}元
            </el-checkbox>
          </el-form-item>
          <el-form-item label="退款原因：">
            <el-select v-model="form.refundReason" placeholder="请选择退款原因" size="small">
              <el-option
                v-for="(item, index) in plainList"
                :key="index"
                :label="item.showText"
                :value="item.showText"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="退款说明：">
            <el-input v-model="form.refundExplain" style="width: 50%" type="textarea" />
          </el-form-item>
        </el-form>
      </el-row>
      <el-row style="padding: 20px 0; text-align: right">
        <el-button type="primary" size="mini" @click="confirmD"> 提 交 </el-button>
      </el-row>
      <el-dialog
        :visible="refundReason.status"
        title="退款提示"
        width="400px"
        @close="refundReason.status = false"
      >
        <div style="padding: 0 20px">
          <p style="display: flex; justify-content: space-between">
            <span>退款商品种类数:{{ refundReason.refundVarietyNum }}</span>
            <span>退款商总数量:{{ refundReason.refundTotalCount }}</span>
          </p>
          <p style="display: flex; justify-content: space-between">
            <span>商品退款金额:{{ refundReason.skuRefundFee }}</span>
            <span style="color: red">运费退款金额:{{ refundReason.freightRefundFee }}</span>
          </p>
          <p style="display: flex; justify-content: space-between">
            <span style="color: red">退款总金额:{{ refundReason.refundFee }}</span>
            <span></span>
          </p>
        </div>
        <div slot="footer">
          <el-button
            type="primary"
            size="small"
            @click="
              sendData(refundReason.params)
              refundReason.status = false
            "
            >确定</el-button
          >
        </div>
      </el-dialog>
    </template>
  </div>
</template>

<script>
import {
  queryCanRefundProductDetail,
  listRefundReason,
  getOrderRefundAmount,
  submitApplyRefundBySeller
} from '@/api/order/index'
// import refundListNewVue from './refundListNew.vue'

export default {
  name: 'orderListRefund',
  // components: { refundListNewVue },
  data() {
    return {
      pageData: {
        oldOrNew: 1 // 灰度方案 判断使用老界面还是新界面
      }, // 页面数据

      refundData: [],
      selectList: [],
      orderNo: '',
      merchantName: '',
      freightRefund: {
        type: 0,
        amount: '',
        checked: true
      },
      form: {
        refundReason: '',
        refundExplain: null
      },
      refundReason: {
        status: false,
        freightRefundFee: '',
        refundFee: '',
        refundTotalCount: '',
        refundVarietyNum: '',
        skuRefundFee: '',
        params: {}
      },
      plainList: [],
      tipHeight: document.documentElement.clientHeight / 3,
      routerObj: ''
    }
  },
  created() {
    this.queryInfo()
  },
  activated() {
    if (this.$refs.multipleTable) {
      this.$refs.multipleTable.clearSelection()
    }
    this.selectList = []
    if (this.routerObj && JSON.stringify(this.routerObj) !== JSON.stringify(this.$route.query)) {
      this.queryInfo()
    }
  },
  methods: {
    goBack() {
      if (this.$refs.multipleTable) {
        this.$refs.multipleTable.clearSelection()
      }
      this.selectList = []
      this.$router.push('/orderList')
    },
    queryInfo() {
      this.selectList = []
      this.routerObj = this.$route.query
      const { orderNo } = this.routerObj
      this.orderNo = orderNo
      listRefundReason({ orderNo }).then((res) => {
        if (res.code === 0) {
          this.plainList = res.result
        }
      })
      queryCanRefundProductDetail({ orderNo }).then((res) => {
        if (res.code === 0) {
          if (res.result) {
            this.merchantName = res.result.merchantName
            this.refundData = res.result.orderDetails?.map((item) => {
              item.thisRefundQuantity = null
              return item
            })
            this.freightRefund.amount = res.result.freightRefundAmount
            this.freightRefund.type = res.result.freightRefundType
          }
        }
      })
    },
    handleSelectionChange(val) {
      this.selectList = val
      console.log(this)
    },
    confirmD() {
      const { orderNo } = this.$route.query
      if (this.selectList.length < 1 && !this.freightRefund.checked) {
        this.$message.warning({ message: '请先选择退款商品', offset: this.tipHeight })
      } else {
        const { refundExplain, refundReason } = this.form
        if (!refundReason) {
          this.$message.warning({ message: '请先选择退款原因', offset: this.tipHeight })
          return
        }
        const params = {
          detailList: this.selectList,
          refundExplain,
          refundReason,
          orderNo,
          refundCensusType:
            this.freightRefund.checked && this.selectList.length < 1
              ? 2
              : !this.freightRefund.checked && this.selectList.length >= 1
              ? 1
              : 3
        }
        getOrderRefundAmount(params).then((res) => {
          if (res.code === 0) {
            this.refundReason = {
              ...res.result,
              params: params,
              status: true
            }
          } else {
            this.$message.error({ message: res.result, offset: this.tipHeight })
          }
          /* const con = `退款商品种类数${res.result.refundVarietyNum}               退款商品总数量${res.result.refundTotalCount}<br />
                         商品退款金额${res.result.skuRefundFee}               运费退款金额${res.result.freightRefundFee}<br />
                         `;
            this.$alert(con, '退款提示', {
              confirmButtonText: '确定',
              callback: (action) => {
                action === 'confirm' ? this.sendData(params) : '';
              },
            });
          } else {
            this.$message.error({ message: res.result, offset: this.tipHeight });
          } */
        })
      }
    },
    sendData(data) {
      submitApplyRefundBySeller(data).then((res) => {
        if (res.code === 0) {
          if (this.$refs.multipleTable) {
            this.$refs.multipleTable.clearSelection()
          }
          this.selectList = []
          this.$message.success({ message: '提交成功', offset: this.tipHeight })
          window.openTab('/orderList')
        } else {
          this.$message.error({ message: res.msg, offset: this.tipHeight })
        }
      })
    },
    changeNum(value) {
      const tempAmount = parseFloat(value.thisRefundAmount + '')
      const regex = /^\d+$/
      if (!regex.test(tempAmount)) {
        this.$message.warning({ message: '请输入整数', offset: this.tipHeight })
        value.thisRefundAmount = ''
      }
      if (value.thisRefundAmount < 0) {
        this.$message.warning({ message: '本次退货数量需为正整数', offset: this.tipHeight })
        value.thisRefundAmount = ''
      }
      if (value.thisRefundAmount > value.canRefundAmount) {
        this.$message.warning({ message: '本次退货数量应该<=可退数量', offset: this.tipHeight })
        value.thisRefundAmount = ''
      }
    }
  }
}
</script>

<style scoped lang="scss">
.paddingDiv {
  padding-top: 15px;
}
.divBox {
  padding: 0 20px;
  .orderTitle {
    padding: 10px 0;
    display: flex;
    justify-content: space-between;
    border-bottom: 1px solid #dddddd;
    span {
      font-size: 20px;
      color: #333333;
      font-weight: bold;
    }
  }
}
</style>
