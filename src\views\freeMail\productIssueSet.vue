<template>
	<common-header :showFold="false" title="商品上架设置">
		<div class="header-tab" v-if="!shopConfig.isFbp">
			<div class="header-tab-box">
				<div class="header-tab-item" v-for="(item, index) in tabData" :key="index" @click="changeTab(index)" :class="{'header-tab-item-check': tabIndex == index}">{{ item }}</div>
			</div>
		</div>
		<div class="form-content" v-if="tabIndex == 0 && !shopConfig.isFbp">
			<el-form label-width="180px">
				<el-form-item label="普通商品来货上下架：">
					<el-radio v-model="form.commonSaleOutAutoUp" :label="0">关闭</el-radio>
					<el-radio v-model="form.commonSaleOutAutoUp" :label="1">开启</el-radio>
				</el-form-item>
				<el-form-item label="拼团商品来货上下架：">
					<el-radio v-model="form.ptSaleOutAutoUp" :label="0">关闭</el-radio>
					<el-radio v-model="form.ptSaleOutAutoUp" :label="1">开启</el-radio>
				</el-form-item>
				<el-form-item label="批购包邮商品来货上下架：">
					<el-radio v-model="form.wholesaleSaleOutAutoUp" :label="0">关闭</el-radio>
					<el-radio v-model="form.wholesaleSaleOutAutoUp" :label="1">开启</el-radio>
				</el-form-item>
				<p style="color:red;margin:30px 0;">开启后，因缺货下架的商品在有库存后将自动上架。</p>
				<el-form-item>
					<el-button size="small" @click="go('/bulkPurchaseFreeMail', null, true)">取消</el-button>
					<el-button size="small" type="primary" @click="commit">确定</el-button>
				</el-form-item>
			</el-form>
		</div>
		<div class="form-content" v-else-if="tabIndex == 1">
			<el-form label-width="200px">
				<el-form-item label="拼团商品加购整单包邮：">
					<el-radio v-model="addCartForm.isGroupBuyingFreeShipping" :label="0">否</el-radio>
					<el-radio v-model="addCartForm.isGroupBuyingFreeShipping" :label="1">是</el-radio>
				</el-form-item>
				<el-form-item label="批购包邮商品加购整单包邮：">
					<el-radio v-model="addCartForm.isWholesaleFreeShipping" :label="0">否</el-radio>
					<el-radio v-model="addCartForm.isWholesaleFreeShipping" :label="1">是</el-radio>
				</el-form-item>
				<p style="color:red;margin:30px 0;">提示：选择“是”，则对店铺内拼团/批购包邮商品加购后，店铺内所有商品均免邮，请谨慎设置！</p>
				<el-form-item>
					<el-button size="small" @click="go('/bulkPurchaseFreeMail', null, true)">取消</el-button>
					<el-button size="small" type="primary" @click="addCartCommit">确定</el-button>
				</el-form-item>
			</el-form>
		</div>
	</common-header>
</template>

<script>
import commonHeader from '../afterSaleManager/components/common-header.vue';
import { skuAutoUpInfoV2, updateSkuAutoUpInfoV2, getFreeShopConfig, setFreeShopConfig } from '../../api/product/index'
import { mapState } from 'vuex'
export default {
	components: {
		commonHeader
	},
	data() {
		return {
			tabIndex: 0,
			tabData: ["来货是否自动上架", "拼团/批购商品加购是否整单包邮"],
			form: {
				commonSaleOutAutoUp: 1,
				ptSaleOutAutoUp: 1,
				wholesaleSaleOutAutoUp: 1
			},
			addCartForm: {
				isGroupBuyingFreeShipping: 0,
				isWholesaleFreeShipping: 0
			}
		}
	},
	computed: {
    	...mapState('app', ['shopConfig']),
	},
	activated() {
		skuAutoUpInfoV2().then(res => {
			if (res.success) {
				this.form.commonSaleOutAutoUp = res.data.commonSaleOutAutoUp;
				this.form.ptSaleOutAutoUp = res.data.ptSaleOutAutoUp;
				this.form.wholesaleSaleOutAutoUp = res.data.wholesaleSaleOutAutoUp;
			}
		}),
		getFreeShopConfig().then(res => {
			if (res.success) {
				this.addCartForm.isGroupBuyingFreeShipping = res.data.isGroupBuyingFreeShipping;
				this.addCartForm.isWholesaleFreeShipping = res.data.isWholesaleFreeShipping;
			}
		})
	},
	mounted() {
		this.$nextTick(() => {
			if (this.shopConfig.isFbp) {
				this.tabIndex = 1;
			}
		})
	},
	methods: {
		changeTab(index) {
			this.tabIndex = index;
		},
		commit() {
			updateSkuAutoUpInfoV2(this.form).then(res => {
				if(res.success) {
					this.$message.success("设置成功")
					this.go('/bulkPurchaseFreeMail', null, true);
				}
			})
		},
		go(to, query, close) {
			if (close) {
				window.closeTab(this.$route.fullPath, true);
			}
			setTimeout(() => {
				window.openTab(to, query ? query : {});
			}, 0)
		},
		addCartCommit() {
			setFreeShopConfig(this.addCartForm).then(res => {
				if(res.success) {
					this.$message.success("设置成功")
					this.go('/bulkPurchaseFreeMail', null, true);
				}
			})
		}
	}
}
</script>

<style lang="scss">
.header-tab {
	display: inline-block;
	height: 44px;
	border: 1px solid #eee;
	border-radius: 4px;
	margin-bottom: 20px;
	.header-tab-box {
		display: flex;
		align-items: center;
		height: 100%;
		.header-tab-item {
			cursor: pointer;
			height: 100%;
			display: flex;
			align-items: center;
			justify-content: center;
			padding: 0 20px;
			box-sizing: border-box;
			&:first-child {
				border-right: 1px solid #eee;
			}
		}
		.header-tab-item-check {
			// background: #eee;
			color: #4183d5;
		}
	}
}
</style>