<template>
	<common-header title="批购包邮单品销售类型" :showFold="false" :shouHeightLine="false">
		<el-form ref="form" label-width="140px" :rules="rules" v-loading="loading" :model="form">
			<el-form-item label="上下架时间：" prop="time">
				<date v-model="form" style="width:400px" endProp="etime" startProp="stime"></date>
			</el-form-item>
			<el-form-item label="申请方式：">
				<div>
					<el-radio v-model="radio.one" label="1">
						<span>单个申请</span>
						<span>
							<el-input :disabled="radio.one == '2'" type="text" size="mini" v-model="productCode" style="width:150px;margin:0 10px;" placeholder="请输入普通商品编码"></el-input>
							<el-button :disabled="radio.one == '2'" size="mini" type="primary" @click="create">创建</el-button>
						</span>
					</el-radio>
				</div>
  				<div>
					<el-radio v-model="radio.one" label="2" style="display:flex; margin-top: 10px;">
						<div style="display: flex; align-items: flex-start;">
							<span>
								批量申请
							</span>
							<span style="margin-left:10px;">
								<el-upload ref="excludeImport" action="" :http-request="fileChange" :file-list="fileList" :show-file-list="true" :limit="1" :on-remove="() => form.file = null" accept=".xls, .xlsx, .XLS, .XLSX">
									<el-button size="small" :disabled="radio.one == '1'" type="primary" style="margin-right: 10px;">选择文件</el-button>
								</el-upload>
							</span>
						</div>
					</el-radio>
					<div style="line-height:normal;color:#b2b2b2;">1、请上传编辑好的xlsx文件，最大允许1000条；</div>
					<div style="line-height:normal;color:#b2b2b2;">2、业务商圈、供货对象和黑白名单相同的可批量提交，否则请分批提交</div>
					<div style="line-height:normal;color:#b2b2b2;">
						<span>3、模板文件 </span>
						<el-link @click="download" download="" :underline="false" type="primary">下载</el-link>
						<!-- <a href="/report/wholesale/apply/getTemplateForBatchImport" target="_blank" download="批购包邮批量申请模板.xlsx"></a> -->
					</div>
				</div>
			</el-form-item>
			<SupplyTypeConfig v-if="radio.one == '2'" v-model="supplyInfo" :baseCustomerGroupName="activityInfo.limitCustomerGroupName" :baseCustomerGroupId="activityInfo.limitCustomerGroupId"  ref="supplyTypeInfo" />
			<el-form-item v-if="radio.one == '2'">
				<el-button size="mini" @click="go('/putProduct')">取消</el-button>
				<el-button size="mini" type="primary" @click="submit">提交</el-button>
			</el-form-item>
		</el-form>
	</common-header>
</template>

<script>
import commonHeader from '../../afterSaleManager/components/common-header.vue'
import {
  getSkuByBarcode,
  batchImportAct,
  download
} from '../../../api/putProduct/index';
import SupplyTypeConfig from '../component/supplyTypeConfig.vue';
import date from '../component/date.vue'
export default {
	name: 'putProduct-multiple',
	components: {
		commonHeader,
		date,
		SupplyTypeConfig
	},
	data() {
		return {
			loading: false,
			productCode: '',
			form: {
				stime: '',
				etime: '',
				file: '',
			},
			radio: {
				one: '2',
			},
			activityInfo: {
				limitCustomerGroupId: 0,
				limitCustomerGroupName: ''
			},
			rules: {
				time: { validator: this.dateValidator, trigger: 'blur',required: true }
			},
			supplyInfo: {}
		}
	},
	computed: {
		fileList() {
			return this.form.file ? [this.form.file] : [];
		}
	},
	created() {
		this.init();
	},
	activated() {
		this.productCode = '';
		this.form.file = null;
		console.log(this.$refs.excludeImport);
		this.$refs.excludeImport.clearFiles()
	},
	methods: {
		init() {
			this.form.stime = Date.now();
			this.form.etime = this.form.stime + 60 * 60 * 24 * 365 * 2 * 1000;
		},
		dateValidator(rule, value, callback) {
			if (!this.form.stime) {
				callback(new Error('请输入开始时间！'));
			} else if (!this.form.etime) {
				callback(new Error('请输入结束时间！'));
			} else {
				callback();
			}
		},
		go(to, query) {
			let form = this.$route.query.form;
			window.closeTab(this.$route.fullPath, true);
			if(form == 'freeMail') {
				to = "/bulkPurchaseFreeMail"
			}
			setTimeout(() => {
				window.openTab(to, query ? query : {});
			}, 0)
		},
		create() {
			if (this.loading) return;
			this.loading = true;
			getSkuByBarcode({
				barcode: this.productCode
			}).then(res => {
				if (res.code == 1000) {
					window.sessionStorage.setItem('formData', JSON.stringify({
						stime: this.form.stime,
						etime: this.form.etime,
						...res.data.csu || {}
					}))
					// window.openTab('/putProduct/single', {
					// 	fromType: 'edit'
					// });
					// this.$router.push({ path: '/putProduct/single', query: {fromType: 'edit'} }); // 在当前页跳转
					window.closeTab(this.$route.fullPath, true);
					setTimeout(() => {
						window.openTab('/putProduct/single', { fromType: 'edit' });
					}, 0)
				} else {
					this.$message.error(res.msg)
				}
			}).finally(() => {
				this.loading = false;
			})
		},
		download() {
			download().then(res => {
				const blob = new Blob([res], {
					type: 'application/octet-stream;charset=UTF-8;'
				})
				const url = URL.createObjectURL(blob);
				const a = document.createElement("a");
				a.href = url;
				a.download = "批购包邮批量申请模板.xls";
				a.target = "_blank";
				a.click();
				URL.revokeObjectURL(url);
			})
		},
		submit() {
			if (this.loading) return;
			const params = {
				...this.form,
				...this.supplyInfo
			}
			if (!params.file) {
				this.$message.error("请先选择文件！")
				return ;
			}
			this.$refs.form.validate((valid, object) => {
				if (valid) {
					if (params.stime + (60 * 60 * 24 * 365 * 2 * 1000) < params.etime) {
						this.$message.error("上下架时间间隔需小于两年")
						return 
					}
					this.loading = true;
					batchImportAct(params, 'add').then(res => {
						/* if (res.success) {
							this.$message.success("提交成功！")
							this.go('/putProduct');
						} else {
							this.$confirm(res.msg, '提示', {
								confirmButtonText: '确定'
							})
						} */
						if (res.code == 1000) {
							const excelRowNumLimitFailureMsg = ((res.data || {}).importGroupProduct || {}).excelRowNumLimitFailureMsg || '';
							if (excelRowNumLimitFailureMsg) {
								this.$message.error(excelRowNumLimitFailureMsg)
							} else {
								let con = ''
								if (res.data.importGroupProduct && res.data.importGroupProduct.failureNum > 0) {
									con = `<p>${res.data.importGroupProduct.successNum}个商品发布成功，${res.data.importGroupProduct.failureNum}个商品导入失败，原因见错误文件<br><a style="color: #ff0021" href="${res.data.importGroupProduct.failureExcelFileDownloadUrl}" download="批量导入批购包邮商品">批量导入批购包邮商品</a></p>`
								} else {
									con = `<p>${res.data.importGroupProduct.successNum}个商品发布成功，${res.data.importGroupProduct.failureNum}个商品导入失败</p>`
								}
								this.$confirm(con, '提示', {
									confirmButtonText: '确定',
									dangerouslyUseHTMLString: true,
									cancelButtonText: '取消'
								}).then(() => {
									this.form.file = null;
									this.go('/productList', { refresh: '1' });
								}).catch(() => {
									this.form.file = null;
								})
							}
						} else {
							this.$message.error(res.msg, '提示')
						}
					}).finally(() => {
						this.loading = false;
					})
				} else {
					this.$message.error(object.time[0].message)
					return false;
				}
			})
			
		},
		fileChange(res) {
			this.form.file = res.file;
		}
	}	
}
</script>

<style>

</style>