<template>
  <el-dialog
    :title="modifyConfig.title"
    :visible="dialogVisible"
    width="45%"
    :before-close="handleClose"
  >
    <el-form ref="ruleForm" :model="formModel" :rules="rules" label-width="130px">
      <el-form-item
        v-if="modifyConfig.modifyType==='suggestPrice'"
        label="单体采购价"
        prop="suggestPrice"
      >
        <el-input
          v-model="formModel.suggestPrice"
          @input="checkPrice"
          style="width: 60%"
          placeholder="请输入大于0的数字，限2位小数"
          :disabled="modifyConfig.priceType===2"
          @blur="inputBlur(formModel.suggestPrice,'suggestPrice')"
        />
      </el-form-item>
      <el-form-item
        v-if="modifyConfig.modifyType==='suggestPrice'&&modifyConfig.priceType===2"
        label="单体毛利率:"
        prop="grossProfitMargin"
      >
        <el-input
          v-model.trim="formModel.grossProfitMargin"
          style="width: 60%"
          placeholder="请输入大于0小于100的数字，限2位小数"
          onkeyup="value=value.replace(/^\D*(\d{0,2}(?:\.\d{0,2})?).*$/g, '$1')"
          @blur="formModel.grossProfitMargin=$event.target.value"
          @change="grossProfitMarginChange"
        ></el-input>
        <div style="font-size:12px;color:#ff2121">
          <span>示例：若毛利为10个点，毛利率应填10.00，而不是0.1</span>
        </div>
      </el-form-item>
      <el-form-item v-if="modifyConfig.modifyType==='suggestPrice'" label="连锁采购价" prop="chainPrice">
        <el-input
          v-model="formModel.chainPrice"
          @input="checkPrices"
          style="width: 60%"
          placeholder="请输入大于等于0的数字，限2位小数"
          :disabled="modifyConfig.priceType===2"
          @blur="inputBlur(formModel.chainPrice,'chainPrice')"
        />
        <div
          style="font-size:12px;line-height:30px"
          v-if="modifyConfig.modifyType==='suggestPrice'"
        >
          <span>温馨提示：</span>
          <br />
          <span class="colorRed">连锁采购价</span>目前只针对以下
          <span class="colorRed">连锁客户类型</span>生效，连锁客户类型包含
          <span class="colorRed">"连锁总部、药品批发、批发（商业）"</span>
        </div>
      </el-form-item>
      <el-form-item
        v-if="modifyConfig.modifyType==='suggestPrice'&&modifyConfig.priceType===2"
        label="连锁毛利率:"
        prop="chainGrossProfitMargin"
      >
        <el-input
          v-model.trim="formModel.chainGrossProfitMargin"
          style="width: 60%"
          placeholder="请输入大于0小于100的数字，限2位小数"
          onkeyup="value=value.replace(/^\D*(\d{0,2}(?:\.\d{0,2})?).*$/g, '$1')"
          @blur="formModel.chainGrossProfitMargin=$event.target.value"
          @change="chainGrossProfitMarginChange"
        ></el-input>
        <div style="font-size:12px;color:#ff2121">
          <span>示例：若毛利为10个点，毛利率应填10.00，而不是0.1</span>
        </div>
      </el-form-item>
      <el-form-item
        v-if="modifyConfig.modifyType==='suggestPrice'"
        label="底价:"
        :prop="modifyConfig.priceType === 2 ? 'basePrice' : 'noRequired'"
      >
        <el-input
          v-model.trim="formModel.basePrice"
          style="width: 60%"
          placeholder="请输入大于0的数字，限2位小数"
          onkeyup="value=value.replace(/^\D*(\d*(?:\.\d{0,2})?).*$/g, '$1')"
          @blur="formModel.basePrice=$event.target.value"
          @change="basePriceChange"
        ></el-input>
      </el-form-item>
      <el-form-item
        v-if="modifyConfig.modifyType==='totalStock'"
        label="设置总库存"
        prop="totalStock"
      >
        <el-input
          v-model="formModel.totalStock"
          @input="checkAvailableQty"
          style="width: 60%"
          @blur="inputBlur(formModel.totalStock,'totalStock')"
        />
      </el-form-item>
      <div v-if="modifyConfig.modifyType==='shelves'">
        <div style="margin-bottom:10px">
          <i class="el-icon-warning"></i>
          <span style="margin-left:4px">您确定要下架此商品吗？</span>
        </div>
        <el-input
          v-model="formModel.shelves"
          type="textarea"
          placeholder="请输入备注"
          style="width: 100%"
          :rows="3"
          maxlength="50"
          show-word-limit
        />
        <!-- @blur="inputBlur(formModel.shelves,'shelves')" -->
      </div>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="handleClose" size="small">取 消</el-button>
      <el-button size="small" type="primary" :loading="loading" @click="modifyConfirm">确 定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { updateStock, updateAmount} from '@/api/product';
import { batchUpAndDown } from '@/api/support'

export default {
  name: 'ModifyDialog',
  props: {
    modifyConfig: {
      type: Object,
      default: () => ({
        modifyType: 'suggestPrice',
        title: '设置药帮忙价',
        barcode: '',
        suggestPrice: null,
        chainPrice: null,
        ids:[],
        priceType:'',
        grossProfitMargin:null,
        chainGrossProfitMargin:null,
        basePrice:null,
      }),
    },
  },
  data() {
    const validatePass = (rule, value, callback) => {
      if (!value) {
        if(rule.field == 'suggestPrice'){
          callback(new Error('请输入大于0的数字，限2位小数'));
        }else if(rule.field == 'totalStock') {
          callback(new Error('请输入大于0的正数'));
        }else {
          callback();
        }
      } else {
        callback();
      }
    };
    return {
      dialogVisible: true,
      loading: false,
      formModel: {
        totalStock: '', // 商品总库存
        suggestPrice: this.modifyConfig.suggestPrice?JSON.parse(JSON.stringify(this.modifyConfig.suggestPrice)):null, // 建议零售价
        chainPrice: this.modifyConfig.chainPrice?JSON.parse(JSON.stringify(this.modifyConfig.chainPrice)):null, // 连锁采购价
        shelves:'',
        grossProfitMargin:this.modifyConfig.grossProfitMargin?JSON.parse(JSON.stringify(this.modifyConfig.grossProfitMargin)):null, // 单体毛利率
        chainGrossProfitMargin:this.modifyConfig.chainGrossProfitMargin?JSON.parse(JSON.stringify(this.modifyConfig.chainGrossProfitMargin)):null, // 连锁毛利率
        basePrice:this.modifyConfig.basePrice?JSON.parse(JSON.stringify(this.modifyConfig.basePrice)):null, // 底价
      },
      rules: {
        totalStock: [{ validator: validatePass, trigger: 'blur' }],
        chainPrice: [{ validator: validatePass, trigger: 'blur' }],
        suggestPrice: [{ required: true, validator: validatePass, trigger: 'blur' }],
        grossProfitMargin: [
          {required: true, message: '单体毛利率不能为空', trigger: 'blur'}
        ],
        basePrice: [
          {required: true, message: '底价不能为空', trigger: 'blur'}
        ],
        noRequired: [
          { required: false },
        ],
      },
      suggestPriceCopy:this.modifyConfig.suggestPrice?JSON.parse(JSON.stringify(this.modifyConfig.suggestPrice)):null,
      chainPriceCopy:this.modifyConfig.chainPrice?JSON.parse(JSON.stringify(this.modifyConfig.chainPrice)):null
    };
  },
  methods: {
    handleClose() {
      this.$emit('update:modifyDialogVisible', false);
    },
    modifyConfirm() {
      this.$refs.ruleForm.validate(async (valid) => {
        if (valid) {
          this.loading = true;
          const { barcode } = this.modifyConfig;
          if (this.modifyConfig.modifyType === 'totalStock') {
            const res = await updateStock({ barcode, totalStock: this.formModel.totalStock });
            if (res.code === 0) {
              // this.$message.success(res.message)
              this.$message.success('设置成功');
              this.handleClose();
              this.confirmCallback();
            } else {
              // this.$message.error(res.message)
              this.$message.error(res.message);
            }
          } else if(this.modifyConfig.modifyType === 'suggestPrice'){
            let params = {
              barcode,
              fob: this.formModel.suggestPrice,
              chainPrice: this.formModel.chainPrice,
              basePrice: this.formModel.basePrice,
            }
            if(this.modifyConfig.priceType === 2){
              params.grossProfitMargin = this.formModel.grossProfitMargin;
              params.chainGrossProfitMargin = this.formModel.chainGrossProfitMargin;
            }
            const res = await updateAmount(params);
            if (res.code === 0) {
              this.$message.success('价格设置成功');
              this.handleClose();
              this.confirmCallback();
            } else {
              // this.$message.error(res.message)
              this.$message.error(res.message);
            }
          }else if(this.modifyConfig.modifyType === 'shelves'){
            const { ids } = this.modifyConfig;
            const params = {
              skuIds: ids,
              // remark: this.formModel.shelves,
              status:2
            };
            const res = await batchUpAndDown(params);
            if (res.code === 0) {
              this.$message.success({ message: res.message, offset: 100 });
              this.handleClose();
              this.confirmCallback();
            } else if (res.code === 2) {
              this.$alert(res.message, '温馨提示', {
                confirmButtonText: '确定',
                callback: (action) => {
                },
              });
            }else {
              this.$message.error({ message: res.message, offset: 100 });
            }
          }
          this.loading = false;
        } else {
          console.log('error submit!!');
          return false;
        }
      });
    },
    checkPrice(value) {
      this.$set(this.formModel, 'suggestPrice', value.match(/^\d*(\.?\d{0,2})/g)[0]);
    },
    checkPrices(value) {
      this.$set(this.formModel, 'chainPrice', value.match(/^\d*(\.?\d{0,2})/g)[0]);
    },
    checkAvailableQty(value) {
      this.$set(this.formModel, 'totalStock', value.replace(/[^0-9]/g, ''));
    },
    inputBlur(value, str) {
      if (value && str) {
        this.$set(this.formModel, str, parseFloat(value));
      }
    },
    confirmCallback() {
      this.$emit('confirmCallback', true);
    },
    // 计算单体采购价，连锁采购价
    grossProfitMarginChange(value){
      if(value){
        this.formModel.grossProfitMargin = value;
        this.formModel.suggestPrice = this.numFilter(
          this.formModel.basePrice/(1-this.formModel.grossProfitMargin/100));
      }else{
        this.formModel.suggestPrice = this.suggestPriceCopy;
      }
    },
    chainGrossProfitMarginChange(value){
      if(value){
        this.formModel.chainGrossProfitMargin = value;
        this.formModel.chainPrice = this.numFilter(
          this.formModel.basePrice/(1-this.formModel.chainGrossProfitMargin/100));
      }else{
        this.formModel.chainPrice = this.chainPriceCopy;
      }
    },
    basePriceChange(value){
      if (this.modifyConfig.priceType === 2) {
        this.grossProfitMarginChange(this.formModel.grossProfitMargin);
        this.chainGrossProfitMarginChange(this.formModel.chainGrossProfitMargin);
      }
    },
    // 截取当前数据到小数点后两位
    numFilter(value) {
      const realVal = parseFloat(value).toFixed(2);
      return realVal;
    },
  },
};
</script>

<style scoped lang="scss">
.colorRed {
  color: #ff2121;
}
.el-icon-warning {
  color: #ffba00;
}
</style>
