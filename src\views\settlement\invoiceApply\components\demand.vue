<template>
  <div class="invoiceDemand-box">
    <p>
      温馨提示：<br />
      1、佣金发票只支持按照自然月开具，支持勾选多个月合并开具。开票总额必须为正数，若当月金额为负数，会与下月进行合并；<br />
      2、佣金发票合并时，只允许月结与月结合并，非月结与非月结合并，不允许月结与非月结进行合并；<br />
      3、申请开票前请在“发票信息”页面填写完整的发票类型、发票抬头等相关信息；<br />
      4、发票开票内容统一为“技术服务费”，纸质发票的邮寄时间约为3-7天；
    </p>
    <template>
      <el-row :gutter="20" class="price-box mb15">
        <el-col :span="12"
          >开票总额(元)：
          <span
            >￥{{
              invoiceMoneyTotal || invoiceMoneyTotal === 0 ? invoiceMoneyTotal.toFixed(2) : ''
            }}</span
          ></el-col
        >
        <el-col :span="12">
          <div class="btn-item">
            <el-button v-permission="['settle_invoice_apply']" type="primary" @click="invoiceSure">申请已选择项目开票</el-button>
          </div>
        </el-col>
      </el-row>
      <xyy-table
        :data="list"
        :list-query="listQuery"
        :hasSelection="true"
        :selectable="selectable"
        :col="col"
        :has-index="true"
        :operation="operation"
        @selectionCallback="selectBill"
        @get-data="getList"
        @operation-click="operationClick"
      ></xyy-table>
      <el-dialog :visible.sync="invoiceTipBox" custom-class="invoice-dialog" top="0" title="提示">
        <p>{{ invoiceTip }}</p>
        <div slot="footer" v-if="!showLink">
          <el-button type="primary" @click="reloadList">确定</el-button>
        </div>
        <div slot="footer" v-if="showLink">
          <el-button @click="invoiceTipBox = false">关闭</el-button>
          <el-button type="primary" @click="checkCommission" >查看佣金缴纳记录</el-button>
        </div>
      </el-dialog>
    </template>
  </div>
</template>

<script>
import { getInvoiceDemandList, applyInvoice } from '@/api/settlement/invoiceApply'

export default {
  name: 'demand',
  data() {
    return {
      activeTab:'demand',
      listQuery: {
        pageSize: 10,
        page: 1,
        total: 0
      },
      list: [],
      col: [
        // {
        //   index: 'hireNo',
        //   name: '序号'
        // },
        {
          index: 'hireMonths',
          name: '收入月份',
          width: 240
        },
        {
          index: 'projectTypeDesc',
          name: '项目类型'
        },
        {
          index: 'actualHireMoney',
          name: '佣金金额（元）',
          formatter: (row, col, cell) => (cell || cell === 0 ? cell.toFixed(2) : ''),
          // renderHeader: (h, { column }) => {
          //   return h(
          //     'div', [
          //       h('span', column.label),
          //       h('el-tooltip', {
          //         props: {
          //           content: '账单的佣金金额=账单中所有单据的佣金金额求和',
          //           placement: 'right',
          //         },
          //       }, [
          //         h('i', { class: 'el-icon-warning-outline' }),
          //       ]),
          //     ],
          //   );
          // },
        },
        {
          index: 'operation',
          name: '操作',
          operation: true,
          width: 200
        }
      ],
      operation: [
        {
          name: '查看明细',
          type: 0
        }
      ],
      selectedList: [],
      invoiceTipBox: false,
      invoiceTip: '开票信息提示',
      showLink: false,
      invoiceMoneyTotal: 0 // 开票总额
    }
  },
  created() {
    this.getList(this.listQuery, true)
  },
  methods: {
    reloadList() {
      this.getList(this.listQuery, true)
      // window.location.reload()
      this.invoiceTipBox = false
    },
    // 获取发票索取列表
    getList(listQuery, reset) {
      const loading = this.$loading({
        lock: true,
        text: 'Loading',
        spinner: 'el-icon-loading',
        background: 'rgba(255,255,255, 0.8)'
      })
      const { page, pageSize } = listQuery
      getInvoiceDemandList({
        pageNum: reset ? 1 : page,
        pageSize
      })
        .then((res) => {
          loading.close()
          if (res.code === '200') {
            const { list, total, pageNum } = res.data
            this.list = list ? list : []
            this.listQuery = {
              ...this.listQuery,
              total,
              page: pageNum
            }
          } else {
            this.$message.error({
              message: res.errorMsg || res.msg,
              customClass: 'center-msg'
            })
          }
        })
        .catch(() => {
          loading.close()
        })
    },
    // 申请开票
    invoiceSure() {
      this.showLink = false;
      if (!this.selectedList.length) {
        this.$message.warning('请选择要开票的项目')
        return
      }
      const hireNoList = this.selectedList.map((el) => el.hireNo)
      // const hireNos = hireNoList.join(',')
      applyInvoice({
        hireNos:hireNoList
      })
        .then((res) => {
          if (res.code === '200') {
            this.invoiceTipBox = true
            this.invoiceTip = res.data
          } else if(res.code === '800'){
            //佣金记录跳转
            this.invoiceTipBox = true
            this.invoiceTip = res.errorMsg || res.msg
            this.showLink = true;
          }else {
            this.$message.error({
              message: res.errorMsg || res.msg,
              customClass: 'center-msg'
            })
          }
        })
        .catch(() => {})
    },
    //查看佣金缴纳记录
    checkCommission(){
      this.$router.push(`/commissionRecord`)
    },
    /**
     * 选中回调
     */
    selectBill(bills) {
      console.log(bills)
      this.selectedList = bills
      const hireMoneyList = bills.map((el) => el.actualHireMoney)
      if (hireMoneyList.length) {
        this.invoiceMoneyTotal = hireMoneyList.reduce((n, m) => n + m)
      }
    },
    /**
     * 重置数据
     */
    reset() {
      this.$refs.listQuery.resetFields()
      this.getList(this.listQuery, true)
    },
    /**
     * 选项操作
     */
    operationClick(type, row) {
      if (type === 0) {
        // 查看明细
        this.$router.push(`/invoiceDemandDetail?hireNo=${row.hireNo}`)
      }
    },
    // 是否可选
    selectable(row) {
      if (row.actualHireMoney < 0) {
        return false
      }
      return true
    }
  }
}
</script>

<style lang="scss" scoped>
.invoiceDemand-box {
  padding: 0 15px 15px;
  .price-box {
    font-size: 16px;
    font-family: PingFangSC, PingFangSC-Semibold;
    font-weight: 600;
    color: #303133;
    line-height: 40px;
    overflow: hidden;
    &.mb15 {
      margin-bottom: 15px;
    }
    span {
      font-size: 28px;
    }
    .el-button {
      padding: 0 12px;
      line-height: 30px;
    }
  }
  > p {
    padding: 8px;
    background: #f9f9f9;
    border-radius: 2px;
    font-size: 14px;
    font-family: PingFangSC, PingFangSC-Regular;
    font-weight: 400;
    color: #666666;
    margin: 15px 0 25px;
  }
  .btn-item {
    float: right;
  }
}
</style>
<style lang="scss">
.el-dialog.invoice-dialog {
  width: 400px;
  top: 50%;
  transform: translateY(-50%);
  .el-dialog__header {
    padding: 0 20px;
    line-height: 50px;
    .el-dialog__headerbtn {
      top: 17px;
    }
  }
  .el-dialog__body {
    box-sizing: border-box;
    padding: 10px 20px;
    max-height: 400px;
    overflow-y: auto;
  }
  .el-button {
    padding: 0 20px;
    line-height: 28px;
    height: 30px;
  }
}
</style>
