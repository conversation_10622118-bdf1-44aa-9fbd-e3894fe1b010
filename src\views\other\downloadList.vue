<template>
  <div class="downloadListWrap">
    <div class="downloadListTitle" @click="handerRefresh">
      <span class="sheetMainBtn">
        文件下载中心
        <!-- <span class="el-icon-refresh"></span> -->
      </span>
      <el-button
        type="primary"
        size="small"
      >
        刷新
      </el-button>
    </div>
    <el-divider />

    <div class="download-tabs">
      <el-table
        ref="downloadTable"
        max-height="397"
        :data="tableData.list"
        v-loading="laodingBoole"
        stripe
        style="width: 100%"
        :header-cell-style="{ background: '#F9F9F9', color: '#666666' }"
      >
        <el-table-column prop="downloadFileName" label="文件名" width="260"></el-table-column>
        <el-table-column label="导出进度">
          <template slot-scope="scope">
            <span v-if="scope.row.downloadSpeed > 0">{{scope.row.downloadSpeed+'%'}}</span>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column label="状态">
          <template slot-scope="scope">
            <span>{{ {1:'新建',2:'生成中',3:'已完成',4:'失败'}[scope.row.status] || '-' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="操作时间" width="200"></el-table-column>
        <el-table-column prop="createBy" label="操作人" ></el-table-column>
        <el-table-column prop="failReason" label="失败原因" ></el-table-column>
        <el-table-column fixed="right" label="操作" width="100">
          <template slot-scope="scope">
            <el-button
              v-if="scope.row.fileUrl"
              type="text"
              @click="handleDownload(scope.row)"
              size="small"
            >下载</el-button>
            <el-button
              v-if="scope.row.failReason"
              type="text"
              @click="handleCheck(scope.row)"
              size="small"
            >查看失败原因</el-button>
          </template>
        </el-table-column>
        <template slot="empty">
          <div class="noData">
            <p class="img-box">
              <img src="@/assets/image/marketing/noneImg.png" alt />
            </p>
            <p>暂无数据</p>
          </div>
        </template>
      </el-table>
       <div class="explain-pag">
        <Pagination
          v-show="tableData.total > 0"
          :total="tableData.total"
          :page.sync="pageData.pageNum"
          :limit.sync="pageData.pageSize"
          @pagination="getList"
        ></Pagination>
      </div>
    </div>
  </div>
</template>
<style>
@import '../../assets/css/changeElement.scss';
</style>
<script>
import { getDownloadFileList } from '@/api/other/index'; //getDownload
import Pagination from '@/components/Pagination'
import utils from '@/utils/filter';
export default {
  name: 'downloadList',
  components: {Pagination},
  data() {
    return {
        tableData: {
          total: 0,
          list: []
        },
        pageData: {
          pageSize: 10,
          pageNum: 1
      },
        laodingBoole: false,
    };
  },
  activated() {
    this.getList();
  },
  methods: {
    getList(from) {
      let that = this
      if (from == 'search') {
        this.pageData.pageNum = 1
      }
      this.laodingBoole = true
      let param = {
        ...this.pageData
      }
      getDownloadFileList(param).then((res) => {
        if (res.code == 0) {
          this.laodingBoole = false
          if(res.result){
              that.tableData.list = res.result.list
              let createTime = {};
              this.tableData.list.forEach((item, index) => {
                createTime = {};
                if (item.createTime == null) {
                  createTime = '-';
                } else {
                  createTime = utils.dataTime(
                    item.createTime,
                    'yy-mm-dd HH:ss:nn'
                  );
                }
                this.tableData.list[index] = Object.assign({}, this.tableData.list[index], {
                  createTime: createTime,
                });
              });
          }else{
              that.tableData.list= [];
          }
          that.tableData.total = res.result.total
        }else {
          this.laodingBoole = false
           this.$message({
                  message: res.msg,
                  type: 'error'
              })
          }
      }) .catch(() => {});
    },
   // 点击下载按钮事件
    handleDownload (row) {
        let url = process.env.VUE_APP_BASE_API + row.fileUrl;
        console.log('api',process.env.VUE_APP_BASE_API);
        console.log('url',row);
        const a = document.createElement('a');
        a.href = url;
        a.click();
    },
    // 查看失败原因
    handleCheck (row) {
        this.$alert(row.failReason, '失败原因', {
            confirmButtonText: '确定',
            callback: () => {},
        });
    },
    // 刷新按钮
    handerRefresh () {
        this.getList();
    },
  },
};
</script>
<style lang="scss" scoped>
.downloadListWrap {
  padding: 20px 24px 5px;
  .downloadListTitle {
    font-size: 20px;
    font-weight: 500;
    .sheetMainBtn {
      cursor: pointer;
    }
  }
  .el-divider--horizontal {
    display: block;
    height: 1px;
    width: 100%;
    margin: 10px 0;
  }
  .download-tabs {
    .el-button + .el-button {
      margin-left: 0px;
    }
  }
}
</style>
