<template>
  <div style="padding: 0 30px;">
    <el-table class="x-w-h-table" header-row-class-name="x-cell" border :data="data" :row-key="(row) => row.id" :expand-row-keys="expandKeys">
      <el-table-column align="center" type="selection" width="55"></el-table-column>
      <el-table-column label="单据信息" width="200">
        <template slot-scope="{ row }"></template>
      </el-table-column>
      <el-table-column align="center" label="申请角色" width="120">
        <template slot-scope="{ row }"></template>
      </el-table-column>
      <el-table-column label="售后类型" width="150">
        <template slot-scope="{ row }"></template>
      </el-table-column>
      <el-table-column label="售后原因" width="250">
        <template slot-scope="{ row }"></template>
      </el-table-column>
      <el-table-column label="退款金额" width="200">
        <template slot-scope="{ row }"></template>
      </el-table-column>
      <el-table-column label="客户信息" width="250">
        <template slot-scope="{ row }"></template>
      </el-table-column>
      <el-table-column label="处理状态" width="200">
        <template slot-scope="{ row }">
          {{ row.id }}
        </template>
      </el-table-column>
      <el-table-column label="标记备注" width="150">
      </el-table-column>
      <el-table-column label="操作" align="center" width="200">
        <template slot-scope="{ row }">
          <p>
            <el-button type="text" @click="expandClick(row.id)">商品详情</el-button>
            <i :class="expandKeys.includes(row.id) ? 'el-icon-arrow-up' : 'el-icon-arrow-down'" />
          </p>
        </template>
      </el-table-column>
      <el-table-column type="expand" width="1px">
        <template slot-scope="props">
          <div style="padding: 0 55px;">
            <el-table :data="props.row.xxx" border>
              <el-table-column label="序号" type="index" width="55" align="center"></el-table-column>
              <el-table-column label="商品编码" width="200"></el-table-column>
              <el-table-column label="商品ERP编码" width="200"></el-table-column>
              <el-table-column label="sku编码" width="200"></el-table-column>
              <el-table-column label="商品名称" width="250"></el-table-column>
              <el-table-column label="规格（型号）" width="200"></el-table-column>
              <el-table-column label="生产厂家" width="200"></el-table-column>
              <el-table-column align="center" label="退回商品数量" width="120"></el-table-column>
              <el-table-column align="center" label="退款金额"></el-table-column>
            </el-table>
          </div>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>
<script>
export default {
  props: {
    data: {
      type: Array,
      default: []
    }
  },
  data() {
    return {
      expandKeys: [],  //当前的展开行
    }
  },
  methods: {
    expandClick(id) {
      let target = true;
      this.expandKeys = this.expandKeys.filter(val => {
        if (val == id) {
          target = false;
          return false;
        } else {
          return true;
        }
      })
      if (target) {
        this.expandKeys.push(id);
      }
    }
  }
}
</script>
<style scoped>
.x-w-h-table p {
  margin: 5px 0;

}
::v-deep  .x-w-h-table th {
  background: #f9f9f9;
}
::v-deep   .el-table__expanded-cell th {
  background: white;
}
::v-deep   .x-cell {
  background-color: black !important;
}
::v-deep   .el-table__expand-column {
  overflow: hidden;
}
::v-deep   .el-table__expanded-cell {
  background-color: rgb(246, 246, 246) !important;
}
::v-deep   .el-table__expanded-cell:hover {
  background-color: rgb(246, 246, 246) !important;
}
</style>
