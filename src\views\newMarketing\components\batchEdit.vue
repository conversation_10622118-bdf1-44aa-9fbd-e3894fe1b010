<template>
  <el-dialog
    class="editDialog"
    title="批量导入更新"
    :visible="true"
    :close-on-click-modal="false"
    @close="handleCancel"
  >
    <el-form label-width="160px" class="activity-info" size="small">
      <el-form-item label="请选择要修改的内容">
        <el-select v-model="updateName" clearable placeholder="请选择" @change="changeUpName">
          <el-option
            v-for="option in batchOperations"
            :key="option.value"
            :value="option.value"
            :label="option.label"
          />
        </el-select>
        <div
          v-if="updateName === 'levelPrice'"
          style="margin-top: 15px;"
        >
          <el-radio-group
            v-model="stepPriceStatus"
            style="line-height: 20px;"
          >
            <el-radio :label="1">是（表示每家参团药店买满起始促销数量后，成团价自动改为对应阶梯价）</el-radio>
            <el-radio :label="2">否（导入的促销起始数量和价格无效。清空指定活动ID的所有阶梯价）</el-radio>
          </el-radio-group>
        </div>
      </el-form-item>
      <el-form-item v-if="updateName === 'businessArea'" label="复用原品商圈：" label-width="180px">
        <el-radio-group v-model="isCopyBusArea">
          <el-radio :label="1">是</el-radio>
          <el-radio :label="2">否</el-radio>
        </el-radio-group>
        <div class="tipBox">
          <p class="redText">注意：选择“是”则拼团商品业务商圈和原品相同，选择“否”则在所选商圈范围内的客户可购买拼团商品。</p>
        </div>
      </el-form-item>
      <el-form-item v-if="isCopyBusArea === 2 && updateName === 'businessArea'" label="业务商圈：">
        <el-button size="small" type="primary" @click="selectBusinessCircle">选择商圈</el-button>
        <span style="padding-left: 10px" v-if="businessCircleInfo.busAreaConfigName">
          已选：{{ businessCircleInfo.busAreaConfigName }}
          <el-button v-if="businessCircleInfo.busAreaConfigName" type="text" class="btn-info" @click="checkDetails">查看商圈</el-button>
        </span>
      </el-form-item>
      <el-form-item v-if="updateName === 'controlUser'" label="复用原品供货对象：" label-width="210px">
        <el-radio-group v-model="isCopyControlUser">
          <el-radio :label="1">是</el-radio>
          <el-radio :label="2">否</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="供货对象:" v-if="isCopyControlUser === 2 && updateName === 'controlUser'">
        <div class="customerType">
          <el-checkbox
            :indeterminate="isIndeterminate"
            v-model="checkAll"
            @change="handleCheckAllChange"
            class="checkedall"
          >全选</el-checkbox>
          <el-checkbox-group
            v-model="controlUserTypes"
            @change="handleCheckedTypesChange"
          >
            <el-checkbox
              v-for="(item,index) in customerTypes"
              :label="item.id"
              style="marginBottom:10px;"
              :key="index"
            >{{item.name}}</el-checkbox>
          </el-checkbox-group>
        </div>
        <div style='color: red;font-size: 12px;'>
          注意：被勾选的客户类型对应的客户可购买当前商品。未勾选的客户类型对应的客户不可购买当前商品。
        </div>
      </el-form-item>
      <el-form-item v-if="updateName === 'controlRoster'" label="黑白名单：">
        <el-radio-group v-model="controlRosterType">
          <el-radio :label="0" @click.native.prevent="clickNoBlackWhite">否</el-radio>
          <el-radio :label="1" @click.native.prevent="clickBlackNameList">黑名单控销组</el-radio>
          <el-radio :label="2" @click.native.prevent="clickWhiteNameList">白名单控销组</el-radio>
        </el-radio-group>
        <div class="div-info">
          <el-button type="primary" v-if="controlRosterType !== 0" size="small" @click="toSelectControlGroups">选择控销组</el-button>
          <span style="padding-left: 10px" v-if="controlGroupName">
            <span>已选“{{controlGroupName}}”</span>
            <el-button type="text" class="btn-info" @click="seeMerchantDetail">查看药店明细</el-button>
          </span>
        </div>
        <!-- <div class="tipBox redText">
          <p>活动维度可见性分为两种场景：</p>
          <p>场景1：选择了黑名单控销组，则仅商圈内指定客户类型-黑名单客户可享受该活动。</p>
          <p>场景2：选择了白名单控销组，则仅白名单的客户可享受该活动。</p>
        </div> -->
      </el-form-item>
      <el-form-item v-if="updateName === 'isVirtualShop'" label="虚拟供应商：">
        <el-radio-group v-model="isVirtualShop">
          <el-radio label="1">是</el-radio>
          <el-radio label="2">否</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item>
        <span slot="label">
          批量导入修改
          <span
            class="upload-tips-info"
          >{{ {'groupPrice': '拼团价', 'groupNum': '起拼数量', 'personalLimitNum': '拼团个人活动限购数量', 'totalLimitNum': '拼团活动总限购数量', 'customerGroupId': '人群ID', 'batchOffline': '批量下线', 'levelPrice': '是否设置阶梯价' }[updateName] }}</span>
        </span>
        <el-upload
          ref="importFile"
          class="upload-demo"
          action
          :before-upload="beforeExcel"
          multiple
          :limit="100"
          :show-file-list="false"
          accept=".xlsx, .xls"
          :on-exceed="handleExceed"
        >
          <el-button size="small" type="primary">选择文件</el-button>
        </el-upload>
        <span>{{ fileName.length>0?fileName:'请上传excel、xls、xlsx文件' }}</span>
        <div class="upload-tips">
          <span v-if="['levelPrice', 'businessArea', 'controlUser', 'controlRoster'].indexOf(updateName) === -1">
            <p>1、导入报名ID批量***，修改后状态更新为 待审核 。每次上传不要超过1000个数据，超过请分多次上传</p>
            <p>2、请上传编辑好的 xlsx 文件。参看模板文件。</p>
            <p v-if="updateName === 'groupPrice'">3、若活动存在阶梯价，则拼团价＞阶梯价1＞阶梯价2＞阶梯价3，不符合则提交失败</p>
          </span>
          <span v-if="updateName === 'levelPrice'">
            <p>1、选是，则采购活动改为对应的阶梯价；选否，则促销起始数量和阶梯价不填。编辑截止时间后不可批量修改阶梯价。每次上传不要超过300个数据。超过请分多次上传</p>
            <p>2、请上传编辑好的 xlsx 文件。</p>
            <p>阶梯价需满足：起拼数量＜促销起始数量1＜促销起始数量2＜促销起始数量3≤单个药店参团数量上限，成团价＞阶梯价1＞阶梯价2＞阶梯价3，不符合则批量修改失败</p>
          </span>
          <span v-if="updateName === 'businessArea'">
            <p>1、已存在的拼团供货方式为“区域、供货对象、黑白名单”才可变更商圈。若拼团活动供货信息配置的是人群，则不可变更业务商圈。</p>
            <p>2、是否复制对应原品商圈。选择"是"则拼团商品业务商圈和原品相同，选择"否"则在所选商圈范围内的客户可购买拼团商品。</p>
            <p>3、编辑截止时间后不可批量修改，每次上传不要超过300个数据，超过请分多次上传。导入时允许部分成功部分失败。</p>
          </span>
          <span v-if="updateName === 'controlUser'">
            <p>1、已存在的拼团供货方式为“区域、供货对象、黑白名单”才可变更供货对象；若拼团活动供货信息配置的是人群，则不可变更供货对象。</p>
            <p>2、编辑截止时间后不可批量修改，每次上传不要超过300个数据，超过请分多次上传。导入时允许部分成功部分失败。</p>
          </span>
          <span v-if="updateName === 'controlRoster'">
            <p>1、已存在的拼团供货方式为“区域、供货对象、黑白名单”才可变更黑白名单。若拼团活动供货信息配置的是人群，则不可变更黑白名单。</p>
            <p>2、编辑截止时间后不可批量修改，每次上传不要超过300个数据，超过请分多次上传。导入时允许部分成功部分失败。</p>
          </span>
          <div
            v-if="updateName"
            style="color: #409eff; cursor: pointer;"
            @click="handleDownloadStepPriceTemplate"
          >
            点击下载模版
          </div>
          <div v-if="['levelPrice', 'businessArea', 'controlUser', 'controlRoster', 'isVirtualShop'].indexOf(updateName) === -1">
            模板如下
            <span class="upload-tips-info">(注意保留首行标题)</span>
            <div class="ulBox">
              <ul class="icon-list">
                <li>报名ID</li>
                <li
                  v-if="updateName && updateName !== 'batchOffline'"
                >{{ {'groupPrice': '拼团价', 'groupNum': '起拼数量', 'personalLimitNum': '拼团个人活动限购数量', 'totalLimitNum': '拼团活动总限购数量', 'customerGroupId': '是否复用原品销售范围', 'batchOffline': '批量下线'}[updateName] }}</li>
                <li
                  v-if="updateName && updateName === 'customerGroupId'"
                >
                  人群ID
                </li>
              </ul>
              <ul class="icon-list">
                <li>此处填入报名ID</li>
                <li
                  v-if="updateName && updateName !== 'batchOffline'"
                >
                  {{ updateName !== 'customerGroupId' ? '此处填入' : '' }}{{ {'groupPrice': '拼团价', 'groupNum': '起拼数量', 'personalLimitNum': '拼团个人活动限购数量', 'totalLimitNum': '拼团活动总限购数量', 'customerGroupId': '选“是”，拼团活动商品和原商品商圈、控销规则相同，后续原商品调整不影响活动商品；选“否”，人群ID必填。该拼团活动商品和原商品商圈、控销规则独立，按照活动人群售卖。', 'batchOffline': '批量下线'}[updateName] }}
                </li>
                <li
                  v-if="updateName && updateName === 'customerGroupId'"
                >
                  此处填入人群ID
                </li>
              </ul>
            </div>
          </div>
        </div>
      </el-form-item>
      <!-- <span v-show="downloadUrl">
        <p>
          <span style="color: red;">批量导入失败，原因参考错误文件:</span>
          <a
            style="color: #409eff;"
            :href="downloadUrl"
            download
            class="downLoader-btn"
          >批量修改-错误.xlsx</a>
        </p>
      </span> -->
    </el-form>
    <span class="dialog-footer" slot="footer">
      <el-button size="small" @click="handleCancel">关闭</el-button>
      <el-button size="small" type="primary" :loading="submitLoading" @click="excelUpload">提交</el-button>
    </span>
    <!-- 选择商圈 -->
    <select-business-circle
      v-if="isSelectBusinessCircle"
      :selected.sync="businessCircleInfo.busAreaId"
      :saleType.sync="businessCircleInfo.saleType"
      fromComp="batchEdit"
      @onDialogChange="cancelDialog"
      @selectChange="selectChange"
    ></select-business-circle>
    <!-- 查看商圈 -->
    <view-business-circle
      :row="{id:businessCircleInfo.busAreaId}"
      v-if="viewBusinessDialog"
      @onDialogChange="cancelDialog"
    ></view-business-circle>
    <!-- 查看药店明细 -->
    <ListOfControlGroups
      v-if="showMerchantInfo"
      :control-group-id="controlGroupId"
      @cancelDialog="cancelDialog"
    />
    <!-- 选择控销组 -->
    <SelectControlGroups
      v-if="showSelectControlGroups"
      :show-select-control-groups.sync="showSelectControlGroups"
      :selected-id="controlGroupId"
      @selectGroupChange="selectGroupChange"
    />
  </el-dialog>
</template>
<script>
import { apiBatchUpdate } from '@/api/market/collageActivity';
import selectBusinessCircle from "@/views/product/components/selectBusinessCircle";
import viewBusinessCircle from '@/views/business-circle/components/viewBusinessCircle';
import ListOfControlGroups from '@/views/product/components/listOfControlGroups';
import SelectControlGroups from '@/views/product/components/selectControlGroups';
import { findUserTypes } from '@/api/product';

export default {
  components: { viewBusinessCircle, selectBusinessCircle, ListOfControlGroups, SelectControlGroups },
  model: {
    prop: 'crowdDialogVis',
    event: 'onDialogChange',
  },
  props: {},
  data() {
    return {
      stepPriceStatus: -1,
      updateName: '',
      fileName: '',
      files: '',
      // downloadUrl: '',
      isCopyBusArea: 2,
      isSelectBusinessCircle: false,
      viewBusinessDialog: false,
      submitLoading: false,
      businessCircleInfo: {
        busAreaId: '',
        saleType: '',
        busAreaConfigName: '',
      },
      isCopyControlUser: 2,
      isIndeterminate: false,
      checkAll: false,
      customerTypes: [],
      controlUserTypes: [],
      controlRosterType: 0, // 黑白名单类型
      controlGroupId: '', // 选择的控销组
      controlGroupName: '', // 控销组名称
      showMerchantInfo: false, // 查看药店明细
      showSelectControlGroups: false, // 选择控销组
      isVirtualShop: '1',
      batchOperations: [
        {
          label: '拼团价',
          value: 'groupPrice',
        },
        {
          label: '起拼数量',
          value: 'groupNum',
        },
        {
          label: '拼团个人活动限购数量',
          value: 'personalLimitNum',
        },
        {
          label: '拼团活动总限购数量',
          value: 'totalLimitNum',
        },
        {
          label: '人群ID',
          value: 'customerGroupId',
        },
        {
          label: '批量下线',
          value: 'batchOffline',
        },
        {
          label: '是否设置阶梯价',
          value: 'levelPrice',
        },
        {
          label: '业务商圈',
          value: 'businessArea',
        },
        {
          label: '供货对象',
          value: 'controlUser',
        },
        {
          label: '黑白名单',
          value: 'controlRoster',
        },
        {
          label: '虚拟供应商',
          value: 'isVirtualShop'
        }
      ],
    };
  },
  mounted() {
    if (this.searchData) {
      this.searchData();
    }
    this.findUserTypes();
  },
  methods: {
    changeUpName() {
      this.businessCircleInfo = {
        busAreaId: '',
        saleType: '',
        busAreaConfigName: '',
      };
      this.controlUserTypes = [];
      this.isIndeterminate = false;
      this.checkAll = false;
      this.isCopyControlUser = 2;
      this.controlRosterType = 0; // 黑白名单类型
      this.controlGroupId = ''; // 选择的控销组
      this.controlGroupName = ''; // 控销组名称
      this.fileName = '';
      this.files = '';
      // this.downloadUrl = '';
    },
    handleDownloadStepPriceTemplate() {
      const url = `${process.env.VUE_APP_BASE_API}/report/groupbuying/apply/downloadTemplateForBatchUpdate?updateName=${this.updateName}`;
      window.open(url);
    },
    beforeExcel(file) {
      const extension = file.name.split('.')[1] === 'xls';
      const extension2 = file.name.split('.')[1] === 'xlsx';
      const isLt2M = file.size / 1024 / 1024 < 200;
      if (!extension && !extension2) {
        this.$message.warning('上传模板只能是 xls、xlsx格式!');
        return;
      }
      if (!isLt2M) {
        this.$message.warning('文件过大，最大支持200M!');
        return;
      }
      this.files = file;
      this.fileName = file.name;
      return false;
    },
    // 导入商品
    handleExceed() {
      this.$message({
        message: '超过上传限制',
        type: 'error',
        duration: 1000,
      });
    },
    excelUpload() {
      if (!this.updateName) {
        this.$message.warning('请选择要修改的内容');
        return;
      }
      if (this.updateName === 'levelPrice') {
        if (this.stepPriceStatus === -1) {
          this.$message.warning('请选择是否设置阶梯价');
          return;
        }
      }
      this.submitLoading = true;
      const fileFormData = {};
      if (this.files && this.files.name !== '') {
        fileFormData.file = this.files;
        // fileFormData.branchCode = this.listQuery.branchCode;
        fileFormData.updateName = this.updateName;
        if (this.updateName === 'levelPrice') {
          fileFormData.stepPriceStatus = this.stepPriceStatus;
        }
        if (this.updateName === 'businessArea') {
          fileFormData.isCopyBusArea = this.isCopyBusArea;
          fileFormData.busAreaId = this.businessCircleInfo.busAreaId;
        }
        if (this.updateName === 'controlUser') {
          fileFormData.isCopyControlUser = this.isCopyControlUser;
          fileFormData.controlUserTypes = this.controlUserTypes.join();
        }
        if (this.updateName === 'controlRoster') {
          fileFormData.controlRosterType = this.controlRosterType;
          fileFormData.controlGroupId = this.controlGroupId;
          fileFormData.isCopyControlRoster = 2;
        }
        if (this.updateName === 'isVirtualShop') {
          fileFormData.isVirtualShop = this.isVirtualShop;
        }
        apiBatchUpdate(fileFormData).then((res) => {
          this.submitLoading = false;
          if (res.code === 1000) {
            const groupBuyingBatchCreatesResult = (res.data || {}).groupBuyingBatchCreatesResult || {};
            if (res.data && groupBuyingBatchCreatesResult && groupBuyingBatchCreatesResult.failureNum > 0) {
              const h = this.$createElement;
              this.$confirm('提示', {
                title: '提示',
                message: h('div', [
                h(
                  'span',
                  null,
                  `批量更新成功${groupBuyingBatchCreatesResult.successNum}条，失败${groupBuyingBatchCreatesResult.failureNum}条。`
                ),
                h(
                  'span',
                  null,
                  `${groupBuyingBatchCreatesResult.failureExcelFileDownloadUrl ? '失败原因请下载错误文件：' : ''}`
                ),
                h('a', {
                  style: 'color: #4183d5;',
                  attrs: {
                    href: groupBuyingBatchCreatesResult.failureExcelFileDownloadUrl,
                    download: '下载错误文件',
                  }
                }, `${groupBuyingBatchCreatesResult.failureExcelFileDownloadUrl ? '下载错误文件' : ''}`),
              ]),
                confirmButtonText: '关闭',
                showCancelButton: false,
              })
                .then(() => {})
                .catch(() => {})
              // if (groupBuyingBatchCreatesResult.failureExcelFileDownloadUrl) {
              //   this.downloadUrl = groupBuyingBatchCreatesResult.failureExcelFileDownloadUrl;
              // }
              this.files = '';
              this.fileName = '';
              this.$refs.importFile.uploadFiles = [];
            } else {
              // this.downloadUrl = '';
              this.files = '';
              this.fileName = '';
              this.$refs.importFile.uploadFiles = [];
              this.$message({
                message: '导入成功',
                type: 'success',
                duration: 1000,
              });
              this.handleCancel();
              this.$emit('refresh');
            }
          } else {
            this.$confirm(res.msg, '提示', {
              confirmButtonText: '确定',
              type: 'warning',
              customClass: 'delete-dialog-customClass',
            })
              .then(() => {})
              .catch(() => {});
          }
        }).catch(() => {
          this.submitLoading = false;
        });
      } else {
        this.submitLoading = false;
        this.$message({
          message: '请上传excel、xls、xlsx文件',
          type: 'warning',
          duration: 1000,
        });
      }
    },
    handleCancel() {
      this.$emit('cancelModal', false);
    },
    // 选择商圈
    selectBusinessCircle() {
      this.isSelectBusinessCircle = true;
    },
    // 查看商圈详情
    checkDetails() {
      this.viewBusinessDialog = true;
    },
    cancelDialog() {
      this.isSelectBusinessCircle = false;
      this.viewBusinessDialog = false;
      this.showMerchantInfo = false;
      this.showSelectControlGroups = false;
    },
    selectChange(row) {
      if(row) {
        this.businessCircleInfo.busAreaId = row.id;
        this.businessCircleInfo.busAreaConfigName = row.busAreaName;
      }
    },
    // 获取用户类型
    findUserTypes() {
      findUserTypes().then((res) => {
        if (res.code == 0) {
          this.customerTypes = res.data;
        } else {
          this.$message({
            message: res.message,
            type: 'warning',
          });
        }
      });
    },
    // 供货对象
    handleCheckAllChange(val) {
      const checkAllId = this.customerTypes.map((item => item.id));
      this.controlUserTypes = val ? checkAllId : [];
      this.isIndeterminate = false;
    },
    handleCheckedTypesChange(value) {
      const checkedCount = value.length;
      this.checkAll = checkedCount === this.customerTypes.length;
      this.isIndeterminate = checkedCount > 0 && checkedCount < this.customerTypes.length;
    },
    // 点击不选黑白名单
    clickNoBlackWhite(e) {
      this.controlGroupId = '';
      this.controlGroupName = '';
      this.controlRosterType = 0;
    },
    // 点击黑名单
    clickBlackNameList(e) {
      this.controlGroupId = '';
      this.controlGroupName = '';
      this.controlRosterType = 1;
      // if (this.controlRosterType === 1) {
      //   this.controlRosterType = 0;
      // } else {
      //   this.controlRosterType = 1;
      // }
    },
    clickWhiteNameList(e) {
      if (this.controlRosterType == 2) {
        // this.controlRosterType = 0;
      } else {
        this.$confirm('选择白名单控销组后，仅白名单控销组内的客户可以购买此商品，确定使用白名单控销组吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
        })
          .then(() => {
            this.controlGroupId = '';
            this.controlGroupName = '';
            this.controlRosterType = 2;
          })
          .catch(() => {});
        }
    },
    seeMerchantDetail() {
      this.showMerchantInfo = true;
    },
    selectGroupChange(row) {
      this.controlGroupId = row.id; // 用户组id
      this.controlGroupName = row.name;
    },
    toSelectControlGroups() {
      this.showSelectControlGroups = true;
    }
  },
};
</script>
<style scoped lang="scss">
.editDialog {
  .activity-info {
    .el-form-item {
      margin-bottom: 10px;
      ::v-deep  .el-form-item__label {
        line-height: 30px;
      }
      ::v-deep  .el-form-item__content {
        line-height: 30px;
        color: #606266;
        a {
          color: #409eff;
          cursor: pointer;
        }
      }
      .el-select {
        max-width: 180px;
      }
    }

    .upload-tips {
      font-size: 12px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #999999;
      line-height: 24px;
    }
    .upload-tips-info {
      color: #f93a4a;
    }
    .file-name {
      font-size: 12px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: rgba(51, 51, 51, 1);
      line-height: 24px;
    }
  }
  .ulBox {
    margin-top: 12px;
  }
  .icon-list {
    overflow: hidden;
    list-style: none;
    padding: 0 !important;
    border: 1px solid #eaeefb;
    border-bottom: 1px solid #eee;
    margin-bottom: -1px;
    margin-top: 0;
    display: flex;
    li {
      float: left;
      flex: 1;
      text-align: center;
      line-height: 40px;
      color: #666;
      font-size: 13px;
      border-right: 1px solid #eee;
      margin-right: -1px;
    }
    li:nth-child(2) {
      width: 40%;
      height: auto;
    }
  }
  .customerType {
    border: 1px solid #eeeeee;
    border-radius: 4px;
    max-height: 260px;
    overflow-y: auto;
    ::v-deep  .el-checkbox {
      width: 14%;
      margin-left: 10px;
    }
    ::v-deep  .checkedall {
      width: 100%;
      padding: 10px;
      margin-left: 0;
      margin-bottom: 10px;
    }
    ::v-deep  .el-checkbox__input.is-checked + .el-checkbox__label {
      color: #333333;
    }
  }
}
</style>
