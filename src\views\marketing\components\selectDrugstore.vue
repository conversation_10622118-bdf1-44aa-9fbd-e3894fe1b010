<template>
  <el-dialog
    title="选择药店"
    :visible.sync="dialogVisible"
    width="60%"
    :append-to-body="true"
    :before-close="handleClose"
  >
    <el-form ref="form" :model="form" label-width="80px">
      <el-form-item label="药店名称">
        <el-input v-model="form.merchantName" style="width: 200px" clearable placeholder="请输入药店全称"></el-input>
        <el-button @click="queryList" type="primary" style="float: right">查 询</el-button>
      </el-form-item>
    </el-form>
    <xyyTable
      ref="productListTable"
      v-loading="tableLoading"
      :data="tableConfig.data"
      :col="tableConfig.col"
      :hasSelection="true"
      :reserveSelection="true"
      @selectionCallback="handleSelectionChange"
      :list-query="listQuery"
      @get-data="queryList"
    />
    <span slot="footer" class="dialog-footer">
    <el-button @click="handleClose">取 消</el-button>
    <el-button type="primary" @click="confirm">确 定</el-button>
  </span>
  </el-dialog>
</template>

<script>
import { selectMerchantByName } from '@/api/coupon';

export default {
  name: 'selectDrugstore',
  data() {
    return {
      dialogVisible: true,
      form: {
        merchantName: ''
      },
      tableLoading: false,
      listQuery: {
        pageSize: 10,
        page: 1,
        total: 0
      },
      tableConfig: {
        data: [],
        col: [
          {
            name: '药店ID',
            index: 'merchantId'
          },
          {
            name: '药店ERP编码',
            index: 'sellerUserId'
          },
          {
            name: '手机号',
            index: 'mobile'
          },
          {
            name: '药店名称',
            index: 'merchantName'
          }
        ]
      },
      selectList: []
    };
  },
  created() {
    this.queryList();
  },
  methods: {
    handleClose() {
      this.$emit('update:selectDrugstoreVisible', false);
    },
    confirm() {
      this.$emit('confirmCallBack', this.selectList);
      this.handleClose();
    },
    getParams() {
      const {
        pageSize,
        page
      } = this.listQuery;
      const params = {
        pageNum: page,
        pageSize,
        ...this.form
      };
      return params;
    },
    async queryList() {
      this.tableLoading = true;
      const res = await selectMerchantByName(this.getParams());
      console.log(res);
      if (res && res.code === 1000) {
        this.tableConfig.data = res.data.pageInfo.list;
        this.listQuery.total = res.data.pageInfo.total;
      } else {
        this.$message.error(res.message || '列表查询失败');
      }
      this.tableLoading = false;
    },
    handleSelectionChange(ary) {
      this.selectList = ary;
    }
  }
};
</script>

<style scoped>

</style>
