<template>
  <div class="productList">
    <div class="contentBox">
      <div class="title">商品信息</div>
      <div class="tips">
        <div v-if="productAbnormal.validity" class="div-info">
          <p>
            <span class="status-span">
              <i style="background: #dcdfe6" />商品近效期
            </span>
            <el-tooltip effect="dark" placement="top">
              <template #content
                >商品近效期至<=90天，若近效期<30天将被自动下架，请及时关注</template
              >
              <i class="el-icon-warning-outline" />
            </el-tooltip>
          </p>
          <p class="refundCountBox">
            <span class="refundCount">{{ productAbnormal.validity }}</span>
            <el-button
              type="primary"
              size="mini"
              class="seeCount"
              @click="tipsSearch('nearTerm', !formModel.nearTerm)"
              >{{ formModel.nearTerm ? '取消' : '筛选' }}</el-button
            >
          </p>
        </div>
        <div v-if="productAbnormal.emptyEffect" class="div-info">
          <p>
            <span class="status-span">
              <i style="background: #dcdfe6" />商品近效期为空
            </span>
            <el-tooltip effect="dark" placement="top">
              <template #content
                >商品“近效期至”或“远效期至”为空，请及时修正</template
              >
              <i class="el-icon-warning-outline" />
            </el-tooltip>
          </p>
          <p class="refundCountBox">
            <span class="refundCount">{{ productAbnormal.emptyEffect }}</span>
            <el-button
              type="primary"
              size="mini"
              class="seeCount"
              @click="tipsSearch('emptyEffect', !formModel.emptyEffect)"
              >{{ formModel.emptyEffect ? '取消' : '筛选' }}</el-button
            >
          </p>
        </div>
        <div v-if="productAbnormal.expiredOffShelfCount" class="div-info">
          <p>
            <span class="status-span">
              <i style="background: #dcdfe6" />商品即将过期自动下架
            </span>
            <el-tooltip effect="dark" placement="top">
              <template #content
                >正在销售中的商品30天内即将过期，已被系统自动下架，请及时修正</template
              >
              <i class="el-icon-warning-outline" />
            </el-tooltip>
          </p>
          <p class="refundCountBox">
            <span class="refundCount">{{
              productAbnormal.expiredOffShelfCount
            }}</span>
            <el-button
              type="primary"
              size="mini"
              class="seeCount"
              @click="tipsSearch('expiredOffShelf', !formModel.expiredOffShelf)"
              >{{ formModel.expiredOffShelf ? '取消' : '筛选' }}</el-button
            >
          </p>
        </div>
        <div v-if="productAbnormal.expiredCount" class="div-info">
          <p>
            <span class="status-span">
              <i style="background: #dcdfe6" />商品即将过期无法上架
            </span>
            <el-tooltip effect="dark" placement="top">
              <template #content
                >非销售中的商品30天内即将过期，无法上架，请及时修正</template
              >
              <i class="el-icon-warning-outline" />
            </el-tooltip>
          </p>
          <p class="refundCountBox">
            <span class="refundCount">{{ productAbnormal.expiredCount }}</span>
            <el-button
              type="primary"
              size="mini"
              class="seeCount"
              @click="tipsSearch('expired', !formModel.expired)"
              >{{ formModel.expired ? '取消' : '筛选' }}</el-button
            >
          </p>
        </div>

      </div>
      <SearchForm
        ref="searchForm"
        :model="formModel"
        :form-items="formItems"
        @submit="handleFormSubmit"
        @reset="handleFormReset"
      >
        <template slot="form-item-status">
          <el-select v-model="formModel.status" placeholder="请选择">
            <el-option
              v-for="(item, index) in productStatusOptions"
              :key="index"
              :label="item.statusName"
              :value="item.statusType"
            />
          </el-select>
        </template>
        <template slot="form-item-category">
          <el-cascader
            ref="productCategoryLevel"
            v-model="formModel.categoryId"
            :options="productCategoryLevelOptions"
            :props="{ label: 'name', value: 'id', checkStrictly: true }"
            :show-all-levels="false"
            clearable
            @change="handleCascaderVal"
          />
        </template>
        <template slot="form-item-InventoryQuantity">
          <el-select v-model="formModel.availableQtySymbol" placeholder="请选择" style="width: 85px;">
            <el-option label="不限制" :value="0" />
            <el-option label="大于" :value="1" />
            <el-option label="小于" :value="2" />
            <el-option label="不等于" :value="3" />
          </el-select>
          <el-input v-model="formModel.availableQty" placeholder="请输入可售库存"></el-input>
        </template>
      </SearchForm>
	  <div style="color: red;margin-bottom: 10px;">
			<span>
				{{ `${freeGroupInfo.status !== 1 ? '未' : ''}参与一盒随心拼` }}
			</span>
			<span v-if="freeGroupInfo.status === 1" style="margin-left: 5px">
				转换系数为{{ freeGroupInfo.discountRadio }}
			</span>
			<span style="color: #4184d5; cursor: pointer; margin-left: 5px" @click="getDefaultDataFromApollo('click')">编辑</span>
		</div>
      <!-- <div class="operation-filter-source">
        <span>商品来源</span>
        <div class="source-checkbox" @change="queryList">
          <el-checkbox-group v-model="formModel.activityTypes">
            <el-checkbox :value="0" :label="0">普通商品</el-checkbox>
            <el-checkbox :value="1" :label="1">拼团商品</el-checkbox>
            <el-checkbox :value="2" :label="2">赠品</el-checkbox>
            <el-checkbox v-if="!shopConfig.isFbp && isGrey" :value="3" :label="3">批购包邮</el-checkbox>
          </el-checkbox-group>
        </div>
      </div> -->
      <div class="operation">
        <el-select
          v-permission="['pg_product_list_export']"
          class="exportProduct"
          :value="exportValue"
          placeholder="导出"
          @change="exportProductOptions"
        >
          <el-option
            v-for="item in exportOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
        <template>
          <el-button
            v-permission="['pg_product_list_release']"
            type="primary"
            size="small"
            @click="releaseProduct"
            >发布商品</el-button
          >
        </template>
        <el-button
          v-permission="['pg_product_list_batchUp']"
          type="primary"
          size="small"
          @click="batchUpAndDown(1)"
          >批量上架</el-button
        >
        <el-button
          v-permission="['pg_product_list_batchDown']"
          type="primary"
          size="small"
          @click="batchUpAndDown(2)"
          >批量下架</el-button
        >
        <el-select
          v-permission="['pg_product_list_batchUpdate']"
          class="modifyProduct"
          :value="modifyValue"
          placeholder="批量修改"
          @change="modifyProduct"
        >
          <el-option
            v-for="item in modifyOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
        <el-button
          style="margin-left: 10px"
          type="primary"
          size="small"
          @click="go('/productIssueSetFreeMail', null, false)"
        >
          商品上架设置
        </el-button>

        <template v-if="shopConfig.isFbp">
          <el-button
            style="margin-left: 10px"
            type="primary"
            size="small"
            @click="releaseWarehousedGoods"
            >发布入仓商品</el-button
          >
        </template>
      </div>

      <el-tabs v-model="activeName" @tab-click="tabHandleClick">
        <el-tab-pane
          v-for="item in tabStatusOptions"
          :key="item.statusType"
          :label="tabPaneLabel(item)"
          :name="String(item.statusType)"
        />
      </el-tabs>
      <xyyTable
        ref="productListTable"
        v-loading="tableLoading"
        :data="tableConfig.data"
        :col="tableConfig.col"
        :has-selection="true"
        :list-query="listQuery"
        @selectionCallback="selectionCallback"
        @get-data="queryList"
        @soryByPrice="soryByPrice"
        v-show="isProductTable"
      >
        <template slot="productInfo">
          <el-table-column label="商品信息">
            <template slot-scope="{ row }">
              <!-- <div
                class="productInfo"
                v-html="row.productInfo"
              />-->
              <div class="productInfo">
                <div class="productName">
                  <div class="name">
                    <span>{{ row.productName }}</span>
                    <!-- <span
                      v-if="row.activityType === 1"
                      class="icon"
                    >拼团</span> -->
                  </div>
                  <span>
                    <el-tag
                      v-if="
                        row.activeName === '特价' ||
                        row.activityType === 1 ||
                        row.activityType === 2 ||
                        row.activityType === 3
                      "
                      size="mini"
                      style="margin: 0 2px"
                    >
                      {{
                        row.activeName === '特价'
                          ? row.activeName
                          : { 2: '赠品', 1: '拼团', 3: '批购包邮' }[row.activityType]
                      }}
                    </el-tag>
                  </span>
                </div>
                <div class="manufacturer">
                  {{ row.manufacturer }}
                </div>
                <div>{{ row.spec }}</div>
                <div>{{ row.approvalNumber }}</div>
                <div>商品编码：{{ row.barcode }}</div>
                <div v-if="row.activityType === 3">CSUID：{{ row.csuid }}</div>
                <div v-else>CSUID编码：{{ row.csuid }}</div>
                <div>ERP编码：{{ row.erpCode }}</div>
                <div v-if="row.standardProductId && row.activityType === 1">主标准库ID：{{ row.standardProductId }}</div>
                <div v-if="row.activityType === 1 || row.activityType === 2 || row.activityType === 3">
                  原商品：{{ row.originalBarcode }}
                </div>
              </div>
            </template>
          </el-table-column>
        </template>
        <template slot="showName">
          <el-table-column label="展示名称">
            <template slot-scope="{ row }">
              <el-image
                style="width: 80px; height: 73px; margin-bottom: 4px"
                :src="row.fullImageUrl"
                :preview-src-list="row.allImageList"
                @click.prevent
              />
              <div><span v-if="row.activityType === 3">【包邮】</span>{{ row.showName }}</div>
              <span v-if="row.highGross === 6" class="high-gross-tag">{{ '特推' }}</span>
              <div v-else>
                是否主推高毛：{{
                  { 1: '否', 2: '高毛', 3: '主推' }[row.highGross]
                }}
              </div>
              <div v-if="row.activityType === 1">
                <span style="color:red;">是否虚拟供应商：{{ row.isVirtualShop == 1 ? '是' : '否' }}</span>
                <span v-if="(row.ptSource && row.ptSource == 2) && row.status != 20">
                  <i class="el-icon-edit-outline" style="color: #4184d5; font-size: 16px; cursor: pointer;" @click="updateData.actId = `PT${row.activityId}`;updateData.type=2;updateData.visible=true;"/>
                </span>
                <span style="color: #ff9500;display: block" v-if="row.reportIdString">{{row.baseFrameName}}</span>
              </div>
              <div v-if="row.activityType === 3">
                <span style="color:red;">是否虚拟供应商：{{ row.isVirtualShop == 1 ? '是' : '否' }}</span>
                <span v-if="row.status != 20">
                  <i
                  class="el-icon-edit-outline"
                  style="color: #4184d5; font-size: 16px; cursor: pointer;"
                  @click="updateVirtualShop(row.activityId,3,row.isVirtualShop)"/>
                </span>
                <!-- <span style="color: #ff9500;display: block" v-if="row.reportIdString">{{row.baseFrameName}}</span> -->
              </div>
            </template>
          </el-table-column>
        </template>
        <template slot="price">
          <el-table-column
            label="价格"
            :sortable="true"
            width="170"
          >
            <template slot-scope="{ row }">
              <div style="padding-top: 20px">
                <template v-if="row.activityType === 3">
                  <div>
                    原价：{{ row.fob }}
                  </div>
                  <div>
                    活动价：{{ row.groupPrice }}
                    <i
                      v-permission="['product_list_editPrice']"
                      class="el-icon-edit-outline"
                      style="color: #4184d5; font-size: 16px"
                      @click="tableModifyBtn(4, row)"
                    />
                  </div>
                  <div>
                    起购数量：{{ row.groupNum }}
                  </div>
                </template>
                <template v-else>
                  <div v-if="row.activityType !== 1">
                    单体采购价：{{ row.fob }}
                    <i
                      v-if="row.activityType !== 2"
                      v-permission="['product_list_editPrice']"
                      class="el-icon-edit-outline"
                      style="color: #4184d5; font-size: 16px;cursor: pointer;"
                      @click="tableModifyBtn(1, row)"
                    />
                  </div>
                  <div
                    v-if="row.activityType == 0 && shopConfig.priceType == 2"
                  >
                    单体毛利率：{{ row.grossProfitMargin }}
                  </div>
                  <div v-if="row.activityType !== 1">
                    连锁采购价：{{ row.chainPrice }}
                  </div>
                  <div
                    v-if="row.activityType == 0 && shopConfig.priceType == 2"
                  >
                    连锁毛利率：{{ row.chainGrossProfitMargin }}
                  </div>
                  <div v-if="row.activityType == 0 && shopConfig.priceType == 2">
                    底价：{{ row.basePrice }}
                  </div>
                  <div v-if="row.haveAreaPrice">
                    <el-button
                      type="text"
                      size="small"
                      @click="showRegional(row)"
                      >查看区域价
                  </el-button>
                  </div>
                  <div
                    v-if="
                      row.activityType !== 1 &&
                      row.activityType !== 2 &&
                      row.activityType !== 3 &&
                      Number(row.priceSyncErp)
                    "
                    style="color: #ff9500"
                  >
                    ERP同步
                  </div>

                  <el-button
                    v-if="row.activityType !== 1 && row.activityType !== 2 && row.activityType !== 3"
                    type="text"
                    size="small"
                    @click="handleCheckSkuAmountLog(row)"
                  >
                    查看价格日志
                  </el-button>

                  <div v-if="row.stepPriceStatus === 1">
                    ￥{{ row.minDiscountPrice }}起
                  </div>
                  <div v-if="row.activityType === 1">

                    <div class="productListTab" style="width:200px">提报价格：<el-badge :value="`${row.status != 20&&row.isBiddingProduct==1&&row.rankingReportPrice&&Number(row.rankingReportPrice)<=100? 'TOP'+row.rankingReportPrice :''}`" class="item">
                       {{ row.reportPrice }}
                      </el-badge>
                    </div>
                    <div>原价：{{ row.fob }}</div>
                    <div>
                      <span style="color:red">拼团价格：{{ row.groupPrice }}</span>
                      <span v-if="(row.ptSource && row.ptSource == 2) && row.status != 20">
                        <i class="el-icon-edit-outline" style="color: #4184d5; font-size: 16px; cursor: pointer;" @click="updateData.actId = `PT${row.activityId}`;updateData.type=1;updateData.visible=true;"/>
                      </span>
                    </div>
                    <div>起拼数量：{{ row.groupNum }}</div>
                    <div v-if="row.buyMostPrice" style="color:red;">
                      热销拼团价格: {{ row.buyMostPrice }}
                    </div>
                    <div v-if="row.buyMostStartQty" style="color:red;">
                      热销起拼数量: {{ row.buyMostStartQty }}
                    </div>
                    <div>
                      {{ row.ptActivityReportGroupAmountDtos ? row.ptActivityReportGroupAmountDtos.filter(item => item.name== '平台').length > 0 ? `平台补贴金额：${row.ptActivityReportGroupAmountDtos.filter(item => item.name== '平台')[0].amount}` : '' : '' }}
                    </div>
                    <div v-if="row.isBiddingProduct==1&&row.status != 20">
                      <span style="color: rgb(65,131,213);cursor: pointer;" @click="toAuction">查看竞价排名</span>
                    </div>
                  </div>
                </template>
              </div>
            </template>
          </el-table-column>
        </template>
        <template slot="totalStock">
          <el-table-column label="库存" :sort-orders="[ 'descending','ascending', null]" :sortable="true">
            <template slot-scope="{ row }">
              <div style="display: inline-block; width: auto">
                <span style="display: block">
                  <el-tooltip effect="dark" placement="top">
                    <template #content v-if="shopConfig.isFbp"
                      >神农系统对接库存</template
                    >
                    <template #content v-else
                      >总库存=可售库存+订单占用库存+活动占用库存</template
                    >
                    <i class="el-icon-warning-outline" />
                  </el-tooltip>
                  <span v-if="shopConfig.isFbp">
                    总库存：<el-button
                      type="text"
                      size="small"
                      @click="seeTotal('totalStock', row.csuid)"
                      >查看</el-button
                    >
                  </span>
                  <span v-else>
                    {{ `总库存：${row.totalStock}` }}
                  </span>
                  <i
                    v-if="!shopConfig.isFbp && row.activityType !== 1 && row.activityType !== 3"
                    v-permission="['product_list_editStock']"
                    class="el-icon-edit-outline"
                    style="color: #4184d5; font-size: 16px"
                    @click="tableModifyBtn(2, row)"
                  />
                </span>
                <span v-if="row.activityType === 1 || row.activityType === 3" style="display: block">
                  {{ `活动总限购库存：${row.totalLimitQtyStr}` }}
                  <i
                    v-if="row.activityType === 3"
                    v-permission="['product_list_editStock']"
                    class="el-icon-edit-outline"
                    style="color: #4184d5; font-size: 16px"
                    @click="tableModifyBtn(6, row)"
                  />
                  <span v-else-if="(row.ptSource && row.ptSource == 2) && row.status != 20">
                    <i class="el-icon-edit-outline" style="color: #4184d5; font-size: 16px; cursor: pointer;" @click="updateData.actId = `PT${row.activityId}`;updateData.type=4;updateData.visible=true;"/>
                  </span>
                </span>
                <span v-if="row.activityType === 3" style="display: block">
                  {{ `单店限购类型：${row.limitType ? personalLimitTypeList[row.limitType] : personalLimitTypeList[0]}` }}
                </span>
                <span v-if="row.activityType === 3" style="display: block">
                  {{ `单店限购数量：${row.limitQty}` }}
                </span>
                <span style="display: block">
                  <el-tooltip effect="dark" placement="top">
                    <template #content
                      >可售库存=ERP同步库存/手工设置商品总库存-订单占用库存-活动占用库存</template
                    >
                    <i class="el-icon-warning-outline" />
                  </el-tooltip>
                  {{ `可售库存：${row.availableQty}` }}
                </span>
                <span style="display: block">
                  <el-tooltip effect="dark" placement="top">
                    <template #content v-if="shopConfig.isFbp"
                      >神农未扣减的库存</template
                    >
                    <template #content v-else
                      >订单占用库存=所有未下推ERP且状态为“待付款”或“待审核”或“出库中”订单的该商品占用库存数量</template
                    >
                    <i class="el-icon-warning-outline" />
                  </el-tooltip>
                  <span v-if="shopConfig.isFbp">
                    订单占用库存：<el-button
                      type="text"
                      size="small"
                      @click="seeTotal('occupyStock', row.csuid)"
                      >查看</el-button
                    >
                  </span>
                  <span v-else>
                    {{ `订单占用库存：${row.occupyStock}` }}
                  </span>
                </span>

                <span style="display: block">
                  <el-tooltip effect="dark" placement="top">
                    <template #content>活动预占库存</template>
                    <i class="el-icon-warning-outline" />
                  </el-tooltip>
                  {{ `活动占用库存：${row.occupiedInventoryQty}` }}
                </span>

                <el-button
                  type="text"
                  size="small"
                  @click="operationClick(8, row)"
                  >查看库存日志</el-button
                >
              </div>
              <div
                v-if="row.nearEffect"
                :class="row.nearExpired ? 'redColor' : ''"
              >
                近至：{{ row.nearEffect }}
              </div>
              <div
                v-if="row.farEffect"
                :class="row.farExpired ? 'redColor' : ''"
              >
                远至：{{ row.farEffect }}
              </div>
              <div
                v-if="Number(row.stockSyncErp) && row.activityType !== 2"
                style="color: #ff9500"
              >
                ERP同步
                <span>:{{ row.erpOriginalStock }}</span>
              </div>
            </template>
          </el-table-column>
        </template>
        <template slot="busAreaName">
          <el-table-column label="供货信息">
            <template slot-scope="{ row }">
              <template v-if="row.activityType === 3 || row.activityType === 0 || row.activityType === 1">
                <template v-if="row.isCopySaleArea === 2">
                  <div>
                    <div v-if="row.isPlatformCustomer">
                      <span>指定人群</span><span style="color:red;">：平台指定人群</span>
                    </div>
                    <div v-else></div>
                    <div class="table-item" >人群ID：
                      <span v-if="row.customerGroupId">{{ row.customerGroupId }}
                        <i
                          class="el-icon-view"
                          style="color: #4183d5; font-size: 16px"
                          @click="addCroed(row)"></i>
                      </span>
                      <span v-else >无</span>
                      <i v-if="row.activityType === 3 || row.activityType === 0"
                        v-permission="['product_list_editStock']"
                        class="el-icon-edit-outline"
                        style="color: #4184d5; font-size: 16px"
                        @click="changeSupply(row)"
                      />
                      <span v-else-if="(row.ptSource && row.ptSource == 2) && row.status != 20">
                        <i class="el-icon-edit-outline" style="color: #4184d5; font-size: 16px; cursor: pointer;" @click="updateData.actId = `PT${row.activityId}`;updateData.type=3;updateData.visible=true;"/>
                      </span>
                    </div>
                    <div class="table-item">人群名称：<span v-if="row.customerGroupName">{{ row.customerGroupName }}</span><span v-else>无</span></div>
                  </div>
                </template>
                <template  v-else>
                  <div>
                    <div style="display: flex;align-items: center;position:relative;">
                      <el-popover
                        placement="top-start"
                        width="200"
                        style="width:100%;"
                        trigger="hover"
                        :content="`商圈名称：${row.busAreaName || '无'}`"
                      >
                        <div slot="reference" class="busAreaNameBox">
                          商圈名称：{{ row.busAreaName }}
                        </div>
                      </el-popover>
                      <i v-if="row.activityType === 3 || row.activityType === 0"
                        v-permission="['product_list_editStock']"
                        class="el-icon-edit-outline"
                        style="color: #4184d5; font-size: 16px"
                        @click="changeSupply(row)"
                      />
                      <span v-else-if="(row.ptSource && row.ptSource == 2) && row.status != 20">
                        <i class="el-icon-edit-outline" style="color: #4184d5; font-size: 16px; cursor: pointer;" @click="updateData.actId = `PT${row.activityId}`;updateData.type=3;updateData.visible=true;"/>
                      </span>
                    </div>
                    <el-popover
                      placement="top-start"
                      width="200"
                      trigger="hover"
                      :content="`供货对象：${row.controlUserTypeList && row.controlUserTypeList.join(',') || '-'}`"
                    >
                      <div slot="reference" class="busAreaNameBox">
                        供货对象：{{ row.controlUserTypeList && row.controlUserTypeList.join(',') || '-' }}
                      </div>
                    </el-popover>
                    <div class="table-item">黑白名单：<span v-if="row.controlRosterType">【{{ row.controlRosterType == 1 ? "黑名单" : "白名单" }}控销组：{{ row.controlGroupName }}】</span><span v-else>无</span></div>
                    <div v-if="row.isPlatformCustomer">
                      <span>指定人群</span><span style="color:red;">：平台指定人群</span>
                    </div>
                    <div v-else></div>
                    <div  class="table-item" v-if="row.customerGroupId">人群ID：
                      <span v-if="row.customerGroupId">{{ row.customerGroupId }}
                        <i
                          class="el-icon-view"
                          style="color: #4183d5; font-size: 16px"
                          @click="addCroed(row)"></i>
                      </span>
                      <!-- <i v-if="row.activityType === 3 || row.activityType === 0"
                        v-permission="['product_list_editStock']"
                        class="el-icon-edit-outline"
                        style="color: #4184d5; font-size: 16px"
                        @click="changeSupply(row)"
                      />
                      <span v-else-if="(row.ptSource && row.ptSource == 2) && row.status != 20">
                        <i class="el-icon-edit-outline" style="color: #4184d5; font-size: 16px; cursor: pointer;" @click="updateData.actId = `PT${row.activityId}`;updateData.type=3;updateData.visible=true;"/>
                      </span> -->
                    </div>
                    <div  class="table-item" v-if="row.customerGroupName">人群名称：<span>{{ row.customerGroupName }}</span></div>
                  </div>
                </template>
              </template>
              <template v-else>
                <span v-if="row.controlRosterType === 2">仅白名单客户可买</span>
                <el-popover
                  v-else
                  placement="top-start"
                  width="200"
                  trigger="hover"
                  :content="`商圈名称：${row.busAreaName}`"
                >
                  <div slot="reference" class="busAreaNameBox">
                    商圈名称：{{ row.busAreaName }}
                  </div>
                </el-popover>
              </template>
            </template>
          </el-table-column>
        </template>
        <template slot="status">
          <el-table-column label="商品状态">
            <template slot-scope="{ row }">
              <div
                v-if="row.availableQty <= 0 && row.status == 1"
                style="color: #ff2121"
              >
                已售罄
              </div>
              <div v-else>
                <div>{{ statusNameStr(row.logicStatus) }}</div>
                <!-- <div>{{ row.disableTypeName  }}</div>
                <div>{{ row.disableNote  }}</div> -->
                <el-button
                  v-permission="['product_list_viewSaleTime']"
                  v-if="Number(row.status) === 1 && row.haveSaleTime"
                  type="text"
                  @click="viewSaleTime(row)"
                  >查看售卖时间</el-button
                >
                <!-- <el-button
                  v-if="Number(row.status)===9"
                  type="text"
                  @click="viewAuditFailed(row)"
                >查看原因</el-button> -->
                <el-button
                  v-if="row.errorTipType"
                  type="text"
                  style="color: #ff2121; cursor: pointer"
                  @click="viewAuditFailed(row)"
                >
                  {{
                    row.errorTipType === 1
                      ? '(商品信息不全)查看原因'
                      : row.errorTipType === 2
                      ? '查看原因'
                      : ''
                  }}
                </el-button>
                <div v-if="row.disableType&&row.disableType!=0&&row.disableType!=3&&row.disableType!=5">
                  <span style="color: #ff0000;">{{row.disableTypeName}}: {{row.disableNote}}</span>
                </div>
              </div>
              <div />
              <div v-if="row.activityType === 1">
                <div>活动id：{{ row.activityIdString }}</div>
                <div>报名id：{{row.reportIdString}}</div>
                <div>
                  活动时间：
                  {{ transferTime(row.activityStartTime) }} ~
                  {{ transferTime(row.activityEndTime) }}
                </div>
                <div v-if="row.multiSegmentDTOList">
                  <div
                  v-for="(item,index) in validFormat(row)"
                  :key="index"
                  v-if="index == 0 || row.isShow"
                  >
                  {{item}}
                  <span class="ex" style="color: red;cursor: pointer;" v-if="index==0&&validFormat(row).length>1" @click="ex(row)">&nbsp;{{row.isShow?'收起':'展开查看'}}</span>
                  </div>

                  <!-- 活动时间：{{ transferTime(row.activityStartTime) }} ~
                  {{ transferTime(row.activityEndTime) }}
                  <div v-if="false">
                    周期循环：{{ transferTime(row.activityStartTime) }} ~
                  {{ transferTime(row.activityEndTime) }}
                  </div> -->
                  <!-- <p>活动时间：</p>
                  <div style="color: red" v-for="(item,index) in validFormat(row)" v-if="index==0||row.isShow">{{item}} <span class="ex" v-if="index==0&&validFormat(row).length>1" @click="ex(row)">&nbsp;{{row.isShow?'收起':'展开'}}</span></div> -->
                </div>
              </div>
              <div style="color: red" v-if="row.relateActNum">
                <span>关联活动数量:</span>
                <span style="padding: 0 10px">{{row.relateActNum}}</span>
                <span style="color: blue;cursor: pointer" @click="showRelevanceActivity(row)">查看</span>
              </div>
            </template>
          </el-table-column>
        </template>
         <template slot="salesData" >
          <el-table-column label="销售数据"  sortable :sort-orders="['descending','ascending',  null]">
            <template slot="header" slot-scope="scope">
            <span>
                  <span>销售数据</span>
                  <span @click.stop>
                  <el-popover
                    v-model="visibleShow"
                    placement="right"
                    width="400"
                    trigger="manual"
                  >
                    <template slot="reference">
                      <img src="./img/sx.png"  @click.stop="visibleShowOpen" class=" salesData" alt="">
                       <!-- <i class="el-icon-s-operation salesData" @click.stop="" style="background-color:rgb(249,249,249)"></i> -->
                    </template>
                    <div>选择采购数量的统计范围：</div>
                      <el-radio-group v-model="saleDataRange">
                        <el-radio  :label="1"> 近1年</el-radio>
                        <el-radio  :label="2"> 近30天</el-radio>
                        <el-radio  :label="3"> 近7天</el-radio>
                      </el-radio-group>
                     <div>选择排序规则：</div>
                     <el-radio v-model="rules" :label="3">采购店数(已支付订单去重客户数)</el-radio>
                     <el-radio v-model="rules" :label="4">采购数量(已支付订单采购数量)</el-radio>
                     <el-radio v-model="rules" :label="5">采购金额(实付金额)</el-radio>
                     <div style="text-align: center;margin-top: 10px"><el-button type="primary" size="small" @click="trueShow"> 确定</el-button><el-button type="" @click="visibleShow=false" size="small"> 取消</el-button></div>
                    </el-popover>
                 </span>
            </span>
            </template>
            <template slot-scope="{ row }">
              <!-- <div v-if="row.status==20?row.showNum:true"> -->
                <div>
                <div >采购明细<span class="lookMore" v-if="row.activityType!=2" @click="lookMore(row)">&nbsp;查看</span></div>
                 <div><i class="el-icon-question" title="有效订单中包含csuid，对应客户去重" />采购店数：{{ row.procureShopNum }}</div>
                 <div><i class="el-icon-question" title="有效订单中包含该csuid，对应订单计数。" />采购订单数：{{ row.procureOrderNum }}</div>
                 <div><i class="el-icon-question" title="有效订单、商品行中包含csuid，取包含对应csuid的各个商品行【应发货数量=商品数量-已退数量】之和" />采购数量：{{ row.procureNum }}</div>
                 <div><i class="el-icon-question" title="有效订单、商品行中包含csuid，取【实付金额*应发货数量/商品数量=实付金额*（商品数量-已退数量）/商品数量】之和" />采购金额：{{ row.procureAmount }}</div>
                 <div style="color: rgb(95, 175, 95)">未支付订单数：{{row.unpaiedOrderNum }}</div>
                 <div style="color: rgb(95, 175, 95)"><span>采购数量：{{ row.unpaiedProcureNum  }}</span></div>
              </div>
              <!-- <div v-else style="text-align: center;">
                <img src="./img/look.png" alt="" style="cursor: pointer;width:20px" @click="$set(row,'showNum',true)" />
              </div> -->
            </template>
          </el-table-column>
        </template>
        <template slot="operation">
          <el-table-column label="操作" fixed="right" width="150">
            <template slot-scope="{ row }">
              <div>
                <el-button
                  v-if="row.activityType !== 1 && row.activityType !== 2"
                  type="text"
                  @click="operationClick(1, row)"
                  >详情</el-button
                >
                <el-button
                  v-permission="['product_list_update']"
                  v-if="
                    !(row.status == 9 && (row.disableType == 1 || row.disableType == 2 || row.disableType == 4)) &&
                    ((row.status != 20 && row.status != 8 && row.activityType !== 1 && row.activityType !== 2 && row.activityType !== 3) ||
                    (row.activityType === 3 && row.status != 9 && row.status != 20 && row.status != 8) ||
                    (row.activityType === 1 && row.ptSource == 2 && (row.ptStatus == 1 || row.ptStatus == 2 || row.ptStatus == 3 || row.ptStatus == 4)))
                  "
                  type="text"
                  @click="operationClick(2, row)"
                  >编辑</el-button
                >
                <el-button
                  v-permission="['product_list_up']"
                  v-if="((row.status == 4 || row.status == 6) && row.activityType !== 3) || (row.activityType === 3 && (row.status === 2 || row.status === 3 || row.status === 4 || row.status === 6))"
                  type="text"
                  @click="operationClick(3, row)"
                  >上架</el-button
                >
                <el-button
                  v-permission="['product_list_down']"
                  v-if="row.status == 1 && row.activityType !== 1"
                  type="text"
                  @click="tableModifyBtn(3, row)"
                  >下架</el-button
                >
                <el-button
                  v-permission="['product_list_down']"
                  v-if="row.ptStatus != 5 && row.ptStatus != 6 && row.activityType == 1"
                  type="text"
                  @click="actOffline.id = row.reportId; actOffline.visible = true; actOffline.row = row;showAdditionalInfor()"
                  >下架</el-button
                >
                <el-button
                  v-permission="['product_list_delete']"
                  v-if="
                    row.status != 20 &&
                    row.activityType !== 1 &&
                    row.activityType !== 2
                  "
                  type="text"
                  @click="operationClick(4, row)"
                  >删除</el-button
                >
                <el-button
                  v-if="
                    row.status != 20 &&
                    row.activityType !== 1 &&
                    row.activityType !== 2 &&
                    row.activityType !== 3
                  "
                  type="text"
                  @click="operationClick(5, row)"
                  >预览</el-button
                >
                <!-- <el-button v-if="row.activityType !== 1 && row.activityType !== 2" type="text" @click="operationClick(6,row)">查看操作日志</el-button> -->
                <el-button
                  v-if="row.status === 20 && row.activityType !== 3"
                  type="text"
                  @click="handleMaintainErpCode(row)"
                >
                  编辑ERP编码
                </el-button>

                <el-button type="text" @click="handleCkeckSkuLog(row)">
                  商品变更日志
                </el-button>
                <el-button type="text" v-if="row.activityType === 3" @click="handleCkeckOperationLog(row)">
                  活动变更日志
                </el-button>
                <el-button type="text" v-if="row.activityType === 1 && row.ptSource == 2" @click="handleCkeckActivityLog(row)">
                  活动变更日志
                </el-button>
                <el-button v-if="[1,2,3].includes(row.logicStatus) && row.activityType == 0" type="text" @click="operationClick(2, row, 'copyPg')">
                  复制批购包邮商品
                </el-button>
                <el-button
                  type="text"
                  @click="hasUpdateGoods(row)"
                  v-if="row.standardChangeMark === 1"
                >
                  标品信息有更新
                </el-button>
              </div>
              <!-- <div v-if="row.activityType === 1">
                <el-button
                  v-permission="['product_list_up']"
                  v-if="row.status == 4 || row.status == 6 "
                  type="text"
                  @click="operationClick(3,row)"
                >上架</el-button>
              </div> -->
            </template>
          </el-table-column>
        </template>
      </xyyTable>

    </div>
    <el-dialog title="提示" :visible="actOffline.visible" @close="actOffline.visible = false;showInfor = false">
      <div>
        <span style="color:red;">拼团活动下架后将置为删除，不可再次操作上架。</span>
        <span>请确定是否继续下架</span>
        <div v-if="showInfor">
          <p>
              如遇<span style="color:red;">厂家控销、查价</span>，可将活动设置为“<span style="color:red;">虚拟供应商</span>”，
              即拼团活动支付成功后才显示真实供应商信息
              如仍被查价，还可将活动供货信息配置为<span style="color:red;">含“VIP活跃用户（月）”标签的人群</span>，即活动仅对平台前20%高频采购用户可见
          </p>
          <span style="color: #4183d5;cursor: pointer;" @click="operationClick(2, actOffline.row);actOffline.visible = false">前往修改》</span>
        </div>
      </div>
      <span slot="footer">
        <el-button size="medium" @click="actOffline.visible = false;showInfor = false">取消</el-button>
        <el-button
          size="medium"
          style="margin-left: 20px"
          type="primary"
          @click="setActOffline"
          >确认下架</el-button>
      </span>
    </el-dialog>
    <el-dialog
      title="设置ERP编码"
      :close-on-click-modal="false"
      :visible="maintainErpCodeVis"
      width="800px"
      @close="maintainErpCodeVis = false"
    >
      <div>
        设置ERP编码
        <el-input
          v-model="currentGood.erpCode"
          clearable
          size="small"
          placeholder="请输入ERP编码"
          style="width: 400px"
        />
      </div>
      <span slot="footer">
        <el-button size="medium" @click="maintainErpCodeVis = false"
          >取消</el-button
        >
        <el-button
          size="medium"
          style="margin-left: 20px"
          type="primary"
          @click="handleSureMaintainErpCode"
          >确定</el-button
        >
      </span>
    </el-dialog>
    <el-dialog
      :visible="upVirtualShop.dialogVisible"
      @close="closeVirtualShop"
      title="修改虚拟供应商">
      <el-form size="small" label-width="170px">
        <el-form-item label="设置虚拟供应商" prop="isVirtualShop">
          <el-radio v-model="upVirtualShop.isVirtualShop" :label="1">是</el-radio>
          <el-radio v-model="upVirtualShop.isVirtualShop" :label="2">否</el-radio>
          <span style="color: #ff2121;display: inline-block;width: 80%;vertical-align: top">
            • 选“是”，则该拼团活动支付前都只显示虚拟供应商信息，支付成功后才显示真实供应商
            信息<br>
            • 选“否”，则正常显示供应商信息
          </span>
        </el-form-item>
      </el-form>
      <template slot="footer">
        <el-button type="primary" @click="submitVirtualShop">提交</el-button>
        <el-button @click="closeVirtualShop">取消</el-button>
      </template>
    </el-dialog>
    <ProductPreviewDialog
      v-if="productPreviewDialogVisible"
      :barcode="productPreviewBarcode"
      :product-preview-dialog-visible.sync="productPreviewDialogVisible"
    />

    <ModifyDialog
      v-if="modifyDialogVisible"
      :modify-dialog-visible.sync="modifyDialogVisible"
      :modify-config="modifyConfig"
      :isShowAreaPrice="isShowAreaPrice"
      @confirmCallback="handleFormSubmit"
    />

    <SkuLogDialog
      v-if="skuLogDialogVisible"
      :sku-log-dialog-visible.sync="skuLogDialogVisible"
      :modify-config="skuLogDialogConfig"
    />

    <BatchModifyProduct
      v-if="batchModifyProductVisible"
      :batch-modify-product-visible.sync="batchModifyProductVisible"
      :excel-template="excelTemplate"
      :price-type="shopConfig.priceType"
      :type="batchModifyType"
      :productTitle="productTitle"
      @refreshTable="handleFormSubmit"
    />

    <BatchModifyProductActivity
      v-if="batchModifyProductActivityVisible"
      :handle-close-activity="handleCloseActivity"
      @refreshTable="handleFormSubmit"
    />
    <PutOnShelvesSetting
      v-if="putOnShelvesVis"
      :handle-close-put-on-shelves="handleClosePutOnShelves"
      @refreshTable="handleFormSubmit"
    />

    <export-tip
      :change-export="changeExport"
      @handleExoprClose="handleExoprClose"
      @handleChangeExport="handleChangeExport"
    />

    <viewingInventoryLogs
      v-if="viewingInventoryLogsVisible"
      :inventory-log-dialog-visible.sync="viewingInventoryLogsVisible"
      :inventory-config="viewingInventoryLogsConfig"
    />

    <viewSkuAmountLog
      v-if="viewSkuAmountLogVis"
      :sku-amount-log-dialog-visible.sync="viewSkuAmountLogVis"
      :sku-amount-config="currentCheckItem"
    />

    <listOperationLog
      v-if="listOperationLogVisible"
      :list-operation-log-visible.sync="listOperationLogVisible"
      :modify-config="listOperationLogConfig"
    />

    <viewSkuLog
      v-if="viewSkuLogVis"
      :sku-log-dialog-visible.sync="viewSkuLogVis"
      :sku-config="currentCheckItem"
    />

    <ReleaseWarehousedGoodDialog
      v-if="releaseWarehousedGoodDialogVisible"
      :releaseWarehousedGoodDialogVisible.sync="
        releaseWarehousedGoodDialogVisible
      "
    />
    <TotalModal
      v-if="showTotalModal"
      :act-id="actId"
      :act-type="actType"
      @handleClose="handleClose"
    />
    <RepeatTips
      v-if="showRepeatTips"
      :product-abnormal="productAbnormal"
      @handleClose="handleClose"
      @tipsSearch="tipsSearch"
    />
    <goodsUpdateAlert
      ref="goodsUpdateAlert"
      @sure="goodsUpdateAlertFinish()"
    ></goodsUpdateAlert>
    <warningAlert
      ref="warningAlert"
      :count="warningform.count"
      :endDate="warningform.endDate"
      @filter="warningFilter"
    ></warningAlert>
    <!-- <updateSupply
      v-if="updateSupplyFlag"
      :dialogVisible="updateSupplyFlag"
      @supplyDialogClose="supplyDialogClose"
    >
    </updateSupply> -->
    <el-dialog
        :title="'修改供货信息'"
        :visible="updateSupplyFlag"
        width="45%"
        :before-close="supplyDialogClose"
        :append-to-body="true"
        v-if="updateSupplyFlag"
    >
      <component v-bind:is="supplyForm.activityType == 0 ? 'commonSupplyTypeConfig' : 'SupplyTypeConfig'"
	   	:baseCustomerGroupName="supplyForm.customerGroupName"
        :baseCustomerGroupId="supplyForm.customerGroupId"
        :sale-scope-dto="supplyForm"
        :isShopUpdate="true"
        :ispgby="false"
        ref="supplyTypeInfo" >
	  </component>
      <span slot="footer" class="dialog-footer">
        <el-button @click="cancelSupplyInfo" size="small">取 消</el-button>
        <el-button size="small" type="primary" @click="updateSupplyInfo">确 定</el-button>
      </span>
    </el-dialog>
    <pintuanPrice v-model:data="updateData" :isNew="true" @getList="handleFormSubmit"></pintuanPrice>
    <listActivityLog
      v-if="listActivityLogVisible"
      :list-operation-log-visible.sync="listActivityLogVisible"
      :modify-config="listActivityLogConfig"
    />
    <!-- 查看业务商圈 -->
    <business-circle-detail-dialog
      :row="selectViewRow"
      v-if="viewBusinessDialog"
      v-model="viewBusinessDialog"
    ></business-circle-detail-dialog>
    <!-- 查看拼团操作日志 -->
    <ptListOperationLog
      v-if="ptListOperationLogVisible"
      :list-operation-log-visible.sync="ptListOperationLogVisible"
      :modify-config="ptListOperationLogConfig"
    />
    <!-- 查看客户信息 -->
    <CustomerInfoLog
      v-if="crowdDialogVis"
      :market-customer-group-id="innerSelected"
      @cancelModal="cancelModal"
    />
    <!-- 人群选择弹框 -->
    <CustomerInfoLog v-if="crowdDialogVis" :market-customer-group-id="innerSelected" @cancelModal="cancelModal" />
    <regional-price :productData="productData" :regionalPriceVisible="regionalPriceVisible" @colse="colseRegional" ref="regionalPrice"/>
  </div>
</template>

<script>
import xyyTable from './components/table'
import SearchForm from '@/components/searchForm'
import exportTip from '@/views/other/components/exportTip'
import listOperationLog from './components/operationLogList'
import ptListOperationLog from "./components/ptListOperationLog"
import listActivityLog from '@/views/marketing/components/listOperationLog'
import CustomerInfoLog from '@/components/customer/customerInfoLog.vue'
import {
  getDefaultDataFromApollo
} from '@/api/market/collageActivity'
import { apiOffLine,exportWarningLimitData,apiCheckOffLine } from '@/api/market/collageActivity'
import BusinessCircleDetailDialog from '../business-circle/components/businessCircleDetailDialog.vue'
import {
  loadStatusCounts,
  pgbyStatistics,
  exportSku,
  batchUpAndDown,
  skuDelete,
  categoryTree,
  getProductList,
  findAct,
  apiDownloadTemplate,
  updateDeleteSkuErpCode,
  getSkuByProductId,
  warnInfo,
  compareFields,
  noStandardIdCount,
  updateParams,
  editCommonSkuScope,
  getGreyList,
  apiApplyListProduct,
  getApplyExport,
  checkRecommendReport,
  merchantGroupGrey
} from '@/api/product'
import {apiApplyList} from "@/api/market/collageActivity"
import { getSkuByBarcode } from '@/api/putProduct';
import { mapState } from 'vuex'
import { actionTracking } from '@/track/eventTracking'
import ProductPreviewDialog from './components/productPreviewDialog'
import ModifyDialog from './components/modifyDialog'
import SkuLogDialog from './components/skuLogDialog'
import BatchModifyProduct from './components/batchModifyProduct'
import BatchModifyProductActivity from './components/batchModifyProductActivity'
import PutOnShelvesSetting from './components/putOnShelvesSetting.vue'
import viewingInventoryLogs from './components/viewingInventoryLogs'
import viewSkuAmountLog from './components/viewSkuAmountLog'
import viewSkuLog from './components/viewSkuLog'
import ReleaseWarehousedGoodDialog from './components/releaseWarehousedGoodDialog'
import TotalModal from './components/totalModal'
import { searchItem } from './config'
import { searchItemFbp } from './configFbp'
import RepeatTips from './components/repeatTips'
import goodsUpdateAlert from './components/goodsUpdateAlert.vue'
import warningAlert from './components/warningAlert.vue'
import updateSupply from './components/updateSupply.vue'
import SupplyTypeConfig from '@/views/marketing/components/supplyTypeConfig.vue';
import commonSupplyTypeConfig from './components/commmonSupplyInformation.vue';
import pintuanPrice from '../pintuanDataUpdate/pintuanPrice.vue'
import regionalPrice from './components/regionalPrice.vue'
export default {
  name: 'ProductList',
  components: {
    pintuanPrice,
    SearchForm,
	commonSupplyTypeConfig,
    xyyTable,
    ProductPreviewDialog,
    ModifyDialog,
    SkuLogDialog,
    BatchModifyProduct,
    listActivityLog,
    BatchModifyProductActivity,
    PutOnShelvesSetting,
    exportTip,
    viewingInventoryLogs,
    ReleaseWarehousedGoodDialog,
    TotalModal,
    viewSkuAmountLog,
    viewSkuLog,
    RepeatTips,
    goodsUpdateAlert,
    warningAlert,
    updateSupply,
    SupplyTypeConfig,
    listOperationLog,
    BusinessCircleDetailDialog,
    ptListOperationLog,
    CustomerInfoLog,
    regionalPrice
  },
  data() {
    return {
      isShowNew:false,
      isShowToCollageActivityNew:false,
      actOffline: {
        visible: false,
        id: '',
        loading: false,
        row: {}
      },
      updateData: {
        actId: '',
        type: '',  //1:拼团价格，2：设置虚拟供应商，3：修改供货信息，4：修改活动库存
        visible: false,
      },
      freeGroupInfo: {
      isGrayShop: false,
      status: '',
      discountRadio: ''
      },
      isGrey: false, //是否在灰度名单内: 灰度名单内则展示批够包邮功能
      warningform: {
        count: undefined,
        endDate: ''
      },
      batchModifyType: 1,
      viewSkuLogVis: false,
      viewSkuAmountLogVis: false,
      currentGood: { erpCode: '' },
      maintainErpCodeVis: false,
      rules:3,
      saleDataRange:1,
      visibleShow:false,
      isProductTable: true,
      formModel: {
        productCode: '', // 商品编码
        showName: '', // 商品名称
        manufacturer: '', // 生产厂家
        approvalNumber: '', // 批准文号
        code: '', // 商品条码
        erpCode: '', // erp编码
        status: -99, // 状态:1-销售中，4-下架，6-待上架，8-待审核，9-审核未通过，20-删除
        priceSyncErp: '', // 价格是否同步ERP（0:否;1:是）
        stockSyncErp: '', // 库存是否同步ERP（0:否;1:是）
        saleType: '', // 药品类型
        characteristic: 0, // 商品特性：0全部、1特长药、2、专供、3独家代理、4特许专供
        busName: '', // 商圈名称
        havePic: '', // 是否有图: 0全部、1有图、2、无图
        categoryLevel: '', // 商品分类级别，1~4
        categoryId: '', // 商品分类id
        createTime: '', // 创建时间
        zeroPrice: false, // 查询价格为0商品：为true时查价格为0商品
        nearTerm: false, // 查询近效期商品：为true时查近效期商品
        authOffShelf: false, // 被强制下架商品，boolean：true查询，false不查询
        needRepair: false, // 需要修复商品，boolean：true查询，false不查询
        expired: false, // 查询过期商品
        expiredOffShelf: false, // 查询过期自动下架商品
        sameErpCode: false, // 查询ERP编码重复的商品
        sameErpCodeFromErp: false, // 查询ERP编码在ERP系统里重复的商品
        isPlatformSubsidy: false, // 查询平台补贴
        stockStatus: '', // 0-无库存，1-有库存
        emptyGrossProfitMargin: false, // 查询单体毛利率商品：为true时查单体毛利率商品
        emptyChainGrossProfitMargin: false, // 查询连锁毛利率商品：为true时查连锁毛利率商品
        emptyBasePrice: false, // 查询底价商品：为true时查底价商品
        csuid: '', // sku编码
        activityType: '', // 商品来源
        activityId: '', // 活动ID
        enrollId: '', // 报名ID
        groupSkuException: false, // 拼团商品状态异常标识  true-选中 false-未选中
        giftSkuException: false, // 赠品状态异常标识 true-选中 false- 未选中
        onSaleStockoutFlag: false, // 在售缺货异常标识
        autoSaleWithStock: false, // 来货自动上架筛选
        lowPrice: false, // 价格过低筛选businessFirstCategory
        notMaintenanceMeProductDice :false, //中药拓展字段维护
        haveMeMark: 0, //标品信息变更
        spec: '', //商品规格
        tiedMeProduct: '', //是否关联标品；0-否；1-是
        productTitle: '批量导入', // 批量弹框标题
        activityTypes: [3], //商品来源多选项
        isVirtualShop: '', //虚拟供应商
        isOrderDone:"" ,//是否成单
        biddingActivitySkuType: '', // 是否关联活动商品1. 全部  2. 拼团商品 3. 批购包邮商品 4. 未关联
        isBiddingProduct:"",//平台竞价商品
        haveAreaPrice: "", // 是否设置区域价
        availableQty:"",//库存数量
        availableQtySymbol :0,//库存数量f:
        directionFiled:3,//排序字段
        direction:0,//排序规则
        saleDataRange:1,//"范围"
        rules:3,//规则
        originalBarcode: ""
      },
      supplyForm: {
        customerGroupId: null,
        customerGroupName: null
      },
      formItems: searchItem.formItems,
      productStatusOptions: [],
      productCategoryLevelOptions: [],
      modifyValue: '批量修改',
      exportValue: "导出",
      exportOptions: [
        {
          label: "导出商品",
          value: 1
        },
        {
          label: "导出拼团活动",
          value: 2
        }
      ],
      modifyOptions: [
        {
          label: '批量上下线',
          value: 7
        },
        {
          label: '批量修改商品信息',
          value: 1
        },
        {
          label: '批量修改库存',
          value: 2
        },
        {
          label: '批量修改价格',
          value: 3
        },
        {
          label: '批量修改ERP编码',
          value: 4
        },
        {
          label: '批量修改批购包邮商品',
          value: 10
        },
        {
          label: '批量修改拼团活动',
          value: 6
        },
        {
          label: '批量设置区域价格',
          value: 8
        }
      ],
      productAbnormal: {
        priceZero: 0,
        validity: 0,
        infoError: 0,
        offShelves: 0,
        correct: 0,
        expiredCount: 0,
        expiredOffShelfCount: 0,
        sameErpCodeCount: 0,
        emptyEffect: 0,
        emptyGrossProfitMargin: 0, // 单体毛利率为空/0
        emptyChainGrossProfitMargin: 0, // 连锁毛利率为空/0
        emptyBasePrice: 0, // 底价为空/0
        groupSkuExceptionCount: 0, // 拼团商品状态异常
        errorGiftSku: 0, // 赠品状态异常
        onSaleStockoutCount: 0, // 在售缺货
        sameErpCodeFromErpCount: 0, // 普通商品在erp系统重复数量
        autoSaleWithStockCount: 0, // 来货自动上架
        lowPriceCount: 0, // 价格过低提醒
        haveMeProductMarkCount: 0, //标品信息变更，一件检查商品信息
        platformSubsidy: 0, // 平台补贴数量
        notMaintenanceMeProductDice:0, //中药拓展字段维护
      },
      editConfig: {
        warningStartPersonalQtyCount: 0,
        searchWarningStartPersonalQtyCount: false,
        warningRateStatusCount: 0,
        searchWarningRateStatusCount: false,
      },
      showRepeatTips: false, // ERP重复提醒
      activeName: '-99', // 全部
      selectList: [],
      tableLoading: false,
      tableConfig: {
        data: [],
        col: [
          {
            name: '商品信息',
            index: 'productInfo',
            slot: 'productInfo'
          },
          {
            name: '展示名称',
            index: 'showName',
            slot: 'showName'
          },
          {
            name: '价格',
            index: 'price',
            slot: 'price'
          },
          {
            name: '库存',
            index: 'totalStock',
            slot: 'totalStock'
          },
          {
            name: '供货信息',
            index: 'busAreaName',
            slot: 'busAreaName'
          },
          {
            name: '商品状态',
            index: 'status',
            slot: 'status'
          },
          {
            index: 'salesData',
            name: '销售数据',
            slot: 'salesData'
          },
          {
            index: 'operation',
            name: '操作',
            slot: 'operation'
          }
        ],
        operation: [
          {
            name: '详情',
            type: 1
          },
          {
            name: '编辑',
            type: 2
          },
          {
            name: '上架',
            type: 3
          },
          {
            name: '删除',
            type: 4
          },
          {
            name: '预览',
            type: 5
          },
          {
            name: '查看操作日志',
            type: 6
          }
        ]
      },
      selectViewRow: {},
      viewBusinessDialog: false, // 查看业务商圈
      ptConfig: {
          data: [],
          col: [
          {
            index: 'frameReportId',
            name: '报名ID/活动ID',
            width: 100,
            slot: true
          },
          {
            index: 'status',
            name: '活动状态',
            width: 100,
            slot: true
          },
          {
            index: 'commodityInformation',
            name: '商品信息',
            width: 220,
            slot: true
          },
          {
            index: 'groupPrice',
            name: '拼团价格',
            width: 150,
            slot: true
          },
          {
            index: 'isCopyCsuModel',
            name: '供货信息',
            width: 200,
            slot: true
          },
          {
            index: 'activityInventory',
            name: '活动库存',
            width: 150,
            slot: true
          },
          {
            index: 'reportTheme',
            name: '拼团主题',
            width: 100,
          },
          {
            index: 'activitySaleDataSummaryInfo',
            name: '销售数据',
            width: 160,
            slot: true
          },
          {
            index: 'actTime',
            name: '活动时间',
            slot: true
          },
          {
            index: 'operation',
            name: '操作',
            width: 80,
            slot: true
          }
          ],
      },
      listQuery: {
        pageSize: 10,
        page: 1,
        total: 0
      },
      ptListQuery: {
        pageSize: 10,
        page: 1,
        total: 0
      },
      productPreviewDialogVisible: false,
      productPreviewBarcode: '',
      modifyDialogVisible: false,
      modifyConfig: {
        modifyType: '',
        title: '',
        suggestPrice: null,
        chainPrice: null,
        grossProfitMargin: null,
        chainGrossProfitMargin: null,
        basePrice: null,
        priceSyncErp: null,
        chainPriceSyncErp: null,
        skuAreaPriceVos: []
      },
      skuLogDialogVisible: false,
      skuLogDialogConfig: { barcode: '' },
      listOperationLogConfig: {
        marketingId: null,
        activityType: 30,
      },
      listOperationLogVisible: false,
      batchModifyProductVisible: false,
      ptListOperationLogVisible: false,
      ptListOperationLogConfig: {},
      putOnShelvesVis: false, // 商品上架设置弹窗
      batchModifyProductActivityVisible: false,
      excelTemplate:
        'https://upload.ybm100.com/pop/temp/商品基本信息批量修改模板.xlsx',
      colorTipStatus: '',
      isFirst: true,
      changeExport: false,
      viewingInventoryLogsVisible: false,
      viewingInventoryLogsConfig: {
        barcode: '',
        productName: '',
        erpCode: ''
      },
      currentCheckItem: {
        barcode: '',
        productName: '',
        erpCode: ''
      },
      releaseWarehousedGoodDialogVisible: false,
      showTotalModal: false,
      actId: '',
      actType: '',
      updateSupplyFlag: false, //修改供货信息弹窗
	    personalLimitTypeList: ["不限制", "活动期间限购", "每天（每天00:00至24:00）", "单笔订单限购", "每周（周一00:00至周日24:00）", "每月（每月1号00:00至每月最后一天24:00）"],
      listActivityLogConfig: {},
      listActivityLogVisible: false,
      showInfor: false, // 是否展示拼团的提示,
      upVirtualShop : {
        dialogVisible: false,
        isVirtualShop: "",
        actId: "",
        updateType: ""
      },
      weekObj: {
        1: '周一',
        2: '周二',
        3: '周三',
        4: '周四',
        5: '周五',
        6: '周六',
        7: '周日'
      },
      crowdDialogVis: false, // 人群弹窗
      innerSelected: "", // 人群id
      newTime: new Date().getTime(), // 当前时间
      regionalPriceVisible: false,
      productData: {},
      isShowAreaPrice: false,
      refresh: true,
    }
  },
  computed: {
    ...mapState('app', ['shopConfig']),
    tabStatusOptions() {
      const options = this.productStatusOptions.map((item) => ({
        statusName: item.statusType === -99 ? '全部商品' : item.statusName,
        statusType: item.statusType,
        count: item.count
      }))
      return options
    }
  },
  activated() {
    if (this.$route.query?.refresh || this.refresh) {
      this.refresh = false;
      this.formModel = this.$refs.searchForm.getInitialFormModel();
      this.queryList();
    }
    this.activate()
    this.getStatistics('first')
    this.getDefaultDataFromApollo()
    if(this.$route.query.activeName){
      this.formModel.status = Number(this.$route.query.activeName)
      this.activeName=this.$route.query.activeName
    }
  },
  created() {
    window.clearData['/productList'] = () => {
      this.refresh = true;
    }
    if (this.shopConfig.isFbp) {
      this.modifyOptions = [
        {
          label: '批量修改商品信息',
          value: 1
        },
        {
          label: '批量修改价格',
          value: 3
        }
      ]
      this.formItems = searchItemFbp.formItems;
    }

    this.getCategoryTree()
    this.initialFormModel = JSON.parse(JSON.stringify(this.formModel))
    checkRecommendReport().then(res => {
      if(res.code === 1000) {
        this.isShowToCollageActivityNew = res.data.supportStatus
      }
    })
    merchantGroupGrey().then(res => {
      if(res.code === 0) {
        this.isShowAreaPrice = res.result
        if(!this.isShowAreaPrice) {
          this.formItems = this.formItems.filter(item => item.prop !== 'haveAreaPrice')
          this.modifyOptions = this.modifyOptions.filter(item => item.value !== 8)
        }
      }
    })
  },
  mounted() {
    if (Number(this.$route.query.homeEnter) !== 1) {
      this.queryList()
    }
    this.apinoStandardIdCount()
    this.formModel.barcode = this.$route.query.barcode
    this.getGreyList()
  },
  methods: {
     renderHeader(h, { column }) { //销售数据
      return h('div', [
        h('span', column.label),
        h(
          'el-tooltip',
          {
            props: {
              content:
                '销售数据剔除未支付、已取消、已退款且部分退中活动商品应发货数量等于0的订单数据，仅统计已支付的有效订单。可至详情页查询未支付、已取消订单',
              placement: 'right'
            }
          },
          [h('i', { class: 'el-icon-warning-outline' })]
        )
      ])
    },
    registrationFormat(row) { // 格式化时间
      const start = this.getShowTime(row.createTime)
      return start || '-'
    },
    handerEdit(row) {
      sessionStorage.setItem('editCollageItem', JSON.stringify(row));
      const path = '/editCollageActivity';
      const obj = {
        frameReportId: row.frameReportId,
        fromType: 'edit'
      }
      sessionStorage.setItem('collageActType', 'edit')
      window.openTab(path, obj)
    },
    handerOffline(row) {
      const frameReportId = row.frameReportId
      apiOffLine({ frameReportId })
        .then((res) => {
          if (res.status === 'success') {
            this.getList(this.listQuery, true)
          } else {
            this.$message.error(res.msg || res.errorMsg || '服务异常')
          }
        })
        .catch(() => {})
    },
    viewOperationLog(row) {
      this.ptListOperationLogConfig = { reportId: row.frameReportId }
      this.ptListOperationLogVisible = true
    },
    // 查看业务商圈
    viewBusiness(row) {
      this.selectViewRow = row
      this.viewBusinessDialog = true
    },
    giveSku(row) { // 查看单品送赠品
      if (row.isGiveSku) {
        // this.$router.push(`/mzPromotion?csuid=${row.skuId}`);
        window.openTab('/mzPromotion', { csuid: row.skuId })
      } else {
        sessionStorage.setItem('pinTuanProductInfo', JSON.stringify(row))
        // window.openTab('/addMzActive', { fromType: 'pinTuan', reportId: row.frameReportId });
        this.$router.push(
          `/addMzActive?fromType=pinTuan&reportId=${row.frameReportId}`
        )
      }
    },
    colseRegional() {
      this.regionalPriceVisible = false
    },
    showRegional(row) {
      this.productData = row
      this.regionalPriceVisible = true
      this.$refs.regionalPrice.redrawTable()
    },
    visibleShowOpen(){
      this.rules=this.formModel.rules
      this.saleDataRange=this.formModel.saleDataRange
      this.visibleShow=true
    },
    trueShow(){
      this.formModel.rules=this.rules
      this.formModel.saleDataRange=this.saleDataRange
      this.formModel.directionFiled =this.formModel.rules
      this.handleFormSubmit()
      this.visibleShow=false
    },
    toAuction(){
      window.openTab('/auctionProductManagement')
    },
    handleClick(e){
      e === this.formModel.saleDataRange ? this.formModel.saleDataRange = 0 : this.formModel.saleDataRange = e
      this.handleFormSubmit()
    },
    getShowTime(aTime) {
      let time = new Date(aTime + 8 * 3600 * 1000)
        .toJSON()
      if(time) {
        time = time.substr(0, 19).replace('T', ' ')
      }
      return time
    },
     ex(row){
      this.$set(row,'isShow',!row.isShow)
    },
    validFormat(row,type) {
      // type 用来区分是拼团还是商品列表，1为拼团，不传或0为商品
      const start = this.getShowTime(row.actStartTime)
      const end = this.getShowTime(row.actEndTime)
      if (Number(row.segmentType) === 1) {
        return [start && end ? `${start} 至 ${end}` : '-']
      }
      if (
        Array.isArray(row.multiSegmentDTOList)
      ) {
        const list = []
        if(type === 1) {
          list.push(
          `${this.formatDate(row.actStartTime, 'YMD')} 至 ${this.formatDate(
            row.actEndTime,
            'YMD'
          )}`)
        }
        row.multiSegmentDTOList.forEach((obj) => {
          list.push(
            `${this.weekObj[obj.stime.cycleNum]} ${
              Number(obj.stime.hour) < 10
                ? '0' + obj.stime.hour
                : obj.stime.hour
            }:${
              Number(obj.stime.minute) < 10
                ? '0' + obj.stime.minute
                : obj.stime.minute
            } 至 ${this.weekObj[obj.etime.cycleNum]} ${
              Number(obj.etime.hour) < 10
                ? '0' + obj.etime.hour
                : obj.etime.hour
            }:${
              Number(obj.etime.minute) < 10
                ? '0' + obj.etime.minute
                : obj.etime.minute
            }`
          )
        })
        return list
      }
    },
    toCollageActivity(){
      window.openTab('/collageActivity', {})
    },
    toCollageActivityNew(){
      window.openTab('/collageActivityNew', {})
    },
    lookMore(row){
      // 发布商品
      if(row.activityType === 0){
        window.openTab('/orderList', { skuId:row.csuid })
      }else{
        var path = '/groupSalesData'
        console.log(row)
        var obj = {
          activityType: row.promotionActivityType,
          marketingIdStr: row.activityId,
          csuId: row.csuid
        }
      window.openTab(path, obj)
      }

  // this.$router.push("/groupSalesData")
    },
    handleCkeckActivityLog(row) {
      this.listActivityLogConfig = { reportId: row.reportId }
      this.listActivityLogVisible = true
    },
    setActOffline() {
      if (this.actOffline.loading) return ;
      this.actOffline.loading = true;
      console.log(this.actOffline);
      apiOffLine({ frameReportId: this.actOffline.id })
        .then((res) => {
          if (res.status === 'success') {
            this.$message.success('下架成功');
            this.actOffline.visible = false;
            this.queryList()
          } else {
            this.$message.error(res.msg || res.errorMsg || '服务异常')
          }
        }).finally(() => {
          this.showInfor = false;
          this.actOffline.loading = false;
        })
    },
	getDefaultDataFromApollo(type) {
      getDefaultDataFromApollo().then((res) => {
        // console.log(1211212121, res);
        if (res.code === 1000) {
          this.freeGroupInfo.status = res.data.status || ''
          this.freeGroupInfo.isGrayShop = res.data.isGrayShop || false
          this.freeGroupInfo.discountRadio = res.data.discountRadio || ''
          if (type) {
            // window.openTab('/freeGroup', { followHeartTopSkuMaxCount: res.data.followHeartTopSkuMaxCount, conversionFactor: res.data.conversionFactor });
            if (this.freeGroupInfo.isGrayShop) {
              window.openTab('/freeGroup', {
                followHeartTopSkuMaxCount: res.data.followHeartTopSkuMaxCount,
                conversionFactor: res.data.conversionFactor,
                importBlackListSkuMaxCount: res.data.importBlackListSkuMaxCount
              })
            } else {
              this.$message.warning('暂未开通此功能，请联系平台运营')
            }
          }
        }
      })
    },
	go(to, query, close) {
		if (close) {
			window.closeTab(this.$route.fullPath, true);
		}
		setTimeout(() => {
			window.openTab(to, query ? query : {});
		}, 0)
	},
    getGreyList() {
      getGreyList().then(res => {
        if (res.code === 1000) {
          this.isGrey = res.data.supportStatus;
        } else {
          this.isGrey = false;
        }
        if (this.shopConfig.isFbp) {
          this.modifyOptions = [
            {
              label: '批量修改商品信息',
              value: 1
            },
            {
              label: '批量修改价格',
              value: 3
            }
          ]
          this.formItems = searchItemFbp.formItems;
          return;
        }
        if (!this.isGrey) {
          this.modifyOptions = [
            {
              label: '批量修改商品信息',
              value: 1
            },
            {
              label: '批量修改库存',
              value: 2
            },
            {
              label: '批量修改价格',
              value: 3
            },
            {
              label: '批量修改ERP编码',
              value: 4
            }
          ]
          this.formItems = searchItemFbp.formItems;
        }
      })
    },
    hasUpdateGoods(row) {
      compareFields().then((ress) => {
        if (ress.code != 0) {
          this.$message.warning(ress.message)
          return
        }
        warnInfo({ barcode: row.barcode, source: row.source }).then((res) => {
          if (res.code !== 0) {
            this.$message.error(res.message)
            return
          }
          let data = res.data.meSkuInfo
          let oldData = res.data.popSkuInfo
          let oldIMGUrl = this.getImageUrl(oldData)
          let newImgUrl = this.getImageUrl(data)
          oldData.imagesList = {
            urlVal: oldIMGUrl ? oldIMGUrl : ''
          }
          data.imagesList = newImgUrl ? newImgUrl : undefined
          data.source = row.source
          oldData.source = row.source
          const keys = this.getFirstCategoryData(
            ress.data,
            data.businessFirstCategoryCode
          )
          this.$refs.goodsUpdateAlert.open(
            data,
            oldData,
            row,
            '/productList',
            keys
          )
        })
      })
    },
    updateSupplyInfo() {
      let supplyInfo = this.$refs['supplyTypeInfo'].getAllSupplyInfo();
      console.log(supplyInfo, 'supplyinfo')
	  if (this.supplyForm.activityType == 0 && supplyInfo.controlRosterType !== 2 && supplyInfo.controlUserTypes.length == 0) {
		this.$message.error("货对象不能为空，请选择供货对象后提交");
		return;
	  }
	  if (this.supplyForm.activityType == 0 && supplyInfo.controlRosterType !== 2 && supplyInfo.busAreaId == -1) {
		this.$message.error("商圈不能为空");
		return;
	  }
      var saleParmas = {};
      if (supplyInfo.isCopySaleArea === 1) {
        saleParmas = {
          isCopySaleArea: 1
        }
      } else if (supplyInfo.isCopySaleArea === 2) {
        saleParmas = {
          isCopySaleArea: 2,
          customerGroupId: supplyInfo.customerGroupId
        }
      } else if (supplyInfo.isCopySaleArea === 3) {
        saleParmas = {
          isCopySaleArea: 3,
          isCopyBusArea: supplyInfo.controlRosterType === 2 ? 1 : supplyInfo.isCopyBusArea,
          busAreaId: supplyInfo.isCopyBusArea === 2 ? supplyInfo.busAreaId : null,
          isCopyControlUser: supplyInfo.controlRosterType === 2 ? 1 : supplyInfo.isCopyControlUser,
          controlUserTypes: supplyInfo.isCopyControlUser === 2 ? supplyInfo.controlUserTypes && supplyInfo.controlUserTypes.join(",") : null,
          isCopyControlRoster: supplyInfo.isCopyControlRoster,
          controlGroupId: supplyInfo.controlGroupId,
          controlGroupName: supplyInfo.controlGroupName,
          controlRosterType: supplyInfo.controlRosterType,
        }
        for(let i in saleParmas) {
          if (saleParmas[i] === null) {
            delete saleParmas[i];
          }
        }
      }
	  //活动商品
      let params = this.supplyForm.activityType == 0 ? {
		updateType: 3, //修改类型  1 价格 2库存 3供货信息
        barcode: this.supplyForm.barcode,
        saleScopeDTO: saleParmas
	  } : {
        updateType: 3, //修改类型  1 价格 2库存 3供货信息
        actId: this.supplyForm.activityId,
        saleScopeDTO: saleParmas
      }
      params.controlUserTypes && params.controlUserTypes.join(",")
	  const fn = this.supplyForm.activityType == 0 ? editCommonSkuScope : updateParams
      fn(params).then(res => {
        if (res.code === 1000) {
          this.$message.success('供货信息修改成功');
          this.cancelSupplyInfo();
          this.handleFormSubmit()
        } else {
          // this.$message.error(res.message)
          this.$message.error(res.msg);
        }
      })
    },
    cancelSupplyInfo() {
      this.updateSupplyFlag = false;
      this.supplyForm = {
        customerGroupId: null,
        customerGroupName: null
      }
    },
    warningFilter(){
      this.formModel.status = 1
      this.formModel.tiedMeProduct = "0"
      this.queryList()
    },
    supplyDialogClose() {

      this.updateSupplyFlag = false;
    },
    apinoStandardIdCount() {
      noStandardIdCount().then((res) => {
        if (res.code !== 0) {
          this.$message.error(res.message)
          return
        }
        if(res.data.popupFlag === false){
          return
        }

        this.warningform = { count: res.data.count, endDate: res.data.endDate }
        this.$refs.warningAlert.open();
      })
    },
    changeSupply(row) {
      this.updateSupplyFlag = true;
      console.log(row, 'rrr')
      this.supplyForm = row;
      this.supplyForm.isCopyControlRoster = 2;  // 是否显示黑白名单选择
    },
    getFirstCategoryData(data, categoryid) {
      if (
        categoryid.toString() === '100001' ||
        categoryid.toString() === '100007' ||
        categoryid.toString() === '100008'
      ) {
        return data['nomalList']
      } else if (
        categoryid.toString() === '100004' ||
        categoryid.toString() === '100010' ||
        categoryid.toString() === '262683'
      ) {
        return data['chiMedList']
      } else if (
        categoryid.toString() === '100002' ||
        categoryid.toString() === '100009'
      ) {
        return data['drugList']
      } else if (
        categoryid.toString() === '100003' ||
        categoryid.toString() === '262104'
      ) {
        return data['healthList']
      } else if (categoryid.toString() === '100005') {
        return data['instrumentList']
      } else {
        return []
      }
    },
    handleDownWarningStartPersonalQtyCount() {
      actionTracking('group_management_top_quick_search', {
        filter_item: 'suggestion_download'
      })
      exportWarningLimitData().then((res) => {
        this.util.exportExcel(res, '起拼/限购建议.xls')
      })
    },
    getImageUrl(data) {
      if (data.imageUrl) {
        return data.imageUrl.split(',')[0]
      }
      if (data.instrutionImageUrl) {
        return data.instrutionImageUrl.split(',')[0]
      }
      return ''
    },
    handleCascaderVal(value) {
      this.formModel.businessFirstCategory = value[0] || ''
      this.formModel.businessSecondCategory = value[1] || ''
    },
    soryByPrice({column, prop, order}) {
      if(column.label=='价格'){
        this.formModel.directionFiled=1
      }else if(column.label=='库存'){
        this.formModel.directionFiled=2
      }
      if(order=='descending'){
        this.formModel.direction=2
      }else if(order=='ascending'){
        this.formModel.direction=1
      }else{
        this.formModel.direction=0
      }
      this.handleFormSubmit()
      console.log(order)
    },
    handleCkeckSkuLog(row) {
      actionTracking('commodity_log', {})
      this.currentCheckItem.barcode = row.barcode
      this.currentCheckItem.productName = row.productName
      this.currentCheckItem.erpCode = row.erpCode
      this.viewSkuLogVis = true
    },
    handleCkeckOperationLog(row) {
      this.listOperationLogConfig.marketingId = row.activityId
      this.listOperationLogVisible = true
    },
    handleCheckSkuAmountLog(row) {
      this.currentCheckItem.barcode = row.barcode
      this.currentCheckItem.productName = row.productName
      this.currentCheckItem.erpCode = row.erpCode
      this.viewSkuAmountLogVis = true
    },
    handleSureMaintainErpCode() {
      if (this.currentGood && this.currentGood.erpCode === '') {
        this.$message.warning('ERP编码不能为空')
        return false
      }
      if (
        this.currentGood &&
        this.currentGood.erpCode &&
        this.currentGood.erpCode.length > 64
      ) {
        this.$message.warning('ERP编码长度不能超过64个字符')
        return false
      }
      updateDeleteSkuErpCode({
        barcode: this.currentGood.barcode,
        erpCode: this.currentGood.erpCode
      }).then((res) => {
        if (res && res.code === 0) {
          this.$message.success('修改成功')
          this.queryList()
        } else {
          this.$message.error('修改失败')
        }
        // this.currentGood = null;
        this.maintainErpCodeVis = false
      })
    },
    handleMaintainErpCode(row) {
      this.currentGood = { ...row }
      this.maintainErpCodeVis = true
    },
    transferTime(time) {
      return window.dayjs(time).format('YYYY-MM-DD HH:mm:ss')
    },
    handleCloseActivity() {
      this.batchModifyProductActivityVisible = false
    },
    handleClosePutOnShelves() {
      this.putOnShelvesVis = false
    },
    activate() {
      const { query } = this.$route
      if (query && Object.keys(query).length > 0) {
        this.resetTipsParams()
        this.$set(this.formModel, 'status', '')
        this.$set(this.formModel, 'stockStatus', '')
        this.$set(this.formModel, 'barcode', '')
        Object.keys(query).map((key) => {
          if (key === 'status') {
            this.$set(this.formModel, key, Number(query[key]))
          } else {
            this.$set(this.formModel, key, query[key])
          }
          if (key === 'barcode') {
            this.$set(this.formModel, key, query[key])
          }
        })
        this.$nextTick(() => {
          setTimeout(() => {
            this.queryList()
          }, 0)
        })
      }
    },

    goodsUpdateAlertFinish() {
      this.handleFormSubmit()
    },

    handleFormSubmit() {
      this.resetTipsParams()
      this.queryList()
      this.getStatistics()
    },
    handleFormReset(obj) {
      this.rules=3
      this.saleDataRange=1
      this.util.clearLoacl('localShopQuery')
      this.formModel = obj
      if (this.$route.query.barcode) {
        this.formModel.barcode = ''
      }
      this.listQuery = {
        page: 1,
        pageSize: 10,
        total: 0
      }
      this.colorTipStatus = ''
      setTimeout(() => {
        this.queryList()
      }, 500)
      // this.refreshList();
    },
    statusNameStr(value) {
      let name = ''
      this.productStatusOptions.forEach((item) => {
        if (item && Number(item.statusType) === Number(value)) {
          name = item.statusName
        }
      })
      return name || ''
    },
    async getCategoryTree() {
      try {
        const res = await categoryTree()
        if (res.code === 0) {
          this.productCategoryLevelOptions = res.data.children
        }
      } catch (e) {
        console.log(e)
      }
    },
    async loadStatusCounts(params) {
      try {
        let tempParams = JSON.parse(JSON.stringify(params))
        delete tempParams.status
        const res = await loadStatusCounts(tempParams)
        if (res) {
          if (this.shopConfig.isFbp) {
            const arr = res.filter((i) => i.statusType != 2 )
            this.productStatusOptions = [
              {
                statusName: '全部',
                statusType: -99
              },
              ...arr
            ]
          } else {
            this.productStatusOptions = [
              {
                statusName: '全部',
                statusType: -99
              },
              ...res
            ]
          }
          // 过滤掉拼团活动审核与待审核
          this.productStatusOptions = this.productStatusOptions.filter(item => ![50, 51].includes(item.statusType));
        }
      } catch (e) {
        console.log(e)
      }
    },
    async getStatistics(strType) {
      try {
        const res = await pgbyStatistics()
        if (res.code === 0) {
          const {
            zeroPriceCount,
            nearTermCount,
            errorInfoCount,
            authOffShelfCount,
            needRepairCount,
            expiredCount,
            expiredOffShelfCount,
            sameErpCodeCount,
            emptyEffect,
            emptyGrossProfitMargin,
            emptyChainGrossProfitMargin,
            emptyBasePrice,
            groupSkuExceptionCount,
            errorGiftSku,
            onSaleStockoutCount,
            sameErpCodeFromErpCount,
            autoSaleWithStockCount,
            lowPriceCount,
            haveMeProductMarkCount,
            platformSubsidy,
            notMaintenanceMeProductDice
          } = res.data
          this.productAbnormal = {
            priceZero: zeroPriceCount || 0,
            validity: nearTermCount || 0,
            infoError: errorInfoCount || 0,
            offShelves: authOffShelfCount || 0,
            correct: needRepairCount || 0,
            expiredCount: expiredCount || 0,
            expiredOffShelfCount: expiredOffShelfCount || 0,
            sameErpCodeCount: sameErpCodeCount || 0,
            emptyEffect: emptyEffect || 0,
            emptyGrossProfitMargin: emptyGrossProfitMargin || 0,
            emptyChainGrossProfitMargin: emptyChainGrossProfitMargin || 0,
            emptyBasePrice: emptyBasePrice || 0,
            groupSkuExceptionCount: groupSkuExceptionCount || 0,
            errorGiftSku: errorGiftSku || 0,
            onSaleStockoutCount: onSaleStockoutCount || 0,
            sameErpCodeFromErpCount: sameErpCodeFromErpCount || 0,
            autoSaleWithStockCount: autoSaleWithStockCount || 0,
            lowPriceCount: lowPriceCount || 0,
            haveMeProductMarkCount: haveMeProductMarkCount || 0,
            platformSubsidy: platformSubsidy || 0,
            notMaintenanceMeProductDice: notMaintenanceMeProductDice || 0
          }
          if (
            strType === 'first' &&
            (sameErpCodeFromErpCount > 0 || sameErpCodeCount > 0)
          ) {
            this.showRepeatTips = true
          }
        }
      } catch (e) {
        console.log(e)
      }
    },
    getParams() {
      const params = { ...this.formModel }
      params.activityTypes = params.activityTypes && params.activityTypes.join(",");
      if (params.status === -99) {
        params.status = ''
      }
      // if(params.haveMeMark === 0){
      //   params.haveMeMark = undefined;
      // }
      if (params.createTime && params.createTime.length > 0) {
        params.createTimeStart = params.createTime[0]
        params.createTimeEnd = params.createTime[1]
      }
      delete params.createTime
      const selected = this.$refs.productCategoryLevel.getCheckedNodes(true)[0]
      if (selected && selected.data) {
        params.categoryLevel = selected.data.level
        params.categoryId = selected.data.id
      }
      //排序
      if(params.direction==0){
        params.directionFiled=0
      }
      return params
    },
    async queryList(listQuery, from) {
      const that = this
      this.tableLoading = true
      if (listQuery) {
        const { pageSize, page } = listQuery
        this.listQuery.pageSize = pageSize
        this.listQuery.page = page
      }
      let params = this.getParams()
      if(params.status === 50 || params.status === 51) {
        if (listQuery) {
          const { pageSize, page } = listQuery
          this.ptListQuery.pageSize = pageSize || 10
          this.ptListQuery.page = page || 1
        }
        this.isProductTable = false
        this.getList("",params)
        this.tableLoading = false
        return
      }
      this.isProductTable = true
      const { pageSize, page } = this.listQuery
      params.page = page
      params.rows = pageSize
      console.log(JSON.stringify(params))
      this.activeName = params.status ? String(params.status) : '-99'
      if (from == 1) {
        const localParams = this.util.getLocal('localShopQuery')
        if (localParams) {
          params = localParams
          params.createTimeEnd = ''
          params.createTimeStart = ''
          this.formModel = { ...localParams }
          this.formModel.status = localParams.status
            ? Number(localParams.status)
            : -99
          this.activeName = localParams.status
            ? String(localParams.status)
            : '-99'
          this.createTime =
            localParams.createTimeStart && localParams.createTimeEnd
              ? [localParams.createTimeStart, localParams.createTimeEnd]
              : ''
          this.listQuery.pageSize = localParams.rows
          this.listQuery.page = localParams.page
          this.listQuery = Object.assign({}, this.listQuery)
        }
      }else if(from==999){
        params.page = 1
        params.queryRelateActSku=true
      } else {
        this.util.setLocal('localShopQuery', params)
      }
      this.loadStatusCounts(params)
      try {
        const res = await getProductList(params)
        that.tableLoading = false
        if (res && res.code === 0) {
          that.tableConfig.data = []
          that.tableConfig.data = this.initTableData(res.result.list)
          that.listQuery.total = res.result.total
          that.listQuery.pageSize = res.result.pageSize
        }
      } catch (e) {
        console.log(e)
        that.tableLoading = false
      }
    },
    // 导出拼团活动
    exportPtData() {
      let params = this.getParams()
      if(params.status === 50 || params.status === 51) {
        params.promotionStatus = params.status === 50 ? 1 : 2
      }
      getApplyExport(params).then((res) => {
        if (res.code !== 0) {
          this.$message.error(res.message)
        } else {
          this.changeExport = true;
        }
      })
    },
    getList(reset,param) {
      let params = this.getParams()
      if(params.activeName) {delete params.activeName}
      params.promotionStatus = params.status === 50 ? 1 : 2
      const { pageSize, page } = this.ptListQuery
      params.pageNum = page
      params.pageSize = pageSize
      const loading = this.$loading({
        lock: true,
        text: 'Loading',
        spinner: 'el-icon-loading',
        background: 'rgba(255,255,255, 0.8)'
      })
      apiApplyListProduct(params)
        .then((res) => {
          loading.close()
          if (res.status === 'success') {
            const { list, total } = res.data.data
            this.ptConfig.data = list || []
            this.$nextTick(() => { // 重新渲染表格，防止表格错乱
              this.$refs.ptTable.$refs.table.doLayout();
            })
            this.ptListQuery = {
              ...this.ptListQuery,
              total
            }
            if (reset) {
              this.getStatistics(params)
            }
          } else {
            this.$message.error(res.msg || res.errorMsg || '服务异常')
          }
        })
        .catch(() => {
          loading.close()
        })
        this.loadStatusCounts(params)
    },
    initTableData(data) {
      if (data && Array.isArray(data)) {
        data.map((item) => {
          // const ary = [];
          // item.productName && ary.push(`<span class="productName">${item.productName}</span>`);
          // item.manufacturer && ary.push(`<span class="manufacturer">${item.manufacturer}</span>`);
          // item.spec && ary.push(item.spec);
          // item.approvalNumber && ary.push(item.approvalNumber);
          // item.barcode && ary.push(item.barcode);
          // item.erpCode && ary.push(item.erpCode);
          // item.productInfo = ary.join('<br/>');
          return item
        })
        return data
      }
    },
    tabPaneLabel(item) {
      let str = ''
      if (item.count || item.count === 0) {
        str = `${item.statusName}(${item.count})`
      } else {
        str = `${item.statusName}`
      }
      return str
    },
    resetTipsParams() {
      this.formModel.zeroPrice = false
      this.formModel.nearTerm = false
      this.formModel.authOffShelf = false
      this.formModel.needRepair = false
      this.formModel.expired = false
      this.formModel.expiredOffShelf = false
      this.formModel.sameErpCode = false
      this.formModel.emptyGrossProfitMargin = false
      this.formModel.emptyChainGrossProfitMargin = false
      this.formModel.emptyBasePrice = false
      this.formModel.sameErpCodeFromErp = false
      this.formModel.autoSaleWithStock = false
      this.formModel.lowPrice = false
      this.formModel.giftSkuException = false
      this.formModel.isPlatformSubsidy = false
    },
    tipsSearch(key, value) {
      if (key === 'warningRateStatusCount') {
         this.editConfig.searchWarningRateStatusCount =
          !this.editConfig.searchWarningRateStatusCount
      }else if(key === "warningStartPersonalQtyCount") {
        this.editConfig.searchWarningStartPersonalQtyCount =
          !this.editConfig.searchWarningStartPersonalQtyCount
      }
      // else if(key === "platformSubsidy") {
      //   this.editConfig.isPlatformSubsidy =
      //     !this.editConfig.isPlatformSubsidy
      // }
      actionTracking('product_management_top_quick_search', {
        filter_item: {
          zeroPrice: '0_price',
          authOffShelf: 'wrong_removed',
          needRepair: 'wrong_modified_in_time',
          nearTerm: 'near_term',
          emptyEffect: 'empty_term',
          expiredOffShelf: 'overdue_removing',
          expired: 'overdue_removed',
          sameErpCode: 'duplicate_ERP_code',
          groupSkuException: 'abnormal_status',
          emptyGrossProfitMargin: 'single_gross',
          emptyChainGrossProfitMargin: 'chain_gross',
          emptyBasePrice: 'base_price',
          onSaleStockoutFlag: 'no_stock',
          giftSkuException: 'gift_abnormal_status',
          lowPrice: 'low_price',
          haveMeMark: 'have_me_mark',
          isPlatformSubsidy: "is_platform_subsidy",
        }[key]
      })
      this.formModel = this.$refs.searchForm.getInitialFormModel()
      this.formModel[key] = value

      this.colorTipStatus = key
      this.queryList()
    },
    // 导出
    async exportProduct() {
      const params = this.getParams()
      const ids = this.selectList.map((item) => item.id)
      params.ids = ids.join(',')
      const strList = []
      Object.keys(params).forEach((key) => {
        strList.push(`${key}=${params[key]}` || '')
      })
      console.log(strList.join('&'))
      try {
        // const res = await exportSku(params);
        // if (res) {
        //   top.open(`/sku/exportSku?${strList.join('&')}`);
        // }
        exportSku(params).then((res) => {
          if (res.code !== 0) {
            this.$message.error(res.message)
            return
          }
          this.changeExport = true
        })
      } catch (e) {
        console.log(e)
      }
    },
    handleChangeExport(info) {
      this.changeExport = false
      if (info === 'go') {
        const path = '/downloadList'
        window.openTab(path)
        // that.$router.push({ path: '/downloadList' });
      }
    },
    handleExoprClose() {
      this.changeExport = false
    },
    releaseProduct() {
      // 发布商品
      var path = '/putProduct/multiple'
      let obj = {fromType: 'edit',form: "freeMail" }
      window.openTab(path, obj)
    },
    // 批量发布
    batchProduct() {
      // const mid = 3333333;
      // window.top.$('#mainFrameTabs').bTabsAdd(mid, '批量发布商品', '/manage/#/batchGoods');
      const path = '/batchGoods'
      const obj = { from: 'toBatchGoods' }
      window.openTab(path, obj)
    },
    // 批量上下架  1:上架、2、下架
    async batchUpAndDown(value) {
      if (this.selectList.length > 0) {
        const Ids = this.selectList.map((item) => item.id)
        const params = {
          skuIds: Ids,
          status: Number(value)
        }
        const tip = []
        this.selectList.map((item) => {
          if (item.nearExpired || item.farExpired) {
            tip.push(item.id)
          }
        })
        // 批量上架不判断 商品“近效期至”或“远效期至”为空或30天内即将过期
        if (tip.length > 0 && value !== 1) {
          this.$alert(
            `商品“近效期至”或“远效期至”为空或30天内即将过期，不允许${
              value === 1 ? '上架' : '下架'
            }。请到商品列表顶部查询并及时修正`,
            '温馨提示',
            {
              confirmButtonText: '确定',
              callback: () => {}
            }
          )
        } else {
          if (value === 1) {
            const conStr = '您确定要上架此商品吗?'
            this.$confirm(conStr, '提示', {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning'
            }).then(async () => {
              try {
                const res = await batchUpAndDown(params)
                if (res.code === 0) {
                  this.$message.success({ message: '上架成功', offset: 100 })
                  this.handleFormSubmit()
                } else if (res.code === 2) {
                  this.$alert(res.message, '温馨提示', {
                    confirmButtonText: '确定',
                    callback: () => {}
                  })
                } else {
                  this.$message.error({ message: res.message, offset: 100 })
                }
              } catch (e) {
                console.log(e)
              }
            })
          } else {
            // 批量下架
            this.modifyConfig = {
              title: '提示',
              modifyType: 'shelves'
            }
            this.modifyConfig.ids = Ids
            this.modifyDialogVisible = true
          }
        }
      } else {
        const str = value === 1 ? '上架' : '下架'
        this.$message({
          type: 'warning',
          message: `请先勾选要${str}的商品`,
          offset: 100
        })
        // this.$alert('请先勾选要' + str + '的商品', {type: 'warning'})
      }
    },
    // 导出设置
    exportProductOptions(value) {
      if(value === 1) {
        this.exportProduct()
      }else if(value === 2) {
        this.exportPtData()
      }
    },
    // 批量设置
    modifyProduct(value) {
      if (value == 6) {
        const path = '/collageActivity'
        window.openTab(path, { showEdit: 1 })
        return;
      }
      if (value == 10) {
        this.productTitle = "批量修改批购包邮商品";
      } else if (value == 7) {
        this.productTitle = "批量上下线";
      } else if (value == 8) {
        this.productTitle = "批量设置区域价格";
      }else {
        this.productTitle = "批量导入";
      }
      this.batchModifyType = value
      this.batchModifyProductVisible = true
      // if (value === 1) {
      //   // 批量修改商品
      //   // const env = process.env.NODE_ENV;
      //   // if (env === 'development' || env === 'test') {
      //   //   this.excelTemplate = 'https://upload.test.ybm100.com/pop/temp/商品基本信息批量修改模板.xlsx';
      //   // }
      //   this.batchModifyProductVisible = true;
      // }
    },
    selectionCallback(selet) {
      this.selectList = selet
    },
    tabHandleClick() {
      this.formModel.status = Number(this.activeName)
      this.queryList({}, 2)
    },
    tableModifyBtn(num, row) {
      if (Number(num) === 1) {
        findAct({ barcode: row.barcode }).then((res) => {
          if (res.code === 0) {
            // if (res.data) {
            //   this.$alert('商品在参与特价活动及平台拼团活动期间，不允许修改商品价格', '温馨提示', {
            //     confirmButtonText: '确定',
            //     callback: (action) => {},
            //   });
            // } else {
            this.modifyConfig = {
              title: '设置价格',
              modifyType: 'suggestPrice',
              suggestPrice: row.fob ? row.fob : null,
              chainPrice: row.chainPrice ? row.chainPrice : null,
              priceType: this.shopConfig.priceType,
              grossProfitMargin: row.grossProfitMargin
                ? row.grossProfitMargin
                : null,
              chainGrossProfitMargin: row.chainGrossProfitMargin
                ? row.chainGrossProfitMargin
                : null,
              basePrice: row.basePrice ? row.basePrice : null,
              chainPriceSyncErp: row.chainPriceSyncErp ? row.chainPriceSyncErp : null,
              priceSyncErp: row.priceSyncErp ? row.priceSyncErp : null,
              skuAreaPriceVos: row.skuAreaPriceVos ? row.skuAreaPriceVos : [],
            }
            this.modifyConfig.barcode = row.barcode
            this.modifyDialogVisible = true
            // }
          } else {
            this.$message.error({ message: res.message, offset: 100 })
          }
        })
      } else if (Number(num) === 2) {
        this.modifyConfig = {
          title: '设置总库存',
          modifyType: 'totalStock'
        }
        this.modifyConfig.barcode = row.barcode
        this.modifyDialogVisible = true
      } else if (Number(num) === 3) {
        // this.$confirm('您确定要下架此商品吗?', '提示', {
        //     confirmButtonText: '确定',
        //     cancelButtonText: '取消',
        //     type: 'warning',
        //   }).then(async () => {
        //     const params = {
        //       skuIds: [row.id],
        //       status: 2,
        //     };
        //     const res = await batchUpAndDown(params);
        //     if (res.code === 0) {
        //       this.$message.success({ message: res.message, offset: 100 });
        //       this.handleFormSubmit();
        //     } else {
        //       this.$message.error({ message: res.message, offset: 100 });
        //     }
        //   }).catch(() => {});
        this.modifyConfig = {
          title: '提示',
          modifyType: 'shelves'
        }
        this.modifyConfig.ids = [row.id]
        this.modifyDialogVisible = true
      } else if (Number(num) === 4) { // 批够包邮修改价格
        this.modifyConfig = {
          title: '设置价格',
          modifyType: 'wholease',
          wholeaseActPrice: row.groupPrice ? row.groupPrice : null, //活动价
          wholeaseOriPrice: row.fob ? row.fob : null, //原价
          wholeaseStartCount: row.groupNum ? row.groupNum : null, // 起购数量
          mediumPackageNum: row.mediumPackageNum ? row.mediumPackageNum : 1, // 购买倍数
          limitQty: row.limitQty ? row.limitQty : null, // 个人限购数量
          totalLimitQty: row.totalLimitQtyStr ? row.totalLimitQtyStr : null, // 活动数量总上限
          actId: row.activityId ? row.activityId : null
        }
        this.modifyConfig.barcode = row.barcode
        this.modifyDialogVisible = true
      } else if (Number(num) === 6) {
        this.modifyConfig = {
          title: '修改活动库存',
          modifyType: 'wholeaseStock',
          actId: row.activityId ? row.activityId : null,
          limitQty: row.limitQty ? row.limitQty : null, // 个人限购数量
          limitType: row.limitType ? row.limitType : null, // 个人限购类型
          totalLimitQty: row.totalLimitQtyStr ? row.totalLimitQtyStr : null, // 活动数量总上限
        }
        this.modifyConfig.barcode = row.barcode
        this.modifyDialogVisible = true
      }
    },
    async viewAuditFailed(row) {
      // 查看审核不通过原因
      const h = this.$createElement
      this.$msgbox({
        title: '查看原因',
        message: h('div', null, [
          row.errorTips.map((i, index) =>
            h(
              'p',
              null,
              row.errorTips.length > 1 ? `${index + 1}、${i}` : `${i}`
            )
          )
        ]),
        confirmButtonText: '确定'
      })
    },
    viewSaleTime(row) {
      // 查看售卖时间
      const path = '/product/productTimeSetting'
      window.openTab(path, { barcode: row.barcode })
    },
    operationClick(type, row, str) {
		//frameReportId=12154&fromType=edit
      const { barcode, showName, erpCode } = row
      switch (Number(type)) {
        case 1: // 详情
          if (barcode) {
            if (row.activityType === 3) {
              const path = '/putProduct/single'
              const obj = {
                barcode: row.barcode,
                fromType: 'detail',
                actid: row.activityId,
                freeMail: "true"
              }
              window.openTab(path, obj)
            } else {
                // window.location.href = `/product/viewSku?barcode=${barcode}`;
                // const path = process.env.VUE_APP_BASE_API + '/product/viewSku'
                // window.openTab(path, {barcode: barcode}, '商品详情')
                const path = '/product/details'
                const obj = {
                  barcode: row.barcode,
                  isEdit: 1,
                  from: 'productList'
                }
                window.openTab(path, obj)
            }
          }
          break
        case 2: // 编辑

          if (barcode) {
            if (row.activityType === 3) {
              const path = '/putProduct/single'
              const obj = {
                barcode: row.barcode,
                fromType: 'edit',
                actid: row.activityId,
                freeMail: "true"
              }
              window.openTab(path, obj)
            } else if (row.activityType === 0 && str === "copyPg") {
				getSkuByBarcode({
					barcode: row.barcode
				}).then(res => {
					if (res.code == 1000) {
						window.sessionStorage.setItem('formData', JSON.stringify({
							stime: Date.now(),
							etime: Date.now() + 60 * 60 * 24 * 365 * 2 * 1000,
							...res.data.csu || {}
						}))
						window.openTab('/putProduct/single', {
							fromType: 'edit',
              freeMail: "true"
						});
					} else {
						this.$message.error(res.msg)
					}
				})
			} else if (row.activityType === 1) {
				const path = '/editCollageActivity';
				apiApplyList({
					actEndTime: "",
					actId : '',
					actStartTime : "",
					barcode : '',
					code : "",
					endCreateTime : "",
					idStr : 'PT' + row.activityId,
					isAgreement : "",
					isGiveSku : "",
					isOrder : "",
					isPlatformSubsidy : "",
					isSoldOut : "",
					isSyncStock : "",
					isUpdateAccessPrice : "",
					isVirtualShop : "",
					pageNum : 1,
					pageSize : 10,
					productName : "",
					reportId : "",
					reportTheme : "",
					skuId : "",
					startCreateTime : "",
					status : '',
					stepPriceStatus : "",
					warningPriceIsTooLow : "",
					warningRateStatus : 2,
					warningStartPersonalQty : ""
				}).then(res => {
					if (res.code == 1000 && res.data.data.list && res.data.data.list.length > 0) {
						const r = res.data.data.list[0];
						sessionStorage.setItem('editCollageItem', JSON.stringify(r));
						const path = '/editCollageActivity';
						const obj = {
							frameReportId: r.frameReportId,
							fromType: 'edit'
						}
						sessionStorage.setItem('collageActType', 'edit')
						window.openTab(path, obj)
					} else {
						this.$message.error("跳转失败")
					}
				})
			} else {
              console.log(this.formModel.businessFirstCategory)
              // window.location.href = `/product/toEditProduce?barcode=${barcode}`;
              // const path = process.env.VUE_APP_BASE_API + '/product/toEditProduce'
              // window.openTab(path, {barcode: barcode}, '商品编辑')
              const path = '/product/detailsEdit'
              const obj = {
                barcode: row.barcode,
                isEdit: 2,
                from: 'productList',
                verify: true,
                source: row.source
              }
              window.openTab(path, obj)
            }
          }
          break
        case 3: // 上架
          this.$confirm(
            !(row.disableType == 1 && row.disableType == 2 && row.disableType == 4) ?
            '您确定要上架此商品吗?': row.disableTypeName + ':' + row.disableNote + '；不允许上架！',
            '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          })
            .then(async () => {
              const params = {
                skuIds: [row.id],
                status: 1
              }
              const res = await batchUpAndDown(params)
              if (res.code === 0) {
                this.$message.success({ message: '上架成功', offset: 100 })
                this.handleFormSubmit()
              } else if (res.code === 2) {
                this.$alert(res.message, '温馨提示', {
                  confirmButtonText: '确定',
                  callback: (action) => {}
                })
              } else {
                // this.$message.error(res.message)
                this.$message.error({ message: res.message, offset: 100 })
                // this.$alert(res.message, {type: 'error'})
              }
            })
            .catch(() => {})
          break
        case 4: // 删除
          this.$confirm('您确定要删除此商品吗?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          })
            .then(async () => {
              const res = await skuDelete(barcode)
              if (res.code === 0) {
                // this.$message.success(res.message)
                this.$message.success({ message: res.message, offset: 100 })
                // this.$alert(res.message, {type: 'success'})
                this.handleFormSubmit()
              } else {
                // this.$message.error((res.message))
                this.$message.error({ message: res.message, offset: 100 })
                // this.$alert(res.message, {type: 'error'})
              }
            })
            .catch(() => {})
          break
        case 5: // 预览
          this.productPreviewBarcode = barcode
          this.productPreviewDialogVisible = true
          break
        case 6: // 查看操作日志
          this.skuLogDialogConfig.barcode = barcode
          this.skuLogDialogVisible = true
          break
        // case 7: // 下架
        //   this.$confirm('您确定要下架此商品吗?', '提示', {
        //     confirmButtonText: '确定',
        //     cancelButtonText: '取消',
        //     type: 'warning',
        //   }).then(async () => {
        //     const params = {
        //       skuIds: [row.id],
        //       status: 2,
        //     };
        //     const res = await batchUpAndDown(params);
        //     if (res.code === 0) {
        //       this.$message.success({ message: res.message, offset: 100 });
        //       this.handleFormSubmit();
        //     } else {
        //       this.$message.error({ message: res.message, offset: 100 });
        //     }
        //   }).catch(() => {});
        //   break;
        // 查看日志
        case 8:
          this.viewingInventoryLogsConfig.barcode = barcode
          ;(this.viewingInventoryLogsConfig.showName = showName),
            (this.viewingInventoryLogsConfig.erpCode = erpCode),
            (this.viewingInventoryLogsVisible = true)
          break
      }
    },
    openProductTimeSetting() {
      const path = '/product/productTimeSetting'
      window.openTab(path)
    },
    // 获取下载模板
    async getDownloadTemplate() {
      const that = this
      try {
        const params = {
          fileName: '商品基本信息批量修改模板.xlsx',
          priceType: that.shopConfig.priceType
        }
        const res = await apiDownloadTemplate(params)
        if (res.code === 0) {
          that.excelTemplate = res.data
        }
      } catch (e) {}
    },
    // 发布入仓商品
    releaseWarehousedGoods() {
      this.releaseWarehousedGoodDialogVisible = true
    },
    // 查看库存
    seeTotal(type, id) {
      this.showTotalModal = true
      this.actId = id
      this.actType = type
    },
    handleClose() {
      this.showTotalModal = false
      this.showRepeatTips = false
    },
    showAdditionalInfor() {
      apiCheckOffLine({ frameReportId: this.actOffline.id }).then(res => {
        if(res.code === 1000) {
          this.showInfor = res.data.checkResult
        }
      })
    },
    // 修改虚拟供应商
    updateVirtualShop(actId,type,isVirtualShop) {
      this.upVirtualShop.actId = actId,
      this.upVirtualShop.updateType = type,
      this.upVirtualShop.isVirtualShop = isVirtualShop
      this.upVirtualShop.dialogVisible = true
    },
    submitVirtualShop() {
      let params = {
        actId: this.upVirtualShop.actId,
        updateType: this.upVirtualShop.updateType,
        isVirtualShop: this.upVirtualShop.isVirtualShop
      }
      updateParams(params).then(res => {
        if (res.code === 1000) {
          this.$message.success('修改虚拟供应商成功');
          this.closeVirtualShop();
          this.queryList();
        } else {
        // this.$message.error(res.message)
           this.$message.error("waiting" || res.msg);
        }
      })
    },
    closeVirtualShop() {
      this.upVirtualShop.dialogVisible = false
    },
    showRelevanceActivity(row) {
      // 清掉其他的查询条件
      this.resetTipsParams()
      this.formModel = this.$refs.searchForm.getInitialFormModel()
      // 带上自己需要的
      this.formModel.originalBarcode = row.barcode
      this.formModel.activityTypes = [1,3]
      this.queryList(false,999)
    },
    addCroed(row) {
      this.innerSelected = row.customerGroupId
      this.crowdDialogVis = true
    },
    cancelModal() {
      this.crowdDialogVis = false
    },
  }
}
</script>

<style scoped lang="scss">
.topTip {
  padding: 5px 20px;
  background: #f3d9b2;
  opacity: 0.8;
  color: #ff2121;
}
.productList {
  //height: 100%;
  .redColor {
    color: #ff2121 !important;
  }
  .contentBox {
    //height: 100%;
    padding: 16px 16px;
    background: #fff;
    margin-bottom: 10px;

    .title {
      font-weight: 500;
      text-align: left;
      color: #000000;
      line-height: 14px;
      margin-bottom: 24px;
    }

    .title:before {
      content: '';
      display: inline-block;
      width: 3px;
      height: 13px;
      background: linear-gradient(360deg, #1d69c4 1%, #8bbdfc 99%);
      border-radius: 2px;
      margin-right: 8px;
      vertical-align: middle;
    }

    .searchForm {
      overflow: hidden;
    }

    .el-form-item {
      vertical-align: middle;
      margin-right: 16px;
      margin-bottom: 12px;
      border: 1px solid #dcdfe6;
      border-radius: 4px;

      ::v-deep   .el-form-item__label {
        font-size: 12px;
        width: 95px;
        height: 33px;
        line-height: 33px;
        border-right: 1px solid #dcdfe6;
      }

      ::v-deep   .el-form-item__content {
        width: 120px;

        .el-input__inner {
          border: none;
          font-size: 12px;
        }
      }
    }

    ::v-deep   .formItemTime .el-form-item__content {
      width: 354px;
    }

    .operation {
      margin-bottom: 12px;

    }
    .operation-filter-source {
      display: flex;
      align-items: center;
      margin-bottom: 10px;
      span {
        font-size: 14px;
      }
      .source-checkbox {
        margin-left: 10px;
      }
    }
    .tips {
      padding: 8px 16px;
      margin-bottom: 10px;
      .div-info:hover {
        border: 1px solid #4183d5;
        background-color: #fff;
        .status-span i {
          background: #4183d5 !important;
        }
      }
      .div-info {
        display: inline-block;
        padding: 5px 10px 10px 5px;
        border-radius: 2px;
        font-size: 14px;
        font-family: PingFangSC, PingFangSC-Regular;
        font-weight: 400;
        border: 1px solid #dcdfe6;
        margin: 0 10px 10px 0;
        p {
          margin: 0;
          padding-top: 5px;
          font-size: 14px;
          font-family: PingFangSC, PingFangSC-Medium;
          font-weight: 500;
          color: #333333;
        }
        .status-span {
          padding-left: 14px;
          position: relative;
          i {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            left: 4px;
            display: inline-block;
            vertical-align: middle;
            width: 5px;
            height: 5px;
            border-radius: 50%;
          }
        }
        .refundCountBox {
          .refundCount {
            margin-left: 12px;
            font-size: 20px;
            font-weight: 500;
            color: #ff3945;
            font-family: PingFangSC, PingFangSC-Medium;
          }
          .seeCount {
            float: right;
            color: #ffffff;
          }
        }
      }
    }
  }

  .table-containter {
    height: 100%;
  }
  ::v-deep   .table-containter {
    .el-button--text {
      color: #4183d5 !important;
    }
  }
  ::v-deep   .el-tabs__item.is-active {
    border: 1px solid #eeeeee;
    border-top-left-radius: 5px;
    border-top-right-radius: 5px;
    border-bottom: none;
  }

  ::v-deep   .el-tabs--top .el-tabs__item:last-child {
    padding-right: 20px ;
  }

  ::v-deep   .el-tabs--top .el-tabs__item.is-top:nth-child(2) {
    padding-left: 20px;
  }

  .exportProduct {
    vertical-align: middle;
    margin-right: 10px;

    ::v-deep   .el-input__inner {
      width: 80px;
      height: 32px;
      line-height: 32px;
      color: #ffffff;
      background-color: #4183d5;
      border-color: #4183d5;
    }

    ::v-deep   .el-input__icon {
      line-height: 32px;
    }
  }

  .modifyProduct {
    vertical-align: middle;
    margin-left: 10px;

    ::v-deep   .el-input__inner {
      width: 110px;
      height: 32px;
      line-height: 32px;
      color: #ffffff;
      background-color: #4183d5;
      border-color: #4183d5;
    }

    ::v-deep   .el-input__icon {
      line-height: 32px;
    }
  }

  ::v-deep   #tabled th > .cell {
    text-align: center;
    padding-left: 16px;
  }

  ::v-deep   #tabled tr td .cell {
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    white-space: break-spaces;
    font-size: 12px;
    font-family: PingFangSC, PingFangSC-Regular;
    font-weight: 400;
    text-align: left !important;
    color: #666666;
    line-height: 22px;
    padding-left: 16px;

    .busAreaNameBox {
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .productInfo {
      text-align: left;
      font-size: 12px;
      font-family: PingFangSC, PingFangSC-Regular;
      font-weight: 400;
      color: #999999;
      line-height: 18px;

      .productName {
        font-size: 14px;
        font-family: PingFangSC, PingFangSC-Medium;
        font-weight: 500;
        color: rgba(51, 51, 51, 0.85);
        line-height: 22px;
        .name {
          display: flex;
          align-items: center;
          .icon {
            font-size: 12px;
            color: #4183d5;
            border: 1px solid #4183d5;
            padding: 0 2px;
            margin-left: 5px;
            border-radius: 2px;
          }
        }
      }

      .manufacturer {
        color: rgba(51, 51, 51, 0.85);
      }
    }

    .el-button {
      height: 22px;
      padding: 0;
      margin: 0;
      text-align: left;
      font-size: 12px;
      line-height: 22px;
    }

    .el-button:before {
      display: none;
    }
  }

  ::v-deep   .el-dialog__title {
    font-size: 16px;
  }

}
::v-deep  .el-input-group__prepend {
  background: none;
  color: #333333;
  padding: 0 10px;
}
::v-deep   .cell {
  .el-checkbox__inner {
    border: 1px solid #000000;
  }
}

.high-gross-tag{
  width: 50px;
  background: #4183d5;
  color: #fff;
  padding: 0 5px;
  border-radius: 2px;
  font-size: 12px;
  font-family: PingFangSC, PingFangSC-Medium;
  font-weight: 500;
  line-height: 22px;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
}
.lookMore{
  color: #1d69c4;
  cursor: pointer;
}
</style>
<style lang="scss">
 .productListTab{
  .el-badge__content{
    height:auto !important;
  }
  .el-badge__content.is-fixed {
    top: -6px;
    right: 23px;
    background-color: rgb(255,152,0);
    padding: 0 9px;
    border-radius: 10px 10px 10px 0  !important;
  }

 }

.operation-filter-source {
  .source-checkbox {
    .el-checkbox {
      margin-right: 25px;
      .el-checkbox__label {
        padding-left: 5px;
      }
    }
  }
}
 .salesData{
     position: absolute;
    right: 7px;
    top: 0px;
    width: 30px;
    background-color: #f9f9f9 !important;
  }
  .item-tips{
        margin-top: -40px;

  }

 .tips-sort{
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
    .flex-text{
      text-align: center;
    /* width: 35px;
    height: 35px; */
    width: 40px;
    height: 40px;
    color: #fefefe;
    /* line-height: 25px; */
    line-height: 30px;
    z-index: 1;
    font-weight: 400;
    font-size: 8px;
    background: url(./img/tip.png);
    background-size: cover;
    /* margin-top: -40px; */
    margin-top: -50px;
    margin-left: -10px;

    }
 }
 .tooltipEllipsis {
    overflow: hidden;
    width: 140px;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-box-pack: center;
    -webkit-box-align: center;
    -webkit-line-clamp: 2; //折两行后显示'...'
  }
</style>
