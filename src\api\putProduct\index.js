import request from '../index'
export function getSkuByBarcode(params) {
	return request.get('/report/wholesale/apply/getSkuByBarcode', params);
}
/**
 * 
 * @param { any } params 
 * @param { "add" | "edit" } type 
 * @returns 
 */
export function batchImportAct(params, type) {
	if (type == 'add') {
		return request.postFormData('/report/wholesale/apply/batchImportAct', params)
	} else if (type == 'edit') {
		return request.post('/report/wholesale/apply/batchUpdateActe', params)
	}
}
/**
 * 
 * @param { any } params 
 * @param { "add" | "edit" } type 
 * @returns { Promise<any> }
 */
export function singleAdd(params, type) {
	if (type == 'add') {
		return request.post('/report/wholesale/apply/add', params)
	} else if (type == 'edit') {
		return request.post('/report/wholesale/apply/update', params)
	}
}
export function download() {
	return request.get('/report/wholesale/apply/getTemplateForBatchImport',null, {
		responseType: 'blob'
	})
}