import request from '@/utils/request';

//
export function getSpecialList(params, page) {
  return request({
    url: `/promo/promotion/promotionList?pageNum=${page.pageNum}&pageSize=${page.pageSize}`,
    method: 'post',
    data: params,
  });
}

export function exportActiveList(params) {
  return request({
    url: '/promo/promotion/exportPromotionData',
    method: 'get',
    params,
  });
}

export function getProductGoodsList(params) {
  return request({
    url: '/promo/promotion/productList',
    method: 'get',
    params,
  });
}
export function getProductPeopleList(params) {
  return request({
    url: '/insight/list',
    method: 'get',
    params,
  });
}

export function offlineGoods(params) {
  return request({
    url: '/promo/promotion/offline',
    method: 'get',
    params,
  });
}

export function operateGoods(params) {
  return request({
    url: '/promo/promotion/doAddOrEdit',
    method: 'post',
    data: params,
  });
}

export function getGoodsDetail(params) {
  return request({
    url: '/promo/promotion/promotionDetail',
    method: 'get',
    params,
  });
}

export function importProduct(params) {
  const forms = new FormData();
  forms.append('excelFile', params.excelFile);
  return request.post('/promo/promotion/importProduct', forms, { headers: { 'Content-Type': 'multipart/form-data' } });
}

export function batchImportPromotion(params) {
  const forms = new FormData();
  forms.append('multipartFile', params.multipartFile);
  return request.post('/promo/promotion/batchImportPromotion', forms, { headers: { 'Content-Type': 'multipart/form-data' } });
}

export function getInfoList(params) {
  return request({
    url: '/promo/promotion/order/list',
    method: 'post',
    data: params,
  });
}

export function exportStatisticInfo(params) {
  return request.post('/promo/promotion/order/export', params, { responseType: 'blob' });
}

export function getStatusCountInfo(params) {
  return request({
    url: '/promo/promotion/getStatusCountInfo',
    method: 'post',
    data: params,
  });
}

export function exportPromotionData(params) {
  return request({
    url: '/promo/promotion/exportPromotionData',
    method: 'get',
    params,
  });
}

export function exportSkuDetailData(params) {
  return request({
    url: '/promo/promotion/exportSkuDetailData',
    method: 'get',
    params,
  });
}
