export const searchItem = {
  formItems : [
    {
      label: '编码信息',
      prop: 'productCode',
      component: 'el-input',
      attrs: {
        placeholder: '请输入'
      }
    },
    {
      label: '商品名称',
      prop: 'showName',
      component: 'el-input',
      attrs: {
        placeholder: '请输入'
      }
    },
    {
      label: '生产厂家',
      prop: 'manufacturer',
      component: 'el-input',
      attrs: {
        placeholder: '请输入'
      }
    },
    {
      label: '商品ERP编码',
      prop: 'erpCode',
      component: 'el-input',
      attrs: {
        placeholder: '请输入'
      }
    },
    {
      label: '商品条码',
      prop: 'code',
      component: 'el-input',
      attrs: {
        placeholder: '请输入'
      }
    },
    {
      label: '批准文号',
      prop: 'approvalNumber',
      component: 'el-input',
      attrs: {
        placeholder: '请输入'
      }
    },
    {
      label: '商品状态',
      prop: 'status',
      component: 'el-select',
      slotName: 'status'
    },
    {
      label: '库存ERP同步',
      prop: 'stockSyncErp',
      component: 'el-select',
      attrs: {
        options: [
          {
            label: '全部',
            value: ''
          },
          {
            label: '是',
            value: 1
          },
          {
            label: '否',
            value: 0
          },
        ]
      }
    },
    {
      label: '商品分类',
      prop: 'categoryId',
      component: 'el-cascader',
      slotName: 'category'
    },
    {
      label: '商品特性',
      prop: 'characteristic',
      component: 'el-select',
      attrs: {
        options: [
          {
            label: '全部',
            value: 0
          },
          {
            label: '特长药',
            value: 1
          },
          {
            label: '专供',
            value: 2
          },
          {
            label: '独家代理',
            value: 3
          },
          {
            label: '特许专供',
            value: 4
          }
        ]
      }
    },
    {
      label: '商品类型',
      prop: 'saleType',
      component: 'el-select',
      attrs: {
        options: [
          {
            label: '全部',
            value: ''
          },
          {
            label: '药品',
            value: 1
          },
          {
            label: '非药品',
            value: 2
          }
        ]
      }
    },
    {
      label: '价格ERP同步',
      prop: 'priceSyncErp',
      component: 'el-select',
      attrs: {
        options: [
          {
            label: '全部',
            value: ''
          },
          {
            label: '是',
            value: 1
          },
          {
            label: '否',
            value: 0
          }
        ]
      }
    },
    {
      label: '商圈名称',
      prop: 'busName',
      component: 'el-input',
      attrs: {
        placeholder: '请输入'
      }
    },
    {
      label: '是否有主图',
      prop: 'havePic',
      component: 'el-select',
      attrs: {
        options: [
          {
            label: '全部',
            value: ''
          },
          {
            label: '有图',
            value: 1
          },
          {
            label: '无图',
            value: 2
          }
        ]
      }
    },
    {
      label: '库存状态',
      prop: 'stockStatus',
      component: 'el-select',
      attrs: {
        options: [
          {
            label: '全部',
            value: ''
          },
          {
            label: '无库存',
            value: '0'
          },
          {
            label: '有库存',
            value: '1'
          }
        ]
      }
    },
    // {
    //   label: 'sku编码',
    //   prop: 'csuid',
    //   component: 'el-input',
    //   attrs: { placeholder: '请输入完整的sku编码' },
    // },
    {
      label: '创建时间',
      prop: 'createTime',
      component: 'el-date-picker',
      // colSpan: 12,
      attrs: {
        type: 'daterange',
        startPlaceholder: '开始日期',
        endPlaceholder: '结束日期',
        rangeSeparator: '至',
        valueFormat: 'yyyy-MM-dd',
      },
    },
    // pop 012 自营1234
    {
      label: '商品来源',
      prop: 'activityType',
      component: 'el-select',
      attrs: {
        options: [
          {
            label: '全部',
            value: '',
          },
          {
            label: '普通商品',
            value: 0,
          },
          {
            label: '拼团商品',
            value: 1,
          },
          {
            label: '赠品',
            value: 2,
          },
          {
            label: '批购包邮',
            value: 3,
          },
        ],
      },
    },
    // {
    //   label: '活动ID',
    //   prop: 'activityId',
    //   component: 'el-input',
    //   attrs: { placeholder: '请输入' },
    // },
    {
      label: '原商品编码',
      prop: 'originalBarcode',
      component: 'el-input',
      colSpan: 6,
      attrs: {
        placeholder: '请输入完整原商品编码'
      }
    },
    // tracingCode
    {
      label: '是否有追溯码',
      prop: 'tracingCode',
      component: 'el-select',
      slotName: 'tracingCode',
      isHidden: false,
      // attrs: {
      //   options: [
      //     {
      //       label: '全部',
      //       value: null
      //     },
      //     {
      //       label: '是',
      //       value: 1
      //     },
      //     {
      //       label: '否',
      //       value: 0
      //     },
      //     {
      //       label: '未录入',
      //       value: 2
      //     }
      //   ]
      // }
    },
    {
      label: '商品规格',
      prop: 'spec',
      component: 'el-input',
      colSpan: 6,
      attrs: {
        placeholder: '请输入'
      }
    },
    {
      label: '虚拟供应商',
      prop: 'isVirtualShop',
      component: 'el-select',
      attrs: {
        options: [
          {
            label: '全部',
            value: '',
          },
          {
            label: '是',
            value: 1,
          },
          {
            label: '否',
            value: 2,
          },
        ],
      },
    },
    {
      label: '是否已关联标品',
      prop: 'tiedMeProduct',
      component: 'el-select',
      colSpan: 6,
      attrs: {
        placeholder: '请选择',
        options:[
          {value:"", label:"全部"},
          {value:"1", label:"是"},
          {value:"0", label:"否"}
        ]
      }
    },
    { // a-> 是否成单
      label: '是否成单',
      prop: 'isOrderDone',
      component: 'el-select',
      colSpan: 6,
      attrs: {
        placeholder: '请选择',
        options:[
          {value:"", label:"全部"},
          {value:"1", label:"是"},
          {value:"2", label:"否"}
        ]
      }
    },
    {  //库存数量
      label: '库存数量',
      prop: 'InventoryQuantity',
      colSpan: 6,
      slotName: 'InventoryQuantity'
    },
    { // b-> 平台竞价商品
      label: '平台竞价商品',
      prop: 'isBiddingProduct',
      component: 'el-select',
      colSpan: 6,
      attrs: {
        placeholder: '请选择',
        options:[
          {value:"", label:"全部"},
          {value:"1", label:"是"},
          {value:"2", label:"否"}
        ]
      }
    },
    {
      label: "是否关联活动商品",
      prop: "biddingActivitySkuType",
      component: 'el-select',
      colSpan: 6,
      attrs: {
        placeholder: "请选择",
        options: [
          {value: "1",label: "全部"},
          {value: "2",label: "拼团商品"},
          {value: "3",label: "批购包邮商品"},
          {value: "4",label: "未关联"}
        ]
      }
    },
    {
      label: '是否设置区域价',
      prop: 'haveAreaPrice',
      component: 'el-select',
      colSpan: 6,
      attrs: {
        placeholder: '请选择',
        options:[
          {value:"", label:"全部"},
          {value:"1", label:"是"},
          {value:"0", label:"否"}
        ]
      }
    },
  ],
  formItemsIn: [
    {
      label: '条码',
      prop: 'code',
      component: 'el-input',
      attrs: {
        placeholder: '请输入完整的条码',
        onkeyup: "value=value.replace(/[^\\d]/g,'')",
      },
    },
    {
      label: '商品搜索',
      prop: 'productName',
      component: 'el-input',
      attrs: {
        placeholder: '请输入商品名称/助记码'
      }
    },
    {
      label: '生产厂家',
      prop: 'manufacturer',
      component: 'el-input',
      attrs: {
        placeholder: '请输入'
      }
    },
    {
      label: '批准文号',
      prop: 'approvalNumber',
      component: 'el-input',
      attrs: {
        placeholder: '请输入完整的批准文号'
      }
    },
    {
      label: '规格',
      prop: 'spec',
      component: 'el-input',
    },
    {
      label: '品牌',
      prop: 'brand',
      component: 'el-input',
    },
    {
      label: '产地',
      prop: 'originPlace',
      component: 'el-input',
    },
    {
      label: '商品大类',
      prop: 'spuCategory',
      component: 'el-select',
      attrs: {
        options: [
          {
            label: '全部',
            value: ''
          },
          {
            label: '普通药品',
            value: 1
          },
          {
            label: '中药',
            value: 2
          },
          {
            label: '医疗器械',
            value: 3
          },
          {
            label: '非药',
            value: 4
          }
        ]
      }
    }
  ],
  formItemsChange : [
    {
      label: '商品名称',
      prop: 'showName',
      component: 'el-input',
      attrs: {
        placeholder: '请输入'
      }
    },
    {
      label: '商品ERP编码',
      prop: 'erpCode',
      component: 'el-input',
      attrs: {
        placeholder: '请输入'
      }
    },
    {
      label: '生产厂家',
      prop: 'manufacturer',
      component: 'el-input',
      attrs: {
        placeholder: '请输入'
      }
    },
     {
      label: '批准文号',
      prop: 'approvalNumber',
      component: 'el-input',
      attrs: {
        placeholder: '请输入'
      }
    },
    {
      label: '商品状态',
      prop: 'status',
      component: 'el-select',
      slotName: 'status'
    },
    {
      label: '商品分类',
      prop: 'categoryId',
      component: 'el-cascader',
      slotName: 'category'
    },
    {
      label: '商品类型',
      prop: 'saleType',
      component: 'el-select',
      attrs: {
        options: [
          {
            label: '全部',
            value: ''
          },
          {
            label: '药品',
            value: 1
          },
          {
            label: '非药品',
            value: 2
          }
        ]
      }
    },
    {
      label: '商品规格',
      prop: 'spec',
      component: 'el-input',
      colSpan: 6,
      attrs: {
        placeholder: '请输入'
      }
    }
  ],
}
export const erpFirstCategoryId = {
  '1' : '100002',
  '2' : '100004',
  '3' : '100005',
  '4100001' : '100001',
  '4100007' : '100007',
  '4100008' : '100008',
  '4100003' : '100003',
  '5' : '100006',
}

const keyMapper = {
  '1' : ['brand', 'spec', 'manufacturer', 'entrustedManufacturer', 'approvalNumber', 'productUnit', 'code', 'businessScopeMultiName'],
  '2' : ['brand', 'spec', 'manufacturer', 'originPlace', 'qualityStandard', 'productUnit', 'code', 'businessScopeMultiName'],
  '3' : ['brand', 'spec', 'manufacturer', 'entrustedManufacturer', 'approvalNumber', 'productUnit', 'code', 'businessScopeMultiName'],
  '4' : ['brand', 'spec', 'manufacturer', 'entrustedManufacturer', 'approvalNumber', 'productUnit', 'code', 'businessScopeMultiName'],
  '5' : ['brand', 'spec', 'manufacturer', 'productUnit', 'code', 'businessScopeMultiName'],
}
//去重
export const Deduplication = (list) => {
  const obj = {};
  const temp = {};
  const result = [];
  list.forEach((item, index) => {
    if (!obj[item.showName]) {
      obj[item.showName] = [];
      obj[item.showName].push(index);
      if (item.firstCategory == 100009) {
        temp[item.showName] = {};
        temp[item.showName][item.productUnit] = item;
      } else {
        result.push(item);
      }
    } else {
      const target = obj[item.showName].some(i => {
        return keyMapper[item.spuCategory].every(k => item[k] === list[i][k])
      })
      if (!target) {
        obj[item.showName].push(index);
        result.push(item);
      } else if (item.firstCategory == 100009) {
        //productUnit\
        /* const key = `${item.showName}${item.productUnit}`; */
        if (!temp[item.showName]) {
          temp[item.showName] = {}
        }
        temp[item.showName][item.productUnit] = item
      }
    }
  })
  console.log(temp);
  for (const key in temp) {
    const len = Object.keys(temp[key]);
    if (len.length === 1) {
      result.push(temp[key][len[0]])
    } else {
      len.forEach(val => {
        if (val!= '支') {
          result.push(temp[key][val])
        }
      })
    }
  }
  return result;
}

//不同项标红
export const diffRed = (list) => {
  const obj = {};
  return list.map((item, index) => {
    item.needRedKey = [];
    if (!obj[item.showName]) {
      obj[item.showName] = [];
      obj[item.showName].push(index);
    } else {
      obj[item.showName].forEach(i => {
        return keyMapper[item.spuCategory].forEach(k => {
          if (item[k] !== list[i][k]) {
            item.needRedKey.push(k)
          }
        })
      })
      item.needRedKey = [...new Set(item.needRedKey)];
    }
    return item;
  })
}
