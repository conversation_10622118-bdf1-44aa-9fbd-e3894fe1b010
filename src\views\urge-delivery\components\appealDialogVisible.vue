<template>
  <div>
    <el-dialog 
   title="申诉"
   :visible="appealDialogVisible"  
   @close="closeDialog">
    <el-form 
    :model="appealForm"
    ref="appealForm"
    :rules="appealRules"
    label-width="100px"
    class="demo-ruleForm">
        <el-form-item label="申诉类型" prop="appealType">
            <el-select v-model="appealForm.appealType" style="width: 60%">
              <el-option
                v-for="item in appealForm.appealOption"
                :key="item.key" 
                :label="item.value" 
                :value="item.key">
              </el-option>
            </el-select>
        </el-form-item>
        <el-form-item label="申诉说明" prop="appealStatement">
            <el-input 
            style="width: 60%"
            type="textarea" 
            v-model="appealForm.appealStatement" 
            placeholder="300字以内"></el-input>
        </el-form-item>
        <el-form-item label="申诉图片" required>
            <div style="width:400px;">
              <i-file
                :multiple="true"
                :bucket="3"
                :maxCount="9"
                style="margin-bottom: 10px"
                v-model="uploadedImgs">
              </i-file>
              <i-img 
                v-model="uploadedImgs" 
                :deleteBtn="true"
                :maxShowLength="9"
                :showNullEl="false"></i-img>
            </div>
        </el-form-item>
    </el-form>
    <div slot="footer">
          <el-button @click="closeDialog">取消</el-button>
          <el-button 
            type="primary" 
            @click="submitAppeal" 
            :loading="btnLoading">
          确认</el-button>
        </div>
   </el-dialog> 
  </div>
</template>

<script>
import iFile from "./iFile.vue"
import iImg from "./i-img.vue"
import {
  submitAppeal,
  getHostName,
  getAppealType
} from "@/api/urge-delivery"
export default {
    props: {
     appealDialogVisible: {
        type: Boolean,
        default: false
     },
     reminder: {
      default: ""
     },
     index: {
      default: -1
     }
    },
    mounted() {
      getHostName().then((res) => {
        if (res.hostName) {
          this.hostName = res.hostName;
        }
      });
      getAppealType().then(res => {
        if(res.code === 0) {
          this.appealForm.appealOption = res.result || []
        }
      })
    },
    components: {
      iFile,
      iImg
    },
    watch:{
      
    },
    data() {
        return {
         appealForm: { // 申诉的弹窗表单项
            appealType: '', // 申诉类型
            appealOption: [], // 申诉选项
            appealStatement: '', // 申诉说明 
            uploadedFileList: [], // 用于存储已上传的文件信息  
          },
          appealRules: {
            appealType: [
                { required: true, message: '请选择申诉类型', trigger: 'change' }
            ],
            appealStatement: [
                { required: true, message: '300字以内', trigger: 'blur' },
                { required: true, max: 300,message: '300字以内', trigger: 'blur' }
            ]
          },
          btnLoading: false,
          hostName: "", //用户名
          uploadedImgs: [],
        }
    },
    methods: {
        closeDialog() {
            this.$emit('cancelDialog');
        },
        submitAppeal() {
          if(this.uploadedImgs.length === 0) {
            this.$message.warning("请至少上传一张申述图片")
            return
          }
          this.$refs.appealForm.validate(valid => {
            if(valid) {
              this.btnLoading = true
              let appealEvidence = []
              this.uploadedImgs.forEach(item => {
                appealEvidence.push(item.url)
              })
              let params = {
                reminderId: this.reminder.id,
                orderNo: this.reminder.orderNo,
                appealCategory: this.appealForm.appealType,
                appealDescription: this.appealForm.appealStatement,
                sourceStatus: this.reminder.status,
                appealEvidence
              }
              submitAppeal(params).then(res => {
                if(res.code === 0) {
                  this.$message.success("申诉成功")
                  this.$emit("freshPage",this.index)
                }else {
                  this.$message.error(res.msg || res.errMsg || "服务异常")
                }
              }).finally(() => {
                this.uploadedImgs = []
                this.closeDialog()
              })    
            }else {
              this.$message.warning("请按提示填写")
              return false
            }
          })
        }
    }
}
</script>

<style lang="scss" scoped>
.hide {
    ::v-deep   .el-upload--picture-card {
        display: none !important;
    }
}
::v-deep   .el-dialog {
  border-radius: 5px;
  overflow: hidden;
}
::v-deep   .el-dialog__header {
  padding: 10px 15px;
  background-color: #f3f3f3;
}
::v-deep   .el-dialog__header span {
  font-size: 14px;
  line-height: normal;
}
::v-deep   .el-dialog__header button {
  right: 15px;
  top: 15px;
}
::v-deep   .el-dialog__body {
  max-height: 500px;
  overflow: auto;
  padding: 5px 15px;
  border-bottom: solid 1px #e4eaf1;
}
::v-deep   .el-dialog__footer {
  padding: 15px;
}
</style>