<template>
  <el-dialog
    title="商品审核日志"
    :visible="dialogVisible"
    width="60%"
    height="300"
    :before-close="handleClose"
  >
    <el-table
      v-loading="loading"
      :data="tableConfig.data"
      border
      height="400"
    >
      <el-table-column label="变更类型">
        <template slot-scope="{row}">
          <div>
            {{ row.typeStr }}
          </div>
        </template>
      </el-table-column>
      <el-table-column
        prop="ctimeStr"
        label="变更时间"
      />
      <el-table-column
        prop="statusStr"
        label="审核状态"
      />
      <el-table-column
        prop="remark"
        label="备注"
      />
      <el-table-column
        prop="operator"
        label="操作人"
      />
    </el-table>
  </el-dialog>
</template>

<script>
import { getSkuLogV2 } from '@/api/support';

export default {
  name: 'VieSkuLog',
  props: {
    skuConfig: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      dialogVisible: true,
      loading: true,
      tableConfig: {
        data: [],
        total: 0,
      },
    };
  },
  mounted() {
    this.getSkulog();
  },
  methods: {
    async getSkulog() {
      try {
        const res = await getSkuLogV2({id:Number(this.skuConfig.id)});
        console.log('res',res);
        if (res.code === 0) {
          this.tableConfig.data = res.data || [];
        } else {
          this.$alert(res.msg, { type: 'error' });
        }
      } catch (e) {
        console.log(e);
      }
      this.loading = false;
    },
    handleClose() {
      this.$emit('update:skuLogDialogVisible', false);
    },
  },
};
</script>

<style scoped lang="scss">
::v-deep   .el-table thead th {
  background: #f9f9f9;
  border: none;

  .cell {
    font-family: PingFangSC, PingFangSC-Regular;
    font-weight: 400;
    text-align: left;
    color: rgba(51, 51, 51, 0.85);
    line-height: 22px;
  }
}

::v-deep   .el-table__body-wrapper {
  font-size: 12px;
  color: #666666;
}
</style>
