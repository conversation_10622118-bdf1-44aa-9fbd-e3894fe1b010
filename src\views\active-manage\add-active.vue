<template>
  <div>
    <div class="serch">
      <div class="Fsearch">
        <el-row
          type="flex"
          align="middle"
          justify="space-between"
          class="my-row"
        >
          <el-row
            type="flex"
            align="middle"
          >
            <span class="sign" />
            <div class="searchMsg">
              设置活动
            </div>
          </el-row>
          <el-button
            type="primary"
            size="small"
            @click="resetForm()"
          >
            返回
          </el-button>
        </el-row>
      </div>
      <div class="fromBlock">
        <el-form
          ref="ruleForm"
          :model="ruleForm"
          :rules="rules"
          size="small"
          label-width="100px"
        >
          <el-form-item label="活动名称" prop="name">
            <el-input v-model="ruleForm.name" maxlength="30" placeholder="最多输入30个字" style="width: 50%" />
          </el-form-item>
          <el-form-item label="领券时间" prop="creatTime">
            <el-date-picker
              v-model.trim="ruleForm.creatTime"
              type="datetimerange"
              format="yyyy-MM-dd HH:mm:ss"
              range-separator="至"
              value-format="timestamp"
              :default-time="['00:00:00', '23:59:59']"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            />
          </el-form-item>

          <el-form-item
            v-if="shopConfig.showTag"
            label="领券中心是否展示"
          >
            <el-radio-group v-model="ruleForm.display">
              <el-radio :label="1">是</el-radio>
              <el-radio :label="0">否</el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item label="人群" prop="participatingCrowd">
            <div>
              <div class="mt16 ml150">
                <el-radio
                  v-model="ruleForm.participatingCrowd"
                  :label="-1"
                  class="radio-class"
                >
                  <span></span>
                </el-radio>
                <span class="radio-span">全部人群参与</span>
              </div>
              <div class="mt16">
                <el-radio
                  v-model="ruleForm.participatingCrowd"
                  :label="1"
                  class="radio-class"
                >
                  <span></span>
                </el-radio>
                <span class="radio-span">指定人群参与</span>
                <el-button
                  type="primary"
                  plain
                  size="small"
                  v-show="ruleForm.participatingCrowd == 1"
                  @click="addPeopel"
                >选择人群
                </el-button
                >
                <span
                  v-if="ruleForm.participatingCrowd == 1 && ruleForm.pepelName"
                  style="margin-left: 10px"
                >
                  已选人群：{{ ruleForm.pepelName }}
                </span>
                <!-- </div> -->
              </div>
            </div>
          </el-form-item>
          <el-form-item label="领券说明" prop="remark">
            <el-input v-model="ruleForm.remark" maxlength="200" placeholder="最多输入200个字，该字段不会展示给客户" style="width: 50%" />
          </el-form-item>
          <el-form-item label="优惠券" required>
            <el-button
              class="addcoupon"
              type="primary"
              plain
              style="width: 92px; height: 32px"
              size="small"
              @click="addCouBtn"
            >
              {{ ruleForm.coupon ? '编辑优惠券' : '添加优惠券' }}
            </el-button>
            <!--            <el-input-->
            <!--              v-if="ruleForm.coupon"-->
            <!--              v-model="ruleForm.coupon"-->
            <!--              class="couponName"-->
            <!--            />-->
            <el-button
              class="addcoupon"
              type="primary"
              plain
              style="width: 92px; height: 32px"
              size="small"
              @click="delCouBtn"
            >
              删除优惠券
            </el-button>
            <div style="color:red;">{{ `提示：满足${shopConfig.configInfoDTO.tagMin}≤使用门槛≤${shopConfig.configInfoDTO.tagMax}元，且发行数量≥${shopConfig.configInfoDTO.tagPopLimit}的优惠券，才能生成活动标签、计入折后价` }}</div>
          </el-form-item>
          <el-form-item label="">
            <el-table
              ref="table"
              :data="tableConfig.tableData"
              :row-key="getRowkey"
              @selection-change="handleSelectionChange"
              style="width: 100%"
            >
              <el-table-column
                type="selection"
                :reserve-selection="true"
                width="55"
              />
              <el-table-column
                prop="id"
                label="券ID"
              />
              <el-table-column
                prop="name"
                label="券名称"
              />
<!--              <el-table-column-->
<!--                prop="scopeTypeName"-->
<!--                label="券类型"-->
<!--              />-->
              <el-table-column
                prop="typeName"
                label="券优惠方式"
                width="180"
              />
              <el-table-column
                prop="moneyInVoucher"
                label="面额"
              >
                <template slot-scope="{row}">
                  <span v-if="row.type===2">{{ `${row.discount}折` }}</span>
                  <span v-else>{{row.moneyInVoucher}}元</span>
                </template>
              </el-table-column>
              <el-table-column
                prop="minMoneyToEnable"
                label="使用条件（满N元）"
                width="180"
              >
                <template slot-scope="{row}">
                  <span>{{ `满${row.minMoneyToEnable}元` }}</span>
                </template>
              </el-table-column>
              <el-table-column
                prop="description"
                label="使用说明"
                width="180"
              >
                <template slot-scope="{row}">
                  <div v-if="Number(row.type)===2">
                    满{{ row.minMoneyToEnable }}元，打{{ row.discount }}折
                    <span v-if="row.maxMoneyInVoucher">，最高减{{ row.maxMoneyInVoucher }}元</span>
                  </div>
                  <div v-else>
                <span v-if="Number(row.reduceType)===1"
                >每满{{ row.minMoneyToEnable }}元，减{{ row.moneyInVoucher }}元，最高减{{ row.discount }}元</span>
                    <span v-else>满{{ row.minMoneyToEnable }}元减{{ row.moneyInVoucher }}元</span>
                  </div>
                </template>
              </el-table-column>
              <el-table-column
                prop="description"
                width="180"
                label="适用商品"
              />
              <el-table-column
                prop="updateTime"
                width="180"
                label="有效期"
              >
                <template slot-scope="scope">
                  <div v-if="scope.row.validityType === 2">
                    <span>起：{{ scope.row.startTime | handleTime }}</span><br>
                    <span>止：{{ scope.row.endTime | handleTime }}</span>
                  </div>
                  <div v-else>
                    {{ scope.row.validityDays }}天
                  </div>
                </template>
              </el-table-column>
              <el-table-column
                label="限领数量（张）"
                width="240"
              >
                <template slot-scope="scope">
                  <el-radio-group v-model="scope.row.sendType" @change="radioChange(scope.$index)">
                    <el-radio label="3">不限制</el-radio>
                    <el-radio label="1">活动期间限领
                      <el-input v-model="scope.row.count1" style="width: 70px"
                                :disabled="Number(scope.row.sendType)!==1"
                                onkeyup="this.value=this.value.replace(/[^\d]/g,'')"
                      ></el-input>
                      张
                    </el-radio>
                    <el-radio label="2">每人每天限领
                      <el-input v-model="scope.row.count2" style="width: 70px"
                                :disabled="Number(scope.row.sendType)!==2"
                                onkeyup="this.value=this.value.replace(/[^\d]/g,'')"
                      ></el-input>
                      张
                    </el-radio>
                  </el-radio-group>
                </template>
              </el-table-column>
            </el-table>
          </el-form-item>
          <el-form-item class="searchBtn">
            <el-button type="primary" @click="submitForm('ruleForm')">
              提交
            </el-button>
            <el-button @click="resetForm('ruleForm')"> 取消</el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>
    <addcoupon
      :template-id="templateId"
      :dialog-visible="addCoupon"
      @querySel="querySel"
      @cancelSel="cancelSel"
    />
    <crowd-selector-dialog
      v-if="dialogVisible"
      v-model="dialogVisible"
      :selected="ruleForm.prMarketCustomerGroupId"
      @onSelect="onSelect"
    />
  </div>
</template>

<script>
import { mapState } from 'vuex';
import { getCouponDetail, couponAcSave } from '@/api/activity/index';
import addcoupon from '@/components/addCoupon.vue';
import CrowdSelectorDialog from '../../components/xyy/customerOperatoin/crowd-selector-dialog.vue';

export default {
  name: 'AddActive',
  filters: {
    handleTime(time) {
      return window.dayjs(time).format('YYYY-MM-DD HH:mm:ss');
    },
  },
  components: {
    addcoupon,
    CrowdSelectorDialog,
  },
  data() {
    return {
      isLoading: false,
      actId: null,
      isCreate: false,
      addCoupon: false,
      dialogVisible: false,
      ruleForm: {
        participatingCrowd: -1,
        name: '',
        coupon: '',
        prMarketCustomerGroupId: undefined,
        remark: '',
        pepelName: '',
        creatTime: [],
        display: 1,
      },
      selItem: [],
      rules: Object.freeze({
        prMarketCustomerGroupId: [
          {
            required: true,
            message: '请选择人群',
            trigger: 'blur',
          },
        ],
        name: [
          {
            required: true,
            message: '请输入活动名称',
            trigger: 'blur',
          },
        ],
        // coupon: [
        //   {
        //     required: true,
        //     message: '请选择优惠券',
        //     trigger: 'change'
        //   }
        // ],
        creatTime: [
          {
            required: true,
            message: '请选择时间',
          },
          {
            validator(rule, value, callback) {
              console.log('rule, value:', rule, value);
              if (value[0] !== value[1]) {
                callback();
              } else {
                callback(new Error('开始结束时间不能相同！'));
              }
            },
            trigger: 'blur',
          },
        ],
      }),
      templateId: null,
      tableLoading: false,
      tableConfig: { tableData: [] },
      selectedList: [],
    };
  },
  computed: { ...mapState('app', ['shopConfig']) },
  created() {
    const {
      id,
      name,
    } = this.$route.query;
    if (id) {
      this.isCreate = true;
      this.actId = id;
      this.couponDetail(id);
    } else {
      this.isCreate = false;
      this.ruleForm.coupon = name;
    }
  },
  methods: {
    addPepel() {
    },
    onSelect(selectItem) {
      this.ruleForm.pepelName = selectItem.tagName;
      this.ruleForm.prMarketCustomerGroupId = selectItem.id;
    },
    addPeopel() {
      this.dialogVisible = true;
    },
    couponDetail(id) {
      this.isLoading = true;
      getCouponDetail({ id })
        .then((res) => {
          this.isLoading = false;
          if (res.success) {
            const {
              name,
              startTime,
              endTime,
              couponDetail,
              display,
            } = res.data.detail;
            this.ruleForm.name = name;
            this.ruleForm.coupon = couponDetail.name;
            this.templateId = couponDetail.id;
            this.ruleForm.creatTime = [startTime, endTime];
            this.ruleForm.display = display;
          } else if (res.msg) this.$message.error(res.msg);
        });
    },
    addCouBtn() {
      this.addCoupon = true;
    },
    getRowkey(row) {
      return row.id;
    },
    delCouBtn() {
      console.log(this.selectedList);
      if (this.selectedList.length > 0) {
        this.$confirm('是否确定删除?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: '',
        }).then(() => {
          const ids = this.selectedList.map(item => item.id);
          this.tableConfig.tableData = this.tableConfig.tableData.filter(item => !ids.includes(item.id));
          this.selectedList = [];
        });
      } else {
        this.$message.warning('请选择数据');
      }
    },
    handleSelectionChange(list) {
      this.selectedList = list;
    },
    querySel(selItem) {
      this.selItem = selItem;
      // const { name, id } = this.selItem
      // this.ruleForm.coupon = name
      // this.templateId = id
      this.tableConfig.tableData = selItem.map((item) => {
        item.sendType = '3';
        this.$set(item, 'count1', '');
        this.$set(item, 'count2', '');
        return item;
      });
      this.$forceUpdate();
      this.addCoupon = false;
    },
    cancelSel() {
      this.addCoupon = false;
    },
    radioChange(index) {
      this.$set(this.tableConfig, 'tableData', this.tableConfig.tableData.map((item, i) => {
        if (i === index) {
          this.$set(item, 'count1', '');
          this.$set(item, 'count2', '');
        }
        return item;
      }));
    },
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          if (this.ruleForm.participatingCrowd !== -1 && !this.ruleForm.prMarketCustomerGroupId) {
            this.$message.error('请选择人群');
            return;
          }
          // if (!this.selectedList.length > 0) {
          //   this.$message.error('请勾选优惠券');
          //   return;
          // }
          const query = {
            endTime: this.ruleForm.creatTime[1],
            startTime: this.ruleForm.creatTime[0],
            name: this.ruleForm.name,
            prMarketCustomerGroupId: this.ruleForm.participatingCrowd !== -1 ? this.ruleForm.prMarketCustomerGroupId : -1,
            id: this.isCreate ? this.actId : null,
            remark: this.ruleForm.remark,
            couponActivitySaveDetailParams: JSON.stringify(this.tableConfig.tableData.map((item) => {
              const obj = {};
              obj.count = item.count1 || item.count2;
              obj.sendType = item.sendType;
              obj.templateId = item.id;
              return obj;
            })),
          };
          if (this.shopConfig.showTag) {
            query.display = this.ruleForm.display;
          }
          couponAcSave(query)
            .then((res) => {
              if (res.success) {
                this.$message.success('提交成功');
                if(this.$store.state.permission.menuGray == 1) {
                  this.$router.replace({
                    path: 'storeVoucher',
                    query: { refresh: true,to: "activemanage" },
                  });
                }else {
                  this.$router.replace({
                    path: 'activemanage',
                    query: { refresh: true },
                  });
                }
              } else if (res.msg) this.$message.error(res.msg);
            });
        } else {
          console.log('error submit!!');
          return false;
        }
        return false;
      });
    },

    resetForm(formName) {
      if(formName) {
        this.$refs[formName].resetFields();
      }
      if(this.$store.state.permission.menuGray == 1) {
        this.$router.replace({
          path: 'storeVoucher',
          query: { to: "activemanage" },
        });
      }else {
        this.$router.push({ path: '/activemanage' });
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.radio-span {
  // font-size: 12px;
  // font-weight: 400;
  color: rgba(51, 51, 51, 1);
  line-height: 17px;
  margin-right: 30px;
}

.radio-class {
  margin-right: 0px;
  margin-top: 8px;
}

.serch {
  .el-form-item__label {
    padding: 0 20px 0 0 !important;
  }

  padding: 20px;
  font-weight: 500;

  .sign {
    display: inline-table;
    width: 3px;
    height: 13px;
    background: linear-gradient(360deg, #1d69c4 1%, #8bbdfc 99%);
    border-radius: 2px;
    margin-right: 8px;
  }

  .fromBlock {
    //width: 500px;
    margin-top: 20px;

    .couponName {
      display: inline-block;
      width: 70%;

      ::v-deep  .el-input__inner {
        border: none;
      }
    }
  }

  ::v-deep   .el-table tr td .cell {
    height: auto;
    line-height: normal;
    text-overflow: inherit;
    white-space: break-spaces;

    .el-radio-group {
      text-align: left;
    }
  }
}
</style>
