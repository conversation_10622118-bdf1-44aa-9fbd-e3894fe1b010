<script>
import { getGroupList } from '../../../api/product/index'
import ClickTips from '../../../components/lwq-components/clickTips.vue';
import addGroup from '../../control-goods/components/addGroup.vue'
export default {
  components: {
    ClickTips,
    addGroup
  },
  props: {
    value: {
      default: () => [],
      type: Array,
    },
    priceData: {
      default: () => {
        return {
          chainPrice: '',
          fob: ''
        }
      },
      type: Object
    },
    disabled: {
      default: false,
      type: Boolean,
    }
  },
  watch: {
    value: {
      handler(newVal) {
        if (JSON.stringify(newVal) == JSON.stringify(this.priceSkuPriceVos)) return
        this.priceSkuPriceVos = newVal ? newVal : [];
      }
    },
    priceSkuPriceVos: {
      handler(newVal) {
        this.$emit('input', newVal.map(item => {
          if (item.priceControlType == 1) {
            //固定
            item.basePriceType = '';
            item.ratio = '';
          }
          return item
        }));
      }
    }
  },
  mounted() {
    this.getGroupList();
  },
  data() {
    return {
      priceSkuPriceVos: [],
      userGroupList: [],
      dialog: {
        visible: false,
        data: {}
      }
    }
  },
  methods: {
    getGroupList() {
      getGroupList().then(res => {
        if(res.code == 0) {
          this.userGroupList = res.result
        }
      })

    },
    addAreaPrice() {
      this.priceSkuPriceVos.push({
        groupId: '',
        groupName: '',
        priceControlType: 1,
        basePriceType: 2,
        ratio: '',
        price: '',
        id	: '',           //编辑时才有，编辑时有修改把id删去，无修改则带上id
      });
    },
    deleteOne(index) {
      this.priceSkuPriceVos = this.priceSkuPriceVos.filter((item, i) => index != i)
    },
    userGroupChange(item) {
      this.userGroupList.some(val => {
        if (val.id == item.groupId) {
          item.groupName = val.groupName
          return true
        }
        return false
      })
      item.id = '';
    },
    show(groupId) {
      this.userGroupList.some(item => {
        if (item.id == groupId) {
          this.dialog.data = item
          return true
        }
        return false
      })
      this.dialog.visible = true;
    },
    valueChange($event, item, key, regexp) {
      if (regexp.test($event)) {
        item[key] =$event;
      }
    },
    getFloatPrice(item) {

      const fob = item.basePriceType == 2 ? this.priceData.chainPrice : this.priceData.fob
      console.log(Number.isNaN(fob) || Number.isNaN(item.ratio));

      if (fob == '' || item.ratio == '') {
        return ''
      }
     return item.priceControlType == 2 ? Number(fob * (1 + item.ratio / 100)).toFixed(2) : Number(fob * (1 - item.ratio / 100)).toFixed(2)
    }
  }
}
</script>

<template>
  <div>
    <div>
      <el-button size="mini" type="primary" @click="addAreaPrice" :disabled="disabled">添加区域价格</el-button>
      <ClickTips v-if="!disabled" @ok="priceSkuPriceVos = []">
        <el-button size="mini" type="text" style="color: red;margin-left: 10px;">
          <i class="el-icon-delete"></i>删除全部区域价格
        </el-button>
        <template slot="content">
          <p>确认删除当前商品的所有区域价格吗？</p>
        </template>
      </ClickTips>
    </div>
    <div style="margin: 5px 0;">
      <div class="i-item">
        <div style="width: 200px;">
          <span style="color:red;">*</span>
          <span>用户组</span>
          <el-tooltip class="item" effect="dark" content="Top Center 提示文字" placement="top">
            <i class="el-icon-warning-outline"></i>
            <template slot="content">
              <p style="margin: 5px 0;">1、可点击“添加用户组”按钮或进入【商品管理-价格用户组】菜单进行用户组配置；</p>
              <p style="margin: 5px 0;">2、用户组选择支持模糊搜索；</p>
            </template>
          </el-tooltip>
          <addGroup @search="getGroupList">
            <el-button size="mini" type="text" style="margin-left:20px" :disabled="disabled">+添加用户组</el-button>
          </addGroup>
        </div>
        <div style="width: 110px;">
          <span style="color:red;">*</span>
          <span>控价方式</span>
          <el-tooltip class="item" effect="dark" content="Top Center 提示文字" placement="top">
            <i class="el-icon-warning-outline"></i>
            <template slot="content">
              <p style="margin: 5px 0;">1、若选择“固定”，则可以自定义当前用户组的商品售价；</p>
              <p style="margin: 5px 0;">2、若选择“浮动上调”，则当前用户组的商品售价=“单体采购价或连锁采购价“*（1+浮动比例）；</p>
              <p style="margin: 5px 0;">3、若选择“浮动下调”，则当前用户组的商品售价=“单体采购价或连锁采购价“*（1-浮动比例）；</p>
            </template>
          </el-tooltip>
        </div>
        <div>
          <span style="color:red;">*</span>
          <span>价格设置</span>
          <el-tooltip class="item" effect="dark" content="Top Center 提示文字" placement="top">
            <i class="el-icon-warning-outline"></i>
            <template slot="content">
              <p style="margin: 5px 0;">1、区域价格不能为0；</p>
              <p style="margin: 5px 0;width: 300px;">2、商品引用价格用户组设置区域价时，同一客户类型若同时设置了“省份”、“城市”、“区县”三个区域价格且省市区县为包含关系，则对客户展示区域价格的优先级为：区县>城市>省份，即优先展示区县用户组的区域价格；</p>
            </template>
          </el-tooltip>
        </div>
        <div style="width: 40px;">操作</div>
      </div>
      <div v-for="(item, index) in priceSkuPriceVos" class="i-item">
        <div style="width: 200px;">
          <el-select :popper-append-to-body="false" size="mini" v-model="item.groupId" @change="userGroupChange(item)" style="width: 150px;margin-right: 5px;" :disabled="disabled" filterable>
            <el-option v-for="(group, index) in userGroupList" :key="index" :label="group.groupName" :value="group.id" :disabled="priceSkuPriceVos.some(item => item.groupId == group.id)"></el-option>
          </el-select>
          <el-button v-if="item.groupId" size="mini" type="text" @click="show(item.groupId)">查看</el-button>
        </div>
        <div  style="width: 110px;">
          <el-select size="mini" v-model="item.priceControlType" @change="item.id = ''" :disabled="disabled" :popper-append-to-body="false">
            <el-option label="固定" :value="1"></el-option>
            <el-option label="浮动上调" :value="2"></el-option>
            <el-option label="浮动下调" :value="3"></el-option>
          </el-select>
        </div>
        <div>
          <div v-if="item.priceControlType===1" i-data="固定">
            <el-input :disabled="disabled" :value="item.price" size="mini" placeholder="请输入大于0小于999999的数字，限两位小数" @change="item.id = ''" @input="valueChange($event, item, 'price', /^([0]([.][0-9]{0,2})?|[1-9][0-9]{0,5}([.][0-9]{0,2})?)?$/)"></el-input>
          </div>
          <div v-else i-data="浮动上调" style="display: flex;align-items: center;">
            <el-select :popper-append-to-body="false" size="mini" :disabled="disabled" v-model="item.basePriceType" style="margin-right: 10px;width: 110px;flex-shrink: 0;" @change="item.id = ''">
              <el-option label="单体采购价" :value="1"></el-option>
              <el-option label="连锁采购价" :value="2"></el-option>
            </el-select>
            <span style="margin-right: 5px;flex-shrink: 0;">浮动</span>
            <el-input :disabled="disabled" size="mini" :value="item.ratio" style="width: 70px;flex-shrink: 0;" @change="item.id = ''" @input="valueChange($event, item, 'ratio', /^([0]([.][0-9]{0,2})?|[1-9][0-9]{0,1}([.][0-9]{0,2})?|100)?$/)"></el-input>
            <span style="margin: 5px;flex-shrink: 0;">%</span>
            <span style="margin: 5px;flex-shrink: 0;">区域价格</span>
            <el-input v-if="item.priceControlType == 1" :disabled="disabled" v-model="item.price" size="mini" placeholder="请输入大于0小于999999的数字，限两位小数" style="width: 0;flex-grow: 1;flex-shrink: 1;" @change="item.id = ''" @input="valueChange($event, item, 'price', /^([0]([.][0-9]{0,2})?|[1-9][0-9]{0,5}([.][0-9]{0,2})?)?$/)"></el-input>
            <el-input v-else :disabled="true" :value="getFloatPrice(item)" size="mini" style="width: 0;flex-grow: 1;flex-shrink: 1;"></el-input>
          </div>
        </div>
        <div style="width: 40px;">
          <el-button v-if="!disabled" size="mini" type="text" @click="deleteOne(index)" style="color: red;"><i class="el-icon-delete"></i>删除</el-button>
        </div>
      </div>
    </div>
    <el-dialog :visible.sync="dialog.visible" title="用户组信息" width="500px" append-to-body>
      <p>价格用户组ID：{{ dialog.data.id }}</p>
      <p>价格用户组名称：{{ dialog.data.groupName }}</p>
      <p>地域：{{ dialog.data.areaNames }}</p>
      <p>客户类型：{{ dialog.data.customerTypeNames }}</p>
      <div slot="footer">
        <el-button type="primary" size="mini" @click="dialog.visible = false;">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<style scoped>
.i-item {
  display: flex;
  align-items: center;
  padding: 5px 0;
  gap: 10px;
  border-bottom: 1px solid #f0f0f0;
}
.i-item > div {
  flex-shrink: 0;
}
.i-item > div:nth-child(3) {
  flex-grow: 1;
}
</style>
