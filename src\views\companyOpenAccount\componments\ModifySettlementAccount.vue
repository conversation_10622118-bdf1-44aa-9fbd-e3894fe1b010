<template>
  <el-dialog
    title='操作流程'
    :visible.sync='dialogVisible'
    width='688px'
    :before-close='handleClose'>
    <el-form :model='formModel' :rules='formRules' ref='form' label-width='120px'>
      <el-form-item label='开卡人名称:' prop='registeredName'>
        <el-input
          v-model='formModel.registeredName'
          disabled
        ></el-input>
      </el-form-item>
      <el-form-item label='银行账号:' prop='registeredBankAccount'>
        <el-input
          v-model='formModel.registeredBankAccount'
          placeholder='请输入企业的对公收款账号'
        ></el-input>
      </el-form-item>
      <el-form-item label='开户银行名称:' prop='bankName'>
        <el-select
          v-model='formModel.bankName'
          placeholder='请选择开户银行'
          style='width: 400px'
          :filterable='true'
          @change='bankChange'
          :key='formModel.bankName'
        >
          <el-option
            v-for='item in bankList'
            :key='item.bankName'
            :label='item.bankName'
            :value='item.bankName'
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label='开户银行支行:' prop='subBankName'>
        <el-select
          ref='subBank'
          v-model='formModel.subBankName'
          placeholder='请选择开户支行名称'
          style='width: 400px'
          :filterable='true'
          :filter-method='searchSubBank'
          @change='subBankChange'
          :key='formModel.subBankName'
        >
          <el-option
            v-for='item in subBankList'
            :key='item.bankName'
            :label='item.bankName'
            :value='item.bankName'
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label='商户手机号:' prop='phone'>
        <el-input
          style='width: 240px'
          v-model='formModel.phone'
          disabled
        ></el-input>
        <el-button
          style='margin-left: 20px'
          type='primary'
          @click='sendVerificationCode'
          :disabled='sendDisabled'
        >发送验证码{{ sendDisabled ? `(${sendCountDown})` : '' }}
        </el-button>
      </el-form-item>
      <el-form-item label='验证码:' prop='smsVerificationCode'>
        <el-input
          v-model='formModel.smsVerificationCode'
          placeholder='请输入商户手机号收到的验证码'
        ></el-input>
      </el-form-item>
    </el-form>
    <div slot='footer' class='dialog-footer'>
      <el-button size='small' @click='handleClose'>取 消</el-button>
      <el-button type='primary' size='small' @click='save' :loading='saveLoading'>提 交</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { queryBankList, querySubBankList, sendActiveCode, updateBankAccount } from '@/api/companyOpenAccount'

export default {
  name: 'ModifySettlementAccount',
  props: {
    modifySettlementObj: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      dialogVisible: true,
      formModel: {
        registeredName: '',
        registeredBankAccount: '',
        bankName: '',
        bankCode: '',
        subBankName: '',
        subBankCode: '',
        phone: '',
        smsVerificationCode: ''
      },
      formRules: {
        registeredBankAccount: [{ required: true, message: '请输入银行账号', trigger: 'blur' }],
        bankName: [{ required: true, message: '请选择开户银行名称', trigger: 'blur' }],
        subBankName: [{ required: true, message: '请选择开户支行名称', trigger: 'blur' }],
        smsVerificationCode: [{ required: true, message: '请输入短信验证码', trigger: 'blur' }]
      },
      bankList: [],
      subBankList: [],
      sendDisabled: false,
      sendCountDown: 60,
      saveLoading: false
    }
  },
  created() {
    this.formModel = { ...this.formModel, ...this.modifySettlementObj };
    this.getBankList();
  },
  methods: {
    handleClose() {
      this.$emit('update:modifySettlementVisible', false)
    },
    async getBankList() {
      const res = await queryBankList()
      if (res && res.code === 0) {
        this.bankList = res.result
      } else {
        this.$message.error(res.msg || '获取开户行失败')
      }
    },
    async bankChange(val) {
      const [obj] = this.bankList.filter(item => item.bankName === val)
      this.formModel.bankCode = obj.bankCode
      this.formModel.subBankName = ''
      this.formModel.subBankCode = ''
      const res = await querySubBankList({ bankName: val, subBankName: '' })
      if (res && res.code === 0) {
        this.subBankList = res.result
      } else {
        this.$message.error(res.msg || '获取开户支行失败')
      }
    },
    subBankChange(val) {
      const [obj] = this.subBankList.filter(item => item.bankName === val)
      this.formModel.subBankCode = obj.bankCode
    },
    async searchSubBank(val) {
      console.log(val)
      const res = await querySubBankList({ bankName: this.formModel.bankName, subBankName: val })
      if (res && res.code === 0) {
        this.subBankList = res.result
      } else {
        this.$message.error(res.msg || '获取开户支行失败')
      }
    },
    async sendVerificationCode() {
      this.sendDisabled = true
      try {
        const res = await sendActiveCode({ phone: this.formModel.phone, codeType: 3 })
        if (res && res.code === 0) {
          this.timeOutSend()
          this.$message.success('验证码发送成功')
        } else {
          this.sendDisabled = false
          this.sendCountDown = 60
          this.$message.error(res.msg)
        }
      } catch (e) {
        console.log(e)
      }
    },
    timeOutSend() {
      setTimeout(() => {
        if (this.sendCountDown > 0) {
          this.sendCountDown--
          this.timeOutSend()
        } else {
          this.sendDisabled = false
          this.sendCountDown = 60
        }
      }, 1000)
    },
    save() {
      this.$refs.form.validate(async (valid) => {
        if (valid) {
          this.saveLoading = true
          const params = { ...this.formModel }
          try {
            const res = await updateBankAccount(params)
            if (res && Number(res.code === 0)) {
              this.$message.success('更新成功')
              this.$emit('updateInfo')
              this.handleClose()
            } else {
              this.$message.error(res.msg || '更新失败')
            }
          } catch (e) {
            console.log(e)
          }
          this.saveLoading = false
        }
      })
    }
  }
}
</script>

<style scoped>
::v-deep   .el-form {
  width: 80%;
  margin: 0 auto;
}
</style>
