<template>
  <el-dialog title="未关联标品信息，商品即将下架提醒" :visible.sync="visids" width="30%">
    <div>
      您有{{ count }}个商品未关联标品信息，将在{{ endDate }}下架, 请及时按照提示更新商品信息，关联标品信息。<el-button type="text" @click="backFunction()">点击筛选</el-button>
    </div>
    <span slot="footer">
      <el-button size="medium" type="primary" @click="visids = false">我知道了</el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  props: {
    count:{
        type:Number,
        default: 0
    },
    endDate:{
        type:String
    }
  },
  data() {
    return {
      visids: false,
    }
  },
  methods: {
    open() {
      this.visids = true
    },
    backFunction(){
      debugger
      this.$emit('filter');
      this.visids = false
    }
  },
}
</script>

<style scoped>
</style>