<script>
import { apiApplyList, getIsApplyStepPrice, saveActivity,updateCheck,reportUpdate  } from '@/api/market/collageActivity'
import SupplyTypeConfig from '../newMarketing/components/supplyTypeConfig.vue';
export default {
  props: {
    value: {
      type: Object,
      default: () => {}
    }
  },
  components: {
    SupplyTypeConfig
  },
  watch: {
    value: {
      handler(newVal) {
        if (!this.value.visible) return;
    
        this.loading = true;
        console.log(this.value.actId)
        this.originData=JSON.parse(JSON.stringify(this.value.actId));
        this.initData(this.originData);
        this.getIsApplyStepPrice(this.originData);
        this.loading = false;
        
      },
      deep:true
    }
  },
  data() {
    return {
      originData: {},
      loading: false,
      titleMap: {
        '1' : '修改价格',
        '2' : '修改虚拟供应商',
        '3' : '修改供货信息',
        '4' : '修改活动库存'
      },
      rules: Object.freeze({
        reportPrice: [{ required: true, message: '请填写拼团价格',trigger: 'blur' }],
        startQty: [
          { required: true, message: '请填写起拼数量',trigger: 'blur' },
          { pattern: /^[1-9]\d*$/, message: '请维护正整数', trigger: ['blur', 'change'] }
        ],
        totalQty: [
          { required: false, message: '请填写拼团活动采购上限',trigger: 'blur' },
          { pattern: /^[1-9]\d*$/, message: '请维护正整数', trigger: ['blur', 'change'] }
        ],
        personalQty: [{ required: false, message: '请填写单个药店参团数量上限',trigger: 'blur' }],
      }),
      ruleForm: {
        marketingActivityLevelDTOList: [],
        stepPriceStatus: -1,
        isCopyCsuModel: 1,
        personalLimitType: '0',
        reportPrice: '',
        startQty: '',
        customerGroupId: undefined,
        customerGroupName: '',
        totalQty: '', //
        personalQty: '',//
        preheatTime: '',
        activityTime: [],
        auditTime: [],
        registrationTime: [],
        isVirtualShop: '',
        onTheWayStock: '',
        topicTitle: '',
        baseCustomerGroupId: '',
        customerGroupVO: null,
      },
      submitLoading: false,
      disabled: false,
      otherRuleDisabled: false,
      judgeIsApplyStepPrice: true, //
      personalQtyDisabled: false,
      pintuanDisabled: false,
      subOtherDisabled: false,
      frameActReportSaleScopeDTO: {},
      swtich: {
        value1: false,
        value2: false,
        personalLimitTypeList: [{
          code: 1, value: "活动期间限购"
        }, {
          code: 2, value: "每天（每天00:00至24:00）"
        }, {
          code: 3, value: "单笔订单限购"
        }, {
          code: 4, value: "每周（周一00:00至周日24:00）"
          }, {
          code: 5, value: "每月（每月1号00:00至每月最后一天24:00）"
        }]
      },
      statusList: {
        status1: false,   //是否不允许修改单个药店限购
        status2: false,  //是否不允许修改限购类型
        status3: false,  //是否不允许修改限购数量
        status4: false,  //是否不允许修改拼团活动限购
        status5: false,  //是否不允许修改拼团活动总数量
      },
      isHidden: true,
    }
  },
  methods: {
    close() {
      const result = this.value;
      result.visible = false;
      this.$refs.ruleForm.resetFields();
      this.$emit('input', result);
    },
    initData(data) {
      this.disabled = false;
      this.pintuanDisabled = false;
      this.statusList = {
        status1: false,   //是否不允许修改单个药店限购
        status2: false,  //是否不允许修改限购类型
        status3: false,  //是否不允许修改限购数量
        status4: false,  //是否不允许修改拼团活动限购
        status5: false,  //是否不允许修改拼团活动总数量
      }
      Object.keys(this.ruleForm).forEach((key) => {
        this.ruleForm[key] = data[key];
      }, this);
      this.swtich.value1 = (!isNaN(Number(this.ruleForm.personalLimitType)) && Number(this.ruleForm.personalLimitType) > 0)
      this.swtich.value2 = (!isNaN(Number(this.ruleForm.totalQty)) && Number(this.ruleForm.totalQty) > 0)
      this.ruleForm.personalLimitType = this.ruleForm.personalLimitType||"".toString();
      this.ruleForm.auditTime = [data.auditStime, data.auditEtime];
      this.ruleForm.activityTime = [data.actStartTime, data.actEndTime];
      this.ruleForm.preheatTime = data.preheatTime;
      this.ruleForm.isCopyCsuModel = data.isCopyCsuModel;
      this.ruleForm.customerGroupId = data.customerGroupId;
      this.ruleForm.isVirtualShop = String(data.isVirtualShop);
      //  this.ruleForm.stepPriceStatus = data.stepPriceStatus||-1;
      this.ruleForm.marketingActivityLevelDTOList = data.marketingActivityLevelDTOList;

      this.otherRuleDisabled = true;
      this.subOtherDisabled = true;
      this.personalQtyDisabled = false;

      this.frameActReportSaleScopeDTO = {
        ...data.frameActReportSaleScopeDTO
      };
      // alert(this.frameActReportSaleScopeDTO.scopeType)
      if(this.frameActReportSaleScopeDTO.scopeType==1||this.frameActReportSaleScopeDTO.scopeType==2){
      
      this.frameActReportSaleScopeDTO.isCopySaleArea=this.frameActReportSaleScopeDTO.scopeType+1
      // alert( this.frameActReportSaleScopeDTO.isCopySaleArea)
    }else if(this.frameActReportSaleScopeDTO.scopeType==-1){
      this.frameActReportSaleScopeDTO.isCopySaleArea=1
    }
      console.log(this);
    },
    getIsApplyStepPrice(data) {
      console.log(data.marketingActivityLevelDTOList,123)
      if(data.marketingActivityLevelDTOList&&data.marketingActivityLevelDTOList.length>0){
        // alert(1)
        this.ruleForm.stepPriceStatus=1
      }else{
        this.ruleForm.stepPriceStatus=2
      }
      console.log(this.ruleForm.stepPriceStatus)
    },
    handleSetGroupLevelPriceDTOList(type, index) {
      if (this.disabled && this.otherRuleDisabled) return;
      if (type === 'add') {
        if (
          this.ruleForm.marketingActivityLevelDTOList.length === 3
        ) {
          this.$message.warning('最多添加3条数据');
          return;
        }
        const obj = { ...this.addInitiatorDTOItem };
        this.ruleForm.marketingActivityLevelDTOList.push(obj);
        return;
      }
      this.ruleForm.marketingActivityLevelDTOList.splice(index, 1);
    },
    submit(){
      // if (this.submitLoading) return true;
      this.$refs.ruleForm.validate(async (valid) => {
        if (valid) {
          if ((this.ruleForm.reportPrice && isNaN(this.ruleForm.reportPrice))
              || (this.ruleForm.startQty && isNaN(this.ruleForm.startQty))
              || (this.ruleForm.totalQty && isNaN(this.ruleForm.totalQty))
              || (this.ruleForm.personalQty && isNaN(this.ruleForm.personalQty))
              || (this.ruleForm.onTheWayStock && isNaN(this.ruleForm.onTheWayStock))
          ) {
            this.$message.warning('请填写数字');
            return;
          }
          let supplyInfo = this.$refs['supplyTypeInfo'].getAllSupplyInfo();
          if (this.ruleForm.stepPriceStatus === 1) {
            if (
              this.ruleForm.marketingActivityLevelDTOList
            ) {
             try{
              this.ruleForm.marketingActivityLevelDTOList.forEach(
                (item, index) => {
                  if(!item.discountPrice||!item.startQty){
                    this.$message.warning(`阶梯价第${index + 1}行数据未填写完整`);
                    
                    throw new Error();
                    
                  }
                  item.level = index + 1;
                },
              );
             }catch{
              return
             }
            }
          }
          const editCollageItem = this.ruleForm;
          // if (this.swtich.value1 && (!this.ruleForm.personalLimitType || !this.ruleForm.personalQty)) {
          //   this.$message.warning("请填写单个药店限购类型及参团数量上限")
          //   return
          // }
          console.log(this.ruleForm,supplyInfo)
         //跟新类型 value.type  type: '',  //1:拼团价格，2：设置虚拟供应商，3：修改供货信息，4：修改活动库存
         let data={

         }
         switch(this.value.type){
           case 1:
             data={
              reportPrice:this.ruleForm.reportPrice,
              startQty:this.ruleForm.startQty,              
             }
             if(this.ruleForm.stepPriceStatus==1){
              data.marketingActivityLevelDTOList=this.ruleForm.marketingActivityLevelDTOList
             }else{
              data.marketingActivityLevelDTOList=null
             }
             break;
            case 2:
              data={
                isVirtualShop:this.ruleForm.isVirtualShop
              }
              break;
            case 3:
                data.frameActReportSaleScopeDTO=supplyInfo
              break;
            case 4:
            if(this.swtich.value1){
              data.personalLimitType=this.ruleForm.personalLimitType
              data.personalQty=this.ruleForm.personalQty
             }else{
              data.personalLimitType="0"
              data.personalQty=-1
             }
             
             if(this.swtich.value2){
              data.totalQty=this.ruleForm.totalQty
             }else{
              data.totalQty=-1
             }
              break;  
         }
         console.log(data,"lwq")
         data.id=this.originData.id,
         data.baseId=this.originData.baseId
         this.submitLoading = true;
         reportUpdate(data).then(res=>{
          if(res.code==1000){
            this.$message.success("修改成功")
            this.$emit("getList","")
            this.close();
          }else{
            this.$message.error(res.msg)
          }
         }).finally(res=>{
          this.submitLoading = false;
         })
        } else {
          return false;
        }
        return false;
      });
    },
    valueChange(value, key) {
      const arr = value.toString().match(/^[0-9]*[1-9][0-9]*$/);
          this.ruleForm[key] = arr ? arr[0] : null;
    },
    handleChangeItemStepPriceStatus(val) {
      if (val === 2) {
        this.ruleForm.marketingActivityLevelDTOList = null;
      } else if (
        this.ruleForm.marketingActivityLevelDTOList === null || (this.ruleForm.marketingActivityLevelDTOList || []).length === 0
      ) {
        const arr = [];
        const obj = { ...this.addInitiatorDTOItem };
        arr[0] = obj;
        this.ruleForm.marketingActivityLevelDTOList = arr;
      }
    },
   async confirmSubmit(query) {
    this.submitLoading = true;
     let res=await updateCheck(query)
     this.submitLoading = false;
     if(res.code!=1000){
        const confirmRes = await this.$confirm(
              res.msg,
                '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }
            ).catch(err => console.log(err))
            // 判断confirmRes的值
            if (confirmRes !== 'confirm') {
                return
            }
     }
      this.submitLoading = true;
      saveActivity(query)
      .then((res) => {
        if (res.success) {
          this.$message.success('提交成功');
          setTimeout(() => {
            this.$emit("getList","")
            this.close();
            // this.$router.replace({
            //   path: '/collageActivity',
            //   query: { refresh: true },
            // });
          }, 500);
        } else if (res.msg) this.$message.warning(res.msg);
      }).finally(() => {
        this.submitLoading = false;
      })
    },
  },
}
</script>
<template>
  <el-dialog :title="titleMap[value.type]" :visible="value.visible" @close="close" >
    <el-form ref="ruleForm" :model="ruleForm" :rules="rules" size="small" label-width="170px">
      <div v-show="value.type == 1">
        <el-form-item label="拼团价格" prop="reportPrice">
          <el-input  v-model="ruleForm.reportPrice" placeholder="请输入" style="width: 200px" />
          <span v-if="originData.buyMostPrice" style="color: #ff2121;margin-left: 8px">热销拼团价格：{{ originData.buyMostPrice }}</span>
        </el-form-item>
        <el-form-item label="起拼数量" prop="startQty">
          <el-input v-model="ruleForm.startQty" placeholder="请输入" style="width: 200px" />
          <span v-if="originData.buyMostStartQty" style="color: #ff2121">热销起拼数量：{{ originData.buyMostStartQty }}</span>
        </el-form-item>
          <el-form-item v-if="judgeIsApplyStepPrice" label="是否设置阶梯价" prop="stepPriceStatus">
            <el-radio-group v-model="ruleForm.stepPriceStatus" style="margin-right: 10px"  @change="handleChangeItemStepPriceStatus">
              <el-radio :label="2">否</el-radio>
              <el-radio :label="1">是</el-radio>
            </el-radio-group>
            <el-tooltip placement="bottom">
              <div slot="content">
                1.阶梯价最多支持3个阶梯。<br />
                2. 设置阶梯价时 最大阶梯起拼数量≤活动个人限购数量≤活动总限购数量，不满足则提交失败。<br />
                3.商品列表中展示的起拼数量为默认起拼数量，一个拼团活动中最多可支持4个拼团价格。<br />
                4.药店买满起始促销数量后，成团价自动改为对应阶梯价；<br />
                起拼数量＜促销起始数量1＜促销起始数量2＜促销起始数量3≤活动个人限购数量，拼团价格＞阶梯价1＞阶梯价2＞阶梯价3，不符合则提交失败；
              </div>
              <i class="el-icon-warning-outline" />
            </el-tooltip>
          </el-form-item>
		      <el-form-item v-if="judgeIsApplyStepPrice && ruleForm.stepPriceStatus === 1" label="" >
            <div v-for="(item, index) in ruleForm.marketingActivityLevelDTOList" :key="index" style="display: flex; align-items: center;margin-bottom: 10px;align-items: center;">
              <span style="margin-right: 10px;">促销起始数量{{ index + 1 }}</span>
              <el-input v-model.number="item.startQty" :precision="0"  placeholder="请输入" style="width: 200px" />
              <span style="margin: 0 10px;">阶梯价{{ index + 1 }}</span>
              <el-input-number v-model="item.discountPrice" :precision="2"  placeholder="请输入" style="width: 200px" />
              <div v-if="isHidden">
                <i v-if="index === 0" style="font-size: 30px; cursor: pointer;margin-left: 10px;" class="el-icon-circle-plus-outline" @click="handleSetGroupLevelPriceDTOList('add', index)" />
                <i v-if="index !== 0" style="font-size: 30px; cursor: pointer;margin-left: 10px;" class="el-icon-remove-outline" @click="handleSetGroupLevelPriceDTOList('reduce', index)"/>
              </div>
            </div>
          </el-form-item>
      </div>
      <div v-show="value.type == 2">
        <el-form-item label="设置虚拟供应商" prop="isVirtualShop">
          <el-radio-group v-model="ruleForm.isVirtualShop">
            <el-radio   label="1">是</el-radio>
          <el-radio  label="2">否</el-radio>
          </el-radio-group>
          
          <span style="color: #ff2121;display: inline-block;width: 80%;vertical-align: top">
            &nbsp; &nbsp; • 选“是”，则该拼团活动支付前都只显示虚拟供应商信息，支付成功后才显示真实供应商
            信息<br>
            &nbsp; &nbsp;• 选“否”，则正常显示供应商信息
          </span>
        </el-form-item>
      </div>
      <div v-show="value.type == 4">
        <el-form-item label="单个药店是否限购">
            <el-switch v-model="swtich.value1" active-color="#4183d5" inactive-color="#b1b1b1" active-text="限制" inactive-text="不限制" @change="ruleForm.personalLimitType = ''; ruleForm.personalQty = ''" style="vertical-align: top;transform: translateY(7px);">
            </el-switch>
            <div style="display: inline-block;margin-left: 10px;">
              <span style="color: rgb(255, 33, 33);">
                单个药店在设定的周期内可购买的最大采购数量，需为正整数。限购数量需≥起拼数量;
                <br>
                系统会从修改那一刻重新计算限购数量。
              </span>
            </div>
        </el-form-item>
        <el-form-item label="单个药店限购类型" prop="personalLimitType" v-if="swtich.value1">
          <el-select v-model="ruleForm.personalLimitType"  placeholder="请选择">
            <el-option v-for="item in swtich.personalLimitTypeList" :key="item.code" :label="item.value" :value="item.code"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item v-if="swtich.value1" label="单个药店参团数量上限" prop="personalQty">
          <el-input  v-model="ruleForm.personalQty" placeholder="请输入正整数" style="width: 200px" @input="valueChange(ruleForm.personalQty, 'personalQty')"/>
          <span style="color: #ff2121"> 每家参团药店活动期间最高采购数量</span>
        </el-form-item>
        <el-form-item label="是否限制活动总限购">
            <el-switch  v-model="swtich.value2" @change="ruleForm.totalQty = ''" active-color="#4183d5" inactive-color="#b1b1b1" active-text="限制" inactive-text="不限制">
            </el-switch>
        </el-form-item>
        <el-form-item label="拼团活动采购上限" prop="totalQty" v-if="swtich.value2">
          <el-input  v-model="ruleForm.totalQty" placeholder="请输入" style="width: 200px" />
          <span style="color: #ff2121"> 所有参团药店采购总数量不能大于该数值</span>
        </el-form-item>
      </div>
      <div v-show="value.type == 3">
        <SupplyTypeConfig ref="supplyTypeInfo" :isShopUpdate="false" :isShopLoad="true" :isShow="false" :dis="false" :baseCustomerGroupName="ruleForm.customerGroupVO ? ruleForm.customerGroupVO.tagName : ''"   :subOtherDisabled="subOtherDisabled" :frameActReportSaleScopeDTO="frameActReportSaleScopeDTO" />
      </div>
    </el-form>
    <template slot="footer">
      <el-button type="primary" @click="submit">提交</el-button>
      <el-button @click="close">取消</el-button>
    </template>
  </el-dialog>
</template>
<style scoped>
</style>
