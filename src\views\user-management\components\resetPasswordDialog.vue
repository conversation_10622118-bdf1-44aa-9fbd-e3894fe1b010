<template>
  <el-dialog title="重置密码" :visible="dialogVisible" width="40%" :before-close="handleClose">
    <el-form ref="formData" :model="formData" :rules="rules" size="small">
      <el-form-item label="新密码" :label-width="formLabelWidth" prop="password">
        <el-input
          v-model="formData.password"
          autocomplete="off"
          type="password"
          class="diaSWidth"
          placeholder="请输入新密码，仅支持数字、字母和特殊字符"
        />
      </el-form-item>
      <el-form-item label="再次输入新密码" :label-width="formLabelWidth" prop="passwordAgain">
        <el-input
          v-model="formData.passwordAgain"
          autocomplete="off"
          type="password"
          placeholder="请再次输入新密码，仅支持数字、字母和特殊字符"
          class="diaSWidth"
        />
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="handleClose" size="small">取 消</el-button>
      <el-button type="primary" @click="submitForm('formData')" size="small" :loading="loading">提 交</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { apiResetPassword } from '@/api/userManagement';
export default {
  name: "ResetPasswordDialog",
  props: {
    userId: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      dialogVisible: true,
      formData: {
        password: '',
        passwordAgain:''
      },
      loading: false,
      rules: {
        password: [
          { required: true, message: '处理登录密码不能为空', trigger: 'blur' },
          { message: '登录密码需为数字、字母、特殊字符的任意两种组合', trigger: 'blur' },
        ],
        passwordAgain: [
          { required: true, message: '处理登录密码不能为空', trigger: 'blur' },
          { message: '登录密码需为数字、字母、特殊字符的任意两种组合', trigger: 'blur' },
        ],
      },
      formLabelWidth: '140px',
    }
  },
  created() {
  },
  methods: {
    handleClose() {
      this.$emit('cancellationBtn');
    },
    async submitForm(formData) {
      const that = this;
      that.$refs[formData].validate((valid) => {
        if (valid) {
          that.loading = true
          let param = {
            ...that.formData
          }
          param.userId = that.userId;
          apiResetPassword(param).then((res) => {
            this.loading = false
            if (res.code === 0) {
              that.$message.success('重置密码成功');
              that.$emit('refreshList');
            } else {
              that.$message.error(res.message);
            }
          });
          return true;
        } else {
          return false;
        }
      });
    }
  }
}
</script>

<style lang="scss" scoped>
.diaSWidth {
  width: 320px;
}
</style>
