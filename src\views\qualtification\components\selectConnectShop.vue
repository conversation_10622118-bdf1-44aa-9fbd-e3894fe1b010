<template>
  <div>
    <el-dialog
      title="选择关联企业"
      :visible="dialogVisible"
      width="60%"
      @close="closeDialog"
    >
      <div>
        <el-form
          ref="ruleForm"
          :model="searchInfo"
          size="small"
          label-width="100px"    
        >
          <el-form-item
            ref="addr"
            label="企业地址："
            class=""
          >
            <div>
              <el-form-item class="width25">
                <el-select
                  v-model.trim="searchAddressInfo.provinceId"
                  placeholder="省份"
                  @change="selectChange($event, 'cityList')"
                >
                  <el-option
                    v-for="(item, index) in provList"
                    :key="index"
                    :label="item.areaName"
                    :value="item.areaCode"
                  />
                </el-select>
              </el-form-item>
              <el-form-item class="width25">
                <el-select
                  v-model.trim="searchAddressInfo.cityId"
                  placeholder="城市"
                  @change="selectChange($event, 'areaList')"
                >
                  <el-option
                    v-for="(item, index) in cityList"
                    :key="index"
                    :label="item.areaName"
                    :value="item.areaCode"
                  />
                </el-select>
              </el-form-item>
              <el-form-item class="width25">
                <el-select
                  v-model.trim="searchAddressInfo.districtId"
                  placeholder="区县"
                  @change="selectChange($event, 'streeList')"
                >
                  <el-option
                    v-for="(item, index) in areaList"
                    :key="index"
                    :label="item.areaName"
                    :value="item.areaCode"
                  />
                </el-select>
              </el-form-item>
              <el-form-item class="width25">
                <el-select
                  v-model.trim="searchAddressInfo.streetId"
                  placeholder="街道"
                  @change="selectChange($event, '')"
                >
                  <el-option
                    v-for="(item, index) in streeList"
                    :key="index"
                    :label="item.areaName"
                    :value="item.areaCode"
                  />
                </el-select>
              </el-form-item>
            </div>
          </el-form-item>
          <el-row>
            <el-col :span="10">
              <el-form-item label="POI_ID：">
                <el-input
                  v-model.trim="searchInfo.poiId"
                  type="text"
                  placeholder="请输入招商提供的POI_ID"
                  style="width: 200px"
                />
              </el-form-item>
            </el-col>
            <el-col :span="10">
              <el-form-item label="企业名称：">
                <el-input
                  v-model.trim="searchInfo.poiCorpName"
                  type="text"
                  placeholder="请输入营业执照上的企业名称"
                  style="width: 200px"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <div style="color: red;">
            请选择平台中当前企业进行关联，企业名称搜索条件需与营业执照企业名称保持一致
            <br />
            如在平台企业信息中找不到您的注册企业，请联系招商人员跟进处理
          </div>
          <el-form-item class="searchBtn" style="text-align: right;padding-right: 20px">
            <el-button @click="resetForm('ruleForm')">
              重置
            </el-button>
            <el-button
              type="primary"
              @click="submitForm('ruleForm')"
            >
              查询
            </el-button>
          </el-form-item>
        </el-form>
        <el-table
          v-loading="isLoading"
          :data="tableData"
          stripe
          border
          style="width: 100%"
          :header-cell-style="{ background: '#F9F9F9', color: '#666666' }"
        >
          <el-table-column
            type="selection"
            width="55"
          >
            <template #default="{ row }">
              <el-radio
                :label="row.poiId"
                @input="selectRadioChange($event, row)"
                :value="selectedPoiId"
              >
                <span />
              </el-radio>
            </template>
          </el-table-column>
          <el-table-column
            label="POI_ID"
            prop="poiId"
          />
          <el-table-column
            prop="name"
            label="平台企业信息"
          >
            <template slot-scope="{row}">
              <div>{{ row.poiCorpName }}</div>
              <div style="color: #999;">{{ row.poiCorpAddress }}</div>
            </template>
          </el-table-column>
        </el-table>
        <div class="pagination-container">
          <div class="pag-text">
            共 {{ pageInfo.totalCount }} 条数据
          </div>
          <el-pagination
            background
            :current-page.sync="pageInfo.pageNum"
            prev-text="上一页"
            next-text="下一页"
            layout="sizes, prev, pager, next, jumper"
            :total="pageInfo.totalCount"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
          
      </div>
      <span slot="footer">
        <el-button
          size="mini"
          @click="closeDialog"
        >取 消</el-button>
        <el-button
          type="primary"
          size="mini"
          @click="confirmD"
        >确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { getAddresInfo, listPoiShop } from '../../../api/qual/index';
export default {
  name: 'SelectConnectShop',
  props: {
    basic: {
      type: Object,
      default: {},
    }
  },
  data() {
    return {
      dialogVisible: true,
      tableData: [],
      isLoading: false,
      selectedPoiId: '',
      selectedRow: {},
      provList: [],
      cityList: [],
      areaList: [],
      streeList: [],
      searchAddressInfo: {
        provinceId: '', // 省
        prov: '',
        cityId: '', // 市
        city: '',
        districtId: '', // 区
        area: '',
        streetId: '', // 街道
        streetName: '',
      },
      searchInfo: {
        poiCorpName: '',
        poiId: '',
      },
      pageInfo: {
        pageNum: 1,
        pageSize: 10,
        totalCount: 0,
      }
      
    };
  },
  created() {
    this.getAddres(0, 'provList');
    if (this.basic.poiCorpName) {
      this.searchInfo.poiCorpName = this.basic.poiCorpName;
    }
    if (this.basic.provinceId || this.basic.cityId || this.basic.districtId || this.basic.streetId) {
      this.searchAddressInfo.provinceId = Number(this.basic.provId) || '';
      this.searchAddressInfo.cityId = Number(this.basic.cityId) || '';
      this.searchAddressInfo.districtId = Number(this.basic.areaId) || '';
      this.searchAddressInfo.streetId = Number(this.basic.streetId) || '';
      this.searchAddressInfo.prov = this.basic.prov || '';
      this.searchAddressInfo.city = this.basic.city || '';
      this.searchAddressInfo.area = this.basic.area || '';
      this.searchAddressInfo.streetName = this.basic.streetName || '';


      this.searchAddressInfo.provinceId ? this.getAddres(this.searchAddressInfo.provinceId, 'cityList') : '';
      this.searchAddressInfo.cityId ? this.getAddres(this.searchAddressInfo.cityId, 'areaList') : '';
      this.searchAddressInfo.districtId ? this.getAddres(this.searchAddressInfo.districtId, 'streeList') : '';
    }
    this.getListPoiShop();
  },
  methods: {
    closeDialog() {
      this.$emit('update:selectShopVis', false);
    },
    getListPoiShop() {
      this.isLoading = true;
      listPoiShop({
        ...this.searchInfo,
        ...this.searchAddressInfo,
        pageNum: this.pageInfo.pageNum,
        pageSize: this.pageInfo.pageSize,
      }).then((res) => {
        this.isLoading = false;
        console.log('企业列表', res);
        this.tableData = res.data.list || [];
        this.pageInfo.totalCount = res.data.total || 0;
      }).catch(() => {
        this.isLoading = false;
      })
    },
    getAddres(code, list) {
      getAddresInfo({ parentCode: code }).then((res) => {
        if (res.code === 0) {
          this[list] = res.data;
        }
      });
    },
    selectRadioChange($event, row) {
			this.selectedPoiId = row.poiId;
			this.selectedRow = row;
    },
    selectChange(value, listStr) {
      const that = this;
      switch (listStr) {
        case 'cityList':
          this.cityList = [];
          this.areaList = [];
          this.streeList = [];
          this.searchAddressInfo.cityId = '';
          this.searchAddressInfo.districtId = '';
          this.searchAddressInfo.streetId = '';
          if (that.searchAddressInfo.provinceId) {
            this.provList.forEach((item) => {
              if (item.areaCode === that.searchAddressInfo.provinceId) {
                that.searchAddressInfo.prov = item.areaName;
              }
            });
          }
          break;
        case 'areaList':
          this.areaList = [];
          this.streeList = [];
          this.searchAddressInfo.districtId = '';
          this.searchAddressInfo.streetId = '';
          if (this.searchAddressInfo.cityId) {
            this.cityList.forEach((item) => {
              if (item.areaCode === that.searchAddressInfo.cityId) {
                that.searchAddressInfo.city = item.areaName;
              }
            });
          }
          break;
        case 'streeList':
          this.streeList = [];
          this.searchAddressInfo.streetId = '';
          if (this.searchAddressInfo.districtId) {
            this.areaList.forEach((item) => {
              if (item.areaCode === that.searchAddressInfo.districtId) {
                that.searchAddressInfo.area = item.areaName;
              }
            });
          }
          break;
        default:
          if (this.searchAddressInfo.streetId) {
            this.streeList.forEach((item) => {
              if (item.areaCode === that.searchAddressInfo.streetId) {
                that.searchAddressInfo.streetName = item.areaName;
              }
            });
          }
          break;
      }
      this.getAddres(value, listStr);
    },
    submitForm() {
      this.pageInfo.pageNum = 1;
      this.getListPoiShop();
    },
    handleSizeChange(val) {
      this.pageInfo.pageSize = val;
      this.getListPoiShop();
    },
    handleCurrentChange(val) {
      this.pageInfo.pageNum = val;
      this.getListPoiShop();
    },
    resetForm(formName) {
      this.$refs[formName].resetFields();
      this.pageInfo = {
        pageNum: 1,
        pageSize: 10,
        totalCount: 0,
      };
      Object.keys(this.searchAddressInfo).forEach((key) => {
        this.searchAddressInfo[key] = '';
      }, this);
      Object.keys(this.searchInfo).forEach((key) => {
        this.searchInfo[key] = '';
      }, this);
      this.submitForm();
      
    },
    confirmD() {
      if (!this.selectedPoiId) {
        this.$message.error('请选择关联企业');
        return;
      }
      this.$emit('selectConnectShop', this.selectedRow);
    },
  },
};
</script>

<style scoped lang="scss">
.width25 {
  width: 23%;
  display: inline-block;
  margin-right: 5px;
}

::v-deep  .el-form-item--small.el-form-item {
  margin-bottom: 10px;
}

::v-deep  .el-dialog__body{
  padding: 10px 20px;
}
::v-deep  .el-dialog__header{
  padding: 10px 20px;
  background: #f9f9f9;
}
::v-deep  .el-dialog__headerbtn{
  top: 15px;
}
::v-deep  .el-dialog__title{
  font-size: 16px;
}
::v-deep  .pagination-container{
  margin: 15px 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
::v-deep  .el-pagination .el-pagination__total {
  float: left;
}
</style>
