<template>
  <el-dialog
    v-loading="loading"
    :title="title"
    :visible="dialogVisible"
    width="700px"
    append-to-body
    :before-close="handleClose">
    <div class="contentBox">
      <el-form :model="formModel" ref="formModel" label-width="100px">
        <el-form-item
          prop="name"
          label="计划名称:"
          :rules="[
      { required: true, message: '计划名称不能为空'},
    ]"
        >
          <el-input style="width: 280px" class="small_30" v-model="formModel.name" placeholder="最多30个字"
                    maxLenght="30"></el-input>
        </el-form-item>
        <el-form-item
          prop="name"
          label="售卖时间:">
          <div class="timeSelectBox" v-for="(item,index) in formModel.time" :key="index">
            <span style="margin-right: 8px">{{ formatWeek(item.week) }}</span>
            <div class="timeSelect">
              <div style="margin-bottom: 8px" v-for=" (li,ind) in item.time" :key="ind">
                <el-select clearable v-model="li.startHour" placeholder="">
                  <el-option
                    v-for="item in hourOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value">
                  </el-option>
                </el-select>
                <span style="margin: 0 5px">:</span>
                <el-select clearable v-model="li.startMinute" placeholder="">
                  <el-option
                    v-for="item in startMinuteOptions"
                    :key="item"
                    :label="item"
                    :value="item">
                  </el-option>
                </el-select>
                <span style="margin: 0 8px">至</span>
                <el-select clearable v-model="li.endHour" placeholder="">
                  <el-option
                    v-for="item in hourOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value">
                  </el-option>
                </el-select>
                <span style="margin: 0 5px">:</span>
                <el-select clearable v-model="li.endMinute" placeholder="">
                  <el-option
                    v-for="item in endMinuteOptions"
                    :key="item"
                    :label="item"
                    :value="item">
                  </el-option>
                </el-select>
                <template v-if="ind===0&&item.time.length<3">
                  <i @click="addAndDelTime(index,ind)" class="icon el-icon-circle-plus-outline"></i>
                </template>
                <template v-else-if="ind!==0">
                  <i @click="addAndDelTime(index,ind)" class="icon el-icon-remove-outline"></i>
                </template>
              </div>
            </div>
          </div>
        </el-form-item>
      </el-form>
    </div>
    <span slot="footer" class="dialog-footer">
    <el-button type="primary" @click="save">保 存</el-button>
  </span>
  </el-dialog>
</template>

<script>
import {updateSaleTimeList, addSaleTimeList} from "@/api/product";

export default {
  name: "editTimePlan",
  props: {
    config: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      loading: false,
      isEdit: false,
      title: '新增售卖时间计划',
      dialogVisible: true,
      formModel: {
        name: '',
        time: [
          {
            week: 1,
            time: [
              {
                startHour: '',
                startMinute: '',
                endHour: '',
                endMinute: ''
              }
            ]
          },
          {
            week: 2,
            time: [
              {
                startHour: '',
                startMinute: '',
                endHour: '',
                endMinute: ''
              }
            ]
          },
          {
            week: 3,
            time: [
              {
                startHour: '',
                startMinute: '',
                endHour: '',
                endMinute: ''
              }
            ]
          },
          {
            week: 4,
            time: [
              {
                startHour: '',
                startMinute: '',
                endHour: '',
                endMinute: ''
              }
            ]
          },
          {
            week: 5,
            time: [
              {
                startHour: '',
                startMinute: '',
                endHour: '',
                endMinute: ''
              }
            ]
          },
          {
            week: 6,
            time: [
              {
                startHour: '',
                startMinute: '',
                endHour: '',
                endMinute: ''
              }
            ]
          },
          {
            week: 7,
            time: [
              {
                startHour: '',
                startMinute: '',
                endHour: '',
                endMinute: ''
              }
            ]
          }
        ],
      },
      hourOptions: [],
      startMinuteOptions: ['00', '05', '10', '15', '20', '25', '30', '35', '40', '45', '50', '55'],
      endMinuteOptions: ['00', '05', '10', '15', '20', '25', '30', '35', '40', '45', '50', '55', '59'],
    }
  },
  created() {
    for (let i = 0; i < 24; i++) {
      this.hourOptions.push({label: i < 10 ? '0' + i : String(i), value: i < 10 ? '0' + i : String(i)})
    }
    if (Object.keys(this.config).length > 0) {
      this.isEdit = true
      this.loading = true
      this.title = '编辑售卖时间计划'
      const {name, time} = this.config
      this.$set(this.formModel, 'name', name)

      const list = this.formModel.time.map(item => {
        const obj = time.find(li => li.week === item.week)
        return{
          ...item,
          ...obj
        }
      })
      console.log(list)
      this.$set(this.formModel, 'time', list)
      this.loading = false
    }
  },
  methods: {
    formatWeek(s) {
      const num = Number(s)
      let str = ''
      switch (num) {
        case 1:
          str = '周一'
          break
        case 2:
          str = '周二'
          break
        case 3:
          str = '周三'
          break
        case 4:
          str = '周四'
          break
        case 5:
          str = '周五'
          break
        case 6:
          str = '周六'
          break
        case 7:
          str = '周日'
          break
      }
      return str
    },
    handleClose() {
      this.$emit('update:editTimePlanDialogVisible', false)
    },
    addAndDelTime(index, ind) {
      if (ind === 0) {
        //增加
        this.formModel.time[index].time.push({
          startHour: '',
          startMinute: '',
          endHour: '',
          endMinute: ''
        })
      } else {
        this.formModel.time[index].time.splice(ind, 1)
      }
    },
    save() {
      this.$refs.formModel.validate(async (valid) => {
        if (valid) {
          const params = {...this.formModel}
          let res = null
          if (this.isEdit) {
            params.id = this.config.id
            res = await updateSaleTimeList(params)
          } else {
            res = await addSaleTimeList(params)
          }
          if (res && res.code === 0) {
            this.$message.success('保存成功！');
            this.$emit('updateList')
            this.handleClose()
          } else {
            this.$message.error(res.message || '保存失败！')
          }
        } else {
          console.log('error submit!!');
          return false;
        }
      });
    }
  }
}
</script>

<style scoped lang="scss">
.el-button {
  padding: 8px 20px;
}

::v-deep   .contentBox {
  .small_30 {
    .el-input__inner {
      width: 100% !important;
    }
  }

  .timeSelectBox {
    display: flex;
    align-items: baseline;
    justify-content: left;
  }

  .icon {
    vertical-align: middle;
    font-size: 25px;
    line-height: 30px;
    margin-left: 12px;
    cursor: pointer;
  }

  .timeSelect, .small_30 {
    display: inline-block;

    .el-input__inner {
      width: 80px;
      height: 30px;
      line-height: 30px;
    }

    .el-input__icon {
      line-height: 30px;
    }
  }
}
</style>
