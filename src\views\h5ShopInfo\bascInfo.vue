<template>
  <div class="baseInfoBox">
    <div v-for="(item, index) in bussinessList" :key="item.orgId" class="bussinessItem">
      <div class="baseBox">
        <div class="nameTop">
          <div class="nameBox">
            <img class="shopNameIcon" src="../../assets/h5/shopIcon.png" alt="">
            <span class="nameTitle">{{ item.companyName }}</span>
          </div>
          <div class="btnBox" @click="showOrHide(index)">
            <span class="btn">{{ item.showMore ? '收起' : '展开' }}</span>
            <img class="btnIcon" :class="!item.showMore ? 'btnIcon2' : ''" src="../../assets/h5/more.png" alt="">
          </div>
        </div>
        <div class="commonTop">企业编码：{{ item.orgId }}</div>
        <div class="commonTop">店铺名称：{{ item.name }}</div>
      </div>
      <div class="detailInfo" v-show="item.showMore">
        <div v-for="commonItem in item.commonList" :key="commonItem.title" class="titleItemBox">
          <div class="itemTitle">
            <div class="leftBorder"></div>
            {{ commonItem.title }}
          </div>
          <div class="itemContentBox" v-for="tableItem in commonItem.list" :key="tableItem.key">
            <div class="contentItem">
              <div class="labelText">{{ tableItem.label }}</div>
              <div class="valueText">{{ tableItem.value || '-' }}</div>
            </div>
          </div>
        </div>
        <div v-for="(addressItem, index) in item.returnAddressList" :key="index" class="titleItemBox">
          <div class="itemTitle" v-if="index === 0">
            <div class="leftBorder"></div>
            店铺退货地址
          </div>
          <div class="itemContentBox">
            <div class="contentItem">
              <div class="labelText">生效省份</div>
              <div class="valueText">{{ addressItem.provinceNames }}</div>
            </div>
            </div>
          <div class="itemContentBox">
            <div class="contentItem">
              <div class="labelText">收货人</div>
              <div class="valueText">{{ addressItem.contactor }}</div>
            </div>
          </div>
          <div class="itemContentBox">
            <div class="contentItem">
              <div class="labelText">联系电话</div>
              <div class="valueText">{{ addressItem.contactorMobile }}</div>
            </div>
          </div>
          <div class="itemContentBox">
            <div class="contentItem">
              <div class="labelText">收货地址</div>
              <div class="valueText">{{ addressItem.address }}</div>
            </div>
          </div>
          <div class="itemContentBox">
            <div class="contentItem">
              <div class="labelText">快递说明</div>
              <div class="valueText">{{ addressItem.remark }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { listCorpBaseInfoByPoiId } from '../../api/storeManagement/index';
export default {
  name: 'BascInfo',
  data() {
    return {
      bussinessList: [],
    };
  },
  created() {
    this.getBascInfo();
  },
  methods: {
    getBascInfo() {
      const poiId = this.util.getUrlParam('poiId');
      listCorpBaseInfoByPoiId({ poiId }).then((res) => {
        // this.bussinessList = res.data || [];
        (res.data || []).forEach((item, index) => {
          let obj = {
            showMore: index === 0 ? true : false,
            companyName: item.companyName,
            orgId: item.orgId,
            name: item.name,
            returnAddressList: item.returnAddressList || [],
            commonList: [],
          };
          let baseInfo = {
            title: '基础信息',
            list: [{
              key: 'shopCode',
              label: '店铺编码',
              value: item.shopCode,
            }, {
              key: 'name',
              label: '店铺名称',
              value: item.name,
            }, {
              key: 'shopStatusName',
              label: '店铺状态',
              value: item.shopStatusName,
            }, {
              key: 'regMobile',
              label: '注册手机号',
              value: item.regMobile,
            }, {
              key: 'regTime',
              label: '注册时间',
              value: item.regTime,
            }, {
              key: 'customerServicePhone',
              label: '客服电话',
              value: item.customerServicePhone,
            }]
          };
          let cooperationInfo = {
            title: '合作信息',
            list: [{
              key: 'saleProvs',
              label: '店铺售卖省份',
              value: item.saleProvs,
            }, {
              key: 'bondMoney',
              label: '保证金金额',
              value: item.bondMoney,
            }, {
              key: 'settlementTypeName',
              label: '佣金结算方式',
              value: item.settlementTypeName,
            }, {
              key: 'transferToName',
              label: '线下转账收款方',
              value: item.transferToName,
            }, {
              key: 'invoiceTypeName',
              label: '佣金发票类型',
              value: item.invoiceTypeName,
            }]
          };
          let shopNotice = {
            title: '店铺公告',
            list: [{
              key: 'expressType',
              label: '快递类型',
              value: item.expressType,
            }, {
              key: 'deliveryProvinceName',
              label: '发货省份',
              value: item.deliveryProvinceName,
            }, {
              key: 'orderHandleTime',
              label: '订单处理时间',
              value: item.orderHandleTime,
            }, {
              key: 'deliveryHandleTime',
              label: '发货时间',
              value: item.deliveryHandleTime,
            }, {
              key: 'content',
              label: '其他',
              value: item.content,
            }]
          };
          obj.commonList.push(baseInfo, cooperationInfo, shopNotice);
          this.bussinessList.push(obj);
        })
      })
    },
    showOrHide(actIndex) {
      this.bussinessList.forEach((item, index) => {
        if (index === actIndex) {
          item.showMore = !item.showMore;
        } else {
          item.showMore = false;
        }
      })
    }
  },
};
</script>

<style scoped lang="scss">
  .baseInfoBox {
    margin: 0 0.2rem;
    .bussinessItem {
      background: #fff;
      border-radius: 0.2rem;
      padding: 0.2rem;
      margin-bottom: 0.2rem;
      .baseBox {
        .nameTop {
          display: flex;
          align-items: center;
          justify-content: space-between;
          .nameBox, .btnBox {
            display: flex;
            align-items: center;
          }
          .shopNameIcon {
            width: 0.4rem;
            height: 0.4rem;
            margin-right: 4px;
          }
          .nameTitle {
            color: #333333;
            font-weight: bold;
            font-size: 0.28rem;
          }
          .btn {
            color: #9494A6;
            font-size: 0.28rem;
            margin-right: 4px;
          }
          .btnIcon {
            width: 0.26rem;
            height: 0.26rem;
          }
          .btnIcon2 {
            transform: rotate(180deg);
          }
        }
        .commonTop {
          color: #292933;
          font-size: 0.2rem;
          margin-top: 0.1rem;
        }
      }
      .detailInfo {
        background: #fff;
        border-top: 1px solid #EEEFF0;
        margin-top: 0.2rem;
        .titleItemBox {
          margin-top: 0.2rem;
          .itemTitle {
            display: flex;
            align-items: center;
            color: #292933;
            margin-bottom: 0.2rem;
            font-weight: bold;
            .leftBorder {
              width: 0.06rem;
              height: 0.4rem;
              background: #00B377;
              border-radius: 3.5px;
              margin-right: 0.1rem;
            }
          }
          .itemContentBox {
            border: 1px solid #EEEFF0;
            border-bottom: none;
            // padding: 0.2rem;
            .contentItem {
              display: flex;
              // align-items: center;
              // border-radius: 6px;
              .labelText {
                background: #F1F6F9;
                color: #676773;
                width: 2.29rem;
                padding: 0.2rem;
                text-align: center;
              }
              .valueText {
                width: calc(100% - 2.29rem);
                background: #fff;
                color: #292933;
                padding: 0.2rem;
              }
            }
          }
          .itemContentBox:nth-last-child(1) {
            border-bottom: 1px solid #EEEFF0;
          }
        }
      }
    }
  }
</style>
