import request from '../index'

/**
 * 催发货数量统计
 * @param {参数} params
 */
export function getUrgeDeliveryCount() {
    return request.get('/reminder/count');
}
/**
 * 催发货查询列表
 * @param {参数} params
 */
export function getUrgeDeliveryList(params) {
    return request.post('/reminder/queryPage',params);
}
/**
 * 催发货发起申诉
 * @param {参数} params
 */
export function submitAppeal(params) {
    return request.post('/reminder/appeal',params,{headers: { 'Content-Type': 'application/json' }});
}
/**
 * 催发货异步导出
 * @param {参数} params
 */
export function exportReminder(params) {
    return request.post('/reminder/export',params,{headers: { 'Content-Type': 'application/json' }});
}
/**
 * 催发货查询列表待处理/申诉中计数
 * @param {参数} params
 */
export function getCountStatus(params) {
    return request.post('/reminder/countStatus',params);
}
/**
 * 催发货查看历史
 * @param {参数} params
 */
export function getHistroy(params) {
    return request.get('/reminder/history',params);
}
/* 上传文件 */
export function uploadFile(params) {
    const forms = new FormData();
    forms.append('file', params.file);
    return request.post("/uploadFile/uploadFDFS",forms,{
      headers: { 'Content-Type': 'multipart/form-data' },
      onUploadProgress: (progressEvent) => {
        const num = (progressEvent.loaded / progressEvent.total) * 100; // 百分比
        params.onProgress({ percent: num }); // 进度条
      }
    })
}
export function getHostName(params) {
    return request.post("/uploadFile/cdn/hostName",params);
}
/**
 * 申诉类别接口
 * @param {参数} params
 */
export function getAppealType() {
    return request.get('/reminder/category');
}
// 催发货状态类别接口
export function getStatusType() {
    return request.get('/reminder/eventStatus');
}