import request from '@/utils/request';

// 退款列表
export function getListAfterSale(params) {
  return request({
    url: '/afterSales/v2/list',
    method: 'get',
    params,
  });
}

// 退款状态数
export function afterSaleStatusCount(params) {
  return request({
    url: '/afterSales/v2/loadStatusCount',
    method: 'get',
    params,
  });
}
// 查询待处理退款单数
export function getRefundCount(params) {
  return request({
    url: '/afterSales/queryPendingRefundCount',
    method: 'get',
    params,
  });
}
// 查询全部退款原因
export function getListAllRefundReason(params) {
  return request({
    url: '/afterSales/v2/listAllRefundReason',
    method: 'get',
    params,
  });
}
// 查询开通域的省份
export function getListProvinceList(params) {
  return request({
    url: '/branch/queryOpenProvinceList',
    method: 'get',
    params,
  });
}
// 查询退款审核日志
export function getListRefundAuditLogs(params) {
  return request({
    url: '/afterSales/v2/queryRefundAuditLogs',
    method: 'get',
    params,
  });
}
// 退款详情
export function getListRefundDetail(params) {
  return request({
    url: '/afterSales/v2/refundOrderDetail',
    method: 'get',
    params,
  });
}
// 退款审核通过
export function updateRefundStatus(params) {
  return request({
    url: '/afterSales/v2/auditRefund',
    method: 'post',
    data: params,
  });
}
// 入库审核通过
export function updateAgreeStockInStatus(params) {
  return request({
    url: '/afterSales/v2/agreeStockIn',
    method: 'post',
    data: params,
  });
}
// 查询退款驳回原因
export function RefundRejectionReason(params) {
  return request({
    url: '/afterSales/v2/queryRefundOrderRefuseReason',
    method: 'get',
    params,
  });
}
// 确认退款和确认已打款、退款驳回
export function confirmAndRejectRefund(params) {
  return request({
    url: '/afterSales/v2/auditRefundByNo',
    method: 'post',
    data: params,
  });
}
export function confirmAndRejectRefunds(params) {
  return request({
    url: '/afterSales/v2/refund',
    method: 'post',
    data: params,
  });
}

// 退款列表导出
export function refundListExport(params) {
  return request({
    url: '/afterSales/async/exportRefundOrder',
    method: 'get',
    params,
  });
}
// 退款列表明细导出
export function refundListDetailsExport(params) {
  return request({
    url: '/afterSales/async/exportRefundOrderDetail',
    method: 'get',
    params,
  });
}
// 卖家中心-售后管理-查询物流信息
export function apiGetRefundExpress(params) {
  return request({
    url: '/afterSales/getRefundExpress',
    method: 'get',
    params,
  });
}
/**
 * 商品改版商品配置信息
 */
export function apiConfig(params) {
  return request.get('/product/config', params);
}

// 退款类型列表
export function getRefundModelList() {
  return request({
    url: '/afterSales/refundModel',
    method: 'get',
  });
}

export function addOrderRefundExpress(params) {
  return request.post('/afterSales/saveOrderRefundExpress', params);
} // 新增快递信息

export function uploadFile(params) {
  const forms = new FormData();
  forms.append('file', params.file);
  return request({
    url: '/uploadFile/uploadFDFS',
    method: 'post',
    data: forms,
    headers: { 'Content-Type': 'multipart/form-data' },
    onUploadProgress: (progressEvent) => {
      const num = (progressEvent.loaded / progressEvent.total) * 100; // 百分比
      params.onProgress({ percent: num }); // 进度条
    },
  });
} // 上传文件

export function SmallPayments(params) {
  return request.post('/afterSales/v2/auditRefundBySmallPayments', params);
} // 退款审核-小额打款

export function IndemnityMoney(params) {
  return request.post('/afterSales/v2/auditRefundByIndemnityMoney', params);
} // 退款审核-额外赔偿

export function editSellerRemark(params) {
  // return request.post('/afterSales/editSellerRemark', params, { 'Content-Type': 'application/x-www-form-urlencoded' },);
  return request({
    url: '/afterSales/editSellerRemark',
    method: 'post',
    params,
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
  });
} // 编辑商家备注
