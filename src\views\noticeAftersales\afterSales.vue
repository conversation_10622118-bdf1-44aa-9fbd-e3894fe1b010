<template>
  <div>
    <div class="serch">
      <el-row
        type="flex"
        align="middle"
      >
        <span class="sign" />
        <div>退货须知</div>
      </el-row>
    </div>
    <div class="boxDiv">
      <p class="label">退货须知内容：</p>
      <quillEditor
        :content-f="afterContent"
        :from-editor="2"
        :content-id="afterId"
      />
    </div>
    <div class="boxDiv">
      <p class="label">展示示意图：</p>
      <div class="imgBox">
        <img
          src="@/assets/image/product/shouhou.png"
          alt=""
        >
      </div>
    </div>
  </div>
</template>

<script>
import { queryReturnNotice } from '@/api/other/index';
import quillEditor from './component/afterSalesQuillEditor';

export default {
  name: 'Aftersales',
  components: { quillEditor },
  data() {
    return { afterContent: '', afterId: null };
  },
  created() {
    queryReturnNotice().then((res) => {
      if (res.code === 0) {
        this.afterContent = res.result ? res.result.content : '';
        this.afterId = res.result ? res.result.id : null;
      } else {
        this.$message.error(res.msg);
      }
    });
  },
};
</script>

<style scoped lang="scss">
.serch {
  font-weight: bold;
  padding: 15px 20px 0;
  .sign {
    display: inline-table;
    width: 3px;
    height: 13px;
    background: linear-gradient(360deg, #1d69c4 1%, #8bbdfc 99%);
    border-radius: 2px;
    margin-right: 8px;
  }
}
.boxDiv{
  display: flex;
  justify-content: left;
  p{
    width: 100px;
    margin: 0;
    padding: 0;
    padding-top: 20px;
    padding-left: 20px;
  }
  .imgBox{
    width: 260px;
    border: 1px solid rgba(0,0,0,0.15);
    border-radius: 4px;
    padding: 10px;
    img{
      width: 100%;
    }
  }
}
</style>
