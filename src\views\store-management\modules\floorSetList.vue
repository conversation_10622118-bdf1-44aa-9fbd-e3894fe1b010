<template>
  <div>
    <div class="main-box">
      <!--      <div class="con-title explain-search">-->
      <!--        <el-row type="primary">-->
      <!--          <span class="line"></span>-->
      <!--          <span>查询条件</span>-->
      <!--          <div class="search-btn">-->

      <!--          </div>-->
      <!--        </el-row>-->
      <!--      </div>-->
      <div class="Fsearch">
        <el-row
          type="flex"
          align="middle"
          justify="space-between"
          class="my-row"
        >
          <el-row
            type="flex"
            align="middle"
          >
            <span class="sign" />
            <div class="searchMsg">
              查询条件
            </div>
          </el-row>
        </el-row>
      </div>
      <div
        class="explain-search searchMy"
        style="padding: 0 20px 10px 20px;border-bottom: 1px solid #f0f2f5;"
      >
        <el-form
          ref="ruleForm"
          size="small"
          :inline="true"
        >
          <el-form-item>
            <span
              class="search-title"
            >区域</span>
            <el-select
              v-model.trim="searchData.branchCode"
              placeholder="请选择"
              clearable
            >
              <el-option
                v-for="(item, index) in regionasList"
                :key="index"
                :label="item.branchName"
                :value="item.branchCode"
              />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-input
              v-model.trim="searchData.floorName"
              clearable
            >
              <template slot="prepend">
                楼层名称
              </template>
            </el-input>
          </el-form-item>
          <el-form-item>
            <span
              class="search-title"
            >楼层状态</span>
            <el-select
              v-model.trim="searchData.status"
              placeholder="请选择"
              clearable
            >
              <el-option
                label="全部"
                value
              />
              <el-option
                label="启用"
                :value="1"
              />
              <el-option
                label="停用"
                :value="2"
              />
            </el-select>
          </el-form-item>
        </el-form>
        <el-row style="text-align: right;padding-bottom: 10px">
          <el-button
            size="small"
            @click="resetForm('ruleForm')"
          >
            重置
          </el-button>
          <el-button
            size="small"
            type="primary"
            @click="getList('search')"
          >
            查询
          </el-button>
        </el-row>
      </div>
      <div class="list-box">
        <div
          class="con-title explain-search"
          style="padding: 5px 15px 0"
        >
          <el-row
            type="flex"
            align="middle"
            justify="space-between"
          >
            <el-row
              type="flex"
              align="middle"
            >
              <span class="sign" />
              <div class="searchMsg">
                楼层基本信息
                <el-tooltip
                  class="item"
                  effect="dark"
                  placement="top"
                >
                  <template #content>
                    <br>1、可以设置全国所有区域通用的楼层，也可以设置单独区域的
                    <br>2、药帮忙平台客户可见全国+当前区域的楼层，必须为启用状态
                    <br>3、按照排序号正序排列，如多个楼层排序号一致，则按照创建时间正序排列
                  </template>
                  <p class="span-tip">
                    ?
                  </p>
                </el-tooltip>
              </div>
            </el-row>
            <el-button
              v-permission="['shop_floor_add']"
              class="xyy-blue"
              type="primary"
              size="small"
              @click="addFloor"
            >
              新增楼层
            </el-button>
          </el-row>
        </div>
        <div style="padding: 10px 20px">
          <div class="customer-tabs">
            <el-table
              ref="goodTable"
              v-loading="laodingBoole"
              :data="tableData.list"
              stripe
              style="width: 100%"
              :header-cell-style="{ background: '#F9F9F9', color: '#666666' }"
            >
              <el-table-column
                prop="branchNames"
                label="区域"
                width="180"
                show-overflow-tooltip
              />
              <el-table-column
                prop="floorName"
                label="楼层名称"
                width="120"
              />
              <el-table-column
                prop="floorDescribe"
                label="楼层描述"
                width="120"
              />
              <el-table-column label="状态">
                <template slot-scope="scope">
                  <span v-if="scope.row.status == 1">启用</span>
                  <span v-else>停用</span>
                </template>
              </el-table-column>
              <el-table-column
                prop="sort"
                label="排序"
              />
              <el-table-column
                prop="updateTime"
                label="操作时间"
                width="160"
              />
              <el-table-column
                prop="updateBy"
                label="操作人"
                width="120"
              />
              <el-table-column
                fixed="right"
                label="操作"
                width="160"
                class-name="operation-box"
              >
                <template slot-scope="scope">
                  <el-button
                    v-permission="['shop_floor_configProduct']"
                    type="text"
                    size="small"
                    @click="configuration(scope.row)"
                  >
                    配置商品
                  </el-button>
                  <el-button
                    v-permission="['shop_floor_edit']"
                    type="text"
                    size="small"
                    @click="edit(scope.row)"
                  >
                    编辑
                  </el-button>
                  <el-button
                    v-permission="['shop_floor_enable']"
                    v-if="scope.row.status == 2"
                    type="text"
                    size="small"
                    @click="enable(scope.row)"
                  >
                    启用
                  </el-button>
                  <el-button
                    v-permission="['shop_floor_disable']"
                    v-else
                    type="text"
                    size="small"
                    @click="disable(scope.row)"
                  >
                    停用
                  </el-button>
                </template>
              </el-table-column>
              <template slot="empty">
                <div class="noData">
                  <p class="img-box">
                    <img
                      src="@/assets/image/marketing/noneImg.png"
                      alt
                    >
                  </p>
                  <p>暂无数据</p>
                </div>
              </template>
            </el-table>
            <div class="explain-pag">
              <Pagination
                v-show="tableData.total > 0"
                :total="tableData.total"
                :page.sync="pageData.page"
                :limit.sync="pageData.rows"
                @pagination="getList"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
    <el-dialog
      :title="floorTitle"
      :visible.sync="changeDialog"
      class="my-dialog"
      :modal-append-to-body="false"
      :close-on-click-modal="false"
      width="55%"
    >
      <div class="explain-table dialogBox">
        <div style="max-height: 260px;overflow-y: auto;margin-top:10px">
          <el-form
            ref="form"
            size="small"
            :inline="true"
            :model="form"
            :rules="rules"
          >
            <el-form-item
              class="my-label2"
              label="区域:"
              label-width="100px"
              prop="branchCodes"
            >
              <el-select
                v-model.trim="form.branchCodes"
                placeholder="请选择"
                class="my-lable2-item"
                multiple
                clearable
                @change="changeSelect"
              >
                <el-option
                  v-for="(item, index) in regionasList"
                  :key="index"
                  :label="item.branchName"
                  :value="item.branchCode"
                />
              </el-select>
            </el-form-item>
            <el-form-item
              class="my-label2"
              label="楼层名称:"
              prop="floorName"
              label-width="100px"
            >
              <el-input
                v-model.trim="form.floorName"
                class="my-lable2-item"
                :maxlength="5"
                clearable
              />
              <span class="my-lable2-info">最多输入5个字符</span>
            </el-form-item>
            <el-form-item
              class="my-label2"
              label="楼层描述:"
              label-width="100px"
            >
              <el-input
                v-model.trim="form.floorDescribe"
                class="my-lable2-item"
                :maxlength="20"
                clearable
              />
              <span class="my-lable2-info">最多输入20个汉字</span>
            </el-form-item>
            <el-form-item
              class="my-label2"
              label="楼层排序:"
              prop="sort"
              label-width="100px"
            >
              <div style="margin-top:5px">
                <el-input
                  v-model.trim="form.sort"
                  class="my-lable2-item"
                  clearable
                />
                <span class="my-lable2-info">只允许输入数字</span>
              </div>
              <span
                class="my-lable2-info"
                style="color: #F56C6C; margin-left: 0; font-family: PingFangSC, PingFangSC-Medium;"
              >数字需为>=0的整数，数字越小排序越靠前</span>
            </el-form-item>
            <el-form-item
              class="my-label2"
              label="状态:"
              label-width="100px"
            >
              <el-radio-group v-model="form.status">
                <el-radio :label="1">
                  启用
                </el-radio>
                <el-radio :label="2">
                  停用
                </el-radio>
              </el-radio-group>
            </el-form-item>
          </el-form>
        </div>
      </div>
      <div
        slot="footer"
        class="dialog-footer"
      >
        <el-button
          size="small"
          @click="cancelDialog"
        >
          取 消
        </el-button>
        <el-button
          type="primary"
          class="xyy-blue"
          size="small"
          @click="determineDialog('form')"
        >
          确定
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>
<style>
@import '../../../assets/css/changeElement.scss';
</style>
<script>
import Pagination from '@/components/Pagination';
import utils from '@/utils/filter';
import info from '@/components/info/info';
import {
  branchsList,
  getFloorList,
  floorNameValidation,
  enableFloor,
  stopUsingFloor,
  editorFloor,
  addFloor,
} from '@/api/storeManagement/index';

export default {
  name: 'FloorSetList',
  components: { Pagination, info },
  data() {
    return {
      searchData: {
        branchCode: '', // 区域
        floorName: '', // 展示名称
        status: '', // 状态
        floorType: '', // 楼层类型
        sort: 'asc',
      },
      form: {
        branchCodes: [],
        floorName: '',
        floorDescribe: '',
        sort: '',
        status: 1,
      },
      tableData: {
        total: 0,
        list: [],
      },
      pageData: {
        rows: 10,
        page: 1,
      },
      laodingBoole: false,
      checkListAry: [],
      detailData: {},
      titleInfo: '楼层设置',
      changeDialog: false,
      regionasList: [],
      rules: {
        branchCodes: [
          { required: true, message: '区域为必填项', trigger: 'blur' },
        ],
        sort: [
          { required: true, message: '楼层排序为必填项', trigger: 'blur' },
          { pattern: /^[0-9]*$/, message: '只允许输入整数', trigger: 'blur' },
        ],
        floorName: [
          { required: true, message: '楼层名称为必填项', trigger: 'blur' },
          { pattern: /^[0-9\u4e00-\u9fa5]+$/, message: '只允许输入数字和文字', trigger: 'blur' },
        ],
      },
      floorId: '',
      editOrAdd: false,
      floorTitle: '新增楼层',
    };
  },
  created() {
    this.queryRegional();
    // 获取列表数据
    this.getList();
  },
  methods: {
    infodata() {
      return [
        { title: '可以设置全国所有区域通用的楼层，也可以设置单独区域的' },
        { title: '药帮忙平台客户可见全国+当前区域的楼层，必须为启用状态' },
        { title: '按照排序号正序排列，如多个楼层排序号一致，则按照创建时间正序排列' },
      ];
    },
    // 获取列表数据
    getList(from) {
      const that = this;
      if (from == 'search') {
        this.pageData.page = 1;
      }
      this.laodingBoole = true;
      const param = {
        ...this.searchData,
        ...this.pageData,
      };
      getFloorList(param).then((res) => {
        if (res.code == 0) {
          this.laodingBoole = false;
          if (res.result) {
            that.tableData.list = res.result.list;
            let updateTime = {};
            let branchNames = {};
            this.tableData.list.forEach((item, index) => {
              updateTime = {};
              branchNames = {};
              if (item.updateTime == null) {
                updateTime = '-';
              } else {
                updateTime = utils.dataTime(
                  item.updateTime,
                  'yy-mm-dd HH:ss:nn',
                );
              }
              if (item.branchNames && item.branchNames.length > 0) {
                branchNames = item.branchNames.join(',');
              }
              this.tableData.list[index] = Object.assign({}, this.tableData.list[index], {
                updateTime,
                branchNames,
              });
            });
          } else {
            that.tableData.list = [];
          }
          that.tableData.total = res.result.total;
        } else {
          this.laodingBoole = false;
          this.$message({
            message: res.msg,
            type: 'error',
          });
        }
      }).catch(() => {});
    },
    queryRegional() {
      branchsList().then((res) => {
        this.regionasList = res.result;
      });
    },
    changeSelect(val) {
      if (val.length > 1 && val.indexOf('XS000000') > -1) {
        const last = val.length - 1;
        if (val[last] === 'XS000000') {
          this.form.branchCodes = ['XS000000'];
        } else {
          const index = val.indexOf('XS000000');
          this.form.branchCodes.splice(index, 1);
        }
      }
    },
    // 配置商品
    configuration(val) {
      this.detailData = {
        floorName: val.floorName,
        floorDescribe: val.floorDescribe,
        areaName: val.branchNames,
        floorId: val.floorId,
      };
      this.$emit('goNext', { from: 'goodsList', data: this.detailData });
    },
    // 添加楼层
    addFloor() {
      this.form = {
        branchCodes: [],
        floorName: '',
        floorDescribe: '',
        sort: '',
        status: 1,
      };
      this.floorId = '';
      this.floorTitle = '新增楼层';
      this.editOrAdd = false;
      this.changeDialog = true;
    },
    // 关闭弹窗
    cancelDialog() {
      this.changeDialog = false;
    },
    // 编辑
    edit(val) {
      this.form = {
        branchCodes: val.branchCodes,
        floorName: val.floorName,
        floorDescribe: val.floorDescribe,
        sort: val.sort,
        status: val.status,
      };
      this.floorId = val.floorId;
      this.floorTitle = '编辑楼层';
      this.editOrAdd = true;
      this.changeDialog = true;
    },
    // 点击确定添加楼层
    determineDialog(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          const that = this;
          const param = { ...this.form };
          param.branchCodes = this.form.branchCodes.join(',');

          if (this.editOrAdd) {
            if (this.floorId) {
              param.floorId = this.floorId;
            }
            editorFloor(param).then((res) => {
              if (res.code == 0) {
                this.getList();
              } else {
                this.$message({
                  message: res.msg,
                  type: 'error',
                });
              }
            }).catch(() => {});
          } else {
            addFloor(param).then((res) => {
              if (res.code == 0) {
                this.getList();
              } else {
                this.$message({
                  message: res.msg,
                  type: 'error',
                });
              }
            }).catch(() => {});
          }
          this.changeDialog = false;
        } else {
          return false;
        }
      });
    },
    // 启用
    enable(val) {
      enableFloor({ id: val.floorId }).then((res) => {
        if (res.code == 0) {
          this.getList();
        } else {
          this.$message({
            message: res.msg,
            type: 'error',
          });
        }
      }).catch(() => {});
    },
    // 停用
    disable(val) {
      stopUsingFloor({ id: val.floorId }).then((res) => {
        if (res.code == 0) {
          this.getList();
        } else {
          this.$message({
            message: res.msg,
            type: 'error',
          });
        }
      }).catch(() => {});
    },
    // 重置列表数据
    resetForm() {
      this.searchData = {
        branchCode: '', // 区域
        floorName: '', // 展示名称
        status: '', // 状态
        floorType: '', // 楼层类型
        sort: 'asc',
      };
      this.pageData = {
        rows: 10,
        page: 1,
      };
      this.getList();
    },
  },
};
</script>

<style lang="scss" scoped>

.con-inner {
  padding-top: 15px;
  padding-left: 23px;
  margin-right: 17px;
  padding-bottom: 10px;
  border-bottom: 1px solid #efefef;
  div {
    display: inline-block;
  }
  .img {
    width: 92px;
    height: 92px;
    img {
      width: 100%;
      height: 100%;
    }
  }
  .text {
    padding-left: 20px;
    vertical-align: top;
    h3 {
      font-size: 14px;
      color: #000000;
      padding: 0;
      margin: 0;
    }
    p {
      padding: 0;
      margin: 0;
      font-size: 12px;
      color: #333333;
      padding-top: 10px;
    }
  }
  .btn {
    float: right;
    padding-top: 26px;
    button {
      width: 100px;
      height: 30px;
      text-align: center;
      line-height: 30px;
      padding: 0;
      background: rgba(65, 131, 213, 1);
      border-color: rgba(65, 131, 213, 1);
      border-radius: 4px;
      font-size: 14px;
    }
    a {
      color: #ffffff;
      text-decoration: none;
    }
    .router-link-active {
      color: #ffffff;
      text-decoration: none;
    }
  }
}
.pag-info {
  width: 500px;
}
.main-box {
  .list-box {
    .customer-tabs {
      .el-button + .el-button {
        margin-left: 0px;
      }
    }
  }
}
.search-btn {
  float: right;
}
.imgInfo {
  width: 80px;
  height: 80px;
  margin-right: 16px;
}
.operation-box {
  width: auto;
  white-space: nowrap;
  letter-spacing: 0;
  margin: 0 -10px;
  .el-button {
    position: relative;
    margin: 0 10px;
  }
  .el-button::before {
    position: absolute;
    // top: 14px;
    right: -6px;
    content: '';
    display: block;
    width: 1px;
    height: 12px;
    background: #dcdfe6;
  }
  .el-button:first-child {
    margin-left: 0;
  }
  .el-button:last-child {
    margin-right: 0;
  }
  .el-button:last-child::before {
    display: none;
  }
}

::v-deep  .my-dialog .user {
  .el-tree-node__content > .el-tree-node__expand-icon {
    padding: 0px;
    font-size: 0;
  }
}
::v-deep  .explain-table {
  padding: 0 6px 0;
}
.span-tip{
  display: inline-block;
  width: 20px;
  height: 20px;
  font-size: 14px;
  border: 1px solid #4183d5;
  color: #4183d5;
  text-align: center;
  line-height: 20px;
  border-radius: 50%;
  margin-left: 5px;
}
.searchMy ::v-deep  .el-form-item__label {
  margin-left: 20px;
  padding: 0;
}
.searchMy ::v-deep  .el-input__inner {
  border-radius: 0 4px 4px 0;
}
.searchMy ::v-deep  .el-date-editor{
  width: 100%;
}
.search-title {
  display: table-cell;
  padding: 0 10px;
  text-align: center;
  border: 1px solid #dcdfe6;
  height: 30px;
  line-height: 30px;
  vertical-align: middle;
  border-right: none;
  border-radius: 4px 0 0 4px;
  color: #333333;
  white-space: nowrap;
}
.searchMy ::v-deep  .el-select {
  display: table-cell;
  width: 100%;
}
.searchMy ::v-deep  .el-form-item__content{
  width: 100%;
}
.searchMy ::v-deep  .el-form-item--small.el-form-item{
  margin-bottom: 10px;
  width: 32%;
}
.searchMy ::v-deep  .el-form-item--small .el-form-item__content{
  line-height: 30px;
  width: 100%;
}
.searchMy ::v-deep  .el-input-group__prepend {
  background: none;
  color: #333333;
  padding: 0 10px;
}
.Fsearch {
  padding: 0px 20px 10px;
  font-weight: 500;
  .searchMsg {
    font-weight: 700;
    width: 200px;
  }
  .sign {
    display: inline-table;
    width: 3px;
    height: 13px;
    background: linear-gradient(360deg, #1d69c4 1%, #8bbdfc 99%);
    border-radius: 2px;
    margin-right: 8px;
  }
}
.dialogBox ::v-deep  .el-form-item--small.el-form-item{
  margin-bottom: 10px;
  width: 90%;
}
</style>
