import request from '@/utils/request';

/**
 * 企业开户信息回显接口
 * @returns {AxiosPromise}
 */
export function bankQuery() {
  return request({
    url: '/bank/query',
    method: 'get',
  });
}

/**
 * 企业开户暂存功能
 * @returns {AxiosPromise}
 */
export function tempSave(data) {
  return request({
    url: '/bank/tempSave',
    method: 'post',
    data,
  });
}

/**
 * 企业开户提交功能
 * @returns {AxiosPromise}
 */
export function save(data) {
  return request({
    url: '/bank/save',
    method: 'post',
    data,
  });
}

/**
 * 开户行接口
 * @returns {AxiosPromise}
 */
export function queryBankList() {
  return request({
    url: '/bank/queryBankList',
    method: 'get',
  });
}

/**
 * 开户行支行接口
 * @returns {AxiosPromise}
 */
export function querySubBankList(params) {
  return request({
    url: '/bank/querySubBankList',
    method: 'get',
    params,
  });
}

/**
 * 短信验证码接口
 * @returns {AxiosPromise}
 */
export function sendActiveCode(params) {
  return request({
    url: '/bank/sendActiveCode',
    method: 'get',
    params,
  });
}

/**
 * 更新结算账户
 * @returns {AxiosPromise}
 */
export function updateBankAccount(data) {
  return request({
    url: '/bank/updateBankAccount',
    method: 'post',
    data,
  });
}

/**
 * 查询基本户修改历史记录
 * @returns {AxiosPromise}
 */
export function getBaseAccountModifiedHistory(params) {
  return request({
    url: '/bank/queryBaseAccountModifiedHistory',
    method: 'get',
    params,
  });
}

// 付款证明-查询保证金接口
export function getInfo(data) {
  return request({
    url: '/paymentProve/query',
    method: 'post',
    data,
  });
}

// 付款证明-保存付款信息接口
export function savePaymentProve(data) {
  return request({
    url: '/paymentProve/save',
    method: 'post',
    data,
  });
}
/**
 * 修改基本户信息
 * @returns {AxiosPromise}
 */
export function updateBaseAccountInfo(data) {
  return request({
    url: '/bank/updateBaseAccountInfo',
    method: 'post',
    data,
  });
}

/**
 * 查询基本户最新修改记录
 * @returns {AxiosPromise}
 */
export function queryBaseAccountLatestModifiedRecord() {
  return request({
    url: '/bank/queryBaseAccountLatestModifiedRecord',
    method: 'get',
  });
}

/**
 * 查询基本户某次修改记录详情
 * @returns {AxiosPromise}
 */
export function queryBaseAccountModifiedDetail(params) {
  return request({
    url: '/bank/queryBaseAccountModifiedDetail',
    method: 'get',
    params,
  });
}

// https://yapi.int.ybm100.com/project/891/interface/api/cat_8632
/**
 * 银行卡打款验证
 * @returns {AxiosPromise}
 */
export function paymentAuth(params) {
  return request({
    url: '/bank/paymentAuth',
    method: 'get',
    params,
  });
}

/**
 * 平安银行开户行接口
 * @returns {AxiosPromise}
 */
export function queryBankListForPA(params) {
  return request({
    url: '/bank/queryBankListForPA',
    method: 'get',
    params,
  });
}

/**
 * 平安银行开户行支行接口
 * @returns {AxiosPromise}
 */
export function querySubBankListForPA(params) {
  return request({
    url: '/bank/querySubBankListForPA',
    method: 'get',
    params,
  });
}

/**
 * 短信验证码接口
 * @returns {AxiosPromise}
 */
export function verifyActiveCode(params) {
  return request({
    url: '/bank/verifyActiveCode',
    method: 'get',
    params,
  });
}

// 更新平安结算账户信息
export function updateBankAccountForPA(data) {
  return request({
    url: '/bank/updateBankAccountForPA',
    method: 'post',
    data,
  });
}

/**
 * 查询结算信息修改记录
 * @returns {AxiosPromise}
 */
export function queryBankAccountModifyInfo(params) {
  return request({
    url: '/bank/queryBankAccountModifyInfo',
    method: 'get',
    params,
  });
}
