import { asyncRoutes, constantRoutes } from '@/router'
import { getMenuList, waitingProcessing,menuSwitch } from '@/api/home'
import { getChangeRedCount,afterSalesOrderCount } from '@/api/product'
import {getUrgeDeliveryCount} from "@/api/urge-delivery"
import { isExternal } from "@/utils/util";
import store from '@/store'

function checkPermission(el, binding) {
  const { value } = binding
  const rolesCodes = store.getters && store.getters.rolesCodes;
  if (value && value instanceof Array) {
    if (value.length > 0) {
      const permissionRoles = value
      const hasPermission = rolesCodes.some(role => {
        return permissionRoles.includes(role?.split('#')[0])
      })
      if (!hasPermission) {
        el.parentNode && el.parentNode.removeChild(el)
      }
    }
  } else {
    throw new Error(`need roles! Like v-permission="['admin','order_btn']"`)
  }
}

function filterAsyncRouter(routes, roles, rolesInfo = {}) {
  const res = []
  const routerFilter = (routerItem, name = '', parent) => {
    const parentName = name
    if (routerItem.meta) {
      const { title } = routerItem.meta
      name += title + '_'
    }
    if (roles.indexOf(name) > -1 || routerItem.hidden) {
      const onlineRouterObj = rolesInfo[name] || {}
      const _meta = routerItem.meta || {}
      const obj = { ...routerItem, meta: { ..._meta, title: onlineRouterObj.name || _meta.title } }
      obj.children = []
      parent.push(obj)
      if (routerItem.children) {
        routerItem.children.forEach(item => {
          routerFilter(item, name, obj.children)
        })
      }
    }
  }
  routes.forEach(item => {
    routerFilter(item, '', res)
  })
  return res
}

function getUserRoles(menuList) {
  const roles = []
  const rolesInfo = {}
  const rolesCodes = []
  if (!menuList) {
    return false
  }
  const getRoleName = (roleObj, name = '') => {
    if (roleObj.menuCode === "product_list_up") {
    }
    if (Number(roleObj.parentId) === -1) {
      name = ''
    }
    name += roleObj.name + '_'
    roles.push(name)
    rolesCodes.push(roleObj.menuCode);
    rolesInfo[name] = { ...roleObj }
    if (roleObj.childs && roleObj.childs.length > 0) {
      roleObj.childs.forEach(item => {
        getRoleName(item, name)
      })
    }
  }
  menuList.forEach(item => {
    getRoleName(item)
  })
  return { roles, rolesInfo, rolesCodes }
}

function getShowMenList(accessedRoutes) {
  const list = []
  accessedRoutes.forEach(item => {
    const len = item.children.length
    const hiddenLen = item.children.filter(child => child.hidden).length
    if (len !== hiddenLen || item.path === "/downloadList") {
      list.push(item)
    }
  })
  return list
}

function flatten(ary) {
  let res = []
  ary.map(item => {
    if (Array.isArray(item.children) && item.children.length > 0) {
      res = res.concat(flatten(item.children))
    } else {
      res.push(item)
    }
  })
  return res
}

const state = {
  showIframe: false,
  iframeSrc: '',
  routers: [],
  flattenRoutesList: [],
  tabList: [],
  currentTab: '',
  currentMenu: '',
  roles: [],
  rolesInfo: {},
  excludeList: [],
  useMenu: [],    //常用操作
  commissionRecordBadge: false,  //佣金缴纳记录菜单角标
  rolesCodes: [],
  pendingShipmentReminderQuantity: 0, //新增待处理催发货数量
  menuGray: 0,
  navRedMap:{
    pendingShipmentReminderQuantity: 0,
    productsInconsistent: 0,
    // 售后管理-待商家处理
    waitSellerHandleCount: 0
  }
}

const mutations = {
  SET_COUNT: (state, flag) => {
    state.pendingShipmentReminderQuantity = flag
    state.navRedMap.pendingShipmentReminderQuantity = flag
  },
  SET_CHANGE_RED_COUNT: (state, payload) => {
    state.navRedMap[payload.key] = payload.flag
  },
  SET_MENU_GRAY: (state, flag) => {
    state.menuGray = flag
  },
  SET_BADGE: (state, flag) => {
    state.commissionRecordBadge = flag
  },
  SET_USEMENU: (state, useMenu) => {
    if (useMenu && useMenu.length > 0) {
      state.useMenu = useMenu.map(item => {
        if (item.url.indexOf('/manage/#') > -1) {
          item.path = item.url.replace('/manage/#', '')
        } else {
          item.path = process.env.VUE_APP_BASE_API + item.url
        }
        return item
      })
    }
  },
  SET_MENULIST: (state, rolesCodes) => {
    state.rolesCodes = rolesCodes;
  },
  SET_ROLES: (state, roles) => {
    state.roles = roles
  },
  SET_ROLES_INFO: (state, rolesInfo) => {
    state.rolesInfo = rolesInfo
  },
  SET_ROUTERS: (state, routers) => {
    state.routers = routers
  },
  SET_IFRAME: (state, obj) => {
    if (obj && Object.keys(obj).length > 0) {
      state.showIframe = obj.showIframe
      state.iframeSrc = obj.iframeSrc
    }
  },
  SET_ROUTERSLIST: (state, list) => {
    state.flattenRoutesList = flatten(list);
    const nameList = [];
    state.flattenRoutesList.forEach((item) => {
      if (item.meta && item.meta.noCache) {
        nameList.push(item.name);
      }
      if (item.children && item.children.length) {
        item.children.forEach((item1) => {
          if (item1.meta && item1.meta.noCache) {
            nameList.push(item1.name);
          }
        });
      }
    });
    state.excludeList = nameList;
  },
  ADD_TABLIST: (state, obj) => {
    const { path, params, name } = obj
    // 先判断两个是否一致
    const pathList = state.tabList.map(item => item.split('?')[0])
    const list = []
    if (params) {
      Object.keys(params).forEach(key => {
        list.push(key + '=' + params[key])
      })
    }
    if (!pathList.includes(path)) {
      if (name) {
        const router = {
          path: path,
          name: path.replace('/', ''),
          meta: { title: name }
        }
        let noFind = true
        state.flattenRoutesList.map(item => {
          if (item.path === path) {
            noFind = false
          }
        })
        if (noFind) {
          state.flattenRoutesList.push(router)
        }
      }
      let url = path
      if (list.length > 0) {
        url = path + '?' + list.join('&')
      }
      // state.tabList.push(isExternal(path) ? url : path)
      state.tabList.push(url);
    } else {
      //有打开，只是参数不同
      const tabList = []
      state.tabList.forEach(item => {
        if (item.split('?')[0] === path.split('?')[0]) {
          if (list.length > 0) {
            item = path.split('?')[0] + '?' + list.join('&')
          } else {
            item = path
          }
        }
        tabList.push(item)
      })
      state.tabList = tabList
    }
  },
  DEL_TABLIST: (state, path) => {
    const ary = []
    state.tabList.map(item => {
      if (item !== path) {
        ary.push(item)
      }
    })
    state.tabList = ary
  },
  SET_CURRENTTAB: (state, path) => {
    // console.log(path)
    state.currentTab = path
  },
  SET_CURRENTMENU: (state, path) => {
    state.currentMenu = path.split('?')[0]
  },
  ADD_EXCLUDELIST: (state, name) => {
    if (!state.excludeList.includes(name)) {
      state.excludeList.push(name)
    }
  },
  DEL_EXCLUDELIST: (state, name) => {
    state.excludeList = state.excludeList.filter(item => item !== name)
  }
}

function filterGrayScaleMenus(menus, grayResult) {
  let targetValue;
  if (grayResult === 0) {
    targetValue = 1;  // 移除 isGrayscale === 1 的节点
  }else if (grayResult === 1) {
    targetValue = -1;  // 移除 isGrayscale === -1 的节点
  }else {
    return menus;
  }

  return menus.map(menu => {
    const newMenu = { ...menu };
    if (newMenu.childs && newMenu.childs.length > 0) {
        newMenu.childs = newMenu.childs.filter(child => child.isGrayscale !== targetValue);
    }
    return newMenu;
  });
}

const actions = {
  generateRoutes({ commit }) {
    return new Promise(resolve => {
      waitingProcessing().then(res => {
        if (res && Number(res.code) === 200) {
          commit('SET_BADGE', res.data)

          menuSwitch().then(grayRes => { // 菜单灰度开关 0 旧 1新 -1隐藏
            if (grayRes && Number(grayRes.code) === 0) {
              commit('SET_MENU_GRAY', grayRes.result)
              getMenuList().then(res => {
                if (res && res.code === 0) {
                  //菜单处理
              try{
                const menuListShop = res.result.menuList.find(item => item.name === '店铺管理')
                if (menuListShop&&Array.isArray(menuListShop.childs)) {
                  if(menuListShop.childs.some(item => item.name == '次日达配置')&&menuListShop.childs.some(item => item.name == '次日达服务')){
                    menuListShop.childs=menuListShop.childs.filter(item=>item.name !== '次日达服务')
                  }
                } 
              }catch(e){
                console.log(e)
              }
              const { menuList, useMenu } = res.result
                  commit('SET_USEMENU', useMenu)
                  const grayMenuList = filterGrayScaleMenus(menuList, grayRes.result)

                  const { roles, rolesInfo, rolesCodes } = getUserRoles(grayMenuList)
                  commit('SET_MENULIST', rolesCodes)
                  const newAsyncRouterMap = [...asyncRoutes]
                  const accessedRoutes = filterAsyncRouter(newAsyncRouterMap, roles, rolesInfo)
                  const menList = getShowMenList(accessedRoutes)
                  const routersList = [...constantRoutes, ...menList]
                  commit('SET_ROUTERS', [...menList])    //显示的菜单
                  commit('SET_ROUTERSLIST', routersList)
                  resolve(routersList)
                }
              })
            }
          })

          getUrgeDeliveryCount().then(res => {
            if(res && Number(res.code) === 0) {
              commit('SET_COUNT', res.result)
            }
          })
        }
      })
    })
  },
  getChangeRedCount({ commit }) {
    getChangeRedCount().then(res => {
      const { data } = res
      if(typeof data === 'number')
      commit('SET_CHANGE_RED_COUNT', {flag: data, key: 'productsInconsistent'})
    }).catch(err=>{
      commit('SET_CHANGE_RED_COUNT', {flag: 0, key: 'productsInconsistent'})
    })
  },
  getAfterSalesOrderCount({ commit }) {
    afterSalesOrderCount().then(res => {
      const { data } = res
      if(typeof data === 'number')
        commit('SET_CHANGE_RED_COUNT', {flag: data , key: 'waitSellerHandleCount'})
      }).catch(err=>{
        commit('SET_CHANGE_RED_COUNT', {flag: 0, key: 'waitSellerHandleCount'})
      }) 
  },
}

export default {
  namespaced: true,
  state,
  actions,
  mutations,
  inserted(el, binding) {
    setTimeout(() => {
      Vue.nextTick(function () {
        checkPermission(el, binding)
      })
    }, 1000)
  },
  update(el, binding) {
    setTimeout(() => {
      Vue.nextTick(function () {
        checkPermission(el, binding)
      })
    }, 5000)
  },
}
