<template>
  <div>
    <div class="main-box">
      <div class="con-title explain-search">
        <el-row type="primary">
          <span class="line" />
          <span>配置商品</span>
          <div class="search-btn">
            <el-button size="small" type="primary" @click="returnList">返回楼层列表</el-button>
          </div>
        </el-row>
      </div>
      <div class="explain-info">
        <p>楼层商品配置规则：</p>
        <p>1、只允许商业添加楼层区域内可售卖的商品；</p>
        <p>2、楼层区域内的客户只能看到在客户省份在售的商品。例如楼层1设置区域：湖北、湖南，商品A销售区域：湖北，则湖南省的客户对楼层1可见，对商品A不可见；</p>
        <p>3、若商品在后期更改了商圈导致不在楼层区域内可售，则该商品对楼层区域内的所有客户均不可见；</p>
      </div>
      <div class="explain-info-information">
        <span>楼层名称：{{ goodsData.floorName }}</span>
        <span>楼层描述：{{ goodsData.floorDescribe }}</span>
        <div style="margin-top: 10px">
          <span>区域：{{ goodsData.areaName }}</span>
        </div>
      </div>
      <div class="con-title explain-search" style="padding: 0 15px 0;">
        <el-row type="primary">
          <span class="line"></span>
          <span>查询条件</span>
          <div class="search-btn">
            <el-button size="small" @click="resetSearch('ruleSearch')">重置</el-button>
            <el-button size="small" type="primary" @click="getList('search')">查询</el-button>
          </div>
        </el-row>
      </div>
      <div class="explain-search" style="padding-bottom: 5px;border-bottom: 1px solid #f0f2f5;">
        <el-form size="small" :inline="true" ref="ruleSearch">
          <el-form-item class="my-label" label="商品编码">
            <el-input v-model.trim="searchData.barcode" style="width: 163px" clearable></el-input>
          </el-form-item>
          <el-form-item class="my-label" label="商品ERP编码">
            <el-input v-model.trim="searchData.erpCode" style="width: 140px" clearable></el-input>
          </el-form-item>
          <el-form-item class="my-label" label="商品名称">
            <el-input v-model.trim="searchData.productName" style="width: 163px" clearable></el-input>
          </el-form-item>
          <el-form-item class="my-label" label="药品类型">
            <el-select
              v-model.trim="searchData.drugClassification"
              placeholder="请选择"
              style="width: 163px"
              clearable
            >
              <el-option label="全部" value></el-option>
              <el-option label="甲类OTC" :value="1"></el-option>
              <el-option label="乙类OTC" :value="2"></el-option>
              <el-option label="处方药" :value="3"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item class="my-label my-label3" label="条形码" label-width="67px">
            <el-input v-model.trim="searchData.code" style="width: 163px" clearable></el-input>
          </el-form-item>
          <el-form-item class="my-label" label="批准文号">
            <el-input v-model.trim="searchData.approvalNumber" style="width: 163px" clearable></el-input>
          </el-form-item>
          <el-form-item class="my-label" label="商品助记码">
            <el-input v-model.trim="searchData.zjm" style="width: 151px" clearable></el-input>
          </el-form-item>
          <el-form-item class="my-label" label="商品状态">
            <el-select
              v-model.trim="searchData.status"
              placeholder="请选择"
              style="width: 163px"
              clearable
            >
              <el-option label="全部" value></el-option>
              <el-option label="销售中" :value="1"></el-option>
              <el-option label="售罄" :value="2"></el-option>
              <el-option label="下架" :value="4"></el-option>
              <el-option label="待上架" :value="6"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item class="my-label" label="商品可售区域">
            <el-select
              v-model.trim="searchData.branchCode"
              placeholder="请选择"
              style="width: 139px"
              clearable
            >
              <el-option
                v-for="(item, index) in regionasList"
                :key="index"
                :label="item.branchName"
                :value="item.branchCode"
              />
            </el-select>
          </el-form-item>
          <el-form-item class="my-label my-label3" label="厂家" label-width="67px">
            <el-input v-model.trim="searchData.manufacturer" style="width: 163px" clearable></el-input>
          </el-form-item>
        </el-form>
      </div>
      <div>
        <div
          class="con-title explain-search"
          style="padding: 5px 15px 0;"
        >
          <el-row type="primary">
            <span class="line" />
            <span>商品基本信息</span>
            <div class="search-btn">
              <el-button
                class="xyy-blue"
                type="primary"
                size="small"
                @click="addGoods('search')"
              >
                新增商品
              </el-button>

              <el-button
                class="xyy-blue"
                type="primary"
                size="small"
                @click="batchImportVisible = true"
              >
                批量新增商品
              </el-button>

              <el-button
                class="xyy-blue"
                type="primary"
                size="small"
                @click="batchRemove"
              >
                批量移除商品
              </el-button>
            </div>
          </el-row>
        </div>
        <div style="padding: 10px 20px">
          <div class="customer-tabs">
            <el-table
              ref="goodTable"
              :data="tableData.list"
              v-loading="laodingBoole"
              stripe
              style="width: 100%"
              @selection-change="seleteShopHander"
              :header-cell-style="{ background: '#F9F9F9', color: '#666666' }"
            >
              <el-table-column type="selection" width="55"></el-table-column>
              <el-table-column label="商品名称" width="246">
                <template slot-scope="scope">
                  <el-row type="flex" justify="start" align="middle">
                    <img class="imgInfo" :src="scope.row.imageUrl" />
                    <div style=" float: left;">
                      <div>{{ scope.row.productName }}</div>
                      <div class="table-text">{{ scope.row.manufacturer }}</div>
                      <div class="table-text">{{ scope.row.spec }}</div>
                      <div class="table-text">{{ scope.row.code }}</div>
                      <div class="table-text">{{ scope.row.barcode }}</div>
                      <div class="table-text">{{ scope.row.erpCode }}</div>
                    </div>
                  </el-row>
                </template>
              </el-table-column>
              <el-table-column prop="approvalNumber" label="批准文号"></el-table-column>
              <el-table-column label="价格" width="120">
                <template slot-scope="scope">
                  <el-row type="flex" justify="start" align="middle">
                    <div style=" float: left;">
                      <div v-if="scope.row.fob">药帮忙价:{{ scope.row.fob }}</div>
                      <div v-else>药帮忙价:-</div>
                      <div v-if="scope.row.suggestPrice">零售价:{{ scope.row.suggestPrice }}</div>
                      <div v-else>零售价:-</div>
                    </div>
                  </el-row>
                </template>
              </el-table-column>
              <el-table-column label="商品状态">
                <template slot-scope="scope">
                  <span v-if="scope.row.status == 1">销售中</span>
                  <span v-else-if="scope.row.status == 2">已售罄</span>
                  <span v-else-if="scope.row.status == 4">下架</span>
                  <span v-else-if="scope.row.status == 6">待上架</span>
                  <span v-else-if="scope.row.status == 7">已录入</span>
                  <span v-else-if="scope.row.status == 8">待审核</span>
                  <span v-else-if="scope.row.status == 9">审核未通过</span>
                  <span v-else-if="scope.row.status == 20">删除</span>
                  <span v-else>-</span>
                </template>
              </el-table-column>
              <el-table-column prop="availableQty" label="库存"></el-table-column>
              <el-table-column prop="areaNmmes" label="商品可售区域" width="120" show-overflow-tooltip></el-table-column>
              <el-table-column label="药品类型">
                <template slot-scope="scope">
                  <span v-if="scope.row.drugClassification == 1">甲类OTC</span>
                  <span v-else-if="scope.row.drugClassification == 2">乙类OTC</span>
                  <span v-else-if="scope.row.drugClassification == 3">处方药</span>
                  <span v-else>-</span>
                </template>
              </el-table-column>
              <el-table-column label="排序" width="120">
                <template slot-scope="scope">
                  <el-input
                    v-model="scope.row.sort"
                    style="padding-right: 4px;width: 56px;height: 24px;"
                    onkeyup="this.value=this.value.replace(/\D/g,'')"
                    onafterpaste="this.value=this.value.replace(/\D/g,'')"
                    size="small"
                  />
                  <!-- <span v-else style="padding-right: 4px;">{{scope.row.sort}}</span> -->
                  <el-button type="text" size="small" @click="confirmEdit(scope.row)">保存</el-button>
                  <!-- <el-button v-else type="text" size="small" @click="scope.row.edit=!scope.row.edit">编辑</el-button> -->
                </template>
              </el-table-column>
              <el-table-column fixed="right" label="操作" width="80">
                <template slot-scope="scope">
                  <el-button type="text" @click="disable(scope.row)" size="small">移除</el-button>
                </template>
              </el-table-column>
              <template slot="empty">
                <div class="noData">
                  <p class="img-box">
                    <img src="@/assets/image/marketing/noneImg.png" alt />
                  </p>
                  <p>暂无数据</p>
                </div>
              </template>
            </el-table>
            <div class="explain-pag">
              <Pagination
                v-show="tableData.total > 0"
                :total="tableData.total"
                :page.sync="pageData.page"
                :limit.sync="pageData.rows"
                @pagination="getList"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
    <el-dialog
      title="选择商品"
      :visible.sync="changeDialog"
      class="my-dialog"
      :modal-append-to-body="false"
      :close-on-click-modal="false"
      width="96%"
    >
      <div class="con-title explain-search">
        <el-row type="primary">
          <span class="line"></span>
          <span>查询条件</span>
          <div class="search-btn">
            <el-button size="small" @click="resetForm('ruleForm')">重置</el-button>
            <el-button size="small" type="primary" @click="addGoods('search')">查询</el-button>
          </div>
        </el-row>
      </div>
      <div class="explain-search" style="padding-bottom: 5px;border-bottom: 1px solid #f0f2f5;">
        <el-form size="small" :inline="true" ref="ruleForm">
          <el-form-item class="my-label" label="商品编码">
            <el-input v-model.trim="form.barcode" style="width: 163px" clearable></el-input>
          </el-form-item>
          <el-form-item class="my-label" label="商品ERP编码">
            <el-input v-model.trim="form.erpCode" style="width: 140px" clearable></el-input>
          </el-form-item>
          <el-form-item class="my-label" label="商品名称">
            <el-input v-model.trim="form.productName" style="width: 163px" clearable></el-input>
          </el-form-item>
          <el-form-item class="my-label" label="药品类型">
            <el-select
              v-model.trim="form.drugClassification"
              placeholder="请选择"
              style="width: 163px"
              clearable
            >
              <el-option label="全部" value></el-option>
              <el-option label="甲类OTC" :value="1"></el-option>
              <el-option label="乙类OTC" :value="2"></el-option>
              <el-option label="处方药" :value="3"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item class="my-label my-label3" label="条形码" label-width="67px">
            <el-input v-model.trim="form.code" style="width: 163px" clearable></el-input>
          </el-form-item>
          <el-form-item class="my-label" label="批准文号">
            <el-input v-model.trim="form.approvalNumber" style="width: 163px" clearable></el-input>
          </el-form-item>
          <el-form-item class="my-label" label="商品助记码">
            <el-input v-model.trim="form.zjm" style="width: 151px" clearable></el-input>
          </el-form-item>
          <el-form-item class="my-label" label="商品状态">
            <el-select v-model.trim="form.status" placeholder="请选择" style="width: 163px" clearable>
              <el-option label="全部" value></el-option>
              <el-option label="销售中" :value="1"></el-option>
              <el-option label="售罄" :value="2"></el-option>
              <el-option label="下架" :value="4"></el-option>
              <el-option label="待上架" :value="6"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item class="my-label" label="商品可售区域">
            <el-select
              v-model.trim="form.branchCode"
              placeholder="请选择"
              style="width: 139px"
              clearable
            >
              <el-option
                v-for="(item, index) in regionasList"
                :key="index"
                :label="item.branchName"
                :value="item.branchCode"
              />
            </el-select>
          </el-form-item>
          <el-form-item class="my-label my-label3" label="厂家" label-width="67px">
            <el-input v-model.trim="form.manufacturer" style="width: 163px" clearable></el-input>
          </el-form-item>
        </el-form>
      </div>
      <div class="con-title explain-search" style="padding: 0 15px;">
        <el-row type="primary">
          <span class="line"></span>
          <span>商品基本信息</span>
        </el-row>
      </div>
      <div class="explain-table">
        <el-table
          v-loading="isProductLoad"
          @selection-change="seleteCustomerHander"
          :data="productData.list"
          ref="dilogTable"
          stripe
          style="width: 100%"
        >
          <el-table-column type="selection" width="55"></el-table-column>
          <el-table-column prop="barcode" label="商品编码" width="120"></el-table-column>
          <el-table-column prop="productName" label="商品名称"></el-table-column>
          <el-table-column prop="code" label="条码" width="120"></el-table-column>
          <el-table-column label="图片">
            <template slot-scope="scope">
              <img class="imgInfo-small" :src="scope.row.imageUrl" />
            </template>
          </el-table-column>
          <el-table-column prop="spec" label="规格" width="120"></el-table-column>
          <el-table-column prop="manufacturer" label="生产厂家" width="120"></el-table-column>
          <el-table-column prop="areaNmmes" label="商品可售区域" width="120" show-overflow-tooltip></el-table-column>
          <el-table-column prop="approvalNumber" label="批准文号"></el-table-column>
          <el-table-column prop="fob" label="药帮忙价"></el-table-column>
          <el-table-column label="商品状态">
            <template slot-scope="scope">
              <span v-if="scope.row.status == 1">销售中</span>
              <span v-else-if="scope.row.status == 2">已售罄</span>
              <span v-else-if="scope.row.status == 4">下架</span>
              <span v-else-if="scope.row.status == 6">待上架</span>
              <span v-else-if="scope.row.status == 7">已录入</span>
              <span v-else-if="scope.row.status == 8">待审核</span>
              <span v-else-if="scope.row.status == 9">审核未通过</span>
              <span v-else-if="scope.row.status == 20">删除</span>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <el-table-column prop="availableQty" label="库存"></el-table-column>
          <el-table-column label="药品类型">
            <template slot-scope="scope">
              <span v-if="scope.row.drugClassification == 1">甲类OTC</span>
              <span v-else-if="scope.row.drugClassification == 2">乙类OTC</span>
              <span v-else-if="scope.row.drugClassification == 3">处方药</span>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <template slot="empty">
            <div class="noData">
              <p class="img-box">
                <img src="@/assets/image/marketing/noneImg.png" alt />
              </p>
              <p>暂无数据</p>
            </div>
          </template>
        </el-table>
      </div>

      <div class="explain-pag">
        <Pagination
          v-show="productData.total > 0"
          :total="productData.total"
          :page.sync="productPage.page"
          :limit.sync="productPage.rows"
          @pagination="addGoods"
        />
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancelDialog" size="small">取 消</el-button>
        <el-button type="primary" class="xyy-blue" @click="determineDialog" size="small">确定</el-button>
      </div>
    </el-dialog>

    <BatchImport
      v-if="batchImportVisible"
      ref="batchImport"
      title="批量导入"
      :batch-import-visible.sync="batchImportVisible"
      @downloadTemplate="downloadTemplate"
      @batchImportFn="batchImportFn"
    >
      <div slot="tips">
        <p>提示：</p>
        <p>1.支持上传xlsx, xls文件，大小不超过3M，商品数量不超过200个</p>
      </div>
    </BatchImport>
  </div>
</template>

<script>
import Pagination from '@/components/Pagination';
import BatchImport from '@/components/BatchImport';
import {
  branchsList,
  getListGoods,
  getCheckListGoods,
  delGoods,
  editGoods,
  batchGoods,
  getHostName,
  batchAddGoods,
} from '@/api/storeManagement/index';
import { apiDownloadTemplate } from '@/api/storeManagement/blacklist';

export default {
  name: 'ConfigurationGoodsList',
  components: {
    Pagination,
    BatchImport,
  },
  props: {
    goodsObj: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      batchImportVisible: false,
      searchData: {
        barcode: '', // 商品编码
        erpCode: '', // 商品erp编码
        productName: '', // 商品名称
        drugClassification: '', // 药品类型
        branchCode: '', // 区域
        floorId: '', // 楼层ID
        code: '', // 条形码
        zjm: '', // 助记码
        manufacturer: '', // 厂家
        status: '', // 状态
        approvalNumber: '', // 批准文号
        sort: 'asc',
      },
      form: {
        barcode: '', // 商品编码
        erpCode: '', // 商品erp编码
        productName: '', // 商品名称
        drugClassification: '', // 药品类型
        branchCode: '', // 区域
        floorId: '', // 楼层ID
        code: '', // 条形码
        zjm: '', // 助记码
        manufacturer: '', // 厂家
        status: '', // 状态
        approvalNumber: '', // 批准文号
        sort: 'asc',
      },
      tableData: {
        total: 0,
        list: [],
      },
      pageData: {
        rows: 10,
        page: 1,
      },
      laodingBoole: false,
      checkListAry: [],
      handerProduct: [],
      changeDialog: false,
      regionasList: [],
      isProductLoad: false,
      productData: {
        list: [],
        total: 0,
      },
      productPage: {
        rows: 10,
        page: 1,
      },
      goodsData: {
        floorName: '',
        floorDescribe: '',
        areaName: '',
        floorId: '',
      },
      imgUrl: '',
    };
  },
  watch: {
    goodsObj: {
      deep: true,
      handler() {
        this.goodsData.floorName = this.goodsObj.floorName;
        this.goodsData.floorDescribe = this.goodsObj.floorDescribe;
        this.goodsData.areaName = this.goodsObj.areaName;
        this.goodsData.floorId = this.goodsObj.floorId;
        this.searchData.floorId = this.goodsObj.floorId;
        this.form.floorId = this.goodsObj.floorId;
        this.goodsData = JSON.parse(JSON.stringify(this.goodsObj));
        // //获取列表数据
        this.queryRegional();
        this.getList();
      },
    },
  },
  created() {
    this.goodsData.floorName = this.goodsObj.floorName;
    this.goodsData.floorDescribe = this.goodsObj.floorDescribe;
    this.goodsData.areaName = this.goodsObj.areaName;
    this.goodsData.floorId = this.goodsObj.floorId;
    this.searchData.floorId = this.goodsObj.floorId;
    this.form.floorId = this.goodsObj.floorId;
    this.goodsData = JSON.parse(JSON.stringify(this.goodsObj));
    // this.queryRegional();
    // this.getList();
    getHostName().then((res) => {
      if (res.hostName) {
        this.imgUrl = `${res.hostName}/ybm/product/min/`;
      }
    });
  },
  methods: {
    batchImportFn(FormData) {
      batchAddGoods(FormData, this.goodsData.floorId).then((res) => {
        if (res.code === 0) {
          const { error, success, errorFileName, errorFileUrl } = res.data;
          const baseUrl = process.env.VUE_APP_BASE_API;
          const h = this.$createElement;
          this.$msgbox({
            title: '上传文件反馈',
            message: h('p', null, [
              h('span', null, `共成功上传${success}个商品，失败${error}条数据${error ? '，下载错误文件：' : ''}`),
              error ? h('a', { attrs: { href: baseUrl + errorFileUrl, download: errorFileName, style: 'color: #4183d5;' } }, `${errorFileName}`) : '']),
            confirmButtonText: '确定',
          }).then(() => {
            this.batchImportVisible = false;
          }).catch(() => {
            this.batchImportVisible = false;
          });
          this.getList();
        } else {
          this.$message.warning(res.message || '失败');
          this.$refs.batchImport.remove();
        }
        this.$refs.batchImport.closeLoading();
      });
    },
    downloadTemplate() {
      const fileName = '楼层批量配置商品.xlsx';
      apiDownloadTemplate(fileName).then((res) => {
        if (res.code && res.code !== 0) {
          this.$message.error(res.message || '请求异常');
        } else {
          this.util.exportExcel(res, fileName);
        }
      }).catch((err) => {
        console.log(err);
      });
    },
    // 获取已选择列表数据
    getList(from) {
      const that = this;
      if (from == 'search') {
        this.pageData.page = 1;
      }
      this.laodingBoole = true;
      const param = {
        ...this.searchData,
        ...this.pageData,
      };
      getCheckListGoods(param).then((res) => {
        if (res.code == 0) {
          this.laodingBoole = false;
          if (res.result) {
            that.tableData.list = res.result.list;
            res.result.list.map((item) => {
              if (item.areaNmmes && item.areaNmmes.length > 0) {
                item.areaNmmes = item.areaNmmes.join(',');
              }
              if (item.imageUrl) {
                item.imageUrl = this.imgUrl + item.imageUrl;
              }
            });
          } else {
            that.tableData.list = [];
          }
          that.tableData.total = res.result.total;
        } else {
          this.laodingBoole = false;
          this.$message({
            message: res.msg,
            type: 'error',
          });
        }
      }).catch(() => {});
    },
    queryRegional() {
      branchsList().then((res) => {
        this.regionasList = res.result;
      });
    },
    // 返回楼层列表
    returnList() {
      this.resetSearch();
      this.resetForm();
      this.$emit('goPrev', { from: 'floorSet' });
    },
    // 添加商品
    addGoods(from) {
      const that = this;
      if (from == 'search') {
        this.productPage.page = 1;
      }
      this.changeDialog = true;
      const param = {
        ...this.form,
        ...this.productPage,
      };
      this.isProductLoad = true;
      getListGoods(param).then((res) => {
        if (res.code == 0) {
          this.isProductLoad = false;
          if (res.result) {
            that.productData.list = res.result.list;
            let areaNmmes = '';
            let imageUrl = '';
            that.productData.list.forEach((item, index) => {
              areaNmmes = '';
              imageUrl = '';
              if (item.areaNmmes && item.areaNmmes.length > 0) {
                areaNmmes = item.areaNmmes.join(',');
              }
              if (item.imageUrl) {
                imageUrl = this.imgUrl + item.imageUrl;
              }
              that.productData.list[index] = Object.assign({}, that.productData.list[index], {
                areaNmmes,
                imageUrl,
              });
            });
          } else {
            that.productData.list = [];
          }
          that.productData.total = res.result.total;
        } else {
          this.$message({
            message: res.msg,
            type: 'error',
          });
        }
      }).catch(() => {
        this.isProductLoad = false;
        this.$message({
          message: '初始列表失败',
          type: 'error',
        });
      });
    },
    // 批量移除
    batchRemove() {
      if (this.checkListAry.length > 0) {
        const ids = [];
        this.checkListAry.forEach((item) => {
          ids.push(item.goodsId);
        });
        this.$confirm('确定要删除选择的商品吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: '',
          customClass: 'delete-dialog-customClass',
        }).then(() => {
          if (this.goodsData.floorId) {
            delGoods({ skuIdList: ids.join(','), floorId: this.goodsData.floorId }).then((res) => {
              if (res.code == 0) {
                this.$message({
                  message: '删除成功',
                  type: 'success',
                });
                this.getList();
              } else {
                this.$message({
                  message: '删除失败',
                  type: 'error',
                });
              }
            });
          } else {
            this.$message({
              message: '缺少楼层id',
              type: 'error',
            });
          }
        }).catch(() => {
          this.$refs.goodTable.clearSelection();
        });
      } else {
        this.$message({
          message: '请选中数据再批量删除',
          type: 'warning',
        });
      }
    },
    // 关闭弹窗
    cancelDialog() {
      this.changeDialog = false;
    },
    // 确定添加商品
    determineDialog() {
      if (this.handerProduct.length > 0) {
        const ids = [];
        this.handerProduct.forEach((item) => {
          ids.push(item.goodsId);
        });
        if (this.goodsData.floorId) {
          batchGoods({ goods: ids.join(','), floorId: this.goodsData.floorId }).then((res) => {
            if (res.code == 0) {
              this.changeDialog = false;
              this.$message({
                message: '添加成功',
                type: 'success',
              });
              this.getList();
            } else if (res.code == 1) {
              // 添加失败弹窗
              this.$confirm(res.msg, '提示', {
                confirmButtonText: '确定',
                showCancelButton: false,
                type: '',
                customClass: 'delete-dialog-customClass',
              }).then(() => {});
            } else {
              this.$message({
                message: '添加失败',
                type: 'error',
              });
            }
          });
        } else {
          this.$message({
            message: '缺少楼层id',
            type: 'error',
          });
        }
      } else {
        this.$message({
          message: '请选中数据再添加',
          type: 'warning',
        });
      }
    },
    // 移除
    disable(val) {
      let id = '';
      if (val) {
        id = val.goodsId;
      }
      if (id) {
        this.$confirm('确定要删除选择的商品吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: '',
          customClass: 'delete-dialog-customClass',
        })
          .then(() => {
            if (this.goodsData.floorId) {
              delGoods({ skuIdList: id, floorId: this.goodsData.floorId }).then((res) => {
                if (res.code == 0) {
                  this.$message({
                    message: '删除成功',
                    type: 'success',
                  });
                  this.getList();
                } else {
                  this.$message({
                    message: '删除失败',
                    type: 'error',
                  });
                }
              });
            }
          })
          .catch(() => {
          });
      }
    },
    // 选择列表数据
    seleteShopHander(val) {
      this.checkListAry = val;
    },
    // 选择商品
    seleteCustomerHander(val) {
      this.handerProduct = val;
    },
    // 修改商品排序
    confirmEdit(val) {
      if (this.goodsData.floorId) {
        editGoods({ id: val.id, floorId: this.goodsData.floorId, sort: val.sort }).then((res) => {
          if (res.code == 0) {
            this.$message({
              message: '修改成功',
              type: 'success',
            });
            this.getList();
          } else {
            this.$message({
              message: '修改失败',
              type: 'error',
            });
          }
        });
      } else {
        this.$message({
          message: '缺少楼层id',
          type: 'error',
        });
      }
    },
    // 重置列表数据
    resetSearch(from) {
      this.searchData = {
        barcode: '', // 商品编码
        erpCode: '', // 商品erp编码
        productName: '', // 商品名称
        drugClassification: '', // 药品类型
        branchCode: '', // 区域
        floorId: this.goodsData.floorId, // 楼层ID
        code: '', // 条形码
        zjm: '', // 助记码
        manufacturer: '', // 厂家
        status: '', // 状态
        approvalNumber: '', // 批准文号
        sort: 'asc',
      };
      this.pageData = {
        rows: 10,
        page: 1,
      };
      if (from == 'ruleSearch') {
        this.getList();
      }
    },

    // 重置t弹窗数据
    resetForm(from) {
      this.form = {
        barcode: '', // 商品编码
        erpCode: '', // 商品erp编码
        productName: '', // 商品名称
        drugClassification: '', // 药品类型
        branchCode: '', // 区域
        floorId: this.goodsData.floorId, // 楼层ID
        code: '', // 条形码
        zjm: '', // 助记码
        manufacturer: '', // 厂家
        status: '', // 状态
        approvalNumber: '', // 批准文号
        sort: 'asc',
      };
      this.productPage = {
        rows: 10,
        page: 1,
      };
      if (from == 'ruleForm') {
        this.addGoods();
      }
    },
  },
};
</script>

<style>
@import '../../../assets/css/changeElement.scss';
</style>
<style lang="scss" scoped>
// @import '../../../assets/css/market';
.con-inner {
  padding-top: 15px;
  padding-left: 23px;
  margin-right: 17px;
  padding-bottom: 10px;
  border-bottom: 1px solid #efefef;
  div {
    display: inline-block;
  }
  .img {
    width: 92px;
    height: 92px;
    img {
      width: 100%;
      height: 100%;
    }
  }
  .text {
    padding-left: 20px;
    vertical-align: top;
    h3 {
      font-size: 14px;
      color: #000000;
      padding: 0;
      margin: 0;
    }
    p {
      padding: 0;
      margin: 0;
      font-size: 12px;
      color: #333333;
      padding-top: 10px;
    }
  }
  .btn {
    float: right;
    padding-top: 26px;
    button {
      width: 100px;
      height: 30px;
      text-align: center;
      line-height: 30px;
      padding: 0;
      background: rgba(65, 131, 213, 1);
      border-color: rgba(65, 131, 213, 1);
      border-radius: 4px;
      font-size: 14px;
    }
    a {
      color: #ffffff;
      text-decoration: none;
    }
    .router-link-active {
      color: #ffffff;
      text-decoration: none;
    }
  }
}
.pag-info {
  width: 500px;
}
// .main-box {
//   .list-box {
//     .customer-tabs {
//       .el-button + .el-button {
//         margin-left: 0px;
//       }
//     }
//   }
// }

.search-btn {
  float: right;
}
.imgInfo {
  width: 64px;
  height: 64px;
  margin-right: 16px;
}
.imgInfo-small {
  width: 20px;
  height: 20px;
  margin-right: 16px;
}
.table-text {
  font-size: 12px;
  font-family: PingFangSC, PingFangSC-Regular;
  font-weight: 400;
  text-align: left;
  color: #666666;
}
.explain-info {
  padding: 0 28px 0;
  margin-top: 0px;
  p {
    font-size: 12px;
    font-family: PingFangSC, PingFangSC-Regular;
    font-weight: 400;
    text-align: left;
    color: #333333;
    line-height: 10px;
  }
}
.explain-info-information {
  background: #f9f9f9;
  border-radius: 6px;
  padding: 10px;
  margin: 0 15px;
  span {
    margin-right: 65px;
    font-size: 14px;
    font-family: PingFangSC, PingFangSC-Regular;
    font-weight: 400;
    text-align: left;
    color: #333333;
    line-height: 20px;
  }
}
::v-deep   .my-label .el-form-item__label {
  border: 1px solid #d9d9d9;
  border-radius: 4px 0px 0px 4px;
  padding: 0 9px 0 9px;
  height: 30px;
  vertical-align: bottom;
  border-right: 0;
  font-size: 12px;
  line-height: 28px;
}
::v-deep   .el-input--small .el-input__inner {
  height: 30px;
  line-height: 30px;
  font-size: 12px;
}
::v-deep  .el-form-item--small.el-form-item {
  margin-bottom: 16px;
}

::v-deep   .edit-btn {
  display: flex;
  justify-content: center;
  align-items: center;

  i.el-icon-edit-outline:before {
    font-size: 16px;
  }
}

::v-deep   .my-label .el-input__inner {
  border-radius: 0px 4px 4px 0px;
}

::v-deep   .el-range-editor--small.el-input__inner {
  height: 30px;
}

::v-deep   .my-label2 .el-form-item__label {
  border: 1px solid #ffffff;
  border-radius: 4px 0px 0px 4px;
  padding: 0 9px 0 9px;
  height: 30px;
  vertical-align: bottom;
  border-right: 0;
  font-size: 12px;
  line-height: 28px;
}
::v-deep   .my-label2 .el-input__inner {
  border-radius: 4px 4px 4px 4px;
}
::v-deep   .my-label2 .my-lable2-item {
  width: 240px;
}
::v-deep   .my-label3 .el-form-item__label {
  border: 1px solid #d9d9d9;
  border-radius: 4px 0px 0px 4px;
  padding: 0 20px 0 9px;
  height: 30px;
  vertical-align: bottom;
  border-right: 0;
  font-size: 12px;
  line-height: 28px;
}
::v-deep  .my-dialog .user {
  .el-tree-node__content > .el-tree-node__expand-icon {
    padding: 0px;
    font-size: 0;
  }
}
</style>
