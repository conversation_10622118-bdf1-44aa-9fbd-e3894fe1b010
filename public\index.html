<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width,initial-scale=1.0">
  <link rel="icon" href="<%= BASE_URL %>logo.ico">
  <title>药帮忙供应商管理平台</title>
  <script type="text/javascript">
    // env
    (function () {
      window.__env__ = 'dev';
      const tmp = /(dev|test|stage).*/.exec(window.location.host);
      if (/(\d+\.){3}\d+/g.test(window.location.host) || window.location.host.indexOf('localhost') >= 0) {
        window.__env__ = 'devlpoment';
      } else if (tmp) {
        window.__env__ = tmp[1];
      } else {
        window.__env__ = 'prod';
      }

      window.isMobile = function () {
        const ua = navigator.userAgent;
        const ipad = ua.match(/(iPad).*OS\s([\d_]+)/);
        const isIphone = ua.match(/(iPhone\sOS)\s([\d_]+)/);
        const isAndroid = ua.match(/(Android)\s+([\d.]+)/);
        const isMobile = ipad || isIphone || isAndroid;
        return !!isMobile;
      }

      function fnResize() {
        if (window.isMobile()) {
          let deviceWidth = document.documentElement.clientWidth || window.innerWidth;
          if (deviceWidth >= 750) {
            deviceWidth = 750;
          }
          if (deviceWidth <= 320) {
            deviceWidth = 320;
          }
          document.documentElement.style.fontSize = (deviceWidth / 7.5) + 'px';
        }
      }

      fnResize();
    })();
  </script>
  <style>
        html,body{
            height: 100%;
            overflow-y: auto;
        }
    </style>
  <script src="//skynet.api.ybm100.com/sdk/js/latest/index.js"></script>
    <script src="static/js/jquery.min.js"></script>
  <script src="static/layer/layer.js"></script>
</head>

<body style="margin:0">
  <noscript>
    <strong>您的网络好像出问题了，请检查一下网络吧</strong>
  </noscript>
  <div id="app"></div>
  <!-- built files will be auto injected -->
</body>

</html>
