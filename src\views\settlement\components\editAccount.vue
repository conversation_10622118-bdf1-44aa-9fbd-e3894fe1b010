<template>
  <span style="margin: 0 10px;">
    <el-button disabled type="primary" size="small" @click="show">
      编辑提现账户
    </el-button>
    <el-dialog title="编辑提现账户" :visible.sync="dialogVisible" width="500px">
      <el-form ref="formRef" :model="form" label-width="80px">
        <el-form-item label="账户名称" prop="accountName" :rules="{ required: true, message: '请填写企业名称', trigger: 'blur' }">
          <el-input :value="form.accountName" placeholder="请填写企业名称" @input="(val) => { inputCheck(val, 'form', 'accountName', /^.{0,30}$/) }"></el-input>
        </el-form-item>
        <el-form-item label="银行卡号" prop="bankCode" :rules="{ required: true, message: '请输入企业银行卡号', trigger: 'blur' }">
          <el-input :value="form.bankCode" placeholder="请输入企业银行卡号" @input="(val) => { inputCheck(val, 'form', 'bankCode', /^.{0,60}$/) }"></el-input>
        </el-form-item>
        <el-form-item label="开户行" prop="bankName" :rules="{ required: true, message: '请输入企业开户行名称', trigger: 'blur' }">
          <el-input type="textarea" :value="form.bankName" placeholder="请输入企业开户行名称" @input="(val) => { inputCheck(val, 'form', 'bankName', /^.{0,300}$/) }"></el-input>
        </el-form-item>
        <el-form-item label="开户支行" prop="subBankName" :rules="{ required: true, message: '请输入企业开户支行名称', trigger: 'blur' }">
          <el-input type="textarea" :value="form.subBankName" placeholder="请输入企业开户支行名称" @input="(val) => { inputCheck(val, 'form', 'subBankName', /^.{0,300}$/) }"></el-input>
        </el-form-item>
      </el-form>
      <template slot="footer">
        <el-button size="small" @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" size="small" @click="submit">保存</el-button>
      </template>
    </el-dialog>
  </span>
</template>
<script>
import { getWithDrawCardInfo, updateWithDrawCardInfo } from '../../../api/settlement/online/index'
export default {
  data() {
    return {
      dialogVisible: false,
      form: {
        accountName: '',
        bankCode: '',
        bankName: '',
        subBankName: ''
      },
      loading: false,
    }
  },
  methods: {
    show() {
      if (this.loading) return
      this.loading = true;
      getWithDrawCardInfo().then(res => {
        if (res.code === 0) {
          this.form.accountName = res.data.accountName
          this.form.bankCode = res.data.bankCode
          this.form.bankName = res.data.bankName
          this.form.subBankName = res.data.subBankName

        }
      }).finally(() => {
        this.dialogVisible = true;
        this.loading = false;
      })

    },
    inputCheck(val, form, key, reg) {
        if (reg.test(val)) {
            this[form][key] = val
        }
    },
    submit() {
      if (this.loading) return;
      this.$refs.formRef.validate((valid) => {
        if (valid) {
          let shopName = this.$store.state.app.shopConfig.shopName.slice(0,5);
          if (this.form.accountName.slice(0,5) != shopName) {
            this.$message.error('账户名称需填写企业名称，请修改后重新提交')
            return;
          }
          this.loading = true;
          updateWithDrawCardInfo(this.form).then(res => {
            if (res.code === 0) {
              this.dialogVisible = false
              this.$message.success('提现账户修改成功')
            } else {
              this.$message.error(res.message)
            }
          }).finally(() => {
            this.loading = false;
          })
        }
      })
    }
  }
}
</script>
<style scoped>
</style>
