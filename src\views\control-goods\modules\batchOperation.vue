<template>
  <div class="main-box" id="customer">
    <div class="list-box" v-loading="isLoadingPeople">
      <div class="batch-handle">
        <div class="box batch-title">
          <span class="go-back" @click="goBack" ><i class="el-icon-arrow-left"></i>返回</span>
          <span class="line">|</span>
          <span class="title">批量操作</span>
        </div>
        <el-tabs v-model="tabName" type="card">
          <el-tab-pane label="批量调整控销数据" name="first">
            <div class="box-flex batch-upload-box">
              <span class="upload-title">上传文件</span>
              <div class="upload-content">
                <div class="box-flex">
                  <el-upload
                    class="upload-demo"
                    ref="upload"
                    :limit="1"
                    :before-upload="beforeUpload"
                    accept=".xls, .xlsx, .XLS, .XLSX"
                    action="doUpload"
                  >
                    <el-button size="small" class="upload-btn">{{laoderName}}</el-button>
                  </el-upload>
                  <span
                    :class="fileName.length>0?'file-name':'upload-tips'"
                  >{{fileName.length>0?fileName:'请上传excel、xls、xlsx文件'}}</span>
                </div>
                <p class="p1">
                  <a
                    :href="downloadTemplate"
                    download
                    class="downLoader-btn"
                  >下载批量调整控销数据导入模板</a>
                </p>
                <p class="p2">
                  <a
                    :href="downloadRegion"
                    download
                    class="downLoader-btn"
                  >下载控销区域、客户类型id</a>
                </p>
                <p class="batch-submit-btn">
                  <el-button size="mini" type="primary" @click="sureUploader">提交批量操作</el-button>
                </p>
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>
    <div class="list-box">
      <div style="height:10px;background:#f4f4f4"></div>
      <div class="explain-box">
        <div class="box-flex row-between con-title" style="padding-left: 0">
          <div >
            <span class="line"></span>
            <span>操作记录</span>
          </div>
          <div>
            <a href="javascript:void(0);" @click="handerSearch">
              <span class="box-flex refresh">
                <img src="@/assets/image/common/refresh.png" alt />刷新
              </span>
            </a>
          </div>
        </div>
        <div class="customer-main">
          <div class="customer-tabs">
            <el-table
              max-height="397"
              :data="tableData"
              stripe
              style="width: 100%"
              header-row-class-name="tableHeaderName"
              row-class-name="tableRowName"
              :header-cell-style="{background:'#F9F9F9',color:'#666666'}"
            >
              <el-table-column prop="fileName" label="文件名称" width="340"></el-table-column>
              <el-table-column label="操作时间" width="170" prop="createTime">
                <!-- <template slot-scope="scope">
                  <span>{{this.$moment(scope.row.createTime).format('YYYY-MM-DD HH:mm:ss')}}</span>
                </template> -->
              </el-table-column>
              <el-table-column label="状态" width="130">
                <template slot-scope="scope">
                  <span>{{scope.row.status==1?"处理中":scope.row.status==2?"处理完成":scope.row.status==0?"待处理":"处理失败"}}</span>
                </template>
              </el-table-column>
              <el-table-column prop="handleResult" label="操作说明" show-overflow-tooltip></el-table-column>
              <el-table-column fixed="right" label="操作" width="100">
                <template slot-scope="scope">
                  <p class="handle">
                    <span v-if="scope.row.failedNum==0">-</span>
                     <el-button v-else type="text" @click="exportExcel(scope.row)" size="small">下载失败列表</el-button>
                    <!-- <a
                      v-else
                      :href="'/product/upload/export?id='+scope.row.id"
                      download
                      class="downLoader-btn"
                    >下载失败列表</a> -->
                  </p>
                </template>
              </el-table-column>

              <template slot="empty">
                <!-- 查询空 -->
                  <div class="noData">
                  <p class="img-box">
                    <img
                      src="@/assets/image/marketing/noneImg.png"
                      alt=""
                    />
                  </p>
                  <p>暂无数据</p>
                </div>
              </template>
            </el-table>
            <div class="explain-pag">
              <Pagination
                v-show="total > 0"
                :total="total"
                :page.sync="listQuery.page"
                :limit.sync="listQuery.pageSize"
                @pagination="startList"
              ></Pagination>
            </div>
            <!-- <div class="customer-table-foot">
              <div class="customer-block">
                <span
                  class="customer-demonstration"
                >共{{this.listQuery.total}}条数据，每页{{this.listQuery.pageSize}}条，共{{Math.ceil(this.listQuery.total/this.listQuery.pageSize)}}页</span>
                <el-pagination
                  @size-change="handleSizeChange"
                  @current-change="handleCurrentChange"
                  :current-page.sync="this.listQuery.page"
                  :page-sizes="[10, 20, 30, 40]"
                  :page-size="this.listQuery.pageSize"
                  prev-text="上一页"
                  next-text="下一页"
                  layout="sizes, prev, pager, next, jumper"
                  :total="this.listQuery.total"
                  popper-class="popperInput"
                ></el-pagination>
              </div>
            </div> -->
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import {startUploadList,importControlSaleInfos} from "@/api/goods/controlGoods.js"
import Pagination from '@/components/Pagination'
import utils from '@/utils/filter';
import {actionTracking} from "@/track/eventTracking";
export default {
  name: 'batchOperation',
  components: {
    Pagination
  },
  created(){
    this.startList(this.listQuery);
  },
  data() {
    return {
      listQuery: {
        page: 1,
        pageSize: 20,
      },
      total: 0,
      tableData: [],
      tabName:"first",
      fileName:'',
      files:"",
      laoderName:"选择文件",
      isLoadingPeople:false,
      branchCode:'',
      downloadTemplate:process.env.VUE_APP_BASE_API+'/salesControl/downloadTemplate',
      downloadRegion:process.env.VUE_APP_BASE_API+'/salesControl/downloadRegion'
    };
  },
  methods: {
    handerSearch() {
      (this.listQuery = {
        page: 1,
        pageSize: 20,
      }),
      this.startList(this.listQuery);
    },
    // 获取操作记录列表
    startList(listQuery){
      const { page, pageSize } = listQuery;
      let that = this
      const param = {
        bizType:1,// 上传结果类型:1:控销上传2:批量添加商品3批量调整商品信息4:批量更新商品图片
        ...this.listQuery
      }
      startUploadList(param)
      .then(res => {
        if(res.code==0){
          // const { total } = res.result
          this.tableData = res.result.list;
          let createTime = {};
          this.tableData.forEach((item, index) => {
            createTime = {};
            if (item.createTime == null) {
              createTime = '-';
            } else {
              createTime = utils.dataTime(
                item.createTime,
                'yy-mm-dd HH:ss:nn'
              );
            }
            this.tableData[index] = Object.assign({}, this.tableData[index], {
              createTime: createTime,
            });
          });
         that.total = res.result.total
          // this.listQuery = {
            // ...this.listQuery,
            // page: Number(res.result.pageNum),
            // pageSize: Number(res.result.pageSize),
            // total: Number(res.result.total)
          // };
        }else{
            this.$message({
            message: res.message,
            type: 'error',
          })
        }}).catch(error => {
            this.$message({
                message: "初始列表失败",
                type: 'error'
            });
            this.isLoadingPeople=false
      })
    },
    // 选择文件
    beforeUpload(file){
      let extension = file.name.split('.')[1] === 'xls';
      let extension2 = file.name.split('.')[1] === 'xlsx';
      let isLt2M = file.size / 1024 / 1024 < 200
      if (!extension && !extension2) {
          this.$message.warning('上传模板只能是 xls、xlsx格式!');
          return
      }
      if(!isLt2M){
          this.$message.warning('文件过大，最大支持200M!');
          return
      }
      this.files = file;
      this.fileName=file.name;
      return false;
    },
    // 提交批量操作
    sureUploader(){
      let fileFormData;
      if(this.files&&this.files.name!=""){
          fileFormData=new FormData();
          fileFormData.append('file', this.files, this.fileName);
          this.isLoadingPeople=true
          importControlSaleInfos(fileFormData).then(res => {
            this.isLoadingPeople=false
              if(res.code==0){
                  this.$message({
                      message:"上传成功",
                      type: 'success'
                  });
                  this.laoderName="重新上传";
                  this.files = "";
                  this.fileName = "";
                  this.handerSearch();
              }else{
                  this.$message({
                      message: res.message,
                      type: 'error'
                  });
              }
              actionTracking('control_batch_modification', {
                control_batch_modification_result : res.code === 0 ? 'success' : 'false'
              })

          }).catch(error => {
                  // this.$message({
                  //     message: "请求失败",
                  //     type: 'error'
                  // });
              this.isLoadingPeople=false
          })
      }
      else{
            this.$message.warning('请选择上传文件!');
      }
    },
    handleChange(file) {
      file?this.fileName=file.name:'';
    },
    // 返回上一级
    goBack(){
      this.$emit('goPrev', { from: 'basic' })
    },
    // 每页条数 改变时会触发
    handleSizeChange(val) {
      this.pageSize=val;
    },
    // 当前页 改变时会触发
    handleCurrentChange(val) {
      this.startList();
    },
    exportExcel(row){
      if (row.id) {
        let url = `${process.env.VUE_APP_BASE_API}/product/upload/export?id=${row.id}`;
        const a = document.createElement('a');
        a.href = url;
        a.click();
      }
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep  .tableHeaderName {
  font-size: 14px;
  th {
    height: 42px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: rgba(51, 51, 51, 0.85);
    background: #f9f9f9;
    padding: 0;
  }
}
::v-deep  .tableRowName {
  td {
    height: 42px;
    padding: 0;
    font-size: 12px;
  }
}
.box-flex {
  display: -webkit-box;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  -webkit-flex-wrap: nowrap;
  -ms-flex-wrap: nowrap;
  flex-wrap: nowrap;
}

.row-between {
  -webkit-box-pack: justify;
  -moz-justify-content: space-between;
  -webkit-justify-content: space-between;
  justify-content: space-between;
}
::v-deep  .el-tabs__item {
  padding: 5px 16px;
  height: 28px;
  line-height: 19px;
  font-size: 14px;
}
.batch-handle {
  padding-left: 13px;
}
.batch-title {
  padding-top: 12px;
  padding-bottom: 22px;
  font-family: PingFangSC-Regular, PingFang SC;
  .go-back {
    cursor: pointer;
    font-size: 14px;
    color: #4184d5;
    margin-right: 8px;
  }
  .line {
    display: inline-block;
    width: 1px;
    height: 13px;
    color: rgba(216, 216, 216, 1);
    font-size: 13px;
  }
  .title {
    font-size: 16px;
    font-weight: 400;
    color: rgba(0, 0, 0, 1);
    line-height: 14px;
    margin-left: 13px;
  }
}
.upload-title {
  font-size: 12px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: rgba(51, 51, 51, 0.85);
  display: inline-block;
  margin-right: 15px;
  line-height: 24px;
  margin-top: 3px;
}
.upload-content {
  .upload-btn {
    font-size: 12px;
    font-weight: 400;
    color: rgba(65, 132, 213, 1);
    width: 76px;
    height: 30px;
    background: rgba(255, 255, 255, 1);
    border-radius: 4px;
    border: 1px solid rgba(65, 132, 213, 1);
  }
  p {
    margin: 0;
    &.batch-submit-btn {
      margin-top: 16px;
      margin-bottom: 16px;
    }
    &.p1 {
      margin-top: 17px;
    }
    &.p2 {
      margin-top: 8px;
    }
    a {
      font-size: 12px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: rgba(65, 132, 213, 1);
      line-height: 17px;
    }
  }
}
.downLoader-btn {
  width: 72px;
  height: 17px;
  font-size: 12px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: rgba(65, 132, 213, 1);
  line-height: 17px;
}
.upload-tips {
  font-size: 12px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #999999;
  line-height: 24px;
  margin-left: 15px;
  display: flex;
  justify-content: center;
  align-items: center;
}
.file-name {
  font-size: 12px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: rgba(51, 51, 51, 1);
  line-height: 24px;
  margin-left: 15px;
  display: flex;
  justify-content: center;
  align-items: center;
}
::v-deep  .el-form--inline .el-form-item__label {
  border: 1px solid #ccc;
  font-size: 12px;
  line-height: 2.3;
  margin-top: 1px;
  border-radius: 3px;
  text-align: center;
  padding: 0 7px;
  background-color: #fff;
}
::v-deep  .el-form-item__content {
  line-height: 30px;
}
::v-deep  .el-form-item {
  margin-bottom: 0;
}
::v-deep  .el-input-group__prepend {
  background-color: #fff;
}
::v-deep  .el-tabs--border-card > .el-tabs__header {
  border: none;
}
.imgInfo {
  width: 80px;
  height: 80px;
}
main.my-main {
  margin: 16px 6px;
}
#customer {
  .crumbs {
    height: 40px;
    line-height: 40px;
    background: rgba(255, 255, 255, 1);
    border-radius: 4px;
    padding-left: 14px;
    font-size: 12px;
    color: #333333;
    .textColor {
      color: #999999;
    }
  }
  .list-box {
    // margin-top: 16px;
    background: #ffffff;
  }
  .explain-box {
    padding: 15px;
  }
  .con-title {
    color: #000000;
    font-size: 14px;
    height: 38px;
    line-height: 38px;
    span {
      display: inline-block;
      vertical-align: middle;
      font-weight: 500;
      font-size: 14px;
    }
    .line {
      width: 3px;
      height: 13px;
      background: linear-gradient(
        360deg,
        rgba(29, 105, 196, 1) 0%,
        rgba(139, 189, 252, 1) 100%
      );
      border-radius: 2px;
      margin-right: 8px;
    }
    .refresh {
      display: inline-block;
      img {
        width: 16px;
        height: 14px;
        margin-right: 8px;
      }
      font-size: 14px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: rgba(65, 132, 213, 1);
      line-height: 20px;
    }
  }
  ::v-deep  .el-select {
    top: -31px;
    left: 62px;
  }
  ::v-deep  .el-pagination__sizes .el-select {
    top: 0;
    left: 0;
  }
  ::v-deep  .el-select .el-input {
    width: 177px;
    height: 30px;
    font-size: 12px;
    color: #333;
    margin: 0 0px 12px 0;
  }
  ::v-deep  .el-select.row2 {
    margin-right: 65px;
  }

  .customer-main {
    .customer-form {
      overflow: hidden;
      ::v-deep  .el-form {
        margin-bottom: 20px;
        ::v-deep  .el-form-item {
          float: left;
          margin-bottom: 0;
          height: 45px;
          .el-form-item__label {
            font-size: 12px;
            background-color: #fff;
            color: #909399;
            vertical-align: middle;
            display: table-cell;
            text-align: center;
            position: relative;
            border: 1px solid #dcdfe6;
            border-radius: 4px 0 0 4px;
            padding: 0 6px;
            margin-top: 6px;
            white-space: nowrap;
            border-right: 0;
            color: #333;
            line-height: 28px;
          }
        }
      }
      ::v-deep  .el-form .el-form-item.searchBtn {
        float: right;
      }
      .el-input {
        width: 215px;
        height: 30px;
        font-size: 12px;
        color: #333;
        margin: 0 6px 5px 0;
        &:nth-of-type(4) {
          margin-right: 0;
        }
        & > div {
          font-size: 12px;
          color: #333333;
          padding: 6px 7px;
        }
        .el-input__inner {
          height: 30px;
          line-height: 30px;
        }
        .el-input__icon {
          line-height: 30px;
        }
      }

      a {
        display: inline-block;
        // float: left;
        display: inline-block;
        width: 56px;
        height: 30px;
        color: rgba(51, 51, 51, 0.5);
        border: none;
        box-sizing: border-box;
        font-size: 12px;
        font-family: 'PingFangSC-Medium,PingFang SC';
        text-align: center;
        line-height: 40px;
        margin-right: 6px;
        &.blue {
          color: #fff;
          background-color: #4184d5;
          border: 0;
        }
        &:hover,
        &:focus {
          opacity: 0.8;
        }
      }
    }
    .customer-tabs {
      .el-tabs__content {
        padding: 15px 0;
      }
      .el-tabs--border-card {
        border: 0;
        box-shadow: none;
      }
      .el-tabs--border-card > .el-tabs__header {
        background: none;
      }
      .el-tabs__item {
        font-size: 14px;
        color: #666;
        margin-top: 0;
        &.is-active {
          border: none;
          border: 1px solid #dcdfe6;
          border-radius: 4px 4px 0px 0px;
          color: #4184d5;
        }
        &:first-child {
          margin-left: 0px;
        }
        &:hover,
        &:focus {
          color: #4184d5;
        }
      }
      .customer-table-foot {
        padding: 15px 0;
         float: right;
      }
      .handle {
        // margin: 0;
        // height: 21px;
        // text-align: center;
        span{
          color: #4183d5;
        }
      }
      .el-tabs--border-card {
        border: 0;
        box-shadow: none;
      }
    }
  }
}
</style>
