import request from '@/utils/request';

//
export function getActivitylist(params) {
  return request({
    url: '/promo/couponActivity/list',
    method: 'post',
    data: params,
  });
}
export function getCouponDetail(params) {
  return request({
    url: '/promo/couponActivity/detail',
    method: 'post',
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
    data: params,
    transformRequest: [
      function (data) {
        const formData = new FormData();
        Object.keys(data).forEach((key) => {
          formData.append(key, data[key]);
        });
        return formData;
      },
    ],
  });
}
export function couponOffline(params) {
  return request({
    url: '/promo/couponActivity/offline',
    method: 'post',
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
    data: params,
    transformRequest: [
      function (data) {
        const formData = new FormData();
        Object.keys(data).forEach((key) => {
          formData.append(key, data[key]);
        });
        return formData;
      },
    ],
  });
}
export function couponActSave(params) {
  return request({
    url: '/promo/couponActivity/save',
    method: 'post',
    data: params,
  });
}
// 券列表查询
export function couponList(params) {
  return request({
    url: '/promo/coupon/list',
    method: 'post',
    data: params,
  });
}
// 编辑活动的提交
export function couponAcSave(params) {
  return request({
    url: '/promo/couponActivity/save',
    method: 'post',
    data: params,
  });
}
// 编辑活动的提交
export function couUserList(params) {
  return request({
    url: 'promo/coupon/user/list',
    method: 'post',
    data: params,
  });
}

// 导出
export function userExport(params) {
  return request({
    url: 'promo/coupon/user/export',
    method: 'get',
    params,
  });
}
