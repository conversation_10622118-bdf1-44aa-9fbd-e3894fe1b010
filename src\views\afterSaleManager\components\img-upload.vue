<template>
	<div>
		<div>
			<div class="upload-box" :style="btnStyle" @click="click" v-loading="loading">
				<input type="file" ref="input" style="display:none" :accept="allowFileType" @change="change">
				<div class="msg-box" style="padding:0 10px;">
					<p style="fontSize: 26px;">
						<span class="el-icon-upload2"></span>
					</p>
					<p style="fontSize:12px;">点击上传</p>
					<p v-if="msg" style="fontSize:12px;margin-top:5px;">{{ msg }}</p>
				</div>
			</div>
			<span class="clear" @click="clear" v-if="value.length > 0">清空</span>
		</div>
		<div style="margin-top:10px;">
			<img-list :maxShowLength="allowMaxFileCount" :value="value" :maxCol="3" :showNullEl="false" :deleteBtn="true" @input="input"></img-list>
		</div>
	</div>
</template>

<script>
import imgList from "./imgList.vue"
export default {
	components: {
		imgList
	},
	props: {
		msg: {
			type: String,
			default:''
		},
		allowMaxFileCount: {
			default: 5,
			type: Number
		},
		allowMaxFileSize: {
			default: 5,
			type: Number
		},
		btnStyle: {
			default: () => {},
			type: Object
		},
		allowFileType: {
			default: '.png,.jpg',
			type: String
		},
		/**
		 * 如果调用失败就resole(false),成功就返回对应图片地址
		 */
		upload: {
			default: async () => {},
			type: Function
		},
		value: {
			type: Array,
			default: () => []
		}
	},
	data() {
		return {
			loading: false,
		}
	},
	methods: {
		change() {
			const file = this.$refs.input.files[0];
			if (file) {
				if (file.size > 5000000) {
					this.$message.error(`最大上传${this.allowMaxFileSize}MB的文件`);
					return;
				}
				this.loading = true;
				this.upload(file).then(res => {
					if (res) {
						this.$emit("input", [...this.value, res])
					}
				}).finally(() => {
					this.loading = false;
				})
			} else {
				this.$message.warning("请选择文件");
			}
			this.$refs.input.value = '';
		},
		click() {
			if (this.value.length >= this.allowMaxFileCount) {
				this.$message.error(`最多上传${this.allowMaxFileCount}张凭证`)
				return;
			}
			this.$refs.input.click()
		},
		clear() {
			this.$emit('input', []);
		},
		input(res) {
			console.log(res);
			this.$emit('input', res)
		}
	}
}
</script>

<style>
.upload-box {
	position: relative;
	display: inline-block;
	width: 100px;
	height: 100px;
	border-radius: 5px;
	border: dashed 1px rgb(156, 156, 156);
	color: rgb(156, 156, 156);
	cursor: pointer;
	font-size: 0;
	line-height: auto;
	transition: all 0.2s;
}
.upload-box:hover {
	border-color: rgb(0, 131, 218);
	color: rgb(0, 131, 218);
}
.upload-box .msg-box {
	position: absolute;
	width: 100%;
	font-weight: 200;
	top: 50%;
	transform: translateY(-50%);
}
.clear {
	margin-left:10px;
	cursor:pointer;
	color:rgb(184, 184, 184)
}
.clear:hover {
	color: rgb(218, 0, 18);
}
.upload-box .msg-box p {
	position: relative;
	text-align: center;
	margin: 0;
	line-height: normal;
}
</style>