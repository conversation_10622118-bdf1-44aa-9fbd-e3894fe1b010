import layout from '@/layout';

const settlementManage = {
  path: '/settlementManage',
  name: 'settlementManage',
  component: layout,
  meta: {
    title: '结算管理',
    icon: 'el-icon-s-claim',
  },
  children: [
    {
      path: '/settlementBill',
      name: 'settlementBill',
      component: () => import('@/views/settlement/settlementBill'),
      meta: { title: '结算单' },
    },
    {
      path: '/settleDetails',
      name: 'settleDetails',
      hidden: true,
      component: () => import('@/views/settlement/settleDetails'),
    }, // 结算单明细
    {
      path: '/bill',
      name: 'bill',
      component: () => import('@/views/settlement/bill'),
      meta: { title: '账单' },
    },
    {
      path: '/withdrawalRecord',
      name: 'withdrawalRecord',
      component: () => import('@/views/settlement/withdrawalRecord/withdrawalRecord'),
      meta: { title: '提现记录' },
    },
    {
      path: '/settlementOnlineList',
      name: 'settlementOnlineList',
      component: () => import('@/views/settlement/onlineList'),
      meta: { title: '在线支付结算单' },
    },
    {
      path: '/settlementOfflineList',
      name: 'settlementOfflineList',
      component: () => import('@/views/settlement/offlineList'),
      meta: { title: '线下转账结算单' },
    },
    {
      path: '/commissionRecord',
      name: 'commissionRecord',
      component: () => import('@/views/settlement/commissionRecord'),
      meta: { title: '佣金缴纳记录', badge: '待处理' },
    },
    {
      path: '/invoiceRecord',
      name: 'invoiceRecord',
      component: () => import('@/views/settlement/invoiceApply/invoiceRecord'),
      meta: { title: '发票申请' },
    },
    {
      path: '/settlementOnlineDetail',
      name: 'settlementOnlineDetail',
      component: () => import('@/views/settlement/onlineDetail'),
      hidden: true,
      meta: { noCache: true },
    },
    {
      path: '/settlementOfflineDetail',
      name: 'settlementOfflineDetail',
      component: () => import('@/views/settlement/offlineDetail'),
      hidden: true,
    },
    {
      path: '/commissionDetail',
      name: 'commissionDetail',
      component: () => import('@/views/settlement/commissionDetail'),
      hidden: true,
    },
    {
      path: '/invoiceDemandDetail',
      name: 'invoiceDemandDetail',
      component: () => import('@/views/settlement/invoiceApply/invoiceDemandDetail'),
      hidden: true,
    },
    {
      path: '/invoiceRecordDetail',
      name: 'invoiceRecordDetail',
      component: () => import('@/views/settlement/invoiceApply/invoiceRecordDetail'),
      hidden: true,
    },
    {
      path: '/withdrawalRecordDetail',
      name: 'withdrawalRecordDetail',
      component: () => import('@/views/settlement/withdrawalRecord/withdrawalRecordDetail'),
      hidden: true,
    },
    {
      path: '/marginAccount',
      name: 'marginAccount',
      component: () => import('@/views/settlement/marginAccount/index'),
      meta: { title: '保证金管理' },
    },
    {
      path: '/marketingServiceQuota',
      name: 'marketingServiceQuota',
      component: () => import('@/views/settlement/marketingServiceQuota/index'),
      meta: { title: '营销服务额度' },
    },
  ],
};
export default settlementManage;
