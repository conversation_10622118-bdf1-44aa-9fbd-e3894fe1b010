<template>
  <div>
    <el-form
      ref="basic"
      label-width="100px"
      size="small"
      label-position="right"
      :rules="basicRules"
      :model="basic"
    >
      <el-form-item
        label="企业名称"
        class="width50"
        prop="name"
      >
        <span>{{ basic.name }}</span>
      </el-form-item>
      <el-form-item
        label="营业执照号"
        class="width50"
        prop="regCode"
      >
        <span>{{ basic.regCode }}</span>
      </el-form-item>
      <el-form-item
        label="企业类型"
        class="width50"
        prop="corporationType"
      >
        <span>{{ basic.corporationType === 0 ? '经营企业' : '生产企业' }}</span>
      </el-form-item>
      <el-form-item
        label="企业地址"
        class="width50"
        prop="addr"
      >
        <div>
          <el-form-item class="width25">
            <el-select
              v-model="basic.provId"
              placeholder="省"
              @change="selectChange($event, 'cityList')"
            >
              <el-option
                v-for="(item, index) in provList"
                :key="index"
                :label="item.areaName"
                :value="item.areaCode"
              />
            </el-select>
          </el-form-item>
          <el-form-item class="width25">
            <el-select
              v-model="basic.cityId"
              placeholder="市"
              @change="selectChange($event, 'areaList')"
            >
              <el-option
                v-for="(item, index) in cityList"
                :key="index"
                :label="item.areaName"
                :value="item.areaCode"
              />
            </el-select>
          </el-form-item>
          <el-form-item class="width25">
            <el-select
              v-model="basic.areaId"
              placeholder="区"
              @change="selectChange($event, 'streeList')"
            >
              <el-option
                v-for="(item, index) in areaList"
                :key="index"
                :label="item.areaName"
                :value="item.areaCode"
              />
            </el-select>
          </el-form-item>
          <el-form-item class="width25">
            <el-select
              v-model="basic.streetId"
              placeholder="街道"
            >
              <el-option
                v-for="(item, index) in streeList"
                :key="index"
                :label="item.areaName"
                :value="item.areaCode"
              />
            </el-select>
          </el-form-item>
        </div>
        <div>
          <el-input
            v-model="basic.addr"
            type="text"
            placeholder="请输入营业执照上的注册地址"
          />
        </div>
      </el-form-item>
      <el-form-item
        label="店铺LOGO"
        prop="logoUrl"
      >
        <el-upload
          class="avatar-uploader"
          action=""
          :http-request="uploadImg"
          :before-upload="beforeAvatarUpload"
          :show-file-list="false"
        >
          <img
            v-if="basic.logoUrl"
            :src="basic.logoUrl"
            class="avatar"
          >
          <i
            v-else
            class="el-icon-plus avatar-uploader-icon"
          />
          <div
            slot="tip"
            class="el-upload__tip"
          >
            不超过1MB，支持jpg、png格式
          </div>
        </el-upload>
      </el-form-item>
      <el-form-item
        label="客服电话："
        class="width50"
        prop="customerServicePhone"
      >
        <el-input
          v-model="basic.customerServicePhone"
          type="text"
          placeholder="请输入企业客服电话，方便客户联系"
        />
      </el-form-item>
      <el-form-item
        label="固定电话："
        class="width50"
      >
        <el-input
          v-model="basic.fixedPhone"
          type="text"
          placeholder="请输入企业固定电话"
        />
      </el-form-item>
      <el-form-item
        label="电子邮件："
        class="width50"
      >
        <el-input
          v-model="basic.email"
          type="text"
          placeholder="请输入企业电子邮箱"
        />
      </el-form-item>
      <el-form-item
        label="网址："
        class="width50"
      >
        <el-input
          v-model="basic.web"
          type="text"
          placeholder="请输入企业网站地址"
        />
      </el-form-item>
      <el-form-item
        label="简介："
        class="width50"
      >
        <el-input
          v-model="basic.brief"
          type="textarea"
          placeholder="请输入企业简介"
        />
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
export default {
  name: 'qualtificationDetail',
  props: {},
};
</script>

<style scoped lang="scss"></style>
