import request from '../../index';

/**
 * 获取账单列表数据
 * @param {参数} params
 */
export function getBillDatas(params) {
  return request.get('/sellerOfflinePopBillSettlement/v1/billList', params);
}
/**
 * 获取账单导出条数
 * @param {参数} params
 */
export function getBillDataNums(params) {
  return request.get('/sellerOfflinePopBillSettlement/v1/queryOfflineExprotBillListCount', params);
}
/**
 * 获取账单明细导出条数
 * @param {参数} params
 */
export function getBillDetailNums(params) {
  return request.get('/sellerOfflinePopBillSettlement/v1/queryOfflineExprotBillDetailCount', params);
}
/**
 * 获取账单价格信息
 * @param {参数} params
 */
export function getBillInfo(params) {
  return request.get('/sellerOfflinePopBillSettlement/v1/queryOfflinePopBillPayStatis', params);
}

/**
 * 获取账单明细列表
 * @param {参数} params
 */
export function getBillDetailList(params) {
  return request.get('/sellerOfflinePopBillSettlement/v1/billDetail', params);
}
/**
 * 获取账单明细数据
 * @param {参数} params
 */
export function getBillDetailInfo(params) {
  return request.get('/sellerOfflinePopBillSettlement/v1/billPayment', params);
}

/**
 * 获取结算单列表
 * @param {参数} params
 */
export function getOfflineSettleDatas(params) {
  return request.get('/sellerOfflinePopBillSettlement/v1/listSettlement', params);
}

/**
 * 获取结算单价格信息
 * @param {参数} params
 */
export function getOfflineSettleInfo(params) {
  return request.get('/sellerOfflinePopBillSettlement/v1/queryPopSettlementStatis', params);
}
/**
 * 获取结算单导出条数
 * @param {参数} params
 */
export function getOfflineSettleDataNums(params) {
  return request.get('/sellerOfflinePopBillSettlement/v1/querySettlementExprotListCount', params);
}

/**
 * 获取打款信息
 * @param {参数} params
 */
export function getPriceInfo() {
  return request.get('/accountStatementOffline/statementMoney');
}

/**
 * 线下支付结算单-导出结算单
 */
export function exportOfflineSettlementList(params) {
  return request.get('/sellerOfflinePopBillSettlement/async/exportOfflineSettlementList', params);
}

/**
 * 线下支付结算单-导出账单
 */
export function exportOfflineBillPaymemtList(params) {
  return request.get('/sellerOfflinePopBillSettlement/async/exportOfflineBillPaymemtList', params);
}

/**
 * 线下支付结算单-导出账单明线
 */
export function exportOfflinePaymemtBillDetailList(params) {
  return request.get('/sellerOfflinePopBillSettlement/async/exportOfflinePaymemtBillDetailList', params);
}
