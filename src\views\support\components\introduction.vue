<template>
  <div class="introduction-box">
    <h3><i class="type-icon">OTC</i>{{ info.showName }}</h3>
    <div class="price-box">
      <el-form>
        <el-form-item
          label="含 税 价"
        >
          <span class="lg-label">￥{{ info.fob }}</span>
        </el-form-item>
        <el-row>
          <el-col :span="8">
            <el-form-item>
              <label
                class="el-form-item__label"
                v-html="`原&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;价`"
              />￥{{ info.fob || info.fob === 0 ? (info.fob * 1.2).toFixed(2) : '' }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              label="建议零售价"
            >
              <span class="red-label">￥{{ info.suggestPrice }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              label="毛 利 率"
            >
              <span class="red-label">{{
                Number(info.grossMargin) ? `${Number(info.grossMargin) * 100}%` : ''
              }}</span>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <i class="operate-icon" />
    </div>
    <div class="info-box">
      <el-form>
        <el-row>
          <el-col :span="12">
            <el-form-item>
              <label
                class="el-form-item__label"
                v-html="`规&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;格`"
              />{{ info.spec }}
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item>
              <label
                class="el-form-item__label"
                v-html="`剂&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;型`"
              />{{ info.dosageForm }}
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item
              label="有效期至"
              label-width="68px"
            >
              近至 {{ info.nearEffect }} 远至 {{ info.farEffect }}
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              label="件 装 量"
            >
              {{ info.pieceLoading }}{{ info.productUnit }}
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="批准文号">
              {{ info.approvalNumber }}
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              label="中 包 装"
            >
              {{ info.mediumPackageNum }}{{ info.productUnit
              }}{{ info.isSplit === 1 ? '（可拆零）' : '（不可拆零）' }}
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="生产厂家">
          {{ info.manufacturer }}
        </el-form-item>
        <el-form-item
          label="购买数量"
        >
          <i class="amount-icon" /> 当前库存：大于1000{{ info.productUnit }}
        </el-form-item>
        <el-form-item
          label-width="65px"
          class="btn-box"
        >
          <el-button>加入采购单</el-button><i class="collection-icon" />
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    info: {
      type: Object,
      default: () => ({
        showName: '', // 名称
        fob: '', // 含税价
        suggestPrice: '', // 建议零售价
        grossMargin: '', // 毛利率
        spec: '', // 规格
        dosageForm: '', // 剂型
        nearEffect: '', // 有效期 近至
        farEffect: '', // 有效期 远至
        pieceLoading: '', // 件装量
        approvalNumber: '', // 批准文号
        mediumPackageNum: '', // 中包装
        manufacturer: '', // 生产厂家
        productUnit: '', // 单位
        isSplit: '', // 是否可拆零 1 是 0 否
      }),
    },
  },
};
</script>

<style lang="scss" scoped>
.introduction-box {
  padding: 10px 25px 10px 10px;
  box-sizing: border-box;
  h3 {
    font-size: 18px;
    font-family: PingFangSC, PingFangSC-Medium;
    font-weight: 500;
    color: #333333;
    height: 25px;
    line-height: 25px;
    position: relative;
    padding-left: 50px;
    i.type-icon {
      background: #ff5b5b;
      border-radius: 2px;
      font-size: 12px;
      font-family: PingFangSC, PingFangSC-Regular;
      font-weight: 400;
      text-align: center;
      color: #ffffff;
      font-style: normal;
      line-height: 17px;
      padding: 0 5px;
      position: absolute;
      top: 4px;
      left: 0;
    }
  }
  .price-box {
    background: rgba(245, 245, 245, 0.6);
    border-radius: 2px;
    padding: 10px;
    box-sizing: border-box;
    margin-top: 5px;
    position: relative;
    .el-form {
      .el-form-item {
        margin-bottom: 10px;
        ::v-deep  .el-form-item__label {
          height: 30px;
          line-height: 30px;
          font-size: 12px;
          font-family: PingFangSC, PingFangSC-Regular;
          font-weight: 400;
          color: #666666;
          white-space: pre-wrap;
        }
        ::v-deep  .el-form-item__content {
          height: 30px;
          line-height: 30px;
          font-size: 12px;
          font-family: PingFangSC, PingFangSC-Regular;
          font-weight: 400;
          color: #333333;
          span.lg-label {
            font-size: 15px;
            font-family: PingFangSC, PingFangSC-Medium;
            font-weight: 500;
            color: #ff5b5b;
          }
          span.red-label {
            color: #ff5b5b;
          }
        }
      }
    }
    .operate-icon {
      background: url(../../../assets/image/product/operate-icon.png) 0 0 no-repeat;
      width: 100px;
      height: 11px;
      display: inline-block;
      position: absolute;
      right: 20px;
      top: 22px;
    }
  }
  .info-box {
    padding: 10px;
    .el-form {
      .el-form-item {
        margin-bottom: 10px;
        font-size: 12px;
        font-family: PingFangSC, PingFangSC-Regular;
        font-weight: 400;
        color: #666;
        ::v-deep  .el-form-item__label {
          min-height: 30px;
          line-height: 30px;
        }
        ::v-deep  .el-form-item__content {
          min-height: 30px;
          line-height: 30px;
          .amount-icon {
            background: url(../../../assets/image/product/amount-icon.png) 0 0 no-repeat;
            width: 115px;
            height: 29px;
            float: left;
            margin-right: 10px;
          }
        }
        &.btn-box {
          ::v-deep  .el-form-item__content {
            height: 45px;
            line-height: 45px;
            overflow: hidden;

            .el-button {
              background: #00c675;
              border-radius: 2px;
              font-size: 15px;
              font-family: PingFangSC, PingFangSC-Medium;
              font-weight: 500;
              color: #ffffff;
              float: left;
              padding: 0 20px;
              line-height: 41px;
            }
            i.collection-icon {
              background: url(../../../assets/image/product/collection-icon.png) 0 0 no-repeat;
              width: 43px;
              height: 43px;
              float: left;
              margin-left: 10px;
            }
          }
        }
      }
    }
  }
}
</style>
