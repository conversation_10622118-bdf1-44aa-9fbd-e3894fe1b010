import layout from '@/layout';

const tradeManage = {
  path: '/tradeManage',
  name: 'tradeManage',
  component: layout,
  meta: {
    title: '交易管理',
    icon: 'el-icon-s-order',
  },
  children: [
    {
      path: '/orderList',
      name: 'orderList',
      component: () => import('@/views/order/orderList'),
      meta: { title: '订单管理' },
    },
    {
      path: '/afterSaleList',
      name: 'afterSalesIndex',
      component: () => import('@/views/afterSale/afterSaleList'),
      meta: { title: '退款管理' },
    },
	{
		path: '/afterSaleManager',
		name: 'afterSaleManager',
		component: () => import('@/views/afterSaleManager'),
		meta: {title: '售后管理',showRedDotNum: true, redDotNumKey: 'waitSellerHandleCount'}
	},
  {
    path: '/urgeDelivery',
    name: 'urgeDelivery',
    component: () => import('@/views/urge-delivery'),
    meta: {title: '催发货管理',showRedDotNum: true, redDotNumKey: 'pendingShipmentReminderQuantity'}
  },
	{
		path: '/afterSaleManager/detail',
		name: 'afterSaleManagerDetail',
		component: () => import('@/views/afterSaleManager/detail.vue'),
		hidden: true,
		meta: { title: '售后详情' }
	},
    // {
    //   path: '/downloadList',
    //   name: 'downloadList',
    //   component: () => import('@/views/other/downloadList'),
    //   meta: {title: '文件下载中心'}
    // },
    {
      path: '/orderList/delivery',
      name: 'orderListDelivery',
      component: () => import('@/views/order/theDelivery'),
      hidden: true,
    },
    {
      path: '/orderList/refund',
      name: 'orderListRefund',
      component: () => import('@/views/order/refundList'),
      meta: { noCache: true },
      hidden: true,
    },
    {
      path: '/orderList/refundNew',
      name: 'orderListRefundNew',
      component: () => import('@/views/order/refundListNew'),
      meta: { noCache: true },
      hidden: true,
    },
    {
      path: '/prd',
      name: 'prd',
      component: () => import('@/views/order/invoicePdf'),
      hidden: true,
    },
  ],
};
export default tradeManage;
