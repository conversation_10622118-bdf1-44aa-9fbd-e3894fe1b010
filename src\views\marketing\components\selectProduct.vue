<template>
  <span>
    <el-button type="primary" size="mini" @click="open">{{ type == 2 ? '添加主品' : '添加赠品' }}</el-button>
    <el-dialog :title="type == 2 ? '添加主品' : '添加赠品'" :visible.sync="showProductsModal" width="1200px" append-to-body>
      <el-form label-position="right" label-width="10px" style="margin-top:10px;">
        <el-row>
          <el-col :xs="16" :md="12" :lg="5" :xl="5">
            <el-form-item style="margin: 0;">
              <el-input placeholder="CSU ID" size="mini" v-model="queryForm.csuid" clearable>
                <template slot="prepend">CSU ID</template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :xs="16" :md="12" :lg="5" :xl="5">
            <el-form-item style="margin: 0;">
              <el-input placeholder="商品编码" size="mini" v-model="queryForm.barcode" clearable>
                <template slot="prepend">商品编码</template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :xs="16" :md="12" :lg="5" :xl="5">
            <el-form-item style="margin: 0;">
              <el-input placeholder="erp编码" size="mini" v-model="queryForm.erpCode" clearable>
                <template slot="prepend">erp编码</template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :xs="16" :md="12" :lg="5" :xl="5">
            <el-form-item style="margin: 0;">
              <el-input placeholder="商品名称" size="mini" v-model="queryForm.showName" clearable>
                <template slot="prepend">商品名称</template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :xs="16" :md="12" :lg="5" :xl="5">
            <el-form-item style="margin: 0;">
              <el-input class="input-with-select" size="mini">
                <template slot="prepend">
                  <span>商品状态</span>
                </template>
                <template slot="append">
                  <el-select style="margin-left:0px;" v-model="queryForm.status" size="mini" clearable>
                    <el-option label="全部" :value="null" />
                    <el-option label="待上架" value="6" />
                    <el-option label="销售中" value="1" />
                    <el-option label="已下架" value="4" />
                  </el-select>
                </template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :xs="16" :md="12" :lg="5" :xl="5">
            <el-form-item style="margin: 0;">
              <el-input placeholder="商品来源" class="input-with-select" size="mini">
                <template slot="prepend">
                  <span>商品来源</span>
                </template>
                <template slot="append">
                  <el-select style="margin-left: 0;" v-model="queryForm.activityType" size="mini" clearable>
                    <el-option label="全部" :value="null" />
                    <el-option label="普通商品" value="0" />
                    <el-option v-if="type == 2" label="拼团商品" value="1"></el-option>
                    <el-option v-if="type == 2" label="批购包邮商品" value="3"></el-option>
                    <el-option v-if="type != 2" label="赠品" value="2" />
                  </el-select>
                </template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :xs="16" :md="12" :lg="5" :xl="5">
            <el-form-item style="margin: 0;">
              <el-input class="input-with-select" size="mini">
                <template slot="prepend">
                  <span>是否近效期</span>
                </template>
                <template slot="append">
                  <el-select style="margin-left:0px;" v-model="queryForm.nearTerm" size="mini" clearable>
                    <el-option label="全部" value="" />
                    <el-option label="是" :value="true" />
                    <el-option label="否" :value="false" />
                  </el-select>
                </template>
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div style="margin: 0 0 10px 10px;">
        <el-button type="primary" size="mini" @click="queryForm.page = 1;handleSearch();">查询</el-button>
        <el-button type="normal" size="mini" @click="handleReset">重置</el-button>
      </div>
      <div v-loading="loading">
        <el-table ref="table" :data="list" border height="600px" @selection-change="(rows) => { handleSelectionChange(rows, true) }" row-key="barcode">
          <el-table-column type="selection" width="55" align="center" reserve-selection>
          </el-table-column>
          <el-table-column label="商品信息" width="350">
            <div slot-scope="scope" style="display: flex;align-items: center;gap: 10px;">
              <div>
                <el-image
                  style="width: 70px; height: 70px;border-radius: 5px;overflow: hidden;background-color: #f3f3f3;"
                  fit="cover"
                  :src="scope.row.fullImageUrl"
                  :preview-src-list="[scope.row.fullImageUrl]">
                </el-image>
              </div>
              <div>
                <p class="keyValue">
                  <span>CSUID</span>：
                  <span>{{ scope.row.csuid }}</span>
                </p>
                <p class="keyValue">
                  <span>商品编码</span>：
                  <span>{{ scope.row.barcode }}</span>
                </p>
                <p class="keyValue">
                  <span>ERP编码</span>：
                  <span>{{ scope.row.erpCode }}</span>
                </p>
                <p class="keyValue">
                  <span>商品名称</span>：
                  <span>{{ scope.row.showName }}</span>
                </p>
                <p class="keyValue">
                  <span>规格</span>：
                  <span>{{ scope.row.spec }}</span>
                </p>
                <p class="keyValue">
                  <span>中包装数量</span>：
                  <span>{{ scope.row.mediumPackageNum }}{{ scope.row.isSplit == 1 ?  '(可拆零)' : '(不可拆零)'}}</span>
                </p>
                <p class="keyValue">
                  <span>生产厂家</span>：
                  <span>{{ scope.row.manufacturer }}</span>
                </p>
              </div>
            </div>
          </el-table-column>
          <el-table-column align="center" label="药帮忙价格" width="150">
            <template slot-scope="scope">
              <p class="keyValue">
                <span style="width: 45px;">单体价</span>：
                <span>{{ scope.row.fob }}</span>
              </p>
              <p class="keyValue">
                <span style="width: 45px;">连锁价</span>：
                <span>{{ scope.row.chainPrice }}</span>
              </p>
            </template>
          </el-table-column>
          <el-table-column align="center" label="可售库存" width="200">
            <template slot-scope="scope">
              <p>{{ scope.row.availableQty }}</p>
              <p class="keyValue">
                <span style="width: 30px;">近至</span>：
                <span>{{ scope.row.nearEffect }}</span>
              </p>
              <p class="keyValue">
                <span style="width: 30px;">远至</span>：
                <span>{{ scope.row.farEffect }}</span>
              </p>
            </template>
          </el-table-column>
          <el-table-column align="center" label="商品状态">
            <template slot-scope="{ row }">
              {{ {1: '销售中', 4: '下架', 6: '待上架', 8: '待审核', 9: '审核未通过', 20: '删除' }[row.status] }}
            </template>
          </el-table-column>
          <el-table-column align="center" label="是否近效期">
            <template slot-scope="{ row }">
              {{ row.expired ? '是' : '否' }}
            </template>
          </el-table-column>
          <el-table-column align="center" label="商品来源">
            <template slot-scope="{ row }">
              {{ {1: '拼团商品', 2: '赠品', 0: '普通商品', 3:'批购包邮商品' }[row.activityType] }}
            </template>
          </el-table-column>
        </el-table>
        <div style="display: flex;justify-content: end;margin: 5px 0;">
          <el-pagination
            background
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            layout="total, prev, pager, next, sizes"
            :page-sizes="[5, 10, 15, 20]"
            :total="queryForm.total">
          </el-pagination>
        </div>
      </div>
      <template slot="footer">
        <el-button type="normal" size="mini" @click="showProductsModal = false;">取消</el-button>
        <el-button type="primary" size="mini" @click="submit">确定</el-button>
      </template>
    </el-dialog>
  </span>
</template>
<script>
import { getProductList } from '@/api/product';
export default {
  props: {
    value: {
      default: () => [],
      type: Array,
    },
    maxCount: {
      default: 10,
      type: Number
    },
    // 0: 赠品包    1: 赠品池       2:主品
    type: {
      default: 0,
      type: Number,
    }
  },
  data() {
    return {
      showProductsModal: false,
      list: [],
      queryForm: {
        csuid: '',
        barcode: '',
				showName: '',
        status: null,
        activityType: null,
        page: 1,
        nearTerm: '',
        erpCode: '',
        pageSize: 10,
        total: 0,
      },
      selection: [],
      loading: false,
      target: false,   //选择商品时用的
    };
  },
  /* mounted() {
    this.showProductsModal = false;
  }, */
  methods: {
    open() {
      this.showProductsModal = true;
      this.handleReset();
      const timer = setTimeout(() => {
        this.$refs.table.clearSelection();
        this.value.forEach(item => {
          this.$refs.table.toggleRowSelection(item, true)
        })
        clearTimeout(timer)
      },0)
    },
    handleReset() {
      this.queryForm = {
        csuid: '',
        barcode: '',
				showName: '',
        status: null,
        activityType: null,
        page: 1,
        pageSize: 10,
        total: 0,
      }
      this.list = [];
      this.handleSearch();
    },
    handleSearch() {
      if (this.loading) return;
      const form = { ...this.queryForm };
      form['statuses'] = form.status ? null : '1,4,6';
      form['activityTypes'] = this.type == 2 ? '0,1,3' : '0,2';
      form.total = 0;
      this.loading = true;
      getProductList({
        ...form,
        rows: form.pageSize
      }).then(res => {
        if (res.code == 0) {
          this.list = res.result.list;
          this.queryForm.total = res.result.total;
        } else {
          this.$message.error(res.msg)
        }

      }).finally(() => {
        this.loading = false;
      })

    },
    handleSizeChange(pageSize) {
      this.queryForm.pageSize = pageSize;
      this.handleSearch();
    },
    handleCurrentChange(pageNum) {
      this.queryForm.page = pageNum;
      this.handleSearch();
    },
    handleSelectionChange(selected) {
      if (this.target && selected.length <= this.maxCount) this.target = false;
      if (this.target) return
      console.log(selected);
      if (selected.length > this.maxCount) {
        this.target = true;
        this.$message.error(`最多选择${this.maxCount}个商品`);
        for (let i = selected.length; i > this.maxCount; i--) {
          this.$refs.table.toggleRowSelection(selected[i - 1], false);
        }
        this.target = false;
        return;
      }
      const rows = selected.filter(item => !this.value.some(val => val.csuid == item.csuid));
      let curList = [];
      switch(this.type) {
        case 0:
          //赠品包
          curList = rows.map(item => {
            return {
              skuId: item.csuid,
              everyQty: 1,
              giveQty: -1,
              tempRadio: 0,
              ...item
            }
          })
          break;
        case 1:
          //赠品池
          curList = rows.map(item => {
            return {
              skuId: item.csuid,
              giveQty: -1,
              everyQty: '',
              tempRadio: 0,
              tempRadio2: 0,
              tempRadio3: 0,
              orderGiveMinQty: -1,
              orderGiveMaxQty: -1,
              ...item
            }
          })
          break;
        default:
          curList = rows;
      }
      this.selection = [...this.value, ...curList];
    },
    submit() {
      this.$emit('input', [...this.selection]);
      this.showProductsModal = false;
    }
  },
}
</script>
<style scoped>
p {
  margin: 0;
}
::v-deep   .el-dialog {
  border-radius: 5px;
  overflow: hidden;
}
::v-deep   .el-dialog__header {
  padding: 15px;
  background-color: rgb(243, 243, 243);
}
::v-deep   .el-dialog__header span {
  font-size: 14px;
  line-height: normal;
}
::v-deep   .el-dialog__header button {
  right: 15px;
  top: 15px;
}
::v-deep   .el-dialog__body {
  padding: 5px 15px;
}
.keyValue {
  display: flex;
}
.keyValue span:first-child {
  width: 70px;
  flex-shrink: 0;
  text-align: justify;
  text-align-last: justify;
}
.keyValue span:last-child {
  display: -webkit-box;
  -webkit-box-orient: vertical;
	-webkit-line-clamp: 2;
  overflow: hidden;
	text-overflow: ellipsis;
}
.input-with-select ::v-deep   input {
    padding: 0 !important;
    border-right: none;
  }
  .input-with-select > ::v-deep   input {
    width: 0px !important;
  }
  ::v-deep   .el-input-group__prepend {
    width: auto;
  }
  ::v-deep   .el-input-group__append {
    background: white;
    width: 100%;
  }
  ::v-deep   .el-input-group__append > div {
    width: 100%;
  }

</style>
