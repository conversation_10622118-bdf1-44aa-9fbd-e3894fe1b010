<template>
  <div>
    <el-dialog
      class="fk-el-dialog"
      title="标品信息更新提醒"
      width="800px"
      :visible.sync="visible"
      :close-on-click-modal="false"
    >
      <div class="goods-check-alert-conten">
        <el-row>
          <el-col
            v-for="(item, index) in pageDatas"
            :key="item.key"
            :span="item.row"
          >
            <div class="goods-check-alert-conten-col">
              <div class="goods-check-alert-conten-title">
                {{ item.name }}
              </div>
              <div class="goods-check-alert-conten-row">
                <div v-if="Object.keys(item.data).length > 1">
                  <div class="line-row">
                    <div>商家:</div>
                    <div
                      v-if="item.key !== 'imagesList'"
                      style="margin-left: 5px"
                      :class="
                        item.data.pt === item.data.sj ? '' : 'red-content'
                      "
                    >
                      {{ item.data.sj ? item.data.sj : '-' }}
                    </div>
                    <div v-else>
                      <el-image
                        v-if="item.data.sj && item.data.sj.length > 0"
                        :src="showImage(item.data.sj)"
                        style="width: 80px; height: 80px; margin-left: 10px"
                      >
                        <div slot="placeholder" class="image-slot">
                          加载中<span class="dot">...</span>
                        </div>
                      </el-image>
                    </div>
                  </div>
                  <div class="line-row">
                    <div>平台:</div>
                    <div
                      v-if="item.key !== 'imagesList'"
                      style="margin-left: 5px"
                      :class="
                        item.data.pt === item.data.sj ? '' : 'red-content'
                      "
                    >
                      {{ item.data.pt ? item.data.pt : '-' }}
                    </div>
                    <div v-else>
                      <el-image
                        v-if="item.data.pt"
                        :src="showImage(item.data.pt)"
                        style="width: 80px; height: 80px; margin-left: 10px"
                      >
                        <div slot="placeholder" class="image-slot">
                          加载中<span class="dot">...</span>
                        </div>
                      </el-image>
                    </div>
                  </div>
                </div>
                <div v-else>
                  {{ item.data.pt }}
                </div>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
      <div
        class="red-content"
        style="margin-top: 20px"
        v-if="oldData.source && oldData.source === 2"
      >
        提示：<br />
        1、建议您检查商品实物是否与当前展示信息相符，若不符合，可一键使用标品信息。<br />2、在您选择“一键使用最新的标品信息”前，前台展示的信息不会改变。
      </div>
      <span slot="footer">
        <el-button
          size="medium"
          style="margin-left: 20px"
          type="primary"
          @click="useGoodsDetai()"
          v-if="oldData.source && (oldData.source === 1 || oldData.source === 2)"
          >使用当前标品信息更新商品</el-button
        >
        <!-- <el-button
          size="medium"
          style="margin-left: 20px"
          @click="imFuckingKnow()"
          v-if="oldData.source && oldData.source === 2"
          >我知道了</el-button
        > -->
        <el-button
          size="medium"
          style="margin-left: 20px"
          @click="useOtherGoods()"
          >重新绑定其他标品</el-button
        >
        <el-button
          size="medium"
          style="margin-left: 20px"
          @click="jumpErrorEditPage()"
          >标品信息有误进行纠错</el-button
        >
      </span>
    </el-dialog>
    <goodsMoreCheckAlert
      ref="goodsMoreCheckAlert"
      :fromType="'goods-update-alert'"
      @finish="morefinish"
    ></goodsMoreCheckAlert>
  </div>
</template>

<script>
import goodsMoreCheckAlert from './goodsMoreCheckAlert.vue'
import { apiConfig, onekeybind, standMark } from '@/api/product'
export default {
  components: { goodsMoreCheckAlert },
  data() {
    return {
      visible: false,
      pageDatas: [],
      datas: undefined,
      oldData: undefined,
      bigImgUrlPrefix: '',
      row: undefined,
      barcode: undefined,
      from: undefined
    }
  },
  created() {
    apiConfig().then((res) => {
      if (res.data) {
        this.bigImgUrlPrefix = res.data.bigImgUrlPrefix // 商品大图地址前缀
      }
    })
  },
  methods: {
    open(datas, oldDatas, row, from, keys) {
      this.from = from
      this.barcode = row.barcode
      this.row = row
      this.pageDatas = this.getDatas(datas, oldDatas, keys)
      this.datas = datas
      this.oldData = oldDatas
      this.visible = true
    },
    imFuckingKnow(){
      this.visible = false;
      standMark({barcode:this.barcode}).then(res => {

      })
    },
    useGoodsDetai() {
      onekeybind({
        barcode: this.barcode ? this.barcode : this.oldData.barcode,
        standardProductId: this.oldData.standardProductId
      }).then((res) => {
        if (res.code === 0) {
          this.$message.success('绑定成功')
          this.visible = false
          this.$emit('sure')
        } else {
          this.$message.error(res.message)
        }
      })
    },

    morefinish() {
      this.$emit('sure')
    },

    showImage(img) {
      if (!img) {
        return ''
      } else {
        if (img.indexOf('http') !== -1) {
          return img
        } else {
          return `${this.bigImgUrlPrefix}${img}`
        }
      }
    },

    useOtherGoods() {
      this.visible = false
      this.oldData['firstCategory'] = this.oldData.businessFirstCategoryCode
      this.oldData['barCode'] = this.barcode
      this.$refs.goodsMoreCheckAlert.open(this.oldData, this.barcode, this.row)
    },
    jumpErrorEditPage() {
      this.visible = false
      const path = '/product/errorEdit'
      const obj = {
        productId: this.oldData.standardProductId,
        from: 'productList',
        verify: true,
        source: this.row.source,
        firstCategory: this.datas.businessFirstCategoryCode,
        barCode: this.row.barcode
      }
      window.openTab(path, obj)
    },

    checkDataWithKey(arr, keys) {
      let arrCopy = arr.filter((item) => {
        if (item.key === 'imagesList') {
          return true
        }
        return keys.includes(item.key)
      })
      return arrCopy
    },

    getDatas(datas, oldDatas, keys) {
      let datasc = [
        {
          key: 'erpcode',
          name: 'ERP编码',
          row: 24,
          data: { pt: datas.erpcode }
        },
        {
          key: 'commonName',
          name:
            datas.businessFirstCategoryCode.toString() === '100005'
              ? '医疗器械名称'
              : '通用名称',
          row: 12,
          data: { pt: datas.commonName, sj: oldDatas.commonName }
        },
        {
          key: 'productName',
          name: '商品名称',
          row: 12,
          data: { pt: datas.productName, sj: oldDatas.productName }
        },
        {
          key: 'spec',
          name: '规格',
          row: 12,
          data: { pt: datas.spec, sj: oldDatas.spec }
        },
        {
          key: 'manufacturer',
          name: '生产厂家',
          row: 12,
          data: { pt: datas.manufacturer, sj: oldDatas.manufacturer }
        },
		{
          key: 'productionAddress',
          name: '生产厂家地址',
          row: 12,
          data: { pt: datas.productionAddress, sj: oldDatas.productionAddress }
        },
        {
          key: 'approvalNumber',
          name: '批准文号',
          row: 12,
          data: { pt: datas.approvalNumber, sj: oldDatas.approvalNumber }
        },
        {
          key: 'code',
          name: '条码',
          row: 12,
          data: { pt: datas.code, sj: oldDatas.code }
        },
        {
          key: 'brand',
          name: '品牌',
          row: 12,
          data: { pt: datas.brand, sj: oldDatas.brand }
        },
        {
          key: 'drugClassification',
          name: '处方类型',
          row: 12,
          data: {
            pt: datas.drugClassificationName,
            sj: oldDatas.drugClassificationName
          }
        },
        {
          key: 'manufacturingLicenseNo',
          name:
            datas.businessFirstCategoryCode.toString() === '100005'
              ? '医疗器械注册证或备案凭证编号'
              : '生产许可证或备案凭证编号',
          row: 12,
          data: {
            pt: datas.manufacturingLicenseNo,
            sj: oldDatas.manufacturingLicenseNo
          }
        },
        {
          key: 'marketAuthor',
          name: '上市许可持有人',
          row: 12,
          data: { pt: datas.marketAuthor, sj: oldDatas.marketAuthor }
        },
		{
          key: 'marketAuthorAddress',
          name: '上市许可持有人地址',
          row: 12,
          data: { pt: datas.marketAuthorAddress, sj: oldDatas.marketAuthorAddress }
        },
        {
          key: 'dosageForm',
          name: '剂型',
          row: 12,
          data: { pt: datas.dosageForm, sj: oldDatas.dosageForm }
        },
        {
          key: 'storageCondition',
          name: '存储条件',
          row: 12,
          data: { pt: datas.storageCondition, sj: oldDatas.storageCondition }
        },
        {
          key: 'producer',
          name: '产地',
          row: 12,
          data: { pt: datas.producer, sj: oldDatas.producer }
        },
        {
          key: 'firstCategoryName',
          name: '一级分类',
          row: 12,
          data: {
            pt: datas.businessFirstCategoryName,
            sj: oldDatas.businessFirstCategoryName
          }
        },
        {
          key: 'productUnit',
          name: '包装单位',
          row: 12,
          data: { sj: datas.productUnit, pt: oldDatas.productUnit }
        },
        {
          key: 'imagesList',
          name: '商品图片',
          row: 24,
          data: { pt: datas.imagesList, sj: oldDatas.imagesList.urlVal }
        },
		{
          key: 'filingsAuthor',
          name: datas.businessFirstCategoryCode.toString() === '100005' ? '医疗器械注册人/备案人名称' : '化妆品备案人/注册人',
          row: 12,
          data: { pt: datas.filingsAuthor, sj: oldDatas.filingsAuthor }
        },
         {
          key: 'isCommissionProduction',
          name: '是否委托生产',
          row: 12,
          data: { pt: datas.isCommissionProduction, sj: oldDatas.isCommissionProduction }
        },
        {
          key: 'entrustedManufacturer',
          name: '受托生产厂家',
          row: 12,
          data: { pt: datas.entrustedManufacturer, sj: oldDatas.entrustedManufacturer }
        },
        {
          key: 'entrustedManufacturerAddress',
          name: '受托生产厂家地址',
          row: 12,
          data: { pt: datas.entrustedManufacturerAddress, sj: oldDatas.entrustedManufacturerAddress}
        }

      ]

      return this.checkDataWithKey(datasc, keys)
    }
  }
}
</script>

<style scoped>
.goods-check-alert-conten {
  padding: 0 10px;
}

.goods-check-alert-conten-title {
  width: 100px;
}

.goods-check-alert-conten-row {
  display: flex;
  flex-direction: row;
  margin-left: 20px;
}

.goods-check-alert-conten-col {
  margin-top: 15px;
  display: flex;
  flex-direction: row;
}
.line-row {
  display: flex;
  flex-direction: row;
}
.red-content {
  color: red;
}
</style>
