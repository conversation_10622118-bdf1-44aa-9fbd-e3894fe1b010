.crumbs {
  height: 40px;
  line-height: 40px;
  background: white;
  border-radius: 4px;
  padding-left: 14px;
  font-size: 12px;
  color: #333333;
}

.crumbs .textColor {
  color: #999999;
}

.crumbs a {
  color: #333333;
  text-decoration: none;
}

.con-title {
  padding-left: 15px;
  color: #000000;
  font-size: 14px;
  height: 38px;
  line-height: 38px;
}

.con-title span {
  display: inline-block;
  vertical-align: middle;
  font-weight: bold;
}

.con-title .line {
  width: 3px;
  height: 13px;
  background: -webkit-gradient(
    linear,
    left bottom,
    left top,
    from(#1d69c4),
    to(#8bbdfc)
  );
  background: linear-gradient(360deg, #1d69c4 0%, #8bbdfc 100%);
  border-radius: 2px;
  margin-right: 8px;
}

.list-box {
  margin-top: 16px;
  background: #ffffff;
  padding-bottom: 10px;
}

.explain-search {
  padding: 10px 15px 0;
}

.explain-table {
  padding: 0 15px 20px;
}

.noData {
  text-align: center;
  padding: 60px 0;
}

.noData .img-box {
  width: 288px;
  height: 170px;
  display: inline-block;
  padding: 0;
  margin: 0;
}

.noData .img-box img {
  width: 100%;
  height: 100%;
}

.noData p {
  line-height: 48px;
  font-size: 14px;
  color: #333333;
  padding: 0;
}

.lookBtn {
  display: inline-block !important;
  vertical-align: middle !important;
  padding-left: 15px !important;
  color: #4183d5 !important;
  cursor: pointer !important;
  font-size: 12px !important;
}

.list-box {
  position: relative;
}

.list-box .sample-box {
  position: absolute;
  top: 50px;
  right: 60px;
}

.list-box .sample-box h4 {
  font-size: 14px;
  color: #333333;
  padding-left: 20px;
}

.list-box .sample-box p {
  width: 347px;
  height: 200px;
}

.list-box .sample-box p img {
  width: 100%;
  height: 100%;
}

.box-flex {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: nowrap;
  flex-wrap: nowrap;
}
/*# sourceMappingURL=market.css.map */


