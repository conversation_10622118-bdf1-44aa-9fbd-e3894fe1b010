export class ExposureTool {

  constructor(scrollContainer, callback) {
    this.callback = callback;
    this.el = scrollContainer;
    this.monitorList = [];
    this.exposured = [];
    this.trackExposure = this.trackExposure.bind(this)
  }

  /**
   *
   * @param scrollContainer 带滚动条的容器
   * @param monitorElList 要监控的元素数组
   */
  begin(monitorElList) {
    this.monitorList = monitorElList;
    this.el.addEventListener('scroll', this.trackExposure);
    this.trackExposure();//进入的时候立即曝光一次
  }

  end() {
    if (this.el) {
      this.el.removeEventListener('scroll', this.trackExposure);
      this.exposured = [];
      this.monitorList = [];
    }
  }

  _isInViewPort(el) {
    const viewPortHeight = window.innerHeight || document.documentElement.clientHeight || document.body.clientHeight
    const top = el.getBoundingClientRect() && el.getBoundingClientRect().top
    return top <= viewPortHeight
  }

  trackExposure() {
    if (this.monitorList && this.monitorList.length > 0 && (this.exposured.length < this.monitorList.length)) { //有没曝光过的
      this.monitorList.forEach((item) => {
        if (this.exposured.includes(item)) return; //曝光过就算了
        const exposure = this._isInViewPort(item)
        if (exposure) {
          this.callback(item)
          this.exposured.push(item);
        }
      })
    }
  }
}

export class VNodeExposureTool extends ExposureTool {
  constructor(scrollContainer, callback) {
    super(scrollContainer, callback);
    const origFun = this._isInViewPort;
    this._isInViewPort = function (vel) {
      return origFun(vel.$el);
    };
  }
}
