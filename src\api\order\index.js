import request from '@/utils/request';

// 订单列表
export function listOrder(params) {
  return request({
    url: '/order/v2/listOrder',
    method: 'get',
    params,
  });
}

// 订单状态数
export function orderStatusCount(params) {
  return request({
    url: '/order/v2/orderStatusCount',
    method: 'get',
    params,
  });
}

// 订单详情列表
export function getOrderDetailList(params) {
  return request({
    url: '/order/v2/queryOrderDetailList',
    method: 'get',
    params,
  });
}

// 查询待处理退款单数
export function getRefundCount(params) {
  return request({
    url: '/afterSales/queryPendingRefundCount',
    method: 'get',
    params,
  });
}

// 重新下推
export function againIssued(params) {
  return request({
    url: '/order/v2/againIssued',
    method: 'post',
    data: params,
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
    transformRequest: [
      function (data) {
        const formData = new FormData();
        Object.keys(data).forEach((key) => {
          formData.append(key, data[key]);
        });
        return formData;
      },
    ],
  });
}

// 导出订单列表&明细列表
export function exportOrder(params) {
  return request({
    url: '/order/v2/saveExportTask',
    method: 'get',
    params: params,
  });
}

// 批量导入物流信息
export function batchImportLogisticsFromExcel(params) {
  const forms = new FormData();
  forms.append('file', params.file);
  return request({
    url: '/order/v2/batchImportLogisticsFromExcel',
    method: 'post',
    data: forms,
    headers: { 'Content-Type': 'multipart/form-data' },
  });
}

// 上传文件
export function uploadFDFS(params) {
  const forms = new FormData();
  forms.append('file', params.file);
  return request({
    url: '/uploadFile/uploadFDFS',
    method: 'post',
    data: forms,
    headers: { 'Content-Type': 'multipart/form-data' },
    // onUploadProgress: (progressEvent) => {
    //   const num = (progressEvent.loaded / progressEvent.total) * 100; // 百分比
    //   params.onProgress({ percent: num }); // 进度条
    // },
  });
}

// 上传发票
export function saveInvoice(params) {
  return request({
    url: '/order/v2/saveInvoice',
    method: 'post',
    data: params,
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
    transformRequest: [
      function (data) {
        const formData = new FormData();
        Object.keys(data).forEach((key) => {
          formData.append(key, data[key]);
        });
        return formData;
      },
    ],
  });
}

// 订单审核通过
export function updateOrderStatus(params) {
  return request({
    url: '/order/v2/updateOrderStatus',
    method: 'post',
    data: params,
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
    transformRequest: [
      function (data) {
        const formData = new FormData();
        Object.keys(data).forEach((key) => {
          formData.append(key, data[key]);
        });
        return formData;
      },
    ],
  });
}

export function getHostName(params) {
  return request({
    url: '/uploadFile/cdn/hostName',
    method: 'post',
    data: params,
  });
}

// 开户
export function openAcount(params) {
  return request({
    url: '/order/v2/openAccount',
    method: 'post',
    data: params,
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
    transformRequest: [
      function (data) {
        const formData = new FormData();
        Object.keys(data).forEach((key) => {
          formData.append(key, data[key]);
        });
        return formData;
      },
    ],
  });
}

// 编辑erp编码
export function editErpCode(params) {
  return request({
    url: '/order/v2/editErpCode',
    method: 'post',
    data: params,
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
    transformRequest: [
      function (data) {
        const formData = new FormData();
        Object.keys(data).forEach((key) => {
          formData.append(key, data[key]);
        });
        return formData;
      },
    ],
  });
}

// 编辑商家备注
export function addSellerRemark(params) {
  return request({
    url: '/order/v2/addSellerRemark',
    method: 'post',
    data: params,
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
    transformRequest: [
      function (data) {
        const formData = new FormData();
        Object.keys(data).forEach((key) => {
          formData.append(key, data[key]);
        });
        return formData;
      },
    ],
  });
}

// 批量编辑商家备注
export function batchAddSellerRemarks(params) {
  return request({
    url: '/order/v2/batchAddSellerRemarks',
    method: 'post',
    data: params,
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
    transformRequest: [
      function (data) {
        const formData = new FormData();
        Object.keys(data).forEach((key) => {
          formData.append(key, data[key]);
        });
        return formData;
      },
    ],
  });
}

// 获取订单状态
export function getOrderStatusrecords(params) {
  return request({
    url: '/order/v2/orderStatusrecords',
    method: 'get',
    params,
  });
}

// 获取包裹信息
export function getQueryLogisticsInfo(params) {
  return request({
    url: '/order/v2/queryLogisticsInfo',
    method: 'get',
    params,
  });
}

// 获取物流信息
export function getQueryTrackInfo(params) {
  return request({
    url: '/order/v2/queryTrackInfo',
    method: 'get',
    params,
  });
}

// 查询发货信息
export function getQueryDeliveryPageInfo(params) {
  return request({
    url: '/order/v2/queryDeliveryPageInfo',
    method: 'get',
    params,
  });
}

// 编辑物流信息
export function updateTrackInfo(params) {
  return request({
    url: '/order/v2/updateTrackInfo',
    method: 'post',
    data: params,
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
    transformRequest: [
      function (data) {
        const formData = new FormData();
        Object.keys(data).forEach((key) => {
          formData.append(key, data[key]);
        });
        return formData;
      },
    ],
  });
}

// 发货
export function saveDeliveryInfo(params) {
  return request({
    url: '/order/v2/saveDeliveryInfo',
    method: 'post',
    data: params,
  });
}

// 退货页面

export function queryCanRefundProductDetail(params) {
  return request({
    url: '/afterSales/queryCanRefundProductDetail',
    method: 'get',
    params,
  });
}

// 退款原因枚举

export function listRefundReason(params) {
  return request({
    url: '/afterSales/listRefundReason',
    method: 'get',
    params,
  });
}

// 退款提示

export function getOrderRefundAmount(params) {
  return request({
    url: '/afterSales/getOrderRefundAmount',
    method: 'post',
    data: params,
  });
}

// 退款提交
export function submitApplyRefundBySeller(params) {
  return request({
    url: '/afterSales/submitApplyRefundBySeller',
    method: 'post',
    data: params,
  });
}

// 查看发票
export function getInvoice(params) {
  return request({
    url: '/order/v2/getInvoice',
    method: 'get',
    params,
  });
}

// 查询物流枚举值
export function queryLogisticsWayEnum(params) {
  return request({
    url: '/order/v2/queryLogisticsWayEnum',
    method: 'get',
    params,
  });
}

// 发货前校验
export function validDelivery(params) {
  return request({
    url: '/order/v2/validDelivery',
    method: 'get',
    params,
  });
}

// 删除发票
export function delInvoice(params) {
  return request({
    url: '/order/v2/saveInvoice',
    method: 'post',
    data: params,
  });
}

// 查询超48小时未发货订单数
export function queryTimeoutOrderCount(params) {
  return request({
    url: '/order/v2/queryTimeoutOrderCount',
    method: 'get',
    params,
  });
}
// 查询超48小时未揽收订单数
export function queryTimeoutReceiptOrderCount(params) {
  return request({
    url: '/order/v2/queryTimeoutReceiptOrderCount',
    method: 'get',
    params,
  });
}
// 查询发货后超48小时未发货订单数
export function queryTimeoutReceiptOrderCountAfterShip(params) {
  return request({
    url: '/order/v2/queryTimeoutReceiptOrderCountAfterShip',
    method: 'get',
    params,
  });
}

// 查询业务员信息
export function queryBDInfoByMerchantId(params) {
  return request({
    url: '/order/v2/queryBDInfoByMerchantId',
    method: 'get',
    params,
  });
}

export function confirmOrderFinish(params) {
  return request({
    url: '/order/v2/confirmOrderFinish',
    method: 'post',
    data: params,
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
    transformRequest: [
      function (data) {
        const formData = new FormData();
        Object.keys(data).forEach((key) => {
          formData.append(key, data[key]);
        });
        return formData;
      },
    ],
  });
}

// 查询下推ERP失败订单数
export function queryPullFailedOrderCount(params) {
  return request({
    url: '/order/v2/queryPullFailedOrderCount',
    method: 'get',
    params,
  });
}

// 获取多个erp客户信息
export function getListByName(params) {
  return request({
    url: '/orgUserRelation/getListByName',
    method: 'get',
    params,
  });
}

// 订单汇款审核
export function confirmReceipt(params) {
  return request({
    url: '/order/v2/confirmReceipt',
    method: 'post',
    data: params,
  });
}

//
export function evidenceCount(params) {
  return request({
    url: '/order/v2/evidenceCount',
    method: 'get',
    params,
  });
}
// 查询商品交易快照
export function apiGetOrderSnapshot(params) {
  return request({
    url: '/order/v2/getOrderSnapshot',
    method: 'get',
    params,
  });
}
// 查询客户资质有变更的客户数量
export function apiQueryCustomerChangedOrderCount(params) {
  return request({
    url: '/order/v2/queryCustomerChangedOrderCount',
    method: 'get',
    params,
  });
}
// 查询客户资质更新时间到当前时间段内的资质变更记录
export function apiQueryQualificationChangeListWithinTime(params) {
  return request({
    url: '/orgUserRelation/queryQualificationChangeListWithinTime',
    method: 'get',
    params,
  });
}
// 更新客户资质更新时间
export function apiUpdateQualificationTime(params) {
  return request({
    url: '/orgUserRelation/updateQualificationTime',
    method: 'post',
    params,
  });
}
// 查询全部资质
export function apiQueryQualificationChangeList(params) {
  return request({
    url: '/orgUserRelation/queryQualificationChangeList',
    method: 'post',
    params,
  });
}
// 修改订单状态 为分拣中
export function apiUpdateOrderStatusSorting(params) {
  return request({
    url: '/order/v2/updateOrderStatusSorting',
    method: 'get',
    params,
  });
}
// 修改订单状态 为待配送
export function apiUpdateOrderStatusWaitDelivery(params) {
  return request({
    url: '/order/v2/updateOrderStatusWaitDelivery',
    method: 'post',
    data: params,
  });
}
// 查询业务类型
export function apiGetBusinessTypes(params) {
  return request({
    url: '/popDeliveryInfo/getBusinessTypes',
    method: 'get',
    params,
  });
}
// 查询发货物流快递接口
export function apiGetDeliverGoods(params) {
  return request({
    url: '/popDeliveryInfo/getDeliverGoods',
    method: 'get',
    params,
  });
}
// 省市区三级联动
export function getRegionList(params) {
  return request({
    url: '/regionList',
    method: 'get',
    params,
  });
}
// 新增发货物流接口
export function apiDeliverGoods(params) {
  return request({
    url: '/popDeliveryInfo/deliverGoods',
    method: 'post',
    data: params,
  });
}
// 修改发货物流接口
export function apiUpdateDeliverGoods(params) {
  return request({
    url: '/popDeliveryInfo/updateDeliverGoods',
    method: 'post',
    data: params,
  });
}
// 重打快递面单接口
export function repeatPrint(params) {
  return request({
    url: '/popDeliveryInfo/repeatPrint',
    method: 'post',
    data: params,
  });
}
// 批量校验
export function apiBatchValidDelivery(params) {
  return request({
    url: '/order/v2/batchValidDelivery',
    method: 'get',
    params,
  });
}
// 打印成功回调
export function apiPrintCallBack(params) {
  return request({
    url: '/popDeliveryInfo/printCallBack',
    method: 'get',
    params,
  });
}

// 打印次数加1
export function addPrintCount(params) {
  return request({
    url: '/popDeliveryInfo/addPrintCount',
    method: 'get',
    params,
  });
}

// 发送商家备注给客服系统
export function apiPopDialogSend(params) {
  return request({
    url: '/popDialogSend/dialogSend',
    method: 'get',
    params,
  });
}

// 发送商家备注给客服系统-new
export function apiPopDialogSendNew(params) {
  return request({
    url: '/popDialogSend/newDialogSend',
    method: 'get',
    params,
  });
}

// 订单列表查询物流异常信息
export function queryLogisticsRemind(params) {
  return request({
    url: '/order/v2/queryLogisticsRemind',
    method: 'get',
    params,
  });
}

// 查询快速备注语列表
export function selectRemarkDict(params) {
  return request({
    url: '/popOrderRemarkDict/selectRemarkDict',
    method: 'get',
    params,
  });
}

// 保存/更新快速备注
export function saveOrUpdateRemark(params) {
  return request({
    url: '/popOrderRemarkDict/saveOrUpdateRemark',
    method: 'post',
    data: params,
  });
}

// 查询订单部分发货配置信息
export function selectConfigurationByOrgId(params) {
  return request({
    url: '/corporationConfiguration/selectConfigurationByOrgId',
    method: 'get',
    params,
  });
}

// 订单列表-查询部分发货数量
export function queryPartialShipmentOrderCount(params) {
  return request({
    url: '/order/v2/queryPartialShipmentOrderCount',
    method: 'get',
    params,
  });
}

// 修改订单部分发货配置信息
export function updateConfiguration(params) {
  return request({
    url: '/corporationConfiguration/updateConfiguration',
    method: 'post',
    data: params,
  });
}

// 批量同步部分发货订单信息
export function batchUpdatePartialShipment(params) {
  return request({
    url: '/order/v2/batchUpdatePartialShipment',
    method: 'get',
    params,
  });
}

// 更新备货信息
export function updateOrderConsignmentDetail(params) {
  return request({
    url: '/order/v2/updateOrderConsignmentDetail',
    method: 'post',
    data: params,
  });
}

// 查询未上传电子发票数量
export function apiQueryNonInvoiceOrderCount(params) {
  return request({
    url: '/order/v2/queryNonInvoiceOrderCount',
    method: 'get',
    params,
  });
}

// 批量查询订单明细
export function apiBatchQueryOrderDetailList(data) {
  return request({
    url: '/order/v2/batchQueryOrderDetailList',
    method: 'post',
    data,
  });
}
// 批量备货完成
export function bathUpdateOrderStatusWaitDelivery(data) {
  return request({
    url: '/order/v2/bathUpdateOrderStatusWaitDelivery',
    method: 'post',
    data,
  });
}

// 获取批量发起退款的概要信息
export function getBatchRefundOrderSummary(data) {
  return request({
    url: '/afterSales/getBatchRefundOrderSummary',
    method: 'post',
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
    data,
    transformRequest: [
      function (data) {
        const formData = new FormData();
        Object.keys(data).forEach((key) => {
          formData.append(key, data[key]);
        });
        return formData;
      },
    ],
  });
}

// 批量发起退款
export function batchSubmitRefundApplyBySeller(data) {
  return request({
    url: '/afterSales/batchSubmitRefundApplyBySeller',
    method: 'post',
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
    data,
    transformRequest: [
      function (data) {
        const formData = new FormData();
        Object.keys(data).forEach((key) => {
          formData.append(key, data[key]);
        });
        return formData;
      },
    ],
  });
}

// 查看异常详情-卖家中心
export function getExceptionList(data) {
  return request({
    url: '/order/exception/list',
    method: 'post',
    data,
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
    transformRequest: [function (data) {
      let ret = '';
      Object.keys(data)
        .forEach((item) => {
          ret += `${encodeURIComponent(item)}=${encodeURIComponent(data[item])}&`;
        });
      return ret;
    }],
  });
}

// 删除标记-卖家中心
export function remove(data) {
  return request({
    url: '/order/exception/remove',
    method: 'post',
    data,
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
    transformRequest: [function (data) {
      let ret = '';
      Object.keys(data)
        .forEach((item) => {
          ret += `${encodeURIComponent(item)}=${encodeURIComponent(data[item])}&`;
        });
      return ret;
    }],
  });
}

export function getAfterSaleCount() {
	return request({
		url: '/afterSale/queryAfterSalesCount',
		method: 'get',
	})
}

export function getIsTrialMerchant() {
  return request.get('/afterSales/trialMerchant');
} // 获取灰度商家信息

export function getOrderRefundDetailAmount(params) {
  return request.post('/afterSales/getOrderRefundDetailAmount', params);
} // 根据退款数量变化，计算退款金额


export function getAfterSaleRefundConfigProperties(params) {
  return request.get('/afterSales/getAfterSaleRefundConfigProperties', params);
} // 售后属性配置

// 是否可以发起退款
export function getIsRefund(params) {
  return request.get(`/reminder/isRefund?orderNo=${params.orderNo}`);
}
// 下推同步erp
export function pushErp(params) {
  return request.post('/order/v2/updateErpCodeV2', params);
}
// 申请面单打印
export function getPrintOrder() {
  return request.post('/popDeliveryInfo/saveApply');
}
// 退款单售后凭证查询接口
export function getAfterSaleVoucher(params) {
  return request.get('/afterSales/getOrderRefundVoucher?orderRefundNo='+params.refundOrderNo);
}