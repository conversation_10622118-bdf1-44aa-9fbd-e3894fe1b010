<template>
  <div>
    <!-- 小额打款和同意并额外赔偿的弹框 -->
    <el-dialog
      title=""
      :visible.sync="dialogVisible"
      @close="dialogVisible = false"
      @closed="resetForm"
      width="40%"
      append-to-body
      :close-on-click-modal="false"
    >
      <div class="box">
        <el-form label-width="110px" :rules="rules" ref="subForm" :model="subForm">
          <el-form-item :label="AmountText" prop="Amount" key="Amount">
            <el-input
              placeholder="请输入"
              v-model="subForm.Amount"
              style="width: 300px"
              type="number"
              :min="0"
              :max="maxAmount"
            ></el-input>
          </el-form-item>
          <el-form-item style="margin: 6px 0;">
            <span>{{ text1 }}</span>
          </el-form-item>
          <el-form-item label="回复内容" prop="response" key="response">
            <el-input
              v-model="subForm.response"
              style="width: 300px"
              type="textarea"
              :rows="4"
              placeholder="请输入内容"
              maxlength="50"
              show-word-limit
            ></el-input>
          </el-form-item>
          <el-form-item style="margin: 6px 0;">
            <span style="color: red">{{ text2 }}</span>
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <!-- <el-button @click="$emit('update:dialogVisible')">取 消</el-button> -->
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitForm()">确定 </el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>

import { SmallPayments, IndemnityMoney } from '@/api/afterSale/index';
import { getAfterSaleRefundConfigProperties } from '@/api/order/index'
export default {
  props: {},
  computed: {
    maxAmount: {
      // getter
      get() {
        let temp1 = parseFloat(((this.refundFee * this.approvedSmallPaymentRate).toFixed(3) + '').slice(0, -1))
        let temp2 = parseFloat(((this.refundFee * this.approvedAdditionalCompensationRate).toFixed(3) + '').slice(0, -1))
        return this.type == '0' ? temp1 : temp2
      },
      // setter
      set(newValue) {}
    }
  },
  data() {
    // 校验两位小数金额
    var checkPayAmount = (rule, value, callback) => {
      console.log(rule)
      // const regex = /^(-?\d+(\.\d+)?)$/
      // const regex = /^(-?\d+(\.\d{1,2})?)$/
      // const regex = /^(\d+(\.\d{1,2})?|\d+\.\d{1,2})$/
      const regex = /^\d+(\.\d{1,2})?$/
      const amount = parseFloat(value+'')

      if (!regex.test(amount)) {
        // 如果不匹配正则表达式，说明输入不符合要求
        this.subForm.Amount = null
        callback(new Error('输入必须为数字，不超过2位小数'))
      } else {
        if (isNaN(amount)) {
          this.subForm.Amount = null
          callback(new Error('输入的金额必须为数字'))
        } else if (amount - this.maxAmount > 0 || amount === 0) {
          this.subForm.Amount = null
          callback(new Error('输入的金额必须大于0，且不能大于' + this.maxAmount + '元'))
        } else {
          // 如果数值合法，将数值格式化为两位小数
          // const formattedValue = amount.toFixed(2)
          const formattedValue = amount
          if (rule.field == 'Amount') this.subForm.Amount = formattedValue.toFixed(2)
          // console.log(formattedValue)
          //   调用callback函数，不传递错误，但传递格式化后的值（如果需要的话）
          //   callback(null, formattedValue); // 假设您想传递格式化后的值回表单（视具体情况而定）
        }
      }
      callback()
    }
    return {
      approvedSmallPaymentRate: 0,
      approvedAdditionalCompensationRate: 0,
      refundFee: '',
      refundId: '',
      type: '',
      AmountText: '打款金额',
      text1: '',
      text2: '*内容将直接回复给用户，请使用礼貌用语',
      dialogVisible: false,
      subForm: {
        Amount: null, // 打款金额&赔偿金额
        response: '' // 回复内容
      },
      rules: {
        Amount: [
          { required: true, message: '请输入金额', trigger: 'blur' },
          { validator: checkPayAmount, trigger: 'blur' }
        ],
        response: [{ required: true, message: '请填写回复内容', trigger: 'blur' }]
      }
    }
  },
  created() {},
  methods: {
    openDialog(val, type) {
      getAfterSaleRefundConfigProperties().then((res) => {
        if (res.code === 0) {
          if (res.result) {
            this.approvedSmallPaymentRate = res.result.approvedSmallPaymentRate
            this.approvedAdditionalCompensationRate = res.result.approvedAdditionalCompensationRate
            // console.log(val)
            this.refundFee = val.refundFee
            this.refundId = val.id
            this.type = type
            this.AmountText = type == '0' ? '打款金额' : '额外赔偿'
            this.text1 =
              type == '0'
                ? '打款金额不高于' + this.maxAmount + '元，打款至客户购物金'
                : '赔偿金额不高于' + this.maxAmount + '元，赔偿至客户购物金'
            this.dialogVisible = true
          }
        }
      })

    },
    submitForm() {
      this.$refs.subForm.validate((valid) => {
        if (valid) {
          let subData = {
            refundId: this.refundId,
            indemnityMoney: this.subForm.Amount,
            refundExplain: this.subForm.response
          }
          // console.log(this.subForm)
          const that = this
          function func(subData) {
            if (that.type == '0') return SmallPayments(subData)
            else if(that.type == '1') return IndemnityMoney(subData)
          }
          func(subData).then((res) => {
            // console.log(res)
            if (res.code == 0) {
              that.$message({
                type: 'success',
                message: '操作成功！'
              })
              that.dialogVisible = false
              that.$parent.stateVisible = false;
              that.$parent.$emit('getNewList');
            } else {
              that.$message({
                type: 'error',
                message: res.message
              })
            }
          })
        } else {
          // console.log('error submit!!')
          this.$message.error('请检查表单内容！')
        }
      })
    },
    resetForm() {
      this.$refs.subForm.resetFields()
    }
  }
}
</script>

<style scoped lang="scss">
.box {
  margin-top: -20px;
  width: 100%;
  display: flex;
  flex-direction: column;
  //   justify-content: space-evenly;
  // align-items: center;
  padding: 0 10px;
}

.textSty {
  color: #575757;
}
::v-deep  .el-form-item {
  margin-bottom: 0 !important;
}
</style>
