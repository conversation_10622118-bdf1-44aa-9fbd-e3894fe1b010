<template>
  <el-table
    :data="detailList"
    border
    show-summary
    style="width: 100%"
    :summary-method="getSummaries"
  >
    <el-table-column label="序号" width="50" type="index" />
    <el-table-column label="商品编码" prop="barcode" />
    <el-table-column label="商品ERP编码" prop="erpCode" />
    <el-table-column label="sku编码" prop="skuId" />
    <el-table-column label="商品名称" prop="productName" />
    <el-table-column label="规格（型号）" prop="spec" />
    <el-table-column label="生产厂家" prop="manufacturer" />
    <el-table-column label="退回商品数量" prop="productQuantity" />
    <el-table-column label="退款金额" prop="refundFee" />
  </el-table>

</template>
<script>
  export default {
    name: 'AfterSaleDetail',
    props: {
      detailList: {
        type: Array,
        default: () => [],
      },
      activeAfterSaleStatus: {
        type: String,
        default: null,
      }
    },
    data() {
      return {};
    },
    created() {},
    methods: {
      formatNum(f, digit) {
        const m = Math.pow(10, digit);
        return Math.round(f * m, 10) / m;
      },
      getSummaries(param) {
        const {
          columns,
          data
        } = param;
        const sums = [];
        columns.forEach((column, index) => {
          if (index === 0) {
            sums[index] = '合计';
          } else if (column.property === 'productQuantity' || column.property === 'refundFee') {
            const values = data.map(item => Number(item[column.property]));
            if (!values.every(value => isNaN(value))) {
              sums[index] = values.reduce((t, c) => this.formatNum(t + c, 10), 0);
            } else {
              sums[index] = '';
            }
          } else {
            sums[index] = '';
          }
        });
        return sums;
      },
    },
  };

</script>
<style lang="scss" scoped>
  .btnText {
    color: #4184D5;
    cursor: pointer;
  }

</style>
