<template>
  <div style="display: inline-block;">
    <div @click="dialogTableVisible = true;findUserTypes();getSaleAreas();">
      <slot>
        <el-button size="small" type="primary">新增</el-button>
      </slot>
    </div>
    <el-dialog  width="60%" append-to-body :visible.sync="dialogTableVisible" @close="handelClose()" >
      <div slot="title" >
           <div>{{ groupMessage?'编辑用户':'新增用户' }}</div>
       </div>
      <div style="margin-bottom: 10px;">
        <div>温馨提示:</div>
        <div>1、用户组名称、地域、客户类型不能为空；</div>
        <div>2、可按照“省份”、“城市”、“区县”三个地域维度分别叠加客户类型设置价格用户组；</div>
        <div>3、不同价格用户组之间，同一地域层级+客户类型不能有交叉和重叠（同一省份+客户类型不能有交叉和重叠、同一城市+客户类型不能有交叉和重叠、同一区县+客户类型不能有交叉和重叠），不同地域层级不校验；</div>
        <div>4、商品引用价格用户组设置区域价时，同一客户类型若同时设置了“省份”、“城市”、“区县”三个区域价格且省市区县为包含关系，则对客户展示区域价格的优先级为：区县>城市>省份，即优先展示区县用户组的区域价格；</div>
      </div>
      <div>
        <el-form :rules="rules" ref="ruleForm" :model="commitInfo" >
          <el-form-item label="用户组名称"  prop="groupName" v-if="dialogTableVisible">
            <el-col :span="8">
              <el-input size="small" maxlength="20" v-model="commitInfo.groupName"></el-input>
            </el-col>
          </el-form-item>
          <div class="daaGroupTop">
            <el-form-item label="地域" required>
            <el-col >
              <div class="treeContent" v-loading="loading">
                <el-tree
                  required
                  ref="saleableArea"
                  :data="saleableAreaData"
                  :props="props"
                  :check-strictly="isCheckStrictly"
                  :default-checked-keys="initCheckKeys"
                  show-checkbox
                  node-key="areaCode"
                  :render-content="renderContent"
                  :default-expanded-keys="restrictedAreaData"
                  @check="handleCheckChange"
                  @node-expand="handleExpand"
                />
              </div>
            </el-col>

          </el-form-item>
          <el-form-item label="客户类型" required>
            <el-col >
              <div class="customerType">
            <el-checkbox
              :indeterminate="isIndeterminate"
              v-model="checkAll"

              @change="handleCheckAllChange"
              class="checkedall"
            >全选</el-checkbox>
            <el-checkbox-group
              v-model="supplyInfo.controlUserTypes"

              @change="handleCheckedTypesChange"
            >
              <el-checkbox
                v-for="(item,index) in customerTypes"
                :label="item.id"
                style="margin-bottom: 10px;"
                :key="index"
              >{{item.name}}</el-checkbox>
            </el-checkbox-group>
          </div>
            </el-col>
        </el-form-item>
          </div>

        </el-form>
      </div>
      <div slot="footer" >
        <el-button  size="medium" @click="dialogTableVisible = false">取消</el-button>
        <el-button type="primary" size="medium" @click="commit">保存</el-button>
      </div>
    </el-dialog>

      <!--重复创建提示  -->
      <el-dialog
        title="提示"
        append-to-body
        v-if="dialogTipVisible"
        :visible.sync="dialogTipVisible"
        width="50%"
      >
      <div style="margin-bottom: 10px;">所选客户类型+地域已存在用户组，不能重复创建</div>
      <div>
        <div v-for="(item,index) in repeatList" :key="index" style="margin-bottom: 5px;">
          {{ item }}
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
      <el-button type="primary" @click="dialogTipVisible = false">确 定</el-button>
      </span>
      </el-dialog>

    </div>

</template>

<script>
import { areasCodes, saleAreas,getAreaTree} from '@/api/storeManagement/storeAreaSalesControl';
import { findUserTypes } from '@/api/product';
import { addUserGroup } from '@/api/product';

export default {
  name:'addGroup',
  props:{
    groupMessage:{
      type: Object,
      default: () => {
      }
    }
  },
  data() {
    return {
      dialogTipVisible:false,//显示重复创建弹框
      loading:false,
      dialogTableVisible:false,
      initCheckKeys: [],//初始地域节点key
      checkKeys: [],//选中地域节点key
      saleableAreaData: [],//地域
      repeatList:[],//重复信息
      commitInfo:{
        id:'',
        groupName:'',
        provinceCodes:[],
        customerTypes:[],
        cityCodes:[],
        areaCodes:[],
        allArea:0,
      },
      customerTypes: [],
      isIndeterminate: false,
      checkAll: false,
      supplyInfo: {
        controlUserTypes: 2,
        controlRosterType: 0,
        isCopySaleArea: 1,
        controlGroupId: '',
        controlGroupName: '',
        controlUserTypes: [],//需要
        isCopyControlRoster: 2,
        isCopyControlUser: 2,
      },
      props: {
        label: 'areaName',
        children: 'childList',
        isLeaf: (data, node) => {
          return node.areaLevel === 4;
        }
      },
      restrictedAreaData: [0],
      unCheckNodes: [],
      isCheckStrictly: false,
      expandedData:'',
      businessShow: false,
      businessVisible: false,
      businessCondition: false,
      rules:{
        groupName:[
          { required: true, message: '请输入用户组名称', trigger: 'blur' }
        ]
      },

    }
  },

  created() {
  },
  watch:{
    groupMessage:{
      handler(val) {
        this.commitInfo.id  = val.id
        this.commitInfo.groupName =val.groupName
        this.commitInfo.customerTypes = val.customerTypes
        this.supplyInfo.controlUserTypes = val.customerTypes
        // this.commitInfo.allArea = this.checkKeys[0] === 0 ? 1 :
        this.commitInfo.allArea = val.allArea === true ? 1 : 0
        // if(this.commitInfo.allArea === 1) {
        //   this.checkKeys = this.expandedData
        // } else{
        //   this.checkKeys = val.areaCodes
        // }
        // let keys = this.checkKeys.filter(item => !!item);
        // this.commitInfo.provinceCodes = keys.filter(item => item % 10000 === 0 )
        // this.commitInfo.cityCodes = keys.filter(item => item % 10000 > 0 && item % 100 === 0)
        // this.commitInfo.areaCodes = keys.filter(item => item % 1000 > 0 && item % 100 > 0)
        this.checkKeys = val.areaCodes
        if(this.commitInfo.allArea === 0) {
          
          let keys = this.checkKeys.filter(item => !!item);
          this.commitInfo.provinceCodes = keys.filter(item => item % 10000 === 0 )
          this.commitInfo.cityCodes = keys.filter(item => item % 10000 > 0 && item % 100 === 0)
          this.commitInfo.areaCodes = keys.filter(item => item % 1000 > 0 && item % 100 > 0)
        }
      },
      // immediate:true,
      deep:true
    }
  },
  mounted() {
    this.handleExpand()
  },
  methods: {
    //弹窗关闭
    handelClose() {
      //初始化数据
      this.isIndeterminate = false;
      this.commitInfo.groupName = ''
      this.commitInfo.id = ''
      this.commitInfo.provinceCodes = []
      this.commitInfo.cityCodes = []
      this.commitInfo.areaCodes = []
      this.commitInfo.allArea = 0
      this.commitInfo.customerTypes = []
      this.checkKeys = []
      this.checkAll = false
      this.supplyInfo.controlUserTypes = []
      this.saleableAreaData = []
      this.restrictedAreaData = [0]
    },
    //提交用户组
    commit() {
      if (!this.commitInfo.groupName) {
        this.$message({
          message: '请输入用户组名称',
          type: 'warning'
        });
        return
      }
      if(this.commitInfo.customerTypes.length === 0 || (this.commitInfo.provinceCodes.length === 0 && this.commitInfo.cityCodes.length === 0 && this.commitInfo.areaCodes.length === 0)){
        this.$message({
          message: '请选择地域和客户类型',
          type: 'warning'
        });
        return
      }

       addUserGroup(this.commitInfo).then(res => {
        console.log(res);
        if (res.code == 0){
          // this.handleClose()
          //有重复创建
          if(res.result.repeatList) {
            this.repeatList = res.result.repeatList
            this.dialogTipVisible = true

          }else{
            if(this.commitInfo.id){
              this.$message({
              message: '修改用户成功',
              type:'success',
              })
              this.$emit('search', 'search')
              this.dialogTableVisible = false

            }else{
              this.$message({
              message: '用户新建成功',
              type:'success',
              })
              this.$emit('search', 'search')
              this.dialogTableVisible = false
            }

          }

        } else {
          this.$message({
            message: res.result,
            type:'warning',
          })
        }

       })

    },
    getProps() {

    },
    // 获取用户类型
    findUserTypes() {
      if (this.customerTypes.length > 0) return
      findUserTypes().then((res) => {
        if (res.code == 0) {
          this.customerTypes = res.data;
        } else {
          this.$message({
            message: res.message,
            type: 'warning',
          });
        }
      });
    },
    // 供货对象
    handleCheckAllChange(val) {
      const checkAllId = this.customerTypes.map((item => item.id));
      this.commitInfo.customerTypes = val ? checkAllId :[]
      // console.log(this.commitInfo.customerTypes);

      this.supplyInfo.controlUserTypes = val ? checkAllId : [];
      this.isIndeterminate = false;
    },
    handleCheckedTypesChange(value) {
      // console.log(value);
      this.commitInfo.customerTypes = value
      // console.log(this.commitInfo.customerTypes);
      const checkedCount = value.length;
      this.checkAll = checkedCount === this.customerTypes.length;
      this.isIndeterminate = checkedCount > 0 && checkedCount < this.customerTypes.length;
    },

    //展开tree节点
    handleExpand() {
      this.$nextTick(() => {
        const levelName = document.getElementsByClassName('foo'); // levelname是上面的最底层节点的名字
        for (let i = 0; i < levelName.length; i++) {
          levelName[i].parentNode.style.cssFloat = 'left'; // 最底层的节点，包括多选框和名字都让他左浮动
          levelName[i].parentNode.style.styleFloat = 'left';
        }
      });
    },


    // 树节点的内容区的渲染 Function
    renderContent(h, { node }) {
      // 树节点的内容区的渲染 Function
      let classname = '';
      // 由于项目中有三级菜单也有四级级菜单，就要在此做出判断
      if (node.level === 4) {
        classname = 'foo';
      }
      if (node.level === 3 && node.childNodes.length === 0) {
        classname = 'foo';
      }
      return h('p', { class: classname }, node.label);
    },
    flatten(ary) {
      const list = [];
      ary.forEach(item => {
        const {
          childList,
          ...obj
        } = item;
        list.push(obj);
        if (childList && childList.length) {
          const childrenList = this.flatten(childList);
          list.push(...childrenList);
        }
      });
      return list;
    },

    //获取地域
    async getAreasCodes() {
      const res = await getAreaTree();//
      if (res && res.code === 0) {
        this.saleableAreaData = []
        const selectAll = {
          areaName: '全国',
          areaCode: 0,
          areaLevel: 0,
          childList: res.data,
        };
        this.saleableAreaData.push(selectAll);
        this.$nextTick(()=>{
          // this.restrictedAreaData = this.expandedData
          this.$refs.saleableArea.setCheckedKeys(this.checkKeys || [])
          this.getCheckNodes();
        })

      } else {
        this.$message.error(res.message);
      }

    },

     async getSaleAreas() {
      this.loading = true
      const res =  await saleAreas();
      if (res && res.code === 0) {
        console.log(1);

        this.expandedData = [...res.data];
        this.initCheckKeys =[...res.data];
        //判断是否为全国
        if(this.commitInfo.allArea === 1) {
          
          let keys = this.checkKeys.filter(item => !!item);
          this.commitInfo.provinceCodes = keys.filter(item => item % 10000 === 0 )

        }
        // let keys = this.checkKeys.filter(item => !!item);
        // this.commitInfo.provinceCodes = keys.filter(item => item % 10000 === 0 )
        // this.commitInfo.cityCodes = keys.filter(item => item % 10000 > 0 && item % 100 === 0)
        // this.commitInfo.areaCodes = keys.filter(item => item % 1000 > 0 && item % 100 > 0)
        // this.commitInfo.allArea = 1
        // this.commitInfo.provinceCodes = [...res.data];
       await this.getAreasCodes();
      } else {
        this.$message.error(res.message);
      }
      this.loading = false
    },

    handleCheckChange() {
      this.getCheckNodes();
      let keys = this.checkKeys.filter(item => !!item);
      console.log(keys);

      this.commitInfo.allArea = this.checkKeys[0] === 0 ? 1 : 0
      this.commitInfo.provinceCodes = keys.filter(item => item % 10000 === 0 )
      this.commitInfo.cityCodes = keys.filter(item => item % 10000 > 0 && item % 100 === 0)
      this.commitInfo.areaCodes = keys.filter(item => item % 1000 > 0 && item % 100 > 0)

      // console.log(keys,this.commitInfo.allArea);

    },

    getCheckNodes() {
      const nodes = this.$refs.saleableArea.getCheckedNodes();
      //去重
      const selectCode = new Map();
      nodes.forEach(item => {
        if (!selectCode.has(item.areaCode)) {
          selectCode.set(item.areaCode, true);
          if (item.parentCode && selectCode.has(item.parentCode)) {
            selectCode.set(item.areaCode, false);
          }
        }
      });
      const selectKeyAry = [];  //选中的key
      selectCode.forEach((val, key) => {
        if (val) {
          selectKeyAry.push(key);
        }
      });
      this.checkKeys = selectKeyAry;
      this.initCheckKeys = selectKeyAry



      const flattenAry = this.flatten(this.saleableAreaData[0].childList);
      const allCheckKeys = this.$refs.saleableArea.getCheckedNodes(false, true)
        .map(item => item.areaCode);

      const unCheckNodes = flattenAry.filter((item) => !allCheckKeys.includes(item.areaCode));

      const unCheckCode = new Map();
      unCheckNodes.forEach(item => {
        if (!unCheckCode.has(item.areaCode)) {
          unCheckCode.set(item.areaCode, true);
          if (item.parentCode && unCheckCode.has(item.parentCode)) {
            unCheckCode.set(item.areaCode, false);
          }
        }
      });
      const unCheckKeys = [];
      unCheckCode.forEach((val, key) => {
        if (val) {
          unCheckKeys.push(key);
        }
      });
      this.unCheckNodes = flattenAry.filter((item) => unCheckKeys.includes(item.areaCode));
    }

  }

}
</script>
<style scoped lang="scss">
::v-deep   .el-dialog__header{

  background: #f5f5f580;
}
::v-deep   .el-dialog__body{

max-height: 400px;
overflow: auto;
}
.top .el-form-item__label{
  border: 1px solid #eeeeee;
  border-bottom: none;
  border-radius: 4px;
  background-color: #F9F9F9;
  width: 100%;
  text-align: left;
}
.treeContent {
        width: 100%;
        border-radius: 4px;
        border: 1px solid #eeeeee;
        border-top:none ;
        height: 300px;
        overflow-y: auto;
      }

.customerType {
    border: 1px solid #eeeeee;
    border-top:none ;
    border-radius: 4px;
    max-height: 260px;
    overflow-y: auto;
    ::v-deep  .el-checkbox {
      width: 14%;
      margin-left: 10px;
    }
    ::v-deep  .checkedall {
      width: 100%;
      padding: 10px;
      margin-left: 0;
      margin-bottom: 10px;
    }
    ::v-deep  .el-checkbox__input.is-checked + .el-checkbox__label {
      color: #333333;
    }
  }
</style>
<style>
.daaGroupTop .el-form-item__label{
  border: 1px solid #eeeeee;
  border-bottom: none;
  border-radius: 4px;
  background-color: #F9F9F9;
  width: 100%;
  text-align: left;
}
</style>
