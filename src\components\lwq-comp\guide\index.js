import lGuide from "./l-guide.vue";
import lGuideStep from "./l-guide-step.vue";
/**
 * 引导组件用法：
 * ```vue
 * <template>
 *   <lGuide>
 *       <p>文本</p>
 *       <lGuideStep :step="1">
 *           <p>引导-步骤1</p>
 *       </lGuideStep>
 *       <p>文本</p>
 *       <lGuideStep :step="2">
 *           <p>引导-步骤2</p>
 *       </lGuideStep>
 *   </lGuide>
 * </template>
 * <script>
 * import { guideCreater } from ''
 * const { lGuide, lGuideStep } = guideCreater()
 * export default {
 *    components: {
 *      lGuide,
 *      lGuideStep
 *   }
 * }
 * </script>
 * ```
 */
export const guideCreater = () => {
  const stepElList = [];
  return {
		lGuide: {
			render(h) {
				return h(lGuide, {
					props: {
						start: this.start,
            stepElList: stepElList,
            step: this.step
					},
          on: {
						end: () => {
							this.$emit("end");
						},
            next: (step) => {
              this.step = step
              this.$emit('next');
            }
					},
				}, [...this.$slots.default, this.$scopedSlots.control ? h('template', { slot: 'control' }, this.$scopedSlots.control(stepElList[this.step - 1] || {})) : ''])
			},
      props: {
        start: {
          type: Boolean,
          default: false
        }
      },
      data() {
        return {
          step: 1
        }
      },
      slot: {
        default: 'default',
      },
      scopedSlots: {
        control: (item) => 'control'
      }
		},
    lGuideStep: {
      render(h) {
        return h(lGuideStep, {
          props: {
            step: this.step,
            title: this.title,
            content: this.content
          },
          on: {
            getStep: (data) => {
              stepElList[data.step - 1] = {
                ref: data.ref,
                title: data.title,
                content: data.content,
              };
            }
          }
        }, this.$slots.default)
      },
      props: {
        step: {
          type: Number,
          default: 0
        },
        title: {
          type: String,
          default: ''
        },
        content: {
          type: String,
          default: ''
        }
      },
      slot: 'default'
    },
	}
}

