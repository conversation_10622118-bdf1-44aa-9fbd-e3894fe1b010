<!-- 售后管理首页——查询及按钮 -->
<template>
	<div>
		<common-header title="售后管理">
			<template slot="header-right">
				<el-button size="small" type="primary" @click="search">查询</el-button>
				<el-button size="small" @click="reset">重置</el-button>
				<el-button size="small" type="primary" @click="exportAfterSaleList">导出</el-button>
			</template>
			<div>
				<el-form label-position="right" label-width="10px" style="margin-top:10px;">
					<el-row>
						<el-col :xs="16" :md="12" :lg="5" :xl="5">
							<el-form-item>
									<el-input placeholder="订单ID" size="small" v-model="form.popOrderId" clearable>
										<template slot="prepend">订单ID</template>
									</el-input>
								</el-form-item>
						</el-col>
						<el-col :xs="16" :md="12" :lg="5" :xl="5">
							<el-form-item>
									<el-input placeholder="订单编号" size="small" v-model="form.orderNo" clearable>
										<template slot="prepend">订单编号</template>
									</el-input>
								</el-form-item>
						</el-col>
						<el-col :xs="16" :md="12" :lg="5" :xl="5">
							<el-form-item>
									<el-input size="small" v-model="form.customerErpCode" clearable>
										<template slot="prepend">客户erp编码</template>
									</el-input>
								</el-form-item>
						</el-col>
						<el-col :xs="16" :md="12" :lg="5" :xl="5">
							<el-form-item>
									<el-input size="small" v-model="form.merchantName" clearable>
										<template slot="prepend">客户名称</template>
									</el-input>
								</el-form-item>
						</el-col>
						<el-col :xs="16" :md="12" :lg="10" :xl="10">
							<el-form-item>
									<span class="search-title">申请日期</span>
									<div class="left-input">
										<el-date-picker
											size="small"
											v-model="time"
											type="datetimerange"
											format="yyyy-MM-dd HH:mm:ss"
											value-format="yyyy-MM-dd HH:mm:ss"
											range-separator="至"
											start-placeholder="开始日期"
											end-placeholder="结束日期"
											:default-time="['00:00:00', '23:59:59']"
											style="width: 100%;"
											:picker-options="pickerOptions"
                  							popper-class="date-style"
											@change="dateOnChange"/>
									</div>
								</el-form-item>
						</el-col>
						<el-col :xs="16" :md="12" :lg="5" :xl="5">
							<el-form-item>
								<province-list v-model="form.provinceCodes" label="省份" :list="provinceList" :multiple="true" labelProp="branchName" valueProp="branchCode"></province-list>
							</el-form-item>
						</el-col>
						<el-col :xs="16" :md="12" :lg="5" :xl="5">
							<el-form-item>
								<province-list v-model="form.auditProcessStates" :list="takeStatusList" label="处理状态" multiple></province-list>
							</el-form-item>
						</el-col>
						<!-- <el-col :xs="16" :md="12" :lg="5" :xl="5">
							<el-form-item>
								<province-list v-model="form.orderStatus" :list="orderStatusList" label="订单状态"></province-list>
							</el-form-item>
						</el-col> -->
						<el-col :xs="16" :md="12" :lg="5" :xl="5">
							<el-form-item>
								<province-list v-model="form.afterSalesType" :list="afterSalesTypeList" label="售后类型"></province-list>
							</el-form-item>
						</el-col>
						<el-col :xs="16" :md="12" :lg="5" :xl="5">
							<el-form-item>
								<province-list v-model="form.subType" :list="subTypeList" label="售后原因"></province-list>
							</el-form-item>
						</el-col>
						<el-col :xs="16" :md="12" :lg="5" :xl="5">
							<el-form-item>
									<el-input size="small" v-model="form.remark" clearable maxlength="50">
										<template slot="prepend">售后备注</template>
									</el-input>
								</el-form-item>
						</el-col>
					</el-row>
				</el-form>
			</div>
		</common-header>
		<div style="padding: 0 30px 10px 23px;">
			<div style="position: relative;">
				<el-tabs @tab-click="tabClick" :value="tagActive">
					<el-tab-pane name="all">
						<template slot="label">
							<p style="margin:0" :title="count.total">
								<span>全部</span>
								&nbsp;
								<span class="badge">{{ count.total > 99 ? '99+' : count.total }}</span>
							</p>
						</template>
					</el-tab-pane>
					<el-tab-pane name="1,7">
						<template slot="label">
							<p style="margin:0" :title="count.waitSellerHandleCount">
								<span>待商家处理</span>
								&nbsp;
								<span class="badge">{{ count.waitSellerHandleCount > 99 ? '99+' : count.waitSellerHandleCount }}</span>
							</p>
						</template>
					</el-tab-pane>
					<el-tab-pane name="7">
						<template slot="label">
							<p style="margin:0" :title="count.waitSellerReceiveCount">
								<span>待商家收货</span>
								&nbsp;
								<span class="badge">{{ count.waitSellerReceiveCount > 99 ? '99+' : count.waitSellerReceiveCount }}</span>
							</p>
						</template>
					</el-tab-pane>
					<el-tab-pane name="6">
						<template slot="label">
							<span style="margin:0" :title="count.waitDeliveryCount">
								<span>待客户寄回</span>
								&nbsp;
								<span class="badge">{{ count.waitDeliveryCount > 99 ? '99+' : count.waitDeliveryCount }}</span>
							</span>
						</template>
					</el-tab-pane>
				</el-tabs>
				<div style="color: #4184d5; cursor: pointer;    position: absolute;top: 21%;right: 22%;" @click="descriptionChange">发票售后自动关单功能说明</div>
			</div>
			<i-content :tableData="table.data" :loading="table.loading" style="margin-bottom:10px;" @needSearch="needSearch"></i-content>
			<el-pagination
				style="position:relative;left:100%;display:inline-block;transform:translateX(-100%)"
				@size-change="handleSizeChange"
				@current-change="handleCurrentChange"
				:current-page="pagination.pageNum"
				:page-sizes="[20, 50, 100]"
				:page-size="pagination.pageSize"
				background
				layout="total, sizes, prev, pager, next, jumper"
				:total="pagination.total">
			</el-pagination>
		</div>
		<exportTips :change-export="changeExport" @handleExoprClose="changeExport = false" @handleChangeExport="handleChangeExport"></exportTips>
	</div>
</template>

<script>
import {
  getListProvinceList,
} from '@/api/afterSale/index';
import {mapMutations} from "vuex";
import exportTips from '../../views/other/components/exportTip.vue'
import {
	getList,
	getAllSalesStatusCount,
	exportAfterSaleList
} from '@/api/afterSaleManager/index'
import commonHeader from "./components/common-header.vue"
import provinceList from "./components/provinceList.vue"
import iContent from "./content.vue"
export default {
	name: 'afterSaleManager',
	components: {
		commonHeader,
		provinceList,
		exportTips,
		iContent
	},
	mounted() {
	},
	created() {
		// 清空页面缓存
		window.clearData['/afterSaleManager'] = () => {
			this.reset();
		}
	},
	data() {
		return {
			table: {
				loading: false,
				data: []
			},
			changeExport: false,
			form: {
				popOrderId: '', //订单号
				orderNo: '',   //订单编号
				customerErpCode: '',  //erp
				merchantName: '', //客户名称
				provinceCodes: [], //省份
				startCreateTime: '',   //开始日期
				endCreateTime: '',    //结束日期
				auditProcessStates: [], //处理状态
				//orderStatus: '',  //订单状态
				afterSalesType: '',  //售后类型
				subType: '', //售后原因
				remark: '', //售后备注
			},
			count: {
				total: 0,    //全部
				waitSellerHandleCount: 0,  //等待商家处理
				waitSellerReceiveCount: 0,   //待商家收货
				waitDeliveryCount: 0,       //待客户寄回
			},
			pagination: {
				pageNum: 1,
				pageSize: 20,
				total: 0
			},
			tagActive: 'all',
			time: [],
			provinceList: [],   //省份列表
			takeStatusList: [
				{ value: '1', label: '等待商家确认' },
				{ value: '2', label: '用户取消' },
				{ value: '3', label: '商家已处理' },
				{ value: '4', label: '申请被退回' },
				{ value: '5', label: '商家已补发' },
				{ value: '6', label: '待客户退回发票' },
				{ value: '7', label: '客户已退回发票' },
			],
			orderStatusList: [
				{ value: '', label: '全部' },
				{ value: '2', label: '配送中' },
				{ value: '3', label: '已完成' },
				{ value: '91', label: '已退款' },
			],
			afterSalesTypeList: [
				{ value: '', label: '全部' },
				{ value: '2', label: '资质售后' },
				{ value: '1', label: '发票售后' },
			],
			subTypeList: [
				{ value: '', label: '全部' },
				{ value: '1', label: '无票' },
				{ value: '2', label: '错票' },
				{ value: '3', label: '申请专票' },
			],
			pickerOptions: {
				shortcuts: [
				{
					text: '近6个月',
					onClick(picker) {
					let now = dayjs();
					let last6Months = now.subtract(6, 'month');
					// 证传入的日期数组的日期为Date类型
					now = new Date(now)
					last6Months = new Date(last6Months)
					picker.$emit('pick', [last6Months, now]);  
					}
				},
				{
					text: '近1年',  
					onClick(picker) {  
					let now = dayjs();
					let last1Year = now.subtract(1, 'year'); 
					// 证传入的日期数组的日期为Date类型
					now = new Date(now)
					last1Year = new Date(last1Year)
					picker.$emit('pick', [last1Year, now]);  
					}  
				},
				{  
					text: '一年前',  
					onClick(picker) {
					let now = dayjs();  
					let last1Year = now.subtract(1, 'year');
					let last3Year = now.subtract(2, 'year');
					last1Year = new Date(last1Year)
					last3Year = new Date(last3Year)
					picker.$emit('pick', [last3Year, last1Year]);  
					}  
				}  
				]
			}
		}
	},
	activated() {
		if (Object.keys(this.$route.query).length != 0) {
			console.log(this.$route.query);
			for (const key in this.form) {
				if (this.$route.query[key]) {
					this.form[key] = this.$route.query[key];
				} else {
					this.form[key] = ''
				}
			}
			if (this.$route.query.homeEnter == 1) {
				this.form.auditProcessStates = ['1','7'];
				this.tagActive = '1,7';
			}
			/* debugger
			for (const key in this.$route.query) {
				if (key == 'homeEnter') {
					this.form.auditProcessStates = ['1','7'];
					this.tagActive = '1,7';
				} else if (this.$route.query[key]) {
					this.form[key] = this.$route.query[key];
				} else {
					this.form[key] = ''
				}
			}; */
			this.form.provinceCodes = this.form.provinceCodes ? this.form.provinceCodes : [];
			this.form.auditProcessStates = this.form.auditProcessStates ? this.form.auditProcessStates : [];
			if (!this.form.startCreateTime && !this.form.endCreateTime) {
				this.initDate();
			}
			this.search();
		}
	},
	mounted() {
		getListProvinceList().then((res) => {
			this.provinceList = res.data || [];
		});
		if (!this.$route.query.homeEnter) {
			this.initDate();
			this.search();
		}
	},
	methods: {
		/**发票售后自动关单功能说明 */
		descriptionChange(){
			const h = this.$createElement;
			this.$msgbox({
				title: '功能说明',
				message: h('div', null, [
				h('div', null, '售后原因为无票或者申请专票，且申请售后时订单未上传发票。'),
				h('div', null, '该类型的发票售后单，当商家在订单页面上传发票后，系统自动审核售后单为商家已处理'),
				]),
				confirmButtonText: '我知道了',
			}).then(action => {
			})
		},
		needSearch(res)  {
			if (Object.entries(this.form).some(item => item[0] == res.key)) {
				for (const key in this.form) {
					this.form[key] = ""
				};
				this.form.provinceCodes = [];
				this.form.auditProcessStates = [];
				this.time = [];
				this.form[res.key] = res.value;
				this.search();

			}
		},
		...mapMutations('permission', ['SET_CHANGE_RED_COUNT']),
		search(status) {
			if (this.table.loading) return;
			this.table.loading = true;
			/* const formData = {}
			for (const key in this.form) {
				formData[key] = this.form[key]
			}
			formData.provinceCodes = JSON.stringify(formData.provinceCodes);
			formData.auditProcessStates = JSON.stringify(formData.auditProcessStates); */
			const form = { ...this.form };
			form.auditProcessStates = []
			getAllSalesStatusCount(form).then(res => {
				if (res.data) {
					this.count.waitDeliveryCount = res.data.waitDeliveryCount;
					this.count.waitSellerHandleCount = res.data.waitSellerHandleCount;
					this.count.waitSellerReceiveCount = res.data.waitSellerReceiveCount;
					this.count.total = res.data.total;
					this.SET_CHANGE_RED_COUNT({flag: this.count.waitSellerHandleCount,key: 'waitSellerHandleCount'});
				}
			})
			this.pagination.pageNum = status ? this.pagination.pageNum : 1;
			getList({
				...this.form,
				pageNo: this.pagination.pageNum,
				pageSize: this.pagination.pageSize
			}).then(res => {
				if (res.code == 0) {
					this.pagination.total = res.data.total;
					if (res.data.list) {
						this.table.data = res.data.list.map(item => {
                item.afterSalesInfo1 = '';
                item.afterSalesInfo2 = '';
							if (item.afterSalesInfo) {
                const arr = item.afterSalesInfo.split('</br>');
                item.afterSalesInfo1 = arr[0]
                item.afterSalesInfo2 = arr[1];
              }
							return item
						})
					} else {
						this.table.data = [];
					}
				} else {
					this.table.data = [];
					this.$message.error(res.message)
				}
				const tag = this.form.auditProcessStates.join(',');
				this.tagActive = ['1,7', '7', '6'].includes(tag) ? tag : 'all';
			}).finally(() => {
				this.table.loading = false
			})
		},
		dateOnChange(val) {
			if (val === null) {
				this.form.startCreateTime = '';
				this.form.endCreateTime = '';
				val = '';
				this.time = [];
			} else if (typeof val[0] === 'string') {
				this.form.startCreateTime = new Date(val[0]).getTime();
				this.form.endCreateTime = new Date(val[1]).getTime();
			}
		},
		tabClick(tag) {
			if (this.table.loading) return
			this.table.loading = true;
			this.form.auditProcessStates = tag.name && tag.name !== 'all' ? tag.name.split(",") : [];
			this.pagination.pageNum = 1;
			getList({
				...this.form,
				pageNo: this.pagination.pageNum,
				pageSize: this.pagination.pageSize
			}).then(res => {
				if (res.code == 0) {
					this.pagination.total = res.data.total;
					if (res.data.list) {
						this.table.data = res.data.list.map(item => {
							const arr = item.afterSalesInfo.split('</br>');
							item.afterSalesInfo1 = arr[0]
							item.afterSalesInfo2 = arr[1];
							return item
						})
					} else {
						this.table.data = [];
					}
				} else {
					this.table.data = [];
					this.$message.error(res.message)
				}
				const tag = this.form.auditProcessStates.join(',');
				this.tagActive = ['1,7', '7', '6'].includes(tag) ? tag : 'all';
			}).finally(() => {
				this.table.loading = false
			})
			return false
		},
		handleCurrentChange(pageNum) {
			this.pagination.pageNum = pageNum;
			this.search(true);
		},
		handleSizeChange(pageSize) {
			this.pagination.pageSize = pageSize;
			this.search(true);
		},
		reset(tag) {
			for (const key in this.form) {
				this.form[key] = ""
			};
			if (!tag) {
				this.initDate();
			}
			this.form.provinceCodes = [];
			this.form.auditProcessStates = [];
			this.search();
		},
		exportAfterSaleList() {
			exportAfterSaleList({...this.form}).then(res => {
				if (res.code !== 0) {
					this.$message.warning(res.msg);
					return;
				}
				this.changeExport = true;
			})
		},
		handleChangeExport(info) {
			this.changeExport = false;
			if (info === 'go') {
				const path = '/downloadList';
				window.openTab(path);
				// that.$router.push({ path: '/downloadList' });
			}
		},
		initDate() {
			const date = new Date();
			date.setMonth(date.getMonth() - 3);
			date.toLocaleDateString();
			const y = date.getFullYear();
			let m = date.getMonth() + 1;
			m = m < 10 ? (`0${m}`) : m;
			let d = date.getDate();
			d = d < 10 ? (`0${d}`) : d;
			const time = `${y}-${m}-${d}`;
			this.time = [`${time} 00:00:00`, `${new Date().getFullYear()}-${new Date().getMonth() + 1}-${new Date().getDate()} 23:59:59`];
			this.dateOnChange(this.time);
		}
	}
}
</script>

<style lang="scss">
.search-title {
	display: table-cell;
	padding: 0 20px;
	text-align: center;
	border: 1px solid #dcdfe6;
	height: 30px;
	line-height: 30px;
	vertical-align: middle;
	border-right: none;
	border-radius: 4px 0 0 4px;
	color: #909399;
	white-space: nowrap;
	background-color: #F5F7FA;
}
.badge {
	background: #ff4d4f;
	border-radius: 11px;
	color: #fff;
	font-size: 12px;
	margin-left: 2px;
	padding: 0 8px;
}
.left-input {
	display: table-cell;
	width: 100%;
	line-height: 24px;
	.el-date-editor {
		border-top-left-radius: 0px;
		border-bottom-left-radius: 0px;
	}
}
.date-style .el-picker-panel__body-wrapper {
  .el-picker-panel__body {
    margin: 0;
  }
  .el-picker-panel__sidebar {
    position: absolute;
    padding: 0;
    top: auto;
    bottom: 0.6vh;
    left: 1%;
    z-index: 100;
    display: flex;
    width: 250px;
    border: none;
    align-items: center;
    .el-picker-panel__shortcut {
      border: 1px solid #DCDFE6;
      margin: 0 3px;
      border-radius: 5px;
      font-size: 12px;
      height: 27px;
      line-height: 27px;
      overflow: hidden;
      text-align: center;
    }
    .el-picker-panel__shortcut:hover {
      border-color: #1d69c4;
    }
  }
}
</style>