<template>
  <div class="home">
    <el-row :gutter="16">
      <el-col :span="16">
        <LeftContent/>
      </el-col>
      <el-col :span="8">
        <RightContent />
      </el-col>
    </el-row>
    <bttomrule v-if="isGrayUser"></bttomrule>
    <!--    <CustomerService/>-->
    <repeatTips />
  </div>
</template>

<script>
import LeftContent from './components/LeftContent'
import RightContent from "./components/RightContent";
import CustomerService from './components/customerService'
import {actionTracking} from "@/track/eventTracking";
import { docTitleScroll } from "../../utils/util.js";
import bttomrule from './components/bottomrule'
import Vue from "vue";
import {checkGrayscaleOrg} from '@/api/home/<USER>'
import {mapMutations,mapState} from 'vuex'
import repeatTips from './components/repeatTips.vue'
export default {
  name: 'Home',
  components: {LeftContent,  RightContent, CustomerService,bttomrule,repeatTips},
  // methods:{
  //   ...mapMutations('app', ['SET_ISGRAYUSER']),
  // },
  computed:{    ...mapState({ isGrayUser: state => state.app.isGrayUser }),},
  mounted() {
    // checkGrayscaleOrg().then(res=>{
    //   this.SET_ISGRAYUSER(res.result)
    // })
    window.getMsg("SYSTEM_NOTIFICATION", true, (data) => {
      docTitleScroll(data.title + data.content);
      window.notify(data.title, data.content).then(() => {
        window.focus();
        window.openTab('/orderList', {
          orderNo: data.jumpObjectId
        })
      })
    })
      setTimeout(()=>{
        if(Vue.prototype.home_exposured) return;
        Vue.prototype.home_exposured = true;
        actionTracking('home_exposure', {})
      }, 3000)
      /* setInterval(() => {
        window.notify('sadashd', 'sadsacxjciozxjo')
      }, 1000) */
  }
}
</script>

<style scoped lang="scss">
.home {
  width: 100%;
  min-height: 100%;
  background: #f5f5f5;
  overflow-x: hidden;
}
</style>
