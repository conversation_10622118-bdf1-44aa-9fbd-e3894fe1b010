<template>
    <el-dialog
        :title="'修改供货信息'"
        :visible="dialogVisible"
        width="45%"
        :before-close="handleClose"
        :append-to-body="true"
    >
        <el-form
            :model="form"
            :rules="formRule"
            ref="rangeRef"
            label-width="140px"
            class="demo-ruleForm"
        >
            <el-form-item label="供货信息配置方式:" prop="type">
                <el-radio-group  v-model="form.type">
                    <el-radio label="0">复用原品销售范围</el-radio>
                    <el-radio label="1">配置业务商圈、供货对象、黑白名单</el-radio>
                    <el-radio label="2">人群</el-radio>
                </el-radio-group>
            </el-form-item>
            <el-form-item v-if="form.type === '2'" label="人群范围：">
                <el-button
                size="small"
                type="primary"
                plain
                @click="crowdVisible = true"
                >选择人群</el-button>
                <span style="padding-left: 10px" v-if="baseCustomerGroupId || peopleInfo.id">
                已选：人群ID：{{ baseCustomerGroupId || peopleInfo.id }} ；人群名称：{{ baseCustomerGroupName || peopleInfo.name }}
                </span>
                <el-button
                v-if="peopleInfo.id"
                type="text"
                :disabled="baseCustomerGroupId || (disabled && subOtherDisabled)"
                @click="seeCrowd(peopleInfo.id)"
                >查看详情</el-button>
            </el-form-item>
        </el-form>
        <supplyInformation
          :basic-data="allData"
          :form-model="formModel"
          @supplyDialogClose="supplyDialogClose"
          ref="supplyInformationForm"
        ></supplyInformation>
        <span slot="footer" class="dialog-footer">
            <el-button @click="handleClose" size="small">取 消</el-button>
            <el-button size="small" type="primary" @click="modifyConfirm">确 定</el-button>
        </span>
        <!-- 选择人群 -->
        <crowd-selector-dialog
            ref="changePeople"
            @onSelect="sendPeopleData"
            v-if="crowdVisible"
            v-model="crowdVisible"
            :selected="peopleInfo.id"
        ></crowd-selector-dialog>
        <!-- 查看客户信息 -->
        <CustomerInfoLog
            v-if="showCustomer"
            :market-customer-group-id="peopleInfo.id"
            @cancelModal="cancelDialog"
        />
  </el-dialog>
</template>
<script>
import supplyInformation from '@/views/product/components/supplyInformation'
import CustomerInfoLog from '@/components/customer/customerInfoLog.vue';
import CrowdSelectorDialog from '@/components/xyy/customerOperatoin/crowd-selector-dialog.vue'
export default {
    props: {
        baseCustomerGroupId: {
            type: String,
            default: ""
        },
        dialogVisible: {
            type: Boolean,
            default: true
        }
    },
    components: {
        supplyInformation,
        CustomerInfoLog,
        CrowdSelectorDialog
    },
    data() {
        return {
            form: {
                type: 0, //供货信息配置方式,
                peopleRange: "", // 选择人群
            },
            formModel: {},
            allData: {},
            radio: 0,
            formRule: {
                type: [{ required: true, message: '请选择供货信息配置方式', trigger: 'blur' }],
            },
            peopleInfo: { // 人群信息
                name: '',
                id: '',
            },
            showCustomer: false,
            crowdVisible: false
        }
    },
    methods: {
        handleClose() {
            console.log("close")
            this.$emit("supplyDialogClose", false);
        },
        modifyConfirm() {

        },
        // 添加人群*
        sendPeopleData(value) {
            this.peopleInfo = {
                name: value.tagName,
                id: value.id,
            };
        },
        seeCrowd() {
            this.showCustomer = true;
        },
        cancelDialog() {
            this.showCustomer = false;
        },
        supplyDialogClose() {
            this.$emit("supplyDialogClose")
        }
    }
}
</script>
<style lang="scss" scoped>

</style>