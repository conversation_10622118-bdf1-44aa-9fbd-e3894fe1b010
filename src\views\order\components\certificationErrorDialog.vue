<template>
  <el-dialog title="关联资质异常" :visible="dialogVisible" width="60%" @close="closeDialog">
    <p style="color: red">订单关联的客户资质异常，请与客户或药帮忙业务员沟通及时更新相关资质，并同步更新ERP系统中的资质</p>
    <div>客户名称：{{ (row.merchantInfo || {}).merchantName }}
      <i
        v-if='row.merchantInfo.merchantId && shopConfig.shopPatternCode !== "ybm"'
        class="el-icon-service"
        style="color: #4184d5;cursor: pointer"
        @click="handleOpenService(row.merchantInfo.merchantName)"
      />
    </div>
    <el-table v-loading="loading" :data="list" border height="400" style="width: 100%; margin-top: 10px;">
      <el-table-column prop="content" label="异常类型">
        <template slot-scope="scope">
          <span>{{ scope.row.exceptionTxt }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="content" label="详情" width="300">
        <template slot-scope="scope">
          <p
            v-for="item in scope.row.detailList"
            :key="item.id"
          >
            {{ item.exceptionText }}
            <span
              v-if="scope.row.exceptionType === 2"
              style="font-size: 16px;color: red;margin-left: 5px;cursor: pointer;"
              @click="handleDeleteItem(item)"
            >x</span>
          </p>
        </template>
      </el-table-column>
      <el-table-column prop="operateTime" label="最后操作时间">
        <template slot-scope="scope">
          <div>{{ scope.row.operateTime }}</div>
        </template>
      </el-table-column>
    </el-table>
    <span v-if="!allList" slot="footer" class="dialog-footer">
      <el-button size="small" type="primary" @click="closeDialog">确认</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { getExceptionList, remove } from '@/api/order/index';

export default {
  name: 'CertificationErrorDialog',
  props: {
    dialogVisible: {
      type: Boolean,
      default: false,
    },
    merchantId: {
      type: Number,
      default: null,
    },
    allList: {
      type: Boolean,
      default: false,
    },
    row: {
      type: Object,
      default: () => {},
    },
    shopConfig: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      loading: true,
      list: [],
    };
  },
  mounted() {
    this.getExceptionList();
  },
  methods: {
    handleDeleteItem(item) {
      this.$confirm('确定删除该异常信息？', '删除提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          remove({ exceptionId: item.id, orderNo: this.row.orderNo }).then((res) => {
            if (res.code === 0) {
              this.$message.success(res.msg || '删除成功！');
              this.getExceptionList();
            }
          });
        });
    },
    handleOpenService(key) {
      this.$emit('openService', key);
    },
    closeDialog() {
      this.$emit('cancelDialog');
    },
    getExceptionList() {
      getExceptionList({
        merchantId: this.row.merchantInfo.merchantId,
        orderNo: this.row.orderNo,
      }).then((res) => {
        this.loading = false;
        if (res.code === 0) {
          const { result } = res;
          this.list = result.exceptionList || [];
        }
      });
    },
  },
};
</script>

<style scoped lang="scss">
::v-deep   .el-table thead th {
  background: #f9f9f9;
  border: none;

  .cell {
    font-family: PingFangSC, PingFangSC-Regular;
    font-weight: 400;
    text-align: left;
    color: rgba(51, 51, 51, 0.85);
    line-height: 22px;
  }
}

::v-deep   .el-table__body-wrapper {
  font-size: 12px;
  color: #666666;
}
::v-deep  .el-dialog__body {
  padding-top: 10px;
}
.titlediv {
  padding-bottom: 10px;
  font-size: 16px;
  font-weight: bold;
  color: #ff4d4f;
}
</style>
