<script>
import lDialog from '../../../components/lwq-comp/l-dialog.vue';
import { apiAllBuyers } from '../../../api/customer-management/index'
export default {
  components: {
    lDialog
  },
  props: {
    maxCount: {
      type: Number,
      default: 5
    },
    value: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      list: [],
      form: {
        buyerId: '',
        buyerName: ''
      },
      selected: [],
      visible: false,
      target: false,
      loading: false,
    }
  },
  methods: {
    open() {
      //调查询接口
      this.visible = true;

      //显示已选择
      const timer = setTimeout(() => {
        this.$refs.table.clearSelection();
        this.value.forEach(item => {
          this.$refs.table.toggleRowSelection(item, true)
        })
        clearTimeout(timer)
      },0)
    },
    handleSelectionChange(selected) {
      this.selected = [...selected];
    },
    search() {
      if(this.loading) return;
      this.loading = true;
      apiAllBuyers({...this.form}).then(res => {
        if (res.code == 0) {
          this.list = res.data;
        } else {
          this.$message.error(res.message);
        }
      }).finally(() => {
        this.loading = false;
      })
    }
  }
}
</script>

<template>
  <div style="display: inline-block;">
    <div @click="open" style="display: flex;">
      <slot>
        <el-button size="mini" type="primary">添加客户</el-button>
      </slot>
    </div>
    <lDialog :visible.sync="visible" title="添加客户" width="700px" @close="visible = false;">
      <el-row>
        <el-col :xs="16" :md="12" :lg="8" :xl="8" style="margin-right: 10px;">
          <el-input placeholder="药店ID" size="mini" v-model="form.buyerId" clearable>
            <template slot="prepend">药店ID</template>
          </el-input>
        </el-col>
        <el-col :xs="16" :md="12" :lg="8" :xl="8">
          <el-input placeholder="药店名称" size="mini" v-model="form.buyerName" clearable>
            <template slot="prepend">药店名称</template>
          </el-input>
        </el-col>
      </el-row>
      <div style="display: flex;justify-content: end;">
        <el-button size="mini" type="primary" @click="search">查询</el-button>
        <el-button size="mini" @click="form = { buyerId: '', buyerName: '' };list = [];">重置</el-button>
      </div>
      <el-table ref="table" :data="list" border stripe style="margin: 10px 0;" height="300"  @selection-change="handleSelectionChange" row-key="merchantId">
        <el-table-column align="center" type="selection" width="40" reserve-selection></el-table-column>
        <el-table-column align="center" prop="merchantId" label="药店ID" width="180"></el-table-column>
        <el-table-column align="center" prop="erpCode" label="ERP编码" width="250"></el-table-column>
        <el-table-column align="center" prop="merchantName" label="药店名称" width="250"></el-table-column>
        <el-table-column align="center" prop="accountStatusDesc" label="开户状态" width="100"></el-table-column>
        <el-table-column align="center" prop="address" label="药店地址" width="150"></el-table-column>
      </el-table>
      <div slot="footer" style="display: flex;justify-content: end;">
        <el-button size="mini" @click="visible = false;form = { buyerId: '', buyerName: '' };list = [];">关闭</el-button>
        <el-button size="mini" type="primary" @click="$emit('input', [...selected]);visible = false;form = { buyerId: '', buyerName: '' };list = [];">确定</el-button>
      </div>
    </lDialog>
  </div>
</template>

<style scoped>
</style>
