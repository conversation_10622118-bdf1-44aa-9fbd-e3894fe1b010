<template>
  <div class="baseInfoBox">
    <div v-for="(item, index) in bussinessList" :key="item.orgId" class="bussinessItem">
      <div class="baseBox">
        <div class="nameTop">
          <div class="nameBox">
            <img class="shopNameIcon" src="../../assets/h5/shopIcon.png" alt="">
            <span class="nameTitle">{{ item.companyName }}</span>
          </div>
          <div class="btnBox" @click="showOrHide(index)">
            <span class="btn">{{ item.showMore ? '收起' : '展开' }}</span>
            <img class="btnIcon" :class="!item.showMore ? 'btnIcon2' : ''" src="../../assets/h5/more.png" alt="">
          </div>
        </div>
        <div class="commonTop">企业编码：{{ item.orgId }}</div>
        <div class="commonTop">店铺名称：{{ item.name }}</div>
      </div>
      <div class="detailInfo" v-show="item.showMore">
        <div class="titleItemBox">
          <div class="itemTitle">
            <div class="leftBorder"></div>
            企业工商资质
          </div>
          <div class="tableBox">
            <div class="tableTitle">
              <div class="name">资质名称</div>
              <div class="date">有效期至</div>
              <div class="enclosure">附件</div>
            </div>
            <div class="tableItem" v-for="tableItem in item.corporationCommonQualifications" :key="tableItem.id">
              <div class="name">{{ tableItem.name }}</div>
              <div class="date" :class="tableItem.expireState == 2 ? 'errorText' : tableItem.expireState == 2 ? 'warnText' : ''">
                {{ formatDate(tableItem.endDate, 'YMD') }}
              </div>
              <div class="enclosure">
                <span v-if="tableItem.urls.length" class="seeText" @click="checkImgs(tableItem)">查看({{ tableItem.urls.length }})</span>
                <span v-else>未提交</span>
              </div>
            </div>
          </div>
        </div>
        <div class="titleItemBox">
          <div class="itemTitle">
            <div class="leftBorder"></div>
            企业经营资质
          </div>
          <div class="tableBox">
            <div class="tableTitle">
              <div class="name">资质名称</div>
              <div class="date">有效期至</div>
              <div class="enclosure">附件</div>
            </div>
            <div class="tableItem" v-for="tableItem in item.corporationBusinessQualifications" :key="tableItem.id">
              <div class="name">{{ tableItem.name }}</div>
              <div class="date" :class="tableItem.expireState == 2 ? 'errorText' : tableItem.expireState == 2 ? 'warnText' : ''">
                {{ formatDate(tableItem.endDate, 'YMD') }}
              </div>
              <div class="enclosure">
                <span v-if="tableItem.urls.length" class="seeText" @click="checkImgs(tableItem)">查看({{ tableItem.urls.length }})</span>
                <span v-else>未提交</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <ImgDialog v-if="showImg" :img-item="imgItem" @handleCancel="closeDialog" />
  </div>
</template>
<script>
import { listCorpQualificationByPoiId } from '../../api/storeManagement/index';
import ImgDialog from './imgDialog.vue';
export default {
  name: 'QualInfo',
  components: { ImgDialog },
  data() {
    return {
      bussinessList: [],
      imgItem: {},
      showImg: false,
    };
  },
  created() {
    this.getBascInfo();
  },
  methods: {
    getBascInfo() {
      const poiId = this.util.getUrlParam('poiId');
      listCorpQualificationByPoiId({ poiId }).then((res) => {
        this.bussinessList = res.data || [];
        this.bussinessList.forEach((item, index) => {
          if (index === 0) {
            this.$set(item, 'showMore', true);
          } else {
            this.$set(item, 'showMore', false);
          }
        })
      })
    },
    showOrHide(actIndex) {
      this.bussinessList.forEach((item, index) => {
        if (index === actIndex) {
          item.showMore = !item.showMore;
        } else {
          item.showMore = false;
        }
      })
    },
    checkImgs(tableItem) {
      this.imgItem = tableItem || {};
      this.showImg = true;
    },
    closeDialog() {
      this.showImg = false;
    }
  },
};
</script>

<style scoped lang="scss">
  .baseInfoBox {
    margin: 0 0.2rem;
    .bussinessItem {
      background: #fff;
      border-radius: 0.2rem;
      padding: 0.2rem;
      margin-bottom: 0.2rem;
      .baseBox {
        .nameTop {
          display: flex;
          align-items: center;
          justify-content: space-between;
          .nameBox, .btnBox {
            display: flex;
            align-items: center;
          }
          .shopNameIcon {
            width: 0.4rem;
            height: 0.4rem;
            margin-right: 4px;
          }
          .nameTitle {
            color: #333333;
            font-weight: bold;
            font-size: 0.28rem;
          }
          .btn {
            color: #9494A6;
            font-size: 0.28rem;
            margin-right: 4px;
          }
          .btnIcon {
            width: 0.26rem;
            height: 0.26rem;
          }
          .btnIcon2 {
            transform: rotate(180deg);
          }
        }
        .commonTop {
          color: #292933;
          font-size: 0.2rem;
          margin-top: 0.1rem;
        }
      }
      .detailInfo {
        background: #fff;
        border-top: 1px solid #EEEFF0;
        margin-top: 0.2rem;
        .titleItemBox {
          margin-top: 0.2rem;
        }
        .itemTitle {
          display: flex;
          align-items: center;
          color: #292933;
          margin-bottom: 0.2rem;
          font-weight: bold;
          .leftBorder {
            width: 0.06rem;
            height: 0.4rem;
            background: #00B377;
            border-radius: 3.5px;
            margin-right: 0.1rem;
          }
        }
        .tableBox {
          margin-top: 0.2rem;
          font-size: 0.2rem;
          border: 1px solid #EEEFF0;
          border-radius: 0.1rem;
          .tableTitle {
            display: flex;
            align-items: center;
            text-align: center;
            color: #676773;
            background: #F1F6F9;
          }
          .tableItem {
            display: flex;
            align-items: center;
            text-align: center;
            border-top: 1px solid #EEEFF0;
          }
          .name, .date, .enclosure {
            padding: 0.48rem 0.2rem;
            height: .6rem;
            // line-height: .6rem;
            display: flex;
            align-items: center;
            justify-content: center;
          }
          .name {
            width: 3rem;
          }
          .date {
            width: 2rem;
            border-left: 1px solid #EEEFF0;
            border-right: 1px solid #EEEFF0;
          }
          .errorText {
            color: #FF2121;
          }
          .warnText {
            color: #ECA100;
          }
          .enclosure {
            width: 1.5rem;
            .seeText {
              color: #00B377;
            }
          }
        }
      }
    }
  }
</style>
