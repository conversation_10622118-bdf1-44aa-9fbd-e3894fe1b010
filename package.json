{"name": "popmerchant", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "build:dev": "vue-cli-service build --mode development", "build:test": "vue-cli-service build --mode test", "build:stage": "vue-cli-service build --mode stage", "build:staging": "vue-cli-service build --mode stage", "build:prod": "vue-cli-service build", "lint-staged": "lint-staged"}, "dependencies": {"axios": "^0.20.0", "core-js": "^3.6.5", "dayjs": "^1.9.4", "dplayer": "^1.27.1", "echarts": "^5.1.2", "element-ui": "^2.13.2", "hls.js": "^1.5.18", "jquery": "^3.5.1", "jszip": "^3.10.1", "lodash": "^4.17.21", "moment": "^2.24.0", "pdfjs-dist": "^2.0.943", "postcss-loader": "^4.0.4", "qrcodejs2": "0.0.2", "quill": "^1.3.7", "swiper": "^6.4.5", "vue": "^2.6.11", "vue-awesome-swiper": "^4.1.1", "vue-clipboard2": "^0.3.3", "vue-draggable-plus": "^0.6.0", "vue-pdf": "^4.3.0", "vue-quill-editor": "^3.0.6", "vue-router": "^3.2.0", "vue-smooth-dnd": "^0.8.1", "vuex": "^3.4.0", "file-saver": "^2.0.5"}, "devDependencies": {"@babel/plugin-syntax-dynamic-import": "^7.8.3", "@vue/cli-plugin-babel": "~4.5.0", "@vue/cli-plugin-eslint": "~4.5.0", "@vue/cli-plugin-router": "~4.5.0", "@vue/cli-plugin-vuex": "~4.5.0", "@vue/cli-service": "~4.5.0", "@vue/eslint-config-airbnb": "^5.0.2", "@xyy-ec/eslint-config-ecfe-base": "^1.0.5", "@xyy-ec/eslint-config-ecfe-vue": "^1.0.6", "babel-eslint": "^10.1.0", "eslint": "^6.7.2", "eslint-config-airbnb-base": "^14.1.0", "eslint-config-prettier": "^6.11.0", "eslint-plugin-import": "^2.20.2", "eslint-plugin-prettier": "^3.1.3", "eslint-plugin-vue": "^7.7.0", "husky": "^4.3.0", "less": "^3.0.4", "less-loader": "^5.0.0", "lint-staged": "^10.5.2", "prettier": "^2.0.5", "sass": "1.26.2", "sass-loader": "^8.0.2", "vue-template-compiler": "^2.6.11"}, "husky": {"hooks": {"post-merge": "yarn", "post-checkout": "yarn"}}, "lint-staged": {"src/**/*.{js,vue}": ["eslint 'src/**/*.{js,vue}' --fix"]}}