import axios from 'axios';
import { Message } from 'element-ui';
import Vue from 'vue';
import { trackingIdentify } from '@/track/eventTracking';
// eslint-disable-next-line no-underscore-dangle
let baseURL = null;
switch (__env__) {
  case 'devlpoment':
    baseURL = '/api';
    // baseURL = 'https://pop.test.ybm100.com'
    break;
  case 'dev':
    baseURL = 'https://yapi.int.ybm100.com/mock/65'
    break;
  case 'test':
    baseURL = 'https://pop.test.ybm100.com';
    break;
  case 'stage':
    baseURL = 'https://pop-new.stage.ybm100.com';
    break;
  case 'prod':
    baseURL = 'https://pop.ybm100.com';
    break;
}

// post请求头
axios.defaults.headers.post['Content-Type'] = 'application/json';
const service = axios.create({
  // eslint-disable-next-line no-underscore-dangle
  // baseURL: process.env.NODE_ENV === 'development' ? '/api' : '/', // url = base url + request url
  baseURL,
  withCredentials: true,
  timeout: 5000000, // request timeout
});
// request interceptor
service.interceptors.request.use(
  config => config,
  error => Promise.reject(error),
);
function initOrgId() {
  if (Vue.prototype.orgId) return;

  const orgId = document.cookie.split(';')
    .find(item => item.split('=')[0].trim() === 'org_id')
    ?.split('=')[1];

  Vue.prototype.orgId = orgId;
  trackingIdentify(orgId);
}
// response interceptor
service.interceptors.response.use(
  (response) => {
    const res = response.data;
    const { headers } = response;
    initOrgId();
    // console.log(headers)
    // if (
    //   response.data
    //   && typeof response.data === 'string'
    //   && response.data.indexOf('<!DOCTYPE HTML>') > -1
    // ) {
    //   window.location.href = process.env.VUE_APP_BASE_API;
    //   return true;
    // }
    if (headers.sessionstatus && headers.sessionstatus === 'timeout') {
      const ua = navigator.userAgent.toLowerCase();
      const isWeixin = ua.indexOf('micromessenger') !== -1;
      if (isWeixin) {
        window.location.href = `${process.env.VUE_APP_BASE_API}/manage/#/h5Dashboard`;
      } else {
        // eslint-disable-next-line no-alert
        alert('登录失效了');
        window.location.href = process.env.VUE_APP_BASE_API;
      }
      response = { error: '登录过期' };
      return Promise.reject(response);
    }
    if (response.status >= 200 && response.status < 300) {
      return res;
    }
    if (!window.isMobile()) {
      Message({
        message: '网络异常，请稍后再尝试',
        type: 'error',
      });
    }
    return res;
  },
  (error) => {
    // if (process.env.NODE_ENV !== 'development') {
    //   window.location.href = process.env.VUE_APP_BASE_API;
    // }
    if (!window.isMobile()) {
      Message({
        message: error.message,
        type: 'error',
      });
    }
    return Promise.reject(error);
  },
);

export default service;
