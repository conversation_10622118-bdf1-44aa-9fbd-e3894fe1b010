<template>
  <div v-if="!item.hidden">
    <template

      v-if="hasOneShowingChild(item.children,item) && (!onlyOneChild.children||onlyOneChild.noShowingChildren)&&!item.alwaysShow" >
      <app-link v-if="onlyOneChild.meta" :to="resolvePath(onlyOneChild.path)" class="abc" >
        <el-menu-item :index="resolvePath(onlyOneChild.path)" :class="{'submenu-title-noDropdown':!isNest}">
          <item :isGrayUser="isGrayUser" :icon="onlyOneChild.meta.icon||(item.meta&&item.meta.icon)" :title="onlyOneChild.meta.title" :badge="onlyOneChild.meta.badge" :count="navRedMap[onlyOneChild.meta.redDotNumKey]" :showRedDotNum="onlyOneChild.meta.showRedDotNum"/>
        </el-menu-item>
      </app-link>
    </template>

    <el-submenu v-else ref="subMenu" :index="resolvePath(item.path)" popper-append-to-body>
      <template slot="title">
        <item :isGrayUser="isGrayUser" v-if="item.meta" :icon="item.meta && item.meta.icon" :title="item.meta.title" :badge="item.meta.badge" :count="navRedMap[item.meta.redDotNumKey]" :showRedDotNum="item.meta.showRedDotNum"/>
      </template>
      <sidebar-item
        v-for="child in item.children"
        :key="child.path"
        :is-nest="true"
        :item="child"
        :count="navRedMap[child.meta.redDotNumKey]"
        :base-path="resolvePath(child.path)"
        class="nest-menu"
      />
    </el-submenu>
  </div>
</template>

<script>
import path from 'path'
import {isExternal} from '@/utils/util'
import Item from './Item'
import AppLink from './Link'
import {mapState,mapMutations} from "vuex";
import {getUrgeDeliveryCount} from "@/api/urge-delivery"

export default {
  name: 'SidebarItem',
  components: {Item, AppLink},
  props: {
    // route object
    item: {
      type: Object,
      required: true
    },
    isNest: {
      type: Boolean,
      default: false
    },
    basePath: {
      type: String,
      default: ''
    },
    count: {
      default: ''
    }
  },
  computed:{
    ...mapState('permission', ['pendingShipmentReminderQuantity', 'navRedMap']),
    ...mapState('app', ['isGrayUser']),
  },
  mounted() {
    window.flashUrgeCount = () => {
      getUrgeDeliveryCount().then(res => {
        if(res && Number(res.code) === 0) {
          this.SET_COUNT(res.result)
        }
      })
    }
  },
  data() {
    this.onlyOneChild = null
    return {
      urgeCount: 0,
      curPath:''
    }
  },
  watch: {
    // 监听$route对象的变化
    $route(to, from) {
      this.curPath = to.path
      // console.log('路由发生了变化');
      // console.log('从', from.path, '跳转到', to.path);
      // 在这里可以根据路由变化后的具体情况，执行更多自定义逻辑，比如根据不同的路由参数来重新请求数据等
    }
  },
  methods: {
    hhh(item){
      item.$parent.$parent.$parent.$el.querySelector('.menu-round').style.backgroundColor = "#4184d5"
      console.log('%c [ item ]-89', 'font-size:13px; background:pink; color:#bf2c9f;', item.$parent.$parent.$parent.$el.querySelector('.menu-round'))

    },
    bd(a){
      // console.log('%c [ a ]-89', 'font-size:13px; background:pink; color:#bf2c9f;', a)
      // let span =  document.querySelector('.abc span')
      // console.log('%c [ span ]-88', 'font-size:13px; background:pink; color:#bf2c9f;', span)
      // span.style.backgroundColor = "#4184d5"
    },
    ...mapMutations('permission', ['SET_COUNT']),
    hasOneShowingChild(children = [], parent) {
      const showingChildren = children.filter(item => {
        if (item.hidden) {
          return false
        } else {
          this.onlyOneChild = item
          return true
        }
      })
      if (showingChildren.length === 1) {
        return true
      }
      if (showingChildren.length === 0) {
        this.onlyOneChild = {...parent, path: '', noShowingChildren: true}
        return true
      }
      return false
    },
    resolvePath(routePath) {
      if (isExternal(routePath)) {
        return routePath
      }
      if (isExternal(this.basePath)) {
        return this.basePath
      }
      return path.resolve(this.basePath, routePath)
    }
  }
}
</script>
<style scoped>

</style>
