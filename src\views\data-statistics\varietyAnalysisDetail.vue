<template>
  <div class="varietyAnalysis">
    <template v-if="!showProductInfo">
      <div class="title_line">商品信息</div>
      <el-row style="line-height: 30px; margin-bottom: 20px">
        <el-col :span="20"
          >商品名称：{{ $route.query.productName }} &nbsp; &nbsp; &nbsp; 规格：{{
            $route.query.spec
          }}
          &nbsp; &nbsp; &nbsp; 生产厂家：{{ $route.query.manufacturer }}</el-col
        >
        <el-col :span="4" style="text-align: right">
          <el-button type="primary" @click="backToList" size="small">返 回</el-button>
        </el-col>
      </el-row>

      <div class="title_line">商品明细</div>
      <SearchForm
        style="margin-bottom: 15px"
        v-if="showForm"
        ref="searchForm"
        :model="formModel"
        :form-items="formItems"
        :hasOpenBtn="false"
        @submit="handleFormSubmit"
        @reset="handleFormReset"
        :key="formModel.statisticalPlacer"
      >
        <template slot="form-item-time">
          <el-select
            style="min-width: 120px; width: 120px"
            v-model="formModel.statisticalPlacer"
            @change="timeTypeChange"
            :key="formModel.statisticalPlacer"
          >
            <el-option
              v-for="item in timeTypeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
          <el-date-picker
            :key="formModel.statisticalPlacer"
            v-if="formModel.statisticalPlacer == 0 || formModel.statisticalPlacer == 5"
            v-model="formModel.time"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="yyyy-MM-dd"
            :picker-options="pickerOptions"
          >
          </el-date-picker>
          <el-date-picker
            v-if="formModel.statisticalPlacer == 1"
            v-model="formModel.time"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="yyyy-MM-dd"
            :picker-options="pickerOptions1"
          >
          </el-date-picker>
          <el-date-picker
            v-if="formModel.statisticalPlacer == 2"
            v-model="formModel.time"
            type="week"
            format="yyyy 第 WW 周"
            placeholder="选择周"
            :picker-options="pickerOptions2"
          >
          </el-date-picker>
          <el-date-picker
            v-if="formModel.statisticalPlacer == 3"
            v-model="formModel.time"
            type="monthrange"
            placeholder="选择月"
            :picker-options="pickerOptions3"
          >
          </el-date-picker>
        </template>
      </SearchForm>

      <xyyTable
        :key="isSold"
        ref="withdrawalRecord"
        v-loading="tableLoading"
        :data="tableConfig.data"
        :col="isSold == 1 ? tableConfig.col : tableConfig.col1"
        :list-query="tableConfig.pagConfig"
        @get-data="queryList"
      >
        <template slot="suggestPrice">
          <el-table-column label="建议价格">
            <template slot="header" slot-scope="scope">
              建议价格
              <el-tooltip
                class="item"
                content="所选时间范围内商品的采购数量（去除这段时间已取消和已退款完成的商品数量）"
                placement="top"
              >
                <i class="el-icon-warning-outline"></i>
              </el-tooltip>
            </template>
            <template slot-scope="{ row }">
              <div v-if="row.suggestPriceMin == row.suggestPriceMax">{{ row.suggestPriceMax }}</div>
              <div v-else>
                {{ (row.suggestPriceMin || '') + ' - ' + (row.suggestPriceMax || '') }}
              </div>
            </template>
          </el-table-column>
        </template>
        <template slot="fob">
          <el-table-column label="价格">
            <template slot="header">
              价格
              <el-tooltip
                class="item"
                :content="isSold == 1 ? '实付总金额/销量' : '药帮忙价'"
                placement="top"
              >
                <i class="el-icon-warning-outline"></i>
              </el-tooltip>
            </template>
            <template slot-scope="{ row }">
              <div>{{ row.fob }}</div>
            </template>
          </el-table-column>
        </template>
        <template slot="productCt">
          <el-table-column label="销量" width="110">
            <template slot="header">
              销量
              <el-tooltip
                class="item"
                content="统计时间范围内订单中商品的采购数量 减去 订单退款成功的商品采购数量"
                placement="top"
              >
                <i class="el-icon-warning-outline"></i>
              </el-tooltip>
            </template>
            <template slot-scope="{ row }">
              <div>{{ row.productCt }}</div>
            </template>
          </el-table-column>
        </template>
        <template slot="payAmount">
          <el-table-column label="销售额" width="110">
            <template slot="header">
              销售额
              <el-tooltip
                class="item"
                content="统计时间范围内订单中商品的原总金额 减去 订单退款成功的原总金额"
                placement="top"
              >
                <i class="el-icon-warning-outline"></i>
              </el-tooltip>
            </template>
            <template slot-scope="{ row }">
              <div>{{ row.payAmount }}</div>
            </template>
          </el-table-column>
        </template>
        <template slot="realPayAmount">
          <el-table-column label="实付总金额" width="120">
            <template slot="header">
              实付总金额
              <el-tooltip
                class="item"
                content="统计时间范围内订单中商品的实付金额 减去 订单退款成功的商品金额"
                placement="top"
              >
                <i class="el-icon-warning-outline"></i>
              </el-tooltip>
            </template>
            <template slot-scope="{ row }">
              <div>{{ row.realPayAmount }}</div>
            </template>
          </el-table-column>
        </template>
        <template slot="unitPrice">
          <el-table-column label="客单价" width="120">
            <template slot="header">
              客单价
              <el-tooltip class="item" content="实付总金额/采购药店数" placement="top">
                <i class="el-icon-warning-outline"></i>
              </el-tooltip>
            </template>
            <template slot-scope="{ row }">
              <div>{{ row.unitPrice }}</div>
            </template>
          </el-table-column>
        </template>
        <template slot="unitCt">
          <el-table-column label="平均每单购买量" width="140">
            <template slot="header">
              平均每单购买量
              <el-tooltip class="item" content="销量/采购订单数" placement="top">
                <i class="el-icon-warning-outline"></i>
              </el-tooltip>
            </template>
            <template slot-scope="{ row }">
              <div>{{ row.unitCt }}</div>
            </template>
          </el-table-column>
        </template>
        <template slot="clickCt">
          <el-table-column label="点击量">
            <template slot="header">
              点击量
              <el-tooltip
                class="item"
                content="客户点击商品信息，进入商品详情页次数"
                placement="top"
              >
                <i class="el-icon-warning-outline"></i>
              </el-tooltip>
            </template>
            <template slot-scope="{ row }">
              <div>{{ row.clickCt }}</div>
            </template>
          </el-table-column>
        </template>
        <template slot="visitorCt">
          <el-table-column label="访客量">
            <template slot="header">
              访客量
              <el-tooltip class="item" content="浏览商品的客户数" placement="top">
                <i class="el-icon-warning-outline"></i>
              </el-tooltip>
            </template>
            <template slot-scope="{ row }">
              <div>{{ row.visitorCt }}</div>
            </template>
          </el-table-column>
        </template>
        <template slot="activityType">
          <el-table-column label="商品类型" width="120">
            <template slot-scope="{ row }">
              <div>{{ activityTypeAry[row.activityType] || '' }}</div>
            </template>
          </el-table-column>
        </template>
        <template slot="shoppingCartUserCt">
          <el-table-column label="加购药店数">
            <template slot="header">
              加购药店数
              <el-tooltip class="item" content="有加购行为的客户数" placement="top">
                <i class="el-icon-warning-outline"></i>
              </el-tooltip>
            </template>
            <template slot-scope="{ row }">
              <div>{{ row.shoppingCartUserCt }}</div>
            </template>
          </el-table-column>
        </template>
        <template slot="userCt">
          <el-table-column label="采购药店数" width="120">
            <template slot="header">
              采购药店数
              <el-tooltip class="item" content="有采购行为的客户数" placement="top">
                <i class="el-icon-warning-outline"></i>
              </el-tooltip>
            </template>
            <template slot-scope="{ row }">
              <div>{{ row.userCt }}</div>
            </template>
          </el-table-column>
        </template>
        <template slot="orderCt">
          <el-table-column label="采购订单数" width="120">
            <template slot="header">
              采购订单数
              <el-tooltip class="item" content="采购订单数（不含退款订单）" placement="top">
                <i class="el-icon-warning-outline"></i>
              </el-tooltip>
            </template>
            <template slot-scope="{ row }">
              <div>{{ row.orderCt }}</div>
            </template>
          </el-table-column>
        </template>
      </xyyTable>
    </template>
  </div>
</template>

<script>
import SearchForm from '@/components/searchForm'
import exportTip from '@/views/other/components/exportTip'
import { actionTracking } from '@/track/eventTracking'
import { ExposureTool, VNodeExposureTool } from '@/utils/exposureTools'

import { selectPage, apiSelectSubPage } from '@/api/data-statistics/varietyAnalysis'

export default {
  name: 'varietyAnalysis',
  components: {
    SearchForm,
    exportTip
  },
  data() {
    return {
      showForm: true,
      isSold: 1,
      activityTypeAry: ['普通商品', '拼团商品', '赠品'],
      barcode: '',
      productCode: '',
      originalSkuId: '',
      formModel: {
        barcode: '',
        statisticalPlacer: 0,
        time: ''
      },
      formItems: [
        {
          label: '商品编码',
          prop: 'barcode',
          component: 'el-input',
          colSpan: 4,
          attrs: {
            placeholder: '请输入商品编码'
          }
        },
        {
          label: '时间',
          slotName: 'time',
          colSpan: 9
        }
      ],
      timeTypeOptions: [
        {
          label: '今日',
          value: 5
        },
        {
          label: '近七天',
          value: 0
        },
        {
          label: '按天统计',
          value: 1
        },
        {
          label: '按周统计',
          value: 2
        },
        {
          label: '按月统计',
          value: 3
        }
      ],
      pickerOptions: {
        disabledDate: (time) => {
          return true
        }
      },
      pickerOptions1: {
        disabledDate: (time) => {
          const day_1 = new Date().getTime() - 24 * 3600 * 1000
          const day_31 = new Date().getTime() - 24 * 3600 * 1000 * 32
          return time.getTime() < day_31 || time.getTime() > day_1
        }
      },
      pickerOptions2: {
        firstDayOfWeek: 1,
        disabledDate: (time) => {
          const day_1 = new Date().getTime() - 24 * 3600 * 1000
          const day_180 = new Date().getTime() - 24 * 3600 * 1000 * 180
          return time.getTime() < day_180 || time.getTime() > day_1
        }
      },
      pickerOptions3: {
        disabledDate: (time) => {
          const day_1 = new Date().getTime() - 24 * 3600 * 1000
          const day_365 = new Date().getTime() - 24 * 3600 * 1000 * 365
          return time.getTime() < day_365 || time.getTime() > day_1
        }
      },
      tableLoading: false,
      tableConfig: {
        data: [],
        col: [
          {
            name: '商品编码',
            index: 'barcode',
            width: 160
          },
          {
            name: '商品类型',
            index: 'activityType',
            slot: 'activityType'
          },
          {
            name: '价格',
            index: 'fob',
            slot: 'fob'
          },
          {
            name: '销量',
            index: 'productCt',
            slot: 'productCt'
          },
          {
            name: '销售额',
            index: 'payAmount',
            slot: 'payAmount'
          },
          {
            name: '实付总金额',
            index: 'realPayAmount',
            slot: 'realPayAmount'
          },
          {
            name: '客单价',
            index: 'unitPrice',
            slot: 'unitPrice'
          },
          {
            name: '平均每单购买量',
            index: 'unitCt',
            slot: 'unitCt'
          },
          {
            name: '点击量',
            index: 'clickCt',
            slot: 'clickCt'
          },
          {
            name: '访客量',
            index: 'visitorCt',
            slot: 'visitorCt'
          },
          {
            name: '加购药店数',
            index: 'shoppingCartUserCt',
            slot: 'shoppingCartUserCt'
          },
          {
            name: '采购药店数',
            index: 'userCt',
            slot: 'userCt'
          },
          {
            name: '采购订单数',
            index: 'orderCt',
            slot: 'orderCt'
          }
        ],
        col1: [
          {
            name: '商品编码',
            index: 'barcode',
            width: 160
          },
          {
            name: '商品类型',
            index: 'activityType',
            slot: 'activityType'
          },
          {
            name: '价格',
            index: 'fob',
            slot: 'fob'
          },
          {
            name: '建议价格',
            index: 'suggestPrice',
            slot: 'suggestPrice'
          },
          {
            name: '点击量',
            index: 'clickCt',
            slot: 'clickCt'
          },
          {
            name: '访客量',
            index: 'visitorCt',
            slot: 'visitorCt'
          },
          {
            name: '加购药店数',
            index: 'shoppingCartUserCt',
            slot: 'shoppingCartUserCt'
          }
        ],
        pagConfig: {
          pageSize: 10,
          page: 1,
          total: 0
        }
      },
      changeExport: false,
      showProductInfo: false,
      productInfo: {}
    }
  },
  created() {
    console.log('this.$route.query:::::::::', this.$route.query)
    this.isSold = this.$route.query.isSold

    if (this.$route.query.statisticalPlacer == 2) {
      this.formModel.time = new Date(Number(this.$route.query.time))
    } else {
      this.formModel.time = [this.$route.query.startTime, this.$route.query.endTime]
    }

    // this.formModel.barcode = this.$route.query.barcode
    this.formModel.statisticalPlacer = Number(this.$route.query.statisticalPlacer)
    this.initialFormModelData = { ...this.formModel }
    // this.timeTypeChange(Number(this.formModel.statisticalPlacer))
    this.queryList()
  },

  mounted() {},

  methods: {
    getParams() {
      const params = { ...this.formModel }
      if (params.statisticalPlacer === 2) {
        params.startTime = window.dayjs(params.time).startOf('week').format('YYYY-MM-DD')
        params.endTime = window.dayjs(params.time).endOf('week').format('YYYY-MM-DD')
      } else if (params.statisticalPlacer === 3) { // 按月统计
        params.startTime = window.dayjs(params.time[0])
          .startOf('month')
          .format('YYYY-MM-DD');
        params.endTime = window.dayjs(params.time[1])
          .endOf('month')
          .format('YYYY-MM-DD');
      } else if (
        (params.statisticalPlacer === 0 || params.statisticalPlacer === 1) &&
        Array.isArray(params.time)
      ) {
        params.startTime = params.time[0] ? window.dayjs(params.time[0]).format('YYYY-MM-DD') : ''
        params.endTime = params.time[1] ? window.dayjs(params.time[1]).format('YYYY-MM-DD') : ''
      }
      if (params.endTime && window.dayjs().unix() < window.dayjs(params.endTime).unix()) {
        //结束时间大于了当前时间
        params.endTime = window.dayjs().subtract(1, 'day').format('YYYY-MM-DD')
      }

      // params.barcode = params.barcode // || this.$route.query.barcode
      params.productCode = this.$route.query.productCode
      params.originalSkuId = this.$route.query.originalSkuId
      params.isSold = this.$route.query.isSold

      delete params.time
      return params
    },
    async queryList(listQuery) {
      let params = this.getParams()
      if (!params) return
      this.tableLoading = true
      if (listQuery) {
        const { pageSize, page } = listQuery
        this.tableConfig.pagConfig.pageSize = pageSize
        this.tableConfig.pagConfig.page = page
      }
      const { pageSize, page } = this.tableConfig.pagConfig
      params.pageNum = page
      params.pageSize = pageSize
      console.log(JSON.stringify(params))
      try {
        const res = await apiSelectSubPage(params)
        if (res && res.code === 0) {
          this.tableConfig.data = res.result.list || []
          this.tableConfig.pagConfig.total = res.result.total
        } else {
          this.$message.error(res.message || '请求异常')
        }
      } catch (e) {
        console.log(e)
      }
      this.tableLoading = false
    },
    handleFormSubmit() {
      this.tableConfig.pagConfig.page = 1
      this.queryList()
    },
    handleFormReset() {
      this.formModel = { ...this.initialFormModelData }
      this.timeTypeChange(this.formModel.statisticalPlacer)
      this.queryList()
    },
    timeTypeChange(val) {
      console.log(val)
      this.showForm = false
      if (val === 0) {
        this.formModel.time = [
          window.dayjs().subtract(7, 'day').format('YYYY-MM-DD'),
          window.dayjs().subtract(1, 'day').format('YYYY-MM-DD')
        ]
      } else if (val === 1) {
        this.formModel.time = [
          window.dayjs().subtract(1, 'day').format('YYYY-MM-DD'),
          window.dayjs().subtract(1, 'day').format('YYYY-MM-DD')
        ]
      } else if (val === 2) {
        this.formModel.time = window.dayjs().startOf('week').format('YYYY-MM-DD')
      } else if (val === 3) {
        this.formModel.time = [window.dayjs().format('YYYY-MM'), window.dayjs().format('YYYY-MM')]
      } else if (val === 5) {
        this.formModel.time = [
          window.dayjs().format('YYYY-MM-DD'),
          window.dayjs().format('YYYY-MM-DD')
        ]
      }

      this.$nextTick(() => {
        this.showForm = true
      })
    },
    backToList() {
      this.$router.back()
      this.$nextTick(() => {
        this.$destroy('varietyAnalysis/detail')
      })
    }
  }
}
</script>

<style scoped lang="scss">
.varietyAnalysis {
  padding: 16px 16px;
  background: #fff;

  ::v-deep   .el-input-group--prepend {
    height: 32px;
    line-height: 32px;

    .el-date-editor .el-range-separator {
      line-height: 24px;
    }

    .el-date-editor .el-range__icon {
      line-height: 24px;
    }
  }

  .operations {
    margin-bottom: 12px;
  }
  ::v-deep   .el-table tr td .cell {
    height: auto !important;
    line-height: 20px !important;
  }
}
</style>
