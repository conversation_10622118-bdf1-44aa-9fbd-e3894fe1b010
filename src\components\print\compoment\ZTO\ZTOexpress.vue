<template>
  <div class="content" style="margin: 0 auto;font-weight: bold;font-family: 黑体;box-sizing: border-box;font-size: 12px;color: #000;border: 1px solid #000;"
    :style="{width:config.templateSize === '76mm*130mm'?'70mm':'96mm'}"
  >
    <div class="page-row" style="height: 18mm;border-bottom: 1px solid #000;text-align: left;background-color: #fff;overflow: hidden;position: relative">
      <div style="width:48%;height:48%;overflow: hidden">
        <div>
          <img style="padding: 0;margin: 0;width: 100%" src="https://oss-ec.ybm100.com/ybm/popDeliverLog/zto_logo.png" alt="">
        </div>
      </div>
      <div style="font-size: 16px;font-weight: bold;position: absolute;top: 0;right: 5px;width: 25%;height: 35%">
        <div v-if="config.isVip">
          <img style="padding: 0;margin: 0;width: 100%" src="https://oss-ec.ybm100.com/ybm/popDeliverLog/zto_vip_logo.png" alt="">
        </div>
        <div v-else style="padding: 10px 0;">普通订单</div>
      </div>
      <div style="padding-left: 2px;padding-top: 2px"><span>第{{ config.printCount }}次打印</span><span
        style="padding-left: 2px"
      > 打印时间:{{ formatDate(config.printTime) }} </span></div>
      <div style="text-align: center;font-size: 18px;font-weight: bold;"><span>{{ zTFace.markDestination
        }}</span>
      </div>
    </div>
    <div class="page-row" style="height: 16mm;border-bottom: 1px solid #000;text-align: left;background-color: #fff;overflow: hidden;position: relative">
      <div class="cell_12" style="width: 100%;height: 100%;">
        <!--        <div>-->
        <!--          <img src="https://files.test.ybm100.com/G1/M00/1E/CD/Cgoz1GGzDXuEMiTaAAAAACPxJQI549.png" alt="">-->
        <!--        </div>-->
      </div>
    </div>
    <div class="page-row flex"
      style="justify-content: space-between;border-bottom: 1px solid #000;text-align: left;background-color: #fff;overflow: hidden;position: relative;"
      :style="{height:config.templateSize === '100mm*115mm'?'56mm':'70mm'}"
    >
      <div style="width:73%;height: 100%">
        <div class="verticalCell_1" style="overflow: hidden;width: 100%;height: 100%;">
          <div style="height:15%;font-weight: bold;font-size: 24px;width: 100%;border-bottom: 1px solid #000;line-height: 9mm"
            :style="{display:config.templateSize === '100mm*115mm'?'none':''}"
          >{{ zTFace.packageName }}</div>
          <div style="overflow: hidden;width: 100%;border-bottom: 1px solid #000;position: relative"
            :style="{height:config.templateSize === '76mm*130mm'?'23mm':'32.5%'}"
          >
            <div class="center" style="width: 20%;height:100%;border-right: 1px solid #000;position: absolute;top: 0;left: 0">
              <div class="filletStr" style="width: 8mm;height: 8mm;text-align: center;font-size: 25px;font-weight: bold;margin: 4.5mm auto">收</div>
            </div>
            <div style="height: 100%;overflow: hidden;line-height: 12px;margin-left: 20%;padding:4px;">
              <div>{{ config.contactor }} {{ config.merchantName }} {{config.merchantErpCode}}<br>
                {{ config.mobile }}<br>
                {{ config.takeAeliveryAddress }}
              </div>
            </div>
          </div>
          <div style="width: 100%;border-bottom: 1px solid #000;position: relative"
            :style="{height:config.templateSize === '76mm*130mm'?'23mm':'32.5%'}"
          >
            <div class="center" style="width: 20%;height:100%;border-right: 1px solid #000;position: absolute;left: 0;top: 0">
              <div class="filletStr" style="width: 8mm;height: 8mm;text-align: center;font-size: 25px;font-weight: bold;margin: 4.5mm auto">寄</div>
            </div>
            <div style="height: 100%;overflow: hidden;line-height: 12px;margin-left: 20%;padding:4px;">
              <div>{{ config.consignor }} {{ config.deliveryMobile }}</div>
              <div>
                {{ config.mailRegionName }} {{ config.mailAddress }}
              </div>
            </div>
          </div>
          <div
            style="height:20%;font-weight: bold;padding-left: 5px;overflow: hidden;width: 100%;"
          >
            <div style="font-size: 14px">{{ config.orderNo }}</div>
            <div>备注：{{ config.remark }}</div>
          </div>
        </div>
      </div>
      <div style="width: 27%;border-left: 1px solid #000;overflow: hidden;position: absolute;right: 0;top: 0"
        :style="{height:config.templateSize === '100mm*115mm'?'60mm':'70mm'}"
      >
        <!--        <div>-->
        <!--          <img src="https://files.test.ybm100.com/G1/M00/1E/CD/Cgoz1GGzDXuEMiTaAAAAACPxJQI549.png" alt="">-->
        <!--        </div>-->
      </div>
    </div>
    <div class="page-row"
      style="padding-left: 5px;border-bottom: 1px solid #000;text-align: left;background-color: #fff;overflow: hidden;position: relative;display: flex;flex-direction: column;justify-content: space-between;"
      :style="{height:config.templateSize === '100mm*150mm'?'20mm':'16mm'}"
    >
      <div style="font-size:12px;">
        本次服务适用中通官网（www.zto.com）公示的快递服务协议条款。您对此订单的签收代表您已经收到快递且包装完好无损
      </div>
      <div style="font-size: 14px;font-weight: bold;">
        签收人/时间：
      </div>
    </div>
    <div class="page-row" style="text-align: left;background-color: #fff;overflow: hidden;position: relative"
      :style="{height:config.templateSize === '100mm*150mm'?'20mm':'6mm'}"
    >
      <div class="cell_12" style="width:100%;height:100%;align-items:center;padding-right: 5px;font-size: 14px;font-weight: bold;text-align: right;line-height: 6mm">
        已验视 已实名
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ZTOexpress',
  props: {
    config: {
      type: Object,
      default: () => {
        return {};
      }
    }
  },
  data() {
    return {
      zTFace: {}
    };
  },
  created() {
    console.log(this.config);
    this.zTFace = { ...this.config.zTFace || {} };
  }
};
</script>

<style scoped>

</style>
