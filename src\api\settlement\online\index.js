import request from '../../index';

/**
 * 获取商家状态
 * @param {参数} params
 */
export function getSellerStatus(params) {
  return request.get('/sellerCommissionSettle/findAccountStatementPageShowContent', params)
}

/**
 * 获取账单列表
 * @param {参数} params
 */
export function getBillDatas(params) {
  return request.get('/sellerOnlinePopBillSettlement/v1/billList', params);
}

/**
 * 获取账单导出条数
 * @param {参数} params
 */
export function getBillDataNums(params) {
  return request.get('/sellerOnlinePopBillSettlement/v1/queryExprotBillListCount', params);
}

/**
 * 获取账单明细导出条数
 * @param {参数} params
 */
export function getBillDetailNums(params) {
  return request.get('/sellerOnlinePopBillSettlement/v1/queryExprotBillDetailCount', params);
}

/**
 * 获取账单价格信息
 * @param {参数} params
 */
export function getBillInfo(params) {
  return request.get('/sellerOnlinePopBillSettlement/v1/queryPopBillPayStatis', params);
}

/**
 * 获取提现信息
 * @param {参数} params
 */
export function getPriceInfo() {
  return request.get('/accountStatement/findAccountSummary');
}

/**
 * 获取账单明细列表
 * @param {参数} params
 */
export function getBillDetailList(params) {
  return request.get('/sellerOnlinePopBillSettlement/v1/billDetail', params);
}

/**
 * 获取账单明细数据
 * @param {参数} params
 */
export function getBillDetailInfo(params) {
  return request.get('/sellerOnlinePopBillSettlement/v1/billPayment', params);
}

/**
 * 获取结算单列表
 * @param {参数} params
 */
export function getOnlineSettleDatas(params) {
  return request.get('/sellerOnlinePopBillSettlement/v1/listSettlement', params);
}

/**
 * 获取结算单价格信息
 * @param {参数} params
 */
export function getOnlineSettleInfo(params) {
  return request.get('/sellerOnlinePopBillSettlement/v1/querySettlementPopBillSettleStatis', params);
}

/**
 * 获取结算单导出条数
 * @param {参数} params
 */
export function getOnlineSettleDataNums(params) {
  return request.get(
    '/sellerOnlinePopBillSettlement/v1/querySettlementExprotBillSettleListCount',
    params,
  );
}

/**
 * 获取提现次数
 */
export function getWithdrawTimes() {
  return request.post('/accountStatement/findCashAdvanceCount');
}

/**
 * 获取账户信息
 */
export function getAccountInfo() {
  return request.post('/accountStatement/findCashAdvanceAccount');
}

/**
 * 发起提现
 * @param {参数} params
 */
export function sendWithdraw(params) {
  return request.get('/accountStatement/saveCashAdvance', params);
}

/**
 * 导出账单
 */
export function exportBillPaymemtList(params) {
  return request.get('/sellerOnlinePopBillSettlement/async/exportBillPaymemtList', params);
}

/**
 * 导出账单明线
 */
export function exportBillPaymemtDetailList(params) {
  return request.get('/sellerOnlinePopBillSettlement/async/exportBillPaymemtDetailList', params);
}

/**
 * 在线支付结算单-导出结算单
 */
export function exportOnlinePaySettleList(params) {
  return request.get('/sellerOnlinePopBillSettlement/async/exportOnlinePaySettleList', params);
}

/**
 * 分润状态枚举
 */
export function getShareStatusOptions() {
  return request.get('/shareStatus');
}

/**
 * 开户提示接口
 * @param {参数} params
 */
export function apiQueryOpenAccountTips(params) {
  return request.get('/bank/queryOpenAccountTips', params);
}

/**
 * 提现验证码校验接口
 * @param {参数} params
 */
export function verifyCashSmsCode(params) {
  return request.get('/accountStatement/verifyCashSmsCode', params);
}

//查询对公账户信息
export function getWithDrawCardInfo() {
  return request.get('/corporation/v2/withDrawCardInfo');
}
//修改对公账户信息
export function updateWithDrawCardInfo(params) {
  return request.post('/corporation/v2/addOrUpdateWithDrawCard', params);
}
