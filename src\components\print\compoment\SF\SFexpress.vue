<template>
  <div class="content"
       style="height: auto;margin: 0mm auto;font-family: 黑体;box-sizing: border-box;font-size: 12px;color: #000;font-weight: bold;border: 1px solid #000;"
       :style="{width: config.templateSize === '76mm*130mm'?'70mm':'94mm'}"
  >
    <div class="page-row one"
         style="height: 10mm;text-align: left;background-color: #fff;overflow: hidden;position: relative;margin-top: 10px;"
    >
      <div style="width:100%;height:100%;overflow: hidden;position: relative">
        <div style="position: absolute;left: 0;top:0;" :style="{width: config.templateSize === '76mm*130mm'?'38%':'40%', height: config.templateSize === '76mm*130mm'?'80%':'100%'}"><img
          style="width: 100%;height: 100%;object-fit: cover;padding: 0;margin: 0"
          src="https://oss-ec.ybm100.com/ybm/popDeliverLog/shunfeng1.png"
          alt=""
        ></div>
        <div style="width: 38%;height: 80%;position: absolute;left: 40%;top:0;" :style="{width: config.templateSize === '76mm*130mm'?'38%':'40%', height: config.templateSize === '76mm*130mm'?'80%':'100%'}">
          <img style="width: 100%;height: 100%;object-fit: cover;padding: 0;margin: 0" src="https://oss-ec.ybm100.com/ybm/popDeliverLog/shunfeng2.png"
                                                  alt=""
        ></div>
      </div>
      <div style="font-size: 20px;font-weight: bold;position: absolute;top: 0;right: 0">{{ sFFace.proCode }}
      </div>
    </div>
    <div class="page-row one"
         style="height: 5mm;text-align: left;background-color: #fff;overflow: hidden;position: relative"
         :style="{fontSize: config.templateSize === '76mm*130mm'?'12px':'14px'}"
    >
      <div class="cell_12" style="width:100%;height: 100%;text-align: center"><span>第{{ config.printCount
        }}次打印</span><span
        style="padding-left: 6px"
      > 打印时间：{{ formatDate(config.printTime) }} </span>
      </div>
    </div>
    <div class="page-row one"
         style="padding-bottom: 3mm;text-align: left;background-color: #fff;overflow: hidden;position: relative"
         :style="{height: config.templateSize === '76mm*130mm'?'18mm':'24mm'}"
    >
      <div style="position: absolute;left: 0;bottom: 0;font-size: 20px;font-weight: bold">{{ config.expressIndex
        }}/{{ config.expressTotal }}
      </div>
      <div style="position: absolute;bottom: 1mm;width: 100%;font-size: 14px" :style="{left: config.templateSize === '76mm*130mm' ? '16mm' : '26mm'}">
        <div v-if="config.expressTotal>1">
          <div>子单号{{ config.currentSubBiLLNo || '' }}</div>
          <div>母单号{{ config.waybillNo }}</div>
        </div>
        <div v-else style="width: 100%;">
          <span>运单号{{ config.waybillNo }}</span>
        </div>
      </div>
    </div>
    <div v-if="config.templateSize != '100mm*150mm'">
      <div style="font-weight: bold;text-align:center;height: 8mm;line-height: 8mm;font-size: 24px;padding-left: 10px;border-top: 1px solid #000;">{{ sFFace.destRouteLabel }}</div>
      <div style="display:flex;align-items:center;justify-content: space-between;border-top: 1px solid #000;">
        <div :style="{width: config.templateSize === '76mm*130mm'?'84%':'80%'}">
          <div class="page-row"
            style="height: 18mm;text-align: left;background-color: #fff;overflow: hidden;position: relative;"
          >
            <div style="font-weight: bold;" :style="{marginLeft: config.templateSize === '76mm*130mm'?'38px':'50px'}">
              {{ config.takeAeliveryAddress }}<br>
              {{ config.merchantName }} {{ config.merchantErpCode }}<br>
              {{ config.contactor }} {{ config.mobile }}<br>
            </div>
            <div style="position: absolute;left: 1mm;top: 2mm">
              <span class="filletStr"
                style="font-weight: bold;border: 1px solid #000;border-radius: 40px;padding: 2px"
                :style="{fontSize: config.templateSize === '76mm*130mm'?'20px':'30px'}"
            >收</span></div>
          </div>
          <div class="page-row"
              style="height: 16mm;border-top: 1px solid #000;text-align: left;background-color: #fff;overflow: hidden;position: relative;"
          >
            <div style="font-weight: bold;position: absolute;top: 1mm;" :style="{left: config.templateSize === '76mm*130mm'?'10mm':'14mm'}">
              {{ config.consignor }}
              {{ config.deliveryMobile }}
              {{ config.mailRegionName }}
              {{ config.mailAddress }}
            </div>
            <div style="position: absolute;left: 1mm;top:2mm">
              <span class="filletStr"
                style="font-weight: bold;border: 1px solid #000;border-radius: 40px;padding: 2px"
                :style="{fontSize:config.templateSize === '76mm*130mm'?'20px':'30px'}"
            >寄</span></div>
          </div>
          <div class="page-row"
              style="height: 25mm;border-top: 1px solid #000;text-align: left;background-color: #fff;overflow: hidden;position: relative;"
              :style="{height: config.templateSize === '76mm*130mm'?'35mm':'25mm'}"
          >
            <div class="verticalCell_1" style="height:100%;overflow: hidden;position: absolute;top: 0;left: 0;border-right:1px solid #000;"
              :style="{width: config.templateSize === '76mm*130mm'?'77%':'68%'}"
            >
              <div style="display:flex;justify-content:space-between; width: 100%;height: 8.3mm;line-height:8.3mm;border-bottom: 1px solid #000;">
                <span>付款方式：{{ config.payType || '寄付月结' }}</span>
                <span>已验视</span>
              </div>
              <div style="width: 100%;height: 8.3mm;line-height:8.3mm;border-bottom: 1px solid #000;">
                月结卡号：{{ config.settlementCardNo }}
              </div>
              <div style="width: 100%;height: 9.3mm;">
                <div>单号：{{ config.orderNo }}</div>
                <div>备注：{{ config.remark }}</div>
              </div>
            </div>
          </div>
        </div>
        <div style="border-left: 1px solid #000;overflow: hidden;" :style="{width:config.templateSize === '76mm*130mm'?'16%':'20%',height:config.templateSize === '76mm*130mm'?'70mm':'60mm'}">
      </div>
      </div>
    </div>
    <div v-else>
      <div class="page-row"
         style="border-top: 1px solid #000;text-align: left;background-color: #fff;overflow: hidden;position: relative"
      >
        <div style="font-weight: bold;font-size: 24px;padding-left: 10px">{{ sFFace.destRouteLabel }}</div>
        <div style="font-weight: bold;margin-left: 50px">
          {{ config.takeAeliveryAddress }}<br>
          {{ config.merchantName }} {{ config.merchantErpCode }}<br>
          {{ config.contactor }} {{ config.mobile }}<br>
        </div>
        <div style="position: absolute;left: 1mm;top: 8mm"><span class="filletStr"
                                                                style="font-size: 30px;font-weight: bold;border: 1px solid #000;border-radius: 40px;padding: 2px"
        >收</span></div>
      </div>
      <div class="page-row"
          style="height: 25mm;border-top: 1px solid #000;text-align: left;background-color: #fff;overflow: hidden;position: relative;"
      >
        <div class="verticalCell_1" style="width: 35%;height:100%;overflow: hidden;position: absolute;top: 0;left: 0">
          <div style="width: 100%;height: 8.3mm;line-height:8.3mm;border-bottom: 1px solid #000;padding-left: 5px">
            付款方式：{{ config.payType || '寄付月结' }}
          </div>
          <div style="width: 100%;height: 8.3mm;border-bottom: 1px solid #000;padding-left: 5px"></div>
          <div style="width: 100%;height: 8.3mm;padding-left: 5px"></div>
        </div>
        <div
          style="width: 30%;height: 100%;border-right: 1px solid #000;border-left: 1px solid #000;position: absolute;left: 35%;top: 0"
        >
        </div>
        <div
          style="width: 10%;height:100%;border-right: 1px solid #000;position: absolute;left: 65%;top: 0"
        >
          <div
            style="width:100%;white-space: break-spaces;font-weight: bold;font-size: 20px;padding-top: 3mm;padding-left: 2mm"
          >已验视
          </div>
        </div>
        <div class="verticalCell_1" style="width: 25%;height:100%;overflow: hidden;position: absolute;top: 0;right: 0">
          <div style="width: 100%;height: 12.5mm;border-bottom: 1px solid #000;padding-left: 5px"></div>
          <div style="width: 100%;height: 12.5mm;padding-left: 5px"></div>
        </div>
      </div>
      <div class="page-row"
          style="height: 18mm;border-top: 1px solid #000;text-align: left;background-color: #fff;overflow: hidden;position: relative;"
      >
        <div style="font-weight: bold;position: absolute;top: 1mm;left:14mm;">
          {{ config.consignor }}
          {{ config.deliveryMobile }}
          {{ config.mailRegionName }}
          {{ config.mailAddress }}
        </div>
        <div style="position: absolute;left: 1mm;top: 5mm"><span class="filletStr"
                                                                style="font-size: 30px;font-weight: bold;border: 1px solid #000;border-radius: 40px;padding: 2px"
        >寄</span></div>
      </div>
      <div class="page-row"
          style="height: 16mm;border-top: 1px solid #000;text-align: left;background-color: #fff;overflow: hidden;position: relative;"
      >
        <div style="padding-top: 3px">增值服务：</div>
        <div>月结卡号：{{ config.settlementCardNo }}</div>
        <div>单号：{{ config.orderNo }}</div>
      </div>
      <div class="page-row"
          style="height: 14mm;padding: 5px;border-top: 1px solid #000;text-align: left;background-color: #fff;overflow: hidden;position: relative;"
      >
        <div>产品类型：{{ config.proName }}</div>
        <div>备注：{{ config.remark }}</div>
      </div>
    </div>
    
  </div>
</template>

<script>
export default {
  name: 'SFexpress',
  props: {
    config: {
      type: Object,
      default: () => {
        return {};
      }
    }
  },
  data() {
    return {
      sFFace: {}
    };
  },
  created() {
    console.log(this.config);
    this.sFFace = { ...this.config.sFFace || {} };
  }
};
</script>

<style scoped>
/* @page {
  size: 100mm 150mm;
  margin: 0;
  padding: 0;
} */

</style>
