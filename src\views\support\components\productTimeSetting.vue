<template>
  <div class="productTimeSetting">
    <div class="contentBox">
      <div class="title">商品售卖时间列表</div>
      <SearchForm
        ref="searchForm"
        :model="formModel"
        :form-items="formItems"
        @submit="handleFormSubmit"
        @reset="handleFormReset"
      >
        <template slot="form-item-timeStatus">
          <el-select v-model="formModel.timeStatus" placeholder="请选择">
            <el-option
              v-for="(item, index) in taskStatusOptions"
              :key="index"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </template>
        <template slot="form-item-skuStatus">
          <el-select v-model="formModel.skuStatus" placeholder="请选择">
            <el-option
              v-for="(item, index) in statusOptions"
              :key="index"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </template>
        <template slot="form-item-configId">
          <el-select v-model="formModel.configId" placeholder="请选择">
            <el-option
              v-for="(item, index) in planNameOptions"
              :key="index"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </template>
      </SearchForm>
      <div class="operation">
        <div class="left_content">
          <el-button type="primary" size="small" @click="operationFn(1)">批量设置商品售卖时间</el-button>
          <el-button type="primary" size="small" @click="operationFn(2)">售卖时间计划设置</el-button>
        </div>
        <div class="right_content">
          <el-button type="primary" size="small" @click="operationFn(3)">导出商品售卖时间列表</el-button>
        </div>
      </div>
      <xyyTable
        ref="productListTable"
        v-loading="tableLoading"
        :data="tableConfig.data"
        :col="tableConfig.col"
        :hasIndex="true"
        :list-query="listQuery"
        @get-data="queryList"
      >
        <template slot="productInfo">
          <el-table-column label="商品信息" width="250px">
            <template slot-scope="{row}">
              <div class="productInfo">
                <div>商品名称：{{ row.productName }}<span>
                    <el-tag
                      size="mini"
                      style="margin:0 2px"
                      v-if="row.activeName"
                    >{{ row.activeName }}</el-tag>
                  </span>
                </div>
                <div>厂家：{{ row.manufacturer }}</div>
                <div>规格：{{ row.spec }}</div>
                <div>批准文号：{{ row.approvalNumber }}</div>
                <div>商品编码：{{ row.barcode }}</div>
                <div>商品ERP编码：{{ row.erpCode }}</div>
              </div>
            </template>
          </el-table-column>
        </template>
        <template slot="showName">
          <el-table-column label="展示名称" width="150px">
            <template slot-scope="{row}">
              <img :src="row.imageUrl" style="width: 80px;height: 73px;margin-bottom: 4px"/>
              <div>{{ row.showName }}</div>
            </template>
          </el-table-column>
        </template>
        <template slot="price">
          <el-table-column label="价格" width="150px">
            <template slot-scope="{row}">
              <div>单体采购价：¥{{ row.fob }}</div>
              <div>连锁采购价：¥{{ row.chainPrice }}</div>
            </template>
          </el-table-column>
        </template>
        <template slot="busAreaName">
          <el-table-column label="供货信息" width="150px">
            <template slot-scope="{row}">
              <el-popover
                placement="top-start"
                width="200"
                trigger="hover"
                :content="`商圈名称：${ row.busAreaName }`"
              >
                <div slot="reference" class="busAreaNameBox">商圈名称：{{ row.busAreaName }}</div>
              </el-popover>
            </template>
          </el-table-column>
        </template>
        <template slot="time">
          <el-table-column label="售卖时间" width="250px">
            <template slot-scope="{row}">
              <div>计划：{{ row.configName || '自定义' }}</div>
              <div v-for="item in row.time" :key="item.id">
                <div style="display: inline-block;vertical-align: baseline">{{ formatWeek(item.week) }}：</div>
                <div style="display: inline-block;vertical-align: top">
                  <div v-for="(li,index) in item.time" :key="index">
                    {{ li.startHour }}:{{ li.startMinute }} 至 {{ li.endHour }}:{{
                      li.endMinute
                    }}{{ (index === item.time.length - 1) ? '' : '，' }}
                  </div>
                </div>
              </div>
            </template>
          </el-table-column>
        </template>
        <template slot="operation">
          <el-table-column label="操作" fixed="right" width="150">
            <template slot-scope="{row}">
              <el-button type="text" @click="operationClick(1,row)">编辑</el-button>
              <el-button v-if="Number(row.timeStatus)===2" type="text" @click="operationClick(2,row)">启用</el-button>
              <el-button v-if="Number(row.timeStatus)===1" type="text" @click="operationClick(3,row)">停用</el-button>
              <el-button type="text" @click="operationClick(4,row)">删除</el-button>
            </template>
          </el-table-column>
        </template>
      </xyyTable>
    </div>
    <BatchSettingCommoditySalesTime
      v-if="batchSettingCommoditySalesTimeDialogVisible"
      :batchSettingCommoditySalesTimeDialogVisible.sync="batchSettingCommoditySalesTimeDialogVisible"
      :config="batchSettingConfig"
      @queryList="queryList"
    />
    <SaleTimePlanSetting
      v-if="saleTimePlanSettingDialogVisible"
      :saleTimePlanSettingDialogVisible.sync="saleTimePlanSettingDialogVisible"
      @refresh="refreshCallback"
    />
    <export-tip
      :change-export="changeExport"
      @handleExoprClose="handleExoprClose"
      @handleChangeExport="handleChangeExport"
    />
  </div>
</template>

<script>
import SearchForm from '@/components/searchForm';
import xyyTable from '@/components/table';
import BatchSettingCommoditySalesTime from './batchSettingCommoditySalesTime'
import SaleTimePlanSetting from './saleTimePlanSetting'
import exportTip from '@/views/other/components/exportTip';

import {
  SelectSaleTimeList,
  saleTimeSkuList,
  changeSkuStatus,
  deleteSkuTime,
  exportSkuSaleTime
} from '@/api/product/index.js'


export default {
  name: "productTimeSetting",
  components: {
    SearchForm,
    xyyTable,
    BatchSettingCommoditySalesTime,
    SaleTimePlanSetting,
    exportTip
  },
  data() {
    return {
      formModel: {
        barcode: '', // 商品编码
        erpCode: '', // erp编码
        showName: '', // 商品名称
        manufacturer: '', // 生产厂家
        code: '', // 商品条码
        approvalNumber: '', // 批准文号
        timeStatus: '',//任务状态
        skuStatus: '', //商品状态
        configId: ''
      },
      formItems: [
        {
          label: '商品编码',
          prop: 'barcode',
          component: 'el-input',
          attrs: {
            placeholder: '请输入'
          }
        }, {
          label: '商户ERP编码',
          prop: 'erpCode',
          component: 'el-input',
          attrs: {
            placeholder: '请输入'
          }
        }, {
          label: '商品名称',
          prop: 'showName',
          component: 'el-input',
          attrs: {
            placeholder: '请输入'
          }
        }, {
          label: '生产厂家',
          prop: 'manufacturer',
          component: 'el-input',
          attrs: {
            placeholder: '请输入'
          }
        }, {
          label: '商品条码',
          prop: 'code',
          component: 'el-input',
          attrs: {
            placeholder: '请输入'
          }
        }, {
          label: '批准文号',
          prop: 'approvalNumber',
          component: 'el-input',
          attrs: {
            placeholder: '请输入'
          }
        }, {
          label: '任务状态',
          prop: 'timeStatus',
          component: 'el-select',
          slotName: 'timeStatus'
        }, {
          label: '商品状态',
          prop: 'skuStatus',
          component: 'el-select',
          slotName: 'skuStatus'
        }, {
          label: '计划名称',
          prop: 'configId',
          component: 'el-select',
          slotName: 'configId'
        },
      ],
      taskStatusOptions: [
        {
          label: '全部',
          value: ''
        },
        {
          label: '启用',
          value: 1
        },
        {
          label: '停用',
          value: 2
        }
      ],
      statusOptions: [
        {
          label: '全部',
          value: '',
        },
        {
          label: '销售中',
          value: 1,
        }, {
          label: '下架',
          value: 4,
        }, {
          label: '待上架',
          value: 6,
        }, {
          label: '待审核',
          value: 8,
        }, {
          label: '审核未通过',
          value: 9,
        },
        // {
        //   label: '删除',
        //   value: 20,
        // },
      ],
      planNameOptions: [],
      tableLoading: false,
      tableConfig: {
        data: [],
        col: [
          {
            name: '商品信息',
            index: 'productInfo',
            slot: 'productInfo',
          },
          {
            name: '展示名称',
            index: 'showName',
            slot: 'showName',
          },
          {
            name: '价格',
            index: 'price',
            slot: 'price',
          },
          {
            name: '供货信息',
            index: 'busAreaName',
            slot: 'busAreaName',
          },
          {
            name: '售卖时间',
            index: 'time',
            slot: 'time',
          },
          {
            name: '商品状态',
            index: 'skuStatus',
            formatter: (row, col, cell) => {
              let str = ''
              this.statusOptions.forEach(item => {
                if (item.value === Number(cell)) {
                  str = item.label
                }
              })
              return str
            },
          },
          {
            name: '任务状态',
            index: 'timeStatus',
            formatter: (row, col, cell) => {
              let str = ''
              this.taskStatusOptions.forEach(item => {
                if (item.value === Number(cell)) {
                  str = item.label
                }
              })
              return str
            },
          },
          {
            name: '最后修改时间',
            index: 'updateTime',
            width: '180',
            formatter: (row, col, cell) => {
              return this.formatDate(cell)
            },
          },
          {
            index: 'operation',
            name: '操作',
            slot: 'operation',
          },
        ]
      },
      listQuery: {
        pageSize: 10,
        page: 1,
        total: 0,
      },
      batchSettingCommoditySalesTimeDialogVisible: false,
      batchSettingConfig: {},
      saleTimePlanSettingDialogVisible: false,
      changeExport: false
    }
  },
  created() {
    this.getSelectSaleTimeList()
    this.queryList()
  },
  activated() {
    const barcode = this.$route.query.barcode
    if (barcode) {
      this.$set(this.formModel, 'barcode', barcode)
      this.queryList()
    }
  },
  methods: {
    formatWeek(s) {
      const num = Number(s)
      let str = ''
      switch (num) {
        case 1:
          str = '周一'
          break
        case 2:
          str = '周二'
          break
        case 3:
          str = '周三'
          break
        case 4:
          str = '周四'
          break
        case 5:
          str = '周五'
          break
        case 6:
          str = '周六'
          break
        case 7:
          str = '周日'
          break
      }
      return str
    },
    async getSelectSaleTimeList() {
      const res = await SelectSaleTimeList()
      if (res && res.code === 0) {
        this.planNameOptions = [{name: '全部', id: ''},{name: '自定义', id: -1}, ...res.data]
      }
    },
    getParams() {
      return {...this.formModel}
    },
    async queryList(listQuery) {
      this.tableLoading = true
      if (listQuery) {
        const {pageSize, page} = listQuery;
        this.listQuery.pageSize = pageSize;
        this.listQuery.page = page;
      }
      let params = this.getParams();
      const {pageSize, page} = this.listQuery;
      params.pageNum = page;
      params.pageSize = pageSize;
      console.log(JSON.stringify(params));
      try {
        const res = await saleTimeSkuList(params)
        console.log(res)
        if (res && res.code === 0) {
          this.tableConfig.data = res.data.list;
          this.listQuery.total = res.data.total;
        }
      } catch (err) {
        console.log(err)
      }
      this.tableLoading = false
    },
    handleFormSubmit() {
      this.queryList()
    },
    handleFormReset(obj) {
      this.formModel = obj;
      this.listQuery = {
        page: 1,
        pageSize: 10,
        total: 0,
      };
      this.queryList();
    },
    operationFn(type) {
      switch (type) {
        case 1:
          console.log('批量设置商品售卖时间')
          this.batchSettingConfig = {}
          this.batchSettingCommoditySalesTimeDialogVisible = true
          break
        case 2:
          console.log('售卖时间计划设置')
          this.saleTimePlanSettingDialogVisible = true
          break
        case 3:
          console.log('导出商品售卖时间列表')
          const params = this.getParams()
          exportSkuSaleTime(params).then((res) => {
            console.log(res)
            if (res && res.code === 0) {
              this.changeExport = true;
            } else {
              this.$message.error(res.message || '导出失败');
            }
          })
          break
      }
    },
    operationClick(type, row) {
      if (type === 1) {
        console.log('编辑')
        this.batchSettingConfig.type = 'edit'
        this.batchSettingConfig.id = row.id
        this.batchSettingCommoditySalesTimeDialogVisible = true
      } else {
        const str = `<p>商品名称：${row.showName}</p><p>确认${type === 2 ? '启用' : (type === 3 ? '停用' : '删除')}当前商品的售卖时间任务吗？</p>`
        this.$confirm(str, `${type === 2 ? '启用提示' : (type === 3 ? '停用提示' : '删除售卖时间任务')}`, {
          dangerouslyUseHTMLString: true,
          distinguishCancelAndClose: true,
          confirmButtonText: '确认',
          cancelButtonText: '关闭',
          type: 'warning'
        }).then(async () => {
          let res = null
          if (type === 4) {
            res = await deleteSkuTime(row.id)
          } else {
            res = await changeSkuStatus({id: row.id, status: type === 2 ? 1 : 2})
          }
          if (res && res.code === 0) {
            this.$message.success('操作成功！');
            this.queryList()
          } else {
            this.$message.error(res.message || '操作失败！')
          }
        }).catch((e) => {
          console.log(e)
        })
      }
    },
    handleExoprClose() {
      this.changeExport = false;
    },
    handleChangeExport() {
      this.changeExport = false;
      const path = '/downloadList'
      window.openTab(path)
    },
    refreshCallback(){
      this.getSelectSaleTimeList()
      this.queryList()
    }
  }
}
</script>

<style scoped lang="scss">
.contentBox {
  //height: 100%;
  padding: 16px 16px;
  background: #fff;
  margin-bottom: 10px;

  .title {
    font-weight: 500;
    text-align: left;
    color: #000000;
    line-height: 14px;
    margin-bottom: 24px;
  }

  .title:before {
    content: '';
    display: inline-block;
    width: 3px;
    height: 13px;
    background: linear-gradient(360deg, #1d69c4 1%, #8bbdfc 99%);
    border-radius: 2px;
    margin-right: 8px;
    vertical-align: middle;
  }

  .operation {
    margin: 15px 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  ::v-deep   #tabled tr td .cell {
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    white-space: break-spaces;
    font-size: 12px;
    font-family: PingFangSC, PingFangSC-Regular;
    font-weight: 400;
    text-align: left;
    color: #666666;
    line-height: 22px;
    padding-left: 16px;

    .busAreaNameBox {
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .productInfo {
      text-align: left;
      font-size: 12px;
      line-height: 18px;

      .productName {
        font-size: 14px;
        font-family: PingFangSC, PingFangSC-Medium;
        font-weight: 500;
        color: rgba(51, 51, 51, 0.85);
        line-height: 22px;
      }

      .manufacturer {
        color: rgba(51, 51, 51, 0.85);
      }
    }

    .el-button {
      height: 22px;
      padding: 0;
      margin: 0;
      text-align: left;
      font-size: 12px;
      line-height: 22px;
    }

    .el-button:before {
      display: none;
    }
  }
}
</style>
