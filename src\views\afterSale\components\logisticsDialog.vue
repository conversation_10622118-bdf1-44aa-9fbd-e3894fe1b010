<template>
  <div>
    <!-- 填写物流信息的弹框 -->
    <el-dialog
      title="填写物流信息"
      :visible.sync="dialogVisible"
      @close="dialogVisible = false"
      @closed="resetForm"
      width="40%"
      append-to-body
      :close-on-click-modal="false"
    >
      <div class="box">
        <div style="margin-top: 10px">
          <el-form label-width="110px" :rules="rules" ref="subForm" :model="subForm">
            <el-form-item label="物流公司" prop="expressName" key="expressName">
              <el-input
                placeholder="请输入"
                v-model="subForm.expressName"
                style="width: 300px"
                maxlength="20"
                show-word-limit
              ></el-input>
            </el-form-item>
            <el-form-item label="快递单号" prop="expressNo" key="expressNo">
              <el-input
                placeholder="请输入"
                v-model="subForm.expressNo"
                style="width: 300px"
                maxlength="20"
                show-word-limit
              ></el-input>
            </el-form-item>
            <el-form-item label="运单截图" key="screenShot">
              <el-upload
                v-model="subForm.screenShot"
                action
                list-type="picture-card"
                :before-upload="beforeAvatarUpload"
                :http-request="uploadImg"
                :on-remove="handleRemove"
                :file-list="fileList"
                :on-exceed="() => this.$message.error('超出文件上传数量限制')"
                multiple
                :limit="1"
              >
                <i class="el-icon-plus"></i>
              </el-upload>
            </el-form-item>
            <el-form-item style="margin-top: -20px">
              <span style="color: #999fa8">最多上传1张，且大小不超过5MB</span>
            </el-form-item>
          </el-form>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <!-- <el-button @click="$emit('update:dialogVisible')">取 消</el-button> -->
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitForm()">确定 </el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { getHostName } from '@/api/storeManagement/index'
import { addOrderRefundExpress, uploadFile } from '@/api/afterSale/index';

export default {
  props: {
    showBtn: {
      type: Boolean,
      default: false
    },
    formData: '',
    dialogVisible1: {
      type: Boolean,
      default: false
    }
  },
  // computed: {
  //   dialogVisible: {
  //     // getter
  //     get() {
  //       return this.dialogVisible1
  //     },
  //     // setter
  //     set(newValue) {
  //       // 注意：我们这里使用的是解构赋值语法
  //       // this.$emit('update:dialogVisible')
  //     }
  //   }
  // },
  data() {
    return {
      dialogVisible: false,
      subForm: {
        orderRefundId: null, // 退款单明细id
        expressName: null, // 物流公司
        expressNo: null, // 快递单号
        payProof: [], // 运单截图
        expressEvidence: '' // 快递凭证
      },
      rules: {
        expressName: [{ required: true, message: '请填写物流公司', trigger: 'blur' }],
        expressNo: [{ required: true, message: '请填写快递单号', trigger: 'blur' }]
      },

      hostName: '',
      fileList: []
    }
  },
  created() {
    getHostName().then((res) => {
      if (res.hostName) {
        this.hostName = res.hostName
      }
    })
    if (this.formData) {
      // console.log(this.formData)
      // this.fileList = this.formData.fileList
      // this.subForm.rechargeAmount = this.formData.rechargeAmount
      // this.subForm.payProof = this.formData.fileList
    }
  },
  methods: {
    openDialog(val) {
      // 拿到表格行数据赋值给相关字段，用于后面提交表单的相关参数
      // console.log(val)
      this.subForm.orderRefundId = val.id
      this.dialogVisible = true
    },
    submitForm() {
      this.$refs.subForm.validate((valid) => {
        if (valid) {
          if(this.subForm.payProof?.length > 0) this.subForm.expressEvidence = this.subForm.payProof[0].name
          // console.log(this.subForm)
          let subData = {
            orderRefundId: this.subForm.orderRefundId,
            expressName: this.subForm.expressName,
            expressNo:  this.subForm.expressNo,
            expressEvidence:  this.subForm.expressEvidence
          }

          addOrderRefundExpress(subData).then((res) => {
            if (res.code == 0) {
              this.dialogVisible = false
              this.$parent.$emit('refreshList')
            } else {
              this.$message({ type: 'error', message: res.message })
            }
          })

          // this.$emit('update:dialogVisible')
          // setTimeout(() => {
          //   this.dialogVisible = false
          // }, 3000)
        } else {
          // console.log('error submit!!')
          this.$message.error('请检查表单内容！')
        }
      })
    },
    resetForm() {
      this.$refs.subForm.resetFields()
      this.fileList = []
    },

    // 上传图片的逻辑
    uploadImg(file) {
      uploadFile(file).then((res) => {
        if (res.code === '200') {
          // this.form.viewImage = `${this.hostName}/${res.data}`;
          let temp = { name: res.data, url: `${this.hostName}${res.data}` }
          // this.fileList = [...this.fileList, temp];
          this.fileList.push(temp)
          this.subForm.payProof = this.fileList
          // console.log(res.data);
          // console.log(this.fileList)
        } else {
          // this.form.viewImage = '';
          console.log('上传失败')
        }
      })
      return true
    },
    beforeAvatarUpload(file) {
      // console.log(file)

      if (!file) {
        this.$message.error('请上传图片')
        return false
      }
      const isJPG =
        file.type === 'image/jpeg' ||
        file.type === 'image/png' ||
        file.type === 'image/bmp' ||
        file.type === 'image/jpg' ||
        file.type === 'image/gif'
      const isLt5M = file.size / 1024 / 1024 < 5

      if (!isJPG || !isLt5M) {
        this.$message.error('图片不满足上传要求，请重新上传')
        return false
      }
      return isJPG && isLt5M
    },
    handleRemove(file, fileList) {
      this.fileList = fileList
      this.subForm.payProof = this.fileList
      // console.log(file, fileList, this.fileList)
    }
  }
}
</script>

<style scoped lang="scss">
.box {
  margin-top: -20px;
  width: 100%;
  display: flex;
  flex-direction: column;
  // justify-content: center;
  // align-items: center;
  padding: 0 10px;
}
.plainTextSty {
  width: 100%;
  // height: 100px;
  padding: 0 10px;
  background-color: #fafafa;
}
.plainTextSty span {
  width: 30px;
}
.textSty {
  color: #575757;
}
/* 推荐，实现简单 */
::v-deep   .el-upload-list__item.is-ready,
::v-deep   .el-upload-list__item.is-uploading {
  display: none !important;
}
</style>
