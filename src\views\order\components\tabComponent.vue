<template>
  <div class="tabComp">
    <orderBtns
      :active-order-status="activeOrderStatus"
      :logistics-company-list="logisticsCompanyList"
      :show-confirm-finish-button="showConfirmFinishButton"
      :select-rows="selectRows"
      :rule-form="ruleForm"
      @setLocal="setLocalBtn"
      v-on="$listeners"
      :tabData='tabData'
    />
    <div class="listTable">
      <el-table
        ref="cargoTable"
        v-loading="loading"
        :data="tableData"
        :row-key="(row) => row.orderNo"
        default-expand-all
        style="width: 100%"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="expand" width="1">
          <template slot-scope="props">
            <div>
				<div style="color: #4184d5;" v-if="props.row.isNeedCredential">
					资质需求:
					<span style="cursor:pointer;" @click="look(props.row.orderNo)">点击查看随货资质需求</span>
				</div>
              <div
                v-if="props.row.remark || props.row.sellerRemark"
                :class="{remarkShowBox: props.row.showChildren}"
                class="remarkBox"
              >
                <div v-if="props.row.remark">
                  客户备注：
                  <el-tooltip :content="props.row.remark" placement="top-start">
                    <span>{{ (props.row.remark || '').substr(0,50) }}{{ (props.row.remark || '').length > 50 ? '...' : '' }}</span>
                  </el-tooltip>
                </div>
                <div v-if="props.row.sellerRemark" style="marginTop: 10px">
                  商家备注：
                  <el-tooltip :content="props.row.sellerRemark" placement="top-start">
                    <span>{{ (props.row.sellerRemark || '').substr(0,50) }}{{ (props.row.sellerRemark || '').length > 50 ? '...' : '' }}</span>
                  </el-tooltip>
                  <span
                    style="color: #4184d5; marginLeft: 5px; cursor: pointer;"
                    @click="popDialogSend(props.row.orderNo, props.row.sellerRemark)"
                  >点击发送给客户和BD（客服消息形式）</span>
                </div>
              </div>
              <div v-if="props.row.showChildren" style="padding: 20px">
                <order-detail
                  :detail-list="props.row.detailList"
                  :active-order-status="activeOrderStatus"
                  :order-no="props.row.orderNo"
                  @setLocal="setLocalBtn"
                />
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column type="selection" width="55" :reserve-selection="true" />
        <el-table-column label="订单ID" prop="popOrderId" v-if="shopConfig.shopPatternCode !== 'ybm'" />
        <el-table-column label="订单编号" prop="orderNo">
          <template slot-scope="scope">
            <div>{{ scope.row.orderNo }}</div>
            <div>
              <el-tag
                size="medium"
                style="margin:0 5px"
                v-if="scope.row.orderType && scope.row.orderType === 7"
              >拼团</el-tag>
              <el-tag
                size="medium"
                style="margin:0 5px"
                v-if="scope.row.orderType && scope.row.orderType === 10"
              >批购包邮</el-tag>
              <el-tag
                size="medium"
                v-if="(scope.row.firstOrderFlag && scope.row.firstOrderFlag === 1)&&(scope.row.status === 7 || scope.row.status === 1 || scope.row.status === 2 || scope.row.status === 3|| scope.row.status === 32|| scope.row.status === 33)"
              >首单
              </el-tag>
            </div>
            <div
              v-if="scope.row.whetherRefund"
			  style="color:red;"
              class="btnText"
              @click="btnClick(scope.row, 'handleRefund')"
            >
              有退款待处理
            </div>
			<div
              v-if="scope.row.isHaveAfterSales"
			  style="color:red;"
              class="btnText"
              @click="btnClick(scope.row, 'handleAfterSale')"
            >
              有售后待处理
            </div>
            <div v-if="scope.row.partialShipment === 1">
              <span
                style="color: red; cursor: pointer;"
                @click="handleEditPartialShipment(scope.row)"
              >部分发货</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="下单/最新处理时间" prop="orderCreateTime" width="140">
          <template slot-scope="scope">
            <!-- <div>
              {{ formatDate(scope.row.orderCreateTime, 'YMD') }}
              <br />
              {{ formatDate(scope.row.orderCreateTime, 'BR') }}
            </div>-->
            <div>{{ scope.row.orderCreateTime | formatDate }}</div>
            <!-- 待付款 -->
            <div
              v-if="scope.row.status === 10 || scope.row.status === 30 || scope.row.status === 31"
            >{{ scope.row.orderCreateTime | formatDate }}</div>
            <!-- 待审核 -->
            <div v-if="scope.row.status === 1">{{ scope.row.payTime | formatDate }}</div>
            <!-- 开单时间 -->
            <div v-if="scope.row.status === 7">{{ scope.row.auditTime | formatDate }}</div>
            <!-- 配送中 -->
            <div v-if="scope.row.status === 2">{{ scope.row.deliveryTime | formatDate }}</div>
            <!-- 已完成 -->
            <div v-if="scope.row.status === 3">{{ scope.row.orderCompletionTime | formatDate }}</div>
            <!-- 已取消 -->
            <div v-if="scope.row.status === 4">{{ scope.row.cancellationTime | formatDate }}</div>
            <!-- 已退款 -->
            <div v-if="scope.row.status === 91">{{ scope.row.refundCompletionTime | formatDate }}</div>
            <!-- 分拣中 -->
            <div v-if="scope.row.status === 32">{{ scope.row.sortingTime | formatDate }}</div>
            <!-- 待配送 -->
            <div v-if="scope.row.status === 33">{{ scope.row.prepareGoodsTime | formatDate }}</div>
            <div
              v-if="scope.row.status==2||scope.row.status==3"
            >揽收时间：{{ formatDate(scope.row.trackingCollectTime,'YMD HMS') }}</div>
            <div v-if="scope.row.status===2||scope.row.status===3||scope.row.status==91">
            签收时间：{{ scope.row.arrivalTime  }}
          </div>
          </template>    
        </el-table-column>
        <el-table-column label="客户名称" prop="merchantInfo" width='230'>
          <template slot-scope="scope">
            <div>
              <span
                v-if="scope.row.merchantInfo&&scope.row.merchantInfo.businessTypeName"
                style="font-weight: bold"
              >【{{ scope.row.merchantInfo.businessTypeName }}】</span>
              {{ (scope.row.merchantInfo || {}).merchantName }}
              <i
                v-if='scope.row.merchantInfo && scope.row.merchantInfo.merchantId && shopConfig.shopPatternCode !== "ybm"'
                class="el-icon-service"
                style="color: #4184d5;cursor: pointer"
                @click='openService(scope.row.merchantInfo.merchantName)'
              />
            </div>
            <div
              v-if="(activeOrderStatus ==='1' || activeOrderStatus ==='7' || activeOrderStatus==='qualificationRemindCount') && scope.row.exceptionFlag"
              class="informationChanges"
              @click="btnClick(scope.row, 'certificationError')"
            >
              关联资质异常
            </div>
            <div
              v-if="(activeOrderStatus==='10' || activeOrderStatus==='2' || activeOrderStatus==='32' || activeOrderStatus==='33' || activeOrderStatus==='1' || activeOrderStatus==='7' || activeOrderStatus==='qualificationRemindCount')&&(scope.row.merchantInfo || {}).qualificationChanged && (scope.row.merchantInfo || {}).qualificationChanged === 1"
              class="informationChanges"
              @click="btnClick(scope.row, 'informationChanges')"
            >
              信息有变更
            </div>
            <div>
              <div
                style="display: inline-block"
                v-if="scope.row.status === 3 || scope.row.status === 2 || scope.row.status === 7 || scope.row.status === 10 || scope.row.status === 1 || scope.row.status === 32 || scope.row.status === 33"
              >
                <div
                  :class="(scope.row.merchantInfo || {}).merchantStatus === 1 ? 'hasOpened' : 'noOpened'"
                >{{ {1: '已开户', 0: '未开户'}[(scope.row.merchantInfo || {}).merchantStatus] }}</div>
                <i
                  v-if="((scope.row.merchantInfo || {}).merchantStatus === 1) && shopConfig.shopPatternCode !== 'ybm'"
                  class="el-icon-search"
                  style="color: #4183d5;marginLeft:5px;"
                  @click="btnClick(scope.row, 'operationLog')"
                />
                <span style='margin-left: 5px' v-if='(scope.row.merchantInfo || {}).merchantStatus !== 1 && (scope.row.status === 7||scope.row.status === 1)'
                      :class="(scope.row.merchantInfo || {}).isSyncCustomer === 1 ? 'hasOpened' : 'noOpened'">{{ (scope.row.merchantInfo || {}).isSyncCustomer === 1 ? '客户已下推' : '客户未下推' }}</span>
              </div>
              <span
                v-if="scope.row.erpNumStatusCode && scope.row.erpNumStatusCode === 1"
                style="color: #ff2121"
              >{{scope.row.erpNumStatusDesc}}</span>
            </div>
            <div>
              {{ (scope.row.merchantInfo || {}).erpCodeOperator === 1?'(系统)':'' }}
              <span v-if="shopConfig.shopPatternCode !== 'ybm'">{{ (scope.row.merchantInfo || {}).merchantErp ? `ERP编码：${(scope.row.merchantInfo || {}).merchantErp}` : `未设置客户ERP编码` }}</span>
              <span v-if="shopConfig.shopPatternCode !== 'ybm'">
                <i
                  v-if="!(scope.row.merchantInfo || {}).merchantErp"
                  v-permission="['deal_order_setErpCode']"
                  class="el-icon-edit"
                  style="color: #4183d5"
                  @click="btnClick(scope.row, 'setERPCode')"
                />
              </span>
            </div>
            <div class="btnText" @click="btnClick(scope.row, 'seeQualification')">查看客户资质</div>
            <div class="btnText">
              <span  @click="btnClick(scope.row, 'ybmSalesman')" v-if="shopConfig.shopPatternCode !== 'ybm'">药帮忙业务员</span>
              <i v-if='scope.row.salesMobile && shopConfig.shopPatternCode !== "ybm"' class="el-icon-service" style='cursor: pointer;margin-left: 5px' @click='openService(scope.row.salesMobile)'/>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="金额" prop="money">
          <template slot-scope="scope">
            <div>订单实付金额：{{ scope.row.money }}</div>
            <div v-if="scope.row.refundAmount">退款金额：{{ scope.row.refundAmount }}</div>
            <div v-if="scope.row.shippingMoney">(包含运费金额：{{ scope.row.shippingMoney }})</div>
            <div v-if="scope.row.totalDiscount">总优惠：{{ scope.row.totalDiscount }}</div>
            <div v-if="scope.row.platformPromoDiscount">平台优惠：{{ scope.row.platformPromoDiscount }}</div>
            <div v-if="scope.row.shopPromoDiscount">店铺优惠：{{ scope.row.shopPromoDiscount }}</div>
            <!-- scope.row.totalAmount != scope.row.money && scope.row.shippingMoney != 0 &&  -->
            <div v-if="scope.row.totalDiscount != 0">订单总金额：{{ scope.row.totalAmount }}</div>
            <div v-if="scope.row.shippingReminderCompensationAmount"
                 style="color: red;cursor: pointer;"
                 @click="btnClick(scope.row, 'handleCompensate')">
              已发生保证金赔付：{{scope.row.shippingReminderCompensationAmount}}
            </div>
          </template>
        </el-table-column>
        <el-table-column label="状态及处理记录">
          <template slot-scope="scope" prop="statusName">
            <div>
              状态：{{ scope.row.statusName }}
              <i
                class="el-icon-search"
                style="color: #4183d5"
                @click="btnClick(scope.row, 'checkStatus')"
              />
            </div>
			<div v-if="scope.row.status == 10">
				支付方式：{{ scope.row.payTypeName }}
			</div>
            <div
              v-if="scope.row.status !== 10"
            >
              支付方式：
              <span v-if="scope.row.payChannel === 8">
                线下转账(
                <span style="color: red">电汇商业</span>)
                <el-button  v-if="scope.row.evidenceUrlList && scope.row.evidenceUrlList.length>0" type="text" @click="btnClick(scope.row, 'viewVoucher')">查看电汇凭证</el-button>
              </span>
              <span v-else>{{scope.row.payTypeName }}</span>
            </div>
            <div v-if="scope.row.status === 1">支付时间：{{ scope.row.payTime | formatDate }}</div>
            <div v-if="scope.row.status === 7">审核结果：通过</div>
            <div v-if="scope.row.status === 1 || scope.row.status === 7">
              下推ERP：{{ scope.row.erpPushDownStatusName }}
              <span
                v-if="scope.row.erpPushDownStatus !== 1 && scope.row.erpPushDownFailMsg"
                class="btnText"
                @click="btnClick(scope.row, 'seeReason')"
              >失败原因</span>
            </div>
            <div v-if="scope.row.status === 30 || scope.row.status === 31" class="dianhui">
              <p>
                请在
                <span style="color: red">{{scope.row.payExpireTime | formatDate}}</span>
              </p>
              <p>前与客户沟通并进行订单电汇审核</p>
              <p>超时订单将会自动取消</p>
            </div>
            <div v-if="scope.row.status === 2">
              包裹数：{{ scope.row.packagesNum > 0 ? scope.row.packagesNum : '' }}
              <i
                class="el-icon-search"
                style="color: #4183d5"
                v-if="scope.row.packagesNum > 0"
                @click="btnClick(scope.row, 'seePackages')"
              />
              <i
                v-if='shopConfig.isFbp !== 1 && shopConfig.shopPatternCode !== "ybm"'
                v-permission="['deal_order_updateLogistics']"
                class="el-icon-edit"
                style="color: #4183d5"
                @click="btnClick(scope.row, 'editLogisticsInfo')"
              />
            </div>
            <div v-if="scope.row.status === 2">{{ scope.row.logisticsWayDesc }}</div>
            <template v-if='scope.row.status === 2 && scope.row.trackingNos'>
              <div v-for='(item,index) in scope.row.trackingNos' :key='index'>{{item}}</div>
            </template>
            <div v-if="scope.row.status === 2">发货时间：{{ scope.row.deliveryTime | formatDate }}</div>
            <div v-if="scope.row.status === 3 || scope.row.status === 91">
              <div v-if="scope.row.status === 3">完成时间：{{ scope.row.orderCompletionTime | formatDate }}</div>
              <div v-if="shopConfig.shopPatternCode !== 'ybm'">{{ scope.row.logisticsWayDesc }}：
                <span v-for="track in scope.row.trackingNos" :key="track"> {{ track }} </span>
                <i
                  class="el-icon-search"
                  style="color: #4183d5"
                  @click="btnClick(scope.row, 'seePackages')"
                />
              </div>
            </div>
            <div
              v-if="scope.row.hasRefund"
              style="color: red; cursor: pointer;"
              @click="btnClick(scope.row, 'handleRefund')"
            >
              有申请退款
            </div>
            <div
              v-if="(scope.row.status === 2 || scope.row.status === 3 || scope.row.status === 33 || scope.row.status === 91) && !!scope.row.invoice"
              class="btnText"
              @click="btnClick(scope.row, 'seePdf')"
            >电子发票</div>
            <div v-if="scope.row.status === 4">取消原因：{{ scope.row.cancelReason }}</div>
            <div v-if="scope.row.status === 4">取消时间：{{ scope.row.cancellationTime | formatDate }}</div>
            <div
              v-if="scope.row.status === 91"
            >退款时间：{{ scope.row.refundCompletionTime | formatDate }}</div>
          </template>
        </el-table-column>
        <el-table-column label="收货信息" prop="takeDeliveryInfo">
          <template slot-scope="scope">
            <div>{{ (scope.row.takeDeliveryInfo || {}).name }}</div>
            <div>{{ (scope.row.takeDeliveryInfo || {}).telepHone }}</div>
            <div>{{ (scope.row.takeDeliveryInfo || {}).address }}</div>
            <div class="btnText" @click="btnClick(scope.row, 'seeInvoice')">
              <span
                v-if="scope.row.invoiceTypeDesc"
                :style="scope.row.invoiceType == 2?'color:red':''"
              >【{{scope.row.invoiceTypeDesc}}】</span>查看开票信息
            </div>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="150" align="center">
          <template slot="header">
            <div style="display:flex;justify-content: space-evenly;">
              <div>
                <el-button
                  type="primary"
                  size="mini"  @click.native="allToogleExpandCargo">
                    {{expandAll ? '全部收起' : '全部展开' }}
                </el-button>
              </div>
              <div style="line-height: 28px !important;">操作</div>
            </div>
          </template>
          <template slot-scope="scope">
            <div class="btnText" @click="toogleExpandCargo(scope.row)">
              商品详情
              <i :class="scope.row.showChildren ? 'el-icon-arrow-up' : 'el-icon-arrow-down'" />
            </div>
            <div class="btnText" v-if="scope.row.contract.showContract" @click="readContract(scope.row.contract.contractUrl)">
              查看购销合同
            </div>
            <div class="btnText" v-if="scope.row.collectContract.showContract" @click="readService(scope.row.collectContract.contractUrl)">
              查看平台代收款合同
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <invoice-info
      v-if="invoiceVisible"
      :dialog-visible="invoiceVisible"
      :merchant-id="(editRow.merchantInfo || {}).merchantId"
      @cancelDialog="cancelDialog(1)"
    />
    <editDialog
      v-if="editVisible"
      :dialog-edit-type="dialogEditType"
      :edit-visible="editVisible"
      :erp-name="erpName"
      :merchant-status="merchantStatus"
      :merchant-id="(editRow.merchantInfo || {}).merchantId"
      @closeDialog="cancelDialog(2)"
      @gitList="$emit('refreshList')"
    />
    <stateDialog ref="stateDialog" @setLocal="setLocalBtn" />
    <logInfoDialog ref="logInfoDialog" />
    <changeLogDialog ref="changeLogDialog" @gitList="$emit('refreshList')" />
    <pdfDialog
      v-if="pdfVisible"
      :pdf-visible="pdfVisible"
      :order-no="(editRow || {}).orderNo"
      :showDelBtn='shopConfig.isFbp !== 1'
      @closeDialog="cancelDialog(3)"
    />
    <ybmSalesmanDialog
      v-if="ybmSalesmanVisible"
      :dialog-visible="ybmSalesmanVisible"
      :merchant-id="(editRow.merchantInfo || {}).merchantId"
      @cancelDialog="cancelDialog(4)"
    />
    <operationLog
      v-if="operationLogDialogVisible"
      ref="operationLog"
      :merchant-id="(editRow.merchantInfo || {}).merchantId"
      :name="(editRow.merchantInfo || {}).merchantName"
      :sku-log-dialog-visible.sync="operationLogDialogVisible"
    />
    <informationChanges-log
      v-if="informationChangesLogVisible"
      :dialog-visible="informationChangesLogVisible"
      :merchant-id="(editRow.merchantInfo || {}).merchantId"
      @cancelDialog="cancelDialog(5)"
      @gitList="$emit('refreshList')"
    />

    <certificationErrorDialog
      v-if="certificationErrorVisible"
      :dialog-visible="certificationErrorVisible"
      :merchant-id="(editRow.merchantInfo || {}).merchantId"
      :row="editRow"
      :shop-config="shopConfig"
      @cancelDialog="cancelDialog(7)"
      @gitList="$emit('refreshList')"
      @openService="openService($event)"
    />

    <logisticsDelivery-dialog
      v-if="logisticsDeliveryDialogVisible"
      :dialog-visible="logisticsDeliveryDialogVisible"
      :merchant-id="(editRow.merchantInfo || {}).merchantId"
      :order-no="(editRow || {}).orderNo"
      :merchant-name="(editRow.merchantInfo || {}).merchantName"
      :edit-information="true"
      @cancelDialog="cancelDialog(6)"
      @gitList="$emit('refreshList')"
    />
    <el-image
      ref="previewImg"
      style="width: 0; height: 0"
      :preview-src-list="srcList"
    />
    <el-dialog
      title="部分发货标注处理"
      :visible="batchUpdatePartialShipmentVis"
      width="60%"
      class="partialShipmentDialog"
      @close="batchUpdatePartialShipmentVis = false"
    >
      <div>
        <el-radio-group
          v-model="partialShipmentStatus"
          style="line-height: 40px;"
        >
          <el-radio :label="2">
            已发起退款申请
          </el-radio>
          <el-radio :label="3">
            已分批发货，无需退款
          </el-radio>
        </el-radio-group>
      </div>
      <span slot="footer">
        <el-button
          size="mini"
          @click="batchUpdatePartialShipmentVis = false"
        >取 消</el-button>
        <el-button
          type="primary"
          size="mini"
          @click="batchUpdatePartialShipment"
        >确 定</el-button>
      </span>
    </el-dialog>
	<el-dialog title="资质需求" :visible="idialog.status" @close="idialog.status = false;">
		<h3>企业资质</h3>
		<p>{{ idialog.corpCredential }}</p>
		<h3>商品首营资料</h3>
		<p>{{ idialog.productCredential }}</p>
		<h3>商品药检报告</h3>
		<p>{{ idialog.drugSupervisionReport }}</p>
		<span slot="footer">
			<el-button size="mini" @click="idialog.status = false" type="primary">确定</el-button>
		</span>
	</el-dialog>
  </div>
</template>
<script>
import { getOrderDetailList, apiPopDialogSendNew, batchUpdatePartialShipment, apiBatchQueryOrderDetailList,pushErp } from '@/api/order/index';
import { look } from "../../../api/afterSaleManager/index"
import { mapState } from 'vuex';
import { queryMsgToken } from '@/api/home';
import invoiceInfo from '../../customer-management/invoiceInfo';
import orderDetail from './orderDetail.vue';
import editDialog from './editDialog.vue';
import stateDialog from './stateRecords.vue';
import logInfoDialog from './logInfoDialog.vue';
import changeLogDialog from './changeLogDialog.vue';
import pdfDialog from '../invoicePdf.vue';
import orderBtns from './orderBtns';
import ybmSalesmanDialog from './ybmSalesmanDialog.vue';
import operationLog from './operationLog.vue';
import informationChangesLog from './informationChangesLog.vue';
import logisticsDeliveryDialog from './logisticsDeliveryDialog.vue';
import certificationErrorDialog from './certificationErrorDialog';

export default {
  name: 'TabComponent',
  components: { invoiceInfo, orderDetail, orderBtns, editDialog, stateDialog, logInfoDialog, changeLogDialog, pdfDialog , ybmSalesmanDialog, operationLog, informationChangesLog, logisticsDeliveryDialog, certificationErrorDialog },
  props: {
    activeOrderStatus: {
      type: String,
      default: null,
    },
    tableData: {
      type: Array,
      default: () => [],
    },
    loading: {
      type: Boolean,
      default: false,
    },
    ruleForm: {
      type: Object,
      default: () => {},
    },
    listQuery: {
      type: Object,
      default: () => {},
    },
    logisticsCompanyList: {
      type: Array,
      default: () => [],
    },
    showConfirmFinishButton: {
      type: Boolean,
      default: false,
    },
    tabData: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      certificationErrorVisible: false,
      currentEditItem: {},
      partialShipmentStatus: -1,
      batchUpdatePartialShipmentVis: false,
      selectRows: [],
      invoiceVisible: false,
      dialogEditType: '',
      editVisible: false,
      editRow: {},
      erpName: '',
      merchantStatus: null,
      pdfVisible: false,
      ybmSalesmanVisible: false,
      operationLogDialogVisible: false,
      informationChangesLogVisible: false,
      logisticsDeliveryDialogVisible: false,
      srcList: [],
      expandAll: false,
	  idialog: {
		corpCredential: '',        //企业资质
		drugSupervisionReport: '',   //药检报告
		productCredential: '',   //企业首营资质
		status: false,
		loading: false,
	  }
    };
  },
  computed: { ...mapState('app', ['shopConfig']) },
  watch: {
    listQuery() {
      this.expandAll = false;
    },
  },
  methods: {
    readContract(url) {
      if (url) {
         window.open(url, "_blank")
      } else {
         this.$message.error('暂未生成购销合同，请稍后重试')
      }
    },
    readService(url) {
      if (url) {
         window.open(url, "_blank")
      } else {
         this.$message.error('暂未生成平台代收款合同，请稍后重试')
      }
    },
    handleEditPartialShipment(row, type) {
      if (!type) {
        this.currentEditItem = row;
      } else {
        this.currentEditItem.batchArray = row;
      }
      this.batchUpdatePartialShipmentVis = true;
    },
    batchUpdatePartialShipment() {
      if (this.partialShipmentStatus === -1) {
        this.$message.warning('请选择部分发货标注处理');
        return false;
      }
      const parmas = {};
      parmas.partialShipmentStatus = this.partialShipmentStatus;
      if (this.currentEditItem.batchArray) {
        parmas.orderNoStr = this.currentEditItem.batchArray.map(item => item.orderNo).join(',');
        parmas.orderStatus = this.currentEditItem.batchArray[0].status;
      } else {
        parmas.orderNoStr = this.currentEditItem.orderNo;
        parmas.orderStatus = this.currentEditItem.status;
      }
      batchUpdatePartialShipment(parmas).then((res) => {
        if (res.code === 0) {
          this.$message.success(res.msg || '成功');
          this.batchUpdatePartialShipmentVis = false;
          this.$emit('refreshList');
        }
      });
    },
    popDialogSend(orderNo, remark) {
      apiPopDialogSendNew({ orderNo, remark }).then((res) => {
        if (res.code === 0) {
          this.$message.success('发送成功');
        } else {
          this.$message.error(res.msg || '发送失败');
        }
      });
    },
    setLocalBtn() {
      const local = { ...this.listQuery, ...this.ruleForm, activeOrderStatus: this.activeOrderStatus };
      this.util.setLocal('orderListLocal', local);
    },
    clearSelection() {
      this.$refs.cargoTable.clearSelection();
    },
    toogleExpandCargo(row) {
      // const $table = this.$refs.cargoTable;
      // $table.toggleRowExpansion(row);
      this.$set(row, 'showChildren', !row.showChildren);
      if (row.showChildren) {
        getOrderDetailList({ orderNo: row.orderNo }).then((res) => {
          if (res.code === 0) {
            this.$set(row, 'detailList', (res || {}).result || []);
          }
        });
      }
    },
    // 全部展开-批量查询订单明细
    allToogleExpandCargo() {
      this.$nextTick(() => {
        if (this.expandAll) {
          this.tableData.forEach((item) => {
            this.$set(item, 'showChildren', false);
          });
        } else {
          const dataLists = [];
          this.tableData.forEach((item) => {
            dataLists.push(item.orderNo);
          });

          apiBatchQueryOrderDetailList({ orderNos: dataLists.join(',') }).then((res) => {
            if (res.code === 0) {
              this.tableData.forEach((item) => {
                let itemChildren = (res.result || []).find((e) => {return e.orderNo === item.orderNo});
                if (itemChildren) {
                  this.$set(item, 'detailList', itemChildren.orderDetailVos || []);
                  this.$set(item, 'showChildren', true);
                }
              });
            }
          });
        }

        this.expandAll = !this.expandAll;
      });
    },
    handleSelectionChange(val) {
      this.selectRows = val;
    },
    cancelDialog(from) {
      if (from === 1) {
        this.invoiceVisible = false;
      } else if (from === 2) {
        this.editVisible = false;
        this.erpName = '';
      } else if (from === 3) {
        this.pdfVisible = false;
      } else if (from === 4) {
        this.ybmSalesmanVisible = false;
      } else if (from === 5) {
        this.informationChangesLogVisible = false;
      } else if (from === 6) {
        this.logisticsDeliveryDialogVisible = false;
      } else if (from === 7) {
        this.certificationErrorVisible = false;
      }
    },
    btnClick(row, type) {
      this.editRow = row || {};
      if (type === 'certificationError') {
        this.certificationErrorVisible = true;
        return false;
      }
      if (type === 'seeQualification') { // 查看资质
        // const local = { ...this.listQuery, ...this.ruleForm, activeOrderStatus: this.activeOrderStatus };
        // this.util.setLocal('orderListLocal', local);
        this.setLocalBtn();
        this.$router.push({ path: '/customerQualification', query: { merchantId: row.merchantInfo?row.merchantInfo.merchantId:'', orderNo: row.orderNo, orderStatus: row.status } });
      } else if (type === 'seeInvoice') { // 查看开票信息
        this.invoiceVisible = true;
      } else if (type === 'checkStatus') { // 状态查看
        this.$refs.stateDialog.getListData(this.editRow.orderNo, row.merchantInfo?row.merchantInfo.merchantId:'');
      } else if (type === 'setERPCode') { // 设置erp编码
        this.erpName = row.merchantInfo.merchantName;
        this.merchantStatus = row.merchantInfo.merchantStatus;
        this.editVisible = true;
        this.dialogEditType = 'editErp';
      } else if (type === 'seePdf') { // 查看电子发票pdf
        this.pdfVisible = true;
      } else if (type === 'seePackages') { // 查看包裹数
        this.$refs.logInfoDialog.getListData(this.editRow.orderNo);
      } else if (type === 'editLogisticsInfo') { // 修改物流信息
        // this.$refs.changeLogDialog.getListData(this.editRow.orderNo);
        this.logisticsDeliveryDialogVisible = true;
      } else if (type === 'seeReason') { // 查看erp下推失败原因
        if (this.editRow.erpPushDownFailMsg) {
          const showConfirm = this.editRow.erpPushDownFailMsg?.includes("ERP商品编码未维护！");
          this.$confirm(this.editRow.erpPushDownFailMsg, 'ERP下推失败原因', {
            confirmButtonText: showConfirm ? '强制下推' : "",
            cancelButtonText: showConfirm ? '取消' : '关闭',
            showConfirmButton: showConfirm,
          }).then(() => {
            const formData = new FormData();
            formData.append('orderNo', this.editRow.orderNo);
            pushErp(formData).then((res) => {
              if(res.code === 0) {
                this.$message.success(res.msg || '成功');
              } else {
                this.$message.error(res.msg || '失败');
              }
            }).catch(() => {
              this.$message.error('失败');
            })
          }).catch(() => {})
        }
      } else if (type === 'handleRefund') { // 处理退款单
        // 打开退款单管理页面
        // const mid = 999999;
        // window.top.$('#mainFrameTabs').bTabsAdd(mid, '售后管理', `/afterSales/index?orderNo=${this.editRow.orderNo}&auditStateListJson=true`);
        const path = '/afterSaleList';
        window.openTab(path, { orderNo: this.editRow.orderNo, auditStateListJson: '[0]', auditState: '0' });
      } else if (type === 'ybmSalesman') { // 查看药帮忙业务员
        this.ybmSalesmanVisible = true;
      } else if (type === 'operationLog') { // 操作日志
        this.operationLogDialogVisible = true;
      } else if (type === 'informationChanges') {
        // 信息有变更
        this.informationChangesLogVisible = true;
      } else if (type === 'viewVoucher') {
        // 查看电汇凭证
        if (Array.isArray(row.evidenceUrlList)) {
          this.srcList = row.evidenceUrlList;
          this.$nextTick(() => {
            this.$refs.previewImg.clickHandler();
          });
        }
      } else if (type === 'handleAfterSale') {
        window.openTab('/afterSaleManager', { homeEnter:1, orderNo: row.orderNo })
      } else if(type === "handleCompensate") { // 处理保证金赔付
        window.openTab('/marginAccount', { homeEnter:1, orderNo: row.orderNo })
      }
    },
    async openService(key) {
      // console.log(**********, key)
      const res = await queryMsgToken();
      if (res && res.code === 0) {
        const { token, domainName } = res.data;
        const str = `&orgId=${key}`;
        const userId = `&userId=${res.data.userId}`;
        console.log(domainName + token + str+userId);
        window.open(domainName + token + str+userId);
      } else {
        this.$message.error(res.message);
      }
    },
	look(orderNo) {
		if (this.idialog.loading) return;
		this.idialog.loading = true;
		look(orderNo).then(res => {
			if (res.code == 0) {
				if (res.result) {
					this.idialog.corpCredential = res.result.corpCredential;
					this.idialog.drugSupervisionReport = res.result.drugSupervisionReport;
					this.idialog.productCredential = res.result.productCredential;
				} else {
					this.idialog.corpCredential = '';
					this.idialog.drugSupervisionReport = '';
					this.idialog.productCredential = '';
				}
				this.idialog.status = true;
			} else {
				this.$message.error(res.message)
			}
		}).finally(() => {
			this.idialog.loading = false;
		})
	}
  },
};
</script>
<style lang="scss" scoped>
.tabComp {
  ::v-deep  .el-table th {
    background: #f9f9f9;
  }
  ::v-deep  .el-table__expand-column {
    pointer-events: none;
  }
  ::v-deep  .el-table__expand-column .el-icon {
    visibility: hidden;
  }
  ::v-deep  .el-table__expanded-cell[class*='cell'] {
    padding: 0;
  }
  .btnText {
    color: #4184d5;
    cursor: pointer;
  }
  .hasOpened {
    display: inline-block;
    font-size: 12px;
    height: 22px;
    line-height: 22px;
    text-align: center;
    background: #e6f7ff;
    border: 1px solid #91d5ff;
    border-radius: 3px;
    color: #1890ff;
    padding: 0 5px;
  }
  .noOpened {
    display: inline-block;
    font-size: 12px;
    height: 22px;
    line-height: 22px;
    text-align: center;
    background: #fff1f0;
    border: 1px solid #ffa39e;
    border-radius: 3px;
    color: #ff4d4f;
    padding: 0 5px;
  }
  .informationChanges {
    margin-bottom: 5px;
    display: inline-block;
    font-size: 12px;
    padding: 0 5px 0 5px;
    text-align: center;
    background: #fff1f0;
    border: 1px solid #ffa39e;
    border-radius: 3px;
    color: #ff4d4f;
    cursor: pointer;
  }
  .remarkBox,
  .remarkShowBox {
    padding: 10px 20px;
    color: #ff2121;
    font-size: 16px;
  }
  .listTable {
    ::v-deep  .el-table td {
      border-bottom: none;
    }
  }
  ::v-deep  .el-table td.el-table__expanded-cell {
    border-bottom: 1px solid #ebeef5;
  }
}
.dianhui {
  p {
    margin: 0;
  }
}
::v-deep  .el-table {
  .el-checkbox__inner {
    border: 1px solid #000000;
  }
}
</style>
