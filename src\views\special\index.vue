<template>
  <div>
    <div class="line-div">
      <div class="topTip">
        温馨提示：商品在参与特价活动期间(活动创建时间-活动结束时间)，超出限购数量按照特价活动创建时的商品快照价格(快照价格非商品实时价格)购买
      </div>
      <div class="serch">
        <el-row type="flex" align="middle">
          <span class="sign" />
          <div>查询条件</div>
        </el-row>
      </div>
      <div class="box-div">
        <el-row :gutter="20" class="my-search-row">
          <el-col :span="6">
            <el-input
              v-model="listQuery.promotionId"
              placeholder="请输入内容"
              size="small"
            >
              <template slot="prepend">活动ID</template>
            </el-input>
          </el-col>
          <el-col :span="6">
            <el-input
              v-model.number="listQuery.csuId"
              placeholder="请输入内容"
              size="small"
            >
              <template slot="prepend">SKU编码</template>
            </el-input>
          </el-col>
          <el-col :span="6">
            <el-input
              v-model="listQuery.skuCode"
              placeholder="请输入内容"
              size="small"
            >
              <template slot="prepend">商品编号</template>
            </el-input>
          </el-col>
          <el-col :span="6">
            <el-input
              v-model="listQuery.skuName"
              placeholder="请输入内容"
              size="small"
            >
              <template slot="prepend">商品名称</template>
            </el-input>
          </el-col>
        </el-row>
        <el-row :gutter="20" class="my-search-row" style="margin-top: 12px">
          <el-col :span="6">
            <el-input
              v-model="listQuery.title"
              placeholder="请输入内容"
              size="small"
            >
              <template slot="prepend">活动名称</template>
            </el-input>
          </el-col>
          <el-col :span="6">
            <span class="search-title">是否成单</span>
              <el-select
              v-model="listQuery.isOrder"
              size="small"
              placeholder="请选择"
            >
              <el-option label="全部" value="" />
              <el-option label="是" :value="1" />
              <el-option label="否" :value="2" />
            </el-select>
          </el-col>
          <el-col :span="12">
            <span class="search-title">活动时间</span>
            <div class="time-box">
              <el-date-picker
                v-model="listQuery.searchStartTime"
                class="picker-time"
                type="date"
                size="small"
                placeholder="选择日期"
              />
              <span class="time-text">至</span>
              <el-date-picker
                v-model="listQuery.searchEndTime"
                class="picker-time"
                type="date"
                size="small"
                placeholder="选择日期"
              />
            </div>
          </el-col>
        </el-row>
        <el-row
          :gutter="20"
          style="margin-top: 12px; text-align: right; width: 100%"
          class="my-search-row"
        >
          <el-col style="text-align: right; width: 100%">
            <el-button size="small" @click="resetList">重置</el-button>
            <el-button type="primary" size="small" @click="getList"
              >查询</el-button
            >
          </el-col>
        </el-row>
      </div>
    </div>
    <div style="padding: 15px 20px">
      <div class="serch">
        <el-row type="flex" align="middle">
          <span class="sign" />
          <div>活动列表</div>
        </el-row>
      </div>
      <div class="button-box">
        <div>
          <!-- <router-link to="specialPrice/operate"> -->
          <el-button
            v-permission="['marketing_specialPrice_add']"
            size="small"
            @click="createAct"
            type="primary"
            >创建</el-button
          >
          <!-- </router-link> -->
          <el-button
            v-permission="['marketing_specialPrice_batchAdd']"
            style="margin-left: 10px"
            size="small"
            type="primary"
            @click="createdDialog = true"
            >批量创建</el-button
          >
        </div>
        <div>
          <el-button
            size="small"
            type="primary"
            plain
            @click="exportActive('exportList')"
            >导出活动列表</el-button
          >
          <el-button
            size="small"
            type="primary"
            plain
            @click="exportActive('exportDetail')"
            >导出活动明细</el-button
          >
          <!-- <el-button
            v-permission="['marketing_specialPrice_exportActivityDetail']"
            size="small"
            type="primary"
            plain
            @click="exportActive('active')"
          >查看活动明细</el-button>
          <el-button
            v-permission="['marketing_specialPrice_exportSkuDetail']"
            size="small"
            type="primary"
            plain
            @click="exportActive('shop')"
          >查看商品明细</el-button> -->
        </div>
      </div>
      <div>
        <div>
          <el-tabs v-model="activePane" @tab-click="tabHandleClick">
            <el-tab-pane
              v-for="item in tabStatusOptions"
              :key="item.tabStatus"
              :name="item.tabStatus"
            >
              <span slot="label">
                {{ item.tabName }}({{ item.tabCount || 0 }})
              </span>
            </el-tab-pane>
          </el-tabs>
        </div>
        <el-table
          ref="multipleTable"
          :data="tableData"
          stripe
          border
          tooltip-effect="dark"
          style="width: 100%"
        >
          <el-table-column prop="promotionId" label="活动ID" />
          <el-table-column prop="title" label="活动名称" width="180" />
          <el-table-column label="活动时间" width="200">
            <template slot-scope="scope">
              <p>起：{{ scope.row.startTime | formatDate }}</p>
              <p>止：{{ scope.row.endTime | formatDate }}</p>
            </template>
          </el-table-column>
          <el-table-column label="人群" width="150">
            <template slot-scope="{ row }">
              人群ID:
              <el-button
                size="small"
                type="text"
                @click="addCroed(row.customerGroupId)"
              >
                {{ row.customerGroupId }}
              </el-button>
            </template>
          </el-table-column>
          <el-table-column
            prop="statisticInfo"
            label="销售数据"
            width="170"
            :render-header="renderHeader"
          >
            <template slot-scope="{ row }">
              <div>
                <p>
                  <span style="color: red">{{
                    row.isOrder === 1 ? '已成单' : '未成单'
                  }}</span>
                  <span
                    v-if="row.isOrder === 1"
                    style="
                      cursor: pointer;
                      font-size: 16px;
                      vertical-align: middle;
                      margin-left: 5px;
                    "
                    @click="handleSeeActivitySaleDataSummaryInfo(row)"
                  >
                    <i class="el-icon-view" />
                  </span>
                </p>
                <div
                  v-if="row.isOrder === 1 && row.activitySaleDataSummaryInfo"
                >
                  <p>
                    <el-tooltip
                      class="item"
                      content="有效订单中包含活动ID，对应客户去重"
                      placement="top"
                    >
                      <i class="el-icon-warning-outline" />
                    </el-tooltip>
                    采购店数：{{
                      row.activitySaleDataSummaryInfo.purchaseMerchantNum
                    }}
                  </p>
                  <p>
                    <el-tooltip
                      class="item"
                      content="有效订单中包含特价活动/拼团活动ID，对应订单计数"
                      placement="top"
                    >
                      <i class="el-icon-warning-outline" />
                    </el-tooltip>
                    采购订单数：{{
                      row.activitySaleDataSummaryInfo.purchaseOrderNum
                    }}
                  </p>
                  <p>
                    <el-tooltip
                      class="item"
                      content="有效订单、商品行中包含活动ID，取包含对应活动ID的各个商品行【应发货数量=商品数量-已退数量】之和"
                      placement="top"
                    >
                      <i class="el-icon-warning-outline" />
                    </el-tooltip>
                    采购数量：{{
                      row.activitySaleDataSummaryInfo.purchaseProductNum
                    }}
                  </p>
                  <p>
                    <el-tooltip
                      class="item"
                      content="有效订单、商品行中包含活动ID，取【实付金额*应发货数量/商品数量=实付金额*（商品数量-已退数量）/商品数量】之和"
                      placement="top"
                    >
                      <i class="el-icon-warning-outline" />
                    </el-tooltip>
                    采购金额：{{
                      row.activitySaleDataSummaryInfo.purchaseAmount
                    }}
                  </p>
                  <span
                    style="color: #4183d5; cursor: pointer"
                    @click="handleGoGroupSalesData(row)"
                    >详情</span
                  >
                </div>
              </div>
              <!-- <el-button type="text" @click="goGoodsDetail('statisticInfo',scope.row)">详情</el-button> -->
            </template>
          </el-table-column>
          <el-table-column prop="businessStatusText" label="活动状态" />
          <el-table-column label="创建时间" width="160">
            <template slot-scope="scope">
              <span>{{ scope.row.createTime | formatDate }}</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" fixed="right" width="140">
            <template slot-scope="scope">
              <span class="colorB" @click="goGoodsDetail('detail', scope.row)"
                >查看</span
              >
              <span
                v-permission="['marketing_specialPrice_edit']"
                v-if="
                  scope.row.businessStatus != 4 && scope.row.businessStatus != 5
                "
                @click="goGoodsDetail('edit', scope.row)"
              >
                |
                <i class="colorB">修改</i> |
              </span>
              <span
                v-permission="['marketing_specialPrice_end']"
                v-if="
                  scope.row.businessStatus != 4 && scope.row.businessStatus != 5
                "
                class="colorB"
                @click="goGoodsDetail('detele', scope.row)"
                >结束</span
              >
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="page-box">
        <Pagination
          v-show="totalList > 0"
          :total="totalList"
          :page.sync="queryPage.pageNum"
          :limit.sync="queryPage.pageSize"
          @pagination="getList"
        />
      </div>
    </div>
    <div class="choose">
      <el-dialog
        title="批量创建"
        :visible.sync="createdDialog"
        v-if="createdDialog"
        width="40%"
      >
        <div class="conten-box">
          <div>请选择上传批量文件</div>
          <div class="uploadBtnBox">
            <el-upload
              ref="excludeImport"
              class="upload-demo"
              action="xxx"
              :http-request="uploadFile"
              :before-remove="removeImportData"
              :show-file-list="true"
              :limit="1"
              accept=".xls, .xlsx, .XLS, .XLSX"
            >
              <el-button size="small" type="primary">选择文件</el-button>
            </el-upload>
            <el-button
              style="margin-left: 10px"
              size="small"
              plain
              @click="exportActive('goods')"
              >下载模板</el-button
            >
          </div>
        </div>
        <div slot="footer" class="dialog-footer">
          <el-button size="small" @click="cancelDialog">取 消</el-button>
          <el-button type="primary" size="small" @click="createdGoodsList"
            >确 定</el-button
          >
        </div>
      </el-dialog>
    </div>
    <crowd-selector-dialog
      :crowd-dialog-vis="crowdDialogVis"
      :selected="innerSelected"
      @cancelModal="cancelModal"
      @selectChange="selectChange"
    />
    <export-tip
      :change-export="changeExport"
      @handleExoprClose="handleExoprClose"
      @handleChangeExport="handleChangeExport"
    />
  </div>
</template>

<script>
import { getSummaryInfo } from '@/api/market/collageActivity'
import exportTip from '@/views/other/components/exportTip';
import {
  getSpecialList,
  offlineGoods,
  batchImportPromotion,
  getStatusCountInfo,
  exportSkuDetailData
} from '../../api/activity/special'
import Pagination from '../../components/Pagination/index.vue'
import crowdSelectorDialog from '../marketing/components/crowdSelectorDialog'

export default {
  name: 'SpecialPrice',
  components: { Pagination, crowdSelectorDialog, exportTip },
  data() {
    return {
      selectIds: '',
      crowdDialogVis: false,
      innerSelected: '',
      changeExport: false,
      listQuery: {
        promotionId: '',
        title: '',
        businessStatus: '',
        searchStartTime: '',
        searchEndTime: '',
        skuName: '',
        skuCode: '',
        csuId: '',
        isOrder: ''
      },
      queryPage: {
        pageNum: 1,
        pageSize: 10
      },
      totalList: 0,
      tableData: [],
      createdDialog: false,
      fileFormData: {},
      tabStatusOptions: [
        {
          tabName: '全部',
          tabStatus: '0',
          tabCount: 0,
          index: 'totalCount'
        },
        {
          tabName: '进行中',
          tabStatus: '3',
          tabCount: 0,
          index: 'startingCount'
        },
        {
          tabName: '未开始',
          tabStatus: '2',
          tabCount: 0,
          index: 'unStartCount'
        },
        {
          tabName: '已下线',
          tabStatus: '5',
          tabCount: 0,
          index: 'stopOrOffLineCount'
        }
      ],
      activePane: '0'
    }
  },
  created() {
    this.getList()
  },
  activated() {
    if (this.$route.query.refresh) {
      this.getList()
    }
  },
  methods: {
    handleExoprClose() {
      this.changeExport = false;
    },
    handleChangeExport(info) {
      this.changeExport = false;
      if (info === 'go') {
        const path = '/downloadList';
        window.openTab(path);
      }
    },
    handleGoGroupSalesData(row) {
      // this.$router.push({ path: '/groupSalesData', query: { activityType: 8, marketingIdStr: row.promotionId } });
      window.openTab('/groupSalesData', {
        activityType: 8,
        marketingIdStr: row.promotionId
      })
    },
    tabHandleClick() {
      this.listQuery.businessStatus =
        this.activePane === '0' ? '' : this.activePane
      this.getList()
    },
    getStatusCountInfo() {
      getStatusCountInfo(this.listQuery).then((res) => {
        if (res.success) {
          const { data } = res
          if (data.statusCountInfo) {
            Object.keys(data.statusCountInfo).forEach((key) => {
              const current = this.tabStatusOptions.find((item) => {
                return item.index === key
              })
              if (current) {
                current.tabCount = data.statusCountInfo[key]
              }
            })
          }
        }
      })
    },
    handleSeeActivitySaleDataSummaryInfo(row) {
      getSummaryInfo({ activityType: 8, marketingIdStr: row.promotionId }).then(
        (res) => {
          if (res.success) {
            const { data } = res
            row.activitySaleDataSummaryInfo = data.summaryInfo || null
            this.$set(row, row)
          }
        }
      )
    },
    selectChange(val) {
      this.selectIds = val
    },
    cancelModal() {
      this.crowdDialogVis = false
      this.bulkChangesDialog = false
    },
    addCroed(value) {
      this.innerSelected = value
      this.crowdDialogVis = true
    },
    renderHeader(h, { column }) {
      return h('div', [
        h('span', column.label),
        h(
          'el-tooltip',
          {
            props: {
              content:
                '销售数据剔除未支付、已取消、已退款且部分退中活动商品应发货数量等于0的订单数据，仅统计已支付的有效订单。可至详情页查询未支付、已取消订单',
              placement: 'right'
            }
          },
          [h('i', { class: 'el-icon-warning-outline' })]
        )
      ])
    },
    getList() {
      this.getStatusCountInfo()
      const params = { ...this.listQuery }
      const load = this.loadingFun()
      getSpecialList(params, this.queryPage).then((res) => {
        load.close()
        if (res.code === 0) {
          this.tableData = res.result.list
          this.totalList = res.result.total
        } else {
          this.$message.error('获取列表失败')
        }
      })
    },
    resetList() {
      this.listQuery = {
        promotionId: '',
        title: '',
        businessStatus: '',
        searchStartTime: '',
        searchEndTime: '',
        skuName: '',
        skuCode: '',
        csuId: '',
        isOrder: ''
      }
      this.queryPage = {
        pageNum: 1,
        pageSize: 10
      }
      this.getList()
    },
    exportActive(from) {
      const send = JSON.parse(JSON.stringify(this.listQuery))
      let url = ''
      if (from === 'exportList') {
        url = `${
          process.env.VUE_APP_BASE_API
        }/promo/promotion/exportPromotionData/${this.getParams(send)}`
      } else if (from === 'exportDetail') {
        const load = this.loadingFun()
        exportSkuDetailData(send).then((res) => {
          load.close()
          if (res.code !== 0) {
            this.$message.error(res.message)
            return
          } else {
            this.changeExport = true
          }
        })
        return
        // url = `${process.env.VUE_APP_BASE_API}/promo/promotion/exportSkuDetailData/${this.getParams(send)}`;
      } else if (from === 'goods') {
        url = `${process.env.VUE_APP_BASE_API}/promo/promotion/templateDownload`
      }
      const a = document.createElement('a')
      a.href = url
      a.click()
      // exportActiveList(send).then( res => {
      //   console.log(res)
      // })
    },
    uploadFile(params) {
      const { file } = params
      if (file) {
        this.fileFormData.multipartFile = file
      } else {
        this.$message.warning('请选择上传文件!')
      }
    },
    removeImportData() {
      this.fileFormData = {}
    },
    loadingFun() {
      const loading = this.$loading({
        lock: true,
        text: 'Loading',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      return loading
    },
    getParams(params) {
      let queryStr = '?'
      Object.keys(params).forEach((key) => {
        queryStr += `${key}=${params[key]}&`
      })
      queryStr = queryStr.substr(0, queryStr.length - 1)
      return queryStr
    },
    cancelDialog() {
      this.fileFormData = {}
      this.createdDialog = false
    },
    createdGoodsList() {
      // this.$refs.goodsUpload.submit()
      if (!this.fileFormData.multipartFile) {
        this.$message.warning('请选择要上传的文件')
        return
      }
      const loading = this.$loading({
        lock: true,
        text: 'Loading',
        spinner: 'el-icon-loading',
        background: 'rgba(255,255,255, 0.8)'
      })
      batchImportPromotion(this.fileFormData)
        .then((res) => {
          loading.close()
          if (res.code === 1000) {
            let con = ''
            const specialProductImportResult =
              res.data.specialProductImportResult || {}
            if (specialProductImportResult.failureNum > 0) {
              con = `<p>导入成功${specialProductImportResult.successNum}条，失败${specialProductImportResult.failureNum}条，失败原因请下载错误文件：<br><a style="color: #ff0021" href="${specialProductImportResult.failureExcelFileDownloadUrl}" download="导入商品错误文件下载">导入商品错误文件下载</a></p>`
            } else {
              con = `<p>导入成功${specialProductImportResult.successNum}条，失败${specialProductImportResult.failureNum}条</p>`
            }
            this.$confirm(con, '提示', {
              confirmButtonText: '确定',
              dangerouslyUseHTMLString: true,
              cancelButtonText: '取消'
            }).then(() => {
              this.createdDialog = false
              this.fileFormData = {}
              setTimeout(() => {
                this.getList()
              }, 500)
            })
          } else {
            this.$message.error(res.msg)
          }
        })
        .catch((error) => {
          console.log(error)
          loading.close()
          this.$message({
            message: '请求失败',
            type: 'error'
          })
        })
    },
    createAct() {
      this.$router.push('/specialPrice/operate')
    },
    goGoodsDetail(from, value) {
      if (from == 'detail') {
        this.$router.push({
          path: '/specialPrice/detail',
          query: { promotionId: value.promotionId }
        })
      } else if (from == 'edit') {
        this.$router.push({
          path: '/specialPrice/operate',
          query: { promotionId: value.promotionId }
        })
      } else if (from === 'statisticInfo') {
        this.$router.push({
          path: '/specialPrice/statisticInfo',
          query: { promotionId: value.promotionId, refresh: true }
        })
      } else {
        this.$confirm('确认要结束此活动吗?', '温馨提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            offlineGoods({ promotionId: value.promotionId }).then((res) => {
              if (res.code == 0) {
                this.getList()
              }
            })
          })
          .catch(() => {})
      }
    }
  }
}
</script>

<style scoped lang="scss">
.upload-demo {
  display: inline-block;
  margin-left: 10px;
}
.topTip {
  padding: 5px 20px;
  background: #f3d9b2;
  opacity: 0.8;
  color: #ff2121;
}
.serch {
  font-weight: bold;
  .sign {
    display: inline-table;
    width: 3px;
    height: 13px;
    background: linear-gradient(360deg, #1d69c4 1%, #8bbdfc 99%);
    border-radius: 2px;
    margin-right: 8px;
  }
}
.line-div {
  padding: 15px 20px;
  border-bottom: 1px solid #efefef;
  .search-title {
    display: table-cell;
    padding: 0 10px;
    text-align: center;
    border: 1px solid #dcdfe6;
    height: 30px;
    line-height: 30px;
    vertical-align: middle;
    border-right: none;
    border-radius: 4px 0 0 4px;
    color: #333333;
    white-space: nowrap;
  }
  .my-search-row ::v-deep  .el-input__inner {
    border-radius: 0 4px 4px 0;
  }
  .my-search-row ::v-deep  .el-select {
    display: table-cell;
  }
  ::v-deep  .el-input-group__prepend {
    background: none;
    color: #333333;
    padding: 0 10px;
  }
  .div-text {
    p {
      margin: 0;
      padding-top: 10px;
    }
    .tip-box {
      padding: 5px 0 10px 20px;
      .tip {
        font-size: 12px;
        color: #666666;
        margin: 0;
        padding: 3px 0;
      }
    }
  }
  .box-div {
    padding-top: 15px;
  }
}
.my-search-row ::v-deep  .el-input__inner {
  border-radius: 0 4px 4px 0;
}
.my-search-row {
  .time-box {
    display: table-cell;
    width: 100%;
    .time-text {
      padding: 0 5px;
    }
  }
  .picker-time {
    width: 45%;
  }
}
.my-search-row ::v-deep  .el-select {
  display: table-cell;
  width: 100%;
}
.button-box {
  padding: 15px 0;
  display: flex;
  justify-content: space-between;
}
.page-box {
  text-align: right;
  padding-top: 15px;
}
.colorB {
  color: #4183d5 !important;
  cursor: pointer;
  font-style: normal;
  a {
    text-decoration: none;
    color: #4183d5 !important;
  }
}
.choose {
  ::v-deep  .el-button--primary {
    background: #4183d5;
  }
  ::v-deep  .el-dialog__body {
    padding: 0 0 10px;
  }
  ::v-deep  .el-dialog__header {
    padding: 10px 16px;
    background: #f9f9f9;
  }
  ::v-deep  .el-dialog__headerbtn {
    top: 13px;
  }
  .conten-box {
    padding: 15px 20px;
    display: flex;
    .uploadBtnBox {
      display: flex;
      align-items: flex-start;
      flex-wrap: wrap;
      max-width: 400px;
    }
  }
}
</style>
