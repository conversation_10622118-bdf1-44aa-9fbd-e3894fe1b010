<template>
	<div class="process">
		<div v-for="(item, i) in processList" :key="i" class="item">
			<div :class="getCss(item, i)">
				<div style="text-align:center;">
					<i v-if="i < processList.length - 1 || getCss(item, i) == 'success'" style="fontSize:30px;" class="el-icon-circle-check"></i>
					<div class="icon-1" v-if="getCss(item, i) == 'wait'">
						<i class="el-icon-more"></i>
					</div>
					<i v-if="getCss(item, i) == 'warn'" style="fontSize:30px;" class="el-icon-circle-close"></i>
				</div>
				<div style="text-align:center;">
					<span style="fontSize:14px;">{{ item.labelTitle }}</span>
					<br />
					<span style="fontSize:12px;">{{ item.createTime }}</span>
				</div>
			</div>
			<div v-if="i < processList.length - 1" class="success jiantou">---------</div>
		</div>
	</div>
</template>

<script>
export default {
	props: {
		processList: {
			type: Array,
			default: () => []
		}
	},
	methods: {
		getCss(item, i) {
			console.log(item);
			if (i < this.processList.length - 1) {
				return 'success'
			}
			if ([6, 1, 10, 7].includes(item.auditProcessState)) {
				return 'wait'
			}
			if ([2, 4, 8, 9].includes(item.auditProcessState)) {
				return 'warn'
			}
			if ([3, 5].includes(item.auditProcessState)) {
				return 'success'
			}
		}
	}
}
</script>

<style lang="scss" scoped>
.process {
	display: flex;
	color: rgb(100, 100, 100);
	border-color: rgb(100, 100, 100);
	> * {
		flex-grow: 0;
		flex-shrink: 0;
	}
	.item {
		display: flex;
		justify-content: flex-start;
		align-items: center;
		gap: 5px;
	}
	p {
		margin: 0;
		width: max-content;
	}
	.line {
		width: 100px;
		border-top: 1px dashed;
		margin: 0 10px;
	}
	.success {
		color: rgb(0, 126, 199);
		border-color: rgb(0, 126, 199);
	}
	.wait {
		color: rgb(199, 146, 0);
		border-color: rgb(199, 146, 0);
	}
	.warn {
		color: rgb(199, 0, 0);
		border-color: rgb(199, 0, 0);
	}
}
.jiantou::after {
	content:'';
	position: relative;
	display: inline-block;
	width: 0;
	height: 0;
	border-style: solid;
	border-width: 5px 0 5px 7px;
	border-color: transparent transparent transparent rgb(0, 126, 199);
}
.icon-1 {
	position: relative;
	display: inline-block;
	padding: 2px;
	width: 27px;
	height: 27px;
	font-size: 16px;
	border-width: 2px;
	border-style: solid;
	border-radius: 50%;
	i {
		position: absolute;
		top: 50%;
		left: 50%;
		transform: translate(-50%,-50%);
	}
}
</style>
