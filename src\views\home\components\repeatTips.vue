<template>
  <el-dialog
    title="标品信息变更提醒"
    :visible="dialogFormVisible"
    width="600px"
    :before-close="handleClose"
  >
    <p>
      您有{{partHaveMeProductMarkCount}}个销售中&缺货下架的商品与标品信息不一致，请检查商品信息<br />
      <el-button type="text" @click="toChangePage">立即查看</el-button>
    </p>
    <div slot="footer">
      <el-button type="primary" @click="handleClose">我知道了</el-button>
    </div>
  </el-dialog>
</template>

<script>
import {mapMutations} from 'vuex'
import { statistics } from '@/api/product'

export default {
  name: 'RepeatTips',
  props: {
  },
  data() {
    return {
      dialogFormVisible: false,
      partHaveMeProductMarkCount: 0
    }
  },
  async mounted() {
    if(this.$store.state.app.standardProductChangeReminder){
      try{
        let { data: { partHaveMeProductMarkCount } } = await statistics()
        this.partHaveMeProductMarkCount = partHaveMeProductMarkCount || 0;
      }catch{
        this.partHaveMeProductMarkCount = 0
      }
      if(this.partHaveMeProductMarkCount !== 0)
        this.dialogFormVisible = true
    }
  },
  methods: {
    ...mapMutations('app',['SET_STANDARDPRODUCTCHANGEREMINDER']),
    handleClose() {
      this.dialogFormVisible = false
      this.SET_STANDARDPRODUCTCHANGEREMINDER(false)
    },
    toChangePage() {
      this.handleClose()
      const path = '/productInfoChange'
      window.openTab(path)
    }
  }
}
</script>
