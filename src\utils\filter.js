/*
 * 时间格式化
 * n {number} 时间戳
 * format { String } 转换的时间格式
 *   YYYY年MMMM月DDDD日 --- 二零一九年/四月/十五日
 *   yy-mm-dd --- 2019-04-15
 *   yy-mm-dd HH:ss:nn --- 2019-04-15 17:33:45
 *   yy年mm月dd日 周ww --- 2019年4月15日 周一
 *   yy-mm-dd 周ww --- 2019-04-15 周一
 * ex：dataTime(1565779196207, 'YYYY年MMMM月DDDD日')
 * */
const dataTime = function (n, format) {
  // .log(n, format);
  if (!n) {
    return;
  }
  const t = new Date(parseInt(n, 10));
  const tf = function (i) {
    return (i < 10 ? '0' : '') + i;
  };
  const changeNum = ['零', '一', '二', '三', '四', '五', '六', '七', '八', '九'];
  const unitNum = ['', '十', '二十', '三十'];
  const YY = function (strArr) {
    let newNum = '';
    for (let i = 0; i < strArr.length; i++) {
      newNum += i === 0 && strArr[i] === '0' ? '' : changeNum[strArr[i]];
    }
    return newNum;
  };
  const MMDD = function (strArr) {
    return unitNum[strArr[0]] + (changeNum[strArr[2]] || '');
  };
  const ww = function (strArr) {
    const changeNum = ['日', '一', '二', '三', '四', '五', '六'];
    return changeNum[strArr];
  };
  return format.replace(/YYYY|MMMM|DDDD|yy|mm|dd|HH|ss|nn|ww/g, (a) => {
    switch (a) {
      case 'YYYY':
        return YY(String(t.getFullYear()));
      case 'MMMM':
        return MMDD(String((t.getMonth() + 1) / 10));
      case 'DDDD':
        return MMDD(String(t.getDate() / 10));
      case 'yy':
        return tf(t.getFullYear());
      case 'mm':
        return tf(t.getMonth() + 1);
      case 'dd':
        return tf(t.getDate());
      case 'HH':
        return tf(t.getHours());
      case 'ss':
        return tf(t.getMinutes());
      case 'nn':
        return tf(t.getSeconds());
      case 'ww':
        return ww(t.getDay());
    }
  });
};
const formatSec = (s) => {
  if (s !== 0 && !s) return s;
  const day = Math.floor(s / 86400);
  const hour = Math.floor(s / 3600) % 24;
  const min = Math.floor(s / 60) % 60;
  const sec = s % 60;
  const dayText = day ? `${day}天` : '';
  const hourText = hour ? `${hour}小时` : '';
  const minText = min ? `${min}分` : '';
  return `${dayText}${hourText}${minText}${sec}秒`;
};
export default {
  dataTime,
  formatSec,
};
