<template>
  <el-dialog
    title="选择人群"
    :visible.sync="seePeopleBoole"
    class="my-dialog"
    width="980px"
    :close-on-click-modal="false"
  >
    <div class="con-title">
      <span class="line"></span>
      <span>人群列表</span>
    </div>
    <div class="explain-list">
      <div class="explain-search"></div>
      <div class="explain-table" v-loading="isLoadingPeople">
        <el-table
          class="my-table marketing-table"
          :data="peopleData.list"
          stripe
          :header-cell-style="{ background: '#F9F9F9', color: '#666666' }"
          max-height="400px"
          style="width: 100%"
        >
          <el-table-column
            prop="tagName"
            label="人群名称"
            width="180"
          ></el-table-column>
          <el-table-column
            prop="tagDef"
            label="人群定义"
            width="470"
          ></el-table-column>
          <el-table-column label="创建时间" width="290">
            <template slot-scope="scope">
              <span> {{ transferCreatTime(scope.row.createTime) }}</span>
            </template>
          </el-table-column>
          <template slot="empty">
            <div class="noData">
              <p class="img-box">
                <img src="@/assets/image/marketing/noneImg.png" alt />
              </p>
              <p>暂无数据</p>
            </div>
          </template>
        </el-table>
      </div>
      <div class="explain-pag">
        <Pagination
          :total="peopleData.total"
          :page.sync="searchPeopleData.pageNum"
          :limit.sync="searchPeopleData.pageSize"
          @pagination="getPeoPleList"
        ></Pagination>
      </div>
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button @click="seePeopleBoole = false" size="small">取 消</el-button>
      <el-button type="primary" @click="seePeopleBoole = false" size="small"
        >确 定</el-button
      >
    </div>
  </el-dialog>
</template>

<script>
import Pagination from '../../../components/Pagination/index.vue'
import { addActivityCouponList } from '@/api/market/addActivity.js'
export default {
  name: 'selectpepel',
  data() {
    return {
      seePeopleBoole: false,
      prMarketCustomerGroupId: '',
      isLoadingPeople: false,
      searchPeopleData: { pageNum: 1, pageSize: 20 },
      peopleData: { list: [], total: 0 }
    }
  },
  components: {
    Pagination
  },
  methods: {
    open(id) {
      this.seePeopleBoole = true
      this.searchPeopleData.pageNum = 1
      this.searchPeopleData.pageSize = 20
      this.prMarketCustomerGroupId = id
      this.getPeoPleList()
    },
    transferCreatTime(time) {
      return dayjs(time).format('YYYY-MM-DD HH:mm:ss')
    },
    getPeoPleList() {
      this.isLoadingPeople = true
      this.searchPeopleData.id = this.prMarketCustomerGroupId
      let _this = this
      addActivityCouponList(this.searchPeopleData)
        .then((res) => {
          this.isLoadingPeople = false
          if (res.code == 1000) {
            _this.peopleData.total = res.data.totalCount
            _this.searchPeopleData.pageSize = res.data.pageSize
            _this.searchPeopleData.pageNum = res.data.pageNo
            _this.peopleData.list = res.data.list
          } else {
            this.$message({
              message: res.msg,
              type: 'error'
            })
          }
        })
        .catch((error) => {
          this.$message({
            message: '初始列表失败',
            type: 'error'
          })
          this.isLoadingPeople = false
        })
    }
  }
}
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped >
.explain-table {
  padding: 0 20px;
}
.explain-height {
  max-height: 200px;
  overflow: auto;
}
</style>

