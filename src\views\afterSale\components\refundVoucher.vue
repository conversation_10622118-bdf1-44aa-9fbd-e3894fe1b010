<template>
<div>
    <el-dialog 
        title="退款单售后凭证"
        :visible.sync="value"
        width="600px"
        @close="hide"
    >
        <div v-loading="diaLoading" class="img-dialog">
          <div class="contentWrap">
              <div class="contentLine">
                  <span>退款单编号：</span>
                  <span>{{ refundOrderMassage.refundOrderNo }}</span>
              </div>
              <div class="contentLine">
                  <span>售后类型：</span>
                  <span v-if="refundOrderMassage.afterSaleType">
                    {{ refundOrderMassage.afterSaleType == 1 ? '退货退款' : '仅退款' }}
                  </span>
              </div>
              <div class="contentLine">
                  <span>退款原因：</span>
                  <span>{{ refundOrderMassage.refundReason }}</span>
              </div>
              <div class="contentLine">
                  <span>退款说明：</span>
                  <span>{{ refundOrderMassage.refundExplain }}</span>
              </div>
              <div class="contentImage">
                  <div>退款凭证：</div>
                  <div class="imageWarg" style="width:400px;">
                      <i-img
                      v-model="voucherImgs"
                      :deleteBtn="false"
                      :maxShowLength="9"
                      :showNullEl="true"></i-img>
                  </div>
              </div>
              <!-- 特殊原因凭证 -->
              <div v-if="hasSpecialReason" class="contentImage">
                  <div>特殊原因：</div>
                  <div class="imageWarg" style="width:400px;">
                      <i-img
                      v-model="specialReasonImgs"
                      :deleteBtn="false"
                      :maxShowLength="9"
                      :showNullEl="true"></i-img>
                  </div>
              </div>
              <!-- 其他原因凭证（无标签） -->
              <div v-if="!hasSpecialReason && otherReasonImgs.length > 0" class="contentImage">
                  <div class="imageWarg" style="width:400px;">
                      <i-img
                      v-model="otherReasonImgs"
                      :deleteBtn="false"
                      :maxShowLength="9"
                      :showNullEl="true"></i-img>
                  </div>
              </div>
          </div>
          <div slot="footer" style="text-align: right;margin-top: 20px;">
              <el-button
              type="primary"
              size="small"
              @click="toReview"
            >
              去审核
            </el-button>
          </div>
        </div>
    </el-dialog>
</div>
</template>

<script>
import iImg from "./i-img.vue"
import { getAfterSaleVoucher } from '@/api/order/index';
export default {
name:'refundVoucher',
props: {
    value: {
      type: Boolean,
      default: false,
    },
    rowData: {
      type: Object,
      default: () => {},
    },
},
components: {
    iImg
},
watch: {
    rowData: {
      handler(newVal) {
        this.getRefundOrderMassage(newVal.refundOrderNo)
      },
      deep: true,
    },
},
data() {
  return {
    diaLoading: false,
    refundOrderMassage: {
      refundOrderNo: '',
      refundReason: '',
      refundExplain: '',
      imgList: "",
      afterSaleType: "",
      orderRefundExtVo: {
        expressWaybillNumber: "",
        damagedGoods: '',
        expressOuterBoxList: [],
        problemDisplay: '',
        physicalBatchNumber: '',
        physicalPicture: '',
        productImage: ''
      }
    },
    voucherImgs: [],
    specialReasonImgs: [], // 特殊原因凭证
    otherReasonImgs: [] // 其他原因凭证
  }
 },
computed: {
  // 判断是否有特殊原因（orderRefundExtVo中有任何字段有值）
  hasSpecialReason() {
    const extVo = this.refundOrderMassage.orderRefundExtVo || {};
    return !!(
      extVo.expressWaybillNumber ||
      extVo.damagedGoods ||
      (extVo.expressOuterBoxList && extVo.expressOuterBoxList.length > 0) ||
      extVo.problemDisplay ||
      extVo.physicalBatchNumber ||
      extVo.physicalPicture ||
      extVo.productImage
    );
  }
},
methods: {
    hide() {
      this.$emit('input', false)
    },
    toReview() {
        this.$emit('input', false)
        this.$emit('toReview')
    },
    getRefundOrderMassage(refundOrderNo) {
      this.diaLoading = true
      getAfterSaleVoucher({refundOrderNo: refundOrderNo}).then(res => {
        if(res.code === 0) {
          this.refundOrderMassage = res.result || {}
          this.processVoucherData()
        }else {
          this.$message.error(res.msg || "网络异常，请稍后再试")
        }
      }).catch(() => {
        this.$message.error("网络异常，请稍后再试")
      }).finally(() => {
        this.diaLoading = false
      })
    },

    // 处理凭证数据
    processVoucherData() {
      const extVo = this.refundOrderMassage.orderRefundExtVo || {};

      // 处理 orderRefundExtVo 字段数据成 voucherImgs 格式
      const voucherUrls = [];

      // 收集所有图片字段
      if (extVo.physicalPicture) {
        voucherUrls.push(...this.parseImageUrls(extVo.physicalPicture));
      }
      if (extVo.productImage) {
        voucherUrls.push(...this.parseImageUrls(extVo.productImage));
      }
      if (extVo.expressOuterBoxList && Array.isArray(extVo.expressOuterBoxList)) {
        extVo.expressOuterBoxList.forEach(item => {
          if (typeof item === 'string') {
            voucherUrls.push(...this.parseImageUrls(item));
          }
        });
      }

      // 转换为 i-img 组件需要的格式
      this.voucherImgs = voucherUrls.map(url => ({ url }));

      // 处理特殊原因凭证（如果有特殊原因）
      if (this.hasSpecialReason) {
        this.specialReasonImgs = this.parseImgList(this.refundOrderMassage.imgList);
        this.otherReasonImgs = [];
      } else {
        // 如果没有特殊原因，处理其他原因凭证
        this.specialReasonImgs = [];
        this.otherReasonImgs = this.parseImgList(this.refundOrderMassage.imgList);
      }
    },

    // 解析图片URL字符串（可能是逗号分隔的）
    parseImageUrls(imageStr) {
      if (!imageStr) return [];
      return imageStr.split(',').filter(url => url.trim()).map(url => url.trim());
    },

    // 解析 imgList 字符串并转换为 i-img 格式
    parseImgList(imgList) {
      if (!imgList) return [];
      return imgList.split(',')
        .filter(url => url.trim())
        .map(url => ({ url: url.trim() }));
    }
},
}
</script>

<style lang='scss' scoped>
.img-dialog {
  max-height: 500px;
  overflow-y: auto;
}
  ::v-deep  .el-dialog__body {
    padding: 10px 20px;
  }

  ::v-deep  .el-dialog__header {
    padding: 10px 20px;
    background: #f9f9f9;
  }

  ::v-deep  .el-dialog__headerbtn {
    top: 15px;
  }

  ::v-deep  .el-dialog__title {
    font-size: 16px;
  }
  .contentWrap {
    width: 90%;
    margin: 0 auto;
    padding: 0;
    word-break: break-all;
    font-size: 14px;
    color: #606266;
  }
  .contentLine {
    height: 30px;
    line-height: 30px;
  }
  .contentImage {
    display: flex;
  }
</style>