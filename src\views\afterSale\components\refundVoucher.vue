<template>
<div>
    <el-dialog 
        title="退款单售后凭证"
        :visible.sync="value"
        width="600px"
        @close="hide"
    >
        <div v-loading="diaLoading" class="img-dialog">
          <div class="contentWrap">
              <div class="contentLine">
                  <span>退款单编号：</span>
                  <span>{{ refundOrderMassage.refundOrderNo }}</span>
              </div>
              <div class="contentLine">
                  <span>售后类型：</span>
                  <span v-if="refundOrderMassage.afterSaleType">
                    {{ refundOrderMassage.afterSaleType == 1 ? '退货退款' : '仅退款' }}
                  </span>
              </div>
              <div class="contentLine">
                  <span>退款原因：</span>
                  <span>{{ refundOrderMassage.refundReason }}</span>
              </div>
              <div class="contentLine">
                  <span>退款说明：</span>
                  <span>{{ refundOrderMassage.refundExplain }}</span>
              </div>
              <div class="contentImage">
                  <div>退款凭证：</div>
                  <div class="imageWarg" style="width:400px;">
                      <i-img 
                      v-model="voucherImgs" 
                      :deleteBtn="false"
                      :maxShowLength="9"
                      :showNullEl="true"></i-img>
                  </div>
              </div>
          </div>
          <div slot="footer" style="text-align: right;margin-top: 20px;">
              <el-button
              type="primary"
              size="small"
              @click="toReview"
            >
              去审核
            </el-button>
          </div>
        </div>
    </el-dialog>
</div>
</template>

<script>
import iImg from "./i-img.vue"
import { getAfterSaleVoucher } from '@/api/order/index';
export default {
name:'refundVoucher',
props: {
    value: {
      type: Boolean,
      default: false,
    },
    rowData: {
      type: Object,
      default: () => {},
    },
},
components: {
    iImg
},
watch: {
    rowData: {
      handler(newVal) {
        this.getRefundOrderMassage(newVal.refundOrderNo)
      },
      deep: true,
    },
},
data() {
  return {
    diaLoading: false,
    refundOrderMassage: {
      refundOrderNo: '',
      refundReason: '',
      refundReason: '',
      imgList: "",
      afterSaleType: "",
      orderRefundExtVo: {
        expressWaybillNumber: "",
        damagedGoods: '',
        expressOuterBoxList: [],
        problemDisplay: '',
        physicalBatchNumber: '',
        physicalPicture: '',
        productImage: ''
      }
    },
    voucherImgs: [
        {
            url:'https://fuss10.elemecdn.com/e/5d/4a731a90594a4af544c0c25941171jpeg.jpeg'
        },
        {
            url:'https://fuss10.elemecdn.com/8/27/f01c15bb73e1ef3793e64e6b7bbccjpeg.jpeg'
        },
        {
            url:'https://fuss10.elemecdn.com/1/8e/aeffeb4de74e2fde4bd74fc7b4486jpeg.jpeg'
        }
    ]
  }
 },
methods: {
    hide() {
      this.$emit('input', false)
    },
    toReview() {
        this.$emit('input', false)
        this.$emit('toReview')
    },
    getRefundOrderMassage(refundOrderNo) {
      this.diaLoading = true
      getAfterSaleVoucher({refundOrderNo: refundOrderNo}).then(res => {
        if(res.code === 0) {
          this.refundOrderMassage = res.re
        }else {
          this.$message.error(res.msg || "网络异常，请稍后再试")
        }
      }).catch(err => {
        this.$message.error("网络异常，请稍后再试")
      }).finally(() => {
        this.diaLoading = false
      })
    }
},
}
</script>

<style lang='scss' scoped>
.img-dialog {
  max-height: 500px;
  overflow-y: auto;
}
  ::v-deep  .el-dialog__body {
    padding: 10px 20px;
  }

  ::v-deep  .el-dialog__header {
    padding: 10px 20px;
    background: #f9f9f9;
  }

  ::v-deep  .el-dialog__headerbtn {
    top: 15px;
  }

  ::v-deep  .el-dialog__title {
    font-size: 16px;
  }
  .contentWrap {
    width: 90%;
    margin: 0 auto;
    padding: 0;
    word-break: break-all;
    font-size: 14px;
    color: #606266;
  }
  .contentLine {
    height: 30px;
    line-height: 30px;
  }
  .contentImage {
    display: flex;
  }
</style>