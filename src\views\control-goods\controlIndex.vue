<template>
  <div class="main-box">
    <!-- <div class="crumbs"> -->
      <!-- <span class="textColor">商品管理</span> /
      <span class="linkColor">控销管理</span> -->
      <!-- <router-link to="controlSellShop" class="linkColor">控销管理</router-link> -->
    <!-- </div> -->
    <div class="list-box">
      <div v-if="nextTip == 1">
        <controlList @goNext="goNext" @goPrev="goPrev"></controlList>
      </div>
      <div v-show="nextTip == 2" style="padding: 16px">
        <controlBasic
          @goNext="goNext"
          :basicEdit="basicEdit"
          :isEditData="isE"
          @goPrev="goPrev"
        ></controlBasic>
      </div>
      <div v-show="nextTip == 3" style="padding: 16px">
        <controlGoods
          @goNext="goNext"
          :isEditData="isE"
          :productListE="productListE"
          @goPrev="goPrev"
        ></controlGoods>
      </div>
      <div v-show="nextTip == 4" style="padding: 16px">
        <controlRules
          @goNext="goNext"
          @goPrev="goPrev"
          :isEditData="isE"
          :rulesEditList="rulesEditList"
          @sendDataRules="sendRules"
          :provId="
            basicData.provinceCode ? basicData.provinceCode.toString() : ''
          "
        ></controlRules>
      </div>
       <div v-if="nextTip == 5" style="padding: 16px">
        <batchOperation @goNext="goNext" @goPrev="goPrev"></batchOperation>
      </div>
    </div>
    <InvalidTips v-if="showTips" @handleClose="handleClose" />
  </div>
</template>

<script>
import controlList from './modules/controlList'
import controlBasic from './modules/controlBasic'
import controlGoods from './modules/controlGoods'
import controlRules from './modules/controlRules'
import batchOperation from './modules/batchOperation'
import InvalidTips from './components/invalidTips'
// import { addControlselling, uploadControlsel } from '@/api/goods/newGood.js'
import {
  update
} from '@/api/goods/controlGoods.js'
import utils from '@/utils/filter';
import { touchstart } from 'dom7'
export default {
  name: 'controlIndex',
  components: {
    controlList,
    controlBasic,
    controlGoods,
    controlRules,
    batchOperation,
    InvalidTips
  },
  data() {
    return {
      nextTip: 1,
      basicData: {},
      chooseProductList: [],
      dataRequesting: false, // 提交连点阻断

      editAllData: {},
      isE: false,
      //基础信息
      basicEdit: {
        provinceCode: '',
        time: '',
        status: 1,
        controlSaleName: ''
      },

      //商品信息
      productListE: [],

      rulesEditList: [],
      showTips: true,
    }
  },
  activated() {
    this.showTips = true;
  },
  methods: {
    handleClose() {
      this.showTips = false;
    },
    //下一步
    goNext(from) {
      if (from.from == 'addCon') {
        this.nextTip = 2
        if (!from.data) {
          this.editAllData = {}
          this.isE = false
          this.basicEdit = {
            provinceCode: '',
            time: '',
            status: 1,
            controlSaleName: ''
          }
          this.productListE = []
          this.rulesEditList = []
        } else {
          this.editAllData = from.data
          this.isE = true
          // 控销设置第一步
          this.basicEdit.status = this.editAllData.control.status
          this.basicEdit.provinceCode = this.editAllData.control.provinceCode
          this.basicEdit.time = utils.dataTime(this.editAllData.control.startTime,'yy-mm-dd HH:ss:nn');
          this.basicEdit.controlSaleName = this.editAllData.control.controlSaleName
          // 控销设置第二步
          this.productListE.push(this.editAllData.sku);
          // 控销设置第三步
          let ruleList = [];
          let firstObj = {
            buyerList:[],
            jointList:[]
          };
          let threeObj = {
            buyerList:[],
            jointList:[]
          };

          firstObj.controlResult = 3;
          firstObj.id = this.editAllData.id;
          if(this.editAllData.whiteCustData.length){
            firstObj.buyerList = this.editAllData.whiteCustData;
          }
          if(this.editAllData.whitePlan&&this.editAllData.whitePlan.length){
            let data = this.editAllData.whitePlan
            data.forEach((item) => {
              let obj = {
                  areaCode: '',
                  areaCodeName: '',
                  businessType: '',
                  businessTypeName: '',
                  userAry: [],
                  cityAry: []
              }
              const _cityIds = [];
              const _cityStr = [];
              if(item.areaCodes.length){
                  item.areaCodes.forEach((itemChild) => {
                      _cityStr.push(itemChild.areaName)
                      _cityIds.push(itemChild.areaCode)
                      obj.cityAry.push(itemChild.areaCode)
                  })
              }
              obj.areaCodeName = _cityStr.join(',')
              obj.areaCode = _cityIds.join(',')

              const _userIds = [];
              const _userStr = [];
              if(item.userTypes.length){
                  item.userTypes.forEach((itemChild) => {
                      _userStr.push(itemChild.value)
                      _userIds.push(itemChild.key)
                      obj.userAry.push(itemChild.key)
                  })
              }
              obj.businessTypeName = _userStr.join(',')
              obj.businessType = _userIds.join(',')

              firstObj.jointList.push(obj);
            })
          }
          ruleList.push(firstObj);

          threeObj.controlResult = 2;
          threeObj.id = this.editAllData.id;
          if(this.editAllData.hideCustData.length){
            threeObj.buyerList = this.editAllData.hideCustData;
          }
          if(this.editAllData.hidePlan && this.editAllData.hidePlan.length){
            let data = this.editAllData.hidePlan
            data.forEach((item) => {
              let obj = {
                  areaCode: '',
                  areaCodeName: '',
                  businessType: '',
                  businessTypeName: '',
                  userAry: [],
                  cityAry: []
              }
              const _cityIds = [];
              const _cityStr = [];
              if(item.areaCodes.length){
                  item.areaCodes.forEach((itemChild) => {
                      _cityStr.push(itemChild.areaName)
                      _cityIds.push(itemChild.areaCode)
                      obj.cityAry.push(itemChild.areaCode)
                  })
              }
              obj.areaCodeName = _cityStr.join(',')
              obj.areaCode = _cityIds.join(',')

              const _userIds = [];
              const _userStr = [];
              if(item.userTypes.length){
                  item.userTypes.forEach((itemChild) => {
                      _userStr.push(itemChild.value)
                      _userIds.push(itemChild.key)
                      obj.userAry.push(itemChild.key)
                  })
              }
              obj.businessTypeName = _userStr.join(',')
              obj.businessType = _userIds.join(',')

              threeObj.jointList.push(obj);
            })
          }
          ruleList.push(threeObj);
          this.rulesEditList = ruleList
        }
      } else if (from.from == 'basic') {
        this.nextTip = 3
        this.basicData = from.data
      } else if (from.from == 'choosePro') {
        this.nextTip = 4
        this.chooseProductList = from.data
      }
      else if (from.from == 'batchOpe') {
        this.nextTip = 5
      }
    },
    //上一步
    goPrev(from) {
      if (from.from == 'basic') {
        this.nextTip = 1
        //取消清空数据
        this.editAllData = {}
        this.isE = false
        this.basicEdit = {
          provinceCode: '',
          time: '',
          status: 1,
          controlSaleName: ''
        }
        this.productListE = []
        this.rulesEditList = []
      } else if (from.from == 'choosePro') {
        this.nextTip = 2
      } else if (from.from == 'last') {
        this.nextTip = 3
      }
    },
    sendRules(val) {
      if (this.dataRequesting) {
        return false
      }
      this.dataRequesting = true
      //规则数据 val
      let sendData = {}
      // sendData.provinceCode = this.basicData.provinceCode
      sendData.startTime = this.basicData.time // 开始时间
      sendData.status = this.basicData.status // 状态
      sendData.controlSaleName = this.basicData.controlSaleName // 状态
      sendData.controlSaleEntityList = [] // 控销商品
      this.chooseProductList.map(function(item) {
        sendData.controlSaleEntityList.push(item.id)
      })
      sendData.whitePlan=val.whitePlan
      sendData.controlSaleWhiteMerchantEntityList=val.controlSaleWhiteMerchantEntityList
      sendData.hidePlan=val.hidePlan
      sendData.controlSaleHideMerchantEntityList=val.controlSaleHideMerchantEntityList
      // sendData.ruleList = val
      // 是否是编辑
      if (this.isE) {
        sendData.id = this.editAllData.id

        update(sendData).then((res) => {
          this.dataRequesting = false
          if (res.code == 0) {
            this.$message({
              message: '保存成功',
              type: 'success'
            })
           window.refreshPage()
          } else {
            this.$message({
              message: res.message,
              type: 'error'
            })
          }
        })
      } else {
        update(sendData).then((res) => {
          this.dataRequesting = false
          if (res.code == 0) {
            this.$message({
              message: '保存成功',
              type: 'success'
            })
            window.refreshPage()
          } else {
            this.$message({
              message: res.message,
              type: 'error'
            })
          }
        })
      }
    }
  }
}
</script>

<style lang="scss">
 .el-popover{
    position: absolute;
    background: #FFFFFF;
    min-width: 150px;
    border-radius: 4px;
    border: 1px solid #EBEEF5;
    padding: 5px;
    z-index: 2000;
    color: #606266;
    line-height: 1.4;
    text-align: justify;
    font-size: 14px;
    // box-shadow: 0 2px 12px 0 rgb(0 0 0 / 10%);
    word-break: break-all;
  }

    .delete-dialog-customClass{
      position: relative;
      top: -30%;
      left: 25%;
      transform: translate(-60%, 30%);

      .el-message-box__message {
        padding-bottom: 30px;
        color: #333333;
        font-size: 14px;
        max-height: 400px;
        overflow-y: auto;
        word-break: break-all;
      }

    }
 .searchMy ::v-deep  .el-form-item__label {
   margin-left: 20px;
   padding: 0;
 }
 .searchMy ::v-deep  .el-input__inner {
   border-radius: 0 4px 4px 0;
 }
 .searchMy ::v-deep  .el-date-editor{
   width: 100%;
 }
 .search-title {
   display: table-cell;
   padding: 0 10px;
   text-align: center;
   border: 1px solid #dcdfe6;
   height: 30px;
   line-height: 30px;
   vertical-align: middle;
   border-right: none;
   border-radius: 4px 0 0 4px;
   color: #333333;
   white-space: nowrap;
 }
 .searchMy ::v-deep  .el-select {
   display: table-cell;
   width: 100%;
 }
 .searchMy ::v-deep  .el-form-item__content{
   width: 100%;
 }
 .searchMy ::v-deep  .el-form-item--small.el-form-item{
   margin-bottom: 10px;
   width: 32%;
 }
 .searchMy ::v-deep  .el-form-item--small .el-form-item__content{
   line-height: 30px;
   width: 100%;
 }
 .searchMy ::v-deep  .el-input-group__prepend {
   background: none;
   color: #333333;
   padding: 0 10px;
 }
 .Fsearch {
   padding: 0px 20px 10px;
   font-weight: 500;
   .searchMsg {
     font-weight: 700;
     width: 200px;
   }
   .sign {
     display: inline-table;
     width: 3px;
     height: 13px;
     background: linear-gradient(360deg, #1d69c4 1%, #8bbdfc 99%);
     border-radius: 2px;
     margin-right: 8px;
   }
 }
</style>

<style lang="scss" scoped>
@import '../../assets/css/market';
</style>
