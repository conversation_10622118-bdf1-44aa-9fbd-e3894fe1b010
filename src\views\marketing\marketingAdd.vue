<template>
  <div class="created-box">
    <div class="Fsearch">
      <el-row
          type="flex"
          align="middle"
          justify="space-between"
          class="my-row"
      >
        <el-row
            type="flex"
            align="middle"
        >
          <span class="sign" />
          <div class="searchMsg">
            基本信息
          </div>
        </el-row>
        <el-button type="primary" size="small" @click="goBack">
          返回
        </el-button>
      </el-row>
    </div>
    <div v-if="isShow" class="list-box" style="width: 974px">
      <el-form
        size="small"
        :model="infoData"
        label-width="120px"
        :rules="rules"
        ref="createdForm"
      >
        <div class="created-basic">
          <el-form-item label="优惠券名称" prop="couponBasicTemplate.name">
            <el-input
              v-model.trim="infoData.couponBasicTemplate.name"
              placeholder="最多可填10个字"
              style="width: 380px"
              maxlength="10"
            ></el-input>
            <p class="waring-tip">
              提示：券名称填写后，会显示到券面上(见右侧示例图), 请慎重填写
            </p>
          </el-form-item>
          <!-- <el-form-item label="券类型">
            <el-select
              v-model.trim="infoData.couponBasicTemplate.type"
              style="width: 380px"
              disabled="disabled"
            >
              <el-option value="1" label="满减券"></el-option>
            </el-select>
          </el-form-item> -->
          <el-form-item label="适用范围" prop="shopActivity.scopeType">
            <div style="width: 380px">
              <el-radio
                v-model.trim="infoData.shopActivity.scopeType"
                label="0"
                @change="clearProduct"
                >全店商品</el-radio
              ><br />
              <el-radio
                v-model.trim="infoData.shopActivity.scopeType"
                label="1"
                @change="clearProduct"
                >指定商品</el-radio
              ><br />
              <el-radio
                v-model.trim="infoData.shopActivity.scopeType"
                label="2"
                @change="clearProduct"
              >指定商品不参与</el-radio
              >
              <div
                v-show="Number(infoData.shopActivity.scopeType) === 1 || Number(infoData.shopActivity.scopeType) === 2"
                style="display: inline-block; vertical-align: middle"
              >
                <div
                  style="
                    display: flex;
                    flex-direction: row;
                    align-items: center;
                  "
                >
                  <el-button
                    type="primary"
                    plain
                    size="small"
                    @click="changeGood"
                    >选择商品</el-button
                  >
                  <el-upload
                    ref="upload"
                    :show-file-list="false"
                    :on-success="uploadSucces"
                    :action="uploadFunction()"
                    accept=".xls, .xlsx"
                    style="margin-left: 10px; margin-top: -1px"
                  >
                    <el-button
                      type="primary"
                      plain
                      size="small"
                      @click="
                        () => {
                          showBatchPop = true
                        }
                      "
                      >批量导入</el-button
                    >
                  </el-upload>
                  <el-button
                    style="margin-left: 10px"
                    type="primary"
                    plain
                    size="small"
                    @click="downLoadExcelMode()"
                    >下载模板</el-button
                  >
                  <p
                    v-if="multipleSelectionAll.length > 0"
                    class="lookBtn"
                    @click="lookGood"
                  >
                    已添加{{ multipleSelectionAll.length }}个商品
                  </p>
                </div>
              </div>
            </div>
          </el-form-item>
          <el-form-item
            label="有效期类型"
            prop="couponBasicTemplate.validityType"
            style="display: block"
          >
            <el-radio
              v-model.trim="infoData.couponBasicTemplate.validityType"
              :label="2"
              @change="onChangeValidityType"
              >有效期（时间段）</el-radio
            ><br />
            <el-radio
              v-model.trim="infoData.couponBasicTemplate.validityType"
              :label="1"
              @change="onChangeValidityType"
              >有效期（天）</el-radio
            >
          </el-form-item>
          <el-form-item
            v-if="infoData.couponBasicTemplate.validityType === 2"
            label="使用时间"
            prop="time"
          >
            <el-date-picker
              ref="time"
              style="width: 380px"
              v-model.trim="infoData.time"
              type="datetimerange"
              format="yyyy-MM-dd HH:mm:ss"
              value-format="timestamp"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              :default-time="['00:00:00', '23:59:59']"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item
            v-if="infoData.couponBasicTemplate.validityType === 1"
            label="自优惠券进入用户账户之日起"
            prop="couponBasicTemplate.validDays"
            :class="'onItem'"
          >
            <el-input
              ref="validDay"
              v-model.trim="infoData.couponBasicTemplate.validDays"
              placeholder="请输入天数"
              style="width: 380px"
            ></el-input>
            天内有效
          </el-form-item>
        </div>
        <div class="created-money" style="margin-top: 60px">
          <div class="con-title">
            <span class="line"></span><span>金额信息</span>
          </div>
          <el-form-item label="优惠方式" prop="couponBasicTemplate.type">
            <el-radio v-model="infoData.couponBasicTemplate.type" label="1" @change="templateTypeChange">满减券</el-radio>
            <el-radio v-model="infoData.couponBasicTemplate.type" label="2" @change="templateTypeChange">满折券</el-radio>
         </el-form-item>
<!--          <el-form-item-->
<!--            label="优惠金额"-->
<!--            prop="couponBasicTemplate.moneyInVoucher"-->
<!--          >-->
<!--            <el-input-->
<!--              ref="moneyInVoucher"-->
<!--              @input="setDis"-->
<!--              v-model.trim="infoData.couponBasicTemplate.moneyInVoucher"-->
<!--              placeholder="(正整数)元"-->
<!--              maxlength="5"-->
<!--              style="width: 380px"-->
<!--            ></el-input>-->
<!--          </el-form-item>-->
          <el-form-item label="使用门槛" required>
            <div v-if="Number(infoData.couponBasicTemplate.type)===1">
              <el-radio v-model.trim="infoData.couponBasicTemplate.reduceType" label="0" @change="reduceTypeChange" style="margin-right: 5px">
                <span style="padding-left: 5px">满
                <el-form-item style="display: inline-block" prop="couponBasicTemplate.minMoneyToEnable1" :rules="String(infoData.couponBasicTemplate.reduceType)==='0'?minMoneyToEnableRules:[{required:false}]">
                  <el-input
                    onkeyup="value=value.replace(/[^\d]/g,'')"
                    style="width: 120px"
                    placeholder="0及以上正整数"
                    ref="minMoneyToEnable"
                    v-model.trim="infoData.couponBasicTemplate.minMoneyToEnable1"
                  >
                  </el-input>
                </el-form-item>
                  减
                <el-form-item label="" style="display: inline-block" prop="couponBasicTemplate.moneyInVoucher1" :rules="String(infoData.couponBasicTemplate.reduceType)==='0'?moneyInVoucherRules:[{required:false}]">
                   <el-input
                     ref="moneyInVoucher"
                     @input="setDis"
                     v-model.trim="infoData.couponBasicTemplate.moneyInVoucher1"
                     placeholder="(正整数)元"
                     maxlength="5"
                     style="width: 120px"
                     onkeyup="value=value.replace(/[^\d]/g,'')"
                   ></el-input>
                </el-form-item>
                元
              </span>
              </el-radio>
              <br />
              <el-radio
                v-model.trim="infoData.couponBasicTemplate.reduceType"
                label="1"
                @change="reduceTypeChange"
                >
                 <span class="p-text" style="padding: 8px 0 0 0px">
                每满
                <el-form-item style="display: inline-block" prop="couponBasicTemplate.minMoneyToEnable2" :rules="Number(infoData.couponBasicTemplate.reduceType)===1?minMoneyToEnableRules:[{required:false}]">
                  <el-input
                    ref="minMoneyToEnable"
                    style="width: 80px"
                    placeholder="正整数"
                    v-model.trim="infoData.couponBasicTemplate.minMoneyToEnable2"
                    onkeyup="value=value.replace(/[^\d]/g,'')"
                  >
                  </el-input>
                </el-form-item>
                   减
                <el-form-item label="" style="display: inline-block" prop="couponBasicTemplate.moneyInVoucher2" :rules="Number(infoData.couponBasicTemplate.reduceType)===1?moneyInVoucherRules:[{required:false}]">
                   <el-input
                     ref="moneyInVoucher"
                     @input="setDis"
                     v-model.trim="infoData.couponBasicTemplate.moneyInVoucher2"
                     placeholder="(正整数)元"
                     maxlength="5"
                     style="width: 120px"
                     onkeyup="value=value.replace(/[^\d]/g,'')"
                   ></el-input>
                </el-form-item>
                元，最高减
                <el-form-item style="display: inline-block" prop="couponBasicTemplate.discount1" :rules="Number(infoData.couponBasicTemplate.reduceType)===1?maxDiscountRules:[{required:false}]">
                  <el-input
                    :disNum="disVal"
                    onchange="
                    let _this = this;
                    if(Number(value) <= Number(_this.getAttribute('disNum'))){
                        value='';
                    }"
                    style="width: 120px"
                    placeholder="正整数"
                    ref="discount"
                    v-model.trim="infoData.couponBasicTemplate.discount1"
                    onkeyup="value=value.replace(/[^\d]/g,'')"
                  >
                  </el-input>
                </el-form-item>
                元
              </span>
              </el-radio>
            </div>
            <div v-else>
              <el-radio v-model="discountType" :label="0" style="margin-right: 5px">
                <span style="padding-left: 5px"> 满
                <el-form-item style="display: inline-block" prop="couponBasicTemplate.minMoneyToEnable3" :rules="minMoneyToEnableRules">
                  <el-input
                    onkeyup="value=value.replace(/[^\d]/g,'')"
                    style="width: 120px"
                    placeholder="0及以上正整数"
                    ref="minMoneyToEnable"
                    v-model.trim="infoData.couponBasicTemplate.minMoneyToEnable3"
                  >
                  </el-input>
                  元，打
                </el-form-item>
                <el-form-item label="" style="display: inline-block" prop="couponBasicTemplate.discount2" :rules="maxDiscount2Rules">
                   <el-input
                     ref="moneyInVoucher"
                     @input="setDis"
                     v-model.trim="infoData.couponBasicTemplate.discount2"
                     placeholder="正数"
                     maxlength="5"
                     style="width: 120px"
                     onkeyup="value=value.replace(/^\D*(\d*(?:\.\d{0,2})?).*$/g,'$1')"
                   ></el-input>
                </el-form-item>
                折，最高减
                <el-form-item label="" style="display: inline-block" prop="couponBasicTemplate.maxMoneyInVoucher">
                  <el-input
                    ref="moneyInVoucher"
                    v-model.trim="infoData.couponBasicTemplate.maxMoneyInVoucher"
                    placeholder="正整数"
                    onchange="
                    let _this = this;
                    if(Number(value) <= Number(_this.getAttribute('disNum'))){
                        value='';
                    }"
                    maxlength="5"
                    style="width: 120px"
                    onkeyup="value=value.replace(/[^\d]/g,'')"
                    @input="setDis"
                  />
                </el-form-item> 元
              </span>
              </el-radio>
            </div>
          </el-form-item>
          <!-- <el-form-item label="发行量">
            <label>限</label>
            <el-input
              v-model.trim="infoData.couponStock.totalCount"
              ref="totalCount"
              oninput="if(value.length>6)value=value.slice(0,6)"
              placeholder="正整数"
              style="width: 100px; margin: 0 10px"
            ></el-input
            ><label>张</label>
          </el-form-item> -->
          <el-form-item>
            <div class="">
              <el-button type="primary" @click="onSubmit('createdForm')">{{
                this.$route.query.form == 'add' ||
                this.$route.query.form == 'copy'
                  ? '提交'
                  : '修改提交'
              }}</el-button>
              <el-button @click="goBack">取消</el-button>
            </div>
          </el-form-item>
        </div>
      </el-form>
      <div class="sample-box" style="margin-top: -20px">
        <h4 style="margin-left: -20px">示例图：</h4>
        <p>
          <img src="../../assets/image/marketing/sampleCard.png" />
        </p>
      </div>
    </div>
    <el-dialog
      title="选择商品"
      :close-on-click-modal="false"
      :visible.sync="addGoodVisible"
      @closed="closedAddGood"
      class="my-dialog"
      width="90%"
    >
      <div class="con-title">
        <span class="line"></span><span>商品列表</span>
      </div>
      <div class="explain-search my-label">
        <el-form size="small" :inline="true">
          <el-form-item class="my-label">
            <el-input
              placeholder="请输入"
              v-model.trim="selectGoodSearch.barcode"
              class="search-input"
              style="width: 227px"
            >
              <div slot="prepend">商品编码</div>
            </el-input>
          </el-form-item>
          <el-form-item class="my-label">
            <el-input
              placeholder="请输入"
              v-model.trim="selectGoodSearch.showName"
              class="search-input"
              style="width: 227px"
            >
              <div slot="prepend">商品名称</div>
            </el-input>
          </el-form-item>
          <el-form-item>
            <span slot="label">商品状态</span>
            <el-select
              v-model.trim="selectGoodSearch.status"
              placeholder="请选择"
              style="width: 150px"
            >
              <el-option key="1" label="销售中" value="1"> </el-option>
              <el-option key="2" label="已售罄" value="2"> </el-option>
              <el-option key="3" label="特惠中" value="3"> </el-option>
              <el-option key="4" label="下架" value="4"> </el-option>
              <el-option key="6" label="待上架" value="6"> </el-option>
            </el-select>
          </el-form-item>
          <el-form-item class="search-btn" style="float: right">
            <el-button type="primary" @click="onSearchGood('search')"
              >查询</el-button
            >
            <el-button @click="resetChange">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
      <div class="explain-table" v-loading="isLoading">
        <el-table
          class="my-table good-table"
          :data="selectGoodList.list ? selectGoodList.list : []"
          stripe
          border
          ref="tableChange"
          :row-class-name="tableRowClass"
          :header-cell-style="{ background: '#F9F9F9', color: '#666666' }"
          style="width: 100%"
          max-height="400px"
          @selection-change="goodSelectionChange"
        >
          <el-table-column align="center" fixed type="selection" width="55">
          </el-table-column>
          <el-table-column
            align="center"
            property="barcode"
            label="商品编码"
          ></el-table-column>
          <el-table-column
            align="center"
            property="showName"
            label="商品名称"
          ></el-table-column>
          <el-table-column
            align="center"
            property="fob"
            label="售价"
            width="120"
          ></el-table-column>
          <el-table-column align="center" label="状态" width="120">
            <template slot-scope="scope">
              <span v-if="scope.row.status == 1">销售中</span>
              <span v-else-if="scope.row.status == 2">已售罄</span>
              <span v-else-if="scope.row.status == 3">特惠中</span>
              <span v-else-if="scope.row.status == 4">下架</span>
              <span v-else-if="scope.row.status == 5">秒杀</span>
              <span v-else-if="scope.row.status == 6">待上架</span>
              <span v-else-if="scope.row.status == 7">已录入</span>
              <span v-else-if="scope.row.status == 8">待审核</span>
              <span v-else-if="scope.row.status == 9">审核未通过</span>
              <span v-else-if="scope.row.status == 20">已刪除</span>
              <span v-else>其他</span>
            </template>
          </el-table-column>
        </el-table>
        <div class="explain-pag">
          <Pagination
            v-show="selectGoodList.total ? selectGoodList.total : 0 > 0"
            :total="selectGoodList.total ? selectGoodList.total : 0"
            :page.sync="selectGoodpage.pageNum"
            :limit.sync="selectGoodpage.pageSize"
            @pagination="onSearchGood"
          ></Pagination>
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="addGoodVisibleCancel" size="small">取 消</el-button>
        <el-button type="primary" @click="changeGoodList" size="small"
          >确 定</el-button
        >
      </div>
    </el-dialog>
    <el-dialog
      title="查看已选商品"
      :close-on-click-modal="false"
      :visible.sync="lookGoodVisible"
      class="my-dialog"
      width="90%"
    >
      <div class="con-title">
        <span class="line"></span><span>商品列表</span>
      </div>
      <div class="explain-search my-label">
        <el-form size="small" :inline="true">
          <el-form-item class="my-label">
            <el-input
              placeholder="请输入"
              v-model.trim="removeGoodSearch.barcode"
              class="search-input"
              style="width: 227px"
            >
              <div slot="prepend">商品编码</div>
            </el-input>
          </el-form-item>
          <el-form-item class="my-label">
            <el-input
              placeholder="请输入"
              v-model.trim="removeGoodSearch.showName"
              class="search-input"
              style="width: 227px"
            >
              <div slot="prepend">商品名称</div>
            </el-input>
          </el-form-item>
          <el-form-item class="search-btn" style="float: right">
            <el-button type="primary" @click="onLookGood">查询</el-button>
            <el-button @click="resetLook">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
      <div class="explain-table">
        <el-table
          class="my-table"
          :data="removeListData"
          stripe
          border
          ref="tableRemove"
          :row-class-name="tableRowClass"
          :header-cell-style="{ background: '#F9F9F9', color: '#666666' }"
          style="width: 100%"
          :key="Math.random()"
          max-height="400px"
          @selection-change="removeGoodVal"
        >
          <el-table-column
            align="center"
            property="barcode"
            label="商品编码"
          ></el-table-column>
          <el-table-column
            align="center"
            property="showName"
            label="商品名称"
          ></el-table-column>
          <el-table-column
            align="center"
            property="fob"
            label="售价"
            width="140"
          ></el-table-column>
          <el-table-column label="状态" width="80">
            <template slot-scope="scope">
              <span>{{ selectStatusTransfer(scope.row.status) }}</span>
            </template>
          </el-table-column>
        </el-table>
        <div class="explain-pag">
          <Pagination
            v-show="checkedAllGood.length > 0"
            :total="checkedAllGood.length"
            :page.sync="removePage.pageNum"
            :limit.sync="removePage.pageSize"
            @pagination="onPageRemove"
          ></Pagination>
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="lookGoodVisible = false" size="small"
          >取 消</el-button
        >
        <el-button type="primary" @click="quRemoveList" size="small"
          >确 定</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script>
import Pagination from '../../components/Pagination/index.vue'
import {
  productName,
  couponDetail,
  couponSave,
  couponPublish
} from '@/api/coupon/index'

export default {
  name: 'addCoupon',
  components: {
    Pagination
  },
  data() {
    // eslint-disable-next-line no-underscore-dangle
    const _this = this
    return {
      disVal: 0,
      typeStr: '满减券',
      infoData: {
        couponBasicTemplate: {
          name: '',
          discount: '',
          minMoneyToEnable: '',
          moneyInVoucher: '',
          reduceType: '0',
          type: '1',
          description: '全店可用',
          validityType: 1,
          validDays: '',
          minMoneyToEnable1:'',
          moneyInVoucher1:'',
          minMoneyToEnable2:'',
          moneyInVoucher2:'',
          discount1:'',
          minMoneyToEnable3:'',
          discount2:'',
          maxMoneyInVoucher: '',
        },
        couponStock: {
          totalCount: ''
        },
        shopActivity: {
          scopeType: '1'
        },
        time: []
      },
      rules: {
        'couponBasicTemplate.scopeType': [
          {
            required: true
          }
        ],
        'couponBasicTemplate.validDays': [
          { required: true, message: '请输入大于0的正整数' },
          {
            validator(rule, value, callback) {
              if (Number.isInteger(Number(value)) && Number(value) > 0) {
                callback()
              } else {
                callback(new Error('请输入大于0的正整数'))
              }
            },
            trigger: 'blur'
          }
        ],
        'couponBasicTemplate.validityType': [
          {
            required: true
          }
        ],
        'shopActivity.scopeType': [
          {
            required: true,
            message: '请选择适用范围'
          }
        ],
        time: [
          {
            required: true,
            message: '请选择使用时间'
          },
          {
            validator(rule, value, callback) {
              console.log('rule, value:', rule, value)
              if (value[0] != value[1]) {
                callback()
              } else {
                callback(new Error('开始结束时间不能相同！'))
              }
            },
            trigger: 'blur'
          }
        ],
        'couponBasicTemplate.name': [
          {
            required: true,
            message: '请填写优惠券名称'
          }
        ],
        'couponBasicTemplate.type': [
          {
            required: true,
            message: '请选择优惠方式'
          }
        ]
      },
      selectGoodList: { list: [] },
      selectGoodSearch: {
        shopCode: '',
        code: '',
        barcode: '',
        showName: '',
        status: ''
      },
      selectGoodpage: {
        pageNum: 1,
        pageSize: 10,
        total: null
      },
      multipleSelectionAll: [],
      multipleSelection: [],
      addGoodVisible: false,
      lookGoodVisible: false,
      removeListData: [],
      removeGoodData: [],
      removePage: {
        pageNum: 1,
        pageSize: 10
      },
      removeGoodSearch: {
        barcode: '',
        showName: ''
      },
      removeIds: [],
      isLoading: false,
      isNext: false,
      removePageTotal: 0,
      checkedAllGood: [],
      discountType: 0,
      minMoneyToEnableRules : [
        {
          required: true,
          message: '请填写使用门槛',
          trigger: 'blur'
        },
        {
          validator(rule, value, callback) {
            if (_this.infoData.couponBasicTemplate.reduceType == 1) {
              if (Number.isInteger(Number(value)) && Number(value) > 0) {
                callback()
              } else {
                callback(new Error('请输入大于0的正整数'))
              }
            } else if (
              Number.isInteger(Number(value)) &&
              Number(value) >= 0
            ) {
              callback()
            } else {
              callback(new Error('请输入0及以上正整数'))
            }
          },
          trigger: 'blur'
        }
      ],
      moneyInVoucherRules: [
        {
          required: true,
          message: '请填写优惠金额',
          trigger: 'blur'
        },
        {
          validator(rule, value, callback) {
            if (Number.isInteger(Number(value)) && Number(value) > 0) {
              callback()
            } else {
              callback(new Error('请输入大于0的正整数'))
            }
          },
          trigger: 'blur'
        }
      ],
      maxDiscountRules : [
        {
          required: true,
          message: '请填写优惠限额',
          trigger: 'blur'
        },
        {
          validator(rule, value, callback) {
            if (Number.isInteger(Number(value)) && Number(value) > 0) {
              if (
                Number(value) <=
                Number(_this.infoData.couponBasicTemplate.moneyInVoucher)
              ) {
                callback('需大于优惠金额')
              } else {
                callback()
              }
            } else {
              callback(new Error('请输入大于0的正整数'))
            }
          }
        }
      ],
      maxDiscount2Rules : [
        {
          required: true,
          message: '请填写优惠限额',
          trigger: 'blur'
        },
        {
          validator(rule, value, callback) {
            if ( Number(value) > 0) {
              callback();
            } else {
              callback(new Error('请输入大于0的正整数'));
            }
          }
        }
      ],
      isShow: false
    }
  },
  created() {},
  async activated() {
    await this.getdetail();
  },
  mounted() {
    this.getdetail()
  },
  methods: {
    selectStatusTransfer(status) {
      if (status === 1) {
        return '销售中'
      } else if (status === 2) {
        return '已售罄'
      } else if (status === 4) {
        return '下架'
      } else if (status === 6) {
        return '待上架'
      } else {
        return ''
      }
    },
    uploadSucces(res) {
      if (res.code === 1000) {
        this.multipleSelectionAll = res.data.shopSkuVos;
        this.removeListData = res.data.shopSkuVos;
        let con = '';
        const importResult = res.data.importResult || {};
        if (importResult.url) {
          con = `<p>导入成功${importResult.successNum}条商品，失败${importResult.failNum}条，失败原因请下载错误文件：<br><a style="color: #4184D5" href="${importResult.url}" download="下载错误文件">下载错误文件</a></p>`;
        } else {
          con = `<p>导入成功${importResult.successNum}条，失败${importResult.failNum}条</p>`;
        }
        this.$confirm(con, '提示', {
          confirmButtonText: '确定',
          dangerouslyUseHTMLString: true,
          showCancelButton: false,
        }).then(() => {});
      } else {
        this.$message({
          message: res.msg,
          type: 'error',
        });
      }
      this.$refs.upload.clearFiles();
    },
    uploadFunction() {
      let baseURL = ''
      switch (__env__) {
        case 'devlpoment':
          baseURL = '/api'
          // baseURL = 'http://192.168.128.34:8091'
          break
        case 'dev':
          baseURL = 'https://pop.dev.ybm100.com'
          break
        case 'test':
          baseURL = 'https://pop.test.ybm100.com'
          break
        case 'stage':
          baseURL = 'https://pop-new.stage.ybm100.com'
          break
        case 'prod':
          baseURL = 'https://pop.ybm100.com'
          break
      }
      return baseURL + '/promo/coupon/importProduct'
    },
    getdetail() {
      this.$nextTick(()=>{
        this.isShow = true
        // this.$refs.createdForm.clearValidate();
      })
      if (
        this.$route.query.form == 'edit' ||
        this.$route.query.form == 'copy'
      ) {
        const { id } = this.$route.query
        let that = this
        couponDetail({
          id
        })
          .then((res) => {
            if (res.status === 'success') {
              const { detail } = res.data
              that.infoData.couponBasicTemplate.name = detail.name
              that.infoData.couponBasicTemplate.type = String(detail.type)
              that.infoData.couponBasicTemplate.validityType =
                detail.validityType
              that.infoData.couponBasicTemplate.validDays = detail.validityDays.toString()
              that.infoData.time = []
              that.infoData.time[0] = detail.startTime
              that.infoData.time[1] = detail.endTime
              that.infoData.shopActivity.scopeType = String(detail.scopeType)
              // that.infoData.couponBasicTemplate.moneyInVoucher = String(
              //   detail.moneyInVoucher
              // )
              // that.infoData.couponBasicTemplate.minMoneyToEnable = String(
              //   detail.minMoneyToEnable
              // )
              that.infoData.couponBasicTemplate.reduceType = String(
                detail.reduceType
              )
              if (Number(detail.type) === 1) {
                //满减券
                if (Number(detail.reduceType) === 1) {
                  //每满
                  that.infoData.couponBasicTemplate.minMoneyToEnable2 = detail.minMoneyToEnable;
                  that.infoData.couponBasicTemplate.moneyInVoucher2 = detail.moneyInVoucher;
                  that.infoData.couponBasicTemplate.discount1 = detail.discount;
                } else {
                  that.infoData.couponBasicTemplate.minMoneyToEnable1 = detail.minMoneyToEnable;
                  that.infoData.couponBasicTemplate.moneyInVoucher1 = detail.moneyInVoucher;
                }
              } else {
                //满折券
                that.infoData.couponBasicTemplate.minMoneyToEnable3 = detail.minMoneyToEnable;
                that.infoData.couponBasicTemplate.discount2 = detail.discount;
                that.infoData.couponBasicTemplate.maxMoneyInVoucher = detail.maxMoneyInVoucher;
              }

              // that.infoData.couponBasicTemplate.discount = detail.discount
              //   ? detail.discount
              //   : null

              that.infoData.couponStock.totalCount = String(detail.totalCount)
              if (
                (Number(that.infoData.shopActivity.scopeType) === 1||Number(that.infoData.shopActivity.scopeType) === 2) &&
                detail.csuIds &&
                detail.csuIds.length > 0
              ) {
                const param = {
                  templateId: that.$route.query.id,
                  pageNum: 1,
                  pageSize: 10,
                  productName: '',
                  barcode: ''
                }
                productName(param)
                  .then((res) => {
                    if (res.status === 'success') {
                      that.multipleSelectionAll = res.data.list
                    } else {
                      that.$message({
                        message: res.msg,
                        type: 'error'
                      })
                    }
                  })
                  .catch((error) => {
                    console.log(error)
                  })
              }
            } else {
              that.$message({
                message: res.msg,
                type: 'error'
              })
            }
          })
          .catch((error) => {
            console.log(error)
          })
      }
    },
    goBack() {
      this.isShow = false
      if(this.$store.state.permission.menuGray == 1) {
        this.$router.replace({
          path: '/storeVoucher',
          query: {to: 'couponList'}
        })
        return
      }
      window.history.go(-1)  
    },
    downLoadExcelMode() {
      window.open(
        '/promo/coupon/downloadImportSkuTemplate'
      )
    },
    onChangeValidityType() {
      if (this.infoData.couponBasicTemplate.validityType === 2) {
        this.infoData.couponBasicTemplate.validDays = 1
        this.infoData.time = []
      }
      if (this.infoData.couponBasicTemplate.validityType === 1) {
        this.infoData.couponBasicTemplate.validDays = ''
        this.infoData.time = ['~', '~']
      }
    },
    /* 提交信息 */
    onSubmit(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          const csuIds = []
          this.multipleSelectionAll.forEach((item) => {
            if (item.id) {
              csuIds.push(Number(item.id))
            }
          })
          const sendData = JSON.parse(JSON.stringify(this.infoData))
          const param = {
            id:
              this.$route.query.form == 'add' ||
              this.$route.query.form == 'copy'
                ? null
                : this.$route.query.id, //ID，新建不传，修改传
            name: sendData.couponBasicTemplate.name, //优惠券名称
            startTime: sendData.time[0] === '~' ? undefined : sendData.time[0], //优惠券开始时间
            endTime: sendData.time[1] === '~' ? undefined : sendData.time[1], //
            type: Number(sendData.couponBasicTemplate.type),
            minMoneyToEnable: sendData.couponBasicTemplate.minMoneyToEnable1 || sendData.couponBasicTemplate.minMoneyToEnable2 || sendData.couponBasicTemplate.minMoneyToEnable3, //起用券最低消费金额
            moneyInVoucher: sendData.couponBasicTemplate.moneyInVoucher1 || sendData.couponBasicTemplate.moneyInVoucher2, //券包含金额
            discount: sendData.couponBasicTemplate.discount1 || sendData.couponBasicTemplate.discount2,
            maxMoneyInVoucher:  sendData.couponBasicTemplate.maxMoneyInVoucher,
            reduceType: Number(sendData.couponBasicTemplate.reduceType), //是否每满减，0满，1每满
            scopeType: Number(sendData.shopActivity.scopeType), //是否是全场，0全场，1指定
            validityType: this.infoData.couponBasicTemplate.validityType,
            validityDays: this.infoData.couponBasicTemplate.validDays,
            csuIds:
              Number(sendData.shopActivity.scopeType) === 0 ? null : csuIds //关联的商品id全场商品时，此字段不填。指定时，必填。
            // totalCount: Number(sendData.couponStock.totalCount), //总库存
          }
          couponSave(param).then((res) => {
            if (res.status == 'success') {
              if (
                this.$route.query.form == 'add' ||
                this.$route.query.form == 'copy'
              ) {
                const { templateId } = res.data
                this.cancelConfirm(templateId)
              } else {
                this.$message({
                  message: '修改成功',
                  type: 'success'
                })
                if(this.$store.state.permission.menuGray == 1) {
                  this.$router.replace({
                    path: '/storeVoucher',
                    query: {refresh: true,to: 'couponList'}
                  })
                }else {
                  this.$router.replace({
                    path: '/couponList',
                    query: {refresh: true}
                  })
                }
              }
            } else {
              this.$message({
                message: res.msg,
                type: 'error'
              })
            }
          })
        } else {
          if (this.infoData.time.length <= 0) {
            if (this.$refs.time) {
              this.$refs.time.$el.childNodes[1].focus();
            }
          } else if (!this.infoData.couponBasicTemplate.moneyInVoucher) {
            this.$refs.moneyInVoucher.$el.firstElementChild.focus()
          } else if (!this.infoData.couponBasicTemplate.minMoneyToEnable) {
            this.$refs.minMoneyToEnable.$el.firstElementChild.focus()
          } else if (
            !this.infoData.couponBasicTemplate.discount &&
            this.infoData.shopActivity.scopeType == 1
          ) {
            this.$refs.discount.$el.firstElementChild.focus()
          } else if (!this.infoData.couponStock.totalCount) {
            this.$refs.totalCount.$el.firstElementChild.focus()
          }
        }
      })
    },
    cancelConfirm(param) {
      this.$confirm('优惠券已提交成功，发布优惠券后即可创建活动', '温馨提示', {
        confirmButtonText: '发布并返回列表',
        cancelButtonText: '暂不发布',
        type: 'success'
      })
        .then(async () => {
          let hanldSuccess = await this.couponPublish(param)
          if(this.$store.state.permission.menuGray == 1) {
            this.$router.replace({
              path: '/storeVoucher',
              query: {refresh: true,to: 'couponList'}
            })
          }else {
            this.$router.replace({
              path: '/couponList',
              query: {refresh: true}
            })
          }
        })
        .catch(() => {
          if(this.$store.state.permission.menuGray == 1) {
            this.$router.replace({
              path: '/storeVoucher',
              query: {to: 'couponList'}
            })
          }else {
            this.$router.replace({
              path: '/couponList',
            })
          }
        })
    },
    async couponPublish(id) {
      return couponPublish({
        id
      }).then((res) => {
        if ((res.status = 'success')) {
          this.$message({
            type: 'success',
            message: '发布成功!'
          })
        } else {
          this.$message({
            type: 'error',
            message: res.msg
          })
        }
      })
    },
    /* 清除 */
    clearMin() {
      this.infoData.couponBasicTemplate.minMoneyToEnable1 = ''
      this.infoData.couponBasicTemplate.moneyInVoucher1 = ''
      this.infoData.couponBasicTemplate.minMoneyToEnable2 = ''
      this.infoData.couponBasicTemplate.moneyInVoucher2 = ''
      this.infoData.couponBasicTemplate.discount1 = ''
      this.infoData.couponBasicTemplate.minMoneyToEnable3 = ''
      this.infoData.couponBasicTemplate.discount2 = ''
      this.infoData.couponBasicTemplate.maxMoneyInVoucher = ''
    },
    clearProduct(val) {
      val == 0
        ? (this.infoData.couponBasicTemplate.description = '全店可用')
        : (this.infoData.couponBasicTemplate.description = '指定商品可用')
      if (this.$route.query.form == 'add') {
        this.multipleSelectionAll = []
        this.multipleSelection = []
      }
    },
    resetChange() {
      this.selectGoodSearch = {
        barcode: '',
        showName: '',
        status: '',
        code: ''
      }
      this.selectGoodpage.pageNum = 1
      this.onSearchGood()
    },
    /* 选择商品 */
    changeGood() {
      this.addGoodVisible = true
      this.onSearchGood()
    },
    /* 选择商品 */
    goodSelectionChange(val) {
      // console.log('val:', val);
      this.multipleSelection = val
    },
    /* 搜索商品 */
    onSearchGood(from) {
      this.changePageCoreRecordData()
      const param = {
        barcode: this.selectGoodSearch.barcode,
        productName: this.selectGoodSearch.showName,
        status: this.selectGoodSearch.status,
        pageNum: from && from.page ? from.page : 1,
        pageSize: from && from.limit ? from.limit : 10
      }
      // console.log('param:', param);
      productName(param)
        .then((res) => {
          if (res.status === 'success') {
            console.log('res.data:', res.data)
            const { list, totalCount, pageNo } = res.data

            this.selectGoodList.list = list
            this.selectGoodpage.pageNum = pageNo
            this.selectGoodList.total = totalCount

            this.isLoading = false

            setTimeout(() => {
              this.setSelectRow()
              // if (!res.data.page || res.data.page.list.length <= 0) {
              //   $('.good-table .el-table__empty-block').css('width', '100%');
              // }
            }, 200)
          } else {
            this.addGoodVisible = false
            this.$message({
              message: res.msg,
              type: 'error'
            })
          }
        })
        .catch((error) => {
          console.log(error)
        })
    },
    addGoodVisibleCancel() {
      this.addGoodVisible = false
    },
    closedAddGood() {
      this.selectGoodSearch.barcode = ''
      this.selectGoodSearch.showName = ''
      this.multipleSelection = this.multipleSelectionAll
    },
    /* 确认选择 */
    changeGoodList() {
      this.changePageCoreRecordData()
      // console.log('this.multipleSelectionAll:', this.multipleSelectionAll);
      this.multipleSelectionAll =
        this.multipleSelectionAll.length > 0
          ? this.multipleSelectionAll
          : this.multipleSelection
      if (this.multipleSelectionAll.length <= 0) {
        this.$message({
          message: '请先选择要添加的商品',
          type: 'warning'
        })
        return
      }
      this.addGoodVisible = false
    },
    // 设置选中的方法
    setSelectRow() {
      if (!this.multipleSelectionAll || this.multipleSelectionAll.length <= 0) {
        return
      }
      console.log('选中this.multipleSelectionAllL:', this.multipleSelectionAll)
      // 标识当前行的唯一键的名称
      const idKey = 'barcode'
      const selectAllIds = []
      this.multipleSelectionAll.forEach((row) => {
        selectAllIds.push(row[idKey])
      })
      this.$refs.tableChange.clearSelection()
      for (let i = 0; i < this.selectGoodList.list.length; i++) {
        if (selectAllIds.indexOf(this.selectGoodList.list[i][idKey]) >= 0) {
          // 设置选中，记住table组件需要使用ref="table"
          this.$refs.tableChange.toggleRowSelection(
            this.selectGoodList.list[i],
            true
          )
        }
      }
    },
    // 记忆选择核心方法
    changePageCoreRecordData() {
      // 标识当前行的唯一键的名称
      const idKey = 'barcode'
      const that = this
      // 如果总记忆中还没有选择的数据，那么就直接取当前页选中的数据，不需要后面一系列计算
      if (this.multipleSelectionAll.length <= 0) {
        this.multipleSelectionAll = this.multipleSelection
        return
      }

      // 总选择里面的key集合
      const selectAllIds = []
      this.multipleSelectionAll.forEach((row) => {
        selectAllIds.push(row[idKey])
      })

      const selectIds = []

      // 获取当前页选中的id
      this.multipleSelection.forEach((row) => {
        selectIds.push(row[idKey])
        // 如果总选择里面不包含当前页选中的数据，那么就加入到总选择集合里
        if (selectAllIds.indexOf(row[idKey]) < 0) {
          that.multipleSelectionAll.push(row)
        }
      })
      const noSelectIds = []

      // 得到当前页没有选中的id
      this.selectGoodList.list.forEach((row) => {
        if (selectIds.indexOf(row[idKey]) < 0) {
          noSelectIds.push(row[idKey])
        }
      })
      console.log('this.selectGoodList.list:', this.selectGoodList.list)
      console.log('5555this.multipleSelectionAll:', this.multipleSelectionAll)
      console.log('noSelectIds;', noSelectIds)
      noSelectIds.forEach((id) => {
        if (selectAllIds.indexOf(id) >= 0) {
          for (let i = 0; i < that.multipleSelectionAll.length; i++) {
            if (that.multipleSelectionAll[i][idKey] === id) {
              // 如果总选择中有未被选中的，那么就删除这条
              that.multipleSelectionAll.splice(i, 1)
              break
            }
          }
        }
      })
      console.log(
        '记忆选择核心that.multipleSelectionAll:',
        that.multipleSelectionAll
      )
    },
    resetLook() {
      this.removeGoodSearch = {
        showName: '',
        barcode: ''
      }
      this.onLookGood()
    },
    /* yichuyixuan */
    onLookGood() {
      const allList = JSON.parse(JSON.stringify(this.checkedAllGood))
      if (this.removeGoodSearch.showName) {
        this.removeListData = this.checkedAllGood.filter(
          (item, index) =>
            item.showName.indexOf(this.removeGoodSearch.showName) > -1
        )
      } else if (this.removeGoodSearch.barcode) {
        this.removeListData = this.checkedAllGood.filter(
          (item, index) =>
            item.barcode.indexOf(this.removeGoodSearch.barcode) > -1
        )
      } else {
        this.removeListData = allList
      }
    },
    lookGood() {
      this.checkedAllGood = JSON.parse(
        JSON.stringify(this.multipleSelectionAll)
      )
      this.removePage.pageNum = 1
      this.removeListData = this.checkedAllGood.slice(
        (this.removePage.pageNum - 1) * this.removePage.pageSize,
        this.removePage.pageNum * this.removePage.pageSize
      )
      this.lookGoodVisible = true
    },
    /* 移除已选 */
    removeGoodList() {
      if (this.removeGoodData.length <= 0) {
        this.$message({
          message: '请先选择要移除的商品',
          type: 'warning'
        })
        return
      }
      const that = this
      this.$confirm('确认移除已选商品?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: ''
      })
        .then(() => {
          this.removeIds = []
          // 得到当前页没有选中的id
          this.removeGoodData.forEach((row) => {
            if (this.removeIds.indexOf(row.barcode) < 0) {
              this.removeIds.push(row.barcode)
            }
          })
          this.removeIds.forEach((barcode) => {
            if (this.removeIds.indexOf(barcode) >= 0) {
              for (let i = 0; i < this.checkedAllGood.length; i++) {
                if (this.checkedAllGood[i].barcode == barcode) {
                  // 如果总选择中有未被选中的，那么就删除这条
                  this.checkedAllGood.splice(i, 1)
                  break
                }
              }
            }
          })
          this.removePage.pageNum = 1
          this.removeListData = this.checkedAllGood.slice(
            (this.removePage.pageNum - 1) * this.removePage.pageSize,
            this.removePage.pageNum * this.removePage.pageSize
          )
          this.isNext = true
        })
        .catch(() => {})
    },
    /* 确认移除商品 */
    quRemoveList() {
      const that = this
      if (!this.isNext) {
        this.lookGoodVisible = false
      } else {
        // let removeIds = [];
        // // 得到当前页没有选中的id
        // this.removeGoodData.forEach(row=>{
        //     if (removeIds.indexOf(row['id']) < 0) {
        //         removeIds.push(row['id']);
        //     }
        // });
        this.removeIds.forEach((barcode) => {
          if (this.removeIds.indexOf(barcode) >= 0) {
            for (let i = 0; i < that.multipleSelectionAll.length; i++) {
              if (that.multipleSelectionAll[i].barcode === barcode) {
                that.multipleSelectionAll.splice(i, 1)
                break
              }
            }
          }
        })
        console.log(
          '移除后that.multipleSelectionAll:',
          that.multipleSelectionAll
        )
        this.lookGoodVisible = false
      }
    },
    /* val */
    removeGoodVal(val) {
      this.removeGoodData = val
    },
    onPageRemove() {
      this.removeListData = this.checkedAllGood.slice(
        (this.removePage.pageNum - 1) * this.removePage.pageSize,
        this.removePage.pageNum * this.removePage.pageSize
      )
    },
    setDis(val) {
      this.disVal = val
      this.infoData.couponBasicTemplate.moneyInVoucher = val
    },
    /* table颜色 */
    tableRowClass({ rowIndex }) {
      if (rowIndex % 2 !== 1) {
        return ''
      }
      return 'success-row'
    },
    reduceTypeChange() {
      this.$refs.createdForm.clearValidate(['couponBasicTemplate.minMoneyToEnable','couponBasicTemplate.moneyInVoucher','couponBasicTemplate.discount'])
      this.clearMin();
    },
    templateTypeChange(){
      this.$refs.createdForm.clearValidate(['couponBasicTemplate.minMoneyToEnable','couponBasicTemplate.moneyInVoucher','couponBasicTemplate.discount'])
      this.infoData.couponBasicTemplate.reduceType = '0'
      this.clearMin();
    }
  }
}
</script>

<style lang="scss" scoped>
@import '../../assets/css/market.css';
// ::v-deep  .el-form-item__content{
//   margin-left: -120px;
// }
.waring-tip {
  padding: 0;
  margin: 0;
  font-size: 12px;
  color: #999999;
}

.p-text {
  padding: 0;
  margin: 0;
  padding: 8px 0 0 25px;
}

.sub-btn {
  padding-left: 120px;
  width: 380px;
}
</style>

<style lang="scss">
@import '../../assets/css/market.css';
.onItem {
  .el-form-item__label {
    line-height: 18px !important;
  }
}
.created-box{
  padding-top: 15px;
}
</style>
