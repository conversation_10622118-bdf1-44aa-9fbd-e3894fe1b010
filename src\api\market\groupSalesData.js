import request from '../index';

/**
 * 根据条件分页查询销售数据
 * @param {参数} params
 */
export function getSalesDataList(params) {
  return request.post('/promo/activity/saleData/paging', params);
}

/**
 * 根据条件查询销售统计数据
 * @param {参数} params
 */
export function getStatisticsInfo(params) {
  return request.post('/promo/activity/saleData/getStatisticsInfo', params);
}

/**
 * 根据条件查询状态及数量
 * @param {参数} params
 */
export function getStatusCountInfo(params) {
  return request.post('/promo/activity/saleData/getStatusCountInfo', params);
}

/**
 * 根据条件分页导出销售数据
 * @param {参数} params
 */
export function exportData(params) {
  return request.post('/promo/activity/saleData/export', params);
}

/**
 * 获取活动销售数据查询信息
 * @param {参数} params
 */
export function getSearchInfo(params) {
  return request.post('/promo/activity/saleData/getSearchInfo', params, {
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
    transformRequest: [function (data) {
      let ret = '';
      Object.keys(data)
        .forEach((item) => {
          ret += `${encodeURIComponent(item)}=${encodeURIComponent(data[item])}&`;
        });
      return ret;
    }],
  });
}

/**
 * 查询省份列表
 * @param {参数} params
 */
export function listProvinces(params) {
  return request.get('/promo/activity/saleData/listProvinces', params);
}

/**
 * 查询订单状态列表
 * @param {参数} params
 */
export function listOrderStatus(params) {
  return request.get('/promo/activity/saleData/listOrderStatus', params);
}
