<template>
  <div class="padding20">
    <div class="serch">
      <el-row
        type="flex"
        align="middle"
      >
        <span class="sign" />
        <div>基本信息</div>
      </el-row>
<!--      <el-button-->
<!--        type="primary"-->
<!--        size="small"-->
<!--        @click="$router.go(-1)"-->
<!--      >-->
<!--        返回-->
<!--      </el-button>-->
    </div>
    <div>
      <el-form
        ref="basic"
        label-width="100px"
        size="small"
        label-position="right"
        :model="basic"
        style="width: 500px"
      >
        <el-form-item
          ref="title"
          label="活动名称"
          prop="title"
        >
          <span>{{ basic.title }}</span>
        </el-form-item>
        <el-form-item
          ref="timeList"
          label="活动时间"
          prop="timeList"
        >
          {{ basic.startTime | formatDate }} - {{ basic.endTime | formatDate }}
        </el-form-item>
        <el-form-item
          ref="preheatTime"
          label="预热时间"
          prop="preheatTime"
        >
          <span>{{ basic.preheatTime | formatDate }}</span>
        </el-form-item>
        <el-form-item label="活动介绍">
          <span>{{ basic.introduction }}</span>
        </el-form-item>
      </el-form>
    </div>
    <div class="serch">
      <el-row
        type="flex"
        align="middle"
      >
        <span class="sign" />
        <div>特价配置</div>
      </el-row>
    </div>
    <div>
      <div class="list-box">
        <div class="text">
          人群范围：
        </div>
        <div>
          <p>
            <el-radio
              v-model="radio"
              disabled
              label="1"
            >
              指定人群参与
            </el-radio>
            <span
              v-if="radio == 1"
              style="padding-left: 10px"
            >{{ peopleIdStr }}</span>
          </p>
          <p style="padding-top: 10px">
            <el-radio
              v-model="radio"
              disabled
              label="2"
              @change="clearPeople"
            >
              全部人群参与
            </el-radio>
          </p>
        </div>
      </div>
      <div>
        <el-table
          ref="shopTable"
          :data="shopListData"
          stripe
          border
          tooltip-effect="dark"
          style="width: 100%"
        >
          <el-table-column
            type="index"
            width="50"
          />
          <el-table-column
            prop="barcode"
            label="商品编码"
          />
          <el-table-column
            prop="showName"
            label="商品名称"
          />
          <el-table-column
            prop="spec"
            label="规格"
          />
          <el-table-column
            prop="fob"
            label="单体采购价"
          />
          <el-table-column
            prop="guidePrice"
            label="连锁采购价"
          />
          <el-table-column
            label="特价价格"
            prop="skuPrice"
          />
          <el-table-column
            prop="availableQty"
            label="原价商品实时库存"
          />
          <el-table-column
            label="商品限购总数量"
            prop="totalQty"
          />
          <el-table-column
            label="活动期间卖出数量"
            prop="purchaseQty"
          />
          <el-table-column
            label="商品个人限购数量"
            prop="personalQty"
          />
          <el-table-column label="可超出限购数量">
            <template slot-scope="scope">
              <span v-if="scope.row.isOver">是</span>
              <span v-else>否</span>
            </template>
          </el-table-column>
          <el-table-column label="创建时间">
            <template slot-scope="scope">
              <span>{{ scope.row.createTime | formatDate }}</span>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
  </div>
</template>

<script>

import { getGoodsDetail } from '../../api/activity/special';

export default {
  name: 'specialPriceDetailPlus',
  data() {
    const checkTime = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('请选择预热时间'));
      } else {
        if (this.basic.timeList[0] && value > new Date(this.basic.timeList[0])) {
          callback(new Error('预热时间必须早于活动开始时间'));
        } else {
          callback();
        }
      }
    };
    const checkName = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('请输入活动名称'));
      } else if (this.nameErrorMsg) {
        callback(new Error(this.nameErrorMsg));
      } else {
        callback();
      }
    };
    return {
      expireTimeOption: {
        disabledDate(date) {
          return date.getTime() <= Date.now();
        },
      },
      basicRules: {
        title: [
          { required: true, validator: checkName, trigger: 'blur' },
          { max: 50, message: '活动名称限制50', trigger: 'blur' },
        ],
        timeList: [
          { type: 'array', required: true, message: '请选择活动时间', trigger: 'change' },
        ],
        preheatTime: [
          { required: true, validator: checkTime, trigger: 'change' },
        ],
      },
      basic: {
        promotionId: '',
        title: '',
        startTime: '',
        endTime: '',
        preheatTime: '',
        introduction: '',
        timeList: [],
      },
      nameErrorMsg: '',
      radio: '2',
      shopListData: [],
      shopListAllData: [],
      peopleIdStr: '',
      customerGroupId: '',
      isDialog: false,
      isInputValue: null,
      routerObj: ''
    };
  },
  created() {
    this.queryInfo()
  },
  activated() {
    if (this.routerObj && JSON.stringify(this.routerObj) !== JSON.stringify(this.$route.query)) {
      this.queryInfo()
    }
  },
  methods: {
    queryInfo(){
      this.routerObj = this.$route.query
      if (this.routerObj && this.routerObj.promotionId) {
        getGoodsDetail({ promotionId: this.routerObj.promotionId }).then((res) => {
          if (res.code == 0) {
            const data = res.result;
            this.basic.title = data.title;
            this.basic.preheatTime = data.preheatTime;
            this.basic.startTime = data.startTime;
            this.basic.endTime = data.endTime;
            this.basic.introduction = data.introduction;
            this.basic.promotionId = data.promotionId;
            this.basic.timeList = [data.startTime, data.endTime];
            if (data.customerGroupId) {
              this.radio = '1';
              this.customerGroupId = data.customerGroupId;
              this.peopleIdStr = data.customerGroupDTO.groupName;
            }
            this.shopListData = data.promotionSkuExtendDTOList;
          } else {

          }
        });
      }
    },
    clearPeople(val) {
      this.peopleIdStr = '';
      this.customerGroupId = '';
    },
  },
};
</script>

<style scoped lang="scss">
.serch {
  font-weight: bold;
  padding-bottom: 5px;
  display: flex;
  justify-content: space-between;
  .sign {
    display: inline-table;
    width: 3px;
    height: 13px;
    background: linear-gradient(360deg, #1d69c4 1%, #8bbdfc 99%);
    border-radius: 2px;
    margin-right: 8px;
  }
}
.padding20{
  padding: 15px 20px;
}
.list-box{
  display: flex;
  padding-left: 20px;
  .text{
    font-size: 14px;
    font-weight: bold;
  }
  p{
    padding: 0;
    margin: 0;
  }
}
.errorTip{
  padding: 0;
  margin: 0;
  padding-top: 5px;
  color: #ff2121;
}
.upload-demo{
  display: inline-block;
  margin: 0 10px;
}
.chooses {
  ::v-deep  .el-button--primary {
    background: #4183d5;
  }
  ::v-deep  .el-dialog__body {
    padding: 0 0 10px;
  }
  ::v-deep  .el-dialog__header {
    padding: 10px 16px;
    background: #f9f9f9;
  }
  ::v-deep  .el-dialog__headerbtn {
    top: 13px;
  }
  .conten-box{
    padding: 15px 20px;
    display: flex;
  }
}
</style>
