<template>
  <el-dialog title='发布入仓商品' :visible='dialogVisible' width='80%' :before-close='handleClose'>
    <SearchForm
      ref='searchForm'
      :model='formModel'
      :form-items='formItems'
      :hasOpenBtn='false'
      @submit='handleFormSubmit'
      @reset='handleFormReset'
    />
    <xyyTable
      style='margin-top: 15px'
      ref='productListTable'
      v-loading='tableLoading'
      :data='tableConfig.data'
      :col='tableConfig.col'
      :has-selection='true'
      :list-query='listQuery'
      @selectionCallback='selectionCallback'
      @get-data='queryList'
    />
    <span slot='footer' class='dialog-footer'>
        <el-button size='small' @click='handleClose'>取 消</el-button>
        <el-button type='primary' size='small' @click='submit' :loading='submitLoading'>确 定</el-button>
      </span>
  </el-dialog>
</template>

<script>
import SearchForm from '@/components/searchForm'
import { apiShenNongProductlist, apiBatchPublish } from '@/api/product'

export default {
  name: 'releaseWarehousedGoodDialog',
  components: {
    SearchForm
  },
  data() {
    return {
      dialogVisible: true,
      formModel: {
        oldProductCode: '',//原商品编码
        productName: '',//商品名称
        smallPackageBarCode: ''//条形码
      },
      formItems: [
        {
          label: '原商品编码',
          prop: 'oldProductCode',
          component: 'el-input',
          attrs: {
            placeholder: '请输入原商品编码'
          }
        },
        {
          label: '商品名称',
          prop: 'productName',
          component: 'el-input',
          attrs: {
            placeholder: '请输入商品名称'
          }
        },
        {
          label: '条形码',
          prop: 'smallPackageBarCode',
          component: 'el-input',
          attrs: {
            placeholder: '请输入条形码'
          }
        }
      ],
      tableLoading: false,
      tableConfig: {
        data: [],
        col: [
          {
            name: '原商品编码',
            index: 'oldProductCode'
          },
          {
            name: '商品名称',
            index: 'productName'
          }, {
            name: '条形码',
            index: 'smallPackageBarCode'
          }, {
            name: '规格',
            index: 'specifications'
          }, {
            name: '创建时间',
            index: 'applicationTime',
            formatter: (row, col, cell) => {
              return this.formatDate(cell)
            }
          }
        ]
      },
      listQuery: {
        pageSize: 10,
        page: 1,
        total: 0
      },
      selectList: [],
      submitLoading: false
    }
  },
  created() {
    this.queryList()
  },
  methods: {
    handleClose() {
      this.$emit('update:releaseWarehousedGoodDialogVisible', false)
    },
    handleFormSubmit() {
      this.queryList()
    },
    handleFormReset(obj) {
      this.formModel = obj
      this.queryList()
    },
    selectionCallback(val) {
      console.log(val)
      this.selectList = val
    },
    async queryList(listQuery) {
      this.tableLoading = true
      if (listQuery) {
        const { pageSize, page } = listQuery
        this.listQuery.pageSize = pageSize
        this.listQuery.page = page
      }
      let params = { ...this.formModel }
      const { pageSize, page } = this.listQuery
      params.pageNum = page
      params.pageSize = pageSize
      console.log(params)
      try {
        const res = await apiShenNongProductlist(params)
        if (res && res.code === 0) {
          this.tableConfig.data = res.data.list
          this.listQuery.total = res.data.total
        } else {
          this.$message.error(res.message || '获取列表失败')
        }
      } catch (e) {
        console.log(e)
      }
      this.tableLoading = false
    },
    async submit() {
      const ids = this.selectList.map(item => item.oldProductCode)
      if (ids && ids.length > 0) {
        this.submitLoading = true
        try {
          const res = await apiBatchPublish(ids)
          if (res && res.code === 0) {
            const { success, error, errorFileName, errorFileUrl } = res.data
            const str = [`<div>${success || 0}个商品发布成功</div>`]
            if (error && error > 0) {
              str.push(`<div>${error || 0}个商品发布失败，<a style='color: #4183d5' href='${errorFileUrl}' download='${errorFileName}'>下载失败文件</a></div>`)
            }
            this.$confirm(str.join(''), '发布商品提醒', {
              distinguishCancelAndClose: true,
              dangerouslyUseHTMLString: true,
              confirmButtonText: '返回商品列表',
              cancelButtonText: '继续发布'
            }).then(() => {
              this.$parent.handleFormSubmit()
              this.handleClose()
            }).catch(action => {
              console.log(action)
            })
          } else {
            this.$message.error(res.message || '发布失败')
          }
        } catch (e) {
          console.log(e)
        }
        this.submitLoading = false
      } else {
        this.$message.warning('请勾选数据！')
      }
    }
  }
}
</script>

<style scoped>

</style>
