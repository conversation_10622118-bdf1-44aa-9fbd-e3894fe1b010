<template>
  <div style="padding: 10px 0;">
    <el-table :data="tableData" border fit>
      <el-table-column align="center" label="序号" type="index" width="50"></el-table-column>
      <el-table-column align="center" label="商品编码" width="220">
				<template slot-scope="scope">
					<p class="item-left" >
						<span >商品编码：</span>
						<span>{{ scope.row.barcode }}</span>
					</p>
					<p class="item-left" >
						<span >ERP编码：</span>
						<span>{{ scope.row.erpCode }}</span>
					</p>
					<p class="item-left">
						<span >CSUID：</span>
						<span>{{ scope.row.csuid }}</span>
					</p>
				</template>
			</el-table-column>
      <el-table-column align="center" label="商品名称" width="220">
				<template slot-scope="scope">
					<p class="item-left">
						<span>通用名：</span>
						<span>{{ scope.row.commonName }}</span>
					</p>
					<p class="item-left">
						<span>商品名称：</span>
						<span>{{ scope.row.productName }}</span>
					</p>
				</template>
			</el-table-column>
      <el-table-column align="center" label="规格 / 包装" width="100" prop="spec"></el-table-column>
      <el-table-column align="center" label="批准文号" width="170" prop="approvalNumber"></el-table-column>
      <el-table-column align="center" label="生产厂家" width="170" prop="manufacturer"></el-table-column>
      <el-table-column align="center" label="申请下载次数" width="120" prop="applyDownloadCount"></el-table-column>
      <el-table-column align="center" label="更新时间" width="170" prop="updateTime"></el-table-column>
      <el-table-column align="center" label="附件">
        <template slot-scope="scope">
						<el-button type="text" @click="readFileClick(scope.row.annexList, '附件')">{{ scope.row.annexCount }}</el-button>
				</template>
      </el-table-column>
      <el-table-column align="center" label="附件（签章）">
        <template slot-scope="scope">
						<el-button type="text" @click="readFileClick(scope.row.sealAnnexList, '附件（盖章）')">{{ scope.row.sealAnnexCount }}</el-button>
				</template>
      </el-table-column>
      <el-table-column align="center" label="操作" width="200">
        <template slot-scope="scope">
          <div>
            <el-button size="small" type="text" @click="upload('/qualificationUploadEdit', { barcode: scope.row.barcode, from: 0 })">上传编辑</el-button>
          </div>
          <div>
            <el-button size="small" type="text" @click="downloadFile(scope.row.barcode, 0)">下载附件</el-button>
          </div>
          <div>
            <el-button size="small" type="text" @click="downloadFile(scope.row.barcode, 1)">下载附件（签章）</el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <el-dialog :title="`查看${readFile.title}`" :visible.sync="readFile.readFileDialogStatus" width="600px">
      <iImg  :maxCount="readFile.currentReadFileList.length" :isEdit="false" v-model="readFile.currentReadFileList" :column="3"></iImg>
      <template slot="footer">
        <el-button size="small" type="primary" @click="readFile.readFileDialogStatus = false">关闭</el-button>
      </template>
    </el-dialog>
    <export-tip
      :change-export="changeExport"
      @handleExoprClose="handleExoprClose"
      @handleChangeExport="handleChangeExport"
    />
  </div>
</template>
<script>
import iImg from './components/i-img.vue'
import { firstSaleQualificationDownFile } from '../../api/qualificationOnline/index'
import exportTip from '../other/components/exportTip.vue'
import { downloadZip } from '@/utils/download'
export default {
  props: ['tableData'],
  components: {
    iImg,
    exportTip
  },
  data() {
    return {
      readFile: {
        currentReadFileList: [],
        readFileDialogStatus: false,
        title: '',
      },
      changeExport: false,
    }
  },
  methods: {
    readFileClick(fileList, title) {
      this.readFile.currentReadFileList = fileList;
      this.readFile.title = title;
      this.readFile.readFileDialogStatus = true;
    },
    downloadFile(barcode, type) {
      firstSaleQualificationDownFile({
        barCode: barcode,
        sealStatus: type
      }).then(async (res) => {
        if(res.code == 0) {
          if (!res.result.length) {
            this.$message.error('无可下载的附件！');
            return;
          }
          downloadZip(res.result, barcode + "资质文件" + (type == 1 ? '(签章)' : ''));
        } else {
          this.$message.error(res.msg || '下载失败！请稍后再试！');
        }
      }).catch(() => {});
    },
    handleChangeExport(info) {
      this.changeExport = false;
      if (info === 'go') {
        const path = '/downloadList'
        window.openTab(path)
      }
    },
    handleExoprClose() {
      this.changeExport = false;
    },
    upload(url, query) {
      //{ path:'/qualificationUploadEdit', query: { barcode: scope.row.barcode, from: 0 } }
      window.openTab(url, query)
    }
  }
}
</script>
<style scoped>
.item-left {
	width: 100%;
	display: flex;
	align-items: flex-start;
	margin: 0;
}
.item-left > span:first-child {
		flex-shrink: 0;
    flex-grow: 0;
		text-align: end;
		color:#8d8d8d;
	}
.item-left > span:nth-child(2) {
		flex-grow: 1;
		display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
		overflow: hidden;
		text-overflow: ellipsis;
		text-align: start !important;
	}
</style>
