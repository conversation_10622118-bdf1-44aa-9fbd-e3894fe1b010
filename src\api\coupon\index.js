import request from '@/utils/request';


// 新接口
// 查询pop优惠券列表
export function couponListNew(params) {
  return request({
    url: '/promo/coupon/list',
    method: 'post',
    data: params,
  });
}

// 新增和修改优惠券
export function updateCouponNew(params) {
  return request({
    url: '/coupon/updateCoupon',
    method: 'post',
    data: params,
  });
}

// 查看优惠券详情
export function queryCouponDetailNew(params) {
  return request({
    url: '/promo/coupon/queryCouponDetail',
    method: 'get',
    params,
  });
}

// 优惠券详情页查询商品信息
export function queryCsuListNew(params) {
  return request({
    url: '/coupon/queryCsuList',
    method: 'get',
    params,
  });
}

// 旧接口
export function couponList(params) {
  return request({
    url: '/promo/coupon/list',
    method: 'post',
    data: params,
  });
}
export function productName(params) {
  return request({
    url: '/promo/coupon/productList',
    method: 'post',
    data: params,
  });
}
export function couponDetail(params) {
  return request({
    url: '/promo/coupon/detail',
    method: 'post',
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
    data: params,
    transformRequest: [
      function (data) {
        const formData = new FormData();
        Object.keys(data).forEach((key) => {
          formData.append(key, data[key]);
        });
        return formData;
      },
    ],
  });
}

export function couponSave(params) {
  return request({
    url: '/promo/coupon/save',
    method: 'post',
    data: params,
  });
}
export function couponPublish(params) {
  return request({
    url: '/promo/coupon/publish',
    method: 'post',
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
    data: params,
    transformRequest: [
      function (data) {
        const formData = new FormData();
        Object.keys(data).forEach((key) => {
          formData.append(key, data[key]);
        });
        return formData;
      },
    ],
  });
}

/**
 * 赠券
 * @param params
 * @returns {AxiosPromise}
 */
export function sendCoupon(params) {
  return request({
    url: '/promo/coupon/sendCoupon',
    method: 'post',
    data: params,
  });
}

/**
 * 查看优惠券详情
 * @param params
 * @returns {AxiosPromise}
 */
export function queryCouponDetail(params) {
  return request({
    url: '/coupon/queryCouponDetail',
    method: 'get',
    params,
  });
}

/**
 * 查看优惠券详情
 * @param params
 * @returns {AxiosPromise}
 */
export function checkSendCouponUser(params) {
  return request({
    url: '/promo/coupon/checkSendCouponUser',
    method: 'get',
    params,
  });
}

/**
 * 赠券查询药店
 * @param params
 * @returns {AxiosPromise}
 */
export function selectMerchantByName(params) {
  return request({
    url: '/promo/coupon/selectMerchantByName',
    method: 'get',
    params,
  });
}
