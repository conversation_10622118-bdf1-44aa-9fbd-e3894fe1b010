<template>
  <el-dialog
    :title="editType == 'add' ? '新增图片楼层' : '编辑图片楼层'"
    :visible="true"
    class="my-dialog"
    :destroy-on-close="true"
    :before-close="cancelDialog"
    width="65%"
  >
    <el-row>
      <div class="explain-table dialogBox">
        <el-col :span="16">
          <div style="overflow-y: auto;margin-top:10px">
            <el-form
              ref="adForm"
              size="small"
              :model="formData"
              :rules="rules"
              label-width="130px"
            >
              <el-form-item
                label="图片楼层名称:"
                prop="name"
              >
                <el-input
                  v-model="formData.name"
                  style="width: 300px"
                  :maxlength="20"
                />
              </el-form-item>
              <el-form-item
                label="人群"
                prop="userType"
              >
                <el-radio-group
                  v-model="formData.userType"
                  @change="changeUser"
                >
                  <el-radio :label="-1">
                    全部人群
                  </el-radio>
                  <el-radio :label="1">
                    指定人群
                  </el-radio>
                  <el-button
                    v-if="formData.userType == 1 "
                    size="small"
                    type="primary"
                    plain
                    @click="selectPeople"
                  >选择人群</el-button>
                </el-radio-group>
                <span style="marginLeft: 10px">{{ designatedUsersName }}</span>
              </el-form-item>
              <el-form-item
                label="展示时间:"
                prop="time"
              >
                <el-date-picker
                  v-model="formData.time"
                  style="width: 380px"
                  type="datetimerange"
                  format="yyyy-MM-dd HH:mm:ss"
                  value-format="timestamp"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  :default-time="['00:00:00', '23:59:59']"
                />
              </el-form-item>
              <el-form-item
                label="楼层图片:"
                prop="imageAddress"
              >
                <el-upload
                  class="avatar-uploader"
                  action
                  :http-request="uploadImg"
                  :before-upload="beforeAvatarUpload"
                  :show-file-list="false"
                  :on-remove="handleRemove"
                  style="height: 64px;"
                >
                  <img
                    v-if="formData.imageAddress"
                    :src="`${hostName}/${formData.imageAddress}`"
                    class="avatar"
                  >
                  <div style="margin-top:5px">
                    <i class="el-icon-plus">
                      <div class="avatar-uploader-icon">上传图片</div>
                    </i>
                  </div>
                </el-upload>
                <span class="my-lable2-info">支持png、jpg、jpeg、gif，宽度为750px时客户端展示效果最好。</span>
              </el-form-item>
              <el-form-item
                label="热区模式:"
                prop="hotSpots"
              >
                <el-radio-group v-model="formData.hotSpots" @change="changeHotSpots">
                  <el-radio
                    v-for="(item, index) in 5"
                    :key="index+1"
                    :label="index+1"
                  >
                    {{ index+1 }}热区
                  </el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item
                v-for="(item, index) in formData.hotSpots"
                :key="index"
                label="APP跳转链接:"
              >
                <el-input
                  v-model.trim="formData.appLinks[index]"
                  clearable
                  style="width: 300px"
                />
              </el-form-item>
              <el-form-item
                label="排序号:"
                prop="sort"
              >
                <el-input
                  v-model.trim="formData.sort"
                  style="width: 300px"
                  clearable
                />
                <span class="my-lable2-info">排序号越小，在店铺首页展示位置越靠前</span>
              </el-form-item>
              <el-form-item
                label="状态:"
                prop="status"
              >
                <el-radio-group v-model="formData.status">
                  <el-radio :label="1">
                    启用
                  </el-radio>
                  <el-radio :label="0">
                    停用
                  </el-radio>
                </el-radio-group>
              </el-form-item>
            </el-form>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="defultInfo">
            <div>店铺首页效果示意图(以2热区为例)</div>
            <div class="defultImg">
              <img src="@/assets/image/common/imgFloor.png" alt="">
            </div>
          </div>
        </el-col>
      </div>
    </el-row>
    <div
      slot="footer"
      class="dialog-footer"
    >
      <el-button
        size="small"
        @click="cancelDialog"
      >
        取 消
      </el-button>
      <el-button
        type="primary"
        class="xyy-blue"
        size="small"
        @click="confirmDialog"
      >
        确定
      </el-button>
    </div>
    <crowd-selector-dialog
      v-if="peopleVisible"
      ref="changePeople"
      v-model="peopleVisible"
      :selected="designatedUsers"
      @onSelect="sendPeopleData"
    />
  </el-dialog>
</template>
<script>
import { uploadFile } from '@/api/qual/index';
import { getHostName, picAdvSave } from '@/api/storeManagement/index';
import utils from '@/utils/filter';
import CrowdSelectorDialog from '../../../components/xyy/customerOperatoin/crowd-selector-dialog.vue';

export default {
  name: 'AddPictureFloor',
  components: { CrowdSelectorDialog },
  props: ['editType', 'editRow'],
  data() {
    return {
      formData: {
        name: '',
        imageAddress: '',
        appLinks: [''],
        sort: '',
        status: 1,
        hotSpots: 1,
        userType: '',
        time: [],
      },
      hostName: '',
      designatedUsersName: '',
      peopleVisible: false,
      designatedUsers: '',
      rules: {
        name: [
          { required: true, message: '图片楼层名称为必填项', trigger: 'blur' },
        ],
        time: [
          { required: true, message: '展示时间为必填项', trigger: 'blur' },
        ],
        userType: [
          { required: true, message: '人群为必填项', trigger: 'blur' },
        ],
        imageAddress: [
          { required: true, message: '广告图片为必填项', trigger: 'blur' },
        ],
        sort: [
          { required: true, message: '排序号为必填项', trigger: 'blur' },
          { pattern: /^[0-9]*$/, message: '只允许输入正整数和0', trigger: 'blur' },
        ],
        hotSpots: [
          { required: true, message: '热区模式为必填项', trigger: 'blur' },
        ],
        status: [
          { required: true, message: '状态为必填项', trigger: 'blur' },
        ],
      },
    };
  },
  created() {
    getHostName().then((res) => {
      if (res.hostName) {
        this.hostName = res.hostName;
      }
    });
    if (Object.keys(this.editRow).length) {
      this.initData();
    }
  },
  methods: {
    initData() {
      Object.keys(this.formData).forEach((key) => {
        this.formData[key] = this.editRow[key];
      }, this);
      this.formData.time = [this.editRow.startTime, this.editRow.endTime];
      this.formData.userType = this.editRow.designatedUsers === -1 ? -1 : 1;
      this.designatedUsers = this.editRow.designatedUsers === -1 ? '' : this.editRow.designatedUsers;
      this.designatedUsersName = this.editRow.designatedUsersName;
    },
    uploadImg(file) {
      uploadFile(file).then((res) => {
        if (res.code === '200') {
          this.formData.imageAddress = res.data;
        } else {
          this.formData.imageAddress = '';
        }
      });
    },
    beforeAvatarUpload(file) {
      if (!file) {
        this.$message.error('请上传图片');
        return false;
      }
      const isJPG = file.type === 'image/jpeg'
        || file.type === 'image/png'
        || file.type === 'image/bmp'
        || file.type === 'image/jpg'
        || file.type === 'image/gif';
      const isLt5M = file.size / 1024 / 1024 < 5;

      if (!isJPG || !isLt5M) {
        this.$message.error('图片不满足上传要求，请重新上传');
        return false;
      }
      return isJPG && isLt5M;
    },
    handleRemove() {

    },
    changeHotSpots(e) {
      this.formData.appLinks = [];
      for (let i = 0; i < e; i++) {
        this.formData.appLinks.push('');
      }
    },
    changeUser() {
      this.designatedUsersName = '';
      this.designatedUsers = '';
    },
    selectPeople() {
      this.peopleVisible = true;
    },
    // 添加人群
    sendPeopleData(value) {
      this.designatedUsersName = value.tagName;
      this.designatedUsers = value.id;
    },
    cancelDialog() {
      this.$emit('cancelDialog');
    },
    confirmDialog() {
      this.$refs.adForm.validate((valid) => {
        if (valid) {
          if (this.formData.userType === 1 && !this.designatedUsers) {
            this.$message.error('请选择人群');
            return;
          }
          const params = {
            ...this.formData,
            startTimeStr: utils.dataTime(this.formData.time[0], 'yy-mm-dd HH:ss:nn'),
            endTimeStr: utils.dataTime(this.formData.time[1], 'yy-mm-dd HH:ss:nn'),
            designatedUsers: this.formData.userType === -1 ? -1 : this.designatedUsers,
            id: this.editRow.id,
          };
          if (!this.editRow.id) {
            delete params.id;
          }
          picAdvSave(params).then((res) => {
            if (res.code === 0) {
              this.$message.success('保存成功');
              this.$emit('refresh');
              this.cancelDialog();
            } else {
              this.$message.error(res.message);
            }
          }).catch((err) => { console.log(err); });
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
::v-deep  .avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  background: rgba(0, 0, 0, 0.02);
  width: 64px;
  height: 64px;
  line-height: 64px;
}
::v-deep  .avatar-uploader .el-upload:hover {
  border-color: #409eff;
}
::v-deep  .avatar-uploader .el-upload__tip {
  margin-top: 0;
  color: #999999;
  font-size: 12px;
}
.avatar-uploader-icon {
  opacity: 1;
  font-size: 12px;
  font-family: PingFangSC, PingFangSC-Regular;
  font-weight: 400;
  text-align: left;
  color: rgba(0, 0, 0, 0.65);
  line-height: 14px;
}
.avatar {
  width: 64px;
  height: 64px;
  display: block;
}
::v-deep  .avatar-uploader .el-upload-list--picture-card .el-upload-list__item {
  width: 64px;
  height: 64px;
}
.defultInfo {
  width: 100%;
  height: 400px;
  margin: 20px 0 20px 20px;
}
.defultImg {
  img {
    margin-top: 10px;
    width: 220px;
  }
}
.my-lable2-info {
  color: #F56C6C;
  display: block;
}
</style>
