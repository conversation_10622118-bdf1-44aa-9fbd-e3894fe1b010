<!--created by <PERSON><PERSON><PERSON><PERSON><PERSON> in 2021/3/13
 指定ID 选人
-->
<template>
  <el-row>
    <el-col class="radio-container">
      <el-radio
        class="radio-style"
        :label="merchantJoinTypes.include"
        :value="merchantJoinType"
        @click.native.prevent="clickCustomerRadio(merchantJoinTypes.include)"
      >
        指定客户参与
      </el-radio>

      <span
        v-if="merchantJoinType === merchantJoinTypes.include"
        style="display: flex; padding-bottom: 10px; align-items: center;"
      >
        <el-upload
          :ref="uploadRef"
          action="xxx"
          :http-request="uploadFile"
          :show-file-list="false"
          :limit="1"
          :before-upload="beforeImportData"
        >
          <el-button
            size="small"
            type="primary"
            :loading="importLoading"
          >批量导入</el-button>
        </el-upload>
        <a
          :href="downloadTemplateUrls.get(merchantJoinTypes.include)"
          class="downLoader-btn"
          style="margin: 0 10px;"
        >
          <el-button
            size="small"
            style="border-color: #4184d5; color: #4184d5;"
          >下载模版</el-button>
        </a>
        <el-link
          v-if="showCustomerNum"
          style="margin-left: 10px;"
          type="primary"
          :underline="false"
          @click="viewImportCustomerList"
        >查看已导入的{{ customerNum }}个客户</el-link>
      </span>
    </el-col>
    <el-col class="radio-container">
      <el-radio
        class="radio-style"
        :label="merchantJoinTypes.exclude"
        :value="merchantJoinType"
        @click.native.prevent="clickCustomerRadio(merchantJoinTypes.exclude)"
      >
        指定客户排除
      </el-radio>

      <span
        v-if="merchantJoinType === merchantJoinTypes.exclude"
        style="display: flex; padding-bottom: 10px; align-items: center;"
      >
        <el-upload
          :ref="uploadRef"
          action="xxx"
          :http-request="uploadFile"
          :show-file-list="false"
          :limit="1"
          :before-upload="beforeImportData"
        >
          <el-button
            size="small"
            type="primary"
            :loading="importLoading"
          >批量导入</el-button>
        </el-upload>
        <a
          :href="downloadTemplateUrls.get(merchantJoinTypes.exclude)"
          class="downLoader-btn"
          style="margin: 0 10px;"
        >
          <el-button
            size="small"
            style="border-color: #4184d5; color: #4184d5;"
          >下载模版</el-button>
        </a>
        <el-link
          v-if="showCustomerNum"
          style="margin-left: 10px;"
          type="primary"
          :underline="false"
          @click="viewImportCustomerList"
        >查看已导入的{{ customerNum }}个客户
        </el-link>
      </span>
    </el-col>
    <el-col v-if="showMerchantJoinTypeError">
      <span style="color: #f56c6c; font-size: 12px;">请为人群选择客户</span>
    </el-col>
    <!--    人群客户列表展示-->
    <customer-list-dialog
      v-if="showCustomerDialog"
      v-model="showCustomerDialog"
      :customer-execute-type="customerDialogType.viewExport"
      :customer-count="customerNum"
      :append-params="appendParamsForCustomerList"
      :append-to-body="true"
      @updateCustomerCount="updateCustomerCount"
    />
  </el-row>
</template>

<script>
import { uploadCrowdCustomer } from './fetch/fetch';
import CustomerListDialog from './customer-list-dialog';
import {
  merchantJoinTypes,
  customerDialogType,
  downloadTemplateUrls,
} from './constant';

export default {
  name: 'CustomerSelectorContainer',
  components: { CustomerListDialog },
  props: {
    merchantJoinType: Number,
    tempSetCode: String,
    showCustomerNum: Boolean,
    customerNum: {
      type: Number,
      default: 0,
    },
  },
  data() {
    return {
      downloadTemplateUrls,
      showCustomerDialog: false,
      importLoading: false,
      appendParamsForCustomerList: undefined,
      merchantJoinTypes,
      customerDialogType,
      showMerchantJoinTypeError: false,
    };
  },
  computed: {
    uploadRef() {
      if (this.merchantJoinTypes.include === this.merchantJoinType) {
        return 'includeImport';
      }
      if (this.merchantJoinTypes.exclude === this.merchantJoinType) {
        return 'excludeImport';
      }
      return '';
    },
  },
  methods: {
    clickCustomerRadio(type) {
      if (type === 1) {
        this.$confirm('选择指定客户参与后，仅白名单内的客户可享受该活动，确定是否继续配置白名单？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
        })
          .then(() => {
            this.$emit('clear')
            if (this.merchantJoinType === type) {
              this.updateType(0);
            } else {
              this.updateType(type);
            }
          })
          .catch(() => {});
      } else {
        if (this.merchantJoinType === type) {
          this.updateType(0);
        } else {
          this.updateType(type);
        }
      }
    },
    updateCustomerCount(newVal) {
      if (newVal === 0) {
        this.$emit('update:tempSetCode', '');
      }
      this.$emit('update:customerNum', newVal);
    },
    validate() {
      let hasCustomer = true;
      if (
        this.merchantJoinType === this.merchantJoinTypes.exclude
        || this.merchantJoinType === this.merchantJoinTypes.include
      ) {
        hasCustomer = this.customerNum !== 0;
      }
      this.showMerchantJoinTypeError = this.showMerchantJoinTypeError || !hasCustomer;
      return hasCustomer;
    },
    updateType($event) {
      this.$emit('update:merchantJoinType', $event);
      this.$emit('update:tempSetCode', '');
      this.$emit('update:customerNum', 0);
      this.$emit('update:showCustomerNum', false);
    },
    viewImportCustomerList() {
      this.appendParamsForCustomerList = { tempSetCode: this.tempSetCode };
      this.showCustomerDialog = true;
    },
    uploadFile(params) {
      const { file } = params;
      let fileFormData;
      if (file && file.name !== '') {
        fileFormData = new FormData();
        fileFormData.append('excelFile', file);
        if (this.tempSetCode) {
          fileFormData.append('tempSetCode', this.tempSetCode);
        } else {
          fileFormData.append('tempSetCode', '');
        }
      } else {
        this.$message.warning('请选择上传文件!');
        return;
      }
      this.importLoading = true;
      uploadCrowdCustomer(fileFormData)
        .then((res) => {
          if (res.code === 1000) {
            this.$message({
              message: res.msg,
              type: 'success',
            });
            this.$emit('update:tempSetCode', res.data.tempSetCode);
            this.$emit('update:customerNum', res.data.merchantIdSize);
            this.$emit('update:showCustomerNum', true);
          } else {
            this.$message({
              showClose: true,
              message: res.msg,
              type: 'error',
              duration: 1000,
            });
          }
        })
        .catch((error) => {
          this.$message({
            message: '请求失败',
            type: 'error',
          });
        })
        .finally(() => {
          this.importLoading = false;
          if (this.uploadRef) {
            this.$refs[this.uploadRef].clearFiles();
          }
        });
    },
    beforeImportData(uploadInfo) {
      const fileName = uploadInfo.name;
      const fileType = fileName.substring(fileName.lastIndexOf('.') + 1);
      if (fileType !== 'xlsx' && fileType !== 'xls') {
        this.$message.warning('选择的文件类型不对');
        return false;
      }
      return true;
    },
  },
};
</script>

<style scoped lang="scss">
.radio-container {
  display: flex;
  .radio-style {
    height: 36px;
    display: block;
    margin-top: 10px;
    width: 100px;
  }
}
</style>
