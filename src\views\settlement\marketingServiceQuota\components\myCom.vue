<template>
  <div>
    <div class="text-father">
      <!-- 当前余额： 当前余额比例： 保证金金额：  -->
      <div class="left-text">
        <span>
          <span>当前余额</span>
          <span style="font-size: 22px">{{ account }}</span>元
        </span>
      </div>
      <!-- 购买记录和购买额度 -->
      <div class="left-right">
        <slot name="rechargeDialog"></slot>
        <slot name="recordDialog"></slot>
      </div>
        
    </div>
  </div>
</template>

<script>
export default {
  name: "marketMyCom",
  props: ['account'],
  data() {
    return {};
  },
  methods: {},
}
</script>

<style lang="scss" scoped>
.text-father{
  width: 100%; 
  height: 100px;
  // padding: 10px; 
  display: flex;
  border-bottom: 10px solid #f3f3f3;
}
.left-text{
  // width: 40%;
  display: flex;
  justify-content: space-around;
  align-items: center;
  margin: 0 50px 0 30px;
  // border: 1px solid red;
  :first-child{
    margin-right: 10px;
    color: #777777;
  }
  :last-child{
    color: #4184D5;
  }
}
.left-right{
  width: 200px;
  // border: 1px solid red;
  // padding: 20px 20px 0 20px;
  display: flex;
  // flex-direction: column;
  justify-content: space-around;
  align-items: center;
}
</style>

