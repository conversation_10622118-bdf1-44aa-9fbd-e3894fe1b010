<template>
  <div class="padding20">
    <div class="serch">
      <el-row type="flex" align="middle">
        <span class="sign" />
        <div>基本信息</div>
      </el-row>
      <el-button type="primary" size="small" @click="$router.go(-1)">返回</el-button>
    </div>
    <div>
      <el-form
        ref="basic"
        label-width="100px"
        size="small"
        label-position="right"
        :model="basic"
        style="width: 500px"
      >
        <el-form-item ref="title" label="活动名称" prop="title">
          <span>{{ basic.title }}</span>
        </el-form-item>
        <el-form-item
          ref="timeList"
          label="活动时间"
          prop="timeList"
        >{{ basic.startTime | formatDate }} - {{ basic.endTime | formatDate }}</el-form-item>
        <el-form-item ref="preheatTime" label="预热时间" prop="preheatTime">
          <span>{{ basic.preheatTime | formatDate }}</span>
        </el-form-item>
        <el-form-item label="活动介绍">
          <span>{{ basic.introduction }}</span>
        </el-form-item>
      </el-form>
    </div>
    <div class="serch">
      <el-row type="flex" align="middle">
        <span class="sign" />
        <div>特价配置</div>
      </el-row>
    </div>
    <div>
      <div class="list-box">
        <div class="text">人群范围：</div>
        <div>
          <p>
            <el-radio v-model="radio" disabled label="1">指定人群参与</el-radio>
            <span v-if="radio == 1" style="padding-left: 10px">{{ peopleIdStr }}</span>
          </p>
          <p style="padding-top: 10px">
            <el-radio v-model="radio" disabled label="2" @change="clearPeople">全部人群参与</el-radio>
          </p>
        </div>
      </div>
      <div style="padding: 10px 0;text-align: right">
        <el-input
          v-model="quickSearchStr"
          style="width: 200px;margin-left: 10px;"
          size="small"
          placeholder="sku编码/商品编码/商品名称/ERP编码/生产厂家"
          prefix-icon="el-icon-search"
          @change="handleQuickSearch"
        />
      </div>
      <div>
        <el-table
          ref="shopTable"
          :data="(quickSearchList ? quickSearchList : shopListData)"
          stripe
          border
          tooltip-effect="dark"
          style="width: 100%"
        >
          <el-table-column prop="barcode" label="编码信息">
            <template slot-scope="{ row }">
              <p>{{ row.skuId }}</p>
              <p>{{ row.barcode }}</p>
              <p>{{ row.erpCode }}</p>
            </template>
          </el-table-column>
          <el-table-column prop="showName" label="商品信息">
            <template slot-scope="{ row }">
              <p>{{ row.showName }}</p>
              <p>{{ row.spec }}</p>
              <p>{{ row.manufacturer }}</p>
              <div v-if="row.imageUrls">
                <!-- <div
                  v-for="(item, index) in row.imageUrls"
                  :key="index"
                >
                  <el-image
                    style="width: 80px;height: 73px;margin-bottom: 4px"
                    :src="item"
                    :preview-src-list="row.imageUrls"
                    @click.prevent
                  />
                </div> -->
                <div v-if="row.imageUrls[0]">
                  <el-image
                    style="width: 80px;height: 73px;margin-bottom: 4px"
                    :src="row.imageUrls[0]"
                    :preview-src-list="[row.imageUrls[0]]"
                    @click.prevent
                  />
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="spec" label="价格">
            <template slot-scope="{ row }">
              <p>单体采购价：{{ row.fob }}</p>
              <p>连锁采购价：{{ row.guidePrice }}</p>
              <p>底价：{{ row.basePrice }}</p>
              <el-button
                  v-if="row.haveAreaPrice === 1"
                  type="text"
                  size="medium"
                  @click="viewAreaPrice(row)"
                  >查看区域价格</el-button
                >
            </template>
          </el-table-column>
          <el-table-column width="140">
            <template slot="header" slot-scope="scope">
              <span>特价价格</span>
              <el-tooltip effect="dark" placement="top">
                <template #content>
                  特惠价格需小于连锁采购价和单体采购价最大值，针对活动价格大于原价的对应活动不生效。
                  <br />示例：单体采购价10，连锁采购价8元，特惠价格可设置9.5元，针对连锁客户特价活动不生效。
                </template>
                <i class="el-icon-warning-outline" />
              </el-tooltip>
            </template>
            <template slot-scope="scope">
              <span>{{ scope.row.skuPrice }}</span>
            </template>
          </el-table-column>
          <el-table-column width="170">
            <template slot="header" slot-scope="scope">
              <span>销售数据</span>
              <el-tooltip effect="dark" placement="top">
                <template #content>
                  销售数据剔除未支付、已取消、已退款且部分退中活动商品应发货数量等于0的订单数据，仅统计已支付的有效订单。可至详情页查询未支付、已取消订单
                </template>
                <i class="el-icon-warning-outline" />
              </el-tooltip>
            </template>
            <template slot-scope="{ row }">
              <p>
                <span style="color: red;">{{ row.isOrder === 1 ? '已成单' : '未成单' }}</span>
                <span
                  v-if="row.isOrder === 1"
                  style="cursor:pointer; font-size: 16px;vertical-align: middle;margin-left: 5px;"
                  @click="handleSeeActivitySaleDataSummaryInfo(row)"
                >
                  <i class="el-icon-view" />
                </span>
              </p>
              <div v-if="row.isOrder === 1 && row.activitySaleDataSummaryInfo">
                <p>
                  <el-tooltip
                    class="item"
                    content="有效订单中包含活动ID，对应客户去重"
                    placement="top"
                  >
                    <i class="el-icon-warning-outline" />
                  </el-tooltip>
                  采购店数：{{ row.activitySaleDataSummaryInfo.purchaseMerchantNum }}
                </p>
                <p>
                  <el-tooltip
                    class="item"
                    content="有效订单中包含特价活动/拼团活动ID，对应订单计数"
                    placement="top"
                  >
                    <i class="el-icon-warning-outline" />
                  </el-tooltip>
                  采购订单数：{{ row.activitySaleDataSummaryInfo.purchaseOrderNum }}
                </p>
                <p>
                  <el-tooltip
                    class="item"
                    content="有效订单、商品行中包含活动ID，取包含对应活动ID的各个商品行【应发货数量=商品数量-已退数量】之和"
                    placement="top"
                  >
                    <i class="el-icon-warning-outline" />
                  </el-tooltip>
                  采购数量：{{ row.activitySaleDataSummaryInfo.purchaseProductNum }}
                </p>
                <p>
                  <el-tooltip
                    class="item"
                    content="有效订单、商品行中包含活动ID，取【实付金额*应发货数量/商品数量=实付金额*（商品数量-已退数量）/商品数量】之和"
                    placement="top"
                  >
                    <i class="el-icon-warning-outline" />
                  </el-tooltip>
                  采购金额：{{ row.activitySaleDataSummaryInfo.purchaseAmount }}
                </p>
                <span
                  style="color: #4183d5; cursor: pointer"
                  @click="handleGoGroupSalesData(row)"
                >详情</span>
              </div>
            </template>
          </el-table-column>
          <!-- <el-table-column
            label="特价价格"
            prop="skuPrice"
          />-->
          <el-table-column prop="availableQty" label="库存" width="120" />
          <el-table-column
            label="限购"
            prop="totalQty"
          >
            <template slot-scope="scope">
              <p>总限购 {{ scope.row.totalQty === -1 ? '不限购' : scope.row.totalQty }}</p>
              <p>单店限购 {{ scope.row.personalQty }}</p>
              <p>是否可超出限购数量：{{ scope.row.isOver ? '是' : '否' }}</p>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
    <viewPrice
    v-if="viewingInventoryLogsVisible"
    :inventory-log-dialog-visible.sync="viewingInventoryLogsVisible"
    :goods-message="goodsMessage"
    >
    </viewPrice>
  </div>
</template>

<script>
import { getSummaryInfo } from '@/api/market/collageActivity';
import { getGoodsDetail } from '../../api/activity/special';
import viewPrice from './components/viewPrice.vue';

export default {
  name: 'SpecialPriceDetail',
  components:{viewPrice},
  data() {
    const checkTime = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('请选择预热时间'));
      } else {
        if (this.basic.timeList[0] && value > new Date(this.basic.timeList[0])) {
          callback(new Error('预热时间必须早于活动开始时间'));
        } else {
          callback();
        }
      }
    };
    const checkName = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('请输入活动名称'));
      } else if (this.nameErrorMsg) {
        callback(new Error(this.nameErrorMsg));
      } else {
        callback();
      }
    };
    return {
      goodsMessage:{},
      viewingInventoryLogsVisible: false,
      quickSearchList: null,
      quickSearchStr: '',
      expireTimeOption: {
        disabledDate(date) {
          return date.getTime() <= Date.now();
        },
      },
      basicRules: {
        title: [
          { required: true, validator: checkName, trigger: 'blur' },
          { max: 50, message: '活动名称限制50', trigger: 'blur' },
        ],
        timeList: [
          { type: 'array', required: true, message: '请选择活动时间', trigger: 'change' },
        ],
        preheatTime: [
          { required: true, validator: checkTime, trigger: 'change' },
        ],
      },
      basic: {
        promotionId: '',
        title: '',
        startTime: '',
        endTime: '',
        preheatTime: '',
        introduction: '',
        timeList: [],
      },
      nameErrorMsg: '',
      radio: '2',
      shopListData: [],
      shopListAllData: [],
      peopleIdStr: '',
      customerGroupId: '',
      isDialog: false,
      isInputValue: null,
      routerObj: '',
    };
  },
  created() {
    if (this.$route.query.promotionId) {
      this.queryInfo();
    }
    // this.queryInfo()
  },
  activated() {
    if (this.$route.query.promotionId) {
      this.queryInfo();
    }
  },
  methods: {
    //查看区域价格
    viewAreaPrice(row) {
      this.goodsMessage = row
      console.log(row);
      this.viewingInventoryLogsVisible=true

    },
    handleGoGroupSalesData(row) {
      // this.$router.push({ path: '/groupSalesData', query: { activityType: 8, marketingIdStr: row.promotionId } });
      window.openTab('/groupSalesData', { activityType: 8, marketingIdStr: row.promotionId });
    },
    handleSeeActivitySaleDataSummaryInfo(row) {
      getSummaryInfo({ activityType: 8, marketingIdStr: row.promotionId, csuId: row.skuId }).then((res) => {
        if (res.success) {
          const { data } = res;
          row.activitySaleDataSummaryInfo = data.summaryInfo || null;
          this.$set(row, row);
        }
      });
    },
    handleQuickSearch(val) {
      if (val === '') {
        this.quickSearchList = this.shopListData;
      } else {
        const arr = [];
        this.shopListData.forEach((item) => {
          if (`${item.skuId}` === val || item.barcode === val || item.showName === val || item.erpCode === val || item.manufacturer === val) {
            arr.push(item);
          }
        });
        this.quickSearchList = arr;
      }
    },
    queryInfo() {
      this.routerObj = this.$route.query;
      if (this.routerObj && this.routerObj.promotionId) {
        getGoodsDetail({ promotionId: this.routerObj.promotionId }).then((res) => {
          if (res.code == 0) {
            const data = res.result;
            this.basic.title = data.title;
            this.basic.preheatTime = data.preheatTime;
            this.basic.startTime = data.startTime;
            this.basic.endTime = data.endTime;
            this.basic.introduction = data.introduction;
            this.basic.promotionId = data.promotionId;
            this.basic.timeList = [data.startTime, data.endTime];
            if (data.customerGroupId) {
              this.radio = '1';
              this.customerGroupId = data.customerGroupId;
              this.peopleIdStr = data.customerGroupDTO.groupName;
            } else {
              this.radio = '2';
            }
            this.shopListData = data.promotionSkuExtendDTOList;
          }
        });
      }
    },
    clearPeople(val) {
      this.peopleIdStr = '';
      this.customerGroupId = '';
    },
  },
};
</script>

<style scoped lang="scss">
.serch {
  font-weight: bold;
  padding-bottom: 5px;
  display: flex;
  justify-content: space-between;
  .sign {
    display: inline-table;
    width: 3px;
    height: 13px;
    background: linear-gradient(360deg, #1d69c4 1%, #8bbdfc 99%);
    border-radius: 2px;
    margin-right: 8px;
  }
}
.padding20 {
  padding: 15px 20px;
}
.list-box {
  display: flex;
  padding-left: 20px;
  .text {
    font-size: 14px;
    font-weight: bold;
  }
  p {
    padding: 0;
    margin: 0;
  }
}
.errorTip {
  padding: 0;
  margin: 0;
  padding-top: 5px;
  color: #ff2121;
}
.upload-demo {
  display: inline-block;
  margin: 0 10px;
}
.chooses {
  ::v-deep  .el-button--primary {
    background: #4183d5;
  }
  ::v-deep  .el-dialog__body {
    padding: 0 0 10px;
  }
  ::v-deep  .el-dialog__header {
    padding: 10px 16px;
    background: #f9f9f9;
  }
  ::v-deep  .el-dialog__headerbtn {
    top: 13px;
  }
  .conten-box {
    padding: 15px 20px;
    display: flex;
  }
}
</style>
