<template>
  <div class="main-box">
    <div class="list-box">
      <div class="explain-search">
        <div class="con-title" style="padding-left: 0">
            <span class="line"></span>
            <span>全部流程</span>
        </div>
        <steps style="padding-bottom: 5px;border-bottom: 1px solid #f0f2f5;" :active="2"></steps>
        <div class="con-title" style="padding-left: 0">
            <span class="line"></span>
            <span>状态信息</span>
        </div>
        <el-row class="googsState">商品默认状态：{{tableTile}}</el-row>

        <div class="con-title" style="padding-left: 0">
          <span class="line"></span><span>控销详情信息设置</span>
          <span>
            <info v-if="true" :info="infodata()" :titleInfo="titleInfo"></info>
          </span>
        </div>
        <div style="padding-top: 10px">
          <el-tabs v-model="activeName" type="card" @tab-click="handleClick" >
            <el-tab-pane label="客户列表" name="zero">
              <el-form size="small" :inline="true" ref="ruleForm">
                <el-form-item class="my-label" label="客户编号">
                  <el-input
                    placeholder="客户编号"
                    v-model.trim="userSearch.id"
                    style="width: 212px"
                  >
                  </el-input>
                </el-form-item>
                <el-form-item class="my-label" label="客户名称">
                  <el-input
                    placeholder="客户名称"
                    v-model.trim="userSearch.realName"
                    style="width: 212px"
                  >
                  </el-input>
                </el-form-item>
                <el-form-item class="my-label" label="手机号码">
                  <el-input
                    placeholder="手机号码"
                    v-model.trim="userSearch.mobile"
                    style="width: 212px"
                  >
                  </el-input>
                </el-form-item>
                <el-form-item class="my-label" label="客户类型">
                  <el-select
                    v-model.trim="userSearch.businessTypeName"
                    placeholder="请选择"
                    style="width: 500px"
                  >
                    <el-option
                      v-for="(item, index) in userTypeList"
                      :label="item.name"
                      :value="item.id"
                      :key="index"
                    ></el-option>
                  </el-select>
                </el-form-item>
                <el-form-item class="search-btn" style="float: right;">
                  <el-button type="primary" @click="getAllUserList('search')"
                    >查询</el-button
                  >
                  <el-button @click="resetAllForm('ruleForm')">重置</el-button>
                </el-form-item>
              </el-form>
            </el-tab-pane>
            <el-tab-pane label="可见可买" name="first">
<!--              <typeActive-->
<!--                :provId="provId"-->
<!--                @setCityList="setCityList"-->
<!--                ref="first"-->
<!--                :fromStr="'first'"-->
<!--                :firstAry="firstAry"-->
<!--                :secondAry="secondAry"-->
<!--                :threeAry="threeAry"-->
<!--              ></typeActive>-->
            </el-tab-pane>
            <!-- <el-tab-pane label="可见不可买" name="second">
              <typeActive
                :provId="provId"
                @setCityList="setCityList"
                ref="second"
                :firstAry="firstAry"
                :secondAry="secondAry"
                :threeAry="threeAry"
                :fromStr="'second'"
              ></typeActive>
            </el-tab-pane> -->
            <el-tab-pane label="不可见不可买" name="three">
              <typeActive
                :provId="provId"
                @setCityList="setCityList"
                ref="three"
                :firstAry="firstAry"
                :secondAry="secondAry"
                :threeAry="threeAry"
                :fromStr="'three'"
              ></typeActive>
            </el-tab-pane>
          </el-tabs>
        </div>
        <div>
          <div class="con-title" style="padding-left: 0">
            <span class="line"></span><span>设置控销用户</span>
          </div>
          <div class="explain-search" style="padding-left: 0">
            <!-- <el-form size="small" :inline="true" ref="ruleForm">
              <el-form-item>
                <el-input
                  placeholder="客户编码"
                  v-model.trim="searchData.buyerId"
                  class="search-input"
                  style="width: 215px"
                >
                  <div slot="prepend">客户编码</div>
                </el-input>
              </el-form-item>
              <el-form-item>
                <el-input
                  placeholder="客户名称"
                  v-model.trim="searchData.name"
                  class="search-input"
                  style="width: 215px"
                >
                  <div slot="prepend">客户名称</div>
                </el-input>
              </el-form-item>
              <el-form-item>
                <el-input
                  placeholder="手机号码"
                  v-model.trim="searchData.mobile"
                  class="search-input"
                  style="width: 215px"
                >
                  <div slot="prepend">手机号码</div>
                </el-input>
              </el-form-item>
              <el-form-item class="my-label">
                <span slot="label">客户类型</span>
                <el-select
                  v-model.trim="searchData.clientType"
                  placeholder="请选择"
                  style="width: 150px"
                >
                  <el-option
                    v-for="(item, index) in userTypeList"
                    :label="item.name"
                    :value="item.id"
                    :key="index"
                  ></el-option>
                </el-select>
              </el-form-item>
              <el-form-item class="search-btn">
                <el-button type="primary" @click="getUserList('search')"
                  >查询</el-button
                >
                <el-button @click="resetForm('ruleForm')">重置</el-button>
              </el-form-item>
            </el-form> -->
            <div class="search-btn my-search">
              <!-- <el-button
                icon="el-icon-circle-plus-outline"
                @click="showUserTip"
                class="add-btn"
                >添加用户</el-button
              > -->
              <span v-if="this.activeName == 'zero'" >
                <el-button class="xyy-blue" type="primary" size="small" @click="sureInnerVisible('first')">移入可见可买</el-button>
                <el-button size="small" @click="sureInnerVisible('three')">移入不可见不可买</el-button>
              </span>
              <span v-else >
                <el-button class="xyy-blue" type="primary" size="small" @click="deleteUserTip" >{{this.btnName}}</el-button>
                <!-- <el-button size="small" @click="exportExcel">导出</el-button> -->
              </span>
            </div>
          </div>
          <!-- v-if="tableData.list.length > 0" -->
          <div class="list-box">
            <el-table
              ref="goodsTable"
              max-height="397"
              :data="tableData.list"
              v-loading="laodingBoole"
              stripe
              style="width: 100%"
              @selection-change="seleteShopHander"
              :header-cell-style="{ background: '#F9F9F9', color: '#666666' }"
            >
              <el-table-column type="selection" width="55"></el-table-column>
              <el-table-column
                prop="id"
                label="客户编号"
              ></el-table-column>
              <el-table-column prop="realName" label="客户名称"></el-table-column>
              <el-table-column prop="mobile" label="手机号"></el-table-column>
              <el-table-column
                prop="businessTypeName"
                label="客户类型"
              ></el-table-column>
              <el-table-column prop="address" label="地址"></el-table-column>
              <el-table-column label="状态">
                <template slot-scope="scope">
                  <span v-if="scope.row.merchantState == 1">激活</span>
                  <span v-else-if="scope.row.merchantState == 2">未激活</span>
                  <span v-else-if="scope.row.merchantState == 3">冻结</span>
                  <span v-else>-</span>
                </template>
              </el-table-column>
              <el-table-column label="创建时间" width="150">
                <template slot-scope="scope">
                  <span>{{getTime(scope.row)}}</span>
                </template>
              </el-table-column>
              <template slot="empty">
                <div class="noData">
                  <p class="img-box">
                    <img
                      src="../../../assets/image/marketing/noneImg.png"
                      alt=""
                    />
                  </p>
                  <p>暂无数据</p>
                </div>
              </template>
            </el-table>
            <div class="explain-pag">
              <Pagination
                v-show="tableData.total > 0"
                :total="tableData.total"
                :page.sync="pageData.pageNum"
                :limit.sync="pageData.pageSize"
                @pagination="getAllUserList"
              ></Pagination>
            </div>
          </div>
        </div>
      </div>

      <!-- <el-dialog
        title="选择控销用户"
        :visible.sync="outerVisible"
        class="my-dialog"
        :modal-append-to-body="false"
        :close-on-click-modal="false"
      >
        <div class="explain-search" style="padding-top: 20px">
          <el-form size="small" :inline="true" ref="ruleForm">
            <el-form-item>
              <el-input
                placeholder="客户编码"
                v-model.trim="userSearch.buyerId"
                class="search-input"
                style="width: 215px"
              >
                <div slot="prepend">客户编码</div>
              </el-input>
            </el-form-item>
            <el-form-item>
              <el-input
                placeholder="客户名称"
                v-model.trim="userSearch.name"
                class="search-input"
                style="width: 215px"
              >
                <div slot="prepend">客户名称</div>
              </el-input>
            </el-form-item>
            <el-form-item>
              <el-input
                placeholder="手机号码"
                v-model.trim="userSearch.mobile"
                class="search-input"
                style="width: 215px"
              >
                <div slot="prepend">手机号码</div>
              </el-input>
            </el-form-item>
            <el-form-item class="my-label">
              <span slot="label">客户类型</span>
              <el-select
                v-model.trim="userSearch.clientType"
                placeholder="请选择"
                style="width: 150px"
              >
                <el-option
                  v-for="(item, index) in userTypeList"
                  :label="item.name"
                  :value="item.id"
                  :key="index"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item class="search-btn">
              <el-button type="primary" @click="getAllUserList('search')"
                >查询</el-button
              >
              <el-button @click="resetAllForm('ruleForm')">重置</el-button>
            </el-form-item>
          </el-form>
        </div>
        <div class="explain-table">
          <el-table
            max-height="397"
            :data="userData.list"
            v-loading="laodingUser"
            stripe
            style="width: 100%"
            @selection-change="seleteUserHander"
            :header-cell-style="{ background: '#F9F9F9', color: '#666666' }"
          >
            <el-table-column type="selection" width="55"></el-table-column>
            <el-table-column prop="buyerId" label="客户编号"></el-table-column>
            <el-table-column prop="name" label="客户名称"></el-table-column>
            <el-table-column
              prop="clientTypeName"
              label="客户类型"
            ></el-table-column>
            <template slot="empty">
              <div class="noData">
                <p class="img-box">
                  <img
                    src="../../../assets/image/marketing/noneImg.png"
                    alt=""
                  />
                </p>
                <p>暂无数据</p>
              </div>
            </template>
          </el-table>
          <div class="explain-pag">
            <Pagination
              v-show="userData.total > 0"
              :total="userData.total"
              :page.sync="userPage.pageNum"
              :limit.sync="userPage.pageSize"
              @pagination="getAllUserList"
            ></Pagination>
          </div>
        </div>
        <div slot="footer" class="dialog-footer">
          <el-button @click="outerVisibleClick" size="small">取 消</el-button>
          <el-button
            type="primary"
            class="xyy-blue"
            @click="sureInnerVisible"
            size="small"
            >确定</el-button
          >
        </div>
      </el-dialog> -->
    </div>
    <el-row
      class="search-btn"
      type="flex"
      justify="end"
      align="middle"
      style="padding-top: 10px;padding-right: 20px"
    >
      <el-button size="small" @click="PreStep">上一步</el-button>
      <!-- <el-button size="small" @click="sendData" type="primary">提交</el-button> -->
    </el-row>
  </div>
</template>
<style scoped>
@import '../../../assets/css/changeElement.scss';
.my-search .el-button {
  border-radius: 4px;
  border-radius: 4px;

  padding: 8px 20px;
}
.my-search .add-btn {
  border: 1px solid #4183d5;
  color: #4183d5;
}
</style>
<script>
import Pagination from '@/components/Pagination'
import typeActive from '../components/typeActive'
import steps from './../components/steps';
import info from '@/components/info/info'
import utils from '@/utils/filter';
import {
  searchMerchantInfo,
  findUserTypes,
  listsExport,
  shopSkuDafaultType
} from '@/api/goods/controlGoods.js'
// import { getUserType, getUserAllList } from '@/api/goods/newGood.js'
export default {
  name: 'controlRules',
  props: {
    provId: {
      type: String,
      default: ''
    },
    rulesEditList: {
      type: Array,
      default: () => []
    }
  },
  components: { Pagination, typeActive ,steps,info},
  data() {
    return {
      activeName: 'zero',
      searchData: {
        id: '',
        realName: '',
        businessTypeName: '',
        mobile: ''
      },
      pageData: {
        pageNum: 1,
        pageSize: 20
      },
      tableData: {
        allList: [],
        list: [],
        total: 0
      },
      userTypeList: [],
      laodingBoole: false,
      userData: {
        list: [],
        total: 0
      },
      userPage: {
        pageNum: 1,
        pageSize: 20
      },
      userSearch: {
        id: '',
        realName: '',
        businessTypeName: '',
        mobile: '',
        blackMerchantIds:[]
      },
      selectUserAll: [],
      checkUserList: [],
      outerVisible: false,
      laodingUser: false,
      firstAry: [],
      secondAry: [],
      threeAry: [],
      firstObj: {
        jointList: [],
        buyerList: [],
        parentId: ''
      },
      secondObj: {
        jointList: [],
        buyerList: [],
        parentId: ''
      },
      threeObj: {
        jointList: [],
        buyerList: [],
        parentId: ''
      },
      newEdit: [],
      tableTile:"可见可买",
      btnName:'',
      titleInfo:'控销规则',
      controlId:''
    }
  },
  watch: {
    rulesEditList: {
      deep: true,
      handler: function(newVal) {
        this.newEdit = JSON.parse(JSON.stringify(newVal))
        this.setDataAll()
      }
    }
  },
  created() {
    let that = this
    const list = [];
    if (this.rulesEditList.length > 0) {
      this.rulesEditList.map(function(item) {
        if (item.controlResult == 3) {
          that.firstObj.parentId = item.id
          that.firstObj.buyerList = item.buyerList
          that.firstObj.jointList = item.jointList
          that.aryClick(item.jointList, item.controlResult)
        } else if (item.controlResult == 1) {
          that.secondObj.parentId = item.id
          that.secondObj.buyerList = item.buyerList
          that.secondObj.jointList = item.jointList
          that.aryClick(item.jointList, item.controlResult)
        } else {
          that.threeObj.parentId = item.id
          that.threeObj.buyerList = item.buyerList
          that.threeObj.jointList = item.jointList
          that.aryClick(item.jointList, item.controlResult)
        }
      })
    }
    // this.findUserTypes();
    // this.getAllUserList();
    // this.shopSkuDafaultType();
  },
  activated () {

  },
  methods: {
    infodata() {
      return [
        {
        title: '客户列表为已开户的客户名单'
        },
        {
        title: '支持在可见可买、不可见不可买设置控销区域+客户类型',
        info: '例如：不可见不可买内设置武汉市+单体药店，则武汉市单体药店用户对设置商品不可见不可买'
        },
        {
        title: '区域+客户类型不可重复出现在可见可买、不可见不可买菜单内',
        info: '例如：可见可买内添加武汉市+单体药店，则不可见不可买内不支持再次添加武汉市+单体药店'
        },
        {
        title: '区域+客户类型控销区/县+客户类型优先级大于市+客户类型',
        info: '例如：武汉市+单体药店、连锁药店可见可买，武汉市(汉阳区)+单体药店可见可买 则武汉市武汉市(汉阳区)+单体药店控销优先级高'
        },
        {
        title: '客户控销优先级大于区域+客户类型控销'
        }
        ,
        {
        title: '一个商品只能有一条生效的控销数据'
        }
      ];
    },
    setDataAll() {
      let that = this
      const list = [];
      if (this.newEdit.length > 0) {
        this.newEdit.map(function(item) {
          if (item.controlResult == 3) {
            that.firstObj.parentId = item.id
            that.firstObj.buyerList = item.buyerList
            that.firstObj.jointList = item.jointList
            that.aryClick(item.jointList, item.controlResult)
            that.setblackMerchantIds(item.buyerList);
          } else if (item.controlResult == 1) {
            that.secondObj.parentId = item.id
            that.secondObj.buyerList = item.buyerList
            that.secondObj.jointList = item.jointList
            that.aryClick(item.jointList, item.controlResult)
            that.setblackMerchantIds(item.buyerList);
          } else {
            that.threeObj.parentId = item.id
            that.threeObj.buyerList = item.buyerList
            that.threeObj.jointList = item.jointList
            that.aryClick(item.jointList, item.controlResult)
            that.setblackMerchantIds(item.buyerList);
          }
        })
      } else {
        that.firstObj.parentId = ''
        that.firstObj.buyerList = []
        that.firstObj.jointList = []
        that.aryClick([], 3)
        that.secondObj.parentId = ''
        that.secondObj.buyerList = []
        that.secondObj.jointList = []
        that.aryClick([], 1)
        that.threeObj.parentId = ''
        that.threeObj.buyerList = []
        that.threeObj.jointList = []
        that.aryClick([], 2)
      }
      this.findUserTypes();
      this.getAllUserList();
      this.shopSkuDafaultType();
    },
    setblackMerchantIds(val){
      if (val.length > 0) {
        let ids = [];
        val.map(function(item) {
            ids.push(item.id)
        })
        this.userSearch.blackMerchantIds.push(...ids);
      }
    },
    aryClick(val, from) {
      let that = this
      if (val.length > 0) {
        val.map(function(item) {
          let obj = {
            userIds: '',
            cityIds: '',
            cityStr: '',
            userStr: '',
            cityAry: [],
            userAry: [],
            id: ''
          }
          obj.userIds += item.businessType
          obj.cityIds += item.areaCode
          obj.cityStr += item.areaCodeName
          obj.userStr += item.businessTypeName
          obj.id += item.id
          obj.cityAry = item.areaCode.split(',')
          obj.userAry = item.businessType.split(',')
          if (from == 3) {
            that.firstAry.push(obj)
          } else if (from == 1) {
            that.secondAry.push(obj)
          } else {
            that.threeAry.push(obj)
          }
        })
      } else {
        if (from == 3) {
          that.firstAry = []
        } else if (from == 1) {
          that.secondAry = []
        } else {
          that.threeAry = []
        }
      }
    },
    handleClick(val) {
      if (this.activeName == 'zero'){
        this.tableData.allList = this.userData.list;
      }else if (this.activeName == 'first') {
        this.tableData.allList = this.removeDuplicateObject(
          this.firstObj.buyerList
        )
        this.btnName = '移出可见可买'
      } else if (this.activeName == 'second') {
        this.tableData.allList = this.removeDuplicateObject(
          this.secondObj.buyerList
        )
        this.btnName = '移出可见不可买'
      } else if(this.activeName == 'three'){
        this.tableData.allList = this.removeDuplicateObject(
          this.threeObj.buyerList
        )
        this.btnName = '移出不可见不可买'
      }
      this.tableData.list = this.tableData.allList.slice(0,this.pageData.pageSize)
      if(this.activeName != 'zero'){
        this.tableData.total = this.tableData.allList.length
      }else{
        this.tableData.total = this.userData.total
      }
    },
    //已选用户选择用户
    seleteShopHander(val) {
      this.checkUserList = val
    },
    //添加用户 -- 确认选择
    sureInnerVisible(activeName) {
      let that = this
      let ids = [];
      if (this.checkUserList.length > 0) {
        this.checkUserList.map(function(item) {
          if (activeName == 'first') {
            let buyerList = that.firstObj.buyerList
            if (!buyerList.some((val) => val.id === item.id)) {
              buyerList.push(item)
              ids.push(item.id);
            }
          } else if (activeName == 'second') {
            let buyerList = that.secondObj.buyerList
            if (!buyerList.some((val) => val.id === item.id)) {
              buyerList.push(item)
              ids.push(item.id);
            }
          } else if(activeName == 'three'){
            let buyerList = that.threeObj.buyerList
            if (!buyerList.some((val) => val.id === item.id)) {
              buyerList.push(item)
              ids.push(item.id);
            }
          }
        })

        this.userSearch.blackMerchantIds.push(...ids);
        this.getAllUserList();

        this.$refs.goodsTable.clearSelection();
        that.$message({
            message: '移入成功',
            type: 'success',
        });
        // if (activeName == 'first') {
        //   that.tableData.allList = this.removeDuplicateObject(
        //     that.firstObj.buyerList
        //   )
        //   // console.log(that.tableData.allList,'00000')
        // } else if (activeName == 'second') {
        //   that.tableData.allList = this.removeDuplicateObject(
        //     that.secondObj.buyerList
        //   )
        // } else if(activeName == 'three'){
        //   that.tableData.allList = this.removeDuplicateObject(
        //     that.threeObj.buyerList
        //   )
        // }

        // that.tableData.list = that.tableData.allList.slice(0,this.pageData.pageSize)
        // that.tableData.total = that.tableData.allList.length
        // that.outerVisible = false
      } else {
        this.$message({
          message: '请先选择用户',
          type: 'warning'
        })
      }
    },
    // 移出
    deleteUserTip() {
      if (this.checkUserList.length > 0) {
        this.$confirm('确认移除已选用户?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: ''
        })
          .then(() => {
            // let ids = this.userSearch.blackMerchantIds;
            // let i = this.checkUserList.length;
            // while(i--){
            //   if(ids.indexOf(this.checkUserList[i].id) > -1){
            //     ids.splice(i,1);
            //   }
            // }

            let ids = [];
            if(this.userSearch.blackMerchantIds.length){
                ids = this.userSearch.blackMerchantIds;
                for(let i=0;i<this.checkUserList.length;i++){
                  for(let j=0;j<ids.length;j++){
                    if(this.checkUserList[i].id == ids[j]){
                      ids.splice(j,1);
                      j--;
                    }
                  }
                }
            }
            this.userSearch.blackMerchantIds = ids;
            this.getAllUserList();

            let set = this.checkUserList.map((item) => item.id)
            let resArr = this.tableData.allList.filter(
              (item) => !set.includes(item.id)
            )
            if(resArr.length<=0){
              this.checkUserList = [];
            }
            this.tableData.allList = resArr
            this.tableData.list = this.tableData.allList.slice(0, this.pageData.pageSize)
            this.tableData.total = this.tableData.allList.length
            if (this.activeName == 'first') {
              this.firstObj.buyerList = resArr
            } else if (this.activeName == 'second') {
              this.secondObj.buyerList = resArr
            } else if(this.activeName == 'three'){
              this.threeObj.buyerList = resArr
            }
          })
          .catch(() => {})
      } else {
        this.$message({
          message: '请先选择用户',
          type: 'warning'
        })
      }
    },
    //所有用户
    getAllUserList() {
      let blackMerchantIds = this.userSearch.blackMerchantIds;
      blackMerchantIds = new Set(blackMerchantIds);
      blackMerchantIds = Array.from(blackMerchantIds)

      let param = {
        ...this.userSearch,
        ...this.pageData,
        blackMerchantIds: blackMerchantIds.join(','),
      }
      searchMerchantInfo(param).then((res) => {
        if (res.code == 0) {
          this.userData.list = res.data.list
          this.userData.total = res.data.total
          this.handleClick();
        }else{
          this.$message({
                message: res.message,
                type: 'error'
            })
        }
      })
    },
    getTime(vel){
        return utils.dataTime(
            vel.createTime,
            'yy-mm-dd HH:ss:nn'
          );
    },
    // 获取用户类型
    findUserTypes(){
       findUserTypes().then((res) => {
        if (res.code == 0) {
          this.userTypeList = res.data;
        }else{
          this.$message({
          message: res.message,
          type: 'warning'
        })
        }
      })
    },
    // 获取可见可买状态
    shopSkuDafaultType(){
       shopSkuDafaultType().then((res) => {
        if (res.code == 0) {
          if(res.data.csuSaleType ==0){
              this.tableTile = '可见可买'
          }else if(res.data.csuSaleType ==1){
              this.tableTile = '可见不可买'
          }else if(res.data.csuSaleType ==2){
              this.tableTile = '不可见不可买'
          }
        }else{
          this.$message({
          message: res.message,
          type: 'warning'
        })
        }
      })
    },
    //重置
    resetAllForm() {
      this.pageData = {
        pageNum: 1,
        pageSize: 20
      }
      this.userSearch = {
        id: '',
        realName: '',
        businessTypeName: '',
        mobile: ''
      }
      this.getAllUserList()
    },
     // 导出客户列表
    exportExcel(){
      const that = this;
      if (!that.tableData.allList || !that.tableData.allList.length) {
        that.$message({
          type: 'info',
          message: '无导出数据！'
        });
        return false;
      }
      const param = {
        controlId: this.controlId, // 控销ID
        rosterType:10001 // 用户类型 10001-白名单 10002-黑名单
      };
      let url = ``;
      listsExport(param).then(res => {
        if (res.hasOwnProperty('code')) {
          that.$message.error(res.message);
        } else {
          // 申请接口带入参数查询数据
          url = `${process.env.VUE_APP_BASE_API}/salesControl/listsExport?controlId=${this.controlId}&rosterType=10001`;
          const a = document.createElement('a');
          a.href = url;
          a.click();
        }
      });
    },
    //区域
    setCityList(val) {
      let that = this
      if (this.activeName == 'first') {
        that.firstObj.jointList = []
        this.firstAry = []
        val.map(function(item, index) {
          let obj = {}
          obj.areaCode = item.cityIds
          obj.businessType = item.userIds
          that.firstObj.jointList.push(obj)
        })
        this.firstAry = val
      } else if (that.activeName == 'second') {
        that.secondObj.jointList = []
        this.secondAry = []
        val.map(function(item, index) {
          let obj = {}
          obj.areaCode = item.cityIds
          obj.businessType = item.userIds
          that.secondObj.jointList.push(obj)
        })
        this.secondAry = val
      } else if(that.activeName == 'three'){
        that.threeObj.jointList = []
        this.threeAry = []
        val.map(function(item, index) {
          let obj = {}
          obj.areaCode = item.cityIds
          obj.businessType = item.userIds
          that.threeObj.jointList.push(obj)
        })
        this.threeAry = val
      }
    },
    //上一步
    PreStep() {
      this.$emit('goPrev', { from: 'last' })
    },
    //提交
    sendData() {

      let ruleList = {
        whitePlan:[],
        hidePlan:[],
        controlSaleWhiteMerchantEntityList:[],
        controlSaleHideMerchantEntityList:[]
      }
      let that = this
      this.firstObj.jointList.map(function(item) {
        let firstAreaCodes = item.areaCode.split(',');
        let firstUserTypes = item.businessType.split(',');

        let whitePlanObj={};
        if(firstAreaCodes.length){
          whitePlanObj.areaCodes = firstAreaCodes;
        }
        if(firstUserTypes.length){
          whitePlanObj.userTypes = firstUserTypes;
        }
        if(that.firstObj.parentId){
          whitePlanObj.id = that.firstObj.parentId;
        }

        if(whitePlanObj.hasOwnProperty('areaCodes') || whitePlanObj.hasOwnProperty('userTypes') || whitePlanObj.hasOwnProperty('id')){
          ruleList.whitePlan.push(whitePlanObj);
        }
      })

      this.threeObj.jointList.map(function(item) {
        let threeAreaCodes=item.areaCode.split(',');
        let threeUserTypes = item.businessType.split(',');

        let hidePlanObj={};
        if(threeAreaCodes.length){
          hidePlanObj.areaCodes = threeAreaCodes;
        }
        if(threeUserTypes.length){
          hidePlanObj.userTypes = threeUserTypes;
        }
        if(that.threeObj.parentId){
          hidePlanObj.id = that.threeObj.parentId;
        }

        if(hidePlanObj.hasOwnProperty('areaCodes') || hidePlanObj.hasOwnProperty('userTypes')|| hidePlanObj.hasOwnProperty('id')){
          ruleList.hidePlan.push(hidePlanObj);
        }
      })

      let controlSaleWhiteMerchantEntityList = [];
        this.firstObj.buyerList.map(function(item) {
        controlSaleWhiteMerchantEntityList.push(item.id);
      })

      let controlSaleHideMerchantEntityList = [];
        this.threeObj.buyerList.map(function(item) {
        controlSaleHideMerchantEntityList.push(item.id);
      })

      if(controlSaleWhiteMerchantEntityList.length){
        ruleList.controlSaleWhiteMerchantEntityList = controlSaleWhiteMerchantEntityList;
      }
      if(controlSaleHideMerchantEntityList.length){
        ruleList.controlSaleHideMerchantEntityList = controlSaleHideMerchantEntityList;
      }

      // let firstAreaCodes = [];
      // let firstUserTypes = [];
      // this.firstObj.jointList.map(function(item) {
      //   firstAreaCodes.push(item.areaCode);
      //   firstUserTypes.push(item.businessType);
      // })

      // let threeAreaCodes = [];
      // let threeUserTypes = [];
      // this.threeObj.jointList.map(function(item) {
      //   threeAreaCodes.push(item.areaCode);
      //   threeUserTypes.push(item.businessType);
      // }

      // let whitePlanObj={};
      // if(firstAreaCodes.length){
      //   whitePlanObj.areaCodes = firstAreaCodes;
      // }
      // if(firstUserTypes.length){
      //   whitePlanObj.userTypes = firstUserTypes;
      // }
      // if(this.firstObj.parentId){
      //   whitePlanObj.id = this.firstObj.parentId;
      // }
      // if(whitePlanObj.hasOwnProperty('areaCodes') || whitePlanObj.hasOwnProperty('userTypes') || whitePlanObj.hasOwnProperty('id')){
      //   ruleList.whitePlan = [];
      //   ruleList.whitePlan.push(whitePlanObj);
      // }

      // let hidePlanObj={};
      // if(threeAreaCodes.length){
      //   hidePlanObj.areaCodes = threeAreaCodes;
      // }
      // if(threeUserTypes.length){
      //   hidePlanObj.userTypes = threeUserTypes;
      // }
      // if(this.threeObj.parentId){
      //   hidePlanObj.id = this.threeObj.parentId;
      // }
      // if(hidePlanObj.hasOwnProperty('areaCodes') || hidePlanObj.hasOwnProperty('userTypes') || hidePlanObj.hasOwnProperty('id')){
      //   ruleList.hidePlan = [];
      //   ruleList.hidePlan.push(hidePlanObj);
      // }


      // let ruleList = [
      //   {
      //     controlResult: 3,
      //     jointList: this.firstObj.jointList,
      //     buyerList: this.firstObj.buyerList,
      //     id: this.firstObj.parentId ? this.firstObj.parentId : ''
      //   },
      //   {
      //     controlResult: 1,
      //     jointList: this.secondObj.jointList,
      //     buyerList: this.secondObj.buyerList,
      //     id: this.secondObj.parentId ? this.secondObj.parentId : ''
      //   },
      //   {
      //     controlResult: 2,
      //     jointList: this.threeObj.jointList,
      //     buyerList: this.threeObj.buyerList,
      //     id: this.threeObj.parentId ? this.threeObj.parentId : ''
      //   }
      // ]
      this.$emit('sendDataRules', ruleList)
    },
    //去掉重复数据
    removeDuplicateObject(arr) {
      let temp = arr.map((item) => {
        return JSON.stringify(item)
      })
      temp = Array.from(new Set(temp))
      return temp.map((item) => {
        return JSON.parse(item)
      })
    },
    //已选用户查询
    getUserList() {
      let allList = JSON.parse(JSON.stringify(this.tableData.allList))

      if (this.searchData.id) {
        allList = allList.filter(
          (item, index) => item.id.indexOf(this.searchData.id) > -1
        )
      } else if (this.searchData.name) {
        allList = allList.filter(
          (item, index) => item.name.indexOf(this.searchData.name) > -1
        )
      } else if (this.searchData.businessType) {
        allList = allList.filter(
          (item, index) =>
            item.businessType.indexOf(this.searchData.businessType) > -1
        )
      } else if (this.searchData.mobile) {
        allList = allList.filter(
          (item, index) => item.mobile.indexOf(this.searchData.mobile) > -1
        )
      } else {
        allList = JSON.parse(JSON.stringify(this.tableData.allList))
      }

      this.tableData.list = allList.slice(
        (this.pageData.pageNum - 1) * this.pageData.pageSize,
        this.pageData.pageNum * this.pageData.pageSize
      )
      this.tableData.total = allList.length
    },
     showUserTip() {
      this.outerVisible = true
      this.getAllUserList()
    },
     seleteUserHander(val) {
      this.selectUserAll = val
    },
    //取消用户选择
    outerVisibleClick() {
      this.outerVisible = false
    },
     //已选用户重置
    resetForm() {
      this.searchData = {
        id: '',
        realName: '',
        businessTypeName: '',
        mobile: ''
      }
      this.pageData = {
        pageNum: 1,
        pageSize: 20
      }
      this.tableData.list = this.tableData.allList.slice(0, this.pageData.pageSize)
      this.tableData.total = this.tableData.allList.length
    }
  }
}
</script>

<style lang="scss" scoped>
// @import '../../../assets/css/market';
.btn-dib {
  display: inline-block;
  margin: 0 10px;
}

.googsState {
    color: #333333;
    font-size: 12px;
    padding-bottom: 15px;
    padding-top: 20px;
    border-bottom: 1px solid #f0f2f5;
  }

.con-inner {
  padding-top: 15px;
  padding-left: 23px;
  margin-right: 17px;
  padding-bottom: 10px;
  border-bottom: 1px solid #efefef;

  div {
    display: inline-block;
  }

  .img {
    width: 92px;
    height: 92px;

    img {
      width: 100%;
      height: 100%;
    }
  }

  .text {
    padding-left: 20px;
    vertical-align: top;

    h3 {
      font-size: 14px;
      color: #000000;
      padding: 0;
      margin: 0;
    }

    p {
      padding: 0;
      margin: 0;
      font-size: 12px;
      color: #333333;
      padding-top: 10px;
    }
  }

  .btn {
    float: right;
    padding-top: 26px;

    button {
      width: 100px;
      height: 30px;
      text-align: center;
      line-height: 30px;
      padding: 0;
      background: rgba(65, 131, 213, 1);
      border-color: rgba(65, 131, 213, 1);
      border-radius: 4px;
      font-size: 14px;
    }

    a {
      color: #ffffff;
      text-decoration: none;
    }

    .router-link-active {
      color: #ffffff;
      text-decoration: none;
    }
  }
}

.pag-info {
  width: 500px;
}
::v-deep   .my-label .el-form-item__label {
  border: 1px solid #d9d9d9;
  border-radius: 4px 0px 0px 4px;
  padding: 0 9px 0 9px;
  height: 30px;
  vertical-align: bottom;
  border-right: 0;
  font-size: 12px;
  line-height: 28px;
}
::v-deep   .my-label .el-input__inner {
  border-radius: 0px 4px 4px 0px;
}

::v-deep   .el-range-editor--small.el-input__inner {
  height: 30px;
}
::v-deep   .el-input--small .el-input__inner {
  height: 30px;
  line-height: 30px;
  font-size: 12px;
}
::v-deep  .el-table {
  .el-checkbox__inner {
    border: 1px solid #000000;
  }
}
</style>
