<template>
  <el-dialog
    title="客户信息"
    :visible="true"
    width="800px"
    :footer="null"
    :before-close="cancel"
    :append-to-body="true"
  >
    <div class="topInfo">
      <h3 style="color: #000;">
        人群基本信息
      </h3>
      <div style="marginBottom: 5px;">
        <span class="infoTitle"> 人群ID：</span>
        {{ marketCustomerGroupId }}
      </div>
      <div style="marginBottom: 5px;">
        <span class="infoTitle"> 人群名称：</span>
        {{ customerInfo.name }}
      </div>
      <div style="marginBottom: 5px;">
        <span class="infoTitle"> 人群定义：</span>
        <div v-for="(item, index) in customerInfo.tagDefs || []" :key="index">
          <p style="margin-left: 30px" v-for="item2 in item" :key="item2">
            {{ item2 }}
          </p>
        </div>
      </div>
    </div>
    <hr>
    <div class="tableInfo">
      <h3 style="color: #000;">
        客户列表（动态人群则只能查询此刻所包含客户）
      </h3>
      <el-form :model="searchForm" ref="listQuery">
        <div class="row">
          <div class="rowItem">
            <div class="itemTitle">
              药店编号：
            </div>
            <el-input
              v-model.trim="searchForm.merchantIdStr"
              placeholder="请输入"
              size="small"
            />
          </div>
          <div class="rowItem">
            <div class="itemTitle">
              药店名称：
            </div>
            <el-input
              v-model.trim="searchForm.realName"
              placeholder="请输入"
              size="small"
            />
          </div>
          <div class="rowItem">
            <div class="itemTitle">
              手机号：
            </div>
            <el-input
              v-model.trim="searchForm.mobile"
              placeholder="请输入"
              size="small"
            />
          </div>
        </div>
        <div class="rowBtn">
          <div class="btnBox">
            <el-button
              type="primary"
              size="small"
              @click="toSearch"
            >
              查询
            </el-button>
            <el-button
              style="marginLeft: 8px;"
              size="small"
              @click="toReset"
            >
              重置
            </el-button>
          </div>
        </div>
      </el-form>
      <el-table
        class="main-table"
        :loading="loading"
        :row-key="record => record.merchantId"
        :scroll="{ x: 500, y: 400 }"
        border
        stripe
        :data="dataSource"
        :header-cell-style="{ background: '#f9f9f9' }"
      >
        <el-table-column prop="merchantId" label="药店编号" />
        <el-table-column prop="realName" label="药店名称" />
        <el-table-column prop="mobile" label="手机号" />
        <el-table-column prop="statusShowName" label="状态" />
        <el-table-column prop="licenseStatusShowName" label="资质审核状态" />
      </el-table>
      <div
        v-if="total != 0"
        class="pagination-container"
      >
        <div class="pag-text">
          共 {{ total }} 条数据，每页{{ 10 }}条，共{{
            Math.ceil(total / 10)
          }}页
        </div>
        <el-pagination
          background
          :page-sizes="[10]"
          prev-text="上一页"
          next-text="下一页"
          layout="sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="pagerChange"
          @current-change="pagerChange"
        />
      </div>
    </div>
    <span slot="footer">
      <el-button type="primary" size="medium" @click="cancel">关闭</el-button>
    </span>
  </el-dialog>
</template>

<script>
  import { getCustomerInfo, getMerchantList } from '@/api/market/collageActivity';

  export default {
    name: "CusomerInfoLog",
    props: {
      cancelModal: {
        type: Function,
        default: () => {},
      },
      marketCustomerGroupId: {
        type: String | Number,
        default: '',
      },
      // customerInfo: {
      //   type: Object,
      //   default: {},
      // }
    },
    data() {
      return {
        pageNum: 1,
        pageSize: 10,
        total: 0,
        loading: false,
        dataSource: [],
        searchForm: {
          merchantIdStr: '',
          realName: '',
          mobile: '',
        },
        customerInfo: {
          name: '',
          tagDefs: [],
        }
      }
    },
    mounted() {
      this.crowdInfoLists();
    },
    methods: {
      crowdInfoLists () {
        getCustomerInfo({ id: this.marketCustomerGroupId, pageNum: 1, pageSize: 10 }).then((res) => {
          if (res.code === 1000) {
            const { info } = res.data || {};
            this.customerInfo = {
              name: info.tagName || '',
              tagDefs: info.contentBundleDescriptions || [],
            }
          }
        }).catch((err) => {
          this.$message.error(err);
        });
      },
      getList() {
        this.loading = true;
        const params = {
          pageNum: this.pageNum,
          pageSize: this.pageSize,
          merchantIdStr: this.searchForm.merchantIdStr,
          realName: this.searchForm.realName,
          mobile: this.searchForm.mobile,
          customerGroupId: this.marketCustomerGroupId,
        };
        if (params.merchantIdStr === '' && params.realName === '' && params.mobile === '') {
          this.$message.error('请选择查询条件：药店编码、药店名称、手机号入参错误');
        }
        getMerchantList(params).then((res) => {
          this.loading = false;
          if (res.code === 1000) {
            this.dataSource = res.data.merchants || [];
            this.total = res.data.totalCount || 0;
          }
        }).catch(() => {
          this.loading = false;
        });
      },
      // 关闭弹框
      cancel() {
        this.$emit('cancelModal', false);
      },
      pagerChange(page, size) {
        this.pageNum = page;
        this.pageSize = size;
        this.getList();
      },
      toSearch() {
        this.getList();
      },
      toReset() {
        this.searchForm = {
          merchantIdStr: '',
          realName: '',
          mobile: '',
        };
        setTimeout(() => {
          this.getList();
        }, 800);
      },
    }
  }
</script>

<style lang="scss" scoped>
  .row {
    display: flex;
    align-items: center;
    margin-top: 12px;
    .rowItem {
      display: flex;
      height: 34px;
      .itemTitle {
        margin: 6px 7px;
        min-width: 70px;
        text-align: center;
      }
    }
  }
  .main-table {
    margin-top: 5px;
    margin-bottom: 5px;
  }
  .topInfo {
    margin-bottom: 10px;
    .infoTitle {
      font-size: 14px;
      font-weight: 600;
    }
  }
  .rowBtn {
    display: flex;
    align-items: center;
    margin: 12px 0;
    justify-content: flex-end;
  }
</style>
