<template>
  <div class="platform-server-page">
    <div class="qual-state" v-if='!ifModify'>
      <div >
          <span class="h">电子签状态:</span>
          <span :class="stateClass">{{ stateText }}</span>
          <span class="t" v-if="!this.state" v-html="stateTip" />
          <span class="t" v-if="showText">{{ showText }}</span>
          <!--        <span>去上传</span>-->
      </div>
      <div class="header-btn">
        <el-button size="small" @click="isShowAccount = true">查看账号</el-button>
        <el-button size="small" @click="isShowSignProve = true">电子签说明</el-button>
        <el-button type="primary" size="small" v-if="!this.state" @click="openElectronicSign">开通电子签服务</el-button>
      </div>
    </div>
    <div class="search-form-content">
      <el-row
        :gutter="20"
        style="padding: 0 20px"
        class="searchMy"
      >
        <el-form
          ref="formModel"
          :inline="true"
          :model="formModel"
          size="small"
        >
          <el-form-item>
            <span
              class="search-title"
            >任务编号</span>
            <el-input
              v-model="formModel.taskId"
              placeholder="请输入内容"
              size="small"
            >
            </el-input>
          </el-form-item>
          <el-form-item>
            <span
              class="search-title"
            >签署任务主题</span>
            <el-input
              v-model="formModel.taskTopic"
              placeholder="请输入内容"
              size="small"
            >
            </el-input>
          </el-form-item>
          <el-form-item prop="time">
            <span
              class="search-title"
            >失效时间</span>
            <div style="display: table-cell; line-height: 24px">
              <el-date-picker
                v-model="time"
                type="daterange"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :default-time="['00:00:00', '23:59:59']"
                @change="dateOnChange"
              />
            </div>
          </el-form-item>
          <el-form-item prop="templateType">
            <span
              class="search-title"
            >状态</span>
            <el-select
              v-model="formModel.taskStatus"
              size="small"
            >
              <el-option
                label="全部"
                value=""
              />
              <el-option
                v-for="(item , index) in taskStatusOption"
                :key="index"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-form>
        <el-row style="text-align: right;padding-bottom: 10px">
          <div class="FsearchBtn">
            <el-button
                size="small"
                @click="resetForm()"
            >
              重置
            </el-button>
            <el-button
                type="primary"
                size="small"
                @click="toSerachForm()"
            >
              查询
            </el-button>
          </div>
        </el-row>
      </el-row>
    </div>
    <div class="search-form-table">
      <el-table
        v-loading="isLoading"
        :data="tableData"
        stripe
        border
        fit
        style="width: 100%"
        :header-cell-style="{ background: '#F9F9F9', color: '#666666' }"
      >
        <el-table-column
          prop="taskId"
          label="任务编号"
          width="150"
        />
        <el-table-column
          prop="taskTopic"
          label="签署任务主题"
        >
        </el-table-column>
        <el-table-column
          prop="effectTime"
          label="生效时间"
          width="250"
        >
          <template slot-scope="scope">
            {{ exDate(scope.row.effectTime) }}
          </template>
        </el-table-column>
        <el-table-column
          prop="failureTime"
          label="失效时间"
          width="250"
        >
          <template slot-scope="scope">
            {{ exDate(scope.row.failureTime) }}
          </template>
        </el-table-column>
        <el-table-column
          prop="signTime"
          label="任务完成/关闭时间"
        >
          <template slot-scope="scope">
            {{ exDate(scope.row.signTime) }}
          </template>
        </el-table-column>
        <el-table-column
          prop="updateTime"
          label="状态"
          width="180"
        >
          <template slot-scope="scope">
            <div :class="exClass(scope.row.taskStatus)">{{ taskStatusOption[scope.row.taskStatus - 1].label }}</div>
          </template>
        </el-table-column>
        <el-table-column
          fixed="right"
          label="操作"
        >
          <template slot-scope="scope">
            <span class="cccc">{{ scope.row.taskType }}12123</span>
            <el-button
              size="small"
              @click="goOpen(1)"
              v-if="scope.row.taskType === 1 && scope.row.taskStatus === 1"
            >
              去开通
            </el-button>
            <el-button
              size="small"
              @click="goSign(scope.row)"
              v-if="scope.row.taskType === 2 && scope.row.taskStatus === 3"
            >
              去签署
            </el-button>
            <el-button
              size="small"
              @click="goOpen(2)"
              v-if="scope.row.taskType === 3 && scope.row.taskStatus === 1"
            >
              去开通
            </el-button>
            <el-button
              size="small"
              @click="goOpen(3)"
              v-if="scope.row.taskType === 4 && scope.row.taskStatus === 1"
            >
              去开通
            </el-button>
            <el-button
              size="small"
              @click="confirmProtocol"
              v-if="scope.row.taskType === 2 && scope.row.taskStatus === 1"
            >
              确认协议
            </el-button>
            <el-button
              size="small"
              @click="openContract(scope.row)"
              v-if="scope.row.taskType === 2 && scope.row.taskStatus === 5"
            >
              查看合同
            </el-button>
            <el-button
              size="small"
              @click="openShadowDialog(scope.row)"
              v-if="scope.row.taskStatus === 6"
            >
              查看终止原因
            </el-button>
            <!-- <el-button
              v-permission="['shop_freight_edit']"
              type="text"
              size="small"
              @click="editorClick(scope.row, 'edit')"
            >
              编辑
            </el-button>
            <el-button
              v-permission="['shop_freight_delete']"
              type="text"
              size="small"
              @click="deleteClick(scope.row)"
            >
              删除
            </el-button> -->
          </template>
        </el-table-column>
      </el-table>
      <div
        v-if="total != 0"
        class="pagination-container"
      >
        <div class="pag-text">
          共 {{ total }} 条数据，每页{{ formModel.pageSize }}条，共{{
            Math.ceil(total / formModel.pageSize)
          }}页
        </div>
        <el-pagination
          background
          :page-sizes="pageSizes"
          :page-size="formModel.pageSize"
          :current-page="formModel.pageNum"
          layout="sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
    <el-dialog
      title="终止原因"
      :visible.sync="isShowShadow"
      v-if="isShowShadow"
      width="30%"
      >
      <span>{{ shadowContent }}</span>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="isShowShadow = false">确 定</el-button>
      </span>
    </el-dialog>
    <el-dialog
      title="电子签说明"
      :visible.sync="isShowSignProve"
      width="30%"
      >
      <span>
        药帮忙平台服务协议电子签署通过第三方电子合同与电子签云服务平台法大大进行，法大大依据《电子签名法》的同时，获得ISO27001及ISO27701等安全认证及保险公司承保，确保签约过程中通过授权收集、信息校验、意向确认、防止篡改和证据留存下载查询等流程的规范性，确保拥有完整的证据链条。同时可直接为每个用户申请拥有一个合法唯一的证书密钥，进行企业电子密码签章交换资料，确保每份文件安全、加密。
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="isShowSignProve = false">确 定</el-button>
      </span>
    </el-dialog>
    <el-dialog
      title="查看账号"
      :visible.sync="isShowAccount"
      width="20%"
      class="account-dialog"
    >
      <div class="account-form">
        <span>
          超管账号
          <el-tooltip
              class="item"
              effect="dark"
              placement="top-start"
          >
            <template #content>
              法大大超管账号，可授权电子印章给企业其他成员
            </template>
            <img src="@/assets/tip.png" alt="">
          </el-tooltip>：
        </span>
        <div class="account-item">
          {{ adminPhone }}
        </div>
      </div>
      <div class="account-form">
        <span>
          可盖章账号
          <el-tooltip
              class="item"
              effect="dark"
              placement="top-start"
          >
            <template #content>
              企业用印员账号，由超管账号授权电子印章给企业成员（用印员）的账号
            </template>
            <img src="@/assets/tip.png" alt="">
          </el-tooltip>：
        </span>
        <div class="account-item" v-if="accountMsg">{{ accountMsg }}</div>
        <div class="account-item" v-else>
          <p v-for="(item) in memberNumbers" :key="item">{{item}}</p>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="isShowAccount = false">确 定</el-button>
      </span>
    </el-dialog>
    <FlatServerComfirmDialog :isShow="isShowFlatServerInfo" @onGetList="getList" @onCancel="flatDialogCancel"/>
  </div>
</template>

<script>
import FlatServerComfirmDialog from './componments/platServeAgreeComponents/platServerConfirmDialog.vue'
import { getSignaturesStatus, openSignatures, getSignaturesList, getFreeSign, goSign, getVisaFreeSign, getAccountInfo,getFadadaOpenUrl } from '@/api/platformServer';
import { color } from 'echarts';
import { result } from 'lodash';
export default {
  components: {
    FlatServerComfirmDialog
  },
  data() {
    return {
      showText: '',
      stateText: '',
      state: 0, //开通状态
      ifModify: false,
      isShowShadow: false,
      stateClass: '',
      time: '',
      stateTip: '邀请您开通电子签服务，开通成功后可在线进行“购销合同印章免验证签授权”及“平台服务协议签署”',
      isShowSignProve: false, //电子签说明弹框是否显示
      isShowAccount: false, //查看账号弹框是否显示
      isShowFlatServerInfo: false, //平台服务协议信息确认弹框是否显示
      shadowContent: '', //终止原因
      formModel: {
        taskId: '', //任务编号
        taskTopic: '', //任务主题,
        failureTimeBegin: '',  //生效时间
        failureTimeEnd: '', //失效时间
        taskStatus: '', //任务状态
        // 当前页
        pageNum: 1,
        // 当前每页显示多少条数据
        pageSize: 10,
      },
      taskStatusOption: [
        { label: '待商家确认', value: 1 },
        { label: '待平台审核', value: 2 },
        { label: '待商家签署', value: 3 },
        { label: '待平台签署', value: 4 },
        { label: '已完成', value: 5 },
        { label: '已终止', value: 6 },
        { label: '已逾期', value: 7 },
        { label: '已作废', value: 8 },
      ],
      isLoading: false,
      tableData: [],
      total: 0,
      pageSizes: [10, 20, 30, 40],
      adminPhone: "",
      memberNumbers: [],
      accountMsg: ""
    }
  },
  created() {
    this.getSignStatus()
    this.getList();
    this.getAccount();
  },
  methods: {
    handleCurrentChange(val) {
      this.formModel.pageNum = val;
      this.getList();
    },
    handleSizeChange(sizi){
      this.formModel.pageSize = sizi;
      this.getList();
    },
    openShadowDialog(item) {
      this.isShowShadow = true;
      this.shadowContent = item.stopCause;
    },
    openContract(item) {
      window.open(item.agreementUrl);
    },
    confirmProtocol() {
      if (this.state) {
        this.isShowFlatServerInfo = true;
      } else {
        this.$message.warning("请先开通电子签服务!");
      }
    },
    // 去开通
    goOpen(type) {
      switch(type) {
        case 1: {
          getFreeSign().then(res => {
            if (res && res.code === 0) {
              this.$router.push({
                path: "/companyOpenAccount/contractPasser",
                query: {
                  signUrl: res.result,
                  title: "购销合同印章免验证签"
                }
              });
            } else {
              this.$message.error(res.msg);
            }
          })
          break;
        }
        case 2: {
          getVisaFreeSign().then(res => {
            if (res && res.code === 0) {
              this.$router.push({
                path: "/companyOpenAccount/contractPasser",
                query: {
                  signUrl: res.result,
                  title: "资质印章免验证签"
                }
              });
            } else {
              this.$message.error(res.msg);
            }
          })
          break;
        }
        case 3: {
          getFadadaOpenUrl().then(res => {
            if (res && res.code === 0) {
              this.$router.push({
                path: "/companyOpenAccount/contractPasser",
                query: {
                  signUrl: res.result,
                  title: "平台代收款印章免验证签"
                }
              });
            } else {
              this.$message.error(res.msg);
            }
          })
          break;
        }
      }
    },
    // 去签署
    goSign(item) {
      let params = {
        taskId: item.taskId
      }
      goSign(params).then(res => {
        if (res && res.code === 0) {
          this.$router.push({
            path: "/companyOpenAccount/contractPasser",
            query: {
              signUrl: res.result,
              title: "平台服务协议签署"
            }
          });
        } else {
          this.$message.warning(res.msg);
        }
      })
    },
    exDate(timeNum) {
      if (!timeNum) {
        return
      }
      let date = new Date(timeNum);
      return date.getFullYear() + '-' + (date.getMonth() + 1) + '-' + date.getDate();
    },
    exClass(status) {
      if (status === 1 || status === 3) {
        return 'color1';
      } else if (status === 6 || status === 7 || status === 8) {
        return 'color2';
      }
    },
    getList() {
      this.isLoading = true;
      let params = {
        ...this.formModel
      }
      getSignaturesList(params).then(res => {
        if (res && res.code === 0) {
          this.tableData = res.result.list;
          this.total = res.result.total;
        } else {
          this.$message.error(res.msg);
        }
        this.isLoading = false;
      })
    },
    getSignStatus() {
      getSignaturesStatus().then(res => {
        if (res && res.code === 0) {
          this.state = res.result.status;
          this.showText = res.result.text;
          this.stateText = this.state === 1 ? '已开通' : '未开通';
          this.stateClass = this.state === 1 ? 'color2' : 'color1';
        } else {
          this.$message.error(res.msg);
        }
      })
    },
    getAccount() {
      getAccountInfo().then(res => {
        if (res && res.code === 0) {
          console.log(res, "ress");
          let { msg, adminPhone, memberNumbers } = res.result;
          this.adminPhone = adminPhone;
          if (!msg) {
            this.memberNumbers = memberNumbers;
          } else {
            this.accountMsg = msg;
          }
        } else {
          this.$message.error(res.msg);
        }
      })
    },
    openElectronicSign() {
      openSignatures().then(res => {
        if (res && res.code === 0) {
          this.$router.push({
            path: "/companyOpenAccount/openElectronicSign",
            query: {
              signUrl: res.result
            }
          });
        } else {
          this.$message.error(res.msg);
        }
      })
    },
    dateOnChange(val) {
      if (val === null) {
        this.formModel.failureTimeBegin = '';
        this.formModel.failureTimeEnd = '';
        val = '';
        this.time = '';
      } else if (typeof val[0] === 'string') {
        this.formModel.failureTimeBegin = val[0];
        this.formModel.failureTimeEnd = val[1];
      }
    },
    toSerachForm() {
      this.formModel.pageNum = 1;
      this.getList();
    },
    resetForm() {
      this.formModel.taskId = '';
      this.formModel.taskTopic = '';
      this.formModel.failureTimeBegin = '';
      this.formModel.failureTimeEnd = null;
      this.formModel.taskStatus = '';
      this.formModel.endTime = '';
      this.time = '';
      this.formModel.pageNum = 1;
      this.getList();
    },
    flatDialogCancel() {
      this.isShowFlatServerInfo = false;
    }
  }
}
</script>
<style lang="scss">
  .platform-server-page {
    .cell {
      display: flex;
      justify-content: center;
      text-align: center;
    }
    .el-form-item {
      margin-right: 24px;
    }
    .el-form-item__content {
      display: flex;
      align-items: center;
      .search-title {
        height: 100%;
      }
      .el-select {
        height: 100%;
      }
    }
    .pagination-container{
      margin: 15px 0;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    .search-form-table {
      .el-table__cell .el-button {
        color: #4184D5;
        border-color: #4184D5;
      }
      .cccc {
        position: absolute;
        opacity: 0;
        z-index: -10;
      }
    }
    .flat-server-dialog {
      .el-dialog__header {
        background: #f5f5f580;
        padding: 10px;
        box-sizing: border-box;
      }
      .el-dialog__footer {
        text-align: center;
        border-top: 1px solid #D9D9D9;
      }
      .el-dialog__headerbtn {
        top: 15px;
      }
    }
    .info-bus-form {
      margin-top: 10px;
    }
    .el-form-item__label {
      font-family: PingFangSC-Regular;
      font-weight: 400;
      font-size: 12px;
      color: #333333;
    }
  }
</style>
<style lang="scss" scoped>
.platform-server-page {
  .qual-state {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 20px;
    font-size: 16px;
    color: #f5222d;
    height: 50px;
    background: #fffbf1;
    .color1 {
      color: #ff9800;
    }
    .color2 {
      color: #52c41a;
    }
    .color4 {
      color: #4183d5;
      .line {
        display: inline-block;
        width: 1px;
        height: 16px;
        background: #d8d8d8;
        vertical-align: middle;
      }
      .li-img {
        width: 16px;
        height: 16px;
        vertical-align: middle;
        margin: 0 1px 0 5px;
      }
      span {
        cursor: pointer;
        margin-left: 5px;
      }
    }
    .h {
      color: #333333;
      padding-right: 8px;
    }
    .t {
      font-family: PingFangSC-Medium;
      font-weight: 500;
      font-size: 12px;
      color: #F5222D;
      margin-left: 10px;
    }
  }
  .search-form-content {
    padding: 20px 0;
    box-sizing: border-box;
  }
  .search-form-table {
    padding-left: 10px;
    box-sizing: border-box;
  }
  .account-dialog {
    .account-form {
      display: flex;
      // align-items: center;
      margin-bottom: 20px;
      span {
        width: 35%;
        height: 20px;
        display: flex;
        align-items: center;
        justify-content: flex-end;
        img {
          width: 12px;
          height: 12px;
          display: flex;
          margin: 0 5px;
          box-sizing: border-box;
        }
      }
      &:last-child {
        margin-bottom: 0px;
      }
    }
    .account-item {
      flex: 1;
      display: flex;
      align-items: center;
      flex-wrap: wrap;
      p {
        margin: 0;
        margin-right: 20px;
        margin-bottom: 10px;
      }
    }
  }
  .color1 {
    color: #ff9800;
  }
  .color2 {
    color: #f5222d;
  }
}
</style>
