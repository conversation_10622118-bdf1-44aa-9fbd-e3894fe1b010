<template>
  <div>
    <div class="selectReport">
      <el-form :model="formData">
        <el-form-item label="单个活动ID提报" v-if="importType === 5">
          <el-input
            v-model="formData.activityTempBarcode"
            size="small"
            placeholder="请输入活动商品报名ID/拼团ID/CSUID/商品编码"
          ></el-input>
        </el-form-item>
        <el-form-item label="单个商品提报" v-if="importType === 6">
          <el-input
            v-model="formData.tempBarcode"
            size="small"
            placeholder="请输入原品编码/原商品CSUID"
          ></el-input>
        </el-form-item>
      </el-form>
    </div>
    <el-row class="importBottom">
      <el-button
        type="primary"
        class="xyy-blue importBtn"
        size="small"
        :loading="btnLoading"
        @click="determineDialog()"
        >提取活动信息</el-button
      >
    </el-row>
  </div>
</template>

<script>
import {
  getTimeStr,
  
  singleActivity
} from '@/api/market/collageActivity'
import {
  getSkuByBarcode
} from '@/api/market/newCollageActivity'
export default {
  props: {
    importType: {
      type: Number,
      default: 6
    },
    frameReportId: {
      default: ''
    },
    activityInfo: {
      type: Object,
      default: function () {
        return {
          registrationStime: '',
          registrationEtime: '',
          auditStime: '',
          auditEtime: '',
          validStime: '',
          validEtime: '',
          preheatStime: '',
          limitCustomerGroupId: ''
        }
      }
    }
  },
  data() {
    return {
      formData: {
        tempBarcode: '',
        activityTempBarcode: ''
      },
      btnLoading: false
    }
  },
  activated() {},
  methods: {
    determineDialog() {
      if (this.importType === 5) {
        if (this.formData.activityTempBarcode === '') {
          this.$message.error('请输入活动商品报名ID/拼团ID/CSUID/商品编码')
          return
        }
        this.btnLoading = true
        singleActivity({
          baseId: this.frameReportId,
          singleActivity: this.formData.activityTempBarcode
        })
          .then((res) => {
            this.btnLoading = false
            if (res.code === 1000) {
              window.sessionStorage.removeItem('singleCreateInfo') // 商业反馈重新选择新的商品创建拼团活动，进入编辑页面还是上一次所选的商品信息。先删再存看看
              sessionStorage.setItem(
                'editCollageItem',
                JSON.stringify({
                  ...res.data.csu,
                  baseId: this.frameReportId,
                  auditTime: [
                    this.activityInfo.auditStime,
                    this.activityInfo.auditEtime
                  ],
                  activityTime: [
                    this.activityInfo.validStime,
                    this.activityInfo.validEtime
                  ],
                  preheatTime: this.activityInfo.preheatStime,
                  topicTitle: this.activityInfo.topicTitle,
                  registrationTime: [
                    this.activityInfo.registrationStime,
                    this.activityInfo.registrationEtime
                  ],
                  customerGroupVO: this.customerGroupVO,
                  baseCustomerGroupId: this.activityInfo.limitCustomerGroupId,
                  customerGroupName : res.data.csu.saleScopeDTO.customerGroupName,
                })
              )
              const path = '/editCollageActivity'
              const obj = {
                fromType: 'editCollageItem',
                isCollageActTypeTb:true
              }
              sessionStorage.setItem('collageActType', 'editCollageItem')
              sessionStorage.setItem('collageActTypeTb', 'collageActTypeTb')
              sessionStorage.removeItem('createCollageActivity')
              sessionStorage.setItem('createCollageActivity', 'true')
              window.openTab(path, obj)
              // this.$router.push('/editCollageActivity?fromType=singleCreate');
            } else {
              this.btnLoading = false
              this.$message.error(res.msg)
            }
          })
          .catch((err) => {
            this.btnLoading = false
            this.$message.error(err.msg || '系统错误')
          })
      } else if (this.importType === 6) {
        if (this.formData.tempBarcode === '') {
          this.$message.error('请输入原品编码/原商品CSUID')
          return
        }
        this.btnLoading = true
        getSkuByBarcode({
          baseId: this.frameReportId,
          singleProduct: this.formData.tempBarcode
        })
          .then((res) => {
            this.btnLoading = false
            if (res.code === 1000) {
              window.sessionStorage.removeItem('singleCreateInfo') // 商业反馈重新选择新的商品创建拼团活动，进入编辑页面还是上一次所选的商品信息。先删再存看看
              sessionStorage.setItem(
                'singleCreateInfo',
                JSON.stringify({
                  ...res.data.csu,
                  baseId: this.frameReportId,
                  auditTime: [
                    this.activityInfo.auditStime,
                    this.activityInfo.auditEtime
                  ],
                  activityTime: [
                    this.activityInfo.validStime,
                    this.activityInfo.validEtime
                  ],
                  preheatTime: this.activityInfo.preheatStime,
                  topicTitle: this.activityInfo.topicTitle,
                  registrationTime: [
                    this.activityInfo.registrationStime,
                    this.activityInfo.registrationEtime
                  ],
                  customerGroupVO: this.customerGroupVO,
                  baseCustomerGroupId: this.activityInfo.limitCustomerGroupId ||  res.data.customerGroupId,
                  isedit:res.data.limitCustomerStatus != 1,
                  customerGroupName : res.data.customerGroupName,
                })
              )
              const path = '/editCollageActivity'
              const obj = {
                fromType: 'singleCreate'
              }
              sessionStorage.setItem('collageActType', 'singleCreate')
              sessionStorage.removeItem('createCollageActivity')
              sessionStorage.setItem('createCollageActivity', 'true')
              window.openTab(path, obj)
              // this.$router.push('/editCollageActivity?fromType=singleCreate');
            } else {
              this.btnLoading = false
              this.$message.error(res.msg)
            }
          })
          .catch((err) => {
            this.btnLoading = false
            this.$message.error(err.msg || '系统错误')
          })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.selectReport {
  padding: 0 15px;
  .el-input {
    width: 40%;
  }
}
.importBottom {
  // 只给盒子的上边设置阴影
  box-shadow: 0px -2px 2px #e6e6e6;
  width: 100%;
  height: 63px;
  display: flex;
  justify-content: center;
  align-items: center;
  // 实现居中
  .importBtn {
    width: 120px;
    height: 32px;
    background: #4184d5;
    border-radius: 4px;
  }
}
</style>