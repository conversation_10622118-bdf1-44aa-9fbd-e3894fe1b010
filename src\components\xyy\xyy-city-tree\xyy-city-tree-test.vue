<!--created by <PERSON><PERSON><PERSON><PERSON><PERSON> in 2021/3/12-->
<template>
  <xyy-city-tree
    v-model="selectArray"
    :half-selected-keys="halfSelectCodeArray"
    :lazy="true"
    :load="loadTree"
    :show-checkbox="true"
    :default-expanded-keys="defaultExpandedKeys"
    node-key="areaCode"
    :props="treeProps"
    @check="checkChange"
  />
</template>

<script>
import XyyCityTree from './index';
import { all, province, city, district } from './testData';

export default {
  name: 'XyyCityTreeTest',
  components: { XyyCityTree },
  data() {
    return {
      selectArray: [],
      checkData: [
        {
          areaCode: 350112,
          areaLevel: 3,
          parent: {
            areaCode: 350100,
            areaLevel: 2,
            parent: {
              areaCode: 350000,
              areaLevel: 1,
              parent: {
                areaCode: all[0].areaCode,
                areaLevel: 0,
              },
            },
          },
        },
        {
          areaCode: 420114,
          areaLevel: 3,
          parent: {
            areaCode: 420100,
            areaLevel: 2,
            parent: {
              areaCode: 420000,
              areaLevel: 1,
              parent: {
                areaCode: all[0].areaCode,
                areaLevel: 0,
              },
            },
          },
        },
      ],
      halfSelectCodeArray: [],
      defaultExpandedKeys: [all[0].areaCode],
      treeProps: {
        label: 'areaName',
        isLeaf: (data, node) => node.level === 4,
      },
    };
  },
  mounted() {
    const halfSelectCodeArraySet = new Set();
    const selectArraySet = new Set();
    this.checkData.forEach((item) => {
      selectArraySet.add(item.areaCode);
      let { parent } = item;
      while (parent) {
        halfSelectCodeArraySet.add(parent.areaCode);
        parent = parent.parent;
      }
    });
    this.halfSelectCodeArray = [...halfSelectCodeArraySet];
    this.selectArray = [...selectArraySet];
  },
  methods: {
    loadTree(node, resolve) {
      if (node.level === 0) {
        return resolve([...all]);
      }
      if (node.level === 1) {
        const { areaCode } = node.data;
        return resolve([...province.get(areaCode)]);
      }
      if (node.level === 2) {
        const { areaCode } = node.data;
        return resolve([...city.get(areaCode)]);
      }
      if (node.level === 3) {
        const { areaCode } = node.data;
        return resolve([...district.get(areaCode)]);
      }
      return resolve([]);
    },
    checkChange(node) {
      console.log(node);
    },
  },
};
</script>

<style scoped></style>
