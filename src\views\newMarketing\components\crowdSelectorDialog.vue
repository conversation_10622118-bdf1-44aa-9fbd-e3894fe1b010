<template>
  <el-dialog
    :title="title"
    :visible="crowdDialogVis"
    width="80%"
    :destroy-on-close="true"
    :before-close="handleDialogClose"
  >
    <div v-if="!innerSelected">
      <el-form
        ref="form"
        class="fix-item-width"
        size="small"
        label-position="right"
        label-width="100px"
        :model="listQuery"
      >
        <el-row>
          <el-col :lg="6" :sm="12">
            <el-form-item label="人群ID" prop="id">
              <el-input v-model="listQuery.id" clearable placeholder="请输入" />
            </el-form-item>
          </el-col>
          <el-col :lg="6" :sm="12">
            <el-form-item label="人群名称" prop="groupName">
              <el-input v-model="listQuery.groupName" clearable placeholder="请输入" />
            </el-form-item>
          </el-col>
          <el-col :lg="6" :sm="12">
            <el-form-item label="创建时间" prop="createStartTime">
              <el-date-picker
                v-model="listQuery.createStartTime"
                type="datetime"
                placeholder="选择日期时间"
                prefix-icon="el-icon-date"
              />
            </el-form-item>
          </el-col>
          <el-col :lg="6" :sm="12" class="btn-group">
            <el-button size="small" type="primary" @click="searchData">查询</el-button>
            <el-button size="small" @click="resetFields">重置</el-button>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <el-table
      :data="list"
      style="margin-top: 10px;"
      border
      max-height="300px"
      :header-cell-style="{ background: '#eeeeee', color: '#666666' }"
      @row-click="rowClick"
    >
      <el-table-column v-if="!innerSelected" align="center" prop="selection" width="50">
        <template v-slot="{ row }">
          <el-radio :value="selections" :label="row.id" @input="selectChange($event, row)">
            <span />
          </el-radio>
        </template>
      </el-table-column>

      <el-table-column prop="id" align="center" label="人群ID" width="120" />
      <el-table-column prop="tagName" label="人群名称" align="center" width="250" />
      <el-table-column label="人群定义" prop="tagDef" min-width="300">
        <template slot="header">
          <div style="width: 100%; text-align: center;">人群定义</div>
        </template>
        <template slot-scope="{ row }">
          <p v-for="(item, index) in row.tagDef" :key="index">{{ item }}</p>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" width="160">
        <template slot-scope="scope">
          <span>{{ scope.row.createTime | formatDate }}</span>
        </template>
      </el-table-column>
    </el-table>
    <div v-show="listQuery.total > 0" class="page-container">
      <div class="pag-text">
        共 {{ listQuery.total }} 条数据，每页{{ listQuery.pageSize }}条，共{{
        Math.ceil(listQuery.total / listQuery.pageSize)
        }}页
      </div>
      <el-pagination
        background
        class="pager"
        :current-page.sync="listQuery.page"
        :page-size.sync="listQuery.pageSize"
        layout="sizes, prev, pager, next, jumper"
        :page-sizes="[10, 20, 30, 50]"
        :total="listQuery.total"
        prev-text="上一页"
        next-text="下一页"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
    <span slot="footer">
      <el-button size="medium" @click="cancel">取消</el-button>
      <el-button size="medium" style="margin-left: 20px;" type="primary" @click="confirm">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { getCrowdList } from '@/components/xyy/customerOperatoin/fetch/fetch';

export default {
  components: {},
  model: {
    prop: 'crowdDialogVis',
    event: 'onDialogChange',
  },
  props: {
    selected: Number | String,
    title: {
      type: String,
      default: '选择人群',
    },
    crowdDialogVis: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      innerSelected: '',
      listQuery: {
        createStartTime: '',
        groupName: '',
        id: '',
        page: 1,
        pageSize: 10,
        total: 0,
      },
      list: [],
      selections: '',
      dialogVisible: false,
    };
  },
  watch: {
    selected: {
      deep: true,
      handler() {
        this.innerSelected = JSON.parse(JSON.stringify(this.selected));
        this.searchData();
      },
    },
  },
  mounted() {
    this.searchData();
  },
  methods: {
    rowClick(row) {
      if (!this.innerSelected) {
        this.selectChange(row.id, row);
      }
    },
    selectChange($event, row) {
      this.selections = row.id;
    },
    cancel() {
      this.handleDialogClose();
    },
    confirm() {
      if (!this.innerSelected) {
        let _selections = '';
        if (this.selections) {
          _selections = this.selections;
        } else {
          this.$message({
            message: '请先选择人群',
            type: 'warning',
          });
          return;
        }
        this.selections = '';
        this.$emit('selectChange', _selections);
      }
      this.handleDialogClose();
    },
    handleDialogClose() {
      this.$emit('cancelModal', false);
    },
    resetFields() {
      this.$refs.form.resetFields();
      this.searchData();
    },
    searchData() {
      this.getPeopleList(this.listQuery, true);
    },
    handleSizeChange(val) {
      this.listQuery.pageSize = val;
      this.getPeopleList(this.listQuery);
    },
    handleCurrentChange() {
      this.getPeopleList(this.listQuery);
    },
    getPeopleList(listQuery, reset) {
      this.loading = true;
      const { page, pageSize } = listQuery;
      const {
        createStartTime,
        groupName,
        id,
      } = this.listQuery;

      const params = {
        pageNum: reset ? 1 : page,
        pageSize,
        createStartTime,
        groupName,
        id: this.innerSelected ? this.innerSelected : id,
      };

      if (params.createStartTime) {
        params.createStartTime = new Date(params.createStartTime).getTime();
      }

      getCrowdList(params)
        .then((res) => {
          if (res.code === 1000) {
            this.list = res.data.list ? res.data.list : [];
            this.listQuery = {
              ...this.listQuery,
              total: res.data.totalCount,
            };
          } else {
            this.$message({
              message: '请求失败',
              type: 'error',
            });
          }
        })
        .catch((error) => {
          console.log(error);
          this.loading = false;
        })
        .finally(() => {
          this.loading = false;
        });
    },
  },
};
</script>
<style scoped lang="scss">
form.el-form.fix-item-width {
  .el-row {
    ::v-deep   .el-col .el-form-item {
      .el-form-item__content {
        > .el-input {
          width: 100%;
        }

        > .el-date-editor {
          width: 100%;
        }
      }
    }
  }
}

.btn-group {
  display: flex;
  justify-content: flex-end;
}
// @import '@/components/xyy/customerOperatoin/style/style';
</style>
