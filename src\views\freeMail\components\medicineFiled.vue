<!--
 * @Descripttion: 
 * @Version: 1.0
 * @Author: zyh
 * @Date: 2025-02-14 14:25:30
 * @LastEditors: zyh
 * @LastEditTime: 2025-02-18 19:38:04
-->

<template>
    <div class="boxDiv">
        <div class="categoryExt"
             v-for="(item,index) in categoryExtList"
             :key="index">
            <span class="title">{{item.dictName}}:</span>
            <span class="right">
                <el-radio-group :disabled="disabled"
                                @click.native="onRadioChange($event,item)"
                                v-model="item.selectedValueId">
                    <el-radio v-for="(jtem, jndex) in item.detailList"
                              :key="jndex"
                              :label="jtem.dictValueId">{{ jtem.dictValueName }}</el-radio>
                </el-radio-group>
            </span>
        </div>
        <div>
        </div>
    </div>
</template>

<script>
export default {
    props: {
        basicData: {
            type: Object,
            default () {
                return {}
            }
        },
        formModel: {
            type: Object,
            default () {
                return {}
            }
        }
    },
    data () {
        return {
            categoryExtList: [],
            disabled: false,
        }
    },
    created () {
        this.initCategoryExtList();
    },
    watch: {
        basicData (newValue) {
            this.initCategoryExtList();
        },
        formModel: {
            handler (newVal) {
                const base = JSON.parse(JSON.stringify(newVal))
                this.disabled = base.isEdit == 1
            },
            immediate: true,
            deep: true
        },
    },
    methods: {
        onRadioChange (e, item) {
            if (e.target.tagName === "INPUT") {
                if (item.selectedValueId) {
                    item.selectedValueId = '';
                }
            }
        },
        initCategoryExtList () {
            this.categoryExtList = this.basicData.categoryExtList.map(item => {
                const selectedItem = item.detailList.find(detail => detail.dictValueMap.selected == "1");
                return {
                    ...item,
                    selectedValueId: selectedItem ? selectedItem.dictValueId : null
                };
            });
        },
        getSubmitCategoryExtList () {
            const submitCategoryExtList = this.categoryExtList.map(item => {
                return {
                    ...item,
                    detailList: item.detailList.map(detail => ({
                        ...detail,
                        dictValueMap: {
                            selected: detail.dictValueId == item.selectedValueId ? "1" : "0"
                        }
                    }))
                };
            });
            return submitCategoryExtList;
        }
    },
}
</script>
<style lang='scss' scoped>
.boxDiv {
    width: 100%;
    height: 100%;
    padding-left: 65px;
    // overflow-y: auto;
    display: flex;
    flex-wrap: wrap;
    .categoryExt {
        // width: 48%;
        flex: 0 0 48%; // 设置每个元素的基础宽度为 48%
        padding-bottom: 20px;
        display: flex;
        align-items: flex-start;
        margin-right: 1%;
        .title {
            font-size: 13px;
            margin-right: 24px;
            white-space: nowrap;
        }
        .right {
            ::v-deep   .el-radio-group {
                .el-radio {
                    margin: 3px 14px 6px;
                }
            }
        }
    }
}
</style>
