<template>
  <el-dialog
    title="商品匹配结果不一致提醒"
    :visible="true"
    width="60%"
    :before-close="handleClose"
  >
    <p>
      您有<span class="redText">{{ dataCount }}</span>个商品通用名称、商品名称、规格、包装单位、批准文号、厂家、产地与所匹配的标品<span class="redText">不一致</span>，<span class="redText">发布后将使用标品信息</span>，请检查无误后再发布或重新创建商品。
      <span
        class="blueText"
        @click="toSearch"
      >立即查看</span>
    </p>
    <div>提示：<span class="redText">请认真检查列表上的标红信息，否则将导致客户收到的货物与平台展示的商品信息不符，引起客诉！</span></div>
  </el-dialog>
</template>

<script>
export default {
  name: 'ResultTips',
  props: {
    dataCount: {
      type: Number,
      default: () => {},
    },
  },
  data() {
    return {};
  },
  created() {
  },
  methods: {
    handleClose() {
      this.$emit('handleClose');
    },
    toSearch() {
      this.$emit('tipsSearch', 2);
      this.handleClose();
    },
  },
};
</script>

<style scoped>
.redText {
  color: red;
}
.blueText {
  color: #4183d5;
  cursor: pointer;
}
</style>
