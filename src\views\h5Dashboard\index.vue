<template>
  <div class="content">
    <el-row style="margin-top: 20px">
      <el-col :span="24">
        <div>
          <img class="logo" src="../../assets/ybm.png" alt="">
          <div class="title">欢迎登录商业版药帮忙</div>
        </div>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="24">
        <el-input v-model="phone" placeholder="请输入手机号" oninput="value=value.replace(/[^\d]/g,'')" maxLength='11'></el-input>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="24">
        <el-input placeholder="请输入登录密码" v-model="password" show-password></el-input>
      </el-col>
    </el-row>
    <el-row style="margin-top: 0.6rem">
      <el-col :span="24">
        <el-button style="width: 100%" :type="(phone&&password)?'primary':'info'" :disabled="!phone||!password"
                   @click="login"
        >登 录
        </el-button>
      </el-col>
    </el-row>
    <div v-show="showTip" class="tips">{{ showTip || '账号或密码有误，请重新输入' }}</div>
  </div>
</template>

<script>
import { login } from '@/api/home';
import {mapMutations} from 'vuex'
export default {
  name: 'index',
  data() {
    return {
      phone: '',
      password: '',
      showTip: ''
    };
  },
  methods: {
    ...mapMutations(['SET_PHONE']),
    async login() {
      this.SET_PHONE(this.phone);
      if (this.phone && this.password) {
        const params = {
          userName: this.phone,
          password: this.password,
          loginType: 1 //h5数据页登陆
        };
        try {
          const res = await login(params);
          if (res && res.code === 0) {
            this.$router.push({ path: '/dataAnalysis' });
          } else if (res && res.code === 1003) {
            //没有权限
            this.$router.push({ path: '/noPermission' });
          } else {
            this.showTip = res.msg;
            setTimeout(() => {
              this.showTip = '';
            }, 2000);
          }
        } catch (e) {
          this.showTip = e;
          setTimeout(() => {
            this.showTip = '';
          }, 2000);
        }
      }
    }
  }
};
</script>

<style scoped lang="scss">
.content {
  box-sizing: border-box;
  width: 100%;
  height: 100%;
  padding: 0.4rem;
  overflow: hidden;
  background-color: #fff;
  position: relative;

  .logo {
    width: 1.4rem;
    height: 0.42rem;
  }

  .el-row {


    ::v-deep   .el-input__inner {
      border: none;
    }
  }

  .title {
    font-size: 0.4rem;
    line-height: 0.56rem;
    font-weight: 500;
    color: #333333;
    margin-bottom: 0.3rem;
  }

  .tips {
    position: absolute;
    z-index: 100;
    left: 50%;
    top: 5.6rem;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.70);
    border-radius: 0.04rem;
    padding: 0.18rem 0.3rem;
    font-weight: 400;
    font-size: 0.32rem;
    color: #FFFFFF;
    line-height: 0.45rem;
    white-space: nowrap;
  }
}
</style>
