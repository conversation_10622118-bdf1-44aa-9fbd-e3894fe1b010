<template>
  <div class="tab-content">
    <audio ref="audio" src="../../assets/audio/notify.mp3"></audio>
    <div style="display: none" ref="audioBtn" @click="audioPlay"></div>
    <el-tabs v-model="activeTab" type="card" closable @tab-remove="removeTab" @tab-click="handleClick"
             @contextmenu.prevent.native="openMenu($event)">
      <el-tab-pane
        v-for="item in editableTabs"
        :key="item.path"
        :label="item.meta.title"
        :name="item.path"
        :tabPath="item.path"
      >
      </el-tab-pane>
    </el-tabs>
    <div class="contextBox">
      <ul v-show="visible" :style="{ left: left + 'px', top: top + 'px' }" class="contextmenu">
        <li @click="refreshSelectedTag">
          {{ '重新加载' }}
        </li>
        <li v-if="selectedTag!=='/home'" @click="closeSelectedTag">
          {{ '关闭标签' }}
        </li>
        <li @click="closeAllTags">
          {{ '关闭全部标签' }}
        </li>
        <li @click="closeOthersTags">
          {{ '关闭其它标签' }}
        </li>
      </ul>
    </div>
  </div>
</template>

<script>
import {mapState, mapMutations} from "vuex";
import {isExternal} from "@/utils/util";
import { initWebSocket } from "../../utils/webSocket/socket";
import { websocketSwitch } from "../../api/home";
export default {
  name: "tabsPart",
  data() {
    return {
      activeTab: '/home',
      editableTabs: [
        {
          path: '/home',
          name: 'home',
          meta: {
            title: '首页'
          }
        }
      ],
      visible: false,
      top: 0,
      left: 0,
      selectedTag: {},
    }
  },
  watch: {
    tabList: {
      handler(newValue, oldValue) {
        const ary = []
        newValue.map(item => {
          const [obj] = this.flattenRoutesList.filter(li => li.path.split('?')[0] === item.split('?')[0])
          if (obj) {
            const tab = JSON.parse(JSON.stringify(obj))
            tab.path = item
            ary.push(tab)
          }
        })
        this.editableTabs = [{
          path: '/home',
          name: 'home',
          meta: {
            title: '首页'
          }
        }, ...ary]
        // console.log(1111111111, this.editableTabs);
      },
      deep: true
    },
    'currentTab'(newValue, oldValue) {
      if (isExternal(newValue)) {
        this.activeTab = newValue
      } else {
        this.editableTabs.forEach(item => {
          if (item.path.split('?')[0] === newValue) {
            this.activeTab = item.path
          }
        })
      }
    },
    visible(value) {
      if (value) {
        document.body.addEventListener('click', this.closeMenu)
      } else {
        document.body.removeEventListener('click', this.closeMenu)
      }
    }
  },
  computed: {
    ...mapState('permission', ['flattenRoutesList', 'tabList', 'currentTab'])
  },
  created() {
    // resetRouter()
    if (this.$route.path !== '/home') {
      // this.$router.replace({path: '/home'})
    }
  },
  beforeMount() {
    window.closeTab = (path, deleteQueryString) => {
      if(deleteQueryString && path !== this.activeTab){
        path = path.split('?')[0]
      }

      let tabs = this.editableTabs;
      let tabPath = path
      let activeName = this.activeTab;
      if (activeName === tabPath) {
        console.log('关闭当前')
        tabs.forEach((tab, index) => {
          if (tab.path === tabPath) {
            let nextTab = tabs[index + 1] || tabs[index - 1];
            if (nextTab) {
              activeName = nextTab.path;
            }
          }
        });
      }
      if (window.clearData[path]) {
        window.clearData[path]();
      }
      this.activeTab = activeName;
      this.DEL_TABLIST(tabPath)
      window.jump(activeName)
    }
    window.jump = (path, params = {}) => {
      //先判断path有没有配置
      if (!this.flattenRoutesList.some(item => item.path === path.split('?')[0])) {
        this.$message.error('您无查看权限，请联系商业主账号进行授权')
        return;
      }
      this.SET_CURRENTMENU(path)
      const routerObj = {
        showIframe: false,
        iframeSrc: '',
      }
      const list = []
      Object.keys(params).map(key => {
        list.push(key + '=' + params[key])
      })
      path = (isExternal(path) && Object.keys(params).length > 0) ? path + '?' + list.join('&') : path

      this.SET_CURRENTTAB(path)

      if (isExternal(path)) {
        //iframe
        routerObj.showIframe = true
        routerObj.iframeSrc = path
        this.SET_IFRAME(routerObj)
      } else {
        routerObj.showIframe = false
        routerObj.iframeSrc = ''
        this.SET_IFRAME(routerObj)
        if (this.$route.path !== path.split('?')[0]) {
          this.$router.replace({path: path, query: params})
        }
      }
    }
    window.refreshPage = (path) => {
      path = path || this.activeTab.split('?')[0]
      if (isExternal(path)) {
        window.jump('/refresh')
        setTimeout(() => {
          window.jump(path)
        }, 300)
      } else {
        const [obj] = this.flattenRoutesList.filter(item => item.path === path)
        if (obj) {
          this.ADD_EXCLUDELIST(obj.name)
          this.$router.replace({path: '/refresh', query: {fromPath: obj.path, fromName: obj.name}});
        }
      }
      const lwq = path.split('?')[0];
      if (window.clearData[lwq]) {
        window.clearData[lwq]();
      }
    }
    window.clearData = {}
    if (window.Notification) {
      Notification.requestPermission()
    }
    window.notify = (title, content) => {
      return new Promise((resolve, reject) => {
        if (!window.Notification) {
          console.log('浏览器不支持通知');
          reject();
        } else {
          // 检查用户曾经是否同意接受通知
          if (Notification.permission === 'granted') {
            var notification = new Notification(title, {
              body: content,
              icon: 'https://xyy-ec.oss-cn-hangzhou.aliyuncs.com/pop/org_new_logo.png', // 图片路径
              silent: true,
              tag: 'notify',
              renotify: true
            }); // 显示通知
            notification.onclick = () => {
              resolve();
            };
            notification.onclose = () => {
              reject();
            }
            notification.onshow = () => {
              this.$refs.audioBtn.click();
              this.$refs.audio.play();
            }
          } else if (Notification.permission === 'default') {
            // 用户还未选择，可以询问用户是否同意发送通知
            Notification.requestPermission().then(permission => {
              if (permission === 'granted') {
                console.log('用户同意授权');
                var notification = new Notification(title, {
                  body: content,
                  icon: 'https://xyy-ec.oss-cn-hangzhou.aliyuncs.com/pop/org_new_logo.png', // 图片路径
                  silent: true,
                  tag: 'notify',
                  renotify: true
                }); // 显示通知
                notification.onclick = () => {
                  resolve();
                };
                notification.onclose = () => {
                  reject();
                }
                notification.onshow = () => {
                  this.$refs.audioBtn.click();
                  this.$refs.audio.play();
                }
              }
            });
          } else {
            // denied 用户拒绝
            this.$message.warning('已拒绝显示通知');
            reject();
          }
        }
      })
    };
    websocketSwitch().then(res => {
      if (res.code == 0 && res.result.isTrialMerchant) {
        const { sendMsg, getMsg } = initWebSocket(process.env.VUE_APP_WEBSOCKET_URL);

        window.sendMsg = sendMsg;
        window.getMsg = getMsg;
      } else {
        const { sendMsg, getMsg } = initWebSocket("");

        window.sendMsg = sendMsg;
        window.getMsg = getMsg;
      }
    })
  },
  methods: {
    ...mapMutations("permission", ['SET_IFRAME', 'DEL_TABLIST', 'SET_CURRENTMENU', 'SET_CURRENTTAB', 'ADD_EXCLUDELIST']),
    handleClick(tab) {
      window.jump(tab.name)
    },
    removeTab(targetName) {
      window.closeTab(targetName)
    },
    audioPlay() {
      console.log(6666666);
    },
    openMenu(e) {
      this.left = e.clientX - 224
      this.top = e.clientY - 105
      if (e.target.id) {
        const path = e.target.id.replace('tab-', '')
        this.selectedTag = path
        this.visible = true
      }
    },
    refreshSelectedTag() {
      window.refreshPage(this.selectedTag)
    },
    closeSelectedTag() {
      window.closeTab(this.selectedTag)
    },
    closeAllTags() {
      this.editableTabs.forEach(item => {
        this.DEL_TABLIST(item.path)
      })
      window.jump('/home')
    },
    closeOthersTags() {
      const list = []
      this.editableTabs.forEach(item => {
        if (item.path !== '/home' && item.path !== this.selectedTag) {
          list.push(item)
        }
      })
      list.forEach(item => {
        this.DEL_TABLIST(item.path)
      })
      window.jump(this.selectedTag)
    },
    closeMenu() {
      this.visible = false
    }
  }
}
</script>

<style scoped lang="scss">

.hideSidebar .tab-content {
  left: 70px;
  width: calc(100% - 70px);
}

.tab-content {
  width: calc(100% - 214px);
  position: fixed;
  left: 214px;
  top: 65px;
  background-color: #fff;
  z-index: 1001;

  .contextBox {
    position: relative;

    .contextmenu {
      margin: 0;
      background: #fff;
      z-index: 10000;
      position: absolute;
      list-style-type: none;
      padding: 5px 0;
      border-radius: 4px;
      font-size: 12px;
      font-weight: 400;
      color: #333;
      box-shadow: 2px 2px 3px 0 rgba(0, 0, 0, 0.3);

      li {
        margin: 0;
        padding: 7px 16px;
        cursor: pointer;

        &:hover {
          background: #eee;
        }
      }
    }
  }


  .el-tabs {
    width: 100%;
    height: 100%;

    ::v-deep   .el-tabs__header {
      background-color: #fff;
      margin: 0;

      .el-tabs__item {
        background: rgba(0, 0, 0, 0.02);
        line-break: 30px;
        font-size: 12px;
        height:30px;
        line-height:30px;
      }

      .el-tabs__item.is-active {
        background: #fff;
      }
    }

    ::v-deep   .el-tabs__nav .el-tabs__item:nth-child(1) span {
      display: none;
    }

    ::v-deep   .el-tabs__content {
      //display: none;
      width: auto;
      height: 0;
      margin-right: 16px;

      .el-tab-pane {
        width: 100%;
        height: 100%;
        overflow: auto;
      }
    }

    ::v-deep   .el-tabs__nav-wrap.is-scrollable {
      padding: 0 80px 0 0;

      .el-tabs__nav-prev, .el-tabs__nav-next {
        width: 20px;
        line-height: 20px;
        top: 10px;
        font-size: 16px;
        border: 1px solid #E4E7ED;
        border-radius: 2px;
      }

      .el-tabs__nav-prev {
        left: inherit;
        right: 40px;
      }

      .el-tabs__nav-next {
        right: 10px;
      }
    }
  }
}
</style>
