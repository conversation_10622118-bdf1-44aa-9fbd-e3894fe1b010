<template>
  <div>
    <el-dialog
      :title="dialogType === 'fastMail' ? '批量导入快递信息' : '上传电子发票'"
      :visible="importUploadVisible"
      width="60%"
      @close="closeDialog"
    >
      <div
        v-if="dialogType === 'fastMail'"
        class="dialogBox"
      >
        <el-upload
          ref="uploadRef"
          action="xxx"
          :http-request="uploadFile"
          :limit="1"
          accept=".xls, .xlsx"
          :before-remove="beforeRemove"
          :on-remove="handleRemove"
          :file-list="fileList"
        >
          <el-button
            size="mini"
            type="primary"
          >
            导入Excel文件
          </el-button>
          <span
            class="btnText"
            @click.stop.prevent="downloadTemplate"
          >
            下载模板
          </span>
        </el-upload>
        <div class="tipBox">
          <div>提示：</div>
          <div>1.只有待配送、配送中状态的订单才能导入快递信息，其他状态不允许导入；</div>
          <div>2.一笔订单只允许有一个快递公司，可以有多个快递单号；</div>
          <div>3.每次上传最好不超过10000个数据，超过请分多次上传；</div>
        </div>
      </div>
      <div
        v-else
        class="dialogBox"
      >
        <el-upload
          ref="uploadRef"
          action="xxx"
          :http-request="uploadFile"
          :limit="10"
          :before-upload="beforeImportData"
          :before-remove="beforeRemove"
          :on-remove="handleRemove"
          :file-list="fileList"
          :on-exceed="handleExceed"
        >
          <el-button
            size="mini"
            type="primary"
          >
            上传文件
          </el-button>
        </el-upload>
        <div class="tipBox">
          <div>提示：</div>
          <div>1.请上传小于1M的pdf文件或ofd文件；</div>
          <div>2.一笔订单如有多张发票请一次全部上传，最多可上传10个文件；</div>
        </div>
      </div>
      <span slot="footer">
        <el-button
          size="mini"
          @click="closeDialog"
        >取 消</el-button>
        <el-button
          size="mini"
          type="primary"
          @click="confirm"
        >确 定</el-button>
      </span>
    </el-dialog>
    <el-dialog
      title="上传文件反馈"
      :visible="excelInfoVis"
      @close="closeDialog"
    >
      导入成功{{ excelInfo.success }}条，导入失败{{ excelInfo.error }}条
      <span v-if="excelInfo.error > 0">
        ，下载导入失败明细
      </span>
      <div
        v-if="excelInfo.error > 0"
        class="btnText"
        @click="downloadFile"
      >
        {{ excelInfo.errorFileName }}
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { batchImportLogisticsFromExcel, saveInvoice, uploadFDFS, getHostName } from '@/api/order/index';

export default {
  name: 'ImportUpload',
  props: {
    importUploadVisible: {
      type: Boolean,
      default: false,
    },
    dialogType: {
      type: String,
      default: '',
    },
    orderNo: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      invoiceInfo: {},
      importLoading: false,
      fileName: '',
      fileList: [],
      fastMailFile: {},
      hostName: '',
      invoiceList: [],
      excelInfo: {},
      excelInfoVis: false,
      tipHeight: document.documentElement.clientHeight / 3,
    };
  },
  created() {
    getHostName().then((res) => {
      this.hostName = (res || {}).hostName || 'http://t-upload.ybm100.com';
    });
  },
  methods: {
    closeDialog() {
      this.excelInfoVis = false;
      this.$emit('cancelDialog');
    },
    downloadTemplate() {
      window.open('https://upload.ybm100.com/pop/temp/批量导入快递信息数据.xlsx');
    },
    uploadFile(params) {
      const { file } = params;
      this.fileName = file.name;
      this.importLoading = true;
      if (this.dialogType === 'fastMail') {
        this.fastMailFile = params;
      } else {
        uploadFDFS(params).then((res) => {
          if (res.code === '200') {
            this.invoiceList.push(`${this.hostName}/${res.data}`);
          }
        });
      }
    },
    beforeImportData(uploadInfo) {
      const fileName = uploadInfo.name;
      let fileType = fileName.substring(fileName.lastIndexOf('.') + 1);
      fileType = fileType.toLocaleLowerCase();
      if (!['pdf', 'ofd'].includes(fileType)) {
        this.$message.warning({ message: '请上传PDF或OFD格式文件', offset: this.tipHeight });
        return false;
      }
      const fileSize = uploadInfo.size / 1024 / 1024;
      if (fileSize > 1) {
        this.$message.warning({ message: '文件需小于1M，请重新选择', offset: this.tipHeight });
        return false;
      }
      return true;
    },
    handleRemove(file, fileList) {
      if (this.dialogType === 'invoice') {
        const index = this.fileList.findIndex(i => i.name === file.name);
        this.invoiceList.splice(index, 1);
      }
      this.fileList = fileList;
    },
    beforeRemove(file, fileList) {
      this.fileList = fileList;
      // beforeImportData方法返回false时会自动触发beforeRemove方法，因此需要再次判断满足以下条件才可执行
      let fileType = file.name.substring(file.name.lastIndexOf('.') + 1);
      fileType = fileType.toLocaleLowerCase();
      const fileSize = file.size / 1024 / 1024;
      if ((this.dialogType === 'fastMail') || ((fileType === 'pdf' || fileType === 'ofd')  && (fileSize < 1 || fileSize === 1))) {
        return this.$confirm(`确定删除 ${file.name}？`);
      }
    },
    handleExceed() {
      this.$message.warning({ message: '最多可上传10个文件', offset: this.tipHeight });
    },
    confirm() {
      if (this.dialogType === 'fastMail') {
        // const fileFormData = new FormData();
        // fileFormData.append('file', this.fastMailFile);
        batchImportLogisticsFromExcel(this.fastMailFile).then((res) => {
          this.excelInfo = res.result || {};
          this.excelInfoVis = true;
        });
      } else {
        if (this.invoiceList.length) {
          saveInvoice({
            orderNo: this.orderNo,
            invoiceList: this.invoiceList,
          }).then((res) => {
            if (res.code === 0) {
              this.$message.success({ message: '上传成功', offset: this.tipHeight });
              this.$emit('getList');
              this.closeDialog();
            } else {
              this.$message.error({ message: res.message, offset: this.tipHeight });
            }
          });
        } else {
          this.$message.error({ message: '上传文件有误，请重新上传', offset: this.tipHeight });
        }
      }
    },
    downloadFile() {
      window.open(this.excelInfo.errorFileUrl);
    },
  },
};
</script>
<style lang="scss" scoped>
  .dialogBox {
    .tipBox {
      padding: 10px;
      background: #fafafa;
      font-size: 12px;
      margin-top: 12px;
      div {
        margin-top: 5px;
      }
    }
  }
  .btnText {
    color: #4184D5;
    cursor: pointer;
    margin-left: 20px;
  }
</style>
