// import request from '@/utils/request'
import request from '../index';

// 修改商圈状态接口 --- 启用
export function getPageQuery(params) {
  return request.postFormData('/busArea/pageQuery', params);
}

// 修改商圈状态接口 --- 启用
export function getUpdateStatus(params) {
  return request.postFormData('/busArea/updateStatus', params);
}

// 查询商圈绑定商品数量 --- 禁用
export function getProductCount(params) {
  return request.postFormData('/busArea/productCount', params);
}

// 查询绑定商圈的商品
export function getProducts(parmas) {
  return request.postFormData('/busArea/products', parmas);
}
// 查看业务商圈
export function getQueryById(parmas) {
  return request.postFormData('/busArea/queryById', parmas);
}
// 查询省市区
export function getQuerySaleableArea(parmas) {
  return request.postFormData('/busArea/querySaleableArea', parmas);
}

// 查询省市区
export function getCheckBusAreaName(parmas) {
  return request.postFormData('/busArea/checkBusAreaName', parmas);
}

// 新增保存接口
export function getSave(parmas) {
  return request.post('/busArea/save', parmas);
}

// 编辑保存接口
export function getUpdate(parmas) {
  return request.post('/busArea/update', parmas);
}

// 编辑查询接口
export function getQueryBusAreaConifigById(parmas) {
  return request.postFormData('busArea/queryBusAreaConifigById', parmas);
}

// 批量删除商圈
export function batchDelete(parmas) {
  return request.post('/busArea/batchDelete', parmas);
}

// 商圈变更日志
export function getAreaLog(parmas) {
  return request.get('/busArea/areaLog', parmas);
}
