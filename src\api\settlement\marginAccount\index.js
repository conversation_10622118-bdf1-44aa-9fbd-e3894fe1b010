import request1 from '@/utils/request';
import request from '../../index';

export function getAccountInfo(params) {
  return request.post('/merchantTotalFundAccount/getAccountInfo', params)
} // 查询当前商家账户余额

export function getMarginChangeType(params) {
  return request.get('/merchantTotalFundAccount/getMarginChangeType', params)
} // 保证金变动类型

export function getPublicAccountInfo(params) {
  return request.get('/merchantTotalFundAccount/getPublicAccountInfo', params)
} // 获取平台对公账户信息

export function getWithdrawalLimitRatio(params) {
  return request.get('/merchantTotalFundAccount/getWithdrawalLimitRatio', params)
} // 查询限制提现比例
  
export function checkRechargeBond(params) {
  // return request.post('/merchantTotalFundAccount/checkRechargeBond', params, { 'Content-Type': 'application/x-www-form-urlencoded' },)
  return request1({
    url: '/merchantTotalFundAccount/checkRechargeBond',
    method: 'post',
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
    params,
  });
} // 检查是否可以充值保证金

export function rechargeBond(params) {
  return request.post('/merchantTotalFundAccount/rechargeBond', params)
} // 充值保证金

export function updateBond(params) {
  return request.post('/merchantTotalFundAccount/updateBond', params)
} // 修改保证金

export function getBond(params) {
  return request.get('paymentProve/getBond', params)
} // 保证金配置

export function pageFundFlow(params) {
  return request.post('/merchantTotalFundAccount/pageFundFlow', params)
} // 分页查询商家资金账户流水

export function pageRechargeFlow(params) {
  return request.post('/merchantTotalFundAccount/pageRechargeFlow', params)
} // 分页查询商家充值记录

export function cancel(params) {
  return request.post('/merchantTotalFundAccount/cancel', params)
} // 取消保证金或营销额度

export function exportFundFlow(params) {
  return request.get('/merchantTotalFundAccount/export', params)
} // 商家资金账户流水导出

export function uploadFile(params) {
  const forms = new FormData();
  forms.append('file', params.file);
  return request({
    url: '/uploadFile/uploadFDFS',
    method: 'post',
    data: forms,
    headers: { 'Content-Type': 'multipart/form-data' },
    onUploadProgress: (progressEvent) => {
      const num = (progressEvent.loaded / progressEvent.total) * 100; // 百分比
      params.onProgress({ percent: num }); // 进度条
    },
  });
} // 文件上传