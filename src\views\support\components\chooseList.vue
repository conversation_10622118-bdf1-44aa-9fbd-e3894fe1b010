<template>
  <div class="choose">
    <div>
      <ul class="goods-ul">
        <li
          v-for="(item, index) in listData"
          :key="index"
          :class="[index === chooseIndex ? 'goods-li choose-li' : 'goods-li']"
          @click="chooseList(item, index)"
        >
          <div class="img-box">
            <!-- @click.stop="lookImg(item.imageUrl)" -->
            <!-- <img :src="item.imageUrl" alt /> -->
            <el-image :src="item.imageUrl" :preview-src-list="[item.imageUrl]" @click.prevent>
              <div slot="error" class="image-slot">
                <el-image src="https://oss-ec.ybm100.com/ybm/product/defaultPhoto.jpg" />
              </div>
            </el-image>
          </div>
          <div class="con-box">
            <h4 v-if="item.showName">{{item.brand}} {{ item.showName }}</h4>
            <p v-if="item.standardProductId">ID：{{ item.standardProductId }}</p>
            <p v-if="item.spec">规格：{{ item.spec }}</p>
            <el-tooltip
              class="item"
              effect="dark"
              :content="item.manufacturer+(item.entrustedManufacturer?'('+item.entrustedManufacturer+')':'')"
              placement="top-start"
            >
              <p>
                <span v-if="item.manufacturer">生产厂家：{{ item.manufacturer }}</span>
                <span v-if="item.entrustedManufacturer">({{ item.entrustedManufacturer }})</span>
              </p>
            </el-tooltip>
            <el-tooltip
              class="item"
              effect="dark"
              :content="item.approvalNumber"
              placement="top-start"
            >
              <p v-if="item.approvalNumber">批准文号：{{ item.approvalNumber }}</p>
            </el-tooltip>
            <p v-if="item.code">条码：{{ item.code }}</p>
            <p v-if="item.drugClassification">处方类型：{{ item.drugClassification }}</p>
            <p v-if="item.storageCondition">存储条件：{{ item.storageCondition }}</p>
            <p v-if="item.term">有效期：{{ item.term }}</p>
            <p v-if="item.productUnit">包装单位：{{ item.productUnit }}</p>
            <p v-if="item.size">码数：{{ item.size }}</p>
            <p v-if="item.colour">颜色：{{ item.colour }}</p>
            <p v-if="item.flavor">口味：{{ item.flavor }}</p>
          </div>
          <p class="tip-box">
            <i class="el-icon-check" />
          </p>
          <p class="text-tip">
            {{
            item.result === 2
            ? '商品经营范围不在企业经营范围内'
            : item.result === 5
            ? '暂无销售权限'
            : ''
            }}
          </p>
        </li>
      </ul>
    </div>
    <el-image-viewer v-if="showViewer" :url-list="srcArr" :on-close="closeViewer" />
  </div>
</template>

<script>
import ElImageViewer from 'element-ui/packages/image/src/image-viewer';

export default {
  name: 'ChooseList',
  components: { ElImageViewer },
  props: {
    propData: {
      type: Array,
      default: () => []
    },
    isSubmit:{
      type: Boolean,
      default: () => false
    },
  },
  data() {
    return {
      sendData: {},
      listData: [],
      chooseIndex: null,
      sendSkuId: '',
      showViewer: false,
      srcArr: [],
      sendSkuIdCopy: ''
    };
  },
  watch: {
    propData: {
      handler(newVal) {
        this.listData = JSON.parse(JSON.stringify(newVal));
        this.initData();
      },
      immediate: true,
      deep: true,
    },
  },
  methods: {
    initData() {
      if (this.listData && this.sendSkuIdCopy) {
        const skuIds =  this.listData.map((item,index) => {
          return item.standardProductId;
        })
        if (skuIds.includes(this.sendSkuIdCopy)) {
          this.listData.forEach((item,index) => {
            if (this.sendSkuIdCopy == item.standardProductId) {
              this.sendSkuId = item.standardProductId;
              this.chooseIndex = index;
              this.sendData = item;
              this.sendSkuIdCopy = this.sendSkuId
            }
          });
        } else {
          this.sendSkuId = '';
          this.chooseIndex = null;
          this.sendData = {};
        }
      }
      if (this.isSubmit) {
        this.sendSkuId = '';
        this.chooseIndex = null;
        this.sendData = {};
        this.$emit('getSubmit', false)
      }
    },
    chooseList(data, index) {
      if (this.sendSkuId === data.standardProductId) {
        this.chooseIndex = null;
        this.sendSkuId = '';
        this.sendData = {};
        this.sendSkuIdCopy = this.sendSkuId
      } else {
        this.sendSkuId = data.standardProductId;
        this.chooseIndex = index;
        this.sendData = data;
        this.sendSkuIdCopy = this.sendSkuId
      }
      this.$emit('chooseGoods', this.sendData)
    },
    closeViewer() {
      this.showViewer = false;
    },
    lookImg(data) {
      this.srcArr.push(data);
      this.showViewer = true;
    }
  },
};
</script>

<style scoped lang="scss">
.choose {
  padding: 0 16px 16px;
  ::v-deep   .el-button--primary {
    background: #4183d5;
  }
  ::v-deep  .el-dialog__body {
    padding: 0 0 10px;
    height: 630px;
    overflow: auto;
  }
  ::v-deep   .el-dialog__header {
    padding: 10px 16px;
    background: #f9f9f9;
  }
  ::v-deep   .el-dialog__headerbtn {
    top: 13px;
  }
  ul,
  li {
    list-style: none;
    margin: 0;
  }
  .goods-detail {
    background: #fafafa;
    padding: 15px;
    margin-top: 10px;
    h3,
    p {
      padding: 0;
      margin: 0;
    }
    p {
      padding-top: 5px;
      font-size: 12px;
      color: #666666;
      span {
        margin-right: 10px;
      }
    }
  }
  .goods-tip {
    color: #ff8e00;
    font-size: 12px;
    padding: 8px 0;
    margin: 0;
  }
  .padding-box {
    padding: 0 15px;
  }
  .goods-ul {
    // box-sizing: border-box;
    display: flex;
    justify-content: flex-start;
    flex-wrap: wrap;
    padding: 0;
    .goods-li {
      width: 19%;
      height: auto;
      border: 1px solid #efefef;
      margin: 10px 10px 0px 0;
      display: inline-block;
      overflow: hidden;
      position: relative;
      background: #f9f9f9;
      border-radius: 4px;
      .img-box {
        padding: 10px 0 10px 0;
        text-align: center;
        display: flex;
        justify-content: center;
        align-items: center;
        background: #ffffff;
        height: 235px;
        width: 100%;
        ::v-deep   .el-image {
          max-height: 235px;
        }
      }
      .con-box {
        padding: 10px 10px 15px;
        h4,
        p {
          margin: 0;
          padding: 0;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
        p {
          padding-top: 5px;
          font-size: 12px;
          color: #666666;
        }
      }
      .tip-box {
        display: none;
        width: 20px;
        height: 20px;
        background: #4183d5;
        border-radius: 50%;
        color: #ffffff;
        position: absolute;
        top: 5px;
        right: 10px;
        margin: 0;
        text-align: center;
      }
      .text-tip {
        display: none;
        width: 100%;
        height: 24px;
        background: #eeeeee;
        font-size: 12px;
        color: #666666;
        text-align: center;
        position: absolute;
        top: 0;
        left: 0;
        margin: 0;
        line-height: 24px;
      }
    }
    li.choose-li {
      border-color: #4183d5;
      .tip-box {
        display: block;
      }
    }
    li.none-right {
      margin-right: 0;
    }
    li.disabled-li {
      .text-tip {
        display: block;
      }
    }
  }
}
</style>
