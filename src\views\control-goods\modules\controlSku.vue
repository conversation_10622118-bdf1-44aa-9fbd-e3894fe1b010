<template>
  <div class="main-box">
    <div class="explain-search searchMy">
      <el-form ref="ruleForm" size="small" :inline="true">
        <el-form-item prop="skuId">
          <el-input v-model.trim="searchData.skuId" oninput="value=value.replace(/[^0-9.]/g,'')" placeholder="sku编码">
            <template slot="prepend">sku编码</template>
          </el-input>
        </el-form-item>
        <el-form-item class="search-btn">
          <el-button type="primary" @click="getList('search')">查询</el-button>
          <el-button @click="resetForm()">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="list-box">
      <div style="padding: 10px 0px">
        <div class="customer-tabs">
          <el-table
            ref="goodTable"
            v-loading="laodingBoole"
            max-height="397"
            :data="tableData.list"
            stripe
            style="width: 100%"
            :header-cell-style="{ background: '#F9F9F9', color: '#666666' }"
          >
            <el-table-column prop="skuId" label="sku编码" />
            <el-table-column prop="erpCode" label="商品ERP编码" />
            <el-table-column prop="showName" label="商品展示名称" />
            <el-table-column prop="approvalNumber" label="批准文号" />
            <el-table-column prop="manufacturer" label="生产厂家" />
            <el-table-column prop="spec" label="规格" />
            <el-table-column prop="statusName" label="商品状态" />
          </el-table>
          <div class="explain-pag">
            <Pagination
              v-show="tableData.total > 0"
              :total="tableData.total"
              :page.sync="pageData.pageNum"
              :limit.sync="pageData.pageSize"
              @pagination="getList"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import Pagination from '@/components/Pagination';
import { skuPage } from '@/api/goods/controlGoods.js';

export default {
  name: 'ControlSku',
  components: { Pagination },
  data() {
    return {
      searchData: {
        skuId: '',
      },
      tableData: {
        total: 0,
        list: [],
      },
      pageData: {
        pageSize: 10,
        pageNum: 1,
      },
      laodingBoole: false,
      showLog: false,
    }
  },
  created() {
    // console.log('bbbbbb');
    // // 获取列表数据
    this.getList();
  },
  activated() {
    console.log('aaaaaa');
    this.getList();
  },
  methods: {
    // 获取列表数据
    getList(from) {      
      let groupId = this.$route.query.groupId;
      if (from == 'search') {
        this.pageData.pageNum = 1;
      }
      this.laodingBoole = true;
      const param = {
        groupId,
        ...this.searchData,
        ...this.pageData
      };
      skuPage(param).then((res) => {
        if (res.code === 0) {
          this.laodingBoole = false;
          if (res.data) {
            this.tableData.list = res.data.list;
          } else {
            this.tableData.list = [];
          }
          this.tableData.total = res.data.total;
        } else {
          this.laodingBoole = false;
          this.$message({
            message: res.message,
            type: 'error',
          });
        }
      }).catch(() => {
        this.laodingBoole = false;
      });
    },
    // 重置列表数据
    resetForm() {
      console.log('重置');
      this.searchData = {
        skuId: '',
      };
      this.pageData = {
        pageSize: 10,
        pageNum: 1,
      };
      this.getList();
    }
  }
}
</script>

<style lang="scss" scoped>
.searchMy ::v-deep  .el-form-item__label {
  margin-left: 20px;
  padding: 0;
}
.searchMy ::v-deep  .el-input-group__prepend {
  background: none;
  color: #333333;
  padding: 0 10px;
}
.searchMy ::v-deep   .el-form-item--small.el-form-item {
  width: 24%;
}

.main-box {
  padding: 20px;
}
.search-btn {
  width: 100% !important;
  text-align: right;
}
</style>