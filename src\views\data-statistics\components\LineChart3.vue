<template>
  <div class="search-info">
    <p><span />面积越大代表销量越大</p>
    <span class="sign" />
    <span class="title_line" style="margin-right: 5px">{{ titleInfo }}</span>
    <el-tooltip class="item" :content="tooltip" placement="top">
      <i class="el-icon-warning-outline"></i>
    </el-tooltip>
    <div class="chartBox" :id="chartId"></div>
  </div>
</template>

<script>
import * as echarts from 'echarts';

export default {
  name: 'LineChart3',
  props: {
    titleInfo: {
      type: String,
      default: '标题',
    },
    chartId: {
      type: String,
      default: '',
    },
    chartColor: {
      type: String,
      default: '#4184D5',
    },
    tooltip: {
      type: String,
      default: '',
    },
    chartConfig: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  data() {
    return { symbolSizeArr: [] };
  },
  mounted() {
    if (this.chartConfig && this.chartConfig.datum2) {
      if (this.chartConfig.datum2.length === 1) {
        this.symbolSizeArr.push(100);
      } else {
        const arr = [...Array(this.chartConfig.datum2.length).keys()];
        const max = Math.max(...this.chartConfig.datum2);
        const indexOfMax = this.chartConfig.datum2.indexOf(max);
        this.chartConfig.datum2.forEach((item, index) => {
          if (index === indexOfMax) {
            arr[index] = 100;
          } else {
            arr[index] = item / max * 100;
          }
        });
        this.symbolSizeArr = arr;
      }
    }
    this.initChart();
  },
  methods: {
    getChartId() {
      return this.chartId;
    },
    initChart() {
      const option = {
        tooltip: {
          trigger: 'axis',
          // formatter: function (params, ticket, callback) {
          //   console.log(params, ticket);
          //   const str = params.name + '<br />' + params.seriesName + '：' + params.value;
          //   return str;
          // }
        },
        toolbox: {
          show: true,
          feature: {
            // magicType: {            //动态类型切换
            //   show: true,           //是否显示该工具
            //   type: ['line', 'bar'], //启用的动态类型
            //   title: {
            //     line: '切换为折线图',
            //     bar: '切换为柱状图'
            //   },
            //   emphasis: {
            //     iconStyle: {
            //       color: '#4184D5',
            //       textFill: '#4184D5'
            //     }
            //   }
            // },
            saveAsImage: { // 保存为图片
              show: true, // 是否显示该工具
              title: '保存',
              emphasis: {
                iconStyle: {
                  color: '#4184D5',
                  textFill: '#4184D5',
                },
              },
            },
          },
        },
        xAxis: [
          {
            type: 'category',
            // boundaryGap: false,
            data: this.chartConfig.abscissa,
            axisPointer: { type: 'shadow' },
          },
        ],
        yAxis: [{ type: 'value' }],
        series: [
          {
            name: this.chartConfig.legend.data[1],
            type: 'line',
            symbol: 'circle',
            symbolSize: (value, params) => {
              console.log(params);
              return this.symbolSizeArr[params.dataIndex];
            },
            showAllSymbol: true,
            data: this.chartConfig.datum2,
            color: '#4184D5',
          },
          {
            name: this.chartConfig.legend.data[0],
            type: 'scatter',
            data: this.chartConfig.datum,
            itemStyle: {
              opacity: 0,
              color: 'orange',
            },
          },
        ],
      };
      this.$nextTick(() => {
        const chart = echarts.init(document.getElementById(this.chartId));
        if (chart) {
          chart.setOption(option);
        }
      });
    },
  },
};
</script>

<style scoped lang="scss">
.search-info {
  padding: 10px 20px;
  font-weight: 500;
  p {
    text-align: center;
    margin: 0 0 15px 0;
    display: flex;
    align-items: center;
    justify-content: center;
    span {
      width: 6px;
      height: 6px;
      border-radius: 50%;
      margin-right: 5px;
      background: #4183d5;
    }
  }
  .chartBox {
    width: 100%;
    height: 408px;
  }
}
</style>
