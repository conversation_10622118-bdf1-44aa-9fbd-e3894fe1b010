<template>
  <div class="drag-head">
    <div
      style="
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 10px;
      "
    >
      <div>
        <span style="font-weight: bold;color:#111111">已添加：</span>
        <span style="color:#555555">{{ list.length }} / {{ totalLength }} 展示顺序由左到右，可拖拽排序</span>
      </div>
      <el-input  class="inline-input" v-model="ModelName" placeholder="请输入指标名称" suffix-icon="el-icon-search" @change="handleSearch"></el-input>
    </div>

    <transition-group tag="div" class="dragContainer">

    <li
      @dragenter="dragenter($event, index)"
      @dragover="dragover($event, index)"
      @dragstart="dragstart(index)"
      @dragend="dragend(index)"
      draggable
      v-for="(item, index) in list"
      :key="item.id"
      :class="{'item':true ,'item-hover':isHover}"
     >
      {{item.moduleName}}
      <img class="item-img" v-if="isHover" src="@/assets/image/home/<USER>" alt="" @click="subItem(item.id)" />
    </li>

    </transition-group>
  </div>
</template>

<script>
import { mounted } from 'vue'
export default {
  name: 'drag',
  props: {
    list: {
      default: []

    },
    totalLength: {
      default: 0
    }
  },
  data() {
    return {
      ending: null,
      dragging: null,
      ModelName: '',
      endingIndex:'',
      dragging:'',
      newItems:[],
      isHover:true,
    }
  },
  watch: {
    list: {
      handler(val) {
        console.log('修改-son')
      }
    }
  },
  methods: {
    shuffle() {
      this.list = this.$shuffle(this.list);
    },
    handleSearch(val){
      this.$emit('handleSearch',val)
    },
    subItem(id) {
      this.$emit('subItem', id)
    },
    handleSelect(item) {
      console.log(item)
    },
    createFilter(queryString) {
      return (restaurant) => {
        return restaurant.value.toLowerCase().indexOf(queryString.toLowerCase()) === 0
      }
    },
    dragstart(index) {
      this.dragIndex = index;
      this.isHover = false
      console.log('%c [ this.isHover ]-89', 'font-size:13px; background:pink; color:#bf2c9f;', this.isHover)
    },
    dragenter(e, index) {

      e.preventDefault();
      // 避免源对象触发自身的dragenter事件
      if (this.dragIndex !== index) {
        const source = this.list[this.dragIndex];
        let newItems = [...this.list]
        newItems.splice(this.dragIndex, 1);
        newItems.splice(index, 0, source);
        newItems.forEach((item,index)=>{
          item.sort = index+1
        })
        this.$emit('updateDragList', newItems)
        // this.list.splice(this.dragIndex, 1);
        // this.list.splice(index, 0, source);
        this.$nextTick(() => {
        this.dragging = null
        this.ending = null
      })

        // 排序变化后目标对象的索引变成源对象的索引
        this.dragIndex = index;
      }

    },
    dragover(e, index) {
      e.preventDefault();
    },
    dragend(){
      this.isHover = true
    }
  },
  mounted() {
    const items = document.querySelector('.item')
    items.addEventListener('dragstart', function (e) {})

  }
}
</script>

<style lang="less" scoped>
.dragContainer {
  display: flex;
  flex-wrap: wrap;
  background-color: #fff;
}
.item {
  position: relative;
  transition: transform 0.15s;
  background-color: #f5f5f5;
  padding: 8px 21px;
  cursor: move;
  margin:0 20px 20px 0;
  border-radius: 4px;
  list-style: none;
  box-sizing:content-box;
  border:1px solid transparent;
  color:#333333;
}
.item-hover:hover{
  display: block;
  border:1px solid rgb(65, 132, 213);
}

.item-hover:hover .item-img {
  display: block;
}
.item-img {
  position: absolute;
  top: 0;
  right: 0;
  transform: translate(50%, -50%);
  cursor: pointer;
  display: none;
}
.inline-input{
  width:200px;
}
.list {
  display: flex;
  list-style: none;
  flex-wrap: wrap;
}
</style>
