<!-- 首页公告列表 -->
<template>
  <div class="noticeListContainer">
    <div class="noticeListTitle">
      <div>平台公告</div>
      <div>
        <el-link :underline="false" style="font-size: 16px; font-weight: bold" @click="moreNotice"
          >更多<i class="el-icon-arrow-right"></i
        ></el-link>
      </div>
    </div>

    <div class="noticeListBlock">
      <div :class="{'item':true,'gray-item':isGrayUser}" v-for="item in dataList" :key="item.id" @click="showNoticeHandle(item)">
        <div style="display: flex;align-items: center;">
          <img src="@/assets/image/home/<USER>" alt="" style="height:18px;margin-right:6px;" v-if="item.redTitle&&isGrayUser">
           <span :style="item.redTitle&&!isGrayUser && 'color:red'" style="font-size: 14px;">{{ item.title }}</span>

        </div>
        <div style="font-size:14px;">
          {{ formatDate(item.updateTime, 'YMD') }}
        </div>
      </div>
    </div>

    <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="70%">
      <div v-html="dialogContent"></div>
      <span slot="footer" class="dialog-footer"> </span>
    </el-dialog>
  </div>
</template>

<script>
import { getBannerPageList } from '@/api/home'
import { mapState } from 'vuex';
export default {
  components: {},
  computed:{    ...mapState({ isGrayUser: state => state.app.isGrayUser }),},
  data() {
    return {
      dataList: [],
      dialogVisible: false,
      dialogTitle: '',
      dialogContent: ''
    }
  },
  watch: {},
  methods: {
    async queryData() {
      const res = await getBannerPageList({
        pageNum: 1,
        pageSize: 5,
        status: 1
      })
      if (res && res.code === 0) {
        this.dataList = res.result.list
      }
    },

    // 打开公告diallog
    showNoticeHandle(item) {
      this.dialogTitle = item.title
      this.dialogContent = item.content
      this.dialogVisible = true
    },

    // 查看更多
    moreNotice(){
      // const path = '/noticeList'
      // window.openTab(path)

      this.$router.push('/noticeList')
    },
  },
  created() {},
  mounted() {
    this.queryData()
  }
}
</script>
<style lang="scss" scoped>
.noticeListContainer {
  background-color: #fff;
  padding: 16px;
  margin-bottom: 16px;
  .noticeListTitle {
    display: flex;
    justify-content: space-between;
    font-weight: bold;
    font-size: 16px;
  }
  .noticeListBlock {
    margin-top: 5px;
    .item {
      display: flex;
      justify-content: space-between;
      margin: 16px 0 0;
      font-size: 14px;
      cursor: pointer;
      div:first-child {
        overflow: hidden; /*也可以用 width:0 代替*/
        flex: 1; /*关于flex的属性值请看官网，这里相当于 flex-grow */

        span {
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;

        }
      }

      div:last-child {
        width: 100px;
        text-align: right;
      }
    }
  }
}
.gray-item{
  font-size: 16px !important;
  margin-top:17px !important;
  color:#111111;
  display: inline-block;
}
.importantTitle{
  border: 1px solid #FFEDED;
  color:#FF4444;
  margin-right: 5px;
  padding:2px;
  border-radius: 3px;
  font-size: 12px;
}
</style>
