<template>
  <div>
    <div class="serch">
      <el-row
        type="flex"
        align="middle"
      >
        <span class="sign" />
        <div>供货对象设置</div>
      </el-row>
    </div>
    <div class="tips">
      <div>1、可设置店铺可售卖或不可售卖的客户类型。</div>
      <div>2、已勾选的客户类型，对店铺内对应客户所在省份的在售商品可见可买。未勾选的客户类型，对店铺内对应客户所在省份的在售商品不可见不可买。</div>
      <div>3、客户能够购买店铺的指定商品，需要同时满足以下三个条件：</div>
      <div>3.1）客户不在店铺控销黑名单内</div>
      <div>3.2）客户对应的客户类型在店铺的供货对象中已勾选；</div>
      <div>3.3）指定商品对该客户没有设置控销或控销设置为可见可买；</div>
    </div>
    <div class="serch">
      <el-row
          type="flex"
          align="middle"
      >
        <span class="sign" />
        <div>选择客户类型</div>
      </el-row>
    </div>
    <div class="customerType">
      <el-checkbox
        :indeterminate="isIndeterminate"
        v-model="checkAll"
        :disabled='disabledStatus'
        @change="handleCheckAllChange"
        class="checkedall"
      >全选
      </el-checkbox>
      <el-checkbox-group
        v-model="checkedCustomers"
        @change="handleCheckedTypesChange"
      >
        <el-checkbox
          v-for="(item,index) in customerTypes"
          :label="item.businessTypeId"
          :disabled='disabledStatus'
          style="marginBottom:10px;"
          :key="index"
        >{{item.businessTypeName}}
        </el-checkbox>
      </el-checkbox-group>
    </div>
    <div class="updateBtn">
      <el-button
          v-permission="['shop_customerConfig_edit']"
          size="small"
          style="float:right;"
          v-if="disabledStatus"
          @click="editBtn"
          type="primary"
      >编 辑
      </el-button>
      <el-button
          size="small"
          style="float:right;"
          v-if="!disabledStatus"
          @click="submitBtn"
          type="primary"
      >保 存
      </el-button>
    </div>
  </div>
</template>

<script>
import { getBusinessTypeList, getSelectedBusinessType, storeSupplyObject } from '@/api/supplyObject/index';
export default {
  name:'supplyObject',
  data() {
    return {
        checkAll: false,
        checkedCustomers: [],
        customerTypes: [],
        isIndeterminate: true,
        disabledStatus:true
    }
  },
  created() {
    this.getBusinessTypeList();
    this.getSelectedBusinessType();
  },
  methods:{
    handleCheckAllChange(val) {
      const checkAllId = this.customerTypes.map((item=>{
        return item.businessTypeId
      }))
      this.checkedCustomers = val ? checkAllId : [];
      this.isIndeterminate = false;
    },
    handleCheckedTypesChange(value) {
      let checkedCount = value.length;
      this.checkAll = checkedCount === this.customerTypes.length;
      this.isIndeterminate = checkedCount > 0 && checkedCount < this.customerTypes.length;
    },
    editBtn() {
      this.disabledStatus = !this.disabledStatus
    },
    submitBtn() {
      const selectedCustomers = this.checkedCustomers.map((item)=>{return item})
      storeSupplyObject(selectedCustomers).then((res)=>{
        if(res.code === 0){
          this.$message.success('保存成功')
        }else{
          this.$message.error(res.message)
        }
      })
      this.disabledStatus = !this.disabledStatus
    },
    getBusinessTypeList() {
      getBusinessTypeList().then((res)=>{
        if(res.code === 0){
          this.customerTypes = res.data || []
        }else{
          this.$message.error(res.message);
        }
      })
    },
    getSelectedBusinessType() {
      getSelectedBusinessType().then((res)=>{
        if(res.code === 0){
          this.checkedCustomers = res.data || []
        }else{
          this.$message.error(res.message);
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
.serch {
  font-weight: bold;
  padding: 15px 20px 0;
  .sign {
    display: inline-table;
    width: 3px;
    height: 13px;
    background: linear-gradient(360deg, #1d69c4 1%, #8bbdfc 99%);
    border-radius: 2px;
    margin-right: 8px;
  }
}
.tips {
  opacity: 1;
  margin-top: 5px;
//   background: #fafafa;
  border-radius: 4px;
  padding: 8px 16px;
  div {
    font-size: 14px;
    font-family: PingFangSC, PingFangSC-Regular;
    font-weight: 400;
    text-align: left;
    color: #333333;
    line-height: 24px;
  }
}
.customerType{
  margin: 10px 32px;
  border: 1px solid #eeeeee;
  border-radius: 4px;
  ::v-deep  .el-checkbox{
    width: 14%;
    margin-left: 10px;
  }
  ::v-deep  .checkedall{
    width: 100%;
    padding: 10px;
    background: #f9f9f9;
    margin-left: 0;
    margin-bottom: 10px;
  }
  ::v-deep  .el-checkbox__input.is-checked + .el-checkbox__label{
    color: #333333;
  }
}
.updateBtn{
  padding-right: 40px;
  text-align: right;
}
</style>
