/**
 * created by <PERSON><PERSON><PERSON><PERSON><PERSON> in 2021/3/15
 */
export const provinceAndMerchantType = {
  appName: 'MarketingAdmin',
  code: 1000,
  status: 'success',
  msg: '成功',
  data: {
    allMerchantTypes: [
      {
        id: '1',
        value: '单体药店'
      },
      {
        id: '2',
        value: '连锁加盟'
      },
      {
        id: '3',
        value: '连锁直营'
      },
      {
        id: '4',
        value: '基层医疗机构-诊所'
      },
      {
        id: '5',
        value: '药品批发'
      },
      {
        id: '6',
        value: '民营医院'
      },
      {
        id: '7',
        value: '公立医院'
      },
      {
        id: '8',
        value: '基层医疗机构-卫生院'
      },
      {
        id: '9',
        value: '基层医疗机构-社区卫生服务站'
      },
      {
        id: '10',
        value: '基层医疗机构-卫生室'
      },
      {
        id: '11',
        value: '医疗机构'
      },
      {
        id: '12',
        value: '终端'
      },
      {
        id: '13',
        value: '基层医疗机构'
      },
      {
        id: '14',
        value: '基层医疗机构-门诊部'
      },
      {
        id: '15',
        value: '基层医疗机构-其他'
      },
      {
        id: '16',
        value: '药品生产'
      },
      {
        id: '17',
        value: '非药类生产'
      },
      {
        id: '18',
        value: '境外企业'
      },
      {
        id: '19',
        value: '非药类经营'
      },
      {
        id: '20',
        value: '其他企业'
      },
      {
        id: '21',
        value: '连锁总部'
      }
    ],
    region: [
      { areaCode: 420000, areaName: '湖北省', parentCode: -9999, areaLevel: 1 },
      { areaCode: 350000, areaName: '浙江省', parentCode: -9999, areaLevel: 1 }
    ]
  }
}
const region = new Map([
  [
    350000,
    [
      {
        areaCode: 350100,
        parentCode: 350000,
        areaName: '福州市',
        level: 2,
        childRegion: [
          {
            areaCode: 350112,
            parentCode: 350100,
            areaName: '长乐区',
            level: 3
          },
          {
            areaCode: 350104,
            parentCode: 350100,
            areaName: '仓山区',
            initials: 'csq',
            level: 3
          },
          {
            areaCode: 350181,
            parentCode: 350100,
            areaName: '福清市',
            level: 3
          }
        ]
      },
      {
        areaCode: 350200,
        parentCode: 350000,
        areaName: '厦门市',
        initials: 'sms',
        level: 2,
        status: 1,
        childRegion: [
          {
            areaCode: 350205,
            parentCode: 350200,
            areaName: '海沧区',
            initials: 'hcq',
            level: 3
          },
          {
            areaCode: 350206,
            parentCode: 350200,
            areaName: '湖里区',
            initials: 'hlq',
            level: 3
          },
          {
            areaCode: 350211,
            parentCode: 350200,
            areaName: '集美区',
            initials: 'jmq',
            level: 3,
            oldAreaCode: 0,
            oldAreaName: 'NULL'
          }
        ]
      }
    ]
  ],
  [
    420000,
    [
      {
        areaCode: 420100,
        parentCode: 420000,
        areaName: '武汉市',
        initials: 'whs',
        level: 2,
        status: 1,
        pinyin: 'Wuhan',
        shortName: '武汉',
        zipCode: 430014,
        cityCode: '027',
        lng: '114.298569',
        lat: '30.584354',
        areaCodePath: 'NULL',
        areaNamePath: 'NULL',
        sort: 1,
        childRegion: [
          {
            id: 261564,
            areaCode: 420114,
            parentCode: 420100,
            areaName: '蔡甸区',
            initials: 'cdq',
            level: 3
          },
          {
            id: 261320,
            areaCode: 420112,
            parentCode: 420100,
            areaName: '东西湖区',
            initials: 'dxhq',
            level: 3
          },
          {
            id: 261493,
            areaCode: 420113,
            parentCode: 420100,
            areaName: '汉南区',
            initials: 'hnq',
            level: 3
          },
          {
            id: 260591,
            areaCode: 420105,
            parentCode: 420100,
            areaName: '汉阳区',
            initials: 'hyq',
            level: 3
          },
          {
            id: 260999,
            areaCode: 420111,
            parentCode: 420100,
            areaName: '洪山区',
            initials: 'hsq',
            level: 3
          },
          {
            id: 262430,
            areaCode: 420116,
            parentCode: 420100,
            areaName: '黄陂区',
            initials: 'hpq',
            level: 3,
            status: 1
          },
          {
            id: 260141,
            areaCode: 420102,
            parentCode: 420100,
            areaName: '江岸区',
            initials: 'jaq',
            level: 3
          },
          {
            id: 260317,
            areaCode: 420103,
            parentCode: 420100,
            areaName: '江汉区',
            initials: 'jhq',
            level: 3
          },
          {
            id: 261982,
            areaCode: 420115,
            parentCode: 420100,
            areaName: '江夏区',
            initials: 'jxq',
            level: 3
          },
          {
            id: 260443,
            areaCode: 420104,
            parentCode: 420100,
            areaName: '硚口区',
            initials: 'qkq',
            level: 3
          },
          {
            id: 260880,
            areaCode: 420107,
            parentCode: 420100,
            areaName: '青山区',
            initials: 'qsq',
            level: 3
          },
          {
            id: 260721,
            areaCode: 420106,
            parentCode: 420100,
            areaName: '武昌区',
            initials: 'wcq',
            level: 3
          },
          {
            id: 263139,
            areaCode: 420117,
            parentCode: 420100,
            areaName: '新洲区',
            initials: 'xzq',
            level: 3
          },
          {
            id: 729702,
            areaCode: 420118,
            parentCode: 420100,
            areaName: '经济技术开发区',
            initials: 'jjjskfq',
            level: 3,
            status: 1,
            pinyin: 'Jingjijishukafaqu',
            shortName: '经济技术开发区',
            zipCode: 430056,
            cityCode: '027',
            lng: 'NULL',
            lat: 'NULL',
            areaCodePath: 'NULL',
            areaNamePath: 'NULL',
            sort: 15,
            oldAreaCode: 0,
            oldAreaName: 'NULL'
          },
          {
            id: 729703,
            areaCode: 420119,
            parentCode: 420100,
            areaName: '东湖新技术开发区',
            initials: 'dhxjskfq',
            level: 3,
            status: 1,
            pinyin: 'Donghuxinjishukaifqu',
            shortName: '东湖新技术开发区',
            zipCode: 430223,
            cityCode: '027',
            lng: 'NULL',
            lat: 'NULL',
            areaCodePath: 'NULL',
            areaNamePath: 'NULL',
            sort: 16,
            oldAreaCode: 0,
            oldAreaName: 'NULL'
          }
        ]
      },
      {
        areaCode: 420200,
        parentCode: 420000,
        areaName: '黄石市',
        level: 2,
        childRegion: [
          {
            id: 264444,
            areaCode: 420281,
            parentCode: 420200,
            areaName: '大冶市',
            initials: 'dys',
            level: 3
          },
          {
            id: 263815,
            areaCode: 420202,
            parentCode: 420200,
            areaName: '黄石港区',
            initials: 'hsgq',
            level: 3
          },
          {
            id: 263943,
            areaCode: 420205,
            parentCode: 420200,
            areaName: '铁山区',
            initials: 'tsq',
            level: 3
          },
          {
            id: 263899,
            areaCode: 420204,
            parentCode: 420200,
            areaName: '下陆区',
            initials: 'xlq',
            level: 3
          },
          {
            id: 263854,
            areaCode: 420203,
            parentCode: 420200,
            areaName: '西塞山区',
            initials: 'xssq',
            level: 3
          },
          {
            id: 263959,
            areaCode: 420222,
            parentCode: 420200,
            areaName: '阳新县',
            level: 3
          }
        ]
      }
    ]
  ]
])
export const regionData = function (data) {
  const { provinceCode } = data
  return {
    appName: 'MarketingAdmin',
    code: 1000,
    status: 'success',
    msg: '成功',
    data: region.get(provinceCode)
  }
}
