<template>
  <div class="companyOpenAccount">
    <div
      class="headerStatus"
    >
      <div class="header-title">
        <h4>银行打款验证</h4>
        <span>请将银行的打款金额和短信中的鉴权序号填入进行核验</span>
      </div>
      <el-button
        type="primary"
        @click="$router.go(-1)"
      >
        返回企业开户页面
      </el-button>
    </div>
    <div
      class="leftContent"
    >
      <div class="leftContent-header">
        <h4>平安银行已向您的收款账户小额打款，并向您提交的手机号 {{ accountContactsVo.phone }} 发送了鉴权短信</h4>
        <div class="header-list">
          <div class="header-list-item">1、请您登录银行账户查询收到的打款金额并回填；</div>
          <div class="header-list-item">2、请您查询短信中的鉴权序号并回填。</div>
        </div>
      </div>
      <div class="leftContent-main">
        <div class="leftContent-main-list">
          <div class="leftContent-item">打款账号：橙E付网络支付手续费支出科目</div>
          <div class="leftContent-item">打款时间：{{ accountVo.paymentTime }} <span class="item-line"></span> 实际到账时间以银行为准</div>
          <div class="leftContent-item">收款账户：<b>{{ accountVo.registeredName }}</b></div>
          <div class="leftContent-item">收款银行账号：{{ accountVo.registeredBankAccount }}</div>
          <div class="leftContent-item">商家手机号：{{ accountContactsVo.phone }}</div>
        </div>
        <div class="leftContent-main-payment">
          <div class="payment-item">
            <div class="payment-item-label"><span>*</span>打款金额</div>
            <div class="payment-item-input">
              <el-input
                v-model="formModel.money"
                placeholder="请输入银行账号收到的转账金额"
                maxlength="10"
                oninput="value=value.toString().match(/^\d+(?:\.\d{0,2})?/)"
              />
              <div class="payment-item-input-tips">*银行打款可能存在一定延时，请您在账户收到打款后48小时内进行验证</div>
            </div>
          </div>
          <div class="payment-item">
            <div class="payment-item-label"><span>*</span>鉴权序号</div>
            <div class="payment-item-input">
              <el-input
                v-model="formModel.authNo"
                placeholder="请输入短信提示的鉴权序号"
                maxlength="10"
                oninput="value=value.toString().match(/^\d+(?:\.\d{0,2})?/)"
              />
              <div class="payment-item-input-tips">未收到鉴权序号短信？<span v-if="!sendResetSuccessDisabled" @click="resetCheck">重新发起验证</span><p v-else>{{ sendResetSuccessCountDown }}s后可重新发起验证</p></div>
            </div>
          </div>
        </div>
        <div class="payment-btn">
          <el-button 
            type="primary"
            :loading="loading"
            @click="paymentVerification"
          >执行打款验证</el-button>
        </div>
      </div>
    </div>
    <el-dialog
      title="重新发起验证"
      :visible.sync="resetDialogFlag"
      width="688px"
    >
      <div class="reset-dialog-content">
        <div class="reset-title">若当前商户手机号<span>{{ accountContactsVo.phone }}</span>无法收到平安银行的鉴权短信，建议您更换手机号重新发起验证</div>
        <div class="bank-payment-item">
          <div class="payment-title">
            <span>商户手机号</span>
          </div>
          <el-input placeholder="请输入商户手机号" maxlength="30" v-model.trim="resetOtp.corporatePhone" style="width: 100%" />
        </div>
        <div class="bank-payment-item">
          <div class="payment-title">
            <span>短信验证码</span>
          </div>
          <div class="payment-content otpItem">
            <el-input placeholder="请输入验证码" maxlength="30" v-model.trim="resetOtp.activeCode" style="width: 100%" />
            <el-button class="otpBtn" type="primary" @click="sendResetOtp" :disabled="sendResetDisabled">获取验证码{{ sendResetDisabled ? `(${sendResetCountDown})` : '' }}</el-button>
          </div>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="resetOk">提交</el-button>
      </span>
    </el-dialog>
</div>
    
</template>

<script>
import { paymentAuth, sendActiveCode, updateBankAccountForPA, bankQuery } from '@/api/companyOpenAccount';
export default {
    data() {
      return {
        loading: false,
        sendResetSuccessDisabled: false,
        sendResetSuccessCountDown: 120,
        formModel: {
          money: "",
          authNo: '',
        },
        resetDialogFlag: false,
        resetOtp: {
          corporatePhone: "",
          activeCode: ""
        },
        sendResetDisabled: false,
        sendResetCountDown: 60,
        accountContactsVo: {},
        accountVo: {},
        jpersonVo: {},
      }
    },
    mounted() {
      this.getInfo();
    },
    methods: {
      resetSubmit() {
        // this.paymentVerification();
      },
      paymentVerification() {
        console.log(this.$route, "route")
        if (this.formModel.money === "") {
          this.$message.warning('请输入打款金额！');
          return;
        } else if (this.formModel.authNo === "") {
          this.$message.warning('请输入鉴权序号！');
          return;
        }
        const params = {
          authType: this.$route.query.authType,
          money: this.formModel.money,
          authNo: this.formModel.authNo,
        };
        this.loading = true;
        paymentAuth(params).then((res) => {
          this.loading = false;
          if (res.code === 0) {
            this.$message.success('打款验证成功');
            // this.handleClose();
            // this.$emit('updateInfo');
          } else {
            let str = res.msg;
            if (res.code === 2) {
              str = '银行卡审核失败：验证已失效，请重新发起';
            }
            this.$confirm(str, `${res.code === 1 ? '打款验证失败提醒' : '银行卡审核失败'}`, {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
            })
              .then(() => {
                if (res.code === 2) {
                  // this.handleClose();
                  // this.$emit('updateInfo');
                }
              })
              .catch(() => {});
          }
        });
      },
      getParams() {
        const accountContactsVo = { ...this.accountContactsVo };
        delete accountContactsVo.areaList;
        delete accountContactsVo.cityList;
        const accountVo = { ...this.accountVo };
        const jpersonVo = { ...this.jpersonVo };
        if (jpersonVo.jpersonLongTerm) {
          jpersonVo.jpersonCardEndTime = new Date('2099-12-31').getTime();
        }
        delete jpersonVo.jpersonLongTerm;
        const params = { ...accountContactsVo, ...accountVo, ...jpersonVo };
        delete params.id;
        delete params.orgId;
        return params;
      },
      resetClose() {
        this.resetDialogFlag = false;
      },
      resetCheck() {
        this.resetDialogFlag = true;
        this.resetOtp.corporatePhone = this.accountContactsVo.phone;
        this.resetOtp.activeCode = "";
      },
      async getInfo() {
        const res = await bankQuery()
        if (res && res.code === 0) {
          const {accountContactsVo, accountVo, corBaseVo, jpersonVo} = res.result;
          this.accountContactsVo = {...accountContactsVo}
          this.accountVo = {...accountVo}
          this.corBaseVo = {...corBaseVo}
          // this.jpersonVo = {...jpersonVo}
          this.jpersonVo.jpersonName = jpersonVo.juridicalPersonName;
          this.jpersonVo.jpersonSex = jpersonVo.sex;
          this.jpersonVo.jpersonCardNumber = jpersonVo.cardNumber;
          this.jpersonVo.jpersonCardStartTime = jpersonVo.cardStartTime;
          this.jpersonVo.jpersonCardEndTime = jpersonVo.cardEndTime;
          this.jpersonVo.jpersonPermanentAddress = jpersonVo.permanentAddress;
          this.jpersonVo.jpersonCardIssueAddress = jpersonVo.cardIssueAddress;
          this.jpersonVo.jpersonPhone = jpersonVo.phone;
          this.jpersonVo.jpersonCardUrl = jpersonVo.cardUrl;
          this.jpersonVo.jpersonCardReverseUrl = jpersonVo.cardReverseUrl;
          if (this.jpersonVo.jpersonCardEndTime === '') {
            // this.jpersonLongTermChange(true);
            this.jpersonVo.jpersonLongTerm = true;
          }
        } else {
          this.$message.error(res.msg || '信息查询失败')
        }
      },
      async resetOk() {
        if (this.resetOtp.corporatePhone === "") {
          this.$message.warning('请输入商户手机号！');
          return;
        }
        if (this.resetOtp.activeCode === "") {
          this.$message.warning('请输入验证码！');
          return;
        }
        const params = this.getParams();
        params.resendCode = 1;
        params.smsVerificationCode = this.resetOtp.activeCode;
        params.codeType = 3;
        params.phone = this.resetOtp.corporatePhone;
        const res = await updateBankAccountForPA(params);
        if (res && res.code === 0) {
          this.$message.success('提交成功');
          this.resetDialogFlag = false;
          this.sendResetSuccessDisabled = true;
          this.timeOutSendResetOutCheck();
        } else {
          this.$alert(res.msg || '提交失败', '提示', {
            confirmButtonText: '确定',
            callback: action => {},
          });
        }
      },
      async sendResetOtp() {
        if (this.resetOtp.corporatePhone === "") {
          this.$message.warning('请输入商户手机号！');
          return;
        }
        this.sendResetDisabled = true;
        this.sendResetCountDown = 60;
        this.timeOutSendReset();
        const res = await sendActiveCode({phone: this.resetOtp.corporatePhone, codeType: 3 })
        if (res && res.code === 0) {
          this.$message.success('验证码发送成功')
        } else {
          this.$message.error(res.msg)
        }
      },
      timeOutSendReset() {
        setTimeout(() => {
          if (this.sendResetCountDown > 0) {
            this.sendResetCountDown --;
            this.timeOutSendReset();
          } else {
            this.sendResetDisabled = false;
            this.sendResetCountDown = 60;
          }
        }, 1000);
      },
      timeOutSendResetOutCheck() {
        setTimeout(() => {
          if (this.sendResetSuccessCountDown > 0) {
            this.sendResetSuccessCountDown--;
            this.timeOutSendResetOutCheck();
          } else {
            this.sendResetSuccessDisabled = false;
            this.sendResetSuccessCountDown = 120;
          }
        }, 1000);
      }
    }
}
</script>



<style scoped lang="scss">

.floatIcon {
  border-radius: 50%;
  padding: 7px 10px;
  position: absolute;
  top: 0px;
  background: red;
}
.el-button {
  padding: 8px 20px;
}

.el-button.is-circle {
  padding: 7px;
  border: none;
}

.companyOpenAccount {
  //min-width: 1400px;
  width: 100%;
  height: 100%;
  position: relative;
  padding-top: 50px;

  .NoCreatedStore {
    width: 100%;
    padding-left: 30px;
    line-height: 77px;
    font-size: 18px;
    color: #ffa012;
    background: #fffaf2;
    position: absolute;
    top: 0;
    left: 0;
  }

  .headerStatus {
    position: absolute;
    top: 0;
    left: 0;
    //min-width: 1400px;
    width: 100%;
    height: 50px;
    background: #fffbf1;
    display: flex;
    justify-content: space-between;
    align-items: center;
    z-index: 100;
    padding: 0 20px;
    box-sizing: border-box;
    .header-title {
      display: flex;
      align-items: center;
      h4 {
        font-family: PingFangSC-Medium;
        font-weight: 500;
        font-size: 16px;
        color: #333333;
      }
      span {
        font-family: PingFangSC-Regular;
        font-weight: 400;
        font-size: 12px;
        color: #666666;
        margin-left: 20px;
      }
    }
   
  }

  .leftContent {
    width: 100%;
    height: 100%;
    overflow-y: auto;
    padding: 30px 16px 0;
    .leftContent-header {
      background: #F9F9F9;
      border: 1px solid #F2F2F2;
      border-radius: 4px;
      padding: 18px 16px;
      box-sizing: border-box;
      h4 {
        font-family: PingFangSC-Medium;
        font-weight: 500;
        font-size: 16px;
        color: #333333;
        margin: 0;
      }
      .header-list {
        margin-top: 10px;
        .header-list-item {
          font-family: PingFangSC-Regular;
          font-weight: 400;
          font-size: 14px;
          color: #F5222D;
          line-height: 24px;
        }
      }
    }
    .leftContent-main {
      padding: 30px 16px;
      box-sizing: border-box;
      .leftContent-main-list {
        display: flex;
        flex-direction: column;
        .leftContent-item {
          font-family: PingFangSC-Regular;
          font-weight: 400;
          font-size: 14px;
          color: #222222;
          display: flex;
          align-items: center;
          margin-bottom: 12px;
          .item-line {
            width: 1px;
            background: #D9D9D9;
            height: 16px;
            margin: 0 10px;
            display: flex;
          }
          b {
            font-family: PingFangSC-Medium;
            font-weight: 500;
          }
        }
      }
      .leftContent-main-payment {
        .payment-item {
          width: 55%;
          display: flex;
          margin-top: 24px;
          .payment-item-label {
            color: #333333;
            font-size: 14px;
            font-face: PingFangSC;
            margin-right: 10px;
            font-weight: 400;
            line-height: 40px;
            span {
              color: #F5222D;
            }
          }
          .payment-item-input {
            flex: 1;
            display: flex;
            flex-direction: column;
            .payment-item-input-tips {
              margin-top: 5px;
              font-family: PingFangSC-Regular;
              font-weight: 400;
              font-size: 14px;
              color: #666666;
              display: flex;
              align-items: center;
              span {
                font-size: 14px;
                font-face: PingFangSC;
                font-weight: 400;
                color: #00B377;
                cursor: pointer;
              }
            }
          }
        }
      }
      .payment-btn {
        margin-top: 20px;
        padding-left: 70px;
      }
    }
    ::v-deep   .el-form {
      width: 80%;

      .el-select {
        margin-right: 14px;
      }

      .el-form-item__label {
        font-size: 12px;
        line-height: 30px;
      }

      .el-form-item__content {
        line-height: 30px;
      }

      .el-input__inner {
        line-height: 30px;
        height: 30px;
        font-size: 12px;
      }

      .el-input__icon {
        line-height: 30px;
      }
    }

    ::v-deep   .el-table__body .el-form-item {
      padding: 20px 0;
    }

    .addrForm .el-form-item {
      display: inline-block;
    }

    .avatar-uploader ::v-deep   .el-upload {
      border: 1px dashed #d9d9d9;
      border-radius: 6px;
      cursor: pointer;
      position: relative;
      overflow: hidden;
    }

    .avatar-uploader ::v-deep   .el-upload:hover {
      border-color: #409eff;
    }

    .avatar-uploader-icon {
      font-size: 28px;
      color: #8c939d;
      width: 178px;
      height: 178px;
      line-height: 178px;
      text-align: center;
    }

    .avatar {
      width: 178px;
      height: 178px;
      display: block;
    }
  }

  .leftContent::-webkit-scrollbar {
    width: 0 !important;
  }
  .reset-dialog-content {
    .reset-title {
      color: #666666;
      font-size: 14px;
      font-face: PingFangSC;
      font-weight: 400;
      margin-bottom: 20px;
      span {
        color: #00B377;
      }
    }
    .bank-payment-item {
      display: flex;
      // flex-direction: column;
      margin-bottom: 10px;
      align-items: center;
      .bank-tips {
        width: 100%;
        display: flex;
        justify-content: flex-end;
        p {
          width: 300px;
          font-size: 14px;
          font-face: PingFangSC;
          font-weight: 400;
          color: #9595a6;
          span {
            color: #00B955;
            cursor: pointer;
          }
        }
      }
      .payment-title {
        margin-right: 10px;
        display: flex;
        justify-content: flex-end;
        width: 15%;
        >span {
          font-family: PingFangSC-Medium;
          font-weight: 500;
          font-size: 12px;
          color: #676773;
        }
        p {
          font-size: 12px;
          font-face: PingFangSC;
          font-weight: 400;
          line-height: 0;
          >span {
            color: #00b955;
          }
        }
      }
      .el-input {
        flex: 1;
      }
      .payment-content {
        flex: 1;
        display: flex;
        overflow: hidden;
        // width: 270px;
      }
      .otpItem {
        .el-button {
          height: 40px;
          margin-left: 10px;
          background: #fff;
          border-radius: 2px;
          font-family: PingFangSC-Regular;
          font-weight: 400;
          font-size: 12px;
          color: #01B377;
          border: 1px solid #00B955;
        }
      }
    }
  }
}
</style>
