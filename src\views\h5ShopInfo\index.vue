
<template>
  <div class="shopInfoBox">
    <div class="tabBox">
      <div class="tabItem" v-for="item in tabData" :key="item.key" @click="changeTab(item.key)">
        <div class="titleText" :class="{activeTitle: activeTab === item.key}">{{ item.title }}</div>
        <div v-if="activeTab === item.key" class="titleBorder"></div>
      </div>
    </div>
    <div class="contentBox" @touchstart="touchstart" @touchmove="touchmove" @touchend="touchend">
      <!-- 基础信息 -->
      <BascInfo v-if="activeTab === 0" />
      <!-- 资质信息 -->
      <QualInfo v-else-if="activeTab === 1" />
      <!-- 服务质量 -->
      <ServiceQuality v-else-if="activeTab === 2" />
    </div>
  </div>
</template>

<script>
  import BascInfo from './bascInfo.vue';
  import QualInfo from './qualInfo.vue';
  import ServiceQuality from './serviceQuality.vue';

  export default {
    name: 'h5ShopInfo',
    components: { BascInfo, QualInfo, ServiceQuality },
    props: {
      corporationType: {
        type: Number,
        default: 0,
      },
    },
    data() {
      return {
        isStatus: false,
        startX: 0,
				startY: 0,
				endX: 0,
				endY: 0,
        activeTab: 0,
        tabData: [{
          title: '基础信息',
          key: 0,
        }, {
          title: '资质信息',
          key: 1,
        }, {
          title: '服务质量',
          key: 2,
        }],
        
      }
    },
    methods: {
      changeTab(key) {
        console.log('传来的key', key);
        window.scrollTo(0, 0);
        this.activeTab = key;
        console.log('改变之后', this.activeTab);
      },
      /* 监听滑动开始 */
			touchstart(e) {
				this.startX = e.touches[0].pageX;
				this.startY = e.touches[0].pageY;
			},
			/* 监听滑动移动 */
			touchmove(e) {
				this.isStatus= true;
				this.endX = e.touches[0].pageX;
				this.endY = e.touches[0].pageY;
			},
      /* 监听滑动结束 */
			touchend(e) {
        // e.preventDefault();
				/* 判断移动方向 */
				let X = this.endX - this.startX,
					Y = this.endY - this.startY;
				/* 判断是否移动还是点击 */
				if (this.isStatus) {
					if (X > 0 && Math.abs(X) > Math.abs(Y)) {
            // 向右
            if (this.activeTab > 0) {
              console.log('向右');
              this.changeTab(this.activeTab-1);
            }
					} else if (X < 0 && Math.abs(X) > Math.abs(Y)) {
						// 向左
            if (this.activeTab < 2) {
              console.log('向左');
              this.changeTab(this.activeTab+1);
            }
					}
				}
			},
    }
  }
</script>
<style lang="scss" scoped>
.shopInfoBox {
  background: #F1F6F9;
  min-height: 100vh;
  overflow-y: scroll;
  .tabBox {
    background: #fff;
    display: flex;
    align-items: baseline;
    justify-content: space-evenly;
    z-index: 100;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    .tabItem {
      margin: 0.23rem 0 0;
      .titleText {
        font-size: 0.28rem;
        color: #575766;
      }
      .activeTitle {
        font-size: 0.3rem;
        color: #292933;
        font-weight: 600;
      }
      .titleBorder {
        margin: 0.1rem auto;
        width: 0.7rem;
        height: 0.06rem;
        background: #00B377;
        border-radius: 4px;
      }
    }
  }
  .contentBox {
    padding-top: 1.17rem;
  }
}
.shopInfoBox::-webkit-scrollbar {
  display: none;
}
</style>
