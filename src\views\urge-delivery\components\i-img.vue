<template>
	<div v-if="value.length > 0" class="img-box" :class="`grid-column-${maxCol}`">
		<div class="img" :style="{paddingBottom:`${hwRatio * 100}%`}" v-for="(path, i) in value.slice(0, this.maxShowLength)" :key="i">
			<div v-if="i < maxShowLength" class="absolute">
				<el-image ref="img" class="absolute" fit="cover" :src="path.url" :alt="`凭证${i + 1}`" :preview-src-list="value.map(img => img.url)" :initial-index="i"></el-image>
				<div class="mask-box">
					<div class="mask" @click="click('show', i)">
						<i class="el-icon-search"></i>
					</div>
					<div v-if="deleteBtn" class="mask" @click="click('delete', i)">
						<i class="el-icon-delete"></i>
					</div>
				</div>
			</div>
		</div>
	</div>
	<div v-else style="flex-grow:1;">
		<el-empty v-if="showNullEl" :image-size="150" description="暂无凭证"></el-empty>
	</div>
</template>

<script>
export default {
	props: {
		maxShowLength: {
			type: Number,
			default: 3
		},
		maxCol: {
			type: Number,
			default: 3
		},
		value: {
			type: Array,
			default: () => []
		},
		showNullEl: {
			type: Boolean,
			default: true
		},
		deleteBtn: {
			type: Boolean,
			default: false
		},
		hwRatio: {
			type: Number,
			default: 0.666
		}
	},
	data() {
		return {
			img: ''
		}
	},
	methods: {
		click(key, i) {
			switch (key) {
				case 'show': {
					this.$refs.img[i].clickHandler()
					break;
				}
				case 'delete': {
					const list = this.value.filter((item, n) => n != i);
					this.$emit('input', list);
					break;
				}
			}
		}
	}
}
</script>

<style lang="scss" scoped>
.img-box {
	display: grid;
	gap: 10px;
}
.img {
	font-size: 0;
	position: relative;
	overflow: hidden;
	border-radius: 7px;
	img {
		object-fit: cover;
	}
}
.absolute {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
}
.mask {
	position: relative;
	z-index: 500;
	cursor: pointer;
	transition: all 0.3s;
	i {
		display: block;
		font-size: 28px;
		text-align: center;
		position: relative;
		top: 50%;
		transform: translateY(-50%);
		color: rgba(255, 255, 255, 0.9);
	}
}
.mask-box {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	display: flex;
	opacity: 0;
	background-color: rgba(0, 0, 0, 0.4);
	transition: all 0.3s;
	 > div {
		flex-grow: 1;
		flex-shrink: 0;
	}
}
.mask:hover {
	background-color: rgba(0, 0, 0, 0.3);
}
.img:hover .mask-box {
	opacity: 1;
}
.grid-column-1 {
	grid-template-columns: repeat(1, 1fr);
}
.grid-column-2 {
	grid-template-columns: repeat(2, 1fr);
}
.grid-column-3 {
	grid-template-columns: repeat(3, 1fr);
}
.grid-column-4 {
	grid-template-columns: repeat(4, 1fr);
}
.grid-column-5 {
	grid-template-columns: repeat(5, 1fr);
}
.grid-column-6 {
	grid-template-columns: repeat(6, 1fr);
}
.grid-column-7 {
	grid-template-columns: repeat(7, 1fr);
}
</style>