<template>
  <div>
    <div class="main-box">
      <div class="Fsearch">
        <el-row
          type="flex"
          align="middle"
          justify="space-between"
        >
          <el-row
            type="flex"
            align="middle"
          >
            <span class="sign" />
            <div class="searchMsg">
              查询条件
            </div>
          </el-row>
        </el-row>
      </div>
      <el-row :gutter="24" style="padding: 10px 30px" class="searchMy">
        <el-form ref="ruleForm" :model="ruleForm" size="small">
          <el-row :span="24">
            <el-col :span="6">
              <el-form-item prop="name">
                <el-input v-model="ruleForm.name" placeholder="请输入">
                  <template slot="prepend">图片楼层名称</template>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item prop="status">
                <span class="search-title">状态</span>
                <el-select v-model="ruleForm.status" size="small" placeholder="全部">
                  <el-option label="全部" :value="''" />
                  <el-option label="启用" :value="1" />
                  <el-option label="停用" :value="0" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item>
                <span class="search-title">展示时间</span>
                <div style="display: table-cell; line-height: 24px">
                  <el-date-picker
                    v-model="ruleForm.time"
                    type="datetimerange"
                    format="yyyy-MM-dd HH:mm:ss"
                    value-format="yyyy-MM-dd HH:mm:ss"
                    range-separator="至"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                    :default-time="['00:00:00', '23:59:59']"
                  />
                </div>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :span="24" style="text-align: right;padding-bottom: 15px">
            <el-col :span="24">
              <el-button type="primary" size="small" @click="getList('search')">查询</el-button>
              <el-button size="small"  @click="resetForm('ruleForm')">重置</el-button>
            </el-col>
          </el-row>
        </el-form>
      </el-row>
      <div class="list-box">
        <div
          class="con-title explain-search Fsearch"
          style="padding: 5px 20px 0"
        >
          <el-row
            type="flex"
            align="middle"
            justify="space-between"
          >
            <el-row
              type="flex"
              align="middle"
            >
              <span class="sign" />
              <div class="searchMsg">
                图片楼层信息
              </div>
            </el-row>
            <el-button
              v-permission="['pictureFloor_add']"
              class="xyy-blue"
              type="primary"
              size="small"
              @click="addFloor('add')"
            >
              新建图片楼层
            </el-button>
          </el-row>
        </div>
        <div style="padding: 10px 20px">
          <div class="customer-tabs">
            <el-table
              ref="goodTable"
              v-loading="laodingBoole"
              :data="tableData.list"
              stripe
              style="width: 100%"
              :header-cell-style="{ background: '#F9F9F9', color: '#666666' }"
            >
              <el-table-column
                prop="sort"
                label="排序"
                show-overflow-tooltip
              />
              <el-table-column
                prop="name"
                label="图片楼层名称"
                width="120"
                show-overflow-tooltip
              />
              <el-table-column
                label="图片"
                width="120"
              >
                <template slot-scope="scope">
                  <el-image
                    class="imgInfo"
                    :src="`${hostName}/${scope.row.imageAddress}`"
                    :preview-src-list="[`${hostName}/${scope.row.imageAddress}`]"
                    @click.prevent
                  />
                </template>
              </el-table-column>
              <el-table-column
                prop="designatedUsersName"
                label="人群"
              />
              <el-table-column label="状态">
                <template slot-scope="scope">
                  <span v-if="scope.row.status == 1">启用</span>
                  <span v-else>停用</span>
                </template>
              </el-table-column>
              <el-table-column label="展示时间" width="170">
                <template slot-scope="scope">
                  <span v-if="scope.row.startTime">{{ scope.row.startTime | handleTime }} ~ {{ scope.row.endTime | handleTime }}</span>
                </template>
              </el-table-column>
              <el-table-column label="创建时间" width="170">
                <template slot-scope="scope">
                  <span>{{ scope.row.createTime | handleTime }}</span>
                </template>
              </el-table-column>
              <el-table-column
                prop="createUser"
                label="创建人"
              />
              <el-table-column label="最新修改时间" width="170">
                <template slot-scope="scope">
                  <span>{{ scope.row.updateTime | handleTime }}</span>
                </template>
              </el-table-column>
              <el-table-column
                prop="updateUser"
                label="修改人"
              />
              <el-table-column
                fixed="right"
                label="操作"
                width="120"
                class-name="operation-box"
              >
                <template slot-scope="scope">
                  <el-button
                    v-permission="['pictureFloor_edit']"
                    type="text"
                    size="small"
                    @click="editAction('edit', scope.row)"
                  >
                    编辑
                  </el-button>
                  <el-button
                    v-if="scope.row.status == 0"
                    v-permission="['pictureFloor_enable']"
                    type="text"
                    size="small"
                    @click="editAction('open', scope.row)"
                  >
                    启用
                  </el-button>
                  <el-button
                    v-else
                    v-permission="['pictureFloor_disable']"
                    type="text"
                    size="small"
                    @click="editAction('stop', scope.row)"
                  >
                    停用
                  </el-button>
                  <el-popconfirm
                    title="确定删除吗？"
                    @confirm="editAction('del', scope.row)"
                  >
                    <el-button
                      slot="reference"
                      v-permission="['pictureFloor_delete']"
                      type="text"
                      size="small"
                      style="marginLeft: 10px"
                    >
                      删除
                    </el-button>
                  </el-popconfirm>
                </template>
              </el-table-column>
              <template slot="empty">
                <div class="noData">
                  <p class="img-box">
                    <img
                      src="@/assets/image/marketing/noneImg.png"
                      alt
                    >
                  </p>
                  <p>暂无数据</p>
                </div>
              </template>
            </el-table>
            <div class="explain-pag">
              <Pagination
                v-show="tableData.total > 0"
                :total="tableData.total"
                :page.sync="pageData.page"
                :limit.sync="pageData.rows"
                @pagination="getList"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
    <add-picture-floor
      v-if="addFloorDialog"
      :edit-type="editType"
      :edit-row="editRow"
      @cancelDialog="cancelDialog"
      @refresh="refresh"
    />
  </div>
</template>

<script>
import Pagination from '@/components/Pagination';
import {
  picAdvList,
  stopUsingAdvertise,
  enableAdvertise,
  deleteAd,
  getHostName,
} from '@/api/storeManagement/index';
import AddPictureFloor from './components/addPictureFloor.vue';

export default {
  name: 'PictureFloor',
  components: { Pagination, AddPictureFloor },
  filters: {
    handleTime(time) {
      return time ? new Date(time + 8 * 3600 * 1000).toJSON().substr(0, 19).replace('T', ' ') : '';
    },
  },
  data() {
    return {
      ruleForm: {
        status: '',
        name: '',
        time: [],
      },
      tableData: {
        total: 0,
        list: [],
      },
      pageData: {
        rows: 10,
        page: 1,
      },
      hostName: '',
      laodingBoole: false,
      addFloorDialog: false,
      editType: 'add',
      editRow: {},
    };
  },
  created() {
    getHostName().then((res) => {
      if (res.hostName) {
        this.hostName = res.hostName;
      }
    });
    // 获取列表数据
    this.getList();
  },
  methods: {
    refresh() {
      this.getList('search');
    },
    // 获取列表数据
    getList(from) {
      if (from === 'search') {
        this.pageData.page = 1;
      }
      this.laodingBoole = true;
      picAdvList({
        ...this.pageData,
        name: this.ruleForm.name,
        status: this.ruleForm.status,
        effectGtTime: (this.ruleForm.time || [])[0],
        effectLtTime: (this.ruleForm.time || [])[1],
      }).then((res) => {
        if (res.code === 0) {
          this.tableData.list = res.data.list || [];
          this.tableData.total = res.data.total || 0;
        }
        this.laodingBoole = false;
      }).catch(() => { this.laodingBoole = false; });
    },
    // 添加楼层
    addFloor(type, row) {
      this.editType = type;
      this.addFloorDialog = true;
      this.editRow = row || {};
    },
    // 关闭弹窗
    cancelDialog() {
      this.addFloorDialog = false;
    },
    editAction(type, row) {
      if (type === 'edit') {
        this.addFloor('edit', row);
      } else if (type === 'open') {
        enableAdvertise({ id: row.id }).then((res) => {
          if (res.code === 0) {
            this.getList();
          } else {
            this.$message({
              message: res.msg,
              type: 'error',
            });
          }
        }).catch(() => {});
      } else if (type === 'stop') {
        stopUsingAdvertise({ id: row.id }).then((res) => {
          if (res.code === 0) {
            this.getList();
          } else {
            this.$message({
              message: res.msg,
              type: 'error',
            });
          }
        }).catch(() => {});
      } else if (type === 'del') {
        deleteAd({ id: row.id }).then((res) => {
          if (res.code === 0) {
            this.getList();
          } else {
            this.$message({
              message: res.message,
              type: 'error',
            });
          }
        }).catch(() => {});
      }
    },
    // 重置列表数据
    resetForm(formName) {
      this.$refs[formName].resetFields();
      this.ruleForm.time = [];
      this.pageData = {
        rows: 10,
        page: 1,
      };
      this.getList();
    },
  },
};
</script>

<style lang="scss" scoped>
.Fsearch {
  padding: 10px 20px;
  font-weight: 500;
  .sign {
    display: inline-table;
    width: 3px;
    height: 13px;
    background: linear-gradient(360deg, #1d69c4 1%, #8bbdfc 99%);
    border-radius: 2px;
    margin-right: 8px;
  }
}
.searchMy ::v-deep  .el-form-item__label {
  margin-left: 20px;
  padding: 0;
}
.searchMy ::v-deep  .el-input__inner {
  border-radius: 0 4px 4px 0;
}
.searchMy ::v-deep  .el-date-editor {
  width: 100%;
}
.search-title {
  display: table-cell;
  padding: 0 10px;
  text-align: center;
  border: 1px solid #dcdfe6;
  height: 30px;
  line-height: 30px;
  vertical-align: middle;
  border-right: none;
  border-radius: 4px 0 0 4px;
  color: #333333;
  white-space: nowrap;
}
.searchMy ::v-deep  .el-select {
  display: table-cell;
  width: 100%;
}
.searchMy ::v-deep  .el-form-item__content {
  width: 100%;
}
.searchMy ::v-deep  .el-form-item--small.el-form-item {
  margin-bottom: 10px;
  width: 32%;
}
.searchMy ::v-deep  .el-form-item--small .el-form-item__content {
  line-height: 30px;
  width: 100%;
}
.searchMy ::v-deep  .el-input-group__prepend {
  background: none;
  color: #333333;
  padding: 0 10px;
}

.searchMy ::v-deep  .el-form-item--small.el-form-item {
  margin-bottom: 10px;
  width: 97%;
}
// ::v-deep  .el-table {
//   .el-checkbox__inner {
//     border: 1px solid #000000;
//   }
// }
</style>
