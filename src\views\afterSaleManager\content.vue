<!-- 售后管理——列表及各种弹框 -->
<template>
	<div>
		<el-table :data="tableData" border fit height="550px" v-loading="loading">
			<el-table-column align="center" label="序号" type="index" width="50"></el-table-column>
			<el-table-column label="订单" width="220">
				<template slot-scope="scope">
					<p class="item-left" :title="scope.row.orderId">
						<span style="width:0px"></span>
						<span>{{ scope.row.orderId }}</span>
					</p>
					<p class="item-left" :title="scope.row.orderNo">
						<span style="width:0px"></span>
						<span style="color:#4183d5; cursor:pointer;" @click="go('/orderList', { orderNo: scope.row.orderNo }, true)">{{ scope.row.orderNo }}</span>
					</p>
					<p class="item-left" :title="scope.row.createTime">
						<span style="width:0px"></span>
						<span>{{ scope.row.createTime }}</span>
					</p>
					<p class="item-left" :title="getOrderStatusStr(scope.row.orderStatus)">
						<span style="width:0px"></span>
						<span>{{ getOrderStatusStr(scope.row.orderStatus) }}</span>
					</p>
					<p v-if="scope.row.afterSalesCount">
						<el-button type="text" style="color:#4183d5;" @click="$emit('needSearch',{ key: 'orderNo', value: scope.row.orderNo })">同订单售后: {{ scope.row.afterSalesCount}}</el-button>
					</p>
				</template>
			</el-table-column>
			<el-table-column align="center" label="售后类型" prop="afterSalesTypeName" width="150"></el-table-column>
			<el-table-column align="center" label="售后信息" prop="afterSalesInfo" width="220">
				<template slot-scope="scope">
					<p class="item-left">
						<span style="width:0px"></span>
						<span>{{ scope.row.afterSalesInfo1 }}</span>
					</p>
					<p class="item-left" :title="scope.row.afterSalesInfo2">
						<span style="width:0px"></span>
						<span style="color:#8d8d8d;">{{ scope.row.afterSalesInfo2 }}</span>
					</p>
				</template>
			</el-table-column>
			<el-table-column label="申请人信息" width="300">
				<template slot-scope="scope">
					<p class="item-left" :title="scope.row.customerErpCode">
						<span style="width:0px"></span>
						<span>{{ scope.row.customerErpCode }}</span>
					</p>
					<p class="item-left" :title="scope.row.merchantName">
						<span style="width:0px"></span>
						<span style="margin-right:10px;">{{ scope.row.merchantName }}</span>
						<i  class="el-icon-service" style="color: #4183d5;cursor: pointer;transform:translateY(5px);" @click="openService(scope.row.merchantName)"/>
					</p>
					<p class="item-left" :title="scope.row.mobile">
						<span style="width:0px"></span>
						<span>{{ scope.row.mobile }}</span>
					</p>
					<p class="item-left" :title="scope.row.address">
						<span style="width:0px"></span>
						<span>{{ scope.row.address }}</span>
					</p>
					<p>
						<el-button type="text" style="color:#4183d5;" @click="showYBM(scope.row.merchantId)">药帮忙业务员&nbsp;&nbsp;&nbsp;</el-button>
					</p>
				</template>
			</el-table-column>
			<el-table-column align="center" label="处理状态" width="180">
				<template slot-scope="scope">
					<p :style="{color: `${[1, 7].includes(scope.row.auditProcessState) ? 'red' : ''}`}">{{ getAuditProcessState(scope.row.auditProcessState) }}</p>
					<p v-if="scope.row.auditProcessState != '1'">{{ scope.row.operateTime }}</p>
				</template>
			</el-table-column>
			<el-table-column align="center" label="最新处理" width="150">
				<template slot-scope="scope">
					<p class="item-left">
						<span style="width:0px;"></span>
						<span>{{ scope.row.operator }}</span>
					</p>
					<p>
						{{ scope.row.lastDealInfo }}
					</p>
				</template>
			</el-table-column>
			<el-table-column align="center" label="售后备注" width="250">
				<template slot-scope="scope">
					<p style="margin:0;">
						<el-button type="text" @click="updateRemark(scope.row)">备注</el-button>
					</p>
					<p style="margin:0;">
						{{ scope.row.sellerRemark }}
					</p>
				</template>
			</el-table-column>
			<el-table-column align="center" label="操作" width="140" fixed="right">
				<template slot-scope="scope">
					<p v-if="scope.row.auditProcessState" style="margin:0;"></p>
					<p v-if="scope.row.auditProcessState == '1' || scope.row.auditProcessState == '6' || scope.row.auditProcessState == '7' || scope.row.auditProcessState == '10'" style="margin:0;">
						<el-button type="text" @click="go('/afterSaleManager/detail', { afterSalesNo: scope.row.afterSalesNo }, true)">去处理售后</el-button>
					</p>
					<p v-if="scope.row.auditProcessState == '2' || scope.row.auditProcessState =='3' || scope.row.auditProcessState == '4' || scope.row.auditProcessState == '5'" style="margin:0;">
						<el-button type="text" @click="go('/afterSaleManager/detail', { afterSalesNo: scope.row.afterSalesNo }, true)">查看售后详情</el-button>
					</p>
					<p v-if="scope.row.auditProcessState == '7'" style="margin:0;">
						<el-button type="text" @click="lookRefund(scope.row.afterSalesNo)">查看退货物流</el-button>
					</p>
				</template>
			</el-table-column>
		</el-table>
		<el-dialog :visible.sync="remark.status" title="编辑售后备注" width="500px">
			<div v-loading="remark.loading">
				<p>售后备注仅内部可见，限100字</p>
				<el-input type="textarea" autosize v-model="remark.text" maxlength="100" show-word-limit rows="4"></el-input>
				<div style="display:flex;justify-content:end;margin-top:10px;">
					<el-button size="small" @click="remark.status = false">取消</el-button>
					<el-button size="small" type="primary" @click="remarkOk">确定</el-button>
				</div>
			</div>
		</el-dialog>
		<el-dialog :visible.sync="refund.status" title="查看退货物流" width="850px">
			<div style="padding:0 15px;">
				<p class="item-left" style="fontSize:16px !important;margin: 10px 0">
					<span style="width:100px">快递公司：</span>
					<span>{{ refund.row.logisticsCompany }}</span>
				</p>
				<p class="item-left" style="fontSize:16px !important;margin: 10px 0">
					<span style="width:100px">快递单号：</span>
					<span>{{ refund.row.trackingNo }}</span>
				</p>
				<p class="item-left" style="fontSize:16px !important;margin: 10px 0">
					<span style="width:100px">快递凭证：</span>
					<span style="flex-grow:1;">
						<img-list :maxShowLength="3" :value="refund.row.evidences" :maxCol="3"></img-list>
					</span>
				</p>
				<p style="text-align:end;margin-bottom:0;">
					<el-button @click="refund.status = false" type="primary" size="small">确定</el-button>
				</p>
			</div>
		</el-dialog>
		<el-dialog :visible.sync="buyer.status" title="药帮忙业务员" width="450px">
			<div style="padding:0 15px;">
				<p class="item-left" style="fontSize:16px !important;margin: 10px 0">
					<span style="width:100px">姓名：</span>
					<span>{{ buyer.name }}</span>
				</p>
				<p class="item-left" style="fontSize:16px !important;margin: 10px 0">
					<span style="width:100px">电话：</span>
					<span>{{ buyer.phone }}</span>
				</p>
				<p style="text-align:end;margin-bottom:0;">
					<el-button @click="buyer.status = false;buyer.name='';buyer.phone=''" type="primary" size="small">确定</el-button>
				</p>
			</div>
		</el-dialog>
	</div>
</template>

<script>
import { saveSellerRemark, getQueryLogistics } from '../../api/afterSaleManager/index'
import { queryBDInfoByMerchantId } from '../../api/order/index'
import { queryMsgToken } from '@/api/home';
import imgList from './components/imgList.vue'
export default {
	components: {
		imgList
	},
	props: {
		tableData: {
			type: Array,
			default: () => []
		},
		loading: {
			type: Boolean,
			default: false
		}
	},
	data() {
		return {
			remark: {
				status: false,
				text: '',
				row: {},
				loading: false,
				autoSize: { minRows: 4, maxRows: 4}
			},
			buyer: {
				name: '',
				phone: '',
				status: false
			},
			refund: {
				row: {
					logisticsCompany: '',
					trackingNo: '',
					evidences: []
				},
				status: false,
				loading: false
			}
		}
	},
	methods: {
		getAuditProcessState(status) {
			switch(status) {
				case 1:
					return '等待商家确认'
				case 2:
					return '用户取消'
				case 3:
					return '商家已处理'
				case 4:
					return '申请被退回'
				case 6:
					return '待客户退回发票'
				case 7:
					return '客户已退回发票'
				case 5:
					return '商家已补发'
				case 8:
					return '售后关闭'
				case 10:
					return '商家确认客户寄回'
			}
		},
		getOrderStatusStr(status) {
			switch(status) {
				case 2:
					return '配送中'
				case 3:
					return '已完成'
				case 91:
					return '已退款'
			}
			return ''
		},
		updateRemark(row) {
			this.remark.status = true;
			this.remark.row = row;
			this.remark.text = row.sellerRemark;
		},
		remarkOk() {
			if (this.remark.loading) return;
			this.remark.loading = true;
			if (this.remark.text == '') {
				this.$message.error("售后备注不能为空")
				this.remark.loading = false;
				return ;
			}
			saveSellerRemark({
				afterSalesNo: this.remark.row.afterSalesNo,
				sellerRemark: this.remark.text
			}).then(res => {
				if (res.fail) {
					this.$message.error("备注保存失败")
					return
				}
				if (res.success) {
					this.$message.success("保存成功")
					this.remark.row.sellerRemark = this.remark.text;
					console.log(this.remark.row);
					this.remark.status = false
				}
			}).finally(() => {
				this.remark.loading = false;
			})
		},
		lookRefund(afterSalesNo) {
			if (this.refund.loading) return;
			this.refund.loading = true;
			getQueryLogistics(afterSalesNo).then(res => {
				this.refund.row = res.data;
				this.refund.status = true;
				console.log(this.refund.row);
			}).finally(() => {
				this.refund.loading = false;
			})
		},
		// 由订单号生成时间
		orderNoToTime(orderNo) {
			let datePart = orderNo.substring(3, 11);
			let year = datePart.substring(0, 4);
			let month = datePart.substring(4, 6);
			let day = datePart.substring(6, 8);
			let dateTimeString = `${year}-${month}-${day} 00:00:00`;
			return dateTimeString
		},
		go(path, params, newTab) {
			if(path === '/orderList') {
				let dateTimeString = this.orderNoToTime(params.orderNo)
				params.createTime = dateTimeString
			}
			if (newTab) {
				window.openTab(path, params);
				return;
			}
			this.$router.push({
				path: path,
				query: params
			})
		},
		async openService(key) {
			// console.log(5555555555, key)
			const res = await queryMsgToken();
			if (res && res.code === 0) {
				const { token, domainName } = res.data;
				const str = `&orgId=${key}`;
				const userId = `&userId=${res.data.userId}`;
				console.log(domainName + token + str+userId);
				window.open(domainName + token + str+userId);
			} else {
				this.$message.error(res.message);
			}
		},
		/**
		 * @param { Array } list
		 */
		showBtn(val, list) {
			console.log(val, list);
			return list.includes(Number(val));
		},
		showYBM(id) {
			if (this.loading) return
			this.loading = true
			queryBDInfoByMerchantId({merchantId: id}).then(res => {
				if (res.code == 0) {
					this.buyer.name = res.result.name;
					this.buyer.phone = res.result.phone;
					this.buyer.status = true
				} else {
					this.$message.error(res.msg);
				}
			}).finally(() => {
				this.loading = false;
			})
		}
	}
}
</script>

<style lang="scss" scoped>
.item-left {
	width: 100%;
	display: flex;
	align-items: flex-start;
	margin: 0;
	> span:first-child,
	> button:first-child {
		flex-shrink: 0;
		width: 150px;
		text-align: end;
		color:#8d8d8d;
	}
	> span:nth-child(2) {
		flex-grow: 0;
		display: -webkit-box;//对象作为弹性伸缩盒子模型显示
        -webkit-box-orient: vertical;//设置或检索伸缩盒对象的子元素的排列方式
        -webkit-line-clamp: 2;//溢出省略的界限
		overflow: hidden;
		text-overflow: ellipsis;
		text-align: start !important;
	}
}
.el-dialog {
	border-radius: 7px;
	.el-dialog__body {
		padding-top: 10px;
		padding-bottom: 20px;
	}
}
</style>
