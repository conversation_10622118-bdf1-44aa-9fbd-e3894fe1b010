import request from '@/utils/request';
/**
 *
 * @param {*} params
 * @returns { Promise<{
 * 		code: number,
 * 		data: {
 * 			currentPage: number,
 * 			pageCount: number,
 * 			total: number,
 * 			rows: object[]
 * 		}
 * }> }
 */
export function getList(params) {
	const data = [{
		location: '湖北省-武汉市',
		afterSalesNo: 'FP2WAS21143',    //售后单号
		orderId: 'AC123',   //订单ID
		orderNo: 'OR8462',      //订单号
		afterSalesCount: '213',   //同订单售后单据数量
		merchantName: '老王',    //客户名称
		createTime: '2023-07-10 12:22:12',
		auditProcessState: '5',
		operateTime: '2023-07-10 12:22:12',
		orderStatus: '2',    //订单状态
		afterSalesType: '2',   //售后类型
		afterSalesTypeName: '发票售后',   //售后类型名称
		mobile: '15623566336',
		remark: '这厮哦你的卡上难得看见你撒大家可能案件看的我尽快你大家可千万你尽快的那我可就去你的口味九年大家快去那我空间的你可千万难得看见你ask就能打开爱上你的看看是你肯定就你空间相册，名字查询，吗， ',
		//售后信息
		afterSalesInfo: '三可能仓库现在拉开车门流口水的麦克拉什么的卡拉马舍得离开阿萨马力可达目录ask麦当劳卡上面到了卡吗是了代码示例的马萨里克面对卢卡斯名单来看萨满了肯定没了ask面对卢卡斯吗离开的马赛克了吗打开拉萨吗到了卡吗上来看大马拉喀阿萨面对卢卡斯AMD离开吗撒赖扩大吗卢卡斯吗的离开啊事',
		operator: '撒旦',  //操作人
		lastDealInfo: '那数据库的那数据库你的就卡死你的空间那数据库的能看见你大家看完你快到期了我看你的离去看见我你打开了你去我空间的你看九十年代卡省境内的卡拉就是',    //最新处理
		address: '人民路小学三年级三班',       //收货地址
		customerErpCode: 'ABHJ213123',   //客户erp编码
		//商业售后备注
		sellerRemark: 'nsjakdnkasjnsjkndjk的那数据库的那节课四年打卡四年大家卡是你尽快那时觉得你健康三点健康三点就喀什你打开那数据库的那数据库你的看见三大块聚散苦的建安三年抗击',
	}];
	return request({
		url: '/afterSale/list',
		method: 'post',
		data: params
	})
	return Promise.resolve({
		code: 200,
		data: {
			currentPage: 1,
			pageCount: 18,
			total: 18,
			rows: data
		},
		message: 'success'

	})
	return request({
		url: '/afterSale/list',
		method: 'get',
		params
	})
}
/**
 *
 * @param {*} params
 * @returns { Promise<{
 * 		code: number,
 * 		data: {
 * 			waitSellerHandleCount: number,
 * 			waitSellerReceiveCount: number,
 * 			waitDeliveryCount: number
 * 		}
 * }> }
 */
export function getAllSalesStatusCount(params) {
	return request({
		url: '/afterSale/queryStatusCount',
		method: 'post',
		data: params
	})
}
export function getDetail(params) {
	const data = {
		code: 200,
		data: {
			afterSaleInfo: {   //售后信息
				afterSalesNo: 'FE213129',   //售后单号
				merchantName: '奥斯曼',		//客户名称（买家）
				sellerName: '阿凯手某些', //商户名称(卖家)
				subType: '3',   //售后子类型:1-申请专票,2-无票，3-错票,4-资质漏发,5-资质错发
				//原因
				reason: '那数据库你打数据库的哪款手机你打卡四年那我i嗲ui大祭司阿娇第哦啊时间i偶的静安寺哦的脚手架都i阿散井哦i到静安寺哦的技能的空间去年我看见的那就看你的卡是对哈岁的哈斯u大海oh滴哦撒谎滴哦阿豪i的脚手架的i哦按实际都i阿散井i偶的萨基哦对教师京东i阿萨',
				amount: '16.23',  //售后金额
				remark: '什么的开幕式领导们我苦命的马赛克了代码临时卡面对卢卡斯麦当啊实打实的，打什么你的空间三的看见三大块九年撒扩大拿撒看见电脑卡刷你的卡是。十九点拿撒看见电脑撒娇看电脑劳卡',   //售后备注
				auditProcessState: '5',   //审核状态
				createTime: '2023-10-19 12:55:22',   //创建时间
				countDownTime: '123254675432', //倒计时时间戳
				tips: '氨基酸多久哦i囧事的你上课难得看见你空间的你撒旦艰苦那数据库',  //处理方案提示文字
				afterSalesDetailInfo: {
					afterSalesTypeName: '发票售后',    //售后类型描述
					specialInvoiceTitle: '客户专票信息(接受电子专票)',   //专票title：如客户专票信息(接受电子专票)
					itemList: [{
						itemTitle: '客户名称',
						itemValue: '阿三'
					}],
					specialInvoiceInfo: {     //专票信息
						enterpriseRegistrationNo: 'FD213121',   //纳税人识别号
						registerAddress: '湖北省-武汉市-江夏区-关山大道',   //地址
						phone: '*********',    //电话
						bankName: '中国人民银行关山大道支行',    //开户银行
						acct: 'PK123933521',    //银行账号
						isElectronicInvoice: '',  //是否接受电子专票
						companyName: '阿凯国际物流公司',   //公司名称
					}
				}
			},
			auditProcessList: [{    //流程
				labelTitle: '客户发起售后',
				auditProcessState: '1',
				createTime: '1999-10-29 22:43:56'
			}, {
				labelTitle: '客户再次发起售后',
				auditProcessState: '1',
				createTime: '2000-11-29 22:43:56'
			}, {
				labelTitle: '客户又发起售后',
				auditProcessState: '1',
				createTime: '2009-10-29 22:43:56'
			}, {
				labelTitle: '客户又又发起售后',
				auditProcessState: '1',
				createTime: '2013-10-09 22:43:56'
			}],
			auditRecords: [{   //协商历史
				createTime: '2023-09-12 22:12:36',
				chatter: '',   //沟通主体
				evidences: '',   //imgUrl
				remarks: '撒旦撒旦没了胃口吗打开里面完全了肯定没了我看默多克拉姆了我看动漫拉开帷幕打开里面刷卡但马上离开名单来看萨满的离开水面离开但马上离开吗的绿卡什么罗迪克马上来到昆明我去打个i活动i去领悟能力的渴望奇妙能力肯定麦克拉什么的绿卡什么了肯定没了上课打开拉萨吗离开的马萨里克'
			}],
		},
		message: 'success'
	}
	if (data.data.afterSaleInfo.afterSalesDetailInfo.specialInvoiceTitle) {
		data.data.afterSaleInfo.afterSalesDetailInfo.itemList = [{
			itemTitle: '公司名称',
			itemValue: data.data.afterSaleInfo.afterSalesDetailInfo.specialInvoiceInfo.companyName
		}, {
			itemTitle: '纳税人识别号',
			itemValue: data.data.afterSaleInfo.afterSalesDetailInfo.specialInvoiceInfo.enterpriseRegistrationNo
		}, {
			itemTitle: '地址',
			itemValue: data.data.afterSaleInfo.afterSalesDetailInfo.specialInvoiceInfo.registerAddress
		}, {
			itemTitle: '电话',
			itemValue: data.data.afterSaleInfo.afterSalesDetailInfo.specialInvoiceInfo.phone
		}, {
			itemTitle: '开户银行',
			itemValue: data.data.afterSaleInfo.afterSalesDetailInfo.specialInvoiceInfo.bankName
		}, {
			itemTitle: '银行账号',
			itemValue: data.data.afterSaleInfo.afterSalesDetailInfo.specialInvoiceInfo.acct
		}]
	}
	return request({
		url: '/afterSale/detail',
		method: 'get',
		params
	})
	return Promise.resolve(data);
	return request({
		url: '/afterSale/detail',
		method: 'get',
		params
	})
}
/**
 *
 * @param { string } afterSalesNo
 * @returns { Promise<{
 * 		code: number,
 * 		data: {
 * 			logisticsCompany: string,
 * 			trackingNo: string,
 * 			evidences: string[]
 * 		},
 * 		message: string
 * }> }
 */
export function getQueryLogistics(afterSalesNo) {
	/* const data = {
		code: 200,
		data: {
			logisticsCompany: '这是一个物流公司的名字',
			trackingNo: 'FPD21u29843213',
			evidences: JSON.stringify(['https://pic.leetcode.cn/**********-QYByku-mmexport1697616531359.png','	https://pic.leetcode.cn/**********-eIkofn-%E5%9B%BE%E7%89%87.png','https://pic.leetcode.cn/**********-QYByku-mmexport1697616531359.png'])
		},
		message: "success"
	}
	return Promise.resolve(data); */
	return request({
		url: '/afterSale/queryLogistics',
		method: 'post',
		data: {
			afterSalesNo: afterSalesNo
		}
	})
}
/**
 *
 * @param { { afterSalesNo: string, sellerRemark: string } } params
 * @returns
 */
export function saveSellerRemark(params) {
	return request({
		url: '/afterSale/saveSellerRemark',
		method: 'post',
		data: params
	})
}

/**
 *
 * @param { File } file
 * @returns { Promise<{
 * 	 	code: 0 | 1,
 * 	 	message: string,
 *   	data: any,
 * 		fail: boolean,
 * 		success: boolean
 * }> }
 */
export function uploadImage(file) {
	const formData = new FormData();
	formData.append("file", file)
	return request({
		url: '/uploadFile/common/uploadImage?uploadPath=/ybm/evidences/',
		method: 'post',
		data: formData
	})

}

export function verify(form) {
	return request({
		url: '/afterSale/saveSellerOperate',
		method: 'post',
		data: form
	})
}
export function look(orderNo) {
	return request({
		url: '/order/v2/queryOrderCredential',
		method: 'get',
		params: {
			orderNo
		}
	})
}
export function exportAfterSaleList(form) {
	return request({
		url: '/afterSale/export',
		method: 'post',
		data: form
	})
}

export function queryReturnAddress(provinceCode) {
  return request({
    url: '/address/queryReturnAddress',
    method: 'get',
    params: {
      provinceCode: provinceCode
    }
  })
}
export function queryLogistics() {
	return request({
		url: '/popDeliveryInfo/queryLogistics',
		method: 'get'
	})
}
