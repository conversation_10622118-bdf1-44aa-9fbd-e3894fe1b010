<template>
  <div class="settlement-box">
    <p>
      结算规则：<br>
      1、当订单支付成功后会生成一条结算单据，退款单退款成功后会生成一条结算单据，结算单据状态默认为“未结算”，结算单据的“佣金结算方式“取支付成功时刻或退款成功时刻商业的佣金结算方式（“月结”或“非月结”）<br>
      2、当订单变为“已完成”状态，完成时间+2个自然日后12:00进行结算（如遇休息日及国家法定节假日顺延至节后第一个工作日中午12:00结算），结算单据状态会变为“已结算”状态，并自动生成对应日期的账单。<br>
      例：订单2022年1月1日支付成功，客户在1月5日签收，则在1月6日订单变为“已完成”，若1月8日（完成时间+2个自然日）为周六，则订单会顺延至1月10日（周一）中午12:00变为“已结算”，并进入1月10日的账单中。<br>
      3、当退款单变成“退款成功”状态且满足退款时间+2个自然日后：<br>
      3.1）若关联订单未结算，则退款单随订单结算周期一起结算<br>
      3.2）若关联订单已结算，则退款单在退款时间+2个自然日12:00进行结算（如遇休息日及国家法定节假日顺延至节后第一个工作日12:00结算）
    </p>
    <div class="searchMy">
      <el-form
        ref="listQuery"
        :model="listQuery"
        :inline="true"
        size="small"
      >
        <el-form-item
          prop="businessNo"
        >
          <el-input
            v-model="listQuery.businessNo"
            placeholder="请输入单据编号或订单ID"
          >
            <template slot="prepend">
              单据号
            </template>
          </el-input>
        </el-form-item>
        <el-form-item
          prop="businessType"
        >
          <span
            class="search-title"
          >单据类型</span>
          <el-select
            v-model="listQuery.businessType"
            placeholder="请选择"
          >
            <el-option
              label="全部"
              value=""
            />
            <el-option
              label="订单"
              :value="1"
            />
            <el-option
              label="退款单"
              :value="2"
            />
            <el-option
              label="调账单"
              :value="3"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          prop="settlementType"
        >
          <span
            class="search-title"
          >佣金结算方式</span>
          <el-select v-model="listQuery.settlementType">
            <el-option
              label="全部"
              value=""
            />
            <el-option
              label="月结"
              :value="2"
            />
            <el-option
              label="非月结"
              :value="1"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          prop="orderSettlementTime"
        >
          <span
            class="search-title"
          >结算时间</span>
          <div style="display: table-cell; line-height: 24px">
            <el-date-picker
              v-model="listQuery.orderSettlementTime"
              type="datetimerange"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              :default-time="['00:00:00', '23:59:59']"
              @change="handleChangeSettlementTime"
            />
          </div>
        </el-form-item>
        <el-form-item
          prop="orderSettlementStatus"
        >
          <span
            class="search-title"
          >结算状态</span>
          <el-select
            v-model="listQuery.orderSettlementStatus"
            placeholder="请选择"
          >
            <el-option
              label="全部"
              value=""
            />
            <el-option
              label="已结算"
              :value="1"
            />
            <el-option
              label="未结算"
              :value="0"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          prop="payType"
        >
          <span
            class="search-title"
          >支付方式</span>
          <el-select
            v-model="listQuery.payType"
            placeholder="请选择"
          >
            <el-option
              label="全部"
              value=""
            />
            <el-option
              label="在线支付"
              :value="1"
            />
            <el-option
              label="线下转账（电汇平台）"
              :value="3"
            />
            <el-option
              label="线下转账（电汇商业）"
              :value="4"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          prop="merchantName"
        >
          <el-input
            v-model="listQuery.merchantName"
            placeholder="请输入"
          >
            <template slot="prepend">
              客户名称
            </template>
          </el-input>
        </el-form-item>
        <el-form-item
          prop="orderPayTime"
        >
          <span
            class="search-title"
          >支付时间</span>
          <div style="display: table-cell; line-height: 24px">
            <el-date-picker
              v-model="listQuery.orderPayTime"
              type="datetimerange"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              :default-time="['00:00:00', '23:59:59']"
              @change="handleChangePayTime"
            />
          </div>
        </el-form-item>
        <el-form-item
          prop="deducted"
        >
          <span
            class="search-title"
          >补贴冲抵佣金</span>
          <el-select
            v-model="listQuery.deducted"
            placeholder="请选择"
          >
            <el-option
              label="全部"
              value=""
            />
            <el-option
              label="是"
              :value="1"
            />
            <el-option
              label="否"
              :value="0"
            />
          </el-select>
        </el-form-item>

        <el-form-item
          prop="deducted"
        >
          <span
            class="search-title"
          >是否参与佣金折扣</span>
          <el-select
            v-model="listQuery.commissionCalcFlag"
            placeholder="请选择"
          >
            <el-option
              label="全部"
              value=""
            />
            <el-option
              label="参与"
              :value="1"
            />
            <el-option
              label="不参与"
              :value="0"
            />
          </el-select>
        </el-form-item>
        <el-form-item prop="paymentChannel">
          <span
            class="search-title"
          >结算方式</span>
          <el-select
            v-model="listQuery.paymentChannel"
            placeholder="请选择"
          >
            <el-option
              label="全部"
              value=""
            />
            <el-option
              label="直连"
              :value="1"
            />
            <el-option
              label="平安"
              :value="3"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          class="btn-item"
          style="width: 100%;text-align: right;padding-right: 20px"
        >
          <el-button
            v-permission="['settle_exportSettlement']"
            @click="handleExportSettlementListDetail"
          >
            导出结算单明细
          </el-button>
          <el-button
            v-permission="['settle_exportSettlement']"
            @click="handleExportSettlementList"
          >
            导出结算单
          </el-button>
          <el-button
            type="primary"
            @click="reset"
          >
            重置
          </el-button>
          <el-button
            type="primary"
            @click="getList(listQuery, true)"
          >
            查询
          </el-button>
        </el-form-item>
      </el-form>
    </div>
    <div style="margin-top: 10px;">
      <xyy-table
        :data="list"
        :list-query="listQuery"
        :col="col"
        @get-data="getList"
      >
        <template
          slot="operation"
          slot-scope="{col}"
        >
          <el-table-column
            :key="col.index"
            :prop="col.index"
            :label="col.name"
            :width="col.width"
            fixed="right"
          >
            <template slot-scope="{row}">
              <el-button
                  size="mini"
                  type="text"
                  @click="viewDetails(row)"
                  style="margin-right: 4px"
                >
                  查看明细
                </el-button>
              <el-button
                v-if="row.attachmentUrl"
                size="mini"
                type="text"
                @click="handleDownAttachmentUrl(row)"
              >
                下载附件
              </el-button>
            </template>
          </el-table-column>
        </template>
      </xyy-table>
    </div>
    <el-row
      :gutter="20"
      class="price-box mb15"
    >
      <el-col :span="6">
        <el-tooltip
          class="item"
          content="当前查询结果所有单据，商品实付金额求和"
          placement="left"
        >
          <i class="el-icon-warning-outline" />
        </el-tooltip>
        商品实付金额合计(元)
        <span>{{
          productActualMoneyTotal || 0
        }}</span>
      </el-col>
      <el-col :span="6">
        <el-tooltip
          class="item"
          content="当前查询结果所有单据，佣金金额求和"
          placement="left"
        >
          <i class="el-icon-warning-outline" />
        </el-tooltip>
        佣金金额合计（元）
        <span>{{
          hireMoneyTotal || 0
        }}</span>
      </el-col>
      <el-col :span="6">
        <el-tooltip
          class="item"
          content="当前查询结果所有单据，应结算金额求和"
          placement="left"
        >
          <i class="el-icon-warning-outline" />
        </el-tooltip>
        应结算金额合计(元)
        <span>{{
          statementTotalMoneyTotal || 0
        }}</span>
      </el-col>
      <el-col :span="6">
        <el-tooltip
          class="item"
          content="当前查询结果所有单据，应缴纳佣金求和"
          placement="left"
        >
          <i class="el-icon-warning-outline" />
        </el-tooltip>
        应缴纳佣金合计(元)
        <span>{{ deductedCommissionTotal || 0 }}</span>
      </el-col>

      <el-col :span="6">
        <el-tooltip
          class="item"
          content="当前查询结果所有单据，实际需缴纳佣金求和"
          placement="left"
        >
          <i class="el-icon-warning-outline" />
        </el-tooltip>
        实际需缴纳佣金合计(元)
        <span>{{
          actualCommissionMoneyTotal || 0
        }}</span>
      </el-col>
      <el-col :span="6">
        <el-tooltip
          class="item"
          content="当前查询结果所有单据，佣金优惠求和"
          placement="left"
        >
          <i class="el-icon-warning-outline" />
        </el-tooltip>
        佣金优惠合计（元）
        <span>{{
          commissionDiscountMoneyTotal || 0
        }}</span>
      </el-col>
    </el-row>
    <export-tip
      :change-export="changeExport"
      @handleExoprClose="handleExoprClose"
      @handleChangeExport="handleChangeExport"
    />
  </div>
</template>

<script>
import exportTip from '@/views/other/components/exportTip';
import { listSettlement, exportSettlementList, exportSettleDetail, querySettlementStatistic, getDetail } from '../../api/settlement/bill';
import { downloadZip } from '@/utils/download'

export default {
  name: 'Bill',
  components: { exportTip },
  data() {
    const date = new Date(Date.now());
    return {
      hireMoneyTotal: 0,
      statementTotalMoneyTotal: 0,
      actualCommissionMoneyTotal: 0,
      commissionDiscountMoneyTotal: 0,
      productActualMoneyTotal: 0,
      deductedCommissionTotal: 0,
      changeExport: false,
      list: [],
      listQuery: {
        pageSize: 10,
        page: 1,
        total: 0,
        paymentChannel: '',
        businessNo: '',
        businessType: '',
        orderSettlementStatus: '',
        settlementType: '',
        payType: '',
        merchantName: '',
        startOrderPayTime: '',
        endOrderPayTime: '',
        startOrderSettlementTime: '',
        endOrderSettlementTime: '',
        deducted: '',
        orderSettlementTime: [],
        orderPayTime: [new Date(date.getFullYear(), date.getMonth() - 1, 1), new Date(date.getFullYear(), date.getMonth(), date.getDate(), 23, 59, 59)],
        commissionCalcFlag: '',
      },
      col: [
        {
          index: 'businessNo',
          name: '单据号',
          width: 200,
        },
        {
          index: 'popOrderId',
          name: '订单ID',
          width: 200
        },
        {
          index: 'settlementType',
          name: '佣金结算方式',
          width: 150,
          formatter: (row, col, cell) => {
            if (cell === 1) {
              return '非月结';
            }
            if (cell === 2) {
              return '月结';
            }
            return '';
          },
        },
        {
          index: 'businessType',
          name: '单据类型',
          width: 150,
          formatter: (row, col, cell) => {
            if (cell === 1) {
              return '订单';
            }
            if (cell === 2) {
              return '退款单';
            }
            if (cell === 3) {
              return '调账单';
            }
            return '';
          },
        },
        {
          index: 'merchantErpCode',
          name: '客户ERP编码',
          width: 150,
        },
        {
          index: 'merchantName',
          name: '客户名称',
          width: 200,
        },
        {
          index: 'productMoney',
          name: '商品金额（元）',
          width: 150,
          formatter: (row, col, cell) => (cell || cell === 0 ? cell.toFixed(2) : ''),
        },
        {
          index: 'freightAmount',
          name: '运费金额（元）',
          width: 150,
          formatter: (row, col, cell) => (cell || cell === 0 ? cell.toFixed(2) : ''),
        },
        {
          index: 'totalMoney',
          name: '单据金额含运费（元）',
          width: 150,
          formatter: (row, col, cell) => (cell || cell === 0 ? cell.toFixed(2) : ''),
        },
        {
          index: 'shopTotalDiscount',
          name: '店铺总优惠（元）',
          width: 150,
          formatter: (row, col, cell) => (cell || cell === 0 ? cell.toFixed(2) : ''),
        },
        {
          index: 'platformTotalDiscount',
          name: '平台总优惠（元）',
          width: 150,
          formatter: (row, col, cell) => (cell || cell === 0 ? cell.toFixed(2) : ''),
        },
        {
          index: 'money',
          name: '单据实付金额（元）',
          width: 150,
          formatter: (row, col, cell) => (cell || cell === 0 ? cell.toFixed(2) : ''),
        },
        {
          index: 'productActualMoney',
          name: '商品实付金额（元）',
          width: 150,
          formatter: (row, col, cell) => (cell || cell === 0 ? cell.toFixed(2) : ''),
        },
        {
          index: 'hireMoney',
          name: '佣金金额（元）',
          width: 150,
          formatter: (row, col, cell) => (cell || cell === 0 ? cell.toFixed(2) : ''),
          renderHeader: (h, { column }) => {
            return h(
              'div', [
                h('span', column.label),
                h('el-tooltip', {
                  props: {
                    content: '单据中每个商品的佣金金额=（商品的实付金额+商品分摊的平台优惠金额）*下单时刻商品的佣金比例',
                    placement: 'right',
                  },
                }, [
                  h('i', { class: 'el-icon-warning-outline' }),
                ]),
              ],
            );
          },
        },
        {
          index: 'statementTotalMoney',
          name: '应结算金额（元）',
          width: 150,
          formatter: (row, col, cell) => (cell || cell === 0 ? cell.toFixed(2) : ''),
          renderHeader: (h, { column }) => {
            return h(
              'div', [
                h('span', column.label),
                h('el-tooltip', {
                  props: {
                    content: 'a、当单据佣金结算方式为“非月结”且支付方式为“在线支付、线下转账电汇平台“，应结算金额=单据实付金额-（佣金金额-平台总优惠）\nb、当单据佣金结算方式为“月结”且支付方式为“在线支付、线下转账电汇平台“，应结算金额=单据实付金额\nc、当单据佣金结算方式为“月结”且支付方式为“线下转账电汇商业“，应结算金额=0',
                    placement: 'right',
                  },
                }, [
                  h('i', { class: 'el-icon-warning-outline' }),
                ]),
              ],
            );
          },
        },
        {
          index: 'deductedCommission',
          name: '应缴纳佣金（元）',
          width: 200,
          renderHeader: (h, { column }) => {
            return h(
              'div', [
                h('span', column.label),
                h('el-tooltip', {
                  props: {
                    content: '结算单明细商品佣金金额求和',
                    placement: 'right',
                  },
                }, [
                  h('i', { class: 'el-icon-warning-outline' }),
                ]),
              ],
            );
          },
        },
        {
          index: 'commissionDiscount',
          name: '佣金折扣',
          width: 150,
          formatter: (row, col, cell) => (cell || cell === 0 ? cell.toFixed(2) : ''),
        },
        {
          index: 'discountReason',
          name: '折扣原因',
          width: 150,
        },
        {
          index: 'actualCommissionMoney',
          name: '实际需缴纳佣金（元）',
          width: 200,
          renderHeader: (h, { column }) => {
            return h(
              'div', [
                h('span', column.label),
                h('el-tooltip', {
                  props: {
                    content: '实际需缴纳佣金=应缴纳佣金',
                    placement: 'right',
                  },
                }, [
                  h('i', { class: 'el-icon-warning-outline' }),
                ]),
              ],
            );
          },
        },
        {
          index: 'commissionDiscountMoney',
          name: '佣金优惠（元）',
          width: 200,
          renderHeader: (h, { column }) => {
            return h(
              'div', [
                h('span', column.label),
                h('el-tooltip', {
                  props: {
                    content: '结算单明细商品佣金优惠求和',
                    placement: 'right',
                  },
                }, [
                  h('i', { class: 'el-icon-warning-outline' }),
                ]),
              ],
            );
          },
        },
        {
          index: 'orderSettlementStatus',
          name: '结算状态',
          formatter: (row, col, cell) => (cell === 1 ? '已结算' : '未结算'),
        },
        {
          index: 'orderPayTime',
          name: '支付时间',
          width: 200,
          formatter: (row, col, cell) => (cell ? new Date(cell + 8 * 3600 * 1000).toJSON().substr(0, 19).replace('T', ' ') : ''),
        },
        {
          index: 'orderFinishTime',
          name: '完成/退款时间',
          width: 200,
          formatter: (row, col, cell) => (cell ? new Date(cell + 8 * 3600 * 1000).toJSON().substr(0, 19).replace('T', ' ') : ''),
        },
        {
          index: 'orderSettlementTime',
          name: '结算时间',
          width: 200,
          formatter: (row, col, cell) => (cell ? new Date(cell + 8 * 3600 * 1000).toJSON().substr(0, 19).replace('T', ' ') : ''),
        },
        {
          index: 'payType',
          name: '支付方式',
          width: 150,
          formatter: (row, col, cell) => {
            if (cell === 1) {
              return '在线支付';
            }
            if (cell === 3) {
              return '线下转账(电汇平台)';
            }
            if (cell === 4) {
              return '线下转账(电汇商业)';
            }
            return '';
          },
        },
        {
          index: 'deducted',
          name: '补贴冲抵佣金',
          width: 150,
          formatter: (row, col, cell) => {
            if (cell === 1) {
              return '是';
            }
            if (cell === 0) {
              return '否';
            }
            return '';
          },
        },
        {
          index: 'commissionCalcFlag',
          name: '是否参与佣金折扣',
          width: 150,
          renderHeader: (h, { column }) => {
            return h(
              'div', [
                h('span', column.label),
                h('el-tooltip', {
                  props: {
                    content: '不参与佣金折扣的结算单，将不计入佣金门槛的金额计算且不享受佣金折扣',
                    placement: 'right',
                  },
                }, [
                  h('i', { class: 'el-icon-warning-outline' }),
                ]),
              ],
            );
          },
          formatter: (row, col, cell) => {
            if (cell === 1) {
              return '参与';
            }
            return '不参与';
          },
        },
        {
          index: 'paymentChannel',
          name: '结算方式',
          width: 150,
          formatter: (row, col, cell) => {
            if (cell === 3) {
              return '平安'
            } else if (cell === 1) {
              return '直连'
            }
          }
        },
        {
          index: 'remark',
          name: '备注',
          width: 150,
        },
        {
          index: 'operation',
          name: '操作',
          width: 170,
          slot: true,
        },
      ],
    };
  },
  created() {
    this.getList(this.listQuery, true);
    this.querySettlementStatistic();
  },
  methods: {
    viewDetails(row) {
      let subData = {
        businessNo: row.businessNo,
        pageNum: 1,
        pageSize: 10
      }
      // this.$router.push({ name: 'settleDetails', params: { tableData: [{a: 2333}], row, total: 10 } })
      getDetail(subData).then((res) => {
        if(res.code === 0) {
          if(res.data.list?.length == 0){
            this.$message.error('当前结算单暂无明细')
            return
          }
          this.$router.push({ name: 'settleDetails', params: { tableData: res.data.list, total: res.data.total, row } })
        }
      })
    }, // 查看明细
    /**
     * 格式化日期
     */
    formatDate(date) {
      return Number(date)
        ? new Date(date + 8 * 3600 * 1000).toJSON().substr(0, 19).replace('T', ' ')
        : '';
    },
    handleChangePayTime(val) {
      if (val) {
        this.listQuery.startOrderPayTime = this.formatDate(val[0].getTime());
        this.listQuery.endOrderPayTime = this.formatDate(val[1].getTime());
      } else {
        this.listQuery.startOrderPayTime = '';
        this.listQuery.endOrderPayTime = '';
      }
    },
    handleChangeSettlementTime(val) {
      if (val) {
        this.listQuery.startOrderSettlementTime = this.formatDate(val[0].getTime());
        this.listQuery.endOrderSettlementTime = this.formatDate(val[1].getTime());
      } else {
        this.listQuery.startOrderSettlementTime = '';
        this.listQuery.endOrderSettlementTime = '';
      }
    },
    querySettlementStatistic() {
      const params = JSON.parse(JSON.stringify(this.listQuery));
      params.orderPayTime = null;
      params.orderSettlementTime = null;
      querySettlementStatistic(params).then((res) => {
        if (res.success) {
          const { data } = res;
          Object.keys(data).forEach((item) => {
            this[item] = data[item];
          });
        }
      });
    },
    handleDownAttachmentUrl(row) {
      // const downLoadUrl = `/uploadFile/downloadFromFastDfs?path=${row.attachmentUrl}&fileName=${row.businessNo}附件.${row.attachmentUrl.split('.').pop()}`;
      // window.open(row.attachmentUrl);
      var hrefArr = [{
        url: row.attachmentUrl,
        name: `${row.businessNo}附件`
      }];
      hrefArr.forEach(item => {
        if (item.url.indexOf("http") == -1) {
          item.url = row.fdfsDownLoadDomain + row.attachmentUrl;
        }
      })
      
      downloadZip(hrefArr, `${row.businessNo}附件`)
      // const a = document.createElement('a')
      // const filename = `${row.businessNo}附件.${row.attachmentUrl.split('.').pop()}`
      // a.download = filename
      // document.body.appendChild(a)
      // a.target = '_blank';
      // a.href = hrefUrl;
      // a.click()
      // document.body.removeChild(a)
    },
    handleChangeExport(info) {
      this.changeExport = false;
      if (info === 'go') {
        const path = '/downloadList';
        window.openTab(path);
        // that.$router.push({ path: '/downloadList' });
      }
    },
    handleExoprClose() {
      this.changeExport = false;
    },
    reset() {
      this.$refs.listQuery.resetFields();
      this.listQuery.startOrderPayTime = '';
      this.listQuery.endOrderPayTime = '';
      this.listQuery.startOrderSettlementTime = '';
      this.listQuery.endOrderSettlementTime = '';
      this.listQuery.commissionCalcFlag = '';
      this.getList(this.listQuery, true);
    },
    getList(listQuery, reset) {
      const loading = this.$loading({
        lock: true,
        text: 'Loading',
        spinner: 'el-icon-loading',
        background: 'rgba(255,255,255, 0.8)',
      });
      const { page, pageSize } = listQuery;
      this.listQuery.pageSize = pageSize;
      this.listQuery.pageNum = reset ? 1 : page;
      this.listQuery.page = reset ? 1 : page;
      const params = JSON.parse(JSON.stringify(this.listQuery));
      params.orderPayTime = null;
      params.orderSettlementTime = null;
      this.querySettlementStatistic();
      listSettlement(params).then((res) => {
        loading.close();
        if (res.success) {
          const { data } = res;
          this.list = data.list || [];
          this.listQuery = {
            ...this.listQuery,
            total: data.total,
            page: data.pageNum,
          };
        } else {
          this.$message.error(res.msg || '请求失败');
        }
      });
    },
    handleExportSettlementList() {
      exportSettlementList(this.listQuery).then((res) => {
        if (res.code !== 0) {
          this.$message.error(res.message);
          return false;
        }
        this.changeExport = true;
      });
    },
    handleExportSettlementListDetail() {
      exportSettleDetail(this.listQuery).then((res) => {
        if (res.code !== 0) {
          this.$message.error(res.message);
          return false;
        }
        this.changeExport = true;
      });
    },
  },
};
</script>

<style>
  .el-tooltip__popper {
    white-space: pre-line;
  }
</style>
<style lang="scss" scoped>
  .settlement-box {
    padding: 15px;
    p {
      padding: 8px;
      background: #f9f9f9;
      border-radius: 2px;
      font-size: 14px;
      font-family: PingFangSC, PingFangSC-Regular;
      font-weight: 400;
      color: #666666;
      margin: 15px 0 25px;
    }
    .searchMy ::v-deep  .el-form-item__label {
      margin-left: 20px;
      padding: 0;
    }
    .searchMy ::v-deep  .el-input__inner {
      border-radius: 0 4px 4px 0;
    }
    .searchMy ::v-deep  .el-date-editor{
      width: 100%;
    }
    .search-title {
      display: table-cell;
      padding: 0 10px;
      text-align: center;
      border: 1px solid #dcdfe6;
      height: 30px;
      line-height: 30px;
      vertical-align: middle;
      border-right: none;
      border-radius: 4px 0 0 4px;
      color: #333333;
      white-space: nowrap;
    }
    .searchMy ::v-deep  .el-select {
      display: table-cell;
      width: 100%;
    }
    .searchMy ::v-deep  .el-form-item__content{
      width: 100%;
    }
    .searchMy ::v-deep  .el-form-item--small.el-form-item{
      margin-bottom: 10px;
      width: 32%;
    }
    .searchMy ::v-deep  .el-form-item--small .el-form-item__content{
      line-height: 30px;
      width: 100%;
    }
    .searchMy ::v-deep  .el-input-group__prepend {
      background: none;
      color: #333333;
      padding: 0 10px;
    }
    .price-box {
      font-size: 14px;
      font-family: PingFangSC, PingFangSC-Semibold;
      font-weight: 600;
      color: #303133;
      line-height: 30px;
      overflow: hidden;
      padding-top: 20px;
      &.mb15 {
        margin-bottom: 15px;
      }
      span {
        font-size: 14px;
      }
      .el-button {
        padding: 0 12px;
        line-height: 20px;
        &.is-plain {
          color: #4183d5;
          border-color: #4183d5;
        }
      }
    }
  }
</style>
