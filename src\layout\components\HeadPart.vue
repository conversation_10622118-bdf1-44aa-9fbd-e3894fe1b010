<template>
  <div :class="{'head':true,'head-gray':isGrayUser}">
    <div class="head-content">
      <div class="logo">
        <img src="../../assets/image/home/<USER>" alt="" v-if="isGrayUser">
        <img src="../../assets/image/home/<USER>" alt="" v-else>
        <span :class="{'platformName':true,'gray-color':isGrayUser}">供应商管理平台</span>
        <div v-if="Number(shopConfig.shopCreateStatus)" class="shopInfo">
          <div class="shopName">
            <span :class="{'gray-color':isGrayUser}">{{ shopConfig.shopName }}</span>
            <div ref="h5data" class="QR_code" v-permission="['h5data']">
              <el-popover
                placement="bottom"
                trigger="click"
              >
                <div id="qrcode" ref="qrcode"></div>
                <img slot="reference" src="../../assets/QR_code.png" alt="">
              </el-popover>
            </div>
          </div>
          <div class="status">
            <el-tooltip placement="bottom">
              <div slot="content">上线：店铺“上线”状态，平台客户可搜索和查看店铺，并可正常与商业进行采购交易<br />
                下线：店铺“下线”状态，平台客户将无法搜索和查看该店铺以及店铺内的所有商品<br />
                如有疑问，请您与平台运营进行沟通
              </div>
              <i :class="{'el-icon-warning-outline':true,'gray-color':isGrayUser}"></i>
            </el-tooltip>
            <span :class="{'shopStatus':true,'gray-color':isGrayUser}">店铺状态：
              <span v-if="isGrayUser" >
                <img src="../../assets/image/home/<USER>" alt="" v-if="shopConfig.shopStatus==2">
                <img src="../../assets/image/home/<USER>" alt="" v-if="shopConfig.shopStatus==3">
                <img src="../../assets/image/home/<USER>" alt="" v-if="shopConfig.shopStatus==4">
              </span>
              <span v-else :class="fontClass">
                {{ shopConfig.shopStatusDesc }}
              </span>
          </span>
            <el-tooltip placement="bottom" v-if="shopConfig.shopPatternCode !== 'ybm'">
              <div slot="content">未对接ERP：ERP还未与平台对接。如需对接请您与平台运营进行沟通<br />
                连接正常：ERP对接工具与平台连接正常<br />
                连接异常，请检查对接工具：ERP对接工具与平台连接异常，请检查对接工具
              </div>
              <i :class="{'el-icon-warning-outline':true,'gray-color':isGrayUser}"></i>
            </el-tooltip>
            <span v-if="shopConfig.shopPatternCode !== 'ybm'" :class="{'gray-color':isGrayUser}">ERP对接状态：
              <span v-if="isGrayUser" class="erpImg">
                <!-- <span  class="connect-abnormal">连接异常</span> -->
                <img  src="../../assets/image/home/<USER>" alt="" v-if="shopConfig.erpConnStatus==0">
                <img  src="../../assets/image/home/<USER>" alt="" v-if="shopConfig.erpConnStatus==1">
                <img src="../../assets/image/home/<USER>" alt="" v-if="shopConfig.erpConnStatus==2">
              </span>
              <span :class="erpClass" v-else>
              {{ shopConfig.erpConnStatusDesc }}</span>
            </span>
          </div>
        </div>
      </div>
      <div class="user">
        <div class="qr-code">
          <p :class="{'gray-color':isGrayUser}">下载手机版</p>
          <div class="qr-code-div">
            <div class="qr-code-img">
              <img src="../../assets/businessQrCode.png" alt="">
              <span>扫一扫下载商业APP<br>移动办公更便捷</span>
            </div>
          </div>
        </div>
        <span v-if="isGrayUser" class="user-fileDown" @click="$router.push('/downloadList')">文件下载中心 <i class="iconfont el-icon-download"></i></span>
        <span :class="{'welcome':true,'gray-color':isGrayUser}">欢迎！ {{ shopConfig.mobile }}</span>
        <span :class="{'signOut':true,'gray-color':isGrayUser}" @click="signOut">[退出]</span>
      </div>
    </div>
  </div>
</template>

<script>
import { getShopInfo, getQualificationExpiredNum } from '@/api/home';
import { mapMutations } from 'vuex';
import QRCode from 'qrcodejs2';
import { mapState } from 'vuex';

export default {
  name: 'HeadPart',
  data() {
    return {
      shopConfig: {
        shopName: '', // 店铺名称
        mobile: '', // 账号
        shopCreateStatus: '', // 店铺创建状态
        shopCreateDesc: '', // 店铺创建状态描述
        shopStatus: '', // 店铺状态
        shopStatusDesc: '', // 店铺状态描述
        erpConnStatus: '', // erp连接状态
        erpConnStatusDesc: '', // erp连接状态描述
      },
      erpStatusInterval: null,
      QR_code: '',
    };
  },
  computed: {
    ...mapState('app', ['shopConfig']),
    ...mapState({ isGrayUser: state => state.app.isGrayUser }),
    fontClass() {
      if (Number(this.shopConfig.shopStatus) === 2) {
        return 'greenColor';
      } else {
        return 'redColor';
      }
    },
    erpClass() {
      if (Number(this.shopConfig.erpConnStatus) === 1) {
        return 'greenColor';
      } else {
        return 'redColor';
      }
    }
  },
  created() {
    console.log(process.env.VUE_APP_BASE_API);
    this.getQualificationExpiredNum();
    this.getShopInfo();
    if (this.erpStatusInterval) {
      clearInterval(this.erpStatusInterval);
    } else {
      this.erpStatusInterval = setInterval(() => {
        this.getShopInfo();
      }, 300000);
    }
  },
  methods: {
    ...mapMutations('app', ['SET_SHOPCONFIG', 'SET_QUALTIFICATIONINFOS']),
    async getQualificationExpiredNum() {
      const res = await getQualificationExpiredNum();
      if (res && res.success) {
        this.SET_QUALTIFICATIONINFOS(res.data);
      }
    },
    async getShopInfo() {
      const res = await getShopInfo();
      if (res && res.code === 0) {
        this.SET_SHOPCONFIG(res.result);
        Object.keys(res.result)
          .map(key => {
            this.$set(this.shopConfig, key, res.result[key]);
          });
        this.createER_code();
      } else {
        this.$message.error(res.msg || '获取店铺基本信息状态失败');
      }
    },
    signOut() {
      clearInterval(this.erpStatusInterval);
      location.href = process.env.VUE_APP_BASE_API;
    },
    createER_code() {
      this.$nextTick(() => {
        const h = (document.documentElement.querySelector('#qrcode') || {}).innerHTML
        if (this.$refs.h5data && !h) {
          const domain = process.env.VUE_APP_BASE_API
          const url = domain + '/manage' + '/#/dataAnalysis'
          new QRCode('qrcode', {
            text: url,
            width: 300,
            height: 300,
            colorDark: '#000',
            colorLight: '#fff'
          });
        }
      });
    }
  }
};
</script>

<style scoped lang="scss">

.head {
  width: 100%;
  line-height: 64px;
  position: fixed;
  background-color:#4184d5;
  top: 0;
  left: 0;

  z-index: 1002;

  .head-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    color: #fff;

    .logo {
      padding-left: 17px;
      display: flex;
      justify-content: space-between;
      align-items: center;

      img {
        height: 33px;
      }

      .platformName {
        font-size: 16px;
      }

      .platformName:before {
        content: '';
        display: inline-block;
        width: 1px;
        height: 16px;
        background: #e1dfdf;
        vertical-align: middle;
        margin-left: 10px;
        margin-right: 15px;
      }


      .shopInfo {
        line-height: 22px;
        font-size: 16px;
        margin-left: 64px;

        .shopName {
          display: flex;
          justify-content: left;
          align-items: center;

        }

        .QR_code {
          font-size: 18px;
          width: 22px;
          height: 22px;
          background-color: #fff;
          border-radius: 50%;
          margin-left: 5px;
          display: flex;
          justify-content: center;
          align-items: center;

          img {
            width: 15px;
            height: 15px;
            line-height: 22px;
          }
        }

        .status {
          margin-top: 4px;
          line-height: 20px;
          font-size: 12px;

          span {
            white-space: nowrap;
          }

          i {
            margin-right: 4px;
          }

          .shopStatus {
            margin-right: 35px;
          }
        }
      }
    }

    .user {
      font-size: 12px;
      padding-right: 30px;
      display: flex;
      align-items: center;
      .qr-code {
        margin-right: 30px;
        position: relative;
        > p {
          height: 24px;
          margin: 0;
          padding: 0 10px;
          box-sizing: border-box;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;
          &:hover {
            background: rgba(0,41,92, .2);
            border-radius: 12px;
            + .qr-code-div {
              display: block;
            }
          }
        }
        .qr-code-div {
          display: none;
          cursor: pointer;
          &:hover {
            display: block;
          }
          .qr-code-img {
            // background: #FFFFFF;
            background-image: url('../../assets/qrCode-div.png');
            background-size: 100% 100%;
            background-repeat: no-repeat;
            // box-shadow: 0 3px 9px 0 #37373742;
            position: absolute;
            bottom: -230px;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            padding: 25px;
            box-sizing: border-box;
            img {
              width: 130px;
              height: 130px;
            }
            span {
              margin-top: 12px;
              font-family: PingFangSC-Medium;
              font-weight: 500;
              font-size: 14px;
              color: #000000;
              text-align: center;
              line-height: 20px;
            }
          }
        }
      }
      .signOut {
        cursor: pointer;
        margin-left: 4px;
      }
    }
  }
}
.head-gray{
  background-color: #fff;
  color:#111111 !important;
}
.user-fileDown{
  cursor:pointer;
  margin-right:10px;
  color:#111111
}
.user-fileDown:after{
        content: '';
        display: inline-block;
        width: 2px;
        height: 16px;
        background: #e1dfdf;
        vertical-align: middle;
        margin-left: 10px;
        margin-right: 15px;

}
.statusBCgreen{
  background-color: rgba(3,194,97,0.1);
  border-radius: 3px;
  padding:1px 3px;
  color:#03C261;

}
.statusBCred{
    color:#FF4444;
    background-color:rgb(255,68,68,0.1) ;
    border-radius:3px;
    padding:1px 3px;
}
.gray-color{
          color:#333333;
        }
        .shopStatus img{
          height:16px !important;
          width:44px !important;
          vertical-align: middle;
        }
        .erpImg img{
          height:16px !important;

          vertical-align: middle;
        }
        .connect-abnormal{
          background-color:rgb(254, 235, 235);
          border-radius:3px;
          padding:0 5px;
          font-size: 12px;
          color:rgb(255, 69, 69)
        }
</style>
