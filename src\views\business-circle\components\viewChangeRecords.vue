<template>
  <div>
    <el-dialog
      :visible="dialogVisibl"
      :before-close="handleDialogClose"
      :width="dialogWidth"
      title="商圈变更记录"
      height="300"
      @open="open"
    >
      <el-table
        v-loading="isLoading"
        :data="tableData"
        stripe
        border
        height="400"
        highlight-current-row
        :header-cell-style="{ background: '#F9F9F9', color: '#666666' }"
      >
        <el-table-column label="变更内容">
          <template slot-scope="{row}">
            <p
              v-for="(item, index) in row.changeInfo"
              :key="index"
            >
              {{ item }}
            </p>
          </template>
        </el-table-column>
        <!-- <el-table-column
          prop="productName"
          label="变更类型"
        /> -->
        <el-table-column
          prop="createTime"
          label="变更时间"
          :formatter="formatDate"
        />
        <el-table-column
          prop="createName"
          label="操作人"
        />
      </el-table>

      <div
        v-if="tablePage.total != 0"
        class="pagination-container"
        style="display: flex;align-items: center;justify-content: space-between;"
      >
        <div class="pag-text">
          共 {{ tablePage.total }} 条数据，每页{{ tablePage.pageSize }}条，共{{
            Math.ceil(tablePage.total / tablePage.pageSize)
          }}页
        </div>
        <el-pagination
          background
          :page-sizes="pageSizes"
          :page-size="tablePage.pageSize"
          :current-page="tablePage.pageNum"
          layout="sizes, prev, pager, next, jumper"
          :total="tablePage.total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
      <el-row :gutter="20">
        <el-col
          :span="2"
          :offset="22"
          style="float: right"
        >
          <el-button
            type="primary"
            style="margin-top: 10px"
            size="small"
            @click="sureBtn"
          >
            确 定
          </el-button>
        </el-col>
      </el-row>
    </el-dialog>
  </div>
</template>

<script>
import { getAreaLog } from '@/api/businessCircle';

export default {
  name: 'ViewChangeRecords',
  model: {
    prop: 'viewProductDialog',
    event: 'onDialogViewProduct',
  },
  props: {
    viewProductDialog: Boolean,
    row: Object,
  },
  data() {
    return {
      show: false,
      dialogWidth: '60%',
      dialogVisibl: false,
      tableData: [], // 弹窗表格
      rowIndex: '',
      pageSizes: [10, 20, 30, 40],
      tablePage: {
        pageNum: 1,
        pageSize: 10,
        total: 1,
      },
      busAreaId: '',
      isLoading: false, // 加载
      ruleForm: {
        tempName: '',
        templateType: '',
        tempStatus: '',
        branchCode: null,
        startTime: '',
      },
    };
  },
  mounted() {
    this.$nextTick(() => {
      this.dialogVisibl = true;
    });
  },
  methods: {
    formatDate(row, column, cellValue) {
      const date = new Date(cellValue);
      const y = date.getFullYear();
      let MM = date.getMonth() + 1;
      MM = MM < 10 ? `0${MM}` : MM;
      let d = date.getDate();
      d = d < 10 ? `0${d}` : d;
      let h = date.getHours();
      h = h < 10 ? `0${h}` : h;
      let m = date.getMinutes();
      m = m < 10 ? `0${m}` : m;
      let s = date.getSeconds();
      s = s < 10 ? `0${s}` : s;
      return `${y}-${MM}-${d} ${h}:${m}:${s}`;
    },
    open(busAreaId) {
      this.dialogVisibl = true;
      this.busAreaId = busAreaId; // 业务商圈ID
      this.searchList(); // 初始化列表
    },
    handleDialogClose() {
      this.dialogVisibl = false;
      this.$emit('onDialogViewProduct', false);
    },
    // 查询
    searchList() {
      const params = { busAreaId: this.row.id };
      Object.assign(params, { pageNum: this.tablePage.pageNum, pageSize: this.tablePage.pageSize });
      this.isLoading = true;
      getAreaLog(params).then((res) => {
        const { code, data } = res;
        if (code === 0) {
          this.isLoading = false;
          this.tableData = data.list;
          this.tablePage.total = data.total; // 总数据数量
          this.tablePage.pageNum = data.pageNum;
        }
      });
    },
    handleSizeChange(val) {
      this.tablePage.pageSize = val;
      this.searchList();
    },
    handleCurrentChange(val) {
      this.tablePage.pageNum = val;
      this.searchList();
    },
    sureBtn() {
      this.handleDialogClose();
    },
  },
};
</script>

<style scoped>
::v-deep  .el-dialog__header {
  background-color: #f8f8ff;
}
</style>
