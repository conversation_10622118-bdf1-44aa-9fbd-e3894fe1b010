const state = {
  shopConfig: {
    shopName: '', // 店铺名称
    mobile: '', // 账号
    shopCreateStatus: '', // 店铺创建状态
    shopCreateDesc: '', // 店铺创建状态描述
    shopStatus: '', // 店铺状态
    shopStatusDesc: '', // 店铺状态描述
    erpConnStatus: '', // erp连接状态
    erpConnStatusDesc: '', // erp连接状态描述
    indexShow: false, // 展示首页
    priceType: '', // 售价模式：1-售价模式，2-底价模式
    isFbp: 0, // 是否fbp商家,0 否 1是
    shopPatternCode: '', // ybm 为自营
    showTag: false, // 营销管理-活动管理控制-新建领券活动 是否显示 '领券中心是否展示' 单选项  true或false
  },
  sidebar: { opened: true },
  qualtificationInfos: {
    isShow: 0,
    names: [],
    size: 0,
  },
  isGrayUser: false, // 是否是灰度用户
  phone:'', // 手机号
  standardProductChangeReminder: true,//标品信息变更提醒
};

const mutations = {
  SET_QUALTIFICATIONINFOS: (state, qualtificationInfos) => {
    state.qualtificationInfos = { ...qualtificationInfos };
  },
  SET_SHOPCONFIG: (state, shopConfig) => {
    state.shopConfig = { ...shopConfig };
  },
  TOGGLE_SIDEBAR: state => {
    state.sidebar.opened = !state.sidebar.opened;
  },
  CLOSE_SIDEBAR: (state) => {
    state.sidebar.opened = false;
  },
  SET_ISGRAYUSER: (state, isGrayUser) => {
    state.isGrayUser = isGrayUser;
  },
  SET_PHONE: (state, phone) => {
    state.phone = phone;
  },
  SET_STANDARDPRODUCTCHANGEREMINDER: (state, standardProductChangeReminder) => {
    state.standardProductChangeReminder = standardProductChangeReminder;
  },
}

const actions = {
  toggleSideBar({commit}) {
    commit('TOGGLE_SIDEBAR')
  },
  closeSideBar({commit}, {withoutAnimation}) {
    commit('CLOSE_SIDEBAR', withoutAnimation)
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}
