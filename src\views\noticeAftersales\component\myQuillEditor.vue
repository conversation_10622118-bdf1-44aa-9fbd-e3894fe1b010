<template>
  <div class="editDiv">
    <quill-editor
      ref="myTextEditor"
      v-model="data.content"
      :options="editorOption"
      style="height: 200px; width: 100%"
      @change="alertValue($event)"
    />
    <div class="textNum">
      {{ TiLength }}/1000
    </div>
  </div>
</template>

<script>
import { quillEditor } from 'vue-quill-editor'; // 调用编辑器
import 'quill/dist/quill.core.css';
import 'quill/dist/quill.snow.css';
import 'quill/dist/quill.bubble.css';

export default {
  name: 'MyQuillEditor',
  components: { quillEditor },
  props: {
    disabled: {
      type: Boolean,
      default: true,
    },
    formData: {
      type: Object,
      default: () => {},
    },
    fromEditor: {
      type: Number,
      default: null,
    },
  },
  data() {
    return {
      data: { content: '' },
      editorOption: {
        placeholder: '例如：电子版开户，无需提供纸质资质，发票随货同行',
        modules: {
          toolbar: [
            [], // toggled buttons
          ],
        },
      },
      TiLength: 0,
    };
  },
  watch: {
    disabled(val) {
      if (!val) {
        this.$refs.myTextEditor.quill.enable(true);
      } else {
        this.$refs.myTextEditor.quill.enable(false);
      }
    },
    formData(val) {
      this.data.content = val.content;
    },
  },
  mounted() {
    this.TiLength = this.$refs.myTextEditor.quill.getLength() - 1;
    this.$refs.myTextEditor.quill.enable(false);
  },
  methods: {
    alertValue(e) {
      e.quill.deleteText(1000, 1);
      if (this.data.content === '') {
        this.TiLength = 0;
      } else {
        this.TiLength = 1000 - (e.quill.getLength() - 1);
      }
      // eslint-disable-next-line vue/no-mutating-props
      this.formData.content = this.data.content;
    },
  },
};
</script>

<style>
div.ql-toolbar.ql-snow {
  padding: 0;
  height: 0;
  border-bottom: none;
}
</style>
<style scoped lang="scss">
.editDiv {
  // padding: 20px 10px;
  // margin-left: 100px;
  display: inline-block;
  width: 500px;
  position: relative;
  vertical-align: bottom;
  .tipBox {
    width: 100%;
    height: 230px;
    opacity: 0;
    position: absolute;
    top: 0;
    left: 0;
    z-index: 100;
  }
}
.textNum {
  text-align: right;
  position: absolute;
  bottom: 10px;
  right: 22px;
  color: #999999;
}

.btnDiv {
  padding-top: 5px;
  width: 100%;
  text-align: right;
}
</style>
