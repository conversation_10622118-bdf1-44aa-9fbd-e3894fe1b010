<script>
import commonHeader from '../afterSaleManager/components/common-header.vue';
import provinceList from '../afterSaleManager/components/provinceList.vue';
import pintuanPrice from '../pintuanDataUpdate/pintuanPrice.vue';
import batchEdit from '../marketing/components/batchEdit.vue'
import { productBiddingInfo, loadBiddingStatusCounts, exportBiddingSku } from '../../api/product'
import { apiGetFrameActBaseForAuction } from '../../api/market/collageActivity'
import Detail from './components/detail.vue';
import exportTip from '@/views/other/components/exportTip';
export default {
  components: {
    commonHeader,
    provinceList,
    Detail,
    exportTip,
    batchEdit,
    pintuanPrice

  },
  data() {
    return {
      bulkChangesDialog: false,
      updateData: {
        actId: '',
        type: 1,
        visible: false
      },
      form: {
        barcode: "",
        erpCode: "",
        productName: "",
        manufacturer: "",
        approvalNumber: "",
        activityType: "",
        skuBiddingType: 'one',
      },
      pagination: {
        pageNum: 1,
        pageSize: 20,
        total: 0
      },
      productTypeList: [
        {
          label: '全部',
          value: ''
        },
        {
          label: '拼团商品',
          value: 1
        },
        {
          label: '批购包邮',
          value: 3
        }
      ],
      auctionStatusList: [
        {
          label: '全部',
          value: ''
        },
        {
          label: '生效',
          value: 1
        },
        {
          label: '失效',
          value: 2
        },
        {
          label: '已删除',
          value: 20
        },
      ],
      tableData: [],
      loading: false,
      personalLimitTypeList: ["不限制", "活动期间限购", "每天（每天00:00至24:00）", "单笔订单限购", "每周（周一00:00至周日24:00）", "每月（每月1号00:00至每月最后一天24:00）"],
      detail: {
        list: [],
        actId: ''
      },
      actList: [],
      totalData: {
        one: '',
        two: '',
        three: ''
      },
      changeExport: false
    }
  },
  mounted() {
    this.search();
  },
  activated() {
    apiGetFrameActBaseForAuction().then(res => {
      if (res.code == 1000) {
        this.actList = res.data.activityReportBaseResDTOS;
      }

    })
  },
  methods: {
    search() {
      if (this.loading) return;
      this.loading = true;
      const form = {...this.form};
      form.skuBiddingType = {
        one: 1,
        two: 2,
        three: 3
      }[this.form.skuBiddingType];
      productBiddingInfo({
        ...form,
        page: this.pagination.pageNum,
        rows: this.pagination.pageSize
      }).then(res => {
        this.tableData=res.result.list;
        this.pagination.total = res.result.total;
      }).finally(() => {
        loadBiddingStatusCounts({
          ...form,
          page: this.pagination.pageNum,
          rows: this.pagination.pageSize
        }).then(res => {
          if (res instanceof Array) {
            res.forEach(item => {
              if (item.statusType == 1) {
                this.totalData.one = item.count
              }
              if (item.statusType == 2) {
                this.totalData.two = item.count
              }
              if (item.statusType == 3) {
                this.totalData.three = item.count
              }
            })
          } else {
            this.$message.error('统计失败');
          }
        }).finally(() => {
          this.loading = false;
        })
      });
    },
    exportExcel(){
      const form = { ...this.form };
      form.skuBiddingType = {
        one: 1,
        two: 2,
        three: 3
      }[this.form.skuBiddingType];
      let params = JSON.parse(JSON.stringify(form));
      Object.keys(params).forEach(key => {
        if (params[key] === '') {
          delete params[key];
        }
      });
      exportBiddingSku(params).then(res=>{
        console.log(res)
        if (res.code == 0) {
            this.changeExport = true;
          } else {
            this.$message.error({
              message: res.message,
              offset: 100,
            });
          }
      })
    },
    reset() {
      this.form = {
        barcode: "",
        erpCode: "",
        productName: "",
        manufacturer: "",
        approvalNumber: "",
        activityType: "",
        biddingStatus: '',
        skuBiddingType: 'one',
      };
      this.pagination = {
        pageNum: 1,
        pageSize: 20,
        total: 0
      };
      this.search();
    },
    lookMore(row){
      // 发布商品
      if(row.activityType === 0){
        window.openTab('/orderList', { skuId:row.csuid })
      }else{
        var path = '/groupSalesData'
        console.log(row)
        var obj = {
          activityType: row.promotionActivityType,
          marketingIdStr: row.activityId,
          csuId: row.csuid
        }
        window.openTab(path, obj)
      }
    },
    transferTime(time) {
      return window.dayjs(time).format('YYYY-MM-DD HH:mm:ss')
    },
    handleExoprClose() {
      this.changeExport = false;
    },
    handleChangeExport(info) {
      this.changeExport = false;
      if (info === 'go') {
        const path = '/downloadList';
        window.openTab(path);
      }
    },
    toCreatAct(item) {
      const path = '/groupActivityTheme'
      const obj = {
        frameReportId: item.frameReportId
      }
      window.openTab(path, obj)
    }
  }
}
</script>

<template>
  <div>
    <export-tip
      :change-export="changeExport"
      @handleExoprClose="handleExoprClose"
      @handleChangeExport="handleChangeExport"
    />
    <pintuanPrice v-model:data="updateData" :isNew="true" @getList="search"></pintuanPrice>
    <common-header title="拼团商品竞价管理" :showFold="false" v-loading="loading">
      <el-form label-position="right" label-width="10px">
        <el-row>
          <el-col :xs="16" :md="12" :lg="5" :xl="5">
            <el-form-item>
                <el-input placeholder="商品编码" size="small" v-model="form.barcode" clearable>
                  <template slot="prepend">商品编码</template>
                </el-input>
              </el-form-item>
          </el-col>
          <el-col :xs="16" :md="12" :lg="5" :xl="5">
            <el-form-item>
                <el-input placeholder="商品ERP编码" size="small" v-model="form.erpCode" clearable>
                  <template slot="prepend">商品ERP编码</template>
                </el-input>
              </el-form-item>
          </el-col>
          <el-col :xs="16" :md="12" :lg="5" :xl="5">
            <el-form-item>
                <el-input placeholder="商品名称" size="small" v-model="form.productName" clearable>
                  <template slot="prepend">商品名称</template>
                </el-input>
              </el-form-item>
          </el-col>
          <el-col :xs="16" :md="12" :lg="5" :xl="5">
            <el-form-item>
                <el-input placeholder="生产厂家" size="small" v-model="form.manufacturer" clearable>
                  <template slot="prepend">生产厂家</template>
                </el-input>
              </el-form-item>
          </el-col>
          <el-col :xs="16" :md="12" :lg="5" :xl="5">
            <el-form-item>
                <el-input placeholder="批准文号" size="small" v-model="form.approvalNumber" clearable>
                  <template slot="prepend">批准文号</template>
                </el-input>
              </el-form-item>
          </el-col>
          <el-col :xs="16" :md="12" :lg="5" :xl="5">
            <el-form-item>
              <province-list v-model="form.activityType" label="商品类型" :list="productTypeList" labelProp="label" valueProp="value"></province-list>
            </el-form-item>
          </el-col>
          <!-- <el-col :xs="16" :md="12" :lg="5" :xl="5">
            <el-form-item>
              <province-list v-model="form.biddingStatus" label="竞价状态" :list="auctionStatusList" labelProp="label" valueProp="value"></province-list>
            </el-form-item>
          </el-col> -->
        </el-row>
        <div style="display: flex;justify-content: space-between;align-items: center;margin-bottom: 15px;">
          <div style="line-height: 35px;">
            <span>提报竞价活动：</span>
            <el-popover
              v-for="item in actList"
              placement="top-start"
              title=""
              width="500"
              trigger="hover"
              style="margin-right: 5px;"
            >
              <el-button slot="reference" size="mini" @click="toCreatAct(item)">{{ item.actName }}</el-button>
              <div v-html="item.competitionRemark"></div>
            </el-popover>
          </div>
          <div style="flex-shrink: 0;">
            <el-button type="primary" size="mini" @click="pagination.pageNum = 1;search();">查询</el-button>
            <el-button size="mini" @click="reset">重置</el-button>
            <el-button type="primary" size="mini" @click="bulkChangesDialog = true;">批量操作</el-button>
            <el-button type="primary" size="mini" @click="exportExcel">导出</el-button>
          </div>
        </div>
         <!-- <div>
            <el-button :type="form.skuBiddingType == 1 ? 'primary' : ''" size="mini" @click="form.skuBiddingType = 1;pagination.pageNum = 1;search();">竞价中标商品（{{totalData.one}}）</el-button>
            <el-button :type="form.skuBiddingType == 2 ? 'primary' : ''" size="mini" @click="form.skuBiddingType = 2;pagination.pageNum = 1;search();">满足竞价未中标商品（{{totalData.two}}）</el-button>
            <el-button :type="form.skuBiddingType == 3 ? 'primary' : ''" size="mini" @click="form.skuBiddingType = 3;pagination.pageNum = 1;search();">竞价失败商品（{{totalData.three}}）</el-button>

          </div> -->
        <el-tabs v-model="form.skuBiddingType" @tab-click="pagination.pageNum = 1;search();">
          <el-tab-pane :label="`竞价中标商品(${totalData.one})`" name="one"></el-tab-pane>
          <el-tab-pane :label="`满足竞价未中标商品(${totalData.two})`" name="two"></el-tab-pane>
          <el-tab-pane :label="`竞价失败商品(${totalData.three})`" name="three"></el-tab-pane>
        </el-tabs>
      </el-form>
       <div style="margin-top: 10px;">
        <el-table  border fit :data="tableData" height="550px">
          <el-table-column label="商品信息" width="250">
            <template slot-scope="{ row }">
              <div style="color:red;">{{ row.productName }}</div>
              <div>{{ row.manufacturer }}</div>
              <div style="color:red;">{{ row.spec }}</div>
              <div>{{ row.approvalNumber }}</div>
              <div class="item-left">
                <span style="width: max-content;">商品编码：</span>
                <span>{{ row.barcode }}</span>
              </div>
              <div class="item-left">
                <span style="width: max-content;">CSUID：</span>
                <span>{{ row.csuid }}</span>
              </div>
              <div class="item-left">
                <span style="width: max-content;">ERP编码：</span>
                <span>{{ row.erpCode }}</span>
              </div>
              <div class="item-left">
                <span style="width: max-content;">原商品编码：</span>
                <span>{{ row.originalBarcode }}</span>
              </div>
              <div class="item-left">
                <span style="width: max-content;">ECSPU：</span>
                <span>{{ row.spuId }}</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="展示信息" width="250">
            <template slot-scope="{ row }">
              <el-image
                style="width: 80px; height: 73px; margin-bottom: 4px"
                :src="row.fullImageUrl"
                :preview-src-list="row.allImageList"
                @click.prevent
              />
              <div>{{ row.showName }}</div>
              <!-- <div class="item-left">
                <span style="width: max-content;">店铺编码：</span>
                <span>{{ row.shopCode }}</span>
              </div>
              <div class="item-left">
                <span style="width: max-content;">店铺名称：</span>
                <span>{{ row.companyName }}</span>
              </div> -->
            </template>
          </el-table-column>
          <el-table-column align="center" label="价格" width="250">
            <template slot-scope="{ row }">
              <!-- <div v-if="row.biddingSuccess" style="position: absolute;top: 0;left: 0;padding: 0 4px;border-bottom-right-radius: 5px;background-color: #00BE48;color: white;font-size: 12px;">
                恭喜中标
              </div> -->
              <div class="item-left">
                <span style="width: max-content;">提报价格：</span>
                <span style="margin-right: 5px;">{{ row.reportPrice }}</span>
              </div>
              <div class="item-left">
                <span style="width: max-content;">原价：</span>
                <span style="margin-right: 5px;">{{ row.fob }}</span>
              </div>
              <div class="item-left">
                <span style="width: max-content;">拼团价格：</span>
                <span style="margin-right: 5px;">{{ row.groupPrice }}</span>
                <i class="el-icon-edit-outline" style="color: #4184d5; font-size: 16px; cursor: pointer;transform:translateY(5px);" @click="updateData.actId = `PT${row.activityId}`;updateData.type=1;updateData.visible=true;"/>
              </div>
              <template v-for="it in row.ptActivityReportGroupAmountDtos">
                <div v-if="it.amount > 0" class="item-left" >
                  <span style="width: max-content;">{{ it.name }}补贴：</span>
                  <span style="margin-right: 5px;">{{ it.amount }}</span>
                </div>
              </template>

              <div class="item-left" v-if="row.provinceNum">
                <span style="width: max-content;"></span>
                <span style="margin-right: 5px;color: red;">{{ row.provinceNum }}个省份中标</span>
                <el-button type="text" size="mini" style="transform:translateY(-2px);" @click="detail = {list:row.priceSkuBiddingRulesDtos, actId: row.activityId}">查看</el-button>
              </div>
              <div v-if="form.skuBiddingType == 'one'" style="font-size: 12px;color: #d16817;text-align: start;">
                <div>恭喜您竞价成功。</div>
                <div>权益：当用户在搜索 该商品名称时，您的链接将会获得曝光加持</div>
              </div>
            </template>
          </el-table-column>
          <el-table-column align="center" label="起拼" width="250">
            <template slot-scope="{ row }">
              <div class="item-left">
                <span style="width: max-content;">起拼数量：</span>
                <span style="margin-right: 5px;">{{ row.groupNum }}</span>
                <i class="el-icon-edit-outline" style="color: #4184d5; font-size: 16px; cursor: pointer;transform:translateY(5px);" @click="updateData.actId = `PT${row.activityId}`;updateData.type=1;updateData.visible=true;"/>
              </div>
            </template>
          </el-table-column>
          <el-table-column align="center" label="库存" width="250">
            <template slot-scope="{ row }">
              <div class="item-left">
                <span style="width: max-content;">商品库存：</span>
                <span style="margin-right: 5px;">{{ row.availableQty }}</span>
              </div>
              <div class="item-left">
                <span style="width: max-content;">库存是否同步ERP：</span>
                <span style="margin-right: 5px;">{{ row.stockSyncErp == 1 ? '是' : '否' }}</span>
              </div>
              <div class="item-left">
                <span style="width: max-content;">活动总限购数量：</span>
                <span style="margin-right: 5px;">{{ row.totalLimitQtyStr }}</span>
              </div>
              <div class="item-left">
                <span style="width: max-content;color: red;">单店限购类型：</span>
                <span style="margin-right: 5px;color: red;">
                  {{ row.limitType ? personalLimitTypeList[row.limitType] : personalLimitTypeList[0] }}
                </span>
              </div>
              <div class="item-left">
                <span style="width: max-content;">单客户限购数量：</span>
                <span style="margin-right: 5px;">{{ row.limitQty }}</span>
              </div>
              <div class="item-left">
                <span style="width: max-content;">剩余活动库存：</span>
                <span style="margin-right: 5px;">{{ Number.isNaN(row.remainActivityInventory) ? row.totalStock : row.remainActivityInventory }}</span>
              </div>
              <div class="item-left">
                <span style="width: max-content;color: red;">库存覆盖客户数：</span>
                <span style="margin-right: 5px;color: red;">{{ row.coverCustomersByInventory }}</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column align="center" label="销售范围" width="250">
            <template slot-scope="{ row }">
              <div class="item-left" :title="row.busAreaName">
                <span style="width: max-content;">商圈：</span>
                <span style="margin-right: 5px;">{{ row.busAreaName }}</span>
              </div>
              <div class="item-left" :title="row.controlUserTypeList && row.controlUserTypeList.join(',') || '-'">
                <span style="width: max-content;">供货对象：</span>
                <span style="margin-right: 5px;">
                  {{ row.controlUserTypeList && row.controlUserTypeList.join(',') || '-' }}
                </span>
              </div>
            </template>
          </el-table-column>
          <el-table-column align="center" label="商品状态" width="250">
            <template slot-scope="{ row }">
              <p>{{ row.statusStr }}</p>
              <div class="item-left">
                <span style="width: max-content;">活动ID：</span>
                <span style="margin-right: 5px;">PT{{ row.activityId }}</span>
              </div>
              <div class="item-left">
                <span style="width: max-content;">活动时间：</span>
                <span style="margin-right: 5px;">
                  {{ transferTime(row.activityStartTime) }} ~ {{ transferTime(row.activityEndTime) }}
                </span>
              </div>
            </template>
          </el-table-column>
          <el-table-column align="center" label="销售数据" width="250">
            <template slot-scope="{ row }">
              <div class="item-left">
                <span style="width: max-content; ">采购明细：</span>
                <el-button type="text" size="mini" style="transform:translateY(-2px);" @click="lookMore(row)">查看</el-button>
              </div>
              <div class="item-left">
                <span style="width: max-content; ">
                  <el-tooltip
                    class="item"
                    content="有效订单中包含活动ID，对应客户去重"
                    placement="top"
                  >
                    <i class="el-icon-warning-outline" />
                  </el-tooltip>
                  采购店数：
                </span>
                <span style="margin-right: 5px;">{{ row.procureShopNum }}</span>
              </div>
              <div class="item-left">
                <span style="width: max-content; ">
                  <el-tooltip
                    class="item"
                    content="有效订单中包含特价活动/拼团活动ID，对应订单计数"
                    placement="top"
                  >
                    <i class="el-icon-warning-outline" />
                  </el-tooltip>
                  采购订单数：
                </span>
                <span style="margin-right: 5px;">{{ row.procureOrderNum }}</span>
              </div>
              <div class="item-left">
                <span style="width: max-content; ">
                  <el-tooltip
                    class="item"
                    content="有效订单、商品行中包含活动ID，取包含对应活动ID的各个商品行【应发货数量=商品数量-已退数量】之和"
                    placement="top"
                  >
                    <i class="el-icon-warning-outline" />
                  </el-tooltip>
                  采购数量：
                </span>
                <span style="margin-right: 5px;">{{ row.procureNum }}</span>
              </div>
              <div class="item-left">
                <span style="width: max-content; ">
                  <el-tooltip
                    class="item"
                    content="有效订单、商品行中包含活动ID，取【实付金额*应发货数量/商品数量=实付金额*（商品数量-已退数量）/商品数量】之和"
                    placement="top"
                  >
                    <i class="el-icon-warning-outline" />
                  </el-tooltip>
                  采购金额：
                </span>
                <span style="margin-right: 5px;">{{ row.procureAmount }}</span>
              </div>
              <div class="item-left">
                <span style="width: max-content; color: #00BE48;">未支付订单数：</span>
                <span style="margin-right: 5px;color: #00BE48;">{{ row.unpaiedOrderNum }}</span>
              </div>
              <div class="item-left">
                <span style="width: max-content; color: #00BE48;">采购数量：</span>
                <span style="margin-right: 5px;color: #00BE48;">{{ row.unpaiedProcureNum }}</span>
              </div>
            </template>
          </el-table-column>
        </el-table>
        <el-pagination
          style="position:relative;left:100%;display:inline-block;transform:translateX(-100%)"
          @size-change="(size) => {pagination.pageSize = size;search();}"
          @current-change="(num) => {pagination.pageNum = num;search();}"
          :current-page="pagination.pageNum"
          :page-sizes="[20, 50, 100]"
          :page-size="pagination.pageSize"
          background
          layout="total, sizes, prev, pager, next, jumper"
          :total="pagination.total">
        </el-pagination>
      </div>
    </common-header>
    <Detail v-model="detail"></Detail>
    <batch-edit
      v-if="bulkChangesDialog"
      @cancelModal="bulkChangesDialog = false;"
    />
  </div>
</template>

<style scoped lang="scss">
.item-left {
	width: 100%;
	display: flex;
	align-items: flex-start;
	margin: 0;
	> span:first-child,
	> button:first-child {
		flex-shrink: 0;
		width: 150px;
		text-align: end;
		color:#8d8d8d;
	}
	> span:nth-child(2) {
    color: rgb(43, 43, 43);
		flex-grow: 0;
		display: -webkit-box;//对象作为弹性伸缩盒子模型显示
    -webkit-box-orient: vertical;//设置或检索伸缩盒对象的子元素的排列方式
    -webkit-line-clamp: 2;//溢出省略的界限
		overflow: hidden;
		text-overflow: ellipsis;
		text-align: start !important;
	}
}
.el-dialog {
	border-radius: 7px;
	.el-dialog__body {
		padding-top: 10px;
		padding-bottom: 20px;
	}
}
</style>