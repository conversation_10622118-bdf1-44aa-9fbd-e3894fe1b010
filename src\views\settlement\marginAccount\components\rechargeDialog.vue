<template>
  <!-- 充值保证金的弹框 -->
  <el-dialog
    title="保证金充值"
    :visible.sync="dialogVisible"
    @closed="resetForm"
    width="40%"
    append-to-body
  >
    <div class="box">
      <div class="plainTextSty">
        <h3>平台对公账户</h3>
        <p><span>账户名称：</span>{{ publicAccountInfo.legalPersonName }}</p>
        <p><span>银行卡号：</span>{{ publicAccountInfo.bankCode }}</p>
        <p><span>开户银行：</span>{{ publicAccountInfo.bankName }}</p>
        <p><span>开户支行：</span>{{ publicAccountInfo.subBankName }}</p>
      </div>
      <div class="">
        <el-form label-width="100px" :rules="rules" ref="subForm" :model="subForm">
          <el-form-item label="充值金额" prop="rechargeAmount" key="rechargeAmount">
            <el-input
              v-model="subForm.rechargeAmount"
              style="width: 300px"
              type="number"
              :min="0"
              :disabled="!canChange"
            ></el-input>
          </el-form-item>
          <el-form-item label="付款证明" prop="payProof" key="payProof">
            <el-upload
              v-model="subForm.payProof"
              action
              list-type="picture-card"
              :before-upload="beforeAvatarUpload"
              :http-request="uploadImg"
              :on-remove="handleRemove"
              :file-list="fileList"
              :on-exceed="() => this.$message.error('超出文件上传数量限制')"
              multiple
              :limit="5"
            >
              <i class="el-icon-plus"></i>
            </el-upload>
          </el-form-item>
        </el-form>
      </div>
      <div class="textSty">
        <p>温馨提示:</p>
        <p>1、向平台约定的对公账户打款</p>
        <p>2、打款完成后请上传付款证明，支持jpeg、jpg、png、gif格式</p>
      </div>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button @click="dialogVisible = false">取 消</el-button>
      <el-button type="primary" @click="submitForm()">确定 </el-button>
    </span>
  </el-dialog>
</template>

<script>
import { getPublicAccountInfo, checkRechargeBond, rechargeBond, updateBond, getBond } from '@/api/settlement/marginAccount/index'
import { uploadFile } from '@/api/qual/index'

import { getHostName } from '@/api/storeManagement/index'

export default {
  props: ['formData'],
  // inject: ['accountData'], // 来源于index组件的accountData
  computed: {},
  data() {
    // 校验两位小数金额
    var checkPayAmount = (rule, value, callback) => {
      // console.log(rule)
      // const regex = /^(-?\d+(\.\d+)?)$/
      // const regex = /^(-?\d+(\.\d{1,2})?)$/
      const regex = /^\d+(\.\d{1,2})?$/
      const amount = parseFloat(value)


      if (!regex.test(amount)) {
        // 如果不匹配正则表达式，说明输入不符合要求
        this.subForm.rechargeAmount = null
        callback(new Error('输入必须为数字，不超过2位小数'))
      } else {
        // 尝试将输入转换为浮点数

        // 检查是否转换成功以及是否大于10.88
        if (isNaN(amount)) {
          this.subForm.rechargeAmount = null
          callback(new Error("输入的金额必须为数字"));
        } else {
          // 如果数值合法，将数值格式化为两位小数
          const formattedValue = amount
          if (formattedValue === 0) {
            this.subForm.rechargeAmount = null
            callback(new Error("输入的金额必须大于0"));
            return
          }
          if (rule.field == 'rechargeAmount') this.subForm.rechargeAmount = formattedValue
          // console.log(formattedValue)
          //   调用callback函数，不传递错误，但传递格式化后的值（如果需要的话）
          //   callback(null, formattedValue); // 假设您想传递格式化后的值回表单（视具体情况而定）
        }
      }
      callback()
    }
    return {
      dialogVisible: false,
      subForm: {
        rechargeAmount: null, // 充值金额
        payProof: [], // 付款证明
        merchantFundAuditId: null,
        type: null,
        voucher: null // 凭证
      },
      rules: {
        rechargeAmount: [
          { required: true, message: '请输入充值金额', trigger: 'blur' },
          { validator: checkPayAmount, trigger: 'blur' }
        ],
        payProof: [{ required: true, message: '请上传图片', trigger: 'change' }]
      },

      publicAccountInfo: {},
      canChange: true,

      hostName: '',
      fileList: []
    }
  },
  created() {
    getHostName().then((res) => {
      if (res.hostName) {
        this.hostName = res.hostName
      }
    })
    getPublicAccountInfo().then((res) => {
      if(res.code == 0) {
        this.publicAccountInfo = res.data
      }
    })

    // if (this.formData) {
    //   this.fileList = this.formData.fileList
    //   this.subForm.rechargeAmount = this.formData.rechargeAmount
    //   this.subForm.payProof = this.formData.fileList
    // }
  },
  methods: {
    openDialog(type, val) {
      // type = 'buy' | 'change'
      this.subForm.type = type
      if (type == 'buy') {
        checkRechargeBond().then((res) => {
          if(res.code == 0) {
            if( res.data.failResult ) {
              this.$message.error(res.data.failResult)
              return
            }
            if(res.data.pass == 2) return
            if(res.data.firstCharge == 1) {
              this.subForm.rechargeAmount = res.data.amount
              this.canChange = false
            }
            this.subForm.voucher = res.data.voucher
            this.dialogVisible = true
          }

        }) // 检查是否可以充值保证金
      }
      if (type == 'change') {
        this.subForm.merchantFundAuditId = val.id
        if(val.paymentProof) {
          this.fileList = val.paymentProof.split(',').map((item) => {
            return {url: item}
          })
          this.subForm.payProof = this.fileList
        }
        if(val.isFirstCharge == 1) {
          getBond().then((res) => {
            this.subForm.rechargeAmount = res.data
            this.canChange = false
          })
        } else this.subForm.rechargeAmount = val.amount
        this.dialogVisible = true
      }
      // console.log(type, val, this.accountData)
    }, // 打开新增弹框

    submitForm() {
      if (this.fileList.length == 0) {
        this.$message.warning('请上传付款证明')
        return false
      }
      this.$refs.subForm.validate((valid) => {
        if (valid) {
          let subData = {
            amount: parseFloat(this.subForm.rechargeAmount),
            paymentProof: this.subForm.payProof.map((item) => item.url).join(','),
            // voucher: this.subForm.voucher
          }
          if(this.subForm.type == 'buy'){
            subData.voucher = this.subForm.voucher
            rechargeBond(subData).then((res) => {
              if (res.code == 0) {
                this.dialogVisible = false
                // this.$parent.loadData()
              } else {
                this.$message.error(res.message)
              }
            })
          } else if(this.subForm.type == 'change'){
            subData.merchantFundAuditId = this.subForm.merchantFundAuditId
            updateBond(subData).then((res) => {
              if (res.code == 0) {
                this.dialogVisible = false
                this.$parent.search()
              } else {
                this.$message.error(res.message)
              }
            })
          }
        } else {
          this.$message.error('请检查表单内容！')
        }
      })
    },
    resetForm() {
      this.$refs.subForm.resetFields()
      this.fileList = []
      this.canChange = true
    },

    // 上传图片的逻辑
    uploadImg(file) {
      uploadFile(file).then((res) => {
        if (res.code === '200') {
          // this.form.viewImage = `${this.hostName}/${res.data}`;
          let temp = { name: res.data, url: `${this.hostName}${res.data}` }
          // this.fileList = [...this.fileList, temp];
          this.fileList.push(temp)
          this.subForm.payProof = this.fileList
          // console.log(res.data);
          console.log(this.fileList)
        } else {
          // this.form.viewImage = '';
          console.log('上传失败')
        }
      })
      return true
    },
    beforeAvatarUpload(file) {
      console.log(file)

      if (!file) {
        this.$message.error('请上传图片')
        return false
      }
      const isJPG =
        file.type === 'image/jpeg' ||
        file.type === 'image/png' ||
        file.type === 'image/bmp' ||
        file.type === 'image/jpg' ||
        file.type === 'image/gif'
      const isLt5M = file.size / 1024 / 1024 < 5

      if (!isJPG || !isLt5M) {
        this.$message.error('图片不满足上传要求，请重新上传')
        return false
      }
      return isJPG && isLt5M
    },
    handleRemove(file, fileList) {
      this.fileList = fileList
      this.subForm.payProof = this.fileList
      console.log(file, fileList, this.fileList)
    }
  }
}
</script>

<style scoped lang="scss">
.box {
  width: 100%;
  display: flex;
  flex-direction: column;
  // justify-content: center;
  // align-items: center;
  padding: 0 10px;
}
.plainTextSty {
  width: 100%;
  // height: 100px;
  padding: 0 10px;
  margin: -20px 0 20px 0;
  background-color: #fafafa;
}
.plainTextSty span {
  width: 30px;
}
.textSty {
  color: #575757;
}
/* 推荐，实现简单 */
::v-deep   .el-upload-list__item.is-ready,
::v-deep   .el-upload-list__item.is-uploading {
  display: none !important;
}
</style>
