<template>
  <div class="serviceQuality" v-if="shopConfig.indexShow">
    <div class="task_title">
      <span @click="goJump" style="cursor: pointer; font-weight: bold">服务质量（近7天）</span>
    </div>
    <el-row class="infoItem">
      <el-col :span="12">
        <div class="todoItem" @click="goJump(0)">
          <span>48h发货率：</span>
          <div class="percent">
            <span >{{ dataObj.sendNum || 0 }}%</span>
            <img src="@/assets/image/home/<USER>/good2x.png" alt="" v-if="dataObj.sendNum >= 90">
            <img src="@/assets/image/home/<USER>/bad2x.png" alt="" v-else-if="dataObj.sendNum <= 75">
            <!-- <img src="@/assets/image/home/<USER>/lowaverage.png" alt="" v-else> -->
            <!-- <div
              :class="
                dataObj.sendNum >= 90
                  ? 'rate-left'
                  : dataObj.sendNum > 75
                  ? 'rate-left1'
                  : 'rate-left2'
              "
            ></div>
            <span
              class="rate"
              :style="{
                background:
                  dataObj.sendNum >= 90 ? '#03C261' : dataObj.sendNum > 75 ? '#FFA215' : '#FF4444'
              }"
              >{{
                dataObj.sendNum >= 90 ? '很棒' : dataObj.sendNum > 75 ? '低于平均值' : '较差'
              }}</span
            >-->
          </div>
        </div>
      </el-col>
      <!-- <el-col :span="12">
        <span class="todoItem bg_red" @click="goJump(1)">商家原因退款率：{{dataObj.refundNum||0}}%</span>
      </el-col> -->
      <el-col :span="12">
        <div class="todoItem" @click="goJump(2)">
          <span>商家原因退货率：</span>
          <div class="percent">
            <span >{{ dataObj.refundOrderNum || 0 }}%</span>
            <img src="@/assets/image/home/<USER>/good2x.png" alt="" v-if="dataObj.refundOrderNum <= 1">
            <img src="@/assets/image/home/<USER>/bad2x.png" alt="" v-else-if="dataObj.refundOrderNum >= 3">
            <!-- <img src="@/assets/image/home/<USER>/lowaverage.png" alt="" v-else> -->

            <!-- <div
              :class="
                dataObj.sendNum <= 1
                  ? 'rate-left'
                  : dataObj.sendNum < 3
                  ? 'rate-left1'
                  : 'rate-left2'
              "
            ></div>
            <span
              class="rate"
              :style="{
                background:
                  dataObj.refundOrderNum <= 1
                    ? '#03C261'
                    : dataObj.refundOrderNum < 3
                    ? '#FFA215'
                    : '#FF4444'
              }"
              >{{
                dataObj.refundOrderNum <= 1
                  ? '很棒'
                  : dataObj.refundOrderNum < 3
                  ? '低于平均值'
                  : '较差'
              }}</span
            > -->
          </div>
        </div>
      </el-col>
    </el-row>
    <el-row class="infoItem">
      <el-col :span="12">
        <div class="todoItem" @click="goJump(3)">
          <span>客服在线响应率：</span>
          <div class="percent">
            <span >{{ dataObj.respondCt || 0 }}% </span>
            <img src="@/assets/image/home/<USER>/good2x.png" alt="" v-if="dataObj.respondCt >= 90">
            <img src="@/assets/image/home/<USER>/bad2x.png" alt="" v-else-if="dataObj.respondCt <= 70">
            <!-- <img src="@/assets/image/home/<USER>/lowaverage.png" alt="" v-else> -->
            <!-- <div
              :class="
                dataObj.respondCt >= 90
                  ? 'rate-left'
                  : dataObj.respondCt > 70
                  ? 'rate-left1'
                  : 'rate-left2'
              "
            ></div>
            <span
              class="rate"
              :style="{
                background:
                  dataObj.invoiceAsNum >= 90
                    ? '#03C261'
                    : dataObj.invoiceAsNum > 70
                    ? '#FFA215'
                    : '#FF4444'
              }"
              >{{
                dataObj.respondCt >= 90 ? '很棒' : dataObj.respondCt > 70 ? '低于平均值' : '较差'
              }}</span
            > -->
          </div>
        </div>
      </el-col>
      <el-col :span="12">
        <el-tooltip placement="top">
          <div slot="content">
            <p>发票售后率=已处理发票售后订单数量/总订单数量</p>
            <p>已处理发票售后状态：商家已处理、商家已补发</p>
            <p>订单状态：待审核、待发货、配送中、已完成、已退款</p>
          </div>
          <div class="todoItem" @click="goJump(4)">
            <span>发票售后率：</span>
            <div class="percent">
              <span >{{ dataObj.invoiceAsNum || 0 }}% </span>
              <img src="@/assets/image/home/<USER>/good2x.png" alt="" v-if="dataObj.invoiceAsNum <= 1.5">
              <img src="@/assets/image/home/<USER>/bad2x.png" alt="" v-else-if="dataObj.invoiceAsNum >= 3">
              <!-- <img src="@/assets/image/home/<USER>/lowaverage.png" alt="" v-else> -->
              <!-- <div
                :class="
                  dataObj.invoiceAsNum <= 1.5
                    ? 'rate-left'
                    : dataObj.invoiceAsNum < 3
                    ? 'rate-left1'
                    : 'rate-left2'
                "
              ></div>
              <span
                class="rate"
                :style="{
                  background:
                    dataObj.invoiceAsNum <= 1.5
                      ? '#03C261'
                      : dataObj.invoiceAsNum < 3
                      ? '#FFA215'
                      : '#FF4444'
                }"
                >{{
                  dataObj.invoiceAsNum <= 1.5
                    ? '很棒'
                    : dataObj.invoiceAsNum < 3
                    ? '低于平均值'
                    : '较差'
                }}</span
              > -->
            </div>
          </div>
        </el-tooltip>
      </el-col>
    </el-row>
    <el-row class="infoItem">
      <el-col :span="12">
        <el-tooltip placement="top">
          <div slot="content">
            <p>资质售后率=已处理资质售后订单数量/总订单数量</p>
            <p>已处理资质售后状态：商家已处理、商家已补发</p>
            <p>订单状态：待审核、待发货、配送中、已完成、已退款</p>
          </div>
          <div class="todoItem" @click="goJump(5)">
            <span>资质售后率：</span>
            <div class="percent">
              <span >{{ dataObj.credentialAsNum || 0 }}%</span>
              <img src="@/assets/image/home/<USER>/good2x.png" alt="" v-if="dataObj.credentialAsNum <= 1.5">
              <img src="@/assets/image/home/<USER>/bad2x.png" alt="" v-else-if="dataObj.credentialAsNum >= 3">
              <!-- <img src="@/assets/image/home/<USER>/lowaverage.png" alt="" v-else> -->

              <!-- <div
                :class="
                  dataObj.credentialAsNum <= 1.5
                    ? 'rate-left'
                    : dataObj.credentialAsNum < 3
                    ? 'rate-left1'
                    : 'rate-left2'
                "
              ></div>
              <span
                class="rate"
                :style="{
                  background:
                    dataObj.credentialAsNum <= 1.5
                      ? '#03C261'
                      : dataObj.credentialAsNum < 3
                      ? '#FFA215'
                      : '#FF4444'
                }"
                >{{
                  dataObj.credentialAsNum <= 1.5
                    ? '很棒'
                    : dataObj.credentialAsNum < 3
                    ? '低于平均值'
                    : '较差'
                }}</span
              > -->
            </div>
          </div>
        </el-tooltip>
      </el-col>
      <el-col :span="12">
        <div class="todoItem" @click="goJump(2)">
          <span>客服响应时长：</span>
          <div class="percent">
            <span >{{ dataObj.firstRespondCt || 0 }}分钟</span>
            <img src="@/assets/image/home/<USER>/good2x.png" alt="" v-if="dataObj.firstRespondCt <= 15">
            <img src="@/assets/image/home/<USER>/bad2x.png" alt="" v-else-if="dataObj.firstRespondCt >= 30">
            <!-- <img src="@/assets/image/home/<USER>/lowaverage.png" alt="" v-else> -->
            <!-- <div
              :class="
                dataObj.firstRespondCt <= 15
                  ? 'rate-left'
                  : dataObj.firstRespondCt < 30
                  ? 'rate-left1'
                  : 'rate-left2'
              "
            ></div>
            <span
              class="rate"
              :style="{
                background:
                  dataObj.firstRespondCt <= 15
                    ? '#03C261'
                    : dataObj.firstRespondCt < 30
                    ? '#FFA215'
                    : '#FF4444'
              }"
              >{{
                dataObj.firstRespondCt <= 15
                  ? '很棒'
                  : dataObj.credentialAsNum < 30
                  ? '低于平均值'
                  : '较差'
              }}</span
            > -->
          </div>
        </div>
      </el-col>
    </el-row>
    <el-row class="infoItem">
      <el-col :span="12">
        <div class="todoItem" @click="jumpPage(1)">
          <span>商品首营资料上传率：</span>
          <div class="percent">
            <span >{{ dataObj.qualificationNum || 0 }}%</span>
            <img src="@/assets/image/home/<USER>/good2x.png" alt="" v-if="dataObj.qualificationNum >= 80">
            <img src="@/assets/image/home/<USER>/bad2x.png" alt="" v-else-if="dataObj.qualificationNum <= 30">
            <!-- <img src="@/assets/image/home/<USER>/lowaverage.png" alt="" v-else> -->

            <!-- <div
              :class="
                dataObj.qualificationNum >= 80
                  ? 'rate-left'
                  : dataObj.qualificationNum > 30
                  ? 'rate-left1'
                  : 'rate-left2'
              "
            ></div>
            <span
              class="rate"
              :style="{
                background:
                  dataObj.qualificationNum >= 80
                    ? '#03C261'
                    : dataObj.qualificationNum > 30
                    ? '#FFA215'
                    : '#FF4444'
              }"
              >{{
                dataObj.qualificationNum >= 80
                  ? '很棒'
                  : dataObj.qualificationNum > 30
                  ? '低于平均值'
                  : '较差'
              }}</span
            > -->
          </div>
        </div>
      </el-col>
      <el-col :span="12">
        <div class="todoItem" @click="jumpPage(2)">
          <span>药检报告订单覆盖率：</span>
          <div class="percent">
            <span >{{ dataObj.drugReportNum || 0 }}%</span>
            <img src="@/assets/image/home/<USER>/good2x.png" alt="" v-if="dataObj.drugReportNum >= 90">
            <img src="@/assets/image/home/<USER>/bad2x.png" alt="" v-else-if="dataObj.drugReportNum <= 70">
            <!-- <img src="@/assets/image/home/<USER>/lowaverage.png" alt="" v-else> -->
            <!-- <div
              :class="
                dataObj.drugReportNum >= 90
                  ? 'rate-left'
                  : dataObj.drugReportNum > 70
                  ? 'rate-left1'
                  : 'rate-left2'
              "
            ></div>
            <span
              class="rate"
              :style="{
                background:
                  dataObj.drugReportNum >= 90
                    ? '#03C261'
                    : dataObj.drugReportNum > 70
                    ? '#FFA215'
                    : '#FF4444'
              }"
              >{{
                dataObj.drugReportNum >= 90
                  ? '很棒'
                  : dataObj.drugReportNum > 70
                  ? '低于平均值'
                  : '较差'
              }}</span
            > -->
          </div>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { apiQueryServeQuality } from '@/api/home'
import { mapState } from 'vuex'
import { actionTracking } from '@/track/eventTracking'

export default {
  name: 'ServiceQualitySeven',

  data() {
    return {
      dataObj: {}
    }
  },
  computed: {
    ...mapState('app', ['shopConfig'])
  },
  created() {
    this.queryData()
  },
  methods: {
    async queryData() {
      const res = await apiQueryServeQuality()
      if (res && res.code === 0) {
        this.dataObj = res.result || {}
      }
    },
    goJump(position) {
      actionTracking('service_quality_click', {
        service_quality: [
          '48send',
          'refund',
          'return',
          'response',
          'afterSale-invoice',
          'afterSale-qualtification'
        ][position]
      })
      let sevenDate = []
      let startDate = ''
      let endDate = ''
      startDate = dayjs().add(-7, 'days').format('YYYY-MM-DD')
      endDate = dayjs().add(-1, 'days').format('YYYY-MM-DD')
      sevenDate = [startDate, endDate]
      sessionStorage.setItem('sevenDate', JSON.stringify(sevenDate))
      window.openTab('/serviceQuality')
    },
    jumpPage(type) {
      const path = type == 1 ? '/qualificationManage' : '/drugTestResultManage'
      window.openTab(path)
    }
  }
}
</script>

<style scoped lang="scss">
.serviceQuality {
  background-color: #fff;
  padding: 16px;
  margin-bottom: 16px;
  .task_title {
    font-size: 16px;
    line-height: 22px;
    color: #333;
    font-weight: 500;
    font-family: PingFangSC, PingFangSC-Medium;
    margin-bottom: 6px;
  }
  .infoItem {

  }
  .todoItem {
    cursor: pointer;
    padding-right: 4px;
    font-size: 14px;
    font-family: PingFangSC, PingFangSC-Regular;
    font-weight: 400;
    line-height: 20px;
    border: 1px solid #e9e9e9;
    border-radius: 3px;
    background-color: #f5f5f5;
    margin: 6px;
    padding: 8px;
  }
  // .todoItem:before {
  //   content: '';
  //   display: inline-block;
  //   vertical-align: middle;
  //   width: 6px;
  //   height: 6px;
  //   border-radius: 50%;
  //   margin-right: 4px;
  // }
  .todoItem.bg_red:before {
    background: red;
  }
  .todoItem.bg_orange:before {
    background: #f07810;
  }
  .todoItem.bg_green:before {
    background: #00a03e;
  }
  .percent {
    font-size: 14px;
    font-weight: bold;
    margin-top: 8px;
    display: flex;
    align-items: center;
    gap:5px;
  }
}
.rate {
  background-color: orange;
  border-radius: 6px 6px 6px 0;
  padding: 3px;
  font-size: 12px;
  color:white;
}
.rate-left {
  display: inline-block;
  width: 20px;
  height: 20px;
  background-image: radial-gradient(circle 20px at 100% 0, transparent 20px, #03c261 50%);
  transform: rotate(270deg);
  vertical-align: -7px;
}
.rate-left1 {
  display: inline-block;
  width: 20px;
  height: 20px;
  background-image: radial-gradient(circle 20px at 100% 0, transparent 20px, #ffa215 50%);
  transform: rotate(270deg);
  vertical-align: -7px;
}
.rate-left2 {
  display: inline-block;
  width: 20px;
  height: 20px;
  background-image: radial-gradient(circle 20px at 100% 0, transparent 20px, #ff4444 50%);
  transform: rotate(270deg);
  vertical-align: -7px;
}
.percent img{
  position: relative;
  bottom:3px;
}
.percent img{
  height:19px;
}
.percent span{
  font-size: 15px;
}
</style>
