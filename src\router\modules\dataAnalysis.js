import layout from '@/layout';

const statisticsManage = {
  path: '/statisticsManage',
  name: 'statisticsManage',
  component: layout,
  meta: {
    title: '数据分析',
    icon: 'el-icon-s-data',
  },
  children: [
    {
      path: '/salesReports',
      name: 'salesReports',
      component: () => import('@/views/data-statistics/salesReports.vue'),
      meta: { title: '销售报表' },
    },
    {
      path: '/serviceQuality',
      name: 'serviceQuality',
      component: () => import('@/views/data-statistics/serviceQuality.vue'),
      meta: { title: '服务质量' },
    },
    {
      path: '/varietyAnalysis',
      name: 'varietyAnalysis',
      component: () => import('@/views/data-statistics/varietyAnalysis.vue'),
      meta: { title: '品种分析' },
    },
    {
      path: '/varietyAnalysis/detail',
      name: 'varietyAnalysis/detail',
      component: () => import('@/views/data-statistics/varietyAnalysisDetail.vue'),
      hidden: true,
      meta: { title: '品种分析', noCache: true },
    },
  ],
};
export default statisticsManage;
