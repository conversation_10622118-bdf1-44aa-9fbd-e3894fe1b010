<template>
  <el-dialog
    title="赠送优惠券"
    :visible.sync="dialogVisible"
    width="80%"
    :before-close="handleClose"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
  >
    <div class="giveCoupon">
      <el-form ref="form" :model="formData" :rules="rules" label-width="120px">
        <el-form-item label="赠送方式">
          <el-radio-group v-model="formData.giveType" @change="radioChange">
            <el-radio :label="2">药店ERP编码</el-radio>
            <el-radio :label="1">药店ID</el-radio>
            <el-radio :label="3">选择药店</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item v-if="formData.giveType===1" label="药店ID" prop="merchantId" :rules="isIdRules">
          <el-input type="textarea" v-model="formData.merchantId" style="padding-bottom: 10px"
                    placeholder="请输入药店ID，多个ID之间用英文逗号隔开。点击【点击目标药店】，核对无误后确认提交"
          ></el-input>
        </el-form-item>
        <el-form-item v-if="formData.giveType===2" label="药店ERP编码" prop="sellerUserId" :rules="isERPRules">
          <el-input type="textarea" v-model="formData.sellerUserId" style="padding-bottom: 10px"
                    placeholder="请输入药店ERP编码，多个ERP编码之间用英文逗号隔开。点击【点击目标药店】，核对无误后确认提交"
          ></el-input>
        </el-form-item>
        <el-form-item v-if="formData.giveType===2||formData.giveType===1">
          <el-button type="primary" @click="checkCouponUser">确认目标药店</el-button>
        </el-form-item>
        <el-form-item v-if="formData.giveType===3" label="药店ID">
          <el-button type="primary" @click="selectDrugstore">+选择药店</el-button>
        </el-form-item>
        <el-form-item label="每人赠送张数" prop="count">
          <el-input v-model="formData.count" style="width: 180px" placeholder="填写大于等于1的正整数"
                    onkeyup="value=value.replace(/[^\d]/g,'')"
          ></el-input>
        </el-form-item>
        <el-form-item label="赠送药店明细">
          <el-input v-model="formData.merchantName" placeholder="搜索药店名称" style="width: 180px"
                    @keyup.native.enter="queryList"
          ></el-input>
        </el-form-item>
        <el-form-item>
          <xyyTable
            ref="productListTable"
            v-loading="tableLoading"
            :data="tableConfig.data"
            :col="tableConfig.col"
            :operation="tableConfig.operation"
            @operation-click="operationClick"
            :list-query="listQuery"
            @get-data="queryList"
          />
        </el-form-item>
      </el-form>
      <selectDrugstore :selectDrugstoreVisible.sync="selectDrugstoreVisible" v-if="selectDrugstoreVisible"
                       @confirmCallBack="selectDrugstoreConfirm"
      />
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button type="primary" @click="submit">提 交</el-button>
      <el-button @click="handleClose">取 消</el-button>
  </span>
  </el-dialog>
</template>

<script>
import selectDrugstore from './components/selectDrugstore';
import { getList } from '@/api/customer-management';
import { sendCoupon, checkSendCouponUser } from '@/api/coupon';

export default {
  name: 'giveCoupon',
  props: {
    giveCouponTemplateId: {
      type: String,
      default: ''
    }
  },
  components: { selectDrugstore },
  data() {
    const isNumber = (rule, value, callback) => {
      let ret = /^([0-9]*)$/;
      if (value === '') {
        callback(new Error('不能为空'));
      } else if (!ret.test(value)) {
        callback(new Error('填写大于等于1的正整数'));
      } else {
        callback();
      }
    };
    return {
      formData: {
        giveType: 1,
        merchantId: '',
        sellerUserId: '',
        merchantName: '',
        count: '',
        storeName: ''
      },
      rules: {
        count: [
          {
            required: true,
            message: '请输入每人赠送张数',
            trigger: 'blur'
          },
          {
            validator: isNumber,
            trigger: 'blur'
          }
        ]
      },
      isIdRules: [
        {
          required: true,
          message: '请输入药店ID',
          trigger: 'blur'
        }
      ],
      isERPRules: [
        {
          required: true,
          message: '请输入药店ERP编码',
          trigger: 'blur'
        }
      ],
      listQuery: {
        pageSize: 200,
        page: 1,
        total: 0
      },
      tableLoading: false,
      tableConfig: {
        data: [],
        col: [
          {
            name: '药店ID',
            index: 'merchantId'
          },
          {
            name: '药店ERP编码',
            index: 'sellerUserId'
          },
          {
            name: '手机号',
            index: 'mobile'
          },
          {
            name: '药店名称',
            index: 'merchantName'
          },
          {
            index: 'operation',
            name: '操作',
            operation: true,
            width: 150
          }
        ],
        operation: [
          {
            name: '删除',
            type: 1
          }
        ]
      },
      templateId: '',
      selectDrugstoreVisible: false,
      dialogVisible: true
    };
  },
  created() {
    console.log('created----------');
    this.templateId = this.giveCouponTemplateId;
  },
  methods: {
    handleClose() {
      this.$emit('update:giveCouponDialogVisible', false);
    },
    getParams() {
      const {
        pageSize,
        page
      } = this.listQuery;
      const {
        merchantId,
        sellerUserId,
        merchantName
      } = this.formData;
      const params = {
        pageNum: page,
        pageSize,
        merchantIdstr: merchantId,
        sellerUserId,
        merchantName
      };
      return params;
    },
    async queryList() {
      this.tableLoading = true;
      const res = await getList(this.getParams());
      if (res && res.code === 0) {
        if (res.data.list.length > 0) {
          this.tableConfig.data = res.data.list;
          this.listQuery.total = res.data.total;
        }
      } else {
        this.$message.error(res.message || '列表查询失败');
      }
      this.tableLoading = false;
    },
    radioChange() {
      this.formData.merchantId = '';
      this.formData.sellerUserId = '';
    },
    async checkCouponUser() {
      //确认目标药店
      if (this.formData.giveType === 1) {
        if (!this.formData.merchantId) {
          this.$message.warning('请添加药店ID!');
          return false;
        }
      } else if (this.formData.giveType === 2) {
        if (!this.formData.sellerUserId) {
          this.$message.warning('请添加药店ERP编码!');
          return false;
        }
      }
      const params = {
        userIds: this.formData.sellerUserId || this.formData.merchantId,
        type: this.formData.giveType
      };
      const res = await checkSendCouponUser(params);
      console.log(res);
      if (res && res.success) {
        const merchantIds = res.data.userIds.filter(item => !!item);
        if (merchantIds && merchantIds.length > 0) {
          const {
            pageSize,
            page
          } = this.listQuery;
          const params = {
            pageNum: page,
            pageSize,
            merchantIdstr: merchantIds.join(',')
          };
          this.tableLoading = true;
          const resp = await getList(params);
          if (resp && resp.code === 0) {
            if (resp.data.list.length > 0) {
              this.tableConfig.data = resp.data.list;
              this.listQuery.total = resp.data.total;
            }
          } else {
            this.$message.error(resp.message || '列表查询失败');
          }
          this.tableLoading = false;
        } else {
          if (this.formData.giveType === 2) {
            this.$message.error(`药店ERP编码${this.formData.sellerUserId}不存在`);
          } else if (this.formData.giveType === 1) {
            this.$message.error(`药店id编码${this.formData.merchantId}不存在`);
          }
        }
      } else {
        this.$message.error(res.msg || '用户校验出错');
      }
    },
    selectDrugstore() {
      this.selectDrugstoreVisible = true;
    },
    selectDrugstoreConfirm(ary) {
      this.tableConfig.data = ary;
    },
    operationClick(type, row) {
      console.log(type, row);
      if (type === 1) {
        this.$confirm('是否确定删除?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            this.tableConfig.data = this.tableConfig.data.filter(item => item.id !== row.id);
          });
      }
    },
    submit() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          if (this.tableConfig.data.length === 0) {
            this.$message.warning('请确认目标药店！');
            return false;
          }
          const {
            count
          } = this.formData;
          const templateId = this.templateId;

          const params = {
            count,
            templateId: templateId,
            userIds: this.tableConfig.data.map(item => {
              return item.merchantId;
            })
              .join(',')
          };
          this.$confirm(`确认给${params.userIds.split(',').length}个用户赠送该优惠券吗?`, '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          })
            .then(async () => {
              const res = await sendCoupon(params);
              if (res && res.success) {
                this.$message.success(res.msg || '提交成功');
                this.handleClose();
              } else {
                this.$message.error(res.msg || '提交失败');
              }
            });
        }
      });
    }
  }
};
</script>

<style scoped lang="scss">
::v-deep  .el-dialog__footer{
  text-align: center;
}
</style>
