<template>
  <div class="content">
    <div class="title_line">商品控销黑名单</div>
    <SearchForm
      ref="searchForm"
      :model="formModel"
      :form-items="formItems"
      :hasOpenBtn="false"
      @submit="handleFormSubmit"
      @reset="handleFormReset"
    />
    <div class="operation">
      <el-button type="primary" size="mini" @click="exportData" style="margin-right: 10px;">导出</el-button>
    </div>
    <div style="margin: 15px 0;color: red">
      <p>以下黑名单中客户无法购买规则对应的控销商品</p>
      <p>
        如要新增、删除请到控销商品组操作
      <el-button type="text" @click="goControlledSalesGoods">点击此处快速跳转到【控销商品组】页面</el-button>
    </p>
    </div>
    <el-table
      ref="productListTable"
      v-loading="tableLoading"
      :data="tableConfig.data"
      style="width: 100%"
    >
      <el-table-column prop="merchantId" label="药店ID"></el-table-column>
      <el-table-column prop="merchantName" label="药店名称"></el-table-column>
      <el-table-column prop="skuNums" label="控销商品">
        <template slot-scope="scope">
          <span class="blueText" @click="onAction('see', scope.row, 'skuType')">{{ scope.row.skuNums }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="groupName" label="控销组名称"></el-table-column>
      <el-table-column label="操作" fixed="right" width="150">
      </el-table-column>
    </el-table>
    <el-pagination
      background
      layout="prev, pager, next, jumper"
      :total="listQuery.total"
      :page-size="listQuery.pageSize"
      :current-page.sync="listQuery.page"
      @current-change="handleCurrentChange"
    ></el-pagination>
    <AddDialog
      v-if="addDialogVisible"
      :addDialogVisible.sync="addDialogVisible"
    />
    <exportTips :change-export="changeExport" @handleExoprClose="changeExport = false" @handleChangeExport="handleChangeExport"></exportTips>
  </div>
</template>

<script>
import SearchForm from '@/components/searchForm';
import exportTips from '@/views/other/components/exportTip.vue'
import AddDialog from '@/views/store-management/blacklistStoreControlledSales/components/addDialog.vue';
import { apiRemoveBuyer, apiDownloadTemplate, exportData, apiGetShopName } from '@/api/storeManagement/blacklist';
import { skuPage } from '@/api/goods/controlGoods.js';

export default {
  name: 'index',
  components: {
    SearchForm,
    AddDialog,
    exportTips
  },
  data() {
    return {
      formModel: {
        buyerId: '',
        buyerName: '',
        skuId: ''
      },
      formItems: [
        {
          label: '药店ID',
          prop: 'buyerId',
          component: 'el-input',
          attrs: {
            placeholder: '请输入'
          }
        },
        {
          label: '药店名称',
          prop: 'buyerName',
          component: 'el-input',
          attrs: {
            placeholder: '请输入'
          }
        },
        {
          label: '控销商品',
          prop: 'skuId',
          component: 'el-input',
          attrs: {
            placeholder: '请输入sku编码查找',
            oninput: "value=value.replace(/[^0-9.]/g,'')"
          }
        }
      ],
      changeExport: false,
      tableLoading: false,
      tableConfig: {
        data: []
      },
      listQuery: {
        pageSize: 10,
        page: 1,
        total: 0
      },
      addDialogVisible: false
    };
  },
  created() {
    this.queryList();
  },
  methods: {
    onAction(type, row, activeName) {
      if (type === 'see') {
        this.$router.push(`/checkControlGroup?activeName=${activeName}&groupId=${row.groupId}`);
      }
    },
    handleCurrentChange(page) {
      this.listQuery.page = page;
      this.queryList();
    },
    exportData() {
      if (this.tableLoading) return;
      this.tableLoading = true;
      let params = { ...this.formModel };
      const {
        pageSize,
        page
      } = this.listQuery;
      params.pageNum = page;
      params.pageSize = pageSize;
      exportData(params).then(res => {
        if (res.code !== 0) {
					this.$message.warning(res.msg);
					return;
				}
        this.changeExport = true;
      }).finally(() => {
        this.tableLoading = false;
      })
    },
    async queryList() {
      this.tableLoading = true;
      let params = {
        merchantId: this.formModel.buyerId,
        merchantName: this.formModel.buyerName,
        pageNum: this.listQuery.page,
        pageSize: this.listQuery.pageSize
      };
      
      if (this.formModel.skuId) {
        params.skuId = this.formModel.skuId;
      }
      
      console.log("查询参数:", JSON.stringify(params));
      try {
        const res = await apiGetShopName(params);
        if (res && res.code === 0) {
          this.tableConfig.data = res.data.list || [];
          this.listQuery.total = res.data.total || 0;
        } else {
          this.$message.warning(res.msg || '获取数据失败');
        }
      } catch (e) {
        console.log(e);
        this.$message.error('获取数据异常');
      }
      this.tableLoading = false;
    },
    async querySkuList() {
      this.tableLoading = true;
      let groupId = this.$route.query.groupId;
      
      const param = {
        groupId,
        skuId: this.formModel.skuId,
        pageNum: this.listQuery.page,
        pageSize: this.listQuery.pageSize
      };
      
      try {
        const res = await skuPage(param);
        if (res.code === 0) {
          console.log("sku查询结果:", res.data);
        } else {
          this.$message({
            message: res.message,
            type: 'error',
          });
        }
      } catch (e) {
        console.log(e);
      }
      this.tableLoading = false;
    },
    handleFormSubmit() {
      this.listQuery.page = 1;
      this.queryList();
    },
    handleFormReset() {
      Object.keys(this.formModel)
        .forEach(key => {
          this.formModel[key] = '';
        });
      this.listQuery.page = 1;
      this.queryList();
    },
    goControlledSalesGoods() {
      this.$router.push({ path: '/controlUserGroup' });
    },
    handleChangeExport(info) {
			this.changeExport = false;
			if (info === 'go') {
				const path = '/downloadList';
				window.openTab(path);
			}
		},
  }
};
</script>

<style scoped lang="scss">
.content {
  padding: 16px;
}
.col_bg{
  color: #4183d5
}
.blueText {
  color: #4183d5;
  cursor: pointer;
}
</style>