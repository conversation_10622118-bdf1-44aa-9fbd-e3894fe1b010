<template>
  <div>
    <el-button v-if="showBtn" size="small" type="primary" @click="openDialog('buy')">
      购买额度
    </el-button>

    <!-- 购买额度的弹框 -->
    <el-dialog
      title="购买额度"
      :visible.sync="dialogVisible"
      @close="dialogVisible = false"
      @closed="resetForm"
      width="40%"
      top="6vh"
      append-to-body
      :close-on-click-modal="false"
    >
      <div class="box">
        <div class="plainTextSty">
          <h3>平台对公账户</h3>
          <p><span>账户名称：</span>{{ publicAccountInfo.legalPersonName }}</p>
          <p><span>银行卡号：</span>{{ publicAccountInfo.bankCode }}</p>
          <p><span>开户银行：</span>{{ publicAccountInfo.bankName }}</p>
          <p><span>开户支行：</span>{{ publicAccountInfo.subBankName }}</p>

          <p style="color: red; font-size: 14px">由平台提供推广服务，统一收取8%的服务费。</p>
          <p style="color: red; font-size: 14px">
            举例：对公汇款100元，实际到账的营销服务额度为92元，开票金额为100元
          </p>
        </div>
        <div style="margin-top: 10px">
          <el-form label-width="110px" :rules="rules" ref="subForm" :model="subForm">
            <el-form-item label="充值金额" prop="rechargeAmount" key="rechargeAmount">
              <el-input
                placeholder="请输入"
                v-model="subForm.rechargeAmount"
                style="width: 300px"
                type="number"
                onkeyup="value=value.replace(/[^\d]/g,'')"
              ></el-input>
            </el-form-item>
            <el-form-item label="付款证明" prop="payProof" key="payProof">
              <el-upload
                v-model="subForm.payProof"
                action
                list-type="picture-card"
                :before-upload="beforeAvatarUpload"
                :http-request="uploadImg"
                :on-remove="handleRemove"
                :file-list="fileList"
                :on-exceed="() => this.$message.error('超出文件上传数量限制')"
                multiple
                :limit="5"
              >
                <i class="el-icon-plus"></i>
              </el-upload>
            </el-form-item>
            <el-form-item label="是否需要开票" prop="invoice" key="invoice">
              <el-radio-group v-model="subForm.invoice">
                <el-radio :label="1">是</el-radio>
                <el-radio :label="2">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-form>
        </div>
        <div class="textSty">
          <p>温馨提示:</p>
          <p>1、向平台约定的对公账户打款</p>
          <p>2、打款完成后请上传付款证明，支持jpeg、jpg、png、gif格式</p>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <!-- <el-button @click="$emit('update:dialogVisible')">取 消</el-button> -->
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitForm()">确定 </el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { getPublicAccountInfo, checkMarketingService, rechargeMarketingServiceQuota, updateMarketingServiceQuotaFundAudit } from '@/api/settlement/marketingServiceQuota/index'
import { uploadFile } from '@/api/qual/index'
import { getHostName } from '@/api/storeManagement/index'

export default {
  props: {
    showBtn: {
      type: Boolean,
      default: false
    },
    // formData: '',
  },
  // computed: {
  //   dialogVisible: {
  //     // getter
  //     get() {
  //       return this.dialogVisible1
  //     },
  //     // setter
  //     set(newValue) {
  //       // 注意：我们这里使用的是解构赋值语法
  //       // this.$emit('update:dialogVisible')
  //     }
  //   }
  // },
  data() {
    return {
      dialogVisible: false,
      subForm: {
        rechargeAmount: null, // 充值金额
        payProof: [], // 付款证明
        invoice: null, // 是否开具发票
        voucher: null, // 开票信息
      },
      rules: {
        rechargeAmount: [{ required: true, message: '请输入充值金额', trigger: 'blur' }],
        payProof: [{ required: true, message: '请上传图片', trigger: 'change' }],
        invoice: [{ required: true, message: '请选择是否开具发票', trigger: 'change' }]
      },

      hostName: '',
      fileList: [],
      publicAccountInfo: {},
    }
  },
  created() {
    getHostName().then((res) => {
      if (res.hostName) {
        this.hostName = res.hostName
      }
    })
    getPublicAccountInfo().then((res) => {
      if(res.code == 0) {
        this.publicAccountInfo = res.data
      }
    })

  },
  methods: {
    openDialog(type, val) {
      // type = 'buy' | 'change'
      this.subForm.type = type
      if (type == 'buy') {
        checkMarketingService().then((res) => {
          if(res.code == 0) {
            if( res.data.failResult ) {
              this.$message.error(res.data.failResult)
              return
            }
            if(res.data.pass == 1) {
              this.subForm.voucher = res.data.voucher
              this.dialogVisible = true
            }
          }

        }) // 检查是否可以充值营销服务额度
      }
      if (type == 'change') {
        this.subForm.merchantFundAuditId = val.id
        if(val.paymentProof) {
          this.fileList = val.paymentProof.split(',').map((item) => {
            return {url: item}
          })
          this.subForm.payProof = this.fileList
        }
        this.subForm.rechargeAmount = val.amount
        this.subForm.invoice = val.isInvoice
        this.dialogVisible = true
      }
    }, // 打开新增弹框

    submitForm() {
      if (this.fileList.length == 0) {
        this.$message.warning('请上传付款证明')
        return false
      }
      if (this.subForm.rechargeAmount == 0) {
        this.$message.warning('充值金额必须为大于0的整数')
        return false
      }
      this.$refs.subForm.validate((valid) => {
        if (valid) {
          let subData = {
            amount: this.subForm.rechargeAmount,
            paymentProof: this.subForm.payProof.map((item) => item.url).join(','),
            isInvoice: this.subForm.invoice,
          }
          if(this.subForm.type == 'buy'){
            subData.voucher = this.subForm.voucher
            rechargeMarketingServiceQuota(subData).then((res) => {
              if (res.code == 0) {
                this.dialogVisible = false
                // this.$parent.loadData()
              } else {
                this.$message.error(res.message)
              }
            })
          } else if(this.subForm.type == 'change'){
            subData.merchantFundAuditId = this.subForm.merchantFundAuditId
            updateMarketingServiceQuotaFundAudit(subData).then((res) => {
              if (res.code == 0) {
                this.dialogVisible = false
                this.$parent.search()
              } else {
                this.$message.error(res.message)
              }
            })
          }
        } else {
          this.$message.error('请检查表单内容！')
        }
      })
    },
    resetForm() {
      this.$refs.subForm.resetFields()
      this.fileList = []
    },

    // 上传图片的逻辑
    uploadImg(file) {
      uploadFile(file).then((res) => {
        if (res.code === '200') {
          // this.form.viewImage = `${this.hostName}/${res.data}`;
          let temp = { name: res.data, url: `${this.hostName}${res.data}` }
          // this.fileList = [...this.fileList, temp];
          this.fileList.push(temp)
          this.subForm.payProof = this.fileList
          // console.log(res.data);
          console.log(this.fileList)
        } else {
          // this.form.viewImage = '';
          console.log('上传失败')
        }
      })
      return true
    },
    beforeAvatarUpload(file) {
      console.log(file)

      if (!file) {
        this.$message.error('请上传图片')
        return false
      }
      const isJPG =
        file.type === 'image/jpeg' ||
        file.type === 'image/png' ||
        file.type === 'image/bmp' ||
        file.type === 'image/jpg' ||
        file.type === 'image/gif'
      const isLt5M = file.size / 1024 / 1024 < 5

      if (!isJPG || !isLt5M) {
        this.$message.error('图片不满足上传要求，请重新上传')
        return false
      }
      return isJPG && isLt5M
    },
    handleRemove(file, fileList) {
      this.fileList = fileList
      this.subForm.payProof = this.fileList
      console.log(file, fileList, this.fileList)
    }
  }
}
</script>

<style scoped lang="scss">
.box {
  margin-top: -20px;
  width: 100%;
  display: flex;
  flex-direction: column;
  // justify-content: center;
  // align-items: center;
  padding: 0 10px;
}
.plainTextSty {
  width: 100%;
  // height: 100px;
  padding: 0 10px;
  margin: -20px 0 20px 0;
  background-color: #fafafa;
}
.plainTextSty span {
  width: 30px;
}
.textSty {
  color: #575757;
}
/* 推荐，实现简单 */
::v-deep   .el-upload-list__item.is-ready,
::v-deep   .el-upload-list__item.is-uploading {
  display: none !important;
}
</style>
