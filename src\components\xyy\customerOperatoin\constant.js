/**
 * created by <PERSON><PERSON><PERSON><PERSON><PERSON> in 2021/3/13
 */
// 客户弹窗
export const customerDialogType = {
  viewDetail: 1, //  查看详情
  viewExport: 2, // 查看导入
};

// 指定 ID 选人
export const merchantJoinTypes = {
  include: 1,
  exclude: 2,
};

// 创建人群、复制人群
export const createOrCopyTypes = {
  create: 1,
  copy: 2,
};

export const customerListUrl = new Map([
  [customerDialogType.viewDetail, '/insight/merchant/list'],
  [customerDialogType.viewExport, '/insight/importMerchant/list'],
]);

export const downloadTemplateUrls = new Map([
  [
    merchantJoinTypes.include,
    'https://upload.ybm100.com/ybm/shop/bc231798-4b8a-4450-a8df-86bdee1effd2.xlsx',
  ],
  [
    merchantJoinTypes.exclude,
    'https://upload.ybm100.com/ybm/shop/bc231798-4b8a-4450-a8df-86bdee1effd2.xlsx',
  ],
]);


// eslint-disable-next-line global-require
export const emptyImg = require('../../../assets/image/marketing/noneImg.png');
