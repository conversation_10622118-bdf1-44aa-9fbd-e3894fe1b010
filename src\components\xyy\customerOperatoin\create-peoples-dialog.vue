<!--created by <PERSON><PERSON><PERSON><PERSON><PERSON> in 2021/3/12
创建人群弹窗
-->
<template>
  <el-dialog
    v-el-drag-dialog
    :title="title"
    :visible="visible"
    :destroy-on-close="true"
    :append-to-body="appendToBody"
    :before-close="handleDialogClose"
    @open="open"
  >
    <el-form
      ref="form"
      :rules="rules"
      :model="formData"
    >
      <part-title title="人群命名" />
      <el-form-item prop="groupName">
        <el-input
          v-model="formData.groupName"
          clearable
          size="small"
          placeholder="请输入人群名称"
          style="width: 400px;"
        />
      </el-form-item>
      <part-title title="选择人群" />
      <div class="tip-content">
        <p
          v-for="item in createCrowdTips"
          :key="item"
          class="tip-item"
          v-html="item"
        />
      </div>
      <div class="edit-container" v-if="formData.merchantJoinType !== merchantJoinTypes.include">
        <span class="edit-label" style="margin-bottom: 10px">标签选人：</span>
        <!-- <el-checkbox
          :value="areaSelectStatus"
          @change="areaSelectChange"
        >
          地域
        </el-checkbox>
        <el-checkbox
          :value="merChantSelectStatus"
          @change="merChantSelectChange"
        >
          客户类型
        </el-checkbox>
        <el-checkbox
          v-model="formData.isNewMan"
          @change="handleTargetBoolean"
          >
          店铺新人
        </el-checkbox> -->
      </div>
      <!-- v-show="areaSelectStatus && formData.merchantJoinType !== merchantJoinTypes.include" -->
      <div
        v-show="formData.merchantJoinType !== merchantJoinTypes.include"
        class="check-container"
      >
        <span style="display: block;marginBottom: 10px;fontWeight: 600">区域:</span>
        <xyy-city-tree
          v-model="selectAreaCode"
          :show-checkbox="true"
          :default-expanded-keys="defaultExpandedKeys"
          node-key="areaCode"
          :props="cityTreeProps"
          :lazy="true"
          :load="loadTree"
          :half-selected-keys="halfSelectCodeArray"
          @check="cityCheckChange"
        />
      </div>
      <!-- v-show="merChantSelectStatus && formData.merchantJoinType !== merchantJoinTypes.include" -->
      <div
        v-show="formData.merchantJoinType !== merchantJoinTypes.include"
        class="check-container"
      >
        <span style="display: block;marginBottom: 10px;fontWeight: 600">客户类型:</span>
        <xyy-checkbox-group
          v-model="formData.merchantTypes"
          :data-array="provinceAndMerchantType.allMerchantTypes"
          name-prop="value"
          key-prop="id"
        />
      </div>
      <div
        v-show="formData.merchantJoinType !== merchantJoinTypes.include"
        class="check-container"
      >
        <span style="display: block;marginBottom: 10px;fontWeight: 600">
          用户标签:
          <span style="color: red;margin-left: 15px">同时勾选多个标签，则用户范围取并集</span>
        </span>
        <el-checkbox
          v-model="formData.isNewMan"
          @change="handleTargetBoolean"
          >
          店铺新人
          <el-tooltip class="item" effect="dark">
           <template #content>
            <span style="display: block;">未在店铺产生有效订单的客户</span>
            <span style="display: block;">1、有效订单的状态包含：待付款、待审核、正在开单、分拣中、待配送、配送中、已完成</span>
            <span style="display: block;">2、若订单取消、整单退款则恢复新人身份</span>
           </template>
            <i class="el-icon-warning-outline"></i>
          </el-tooltip>
        </el-checkbox>
         <!-- 接口：/insight/default/areaAndMerchatType -->
        <!-- provinceAndMerchantType.userLabels -->
        <el-checkbox-group v-model="formData.tagIds" class="check_left" style="display: inline-block">
          <el-checkbox
            v-for="item in provinceAndMerchantType.userLabels"
            :key="item.id"
            :label="item.id"
          >
          {{ item.name }}
          <el-tooltip effect="dark" v-if="item.name == 'VIP活跃用户(月)'">
           <template #content>平台前20%高频采购用户</template>
            <i class="el-icon-warning-outline"></i>
          </el-tooltip>
          </el-checkbox>
        </el-checkbox-group>
      </div>
      <div class="edit-container">
        <span
          class="edit-label"
          style="margin-top: 10px;"
        >指定ID选人：</span>
        <customer-selector-container
          ref="customerSelector"
          :merchant-join-type.sync="formData.merchantJoinType"
          :temp-set-code.sync="formData.merchantTag"
          :customer-num.sync="customerNum"
          :show-customer-num.sync="showCustomerNum"
          @clear="handleClearArray"
        />
      </div>
    </el-form>

    <span slot="footer">
      <el-button
        size="medium"
        @click="cancel"
      >取消</el-button>
      <el-button
        size="medium"
        style="margin-left: 20px;"
        type="primary"
        :loading="createLoading"
        @click="submit"
      >确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import elDragDialog from '@/components/directive/el-dragDialog';
import XyyCityTree from '@/components/xyy/xyy-city-tree/index';
import XyyCheckboxGroup from '@/components/xyy/xyy-checkbox-group/index';
import PartTitle from '@/components/part-title';
import { createCrowd, getProvinceRegions } from './fetch/fetch';
import CustomerSelectorContainer from './customer-selector-container';
import { merchantJoinTypes, createOrCopyTypes } from './constant';
import {actionTracking} from "@/track/eventTracking";

const DEFAULT_SELECT_ALL_CODE = -9999;
// 拖拽指令
export default {
  name: 'CreatePeoplesDialog',
  components: {
    XyyCheckboxGroup,
    XyyCityTree,
    CustomerSelectorContainer,
    PartTitle,
  },
  directives: { elDragDialog },
  model: {
    prop: 'dialogVisible',
    event: 'onDialogChange',
  },
  props: {
    appendToBody: Boolean,
    createOrCopy: {
      type: Number,
      default: createOrCopyTypes.create,
    },
    title: {
      type: String,
      default() {
        return '创建人群';
      },
    },
    provinceAndMerchantType: {
      type: Object,
      default: undefined,
    },
    groupData: {
      // eslint-disable-next-line no-bitwise,vue/require-prop-type-constructor
      type: Object,
      default() {
        return undefined;
      },
    },
    dialogVisible: {
      type: Boolean,
      default() {
        return false;
      },
    },
  },
  data() {
    return {
      rules: {
        groupName: [
          { required: true, message: '请输入人群名称', trigger: 'blur' },
        ],
      },
      visible: false,
      showCustomerDialog: false,
      formData: {
        conditionAreas: [],
        groupName: '',
        merchantJoinType: 0,
        merchantTag: '',
        merchantTypes: [],
        isNewMan: false,
        tagIds:[]
      },
      merchantJoinTypes,
      customerNum: 0, // 客户数量
      showCustomerNum: false,
      areaSelectStatus: false,
      merChantSelectStatus: false,
      provinceData: [],
      defaultExpandedKeys: [DEFAULT_SELECT_ALL_CODE],
      cityTreeProps: {
        label: 'areaName',
        isLeaf: (data, node) => node.level === 4,
      },
      selectAreaCode: [],
      halfSelectCodeArray: [],
      createLoading: false,
      createCrowdTips: [
        '<span>1、地域、客户类型、用户标签之间取交集，同类型之间取并集。示例：区域选择“湖北”，客户类型选择“单体”，用户标签选择“店铺新人”，则圈定的客户范围为湖北省单体客户且店铺新人可享受活动；</span>',
        '<span>2、指定客户参与：仅指定的客户可享受活动；</span>',
        '<span>3、标签和指定客户排除：则标签范围内将黑名单客户剔除；</span>',
      ],
      vipStatus:false,
      peopleStatus:false
    };
  },
  mounted() {
    this.$nextTick(() => {
      // 复制
      if (this.createOrCopy === createOrCopyTypes.copy) {
        this.initDataFromDetail();
      }
      this.parseProvinceAndMerchantTypes();
      this.visible = true;
    });
  },
  methods: {
    handleTargetBoolean(val){
      this.peopleStatus = val
    },
    handleClearArray(){
      this.formData.conditionAreas = []
      this.formData.merchantTypes = []
      this.formData.tagIds = []
    },
    initDataFromDetail() {
      this.formData.isNewMan = this.groupData.isNewMan === 1;
      this.formData.groupName = this.groupData.groupName;
      this.formData.merchantJoinType = this.groupData.merchantJoinType;
      this.formData.merchantTag = this.groupData.tempSetCode;
      this.formData.tagIds = this.groupData.tagIds
      this.formData.merchantTypes = this.groupData.merchantTypes
        ? this.groupData.merchantTypes.map(item => String(item))
        : [];
      this.initAreaDataFromDetail();
      this.customerNum = this.groupData.merchantIdSize;
      this.showCustomerNum = true;
      this.areaSelectStatus = this.formData.conditionAreas.length > 0;
      this.merChantSelectStatus = this.formData.merchantTypes.length > 0;
    },
    initAreaDataFromDetail() {
      if (
        this.groupData.conditionAreas
        && this.groupData.conditionAreas.length > 0
      ) {
        const halfSelectCodeArraySet = new Set();
        const selectArraySet = new Set();
        this.groupData.conditionAreas.forEach((item) => {
          selectArraySet.add(item.areaCode);
          const { areaCode, areaLevel } = item;
          this.formData.conditionAreas.push({ areaCode, areaLevel });
          let { parent } = item;
          while (parent) {
            halfSelectCodeArraySet.add(parent.areaCode);
            parent = parent.parent;
          }
        });
        this.selectAreaCode = [...selectArraySet];
        halfSelectCodeArraySet.add(DEFAULT_SELECT_ALL_CODE);
        this.halfSelectCodeArray = [...halfSelectCodeArraySet];
      }
    },
    cityCheckChange(node) {
      this.formData.conditionAreas = [];
      // 如果选中的是全选，则收集所有省份
      if (node.length === 1 && node[0].areaCode === DEFAULT_SELECT_ALL_CODE) {
        const selectAreaCode = [];
        const conditionAreas = [];
        this.provinceData.forEach((item) => {
          selectAreaCode.push(item.areaCode);
          conditionAreas.push({
            areaCode: item.areaCode,
            areaName: item.areaName,
            areaLevel: item.level,
          });
        });
        this.selectAreaCode = selectAreaCode;
        this.formData.conditionAreas = conditionAreas;
      } else {
        node.forEach((item) => {
          if (this.selectAreaCode.indexOf(item.areaCode) >= 0) {
            this.formData.conditionAreas.push({
              areaCode: item.areaCode,
              areaName: item.areaName,
              level: item.level,
            });
          }
        });
      }
    },
    // eslint-disable-next-line consistent-return
    loadTree(node, resolve) {
      if (node.level === 0) {
        return resolve([
          {
            areaName: '全选',
            areaCode: DEFAULT_SELECT_ALL_CODE,
          },
        ]);
      }
      if (node.level === 1) {
        resolve([...this.provinceData]);
      } else if (node.level === 2) {
        const { areaCode } = node.data;
        getProvinceRegions({ provinceCode: areaCode })
          .then((res) => {
            if (res.code === 1000) {
              const array = res.data;
              node.data.childRegion = array;
              resolve([...array]);
            } else {
              resolve([]);
              node.loaded = false;
            }
          })
          .catch((error) => {
            console.log(error);
            resolve([]);
          });
      } else if (node.level === 3) {
        resolve([...node.data.childRegion]);
      } else {
        resolve([]);
      }
    },
    parseProvinceAndMerchantTypes() {
      if (!this.provinceAndMerchantType.region) {
        return;
      }
      if (this.provinceAndMerchantType.region.length === 0) {
        return;
      }
      this.provinceData = this.provinceAndMerchantType.region.map(item => ({
        areaCode: item.areaCode,
        areaName: item.areaName,
        level: item.level,
      }));
    },
    areaSelectChange(newVal) {
      this.areaSelectStatus = newVal;
      if (!newVal) {
        this.formData.conditionAreas = [];
        this.selectAreaCode = [];
      }
    },
    merChantSelectChange(newVal) {
      this.merChantSelectStatus = newVal;
      if (!newVal) {
        this.formData.merchantTypes = [];
      }
    },
    open() {},
    cancel() {
      this.handleDialogClose();
    },
    submit() {
      actionTracking('create_crowd_source_click', {
        create_crowd_source : { 1 : 'manual_creation',
                                2 : 'copy_creation' }[this.createOrCopy]
      })
      const hasCustomer = this.$refs.customerSelector.validate();
      this.$refs.form.validate((valid) => {
        if (valid && hasCustomer) {
          this.createPeopleGroup();
          return true;
        }
        return false;
      });
    },
    createPeopleGroup() {
      const {merchantTypes,merchantTag,tagIds,conditionAreas} = this.formData
      if(merchantTypes.length === 0 && tagIds.length === 0 && conditionAreas.length === 0 && !this.peopleStatus
        && merchantTag === ''
      ){
        return this.$message.warning('标签及指定ID选人不能同时为空');
      }
      if (this.formData.isNewMan) {
        this.formData.isNewMan = 1;
      } else {
        this.formData.isNewMan = 2;
      }
      this.createLoading = true;
      createCrowd(this.formData)
        .then((res) => {
          if (res.code === 1000) {
            this.$message({
              message: '设置成功',
              type: 'success',
            });
            this.confirm();
          } else {
            this.$message({
              message: res.msg,
              type: 'error',
            });
          }
        })
        .catch((error) => {
          console.log(error);
          this.$message({
            message: '提交失败',
            type: 'error',
          });
        })
        .finally(() => {
          this.createLoading = false;
        });
    },
    confirm() {
      this.$emit('confirm');
      this.handleDialogClose();
    },
    handleDialogClose() {
      this.visible = false;
      this.$emit('onDialogChange', false);
    },
  },
};
</script>
<style lang="scss" scoped>
.tip-content {
  background-color: #f9fafc;
  padding: 10px;
  width: fit-content;
  .tip-item {
    color: #333333;
    ::v-deep   span {
      color: #f56c6c;
    }
  }
}

.edit-container {
  margin-left: 20px;
  margin-top: 20px;
  display: flex;

  .edit-label {
    font-size: 14px;
    font-family: PingFangSC-Regular, PingFang SC, sans-serif;
    font-weight: 400;
    color: #292933;
    line-height: 14px;
    width: 100px;
    text-align: right;
  }
}

.check-container {
  max-height: 200px;
  min-height: 100px;
  overflow-y: auto;
  border: 1px solid rgba(208, 208, 208, 1);
  margin: -1px 0 0 60px;
  padding: 10px;
}
.check_left{
  margin-left: 20px;
}
::v-deep   .el-dialog {
  border-radius: 5px;
  overflow: hidden;
}
::v-deep   .el-dialog__header span {
  font-size: 14px;
  line-height: normal;
}
::v-deep   .el-dialog__header button {
  right: 15px;
  top: 15px;
}
::v-deep   .el-dialog__body {
  max-height: 70vh;
  overflow: auto;
  padding: 5px 15px;
  border-bottom: solid 1px #e4eaf1;
}
::v-deep   .el-dialog__footer {
  padding: 15px;
}
</style>
