<template>
<div>
    <CommonHeader :showFold="false" :shouHeightLine="false">
      <div v-loading="loading">
        <el-form>
          <el-row>
						<el-col :xs="16" :md="12" :lg="6" :xl="6">
							<el-form-item label="通用名称：">
                <span>{{ data.commonName }}</span>
              </el-form-item>
						</el-col>
            <el-col :xs="16" :md="12" :lg="6" :xl="6">
							<el-form-item label="商品名称：">
                <span>{{ data.commonName }}</span>
              </el-form-item>
						</el-col>
            <el-col v-if="from == 1" :xs="16" :md="12" :lg="6" :xl="6">
							<el-form-item label="批号：">
                <span>{{ data.batchCode }}</span>
              </el-form-item>
						</el-col>
            <el-col :xs="16" :md="12" :lg="6" :xl="6">
							<el-form-item label="规格：">
                <span>{{ data.spec }}</span>
              </el-form-item>
						</el-col>
            <el-col :xs="16" :md="12" :lg="6" :xl="6">
							<el-form-item label="批准文号：">
                <span>{{ data.approvalNumber }}</span>
              </el-form-item>
						</el-col>
            <el-col :xs="16" :md="12" :lg="6" :xl="6">
							<el-form-item label="生产厂家：">
                <span>{{ data.manufacturer }}</span>
              </el-form-item>
						</el-col>
            <el-col :xs="16" :md="12" :lg="6" :xl="6">
							<el-form-item label="商品编码：">
                <span>{{ data.barcode }}</span>
              </el-form-item>
						</el-col>
            <el-col :xs="16" :md="12" :lg="6" :xl="6">
							<el-form-item label="ERP编码：">
                <span>{{ data.erpCode }}</span>
              </el-form-item>
						</el-col>
            <el-col :xs="16" :md="12" :lg="6" :xl="6">
							<el-form-item label="CSUID：">
                <span>{{ data.csuid }}</span>
              </el-form-item>
						</el-col>
          </el-row>
        </el-form>
      </div>
      <div style="border: dotted 1px #2e2e2e51;padding: 20px;">
        <p>
          {{ `您${status ? '已开通电子签章资格，所有上传的图片，我们都会为您加盖由国家指定机构为您颁发的电子签章。' : '未开通电子签章资格，系统支持上传文件但无法对上传的文件加盖电子签章。' }` }}
        </p>
        <p>
          图片请以
          <span style="color:red;">商品编码+英文下划线+资质名称(如:123456_说明书)</span>
          ，我们自动帮您通过图片命令摘取资质关键字，您也可以核实修正，方便药店客户选择下载。
        </p>
        <p>
          您可以点击【添加】选择进入文件夹，按住 CTRL+A全选图片，统一上传。
        </p>
      </div>
      <p></p>
      <div class="content">
        <p>
          <span>{{ from == 0 ? '商品首营资质' : '药检报告' }}图片：最多支持上传100张图片，每张图片大小为10K - 15MB，且建议长宽比例参考A4纸（21cm * 29.7cm）。</span>
          <span style="color: red;">请优先上传PDF</span>
          <span>，支持PDF和图片格式，包括jpg、png、jpeg格式。</span>
        </p>
        <div style="display: flex;gap: 200px;">
          <div style="flex-shrink: 0;">
            <iFile :multiple="true" :allowType="['.png', '.jpg', '.pdf','.jpeg']" :maxCount="maxCount" v-model="fileList" :bucket="3">
              <!-- <span>请优先上传PDF，支持pdf和图片格式，包括jpg、png、jpeg格式</span> -->
            </iFile>
          </div>
          <div style="flex-grow: 1;overflow-x: auto;">
            <div style="min-width: 700px;">
              <div style="margin-bottom: 10px;background-color: white; padding: 10px;">
                <el-button size="small" type="primary" @click="fileList = []">全部删除</el-button>
              </div>
              <i-img :maxCount="maxCount" :isEdit="true" v-model="fileList" :column="3"></i-img>
            </div>
          </div>
        </div>
      </div>
      <p></p>
      <div class="content" style="display: flex;justify-content: center;">
        <el-button size="small" @click="close">取消</el-button>
        <el-button type="primary" size="small" @click="submit">确定</el-button>
      </div>
    </CommonHeader>
  </div>
</template>
<script>
import CommonHeader from '../afterSaleManager/components/common-header.vue'
import iFile from './components/i-file.vue'
import iImg from './components/i-img.vue'
import { qualificationSearch, platformServiceAgreementGetSealStatus, popFirstSaleQualificationAddFile, reportBatchUpload, drugReportQuery } from '../../api/qualificationOnline/index'
export default {
  name: '',
  components: {
    CommonHeader,
    iFile,
    iImg
  },
  data() {
    return {
      loading: false,
      barcode: '',
      batchCode: '',
      from: 0,   //0:首营  1:药检
      data: {
        erpCode: "",
        barcode: "",
        csuid: "",
        productName: "",
        commonName: "",
        spec: "",
        batchCode: "",
        approvalNumber: "",
        manufacturer: "",
        applyDownloadCount: 0,
        updateTime: "",
        annexList: [],
        sealAnnexList: [],
      },
      fileList: [],
      status: false,
      maxCount: 100
    }
  },
  activated() {
    this.from = this.$route.query.from;
    const params = {
      sealType: this.from == 1 ? 0 : 2
    }
    platformServiceAgreementGetSealStatus().then(res => {
      if (res.code === 0) {
        this.status = res.result.status;
      }
    })
    if (this.$route.query.barcode !== this.barcode || this.$route.query.batchCode !== this.batchCode) {
      this.from == 0 ? this.first() : this.drug();
    }
  },
  created() {
    window.clearData['/qualificationUploadEdit'] = () => {
      this.clear();
    }
  },
  methods: {
    first() {
      this.loading = true;
      qualificationSearch({
        code: this.$route.query.barcode,
        pageNum: 1,
        pageSize: 20
      }).then(res => {
        if (res.code == 0) {
          this.data = res.result.list[0];
          this.fileList = res.result.list[0].annexList;
          this.barcode = this.$route.query.barcode
        } else {
          this.$message.error('查询失败');
        }
      }).finally(() => {
        this.loading = false;
      })
    },
    drug() {
      drugReportQuery({
        barcode: this.$route.query.barcode,
        batchCode: this.$route.query.batchCode,
        pageNum: 1,
        pageSize: 20
      }).then(res => {
        if (res.code == 0) {
          this.data = res.result.list[0];
          const list = res.result.list[0].annexList ? res.result.list[0].annexList : []
          this.fileList = list;
          this.barcode = this.$route.query.barcode;
          this.batchCode = this.$route.query.batchCode;
        } else {
          this.$message.error('查询失败');
        }
      })
    },
    close() {
      const from = this.from
      window.closeTab(this.$route.fullPath, true);
      window.openTab(from == 0 ? '/qualificationManage' : '/drugTestResultManage');
    },
    submit() {
      if (this.loading) return
      this.loading = true;
      const req = this.from == 0 ? popFirstSaleQualificationAddFile : reportBatchUpload
      let params = {
        barCode: this.data.barcode,
        erpCode: this.data.erpCode,
        sourceImageUrl: this.fileList
      };
      params = this.from == 0 ? params : {
        ...params,
        batchCode: this.batchCode
      }
      req(params).then(res => {
        if (res.code == 0) {
          this.$message.success('上传成功')
          this.close()
        } else {
          this.$message.error(res.message)
        }
      }).finally(() => {
        this.loading = false;
      })
    },
    clear() {
      this.barcode = '';
      this.from = 0;
      this.data = {
        erpCode: "",
        barcode: "",
        csuid: "",
        productName: "",
        commonName: "",
        spec: "",
        batchCode: "",
        approvalNumber: "",
        manufacturer: "",
        applyDownloadCount: 0,
        updateTime: "",
      }
      this.fileList = [];
    }
  }
}
</script>
<style scoped>
.content {
  padding: 15px;
  background-color: rgb(248, 248, 248);
  border-radius: 5px;
}
</style>
