<template>
  <div class="explain-question">
    <el-popover
      :title="titleInfo"
      placement="bottom"
      width="472"
      trigger="hover"
      effect="light"
      :popper-class="['m-pop', popClassList].join(' ')"
    >
      <div v-for="(item,index) in info" :key="index">
        <div >
          <div class="radius"></div>
          <header>{{ item.title }}</header>
        </div>
        <div v-if="item.mark" class="line"></div>
        <p>{{ item.info }}</p>
      </div>
      <div slot="reference">
          <img class="svg-icon" src="@/assets/image/control/icon_control_info.png" />
      </div>
    </el-popover>
  </div>
</template>

<script>
export default {
  name: 'Info',
  props: {
    info: {
      type: Array,
      default: function() {
        return [];
      }
    },
    titleInfo:{
      type: String,
      default:'标题'
    },
    popClassList: {
      type: String
    }
  }
};
</script>

<style scoped>
.explain-question{
  display: inline-block;
}
.line {
  border-top: 1px dashed #dedee5;
  height: 1px;
  margin-bottom: 12px;
}
.svg-icon {
  width: 1em;
  height: 1em;
  vertical-align: -0.1em;
  fill: currentColor;
  stroke: currentColor;
  overflow: hidden;
  margin-left: 8px;

}
header{
  font-size: 14px;
  color: rgba(51,51,51,0.85);
}
p{
  font-size: 12px;
  color: #999999;
}
.radius{
  float: left;
  margin: 5px;
  width: 5px;
  height: 5px;
  opacity: 1;
  background: #d8d8d8;
  border-radius: 50%;
}
</style>
