<template>
  <div>
    <noticeTop5/>
    <ServiceQualitySeven2 v-if="isGrayUser" />
    <ServiceQualitySeven1 v-else />
    <merchantCourse v-if="isGrayUser" />
    <ybmContact v-if="isGrayUser"/>
    <StoreDataToday v-if="!isGrayUser"/>
  </div>
</template>

<script>
import StoreDataToday from './StoreDataToday'
import ServiceQualitySeven2 from './serviceQualitySeven2'
import ServiceQualitySeven1 from './serviceQualitySeven1'
import noticeTop5 from './noticeTop5'
import merchantCourse from './merchantCourse.vue'
import ybmContact from './ybmContact.vue'
import {actionTracking} from "@/track/eventTracking";
 import {mapState} from 'vuex'
export default {
  name: "RightContent",
  computed:{    ...mapState({ isGrayUser: state => state.app.isGrayUser }),},
  components: {StoreDataToday ,ServiceQualitySeven2,ServiceQualitySeven1,noticeTop5,merchantCourse,ybmContact},
}
</script>

<style scoped>

</style>
