<!-- 保证金管理 -->
<template>
  <div>
    <!-- 头部查询组件 -->
    <margin-header @search="search"></margin-header>

    <!-- 表格 -->
    <margin-table ref="marginTable"></margin-table>
  </div>
</template>

<script>
import marginHeader from './components/marginHeader.vue'
import marginTable from './components/marginTable.vue'

export default {
  // provide() {
  //   return {
  //     accountData: this.accountData
  //   }
  // }, // 共享数据给rechargeDialog和recordDialog
  name: 'marginAccount',
  components: {
    marginHeader,
    marginTable
  },
  mounted() {
    // 调用接口获取当前账户数据，比如账户号码
  },
  data() {
    return {
      // accountData: '333' // 账户数据，进入页面时获取
    }
  },
  methods: {
    search(val) {
      this.$refs.marginTable.search(val)
    }, // 查询逻辑
    setChangeType(val) {
      this.$refs.marginTable.setChangeType(val)
    },
  }
}
</script>

<style lang="scss" scoped>
.tableSty {
  width: 98%;
  //   border: 1px solid red;
  margin: 0 auto;
}
.selectSty {
  display: flex;
  flex-wrap: wrap;
}

</style>
