<template>
  <div></div>
</template>

<script>
import {mapMutations} from 'vuex'

export default {
  name: "refresh",
  created() {
    const {fromPath, fromName} = this.$route.query
    if (fromPath) {
      this.$router.replace({path: fromPath}).then(() => {
        this.DEL_EXCLUDELIST(fromName)
      })
    }
  },
  methods: {
    ...mapMutations('permission', ['DEL_EXCLUDELIST'])
  }
}
</script>

<style scoped>

</style>
