<template>
  <div class="home">
    <div class="serch">
      <el-row
        type="flex"
        align="middle"
      >
        <span class="sign" />
        <div>查询条件</div>
      </el-row>
    </div>
    <el-row
      class="condition searchMy"
      style="padding:0 20px;"
    >
      <el-form
        ref="ruleForm"
        :inline="true"
        :model="ruleForm"
        :rules="rules"
        size="small"
      >
        <el-form-item
          prop="couponName"
        >
          <el-input
            v-model="ruleForm.couponName"
          >
            <template slot="prepend">
              优惠券名称
            </template>
          </el-input>
        </el-form-item>
        <el-form-item
          prop="couponId"
        >
          <el-input
            v-model="ruleForm.couponId"
          >
            <template slot="prepend">
              优惠券ID
            </template>
          </el-input>
        </el-form-item>
        <el-form-item
          prop="couponActivityName"
        >
          <el-input
            v-model="ruleForm.couponActivityName"
          >
            <template slot="prepend">
              券活动名称
            </template>
          </el-input>
        </el-form-item>
        <el-form-item
          prop="couponActivityId"
        >
          <el-input
            v-model="ruleForm.couponActivityId"
          >
            <template slot="prepend">
              券活动ID
            </template>
          </el-input>
        </el-form-item>
        <el-form-item
          prop="joinType"
        >
           <span
               class="search-title"
           >券活动类型</span>
          <el-select
            v-model="ruleForm.joinType"
            placeholder="请选择券活动类型"
          >
            <el-option
              label="全部"
              :value="null"
            />
            <el-option
              label="领券活动"
              value="1"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          prop="name"
        >
          <el-input
            v-model="ruleForm.name"
          >
            <template slot="prepend">
              客户名称
            </template>
          </el-input>
        </el-form-item>
        <el-form-item>
          <el-input
            v-model="ruleForm.id"
          >
            <template slot="prepend">
              客户手机号
            </template>
          </el-input>
        </el-form-item>
        <el-form-item
          prop="couponInstanceId"
        >
          <el-input
            v-model="ruleForm.couponInstanceId"
          >
            <template slot="prepend">
              优惠券子券ID
            </template>
          </el-input>
        </el-form-item>
        <el-form-item
          prop="logicalStatus"
        >
          <span
            class="search-title"
          >优惠券子券状态</span>
          <el-select
            v-model="ruleForm.logicalStatus"
            placeholder="请选择子券状态"
          >
            <el-option
              label="全部"
              :value="null"
            />
            <el-option
              label="未使用"
              value="0"
            />
            <el-option
              label="已使用"
              value="2"
            />
            <el-option
              label="已过期"
              value="1"
            />
            <el-option
              label="已召回"
              value="6"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-input v-model="ruleForm.orderNo">
            <template slot="prepend">
              订单号
            </template>
          </el-input>
        </el-form-item>
        <el-form-item style="width: 30%">
          <span class="search-title">
            发/领券时间
          </span>
          <el-date-picker
            v-model.trim="ruleForm.time"
            style="line-height: 30px; height: 30px;"
            :picker-options="pickerOptions"
            range-separator="至"
            size="small"
            format="yyyy-MM-dd"
            value-format="timestamp"
            placeholder="选择日期时间"
            type="daterange"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          />
        </el-form-item>
        <el-form-item class="searchBtn" style="text-align: right;padding-right: 20px">
          <el-button
            type="primary"
            @click="submitForm('ruleForm')"
          >
            查询
          </el-button>
          <el-button @click="resetForm('ruleForm')">
            重置
          </el-button>
          <el-button v-permission="['marketing_couponData_export']" @click="exportExcel">
            导出
          </el-button>
        </el-form-item>
      </el-form>
    </el-row>
    <el-divider />
    <el-row class="actlist">
      <div class="serch">
        <el-row
          type="flex"
          align="middle"
        >
          <span class="sign" />
          <div>优惠券数据列表</div>
        </el-row>
      </div>
      <el-table
        v-loading="isLoading"
        :data="tableData"
        row-key="id"
        stripe
        border
        style="width: 100%"
        :header-cell-style="{ background: '#F9F9F9', color: '#666666' }"
      >
        <el-table-column
          prop="couponName"
          label="优惠券名称/ID"
          width="180"
        >
          <template slot-scope="scope">
            <div>
              <div>名称：{{ scope.row.couponName }}</div>
              <div>ID: {{ scope.row.couponId }}</div>
            </div>
          </template>
        </el-table-column>
        <el-table-column
          label="门槛&金额"
          width="180"
          prop="description"
        />
        <el-table-column
          prop="name"
          label="券活动名称/ID"
          width="150"
        >
          <template slot-scope="scope">
            <div v-if="scope.row.couponActivityName">
              <div>名称：{{ scope.row.couponActivityName }}</div>
              <div>ID: {{ scope.row.couponActivityId }}</div>
            </div>
          </template>
        </el-table-column>
        <el-table-column
          width="100"
          prop="joinType"
          label="券活动类型"
        >
          <template slot-scope="scope">
            <div>
              <div v-if="scope.row.joinType === 2">
                发券活动
              </div>
              <div v-if="scope.row.joinType === 1">
                领券活动
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column
          width="180"
          prop="merchantName"
          label="客户名称"
        />
        <el-table-column
          width="180"
          prop="mobile"
          label="客户手机号"
        />
        <el-table-column
          width="180"
          prop="couponUserId"
          label="优惠券子券ID"
        />
        <el-table-column
          width="180"
          prop="createTime"
          label="领券时间"
        >
          <template slot-scope="{row}">
            <div>{{ formatDate(row.createTime) }}</div>
          </template>
        </el-table-column>
        <el-table-column
          width="180"
          prop="endTime"
          label="券有效期"
        >
          <template slot-scope="{row}">
            <div>{{ Math.ceil((row.endTime - row.startTime) / 1000 / 3600 / 24) }}天</div>
          </template>
        </el-table-column>
        <el-table-column
          width="150"
          prop="statusDesc"
          label="优惠券子券状态"
        />
        <el-table-column
          width="180"
          prop="orderNo"
          label="用券订单号"
        />
        <el-table-column
          width="150"
          prop="totalAmount"
          label="订单商品总额"
        />
        <el-table-column
          width="150"
          prop="money"
          label="订单实付金额"
        />
        <el-table-column
          width="150"
          prop="voucherAomunt"
          label="券优惠金额"
        />
        <el-table-column
          width="180"
          prop="useTime"
          label="券使用时间"
        >
          <template slot-scope="{row}">
            <div>{{ formatDate(row.useTime) }}</div>
          </template>
        </el-table-column>

        <template slot="empty">
          <div class="noData">
            <p class="img-box">
              <img
                src="@/assets/image/marketing/noneImg.png"
                alt=""
              >
            </p>
            <p>暂无数据</p>
          </div>
        </template>
      </el-table>
      <div
        v-if="page.totalPage != 0"
        class="pagination-container"
      >
        <div class="pag-text">
          共 {{ page.totalCount }} 条数据，每页{{ 10 }}条，共{{
            Math.ceil(page.totalCount / 10)
          }}页
        </div>
        <el-pagination
          background
          :current-page.sync="page.pageNum"
          :page-sizes="[10]"
          prev-text="上一页"
          next-text="下一页"
          layout="sizes, prev, pager, next, jumper"
          :total="page.totalCount"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-row>
    <export-tip
      :change-export="changeExport"
      @handleExoprClose="handleExoprClose"
      @handleChangeExport="handleChangeExport"
    />
  </div>
</template>

<script>
import exportTip from '@/views/other/components/exportTip';
import { couUserList,userExport } from '@/api/activity/index';
import {actionTracking} from "@/track/eventTracking";

export default {
  name: 'CouponUserList',
  components:{exportTip},
  props: {
    couponId: {
      type: Number,
      default: '',
    },
  },
  filters: {
    handleTime(date) {
      return date
        ? new Date(date + 8 * 3600 * 1000)
          .toJSON()
          .substr(0, 19)
          .replace('T', ' ')
        : '';
    },
  },
  data() {
    return {
      changeExport:false,
      pickerOptions: {
        disabledDate(time) {
          // 获取选中时间
          // eslint-disable-next-line no-undef
          const { timeOptionRange } = thisVue;
          // 获取时间范围(30天的毫秒数)
          const secondNum = 29 * 24 * 60 * 60 * 1000;
          if (timeOptionRange) {
            // 如果有选中时间 设置超过选中时间后的30天
            return (
              time.getTime() > timeOptionRange.getTime() + secondNum || time.getTime() < timeOptionRange.getTime() - secondNum
            );
          }
        },
        onPick(maxDate) {
          // 当选中了第一个日期还没选第二个
          // 只选中一个的时候自动赋值给minDate，当选中第二个后组件自动匹配，将大小日期分别赋值给maxDate、minDate
          if (maxDate.minDate && !maxDate.maxDate) {
            // eslint-disable-next-line no-undef
            thisVue.timeOptionRange = maxDate.minDate;
          }
          // 如果有maxDate属性，表示2个日期都选择了，则重置timeOptionRange
          if (maxDate.maxDate) {
            // eslint-disable-next-line no-undef
            thisVue.timeOptionRange = null;
          }
        },
      },
      isLoading: false,
      ruleForm: {
        couponId: '',
        couponActivityName: '',
        couponActivityId: '',
        couponInstanceId: '',
        joinType: undefined,
        name: '',
        phone: '',
        couponName: '',
        id: '',
        logicalStatus: null,
        orderNo: '',
        time: [],
      },
      rules: {
        name: [],
        logicalStatus: [],
      },
      tableData: [],
      page: {
        pageNum: 1,
        pageSize: 10,
        totalPage: null,
        totalCount: null,
      },
    };
  },
  activated() {
    if(this.$store.state.permission.menuGray == 1) {
      if (this.couponId && this.couponId !== this.ruleForm.couponId) {
        this.ruleForm.couponId = this.couponId;
        this.searchTabaleData();
      }
    }else {
      if (this.$route.query.id && this.ruleForm.couponId && this.$route.query.id !== this.ruleForm.couponId) {
        this.ruleForm.couponId = this.$route.query.id;
        this.searchTabaleData();
      }
    }
  },
  created() {
    const id =this.$store.state.permission.menuGray == 1 ?  this.couponId : this.$route.query.id;
    if (id) {
      this.ruleForm.couponId = id;
    }
    this.ruleForm.time = [new Date().getTime() - 24 * 3600 * 1000 * 29, new Date().getTime()];
    this.searchTabaleData();
  },

  mounted() {
    window.thisVue = this;
    actionTracking('coupon_exposure', {
      time: new Date().getTime(),
      org_id : this.orgId
    })
  },

  methods: {
    handleExoprClose() {
      this.changeExport = false;
    },
    handleChangeExport(info) {
      this.changeExport = false;
      if (info === 'go') {
        const path = '/downloadList';
        window.openTab(path);
      }
    },
    tableRowClass({ rowIndex }) {
      if (rowIndex % 2 !== 1) {
        return '';
      }
      return 'success-row';
    },
    creatActive() {
      this.$router.push({ path: 'addActive' });
    },
    handleEdit(id) {
      this.$router.push({
        path: 'addActive',
        query: { id },
      });
    },
    submitForm() {
      this.page.pageNum = 1;
      this.searchTabaleData();
    },
    resetForm(formName) {
      this.ruleForm.couponName = '';
      this.ruleForm.id = '';
      this.ruleForm.orderNo = '';
      this.ruleForm.time = [new Date().getTime() - 24 * 3600 * 1000 * 29, new Date().getTime()];
      this.$refs[formName].resetFields();
      this.ruleForm.couponInstanceId = '';
      this.page.pageNum = 1;
      if(this.$store.state.permission.menuGray == 1) {
        this.ruleForm.couponId = "" 
      }
      this.searchTabaleData();
    },

    searchTabaleData() {
      this.isLoading = true;
      const queryData = {
        name: this.ruleForm.couponName,
        couponId: this.ruleForm.couponId,
        couponActivityName: this.ruleForm.couponActivityName,
        couponActivityId: this.ruleForm.couponActivityId,
        joinType: this.ruleForm.joinType,
        couponInstanceId: this.ruleForm.couponInstanceId,
        merchantPhone: this.ruleForm.id,
        // merchantId: this.ruleForm.couponId,
        merchantName: this.ruleForm.name,
        status: this.ruleForm.logicalStatus
          ? Number(this.ruleForm.logicalStatus)
          : null,
        pageNum: this.page.pageNum,
        pageSize: 10,
        orderNo: this.ruleForm.orderNo,
        searchStime: this.ruleForm.time[0],
        searchEtime: this.ruleForm.time[1],
      };
      couUserList(queryData)
        .then((res) => {
          this.isLoading = false;

          if (res.success) {
            const { list, totalPage, pageNo, totalCount } = res.data;
            this.tableData = list;
            this.page.pageNum = pageNo;
            if (totalPage) {
              this.page.totalPage = totalPage;
              this.page.totalCount = totalCount;
            } else {
              this.page.totalPage = 0;
            }
          } else {
            this.$message.error(res.msg);
          }
        })
        .catch((error) => {
          console.log(error);
        });
    },

    handleSizeChange(val) {
      console.log(`每页 ${val} 条`);
    },
    handleCurrentChange(val) {
      console.log(`当前页: ${val}`);
      this.page.pageNum = val;
      this.searchTabaleData();
    },
    async exportExcel() {
      actionTracking('coupon_export', {
        time: new Date().getTime(),
        org_id : this.orgId
      })

      const params = {
        name: this.ruleForm.couponName,
        couponId: this.ruleForm.couponId,
        couponActivityName: this.ruleForm.couponActivityName,
        couponActivityId: this.ruleForm.couponActivityId,
        joinType: this.ruleForm.joinType || '',
        couponInstanceId: this.ruleForm.couponInstanceId,
        merchantPhone: this.ruleForm.id,
        // merchantId: this.ruleForm.couponId,
        merchantName: this.ruleForm.name,
        status: this.ruleForm.logicalStatus ? Number(this.ruleForm.logicalStatus) : '',
        pageNum: this.page.pageNum,
        pageSize: 10,
        orderNo: this.ruleForm.orderNo,
        searchStime: this.ruleForm.time[0],
        searchEtime: this.ruleForm.time[1],
      };
      this.isLoading = true;
      userExport(params).then(res => {
      this.isLoading = false;
        if (res.code !== 0) {
            this.$message.error(res.message);
          }else{
            this.changeExport = true;
          }
      })
      return
      let list = []
      Object.keys(params)
        .forEach(key => {
          list.push( key + '=' + params[key]);
        });
      const a = document.createElement('a');
      a.href = process.env.VUE_APP_BASE_API + '/promo/coupon/user/export?' + list.join('&');
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
    }
  },
};
</script>
<!-- Add "scoped" attribute to limit CSS to this component only -->

<style lang="scss" scoped>
.searchMy ::v-deep  .el-form-item__label {
  margin-left: 20px;
  padding: 0;
}
.searchMy ::v-deep  .el-input__inner {
  border-radius: 0 4px 4px 0;
}
.searchMy ::v-deep  .el-date-editor{
  width: 100%;
}
.search-title {
  display: table-cell;
  padding: 0 10px;
  text-align: center;
  border: 1px solid #dcdfe6;
  height: 30px;
  line-height: 30px;
  vertical-align: middle;
  border-right: none;
  border-radius: 4px 0 0 4px;
  color: #333333;
  white-space: nowrap;
}
.searchMy ::v-deep  .el-select {
  display: table-cell;
  width: 100%;
}
.searchMy ::v-deep  .el-form-item__content{
  width: 100%;
}
.searchMy ::v-deep  .el-form-item--small.el-form-item{
  margin-bottom: 10px;
  width: 32%;
}
.searchMy ::v-deep  .el-form-item--small .el-form-item__content{
  line-height: 30px;
  width: 100%;
  display: flex;
}
.searchMy ::v-deep  .el-input-group__prepend {
  background: none;
  color: #333333;
  padding: 0 10px;
}
.Fsearch {
  padding: 0px 20px 10px;
  font-weight: 500;
  .searchMsg {
    font-weight: 700;
    width: 200px;
  }
  .sign {
    display: inline-table;
    width: 3px;
    height: 13px;
    background: linear-gradient(360deg, #1d69c4 1%, #8bbdfc 99%);
    border-radius: 2px;
    margin-right: 8px;
  }
}
.searchMy ::v-deep   .el-form-item--small.el-form-item{
  width: 24%;
}
.search-btn-custom {
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  width: 606px;
}
.serch {
  padding: 15px 20px;
  font-weight: 700;

  .sign {
    display: inline-table;
    width: 3px;
    height: 13px;
    background: linear-gradient(360deg, #1d69c4 1%, #8bbdfc 99%);
    border-radius: 2px;
    margin-right: 8px;
  }
}

.searchBtn {
  float: right;
}

::v-deep  .el-divider--horizontal {
  margin: 0px;
}

.creatActive {
  margin-bottom: 10px;
}

.actlist {
  padding: 0 20px;

  .serch {
    padding: 15px 0;
    font-weight: 700;
  }
}

.el-pagination {
  margin-top: 10px;
  text-align: right;
}
::v-deep  .pagination-container{
  margin: 15px 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
::v-deep  .el-pagination .el-pagination__total {
  float: left;
}
</style>
