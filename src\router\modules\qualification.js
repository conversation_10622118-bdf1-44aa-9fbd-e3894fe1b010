import layout from '../../layout/index.vue';

const qualificationRouter = {
  path: '/qualification',
  name: 'qualification',
  component: layout,
  meta: {
    title: '资质管理',
    icon: 'el-icon-s-tools',
  },
  children: [
    {
      path: '/qualificationManage',
      name: 'qualificationManage',
      component: () => import('../../views/qualificationOnline/index.vue'),
      meta: { title: '首营资质管理' },
    },
    {
      path: '/qualificationMultipartUpload',
      name: 'qualificationMultipartUpload',
      component: () => import('../../views/qualificationOnline/multipartUpload.vue'),
      meta: { title: '批量上传' },
      hidden: true,
    },
    {
      path: '/qualificationUploadEdit',
      name: 'qualificationUploadEdit',
      component: () => import('../../views/qualificationOnline/uploadEdit.vue'),
      meta: { title: '上传编辑' },
      hidden: true,
    },
    {
      path: '/drugTestResultManage',
      name: 'drugTestResultManage',
      component: () => import('../../views/drugTestResult/index.vue'),
      meta: { title: '我的药检报告' },
    },
    {
      path: '/addNewBatch',
      name: 'addNewBatch',
      component: () => import('../../views/drugTestResult/addNewBatch.vue'),
      meta: { title: '新增批号' },
      hidden: true,
    },
  ],
};
export default qualificationRouter;
