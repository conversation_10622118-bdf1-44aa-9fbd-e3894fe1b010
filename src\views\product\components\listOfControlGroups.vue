<template>
  <div class="my-dialog">
    <el-dialog
      title="控销组名单"
      :visible="true"
      ref="viewproduct"
      :modal-append-to-body="false"
      :before-close="handleDialogClose"
      width="80%"
    >
      <div>
        <el-form ref="ruleForm" :inline="true" size="small" label-width="100px">
          <el-form-item label="药店编号" prop="merchantId">
            <el-input v-model.trim="ruleForm.merchantId" oninput="value=value.replace(/[^0-9.]/g,'')" placeholder="请输入" size="small"></el-input>
          </el-form-item>
          <el-form-item label="药店名称" prop="merchantName">
            <el-input v-model.trim="ruleForm.merchantName" placeholder="请输入" size="small"></el-input>
          </el-form-item>
          <el-form-item label="手机号" prop="merchantMobile">
            <el-input v-model.trim="ruleForm.merchantMobile" oninput="value=value.replace(/[^0-9.]/g,'')" placeholder="请输入" size="small"></el-input>
          </el-form-item>
          <el-form-item class="btnBox">
            <el-button size="small" @click="resetForm()">重置</el-button>
            <el-button size="small" type="primary" @click="getList('search')">查询</el-button>
          </el-form-item>
        </el-form>
      </div>
      <div class="explain-table">
        <el-table
          v-loading="laodingBoole"
          :data="tableData.list"
          show-overflow-tooltip
          border
          :header-cell-style="{ background: '#F9F9F9', color: '#666666' }"
        >
          <el-table-column prop="merchantId" label="药店编号" />
          <el-table-column prop="merchantName" label="药店名称" />
          <el-table-column prop="merchantMobile" label="手机号" />
        </el-table>
      </div>
      <div class="explain-pag">
        <Pagination
          v-show="tableData.total > 0"
          :total="tableData.total"
          :page.sync="productPage.pageNum"
          :limit.sync="productPage.pageSize"
          @pagination="getList"
        ></Pagination>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import Pagination from '@/components/Pagination'
import { getMerchantPage } from '@/api/product';
export default {
  name: 'ListOfControlGroups',
  components: {
    Pagination
  },
  props: {
    controlGroupId: {
      type: Number | String,
    }
  },
  data() {
    return {
      ruleForm:{
        merchantId: '',
        merchantName: '',
        merchantMobile: '',
      },
      list: [],
      tableData: {
        list: [],
        total: 0
      },
      laodingBoole: false, // 加载
      productPage: {
        pageSize: 10,
        pageNum: 1
      },
    }
  },
  mounted() {
    this.getList();
  },
  methods: {
    handleDialogClose() {
      this.$emit('cancelDialog', false);
    },
    getList(from) {
      const that = this;
      if (from == 'search') {
        this.productPage.pageNum = 1;
      }
      this.laodingBoole = true;
      const param = {
        groupId: this.controlGroupId,
        ...this.ruleForm,
        ...this.productPage,
      };
      getMerchantPage(param).then((res) => {
        if (res.code == 0) {
          this.laodingBoole = false;
          if (res.data) {
            that.tableData.list = res.data.list || [];
          } else {
            this.tableData.list = [];
          }
          this.tableData.total = res.data.total;
        } else {
          this.laodingBoole = false;
          this.$message({
            message: res.message,
            type: 'error',
          });
        }
      }).catch(() => {
        this.laodingBoole = false;
      });
    },
     //重置弹窗数据
    resetForm() {
      this.ruleForm = {
        merchantId: '',
        merchantName: '',
        merchantMobile: '',
      },
      this.productPage = {
        pageSize: 10,
        pageNum: 1
      }
      this.getList();
    },
  }
}
</script>
<style lang="scss" scoped>
.my-dialog {
  min-width: 400px;
}
::v-deep  .el-form-item__label {
  margin-left: 20px;
  padding: 0;
}
::v-deep  .el-input__inner {
  border-radius: 0 4px 4px 0;
}
::v-deep  .el-form-item__label {
  padding: 0;
}
::v-deep  .el-input__inner {
  border-radius: 0;
}
::v-deep  .el-dialog__body {
  padding: 0 20px;
}
::v-deep  .el-dialog__wrapper .el-dialog__header {
  background: #f9f9f9;
}
::v-deep  .el-dialog__wrapper .el-dialog__title {
  font-size: 16px;
  font-weight: 500;
  color: #333333;
}
::v-deep  .el-divider--horizontal {
  width: 104%;
  margin: 0 0 0 -20px;
}
.btnBox {
  display: flex;
  justify-content: flex-end;
}
</style>
