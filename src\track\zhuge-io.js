(function () {
  if (window.webSdk) return;
  window.webSdk = [];
  window.webSdk.methods = 'init identify track setSuperProperty'.split(' ');
  window.webSdk.factory = function (b) {
    return function () {
      const a = Array.prototype.slice.call(arguments);
      a.unshift(b);
      window.webSdk.push(a);
      return window.webSdk;
    };
  };
  for (let i = 0; i < window.webSdk.methods.length; i++) {
    const key = window.webSdk.methods[i];
    window.webSdk[key] = window.webSdk.factory(key);
  }
  window.webSdk.load = function (b, x) {
    if (!document.getElementById('sdk-js')) {
      const a = document.createElement('script');
      const verDate = new Date();
      const verStr = verDate.getFullYear().toString() + verDate.getMonth().toString() + verDate.getDate().toString();

      a.type = 'text/javascript';
      a.id = 'sdk-js';
      a.async = !0;
      a.src = `https://zg.ybm100.com/pc-sdk.js?v=${verStr}`;
      a.onerror = function () {
        window.webSdk.identify = window.webSdk.track = function (ename, props, callback) {
          if (callback && Object.prototype.toString.call(callback) === '[object Function]') {
            callback();
          } else if (Object.prototype.toString.call(props) === '[object Function]') {
            props();
          }
        };
      };
      const c = document.getElementsByTagName('script')[0];
      c.parentNode.insertBefore(a, c);
      window.webSdk.init(b, x);
    }
  };


  let AppKey = 'dc504156a0b54a04bd19c57da5422a32';
  if (process.env.NODE_ENV === 'production') {
    // 正式环境
    AppKey = '8b5e1b0f250a436e8c6af9871354bfba';
  }

  window.webSdk.load(AppKey, {
    superProperty: { // 全局的事件属性(选填)
      应用名称: '小药药io',
    },
    debug: true,
    platform: 'h5',
    adTrack: false, // 广告监测开关,默认为false
    autoScroll: false, // 是否监测滚动,启用autoTrack后生效（选填，默认false）
    // zgsee: false,//视屏采集开关, 默认为false
    autoTrack: true,
    // 启用全埋点采集（选填，默认false）
    singlePage: true, // 是否是单页面应用（SPA），启用autoTrack后生效（选填，默认false）

  });
}());
