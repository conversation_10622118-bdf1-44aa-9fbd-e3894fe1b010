<template>
<div class="container">
    <div class="body">
        <div class="placardArea">
            <img src="../../../assets/image/printedPoster.png" alt="" class="mainImg">
            <div class="floating-btn" @click="btnClick">
                <img src="../../../assets/image/printedBtn.png" alt="">
            </div>
        </div>
    </div>
</div>
</template>

<script>
import { getPrintOrder } from '@/api/order'
export default {
name:'placard',
data() {
  return {
  }
 },
methods: {
    btnClick() {
        getPrintOrder().then(res => {
            if(res.code === 0) {
                this.successMag()
            }else {
                this.$message.error(res.msg || "申请失败，稍后再试")
            }
        }).catch(err => {
            this.$message.error('申请失败，稍后再试')
        })
    },
    successMag() {
        this.$alert('申请成功，请等待工作人员电联。', '', {
          confirmButtonText: '确定',
          callback: action => {},
          confirmButtonClass: 'custom-confirm-button'
        });
    }
},
}
</script>

<style lang='scss' scoped>
.header {
    width: 100%;
    .title {
        padding: 10px;
        color: #000;
        font-weight: 500;
        font-size: 16px;
    }
}
.body {
    width: 100%;
    .placardArea {
        width: 90%;
        margin: 0 auto;
        display: flex;
        flex-direction: column; // 改为纵向布局
        min-height: 80vh; // 保留最小高度保证基础展示
        overflow-y: auto;  // 启用垂直滚动
        position: relative;
        
        // 添加滚动容器内边距
        padding: 20px 0;
        
        .mainImg {
            max-width: 100%;
            // 自动高度的弹性图片
            height: auto;
            flex-shrink: 0; // 防止图片被压缩
            
            // 根据容器比例调整
            @media (min-aspect-ratio: 1/1) {
                min-height: 80vh;
            }
        }
        
        .floating-btn {
            position: absolute;
            bottom: 4%;
            left: 50%;
            transform: translateX(-50%);
            z-index: 10;
            border: none;
            background: transparent;
            cursor: pointer;
            display: flex;
            justify-content: center;
            align-items: center;
            width: 200px;
            /* 按钮图片样式 */
            img {
                width: 100%;
                height: 5vh;
                filter: drop-shadow(0 2px 4px rgba(0,0,0,0.2));
                transition: transform 0.1s ease;
            }
            
            // 修复悬停时的变形抖动
            &:hover {
                transform: translateX(-50%) scale(1.1);
                transition: transform 0.3s ease;
            }
            
            // 避免点击时抖动
            &:active {
                transform: translateX(-50%) scale(0.95);
                
                img {
                    filter: drop-shadow(0 1px 2px rgba(0,0,0,0.3));
                }
            }
        }
    }
}
</style>

<style lang='scss'>
.custom-confirm-button {
  background-color: #00b955 !important;
  border-color: #00b955 !important;
}
</style>