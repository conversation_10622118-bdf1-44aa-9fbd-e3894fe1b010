import request from '@/utils/request';

export function getHostName(params) {
  return request({
    url: '/uploadFile/cdn/hostName',
    method: 'post',
    data: params,
  });
}
/* 上传文件 */
export function uploadFile(params) {
  const forms = new FormData();
  forms.append('file', params.file);
  return request({
    url: '/uploadFile/uploadFDFS',
    method: 'post',
    data: forms,
    headers: { 'Content-Type': 'multipart/form-data' },
    onUploadProgress: (progressEvent) => {
      const num = (progressEvent.loaded / progressEvent.total) * 100; // 百分比
      params.onProgress({ percent: num }); // 进度条
    },
  });
}
/* 获取经营类目 */
export function getBusinessCategory(params) {
  return request({
    url: '/corporation/v2/listBusinessCategory',
    method: 'get',
    params,
  });
}
/* 获取资质信息 */
export function getQualificationInfo(params) {
  return request({
    url: '/corporation/v2/listPopBusinessQualificationConfigs',
    method: 'get',
    params,
  });
}
/* 查询企业信息 */
export function getCompanyInfo(params) {
  return request({
    url: '/corporation/v2/findCorporation',
    method: 'get',
    params,
  });
}
/* 历史记录查询企业信息 */
export function getCompanyHistoryInfo(params) {
  return request({
    url: '/corporation/v2/findCorporatioinBantch',
    method: 'get',
    params,
  });
}
/* 获取省市区街道 */
export function getAddresInfo(params) {
  return request({
    url: '/corporation/v2/dicAreas',
    method: 'get',
    params,
  });
}
/* 保存资质（未认证过的） */
export function submitAddCorporation(params) {
  return request({
    url: '/corporation/v2/addCorporation',
    method: 'post',
    data: params,
  });
}

/* 审核提交 */
export function submitAddCheckCorporation(params) {
  return request({
    url: '/corporation/v2/addCheckCorporation',
    method: 'post',
    data: params,
  });
}

/* 修改提交 */
export function submitModifyCorporation(params) {
  return request({
    url: '/corporation/v2/modifyCorporation',
    method: 'post',
    data: params,
  });
}

/* 保存草稿 */
export function saveCorporationDraft(params) {
  return request({
    url: '/corporation/v2/saveCorporationDraft',
    method: 'post',
    data: params,
  });
}

/* 修改记录查询 */
export function getHistoryList(params) {
  return request({
    url: '/corporation/v2/listHistor',
    method: 'get',
    params,
  });
}
/* 企业信息认证记录查询 */
export function getCorporationDraft(params) {
  return request({
    url: '/corporation/v2/getCorporationDraft',
    method: 'get',
    params,
  });
}

/**/
export function getCheckCorporation(params) {
  return request({
    url: '/corporation/v2/checkCorporation',
    method: 'get',
    params,
  });
}

/*卖家中心-修改企业信息（不需要审核）*/
export function updateCorporationInfoWithoutCheck(data) {
  return request({
    url: '/corporation/v2/updateCorporationInfoWithoutCheck',
    method: 'post',
    data,
  });
}

// poi企业列表查询
export function listPoiShop(params) {
  return request({
    url: '/corporation/v2/listPoiShop',
    method: 'get',
    params,
  });
}

/*卖家中心-修改企业对公账户信息 获取回显信息*/
export function getUpdateBankInfo(params) {
  return request({
    url: '/corporation/v2/queryCorporateAccount',
    method: 'get',
    params,
  });
}

/*卖家中心-修改企业对公账户信息 修改*/
export function updateBankInfo(data) {
  return request({
    url: '/corporation/v2/addOrUpdateCorporateAccount',
    method: 'post',
    data,
  });
}
