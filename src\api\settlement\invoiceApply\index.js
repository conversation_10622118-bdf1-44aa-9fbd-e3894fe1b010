import request from '../../index'

/**
 * 获取发票索取列表
 * @param {参数} params
 */
export function getInvoiceDemandList(params) {
  return request.get('/commission/invoice/queryCommissionSettlementList', params)
}
/**
 * 申请开票
 * @param {参数} params
 */
export function applyInvoice(params) {
  return request.post('/commission/invoice/applyInvoice', params)
}
/**
 * 获取发票索取明细列表
 * @param {参数} params
 */
export function getInvoiceDemandDetailList(params) {
  return request.get('/commission/invoice/queryCommissionSettlementDetailList', params)
}
/**
 * 获取发票索取信息
 * @param {参数} params
 */
export function getInvoiceDemandDetailInfo(params) {
  return request.get('/commission/invoice/getCommissionSettlementBaseInfo', params)
}

/**
 * 获取发票记录列表
 * @param {参数} params
 */
export function getInvoiceRecordList(params) {
  return request.get('/commission/invoice/queryInvoiceApplyList', params)
}
/**
 * 重新申请开票
 * @param {参数} params
 */
export function reApplyInvoice(params) {
  return request.post('/commission/invoice/reapplyInvoice', params)
}

/**
 * 获取发票记录明细列表
 * @param {参数} params
 */
export function getInvoiceRecordDetailList(params) {
  return request.get('/commission/invoice/queryInvoiceApplyDetailList', params)
}

/**
 * 获取发票记录基本信息
 * @param {参数} params
 */
export function getInvoiceRecordDetailInfo(params) {
  return request.get('/commission/invoice/queryInvoiceApplyBaseInfo', params)
}
/**
 * 获取发票信息
 * @param {参数} params
 */
export function getInvoiceInfo(params) {
  return request.get('/commission/invoice/queryInvoiceInfo', params)
}

/**
 * 保存发票信息
 * @param {参数} params
 */
export function saveInvoiceInfo(params) {
  return request.post('/commission/invoice/saveOrUpdateInvoice', params)
}
