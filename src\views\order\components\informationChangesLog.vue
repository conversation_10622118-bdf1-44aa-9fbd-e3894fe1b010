<template>
  <el-dialog title="客户信息变更日志" :visible="dialogVisible" width="60%" @close="closeDialog">
    <div v-if="!allList" class="titlediv">客户信息有更新，请仔细核对您erp中的客户资料，并及时更新，以免造成售后问题。</div>
    <el-table v-loading="loading" :data="tableConfig.data" border height="400" style="width: 100%">
      <el-table-column prop="content" label="变更内容">
        <template slot-scope="scope">
          <div>
            <!-- <div><span>{{scope.row.content || ''}}</span></div> -->
            <span v-html="scope.row.content || ''"></span>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="time" label="变更时间" width="200">
        <template slot-scope="scope">
          <div>{{formatDate(scope.row.time,'YMD HMS') }}</div>
        </template>
      </el-table-column>
    </el-table>
    <span v-if="!allList" slot="footer" class="dialog-footer">
      <el-button size="small" @click="closeDialog">关闭</el-button>
      <el-button size="small" type="primary" @click="handleQuery">确认已更新</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { apiQueryQualificationChangeListWithinTime, apiUpdateQualificationTime, apiQueryQualificationChangeList } from '@/api/order/index';

export default {
  name: 'InformationChangesLog',
  props: {
    dialogVisible: {
      type: Boolean,
      default: false,
    },
    merchantId: {
      type: Number,
      default: null,
    },
    allList: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      loading: true,
      tableConfig: {
        data: []
      },
    }
  },
  mounted() {
    if (this.allList) {
      this.getAllInformationChangesLog();
    } else {
      this.getInformationChangesLog();
    }
  },
  methods: {
    // 查看特定时间段
    async getInformationChangesLog() {
      const that = this;
      try {
        const res = await apiQueryQualificationChangeListWithinTime({merchantId: this.merchantId})
        if (res.code === 0) {
          // this.tableConfig.data = res.data || [];
          if (res.data) {
            that.tableConfig.data = res.data || [];
            let content = {};
            that.tableConfig.data.forEach((item, index) => {
              content = {};
              if (item.content == null) {
                content = '-';
              } else {
                content = item.content.replace(/【/g, "<br/> 【");
              }
              that.tableConfig.data[index] = Object.assign({}, that.tableConfig.data[index], {
                content
              });
            });
          }else{
            that.tableConfig.data = [];
          }
        } else {
          this.$alert(res.message, {type: 'error'});
        }
      } catch (e) {
        console.log(e)
      }
      this.loading = false
    },
    // 查看全部
    async getAllInformationChangesLog() {
      const that = this;
      try {
        const res = await apiQueryQualificationChangeList({merchantId: this.merchantId})
        if (res.code === 0) {
          // this.tableConfig.data = res.data || [];
          if (res.data) {
            that.tableConfig.data = res.data || [];
            let content = {};
            that.tableConfig.data.forEach((item, index) => {
              content = {};
              if (item.content == null) {
                content = '-';
              } else {
                content = item.content.replace(/【/g, "<br/> 【");
              }
              that.tableConfig.data[index] = Object.assign({}, that.tableConfig.data[index], {
                content
              });
            });
          } else {
            that.tableConfig.data = [];
          }
        } else {
          that.$alert(res.message, { type: 'error' });
        }
      } catch (e) {
        console.log(e)
      }
      this.loading = false
    },
    closeDialog() {
      this.$emit('cancelDialog');
    },
    handleQuery() {
      apiUpdateQualificationTime({ merchantId: this.merchantId }).then((res) => {
        if (res.code === 0) {
          this.$message.success('更新成功');
          this.$emit('gitList');
          this.closeDialog();
        } else {
          this.$message.error(res.message);
        }
      })
    },
  }
}
</script>

<style scoped lang="scss">
::v-deep   .el-table thead th {
  background: #f9f9f9;
  border: none;

  .cell {
    font-family: PingFangSC, PingFangSC-Regular;
    font-weight: 400;
    text-align: left;
    color: rgba(51, 51, 51, 0.85);
    line-height: 22px;
  }
}

::v-deep   .el-table__body-wrapper {
  font-size: 12px;
  color: #666666;
}
::v-deep  .el-dialog__body {
  padding-top: 10px;
}
.titlediv {
  padding-bottom: 10px;
  font-size: 16px;
  font-weight: bold;
  color: #ff4d4f;
}
</style>
