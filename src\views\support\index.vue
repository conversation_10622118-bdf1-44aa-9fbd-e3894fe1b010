<template>
  <div class="productList">
    <div class="contentBox">
      <div class="title">商品信息</div>
      <!-- <div class="topTip">温馨提示：商品在参与特价活动及平台拼团活动期间（活动创建时间-活动结束时间），不允许修改商品价格</div> -->
      <SearchForm
        ref="searchForm"
        :model="formModel"
        :form-items="formItems"
        @submit="handleFormSubmit"
        @reset="handleFormReset"
      >
        <template slot="form-item-status">
          <el-select v-model="formModel.status" placeholder="请选择">
            <el-option
              v-for="(item, index) in productStatusOptions"
              :key="index"
              :label="item.statusName"
              :value="item.statusType"
            />
          </el-select>
        </template>
        <!-- <template slot="form-item-category">
          <el-cascader
            ref="productCategoryLevel"
            v-model="formModel.categoryId"
            :options="productCategoryLevelOptions"
            :props="{ label: 'name', value: 'id', checkStrictly: true }"
            :show-all-levels="false"
            clearable
          />
        </template> -->
      </SearchForm>
      <div class="operation">
        <el-button
          v-permission="['product_list_batchUp']"
          type="primary"
          size="small"
          @click="batchUpAndDown(1)"
          >批量上架</el-button
        >
        <el-button
          v-permission="['product_list_batchDown']"
          type="primary"
          size="small"
          @click="batchUpAndDown(2)"
          >批量下架</el-button
        >
        <!-- <el-button
          v-permission="['product_list_export']"
          type="primary"
          size="small"
          @click="exportProduct"
          >导出商品</el-button
        >
        <template v-if="!shopConfig.isFbp">
          <el-button
            v-permission="['product_list_release']"
            type="primary"
            size="small"
            @click="releaseProduct"
            >发布商品</el-button
          >
          <el-button
            v-permission="['product_list_batchRelease']"
            type="primary"
            size="small"
            @click="batchProduct"
            >批量发布商品</el-button
          >
        </template>

        <el-select
          v-permission="['product_list_batchUpdate']"
          class="modifyProduct"
          :value="modifyValue"
          placeholder="批量修改"
          @change="modifyProduct"
        >
          <el-option
            v-for="item in modifyOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
        <el-button
          v-permission="['product_list_setSaleTime']"
          style="margin-left: 10px"
          type="primary"
          size="small"
          @click="openProductTimeSetting"
          >商品售卖时间设置</el-button
        >

        <el-button
          style="margin-left: 10px"
          type="primary"
          size="small"
          @click="batchModifyProductActivityVisible = true"
        >
          批量修改活动商品
        </el-button>
        <el-button
          v-if="!shopConfig.isFbp"
          style="margin-left: 10px"
          type="primary"
          size="small"
          @click="putOnShelvesVis = true"
        >
          商品上架设置
        </el-button> -->

        <!-- <template v-if="shopConfig.isFbp">
          <el-button
            style="margin-left: 10px"
            type="primary"
            size="small"
            @click="releaseWarehousedGoods"
            >发布入仓商品</el-button
          >
        </template> -->
      </div>
      <el-tabs v-model="activeName" @tab-click="tabHandleClick">
        <el-tab-pane
          v-for="item in tabStatusOptions"
          :key="item.statusType"
          :label="tabPaneLabel(item)"
          :name="String(item.statusType)"
        />
      </el-tabs>
      <xyyTable
        ref="productListTable"
        v-loading="tableLoading"
        :data="tableConfig.data"
        :col="tableConfig.col"
        :has-selection="true"
        :list-query="listQuery"
        @selectionCallback="selectionCallback"
        @get-data="queryList"
      >
        <template slot="productInfo">
          <el-table-column label="商品信息">
            <template slot-scope="{ row }">
              <!-- <div
                class="productInfo"
                v-html="row.productInfo"
              />-->
              <div class="productInfo">
                <div class="productName">
                  <div class="name">
                    <span>{{ row.productName }}</span>
                    <!-- <span
                      v-if="row.activityType === 1"
                      class="icon"
                    >拼团</span> -->
                  </div>
                  <span>
                    <el-tag
                      v-if="
                        row.activityType === 5 ||
                        row.activityType === 1 ||
                        row.activityType === 2
                      "
                      size="mini"
                      style="margin: 0 2px"
                    >
                      {{
                          {5:'秒杀',1:'拼团',2:'赠品',}[row.activityType]
                      }}
                    </el-tag>
                  </span>
                </div>
                <div class="manufacturer">
                  {{ row.manufacturer }}
                </div>
                <div>{{ row.spec }}</div>
                <div>{{ row.approvalNumber }}</div>
                <div>商品编码：{{ row.barcode }}</div>
                <div>sku编码：{{ row.csuid }}</div>
                <!-- <div>ERP编码：{{ row.erpCode }}</div> -->
                <div v-if="row.activityType === 5 && shopConfig.shopPatternCode !=='ybm'">
                  原商品：{{ row.originalBarcode }}
                </div>
              </div>
            </template>
          </el-table-column>
        </template>
        <template slot="showName">
          <el-table-column label="展示名称">
            <template slot-scope="{ row }">
              <el-image
                style="width: 80px; height: 73px; margin-bottom: 4px"
                :src="row.fullImageUrl"
                :preview-src-list="row.allImageList"
                @click.prevent
              />
              <div>{{ row.showName }}</div>
              <div>
                是否主推高毛：{{
                  { 1: '否', 2: '高毛', 3: '主推' }[row.highGross]
                }}
              </div>
            </template>
          </el-table-column>
        </template>
        <template slot="price">
          <el-table-column
            label="价格"
            :sortable="true"
            :sort-method="soryByPrice"
          >
            <template slot-scope="{ row }">
              <div>
                <div v-if="row.activityType !== 1">
                  单体采购价：{{ row.fob }}
                  <i
                    v-if="row.activityType !== 2 && shopConfig.shopPatternCode !=='ybm'"
                    v-permission="['product_list_editPrice']"
                    class="el-icon-edit-outline"
                    style="color: #4184d5; font-size: 16px"
                    @click="tableModifyBtn(1, row)"
                  />
                </div>
                <div
                  v-if="row.activityType !== 1 && shopConfig.priceType === 2"
                >
                  单体毛利率：{{ row.grossProfitMargin }}
                </div>
                <!-- 暂时隐藏 连锁采购价 -->
                <!-- <div v-if="row.activityType === 1  || row.activityType === 4">
                  连锁采购价：{{ row.chainPrice }}
                </div> -->
                <div
                  v-if="row.activityType !== 1 && shopConfig.priceType === 2"
                >
                  连锁毛利率：{{ row.chainGrossProfitMargin }}
                </div>
                <!-- <div v-if="row.activityType !== 1">
                  底价：{{ row.basePrice }}
                </div> -->
                <div
                  v-if="
                    row.activityType !== 1 &&
                    row.activityType !== 2 &&
                    Number(row.priceSyncErp)
                  "
                  style="color: #ff9500"
                >
                  ERP同步
                </div>

                <!-- <el-button
                  v-if="row.activityType !== 1 && row.activityType !== 2"
                  type="text"
                  size="small"
                  @click="handleCheckSkuAmountLog(row)"
                >
                  查看价格日志
                </el-button> -->

                <div v-if="row.stepPriceStatus === 1">
                  ￥{{ row.minDiscountPrice }}起
                </div>
                <div v-if="row.activityType === 1">
                  拼团价：{{ row.groupPrice }}
                </div>
              </div>
            </template>
          </el-table-column>
        </template>
        <template slot="totalStock">
          <el-table-column label="库存">
            <template slot-scope="{ row }">
              <div style="display: inline-block; width: auto">
                <span style="display: block">
                  <el-tooltip effect="dark" placement="top">
                    <template #content v-if="shopConfig.isFbp"
                      >神农系统对接库存</template
                    >
                    <template #content v-else
                      >总库存=可售库存+订单占用库存+活动占用库存</template
                    >
                    <i class="el-icon-warning-outline" />
                  </el-tooltip>
                  <span v-if="shopConfig.isFbp">
                    总库存：<el-button
                      type="text"
                      size="small"
                      @click="seeTotal('totalStock', row.csuid)"
                      >查看</el-button
                    >
                  </span>
                  <span v-else>
                    {{ `总库存：${row.totalStock}` }}
                  </span>
                  <!-- 暂时隐藏 总库存图标 -->
                  <i
                    v-if="!shopConfig.isFbp && row.activityType !== 1 && shopConfig.shopPatternCode !=='ybm'"
                    v-permission="['product_list_editStock']"
                    class="el-icon-edit-outline"
                    style="color: #4184d5; font-size: 16px"
                    @click="tableModifyBtn(2, row)"
                  />
                </span>
                <span v-if="row.activityType === 1" style="display: block">
                  {{ `活动总限购库存：${row.totalLimitQtyStr}` }}
                </span>
                <span style="display: block">
                  <el-tooltip effect="dark" placement="top">
                    <template #content
                      >可售库存=ERP同步库存/手工设置商品总库存-订单占用库存-活动占用库存</template
                    >
                    <i class="el-icon-warning-outline" />
                  </el-tooltip>
                  {{ `可售库存：${row.availableQty}` }}
                </span>
                <span style="display: block">
                  <el-tooltip effect="dark" placement="top">
                    <template #content v-if="shopConfig.isFbp"
                      >神农未扣减的库存</template
                    >
                    <template #content v-else
                      >订单占用库存=所有未下推ERP且状态为“待付款”或“待审核”或“出库中”订单的该商品占用库存数量</template
                    >
                    <i class="el-icon-warning-outline" />
                  </el-tooltip>
                  <span v-if="shopConfig.isFbp">
                    订单占用库存：<el-button
                      type="text"
                      size="small"
                      @click="seeTotal('occupyStock', row.csuid)"
                      >查看</el-button
                    >
                  </span>
                  <span v-else>
                    {{ `订单占用库存：${row.occupyStock}` }}
                  </span>
                </span>

                <span style="display: block">
                  <el-tooltip effect="dark" placement="top">
                    <template #content>活动预占库存</template>
                    <i class="el-icon-warning-outline" />
                  </el-tooltip>
                  {{ `活动占用库存：${row.occupiedInventoryQty}` }}
                </span>

                <!-- <el-button
                  type="text"
                  size="small"
                  @click="operationClick(8, row)"
                  >查看库存日志</el-button
                > -->
              </div>
              <div
                v-if="row.nearEffect"
                :class="row.nearExpired ? 'redColor' : ''"
              >
                近至：{{ row.nearEffect }}
              </div>
              <div
                v-if="row.farEffect"
                :class="row.farExpired ? 'redColor' : ''"
              >
                远至：{{ row.farEffect }}
              </div>
              <div
                v-if="Number(row.stockSyncErp) && row.activityType !== 2"
                style="color: #ff9500"
              >
                ERP同步
                <span>:{{ row.erpOriginalStock }}</span>
              </div>
            </template>
          </el-table-column>
        </template>
        <!-- <template slot="busAreaName">
          <el-table-column label="供货信息">
            <template slot-scope="{ row }">
              <span v-if="row.controlRosterType === 2">仅白名单客户可买</span>
              <el-popover
                v-else
                placement="top-start"
                width="200"
                trigger="hover"
                :content="`商圈名称：${row.busAreaName}`"
              >
                <div slot="reference" class="busAreaNameBox">
                  商圈名称：{{ row.busAreaName }}
                </div>
              </el-popover>
            </template>
          </el-table-column>
        </template> -->
        <template slot="status">
          <el-table-column label="商品状态">
            <template slot-scope="{ row }">
              <div
                v-if="row.availableQty <= 0 && row.status == 1"
                style="color: #ff2121"
              >
                已售罄
              </div>
              <div v-else>
                <div v-if="row.logicStatus == 4 && shopConfig.shopPatternCode === 'ybm'">下架</div>
                <div else>{{ statusNameStr(row.logicStatus) }}</div>
                <el-button
                  v-permission="['product_list_viewSaleTime']"
                  v-if="Number(row.status) === 1 && row.haveSaleTime"
                  type="text"
                  @click="viewSaleTime(row)"
                  >查看售卖时间</el-button
                >
                <!-- <el-button
                  v-if="Number(row.status)===9"
                  type="text"
                  @click="viewAuditFailed(row)"
                >查看原因</el-button> -->

                <!-- 原有代码 不需改动-->
                <el-button
                  v-if="row.errorTipType"
                  type="text"
                  style="color: #ff2121; cursor: pointer"
                  @click="viewAuditFailed(row)"
                >
                  {{
                    row.errorTipType === 1
                      ? '(商品信息不全)查看原因'
                      : row.errorTipType === 2
                      ? '查看原因'
                      : ''
                  }}
                </el-button>
              </div>
              <div />
              <!-- v-if="row.activityType === 1" -->
              <div v-if="row.activityType === 1">
                <div>活动id：{{ row.activityId }}</div>
                <div>活动时间：{{ transferTime(row.activityStartTime) }} ~
                  {{ transferTime(row.activityEndTime) }}
                </div>
              </div>
            </template>
          </el-table-column>
        </template>
        <template slot="operation">
          <el-table-column label="操作" fixed="right" width="150">
            <template slot-scope="{ row }">
              <div>
                <el-button
                  v-if="row.activityType === 0 && row.status !== 20"
                  type="text"
                  @click="operationClick(1, row)"
                  >详情</el-button
                >
                <el-button
                  v-permission="['product_list_update']"
                  v-if="row.activityType === 0 && row.status !== 8 && row.status !== 20"
                  type="text"
                  @click="operationClick(2, row)"
                  >编辑</el-button
                >
                <el-button
                  v-permission="['product_list_up']"
                  v-if="row.status == 4 || row.status == 6"
                  type="text"
                  @click="operationClick(3, row)"
                  >上架</el-button
                >
                <!-- && (row.activityType == 1 || row.activityType == 2 || row.activityType == 3 || row.activityType == 4)   -->
                <el-button
                  v-permission="['product_list_down']"
                  v-if="row.status == 1"
                  type="text"
                  @click="operationClick(7, row)"
                  >下架</el-button
                >
                <!-- <el-button
                  v-permission="['product_list_delete']"
                  v-if="
                    row.status != 20 &&
                    row.activityType !== 1 &&
                    row.activityType !== 2
                  "
                  type="text"
                  @click="operationClick(4, row)"
                  >删除</el-button
                >
                <el-button
                  v-if="
                    row.status != 20 &&
                    row.activityType !== 1 &&
                    row.activityType !== 2
                  "
                  type="text"
                  @click="operationClick(5, row)"
                  >预览</el-button
                > -->

                <!-- <el-button v-if="row.activityType !== 1 && row.activityType !== 2" type="text" @click="operationClick(6,row)">查看操作日志</el-button> -->
                <!-- <el-button
                  v-if="row.status === 20"
                  type="text"
                  @click="handleMaintainErpCode(row)"
                >
                  编辑ERP编码
                </el-button> -->

                <el-button type="text" @click="handleCkeckSkuLog(row)">
                  查看审核日志
                </el-button>
              </div>
              <!-- <div v-if="row.activityType === 1">
                <el-button
                  v-permission="['product_list_up']"
                  v-if="row.status == 4 || row.status == 6 "
                  type="text"
                  @click="operationClick(3,row)"
                >上架</el-button>
              </div> -->
            </template>
          </el-table-column>
        </template>
      </xyyTable>
    </div>

    <el-dialog
      title="设置ERP编码"
      :close-on-click-modal="false"
      :visible="maintainErpCodeVis"
      width="800px"
      @close="maintainErpCodeVis = false"
    >
      <div>
        设置ERP编码
        <el-input
          v-model="currentGood.erpCode"
          clearable
          size="small"
          placeholder="请输入ERP编码"
          style="width: 400px"
        />
      </div>
      <span slot="footer">
        <el-button size="medium" @click="maintainErpCodeVis = false"
          >取消</el-button
        >
        <el-button
          size="medium"
          style="margin-left: 20px"
          type="primary"
          @click="handleSureMaintainErpCode"
          >确定</el-button
        >
      </span>
    </el-dialog>

    <ProductPreviewDialog
      v-if="productPreviewDialogVisible"
      :barcode="productPreviewBarcode"
      :product-preview-dialog-visible.sync="productPreviewDialogVisible"
    />

    <ModifyDialog
      v-if="modifyDialogVisible"
      :modify-dialog-visible.sync="modifyDialogVisible"
      :modify-config="modifyConfig"
      @confirmCallback="handleFormSubmit"
    />

    <SkuLogDialog
      v-if="skuLogDialogVisible"
      :sku-log-dialog-visible.sync="skuLogDialogVisible"
      :modify-config="skuLogDialogConfig"
    />

    <BatchModifyProduct
      v-if="batchModifyProductVisible"
      :batch-modify-product-visible.sync="batchModifyProductVisible"
      :excel-template="excelTemplate"
      :price-type="shopConfig.priceType"
      :type="batchModifyType"
      @refreshTable="handleFormSubmit"
    />

    <BatchModifyProductActivity
      v-if="batchModifyProductActivityVisible"
      :handle-close-activity="handleCloseActivity"
      @refreshTable="handleFormSubmit"
    />
    <PutOnShelvesSetting
      v-if="putOnShelvesVis"
      :handle-close-put-on-shelves="handleClosePutOnShelves"
      @refreshTable="handleFormSubmit"
    />

    <export-tip
      :change-export="changeExport"
      @handleExoprClose="handleExoprClose"
      @handleChangeExport="handleChangeExport"
    />

    <viewingInventoryLogs
      v-if="viewingInventoryLogsVisible"
      :inventory-log-dialog-visible.sync="viewingInventoryLogsVisible"
      :inventory-config="viewingInventoryLogsConfig"
    />

    <viewSkuAmountLog
      v-if="viewSkuAmountLogVis"
      :sku-amount-log-dialog-visible.sync="viewSkuAmountLogVis"
      :sku-amount-config="currentCheckItem"
    />

    <viewSkuLog
      v-if="viewSkuLogVis"
      :sku-log-dialog-visible.sync="viewSkuLogVis"
      :sku-config="currentCheckItem"
    />

    <ReleaseWarehousedGoodDialog
      v-if="releaseWarehousedGoodDialogVisible"
      :releaseWarehousedGoodDialogVisible.sync="
        releaseWarehousedGoodDialogVisible
      "
    />
    <TotalModal
      v-if="showTotalModal"
      :act-id="actId"
      :act-type="actType"
      @handleClose="handleClose"
    />
    <RepeatTips
      v-if="showRepeatTips"
      :product-abnormal="productAbnormal"
      @handleClose="handleClose"
      @tipsSearch="tipsSearch"
    />
  </div>
</template>

<script>
import xyyTable from '@/components/table'
import SearchForm from '@/components/searchForm'
import exportTip from '@/views/other/components/exportTip'
import {
  loadStatusCounts,
  statistics,
  exportSku,
  skuDelete,
  categoryTree,
  // getProductList,
  findAct,
  apiDownloadTemplate,
  updateDeleteSkuErpCode
} from '@/api/product'
import { getProductList,batchUpAndDown } from '@/api/support'
import { mapState } from 'vuex'
import { actionTracking } from '@/track/eventTracking'
import ProductPreviewDialog from './components/productPreviewDialog'
import ModifyDialog from './components/modifyDialog'
import SkuLogDialog from './components/skuLogDialog'
import BatchModifyProduct from './components/batchModifyProduct'
import BatchModifyProductActivity from './components/batchModifyProductActivity'
import PutOnShelvesSetting from './components/putOnShelvesSetting.vue'
import viewingInventoryLogs from './components/viewingInventoryLogs'
import viewSkuAmountLog from './components/viewSkuAmountLog'
import viewSkuLog from './components/viewSkuLog'
import ReleaseWarehousedGoodDialog from './components/releaseWarehousedGoodDialog'
import TotalModal from './components/totalModal'
import { searchItem } from './config'
import RepeatTips from './components/repeatTips'

export default {
  name: 'support',
  components: {
    SearchForm,
    xyyTable,
    ProductPreviewDialog,
    ModifyDialog,
    SkuLogDialog,
    BatchModifyProduct,
    BatchModifyProductActivity,
    PutOnShelvesSetting,
    exportTip,
    viewingInventoryLogs,
    ReleaseWarehousedGoodDialog,
    TotalModal,
    viewSkuAmountLog,
    viewSkuLog,
    RepeatTips
  },
  data() {
    return {
      batchModifyType: 1,
      viewSkuLogVis: false,
      viewSkuAmountLogVis: false,
      currentGood: { erpCode: '' },
      maintainErpCodeVis: false,
      formModel: {
        barcode: '', // 商品编码
        showName: '', // 商品名称
        manufacturer: '', // 生产厂家
        approvalNumber: '', // 批准文号
        code: '', // 商品条码
        erpCode: '', // erp编码
        status: -99, // 状态:1-销售中，4-下架，6-待上架，8-待审核，9-审核未通过，20-删除
        priceSyncErp: '', // 价格是否同步ERP（0:否;1:是）
        stockSyncErp: '', // 库存是否同步ERP（0:否;1:是）
        saleType: '', // 药品类型
        characteristic: 0, // 商品特性：0全部、1特长药、2、专供、3独家代理、4特许专供
        busName: '', // 商圈名称
        havePic: '', // 是否有图: 0全部、1有图、2、无图
        categoryLevel: '', // 商品分类级别，1~4
        // categoryId: '', // 商品分类id
        createTime: '', // 创建时间
        zeroPrice: false, // 查询价格为0商品：为true时查价格为0商品
        nearTerm: false, // 查询近效期商品：为true时查近效期商品
        authOffShelf: false, // 被强制下架商品，boolean：true查询，false不查询
        needRepair: false, // 需要修复商品，boolean：true查询，false不查询
        expired: false, // 查询过期商品
        expiredOffShelf: false, // 查询过期自动下架商品
        sameErpCode: false, // 查询ERP编码重复的商品
        sameErpCodeFromErp: false, // 查询ERP编码在ERP系统里重复的商品
        stockStatus: '', // 0-无库存，1-有库存
        emptyGrossProfitMargin: false, // 查询单体毛利率商品：为true时查单体毛利率商品
        emptyChainGrossProfitMargin: false, // 查询连锁毛利率商品：为true时查连锁毛利率商品
        emptyBasePrice: false, // 查询底价商品：为true时查底价商品
        csuid: '', // sku编码
        activityType: '', // 商品来源
        activityId: '', // 活动ID
        groupSkuException: false, // 拼团商品状态异常标识  true-选中 false-未选中
        giftSkuException: false, // 赠品状态异常标识 true-选中 false- 未选中
        onSaleStockoutFlag: false, // 在售缺货异常标识
        autoSaleWithStock: false, // 来货自动上架筛选
        lowPrice: false // 价格过低筛选
      },
      formItems: searchItem.formItems,
      productStatusOptions: [],
      productCategoryLevelOptions: [],
      modifyValue: '批量修改',
      modifyOptions: [
        {
          label: '批量修改商品信息',
          value: 1
        },
        {
          label: '批量修改库存',
          value: 2
        },
        {
          label: '批量修改价格',
          value: 3
        },
        {
          label: '批量修改ERP编码',
          value: 4
        }
      ],
      productAbnormal: {
        priceZero: 0,
        validity: 0,
        infoError: 0,
        offShelves: 0,
        correct: 0,
        expiredCount: 0,
        expiredOffShelfCount: 0,
        sameErpCodeCount: 0,
        emptyEffect: 0,
        emptyGrossProfitMargin: 0, // 单体毛利率为空/0
        emptyChainGrossProfitMargin: 0, // 连锁毛利率为空/0
        emptyBasePrice: 0, // 底价为空/0
        groupSkuExceptionCount: 0, // 拼团商品状态异常
        errorGiftSku: 0, // 赠品状态异常
        onSaleStockoutCount: 0, // 在售缺货
        sameErpCodeFromErpCount: 0, // 普通商品在erp系统重复数量
        autoSaleWithStockCount: 0, // 来货自动上架
        lowPriceCount: 0 // 价格过低提醒
      },
      showRepeatTips: false, // ERP重复提醒
      activeName: '-99', // 全部
      selectList: [],
      tableLoading: false,
      tableConfig: {
        data: [],
        col: [
          {
            name: '商品信息',
            index: 'productInfo',
            slot: 'productInfo'
          },
          {
            name: '展示名称',
            index: 'showName',
            slot: 'showName'
          },
          {
            name: '价格',
            index: 'price',
            slot: 'price'
          },
          {
            name: '库存',
            index: 'totalStock',
            slot: 'totalStock'
          },
          {
            name: '供货信息',
            index: 'busAreaName',
            slot: 'busAreaName'
          },
          {
            name: '商品状态',
            index: 'status',
            slot: 'status'
          },
          {
            index: 'operation',
            name: '操作',
            slot: 'operation'
          }
        ],
        operation: [
          {
            name: '详情',
            type: 1
          },
          {
            name: '编辑',
            type: 2
          },
          {
            name: '上架',
            type: 3
          },
          {
            name: '删除',
            type: 4
          },
          {
            name: '预览',
            type: 5
          },
          {
            name: '查看操作日志',
            type: 6
          }
        ]
      },
      listQuery: {
        pageSize: 10,
        page: 1,
        total: 0
      },
      productPreviewDialogVisible: false,
      productPreviewBarcode: '',
      modifyDialogVisible: false,
      modifyConfig: {
        modifyType: '',
        title: '',
        suggestPrice: null,
        chainPrice: null,
        grossProfitMargin: null,
        chainGrossProfitMargin: null,
        basePrice: null
      },
      skuLogDialogVisible: false,
      skuLogDialogConfig: { barcode: '' },
      batchModifyProductVisible: false,
      putOnShelvesVis: false, // 商品上架设置弹窗
      batchModifyProductActivityVisible: false,
      excelTemplate:
        'https://upload.ybm100.com/pop/temp/商品基本信息批量修改模板.xlsx',
      colorTipStatus: '',
      isFirst: true,
      changeExport: false,
      viewingInventoryLogsVisible: false,
      viewingInventoryLogsConfig: {
        barcode: '',
        productName: '',
        erpCode: ''
      },
      currentCheckItem: {
        barcode: '',
        productName: '',
        erpCode: '',
        id:undefined
      },
      releaseWarehousedGoodDialogVisible: false,
      showTotalModal: false,
      actId: '',
      actType: ''
    }
  },
  computed: {
    ...mapState('app', ['shopConfig']),
    tabStatusOptions() {
      console.log('this.productStatusOptions',this.productStatusOptions);
      const options = this.productStatusOptions.map((item) => ({
        statusName: item.statusType === -99 ? '全部商品' : item.statusName,
        statusType: item.statusType,
        count: item.count
      }))
      return options
    }
  },
  watch: {
    'shopConfig.isFbp': {
      handler(val) {
        if (val === 1) {
          this.modifyOptions = [
            {
              label: '批量修改商品信息',
              value: 1
            },
            {
              label: '批量修改价格',
              value: 3
            }
          ]
        }
      },
      deep: true
    }
  },
  activated() {
    this.activate()
    this.getStatistics('first')
  },
  created() {
    this.getCategoryTree()
    // this.loadStatusCounts();
    // this.getStatistics();
    // this.getDownloadTemplate();
    this.initialFormModel = JSON.parse(JSON.stringify(this.formModel))
  },
  mounted() {
    if (Number(this.$route.query.homeEnter) !== 1) {
      this.queryList()
    }
  },
  methods: {
    soryByPrice(obj1, obj2) {
      return obj1.fob - obj2.fob
    },
    handleCkeckSkuLog(row) {
      console.log('id',row.id);
      actionTracking('commodity_log', {})
      this.currentCheckItem.barcode = row.barcode
      this.currentCheckItem.productName = row.productName
      this.currentCheckItem.erpCode = row.erpCode
      this.currentCheckItem.id = row.id
      this.viewSkuLogVis = true
    },
    handleSureMaintainErpCode() {
      if (this.currentGood && this.currentGood.erpCode === '') {
        this.$message.warning('ERP编码不能为空')
        return false
      }
      if (
        this.currentGood &&
        this.currentGood.erpCode &&
        this.currentGood.erpCode.length > 64
      ) {
        this.$message.warning('ERP编码长度不能超过64个字符')
        return false
      }
      updateDeleteSkuErpCode({
        barcode: this.currentGood.barcode,
        erpCode: this.currentGood.erpCode
      }).then((res) => {
        if (res && res.code === 0) {
          this.$message.success('修改成功')
          this.queryList()
        } else {
          this.$message.error('修改失败')
        }
        // this.currentGood = null;
        this.maintainErpCodeVis = false
      })
    },
    // handleMaintainErpCode(row) {
    //   this.currentGood = { ...row }
    //   this.maintainErpCodeVis = true
    // },
    transferTime(time) {
      return window.dayjs(time).format('YYYY-MM-DD HH:mm:ss')
    },
    handleCloseActivity() {
      this.batchModifyProductActivityVisible = false
    },
    handleClosePutOnShelves() {
      this.putOnShelvesVis = false
    },
    activate() {
      const { query } = this.$route
      if (query && Object.keys(query).length > 0) {
        this.resetTipsParams()
        this.$set(this.formModel, 'status', '')
        this.$set(this.formModel, 'stockStatus', '')
        this.$set(this.formModel, 'barcode', '')
        Object.keys(query).map((key) => {
          if (key === 'status') {
            this.$set(this.formModel, key, Number(query[key]))
          } else {
            this.$set(this.formModel, key, query[key])
          }
          if (key === 'barcode') {
            this.$set(this.formModel, key, query[key])
          }
        })
        this.$nextTick(() => {
          setTimeout(() => {
            this.queryList()
          }, 0)
        })
      }
    },
    handleFormSubmit() {
      this.resetTipsParams()
      this.queryList()
      this.getStatistics()
    },
    handleFormReset(obj) {
      this.util.clearLoacl('localShopQuery')
      this.formModel = obj
      this.listQuery = {
        page: 1,
        pageSize: 10,
        total: 0
      }
      this.colorTipStatus = ''
      setTimeout(() => {
        this.queryList()
      }, 500)
      // this.refreshList();
    },
    statusNameStr(value) {
      let name = ''
      this.productStatusOptions.forEach((item) => {
        if (item && Number(item.statusType) === Number(value)) {
          name = item.statusName
        }

      })
      return name || ''
    },
    async getCategoryTree() {
      try {
        const res = await categoryTree()
        if (res.code === 0) {
          this.productCategoryLevelOptions = res.data.children
        }
      } catch (e) {
        console.log(e)
      }
    },
    async loadStatusCounts(params) {
      try {
        // const params = { ...this.formModel };
        // if (from == 2) {
        //   delete params.status;
        // }
        let tempParams = JSON.parse(JSON.stringify(params))
        delete tempParams.status
        const res = await loadStatusCounts(tempParams)
        console.log('res',res);
        if (res) {
          console.log('this.shopConfig.isFbp',this.shopConfig.isFbp);
            const arr = res.filter((i) => i.statusType != 2)
            this.productStatusOptions = [
              {
                statusName: '全部',
                statusType: -99
              },
              ...arr
            ]
        }
      } catch (e) {
        console.log(e)
      }
    },
    async getStatistics(strType) {
      try {
        const res = await statistics()
        if (res.code === 0) {
          const {
            zeroPriceCount,
            nearTermCount,
            errorInfoCount,
            authOffShelfCount,
            needRepairCount,
            expiredCount,
            expiredOffShelfCount,
            sameErpCodeCount,
            emptyEffect,
            emptyGrossProfitMargin,
            emptyChainGrossProfitMargin,
            emptyBasePrice,
            groupSkuExceptionCount,
            errorGiftSku,
            onSaleStockoutCount,
            sameErpCodeFromErpCount,
            autoSaleWithStockCount,
            lowPriceCount
          } = res.data
          this.productAbnormal = {
            priceZero: zeroPriceCount || 0,
            validity: nearTermCount || 0,
            infoError: errorInfoCount || 0,
            offShelves: authOffShelfCount || 0,
            correct: needRepairCount || 0,
            expiredCount: expiredCount || 0,
            expiredOffShelfCount: expiredOffShelfCount || 0,
            sameErpCodeCount: sameErpCodeCount || 0,
            emptyEffect: emptyEffect || 0,
            emptyGrossProfitMargin: emptyGrossProfitMargin || 0,
            emptyChainGrossProfitMargin: emptyChainGrossProfitMargin || 0,
            emptyBasePrice: emptyBasePrice || 0,
            groupSkuExceptionCount: groupSkuExceptionCount || 0,
            errorGiftSku: errorGiftSku || 0,
            onSaleStockoutCount: onSaleStockoutCount || 0,
            sameErpCodeFromErpCount: sameErpCodeFromErpCount || 0,
            autoSaleWithStockCount: autoSaleWithStockCount || 0,
            lowPriceCount: lowPriceCount || 0
          }
          if (
            strType === 'first' &&
            (sameErpCodeFromErpCount > 0 || sameErpCodeCount > 0)
          ) {
            this.showRepeatTips = true
          }
        }
      } catch (e) {
        console.log(e)
      }
    },
    getParams() {
      const params = { ...this.formModel }
      if (params.status === -99) {
        params.status = ''
      }
      if (params.createTime && params.createTime.length > 0) {
        params.createTimeStart = params.createTime[0]
        params.createTimeEnd = params.createTime[1]
      }
      delete params.createTime
      // const selected = this.$refs.productCategoryLevel.getCheckedNodes(true)[0]
      // console.log('-----------', selected)
      // if (selected && selected.data) {
      //   params.categoryLevel = selected.data.level
      //   params.categoryId = selected.data.id
      // }
      return params
    },
    async queryList(listQuery, from) {
      const that = this
      this.tableLoading = true
      if (listQuery) {
        const { pageSize, page } = listQuery
        this.listQuery.pageSize = pageSize
        this.listQuery.page = page
      }
      let params = this.getParams()
      const { pageSize, page } = this.listQuery
      params.page = page
      params.rows = pageSize
      console.log(JSON.stringify(params))
      this.activeName = params.status ? String(params.status) : '-99'
      if (from == 1) {
        const localParams = this.util.getLocal('localShopQuery')
        if (localParams) {
          params = localParams
          params.createTimeEnd = ''
          params.createTimeStart = ''

          this.formModel = { ...localParams }
          this.formModel.status = localParams.status
            ? Number(localParams.status)
            : -99
          this.activeName = localParams.status
            ? String(localParams.status)
            : '-99'
          this.createTime =
            localParams.createTimeStart && localParams.createTimeEnd
              ? [localParams.createTimeStart, localParams.createTimeEnd]
              : ''
          this.listQuery.pageSize = localParams.rows
          this.listQuery.page = localParams.page
          this.listQuery = Object.assign({}, this.listQuery)
        }
      } else {
        this.util.setLocal('localShopQuery', params)
      }
      this.loadStatusCounts(params)
      try {
        // 映射
        params.activityType = { 0: 1, 1: 3, 2: 4 }[params.activityType];
        const res = await getProductList(params)
        that.tableLoading = false
        console.log('---getList---', res)
        if (res && res.code === 0) {
          this.tableConfig.data = []
          this.tableConfig.data = this.initTableData(res.result.list)
          this.listQuery.total = res.result.total
          this.listQuery.pageSize = res.result.pageSize
        }
      } catch (e) {
        console.log(e)
        that.tableLoading = false
      }
    },
    initTableData(data) {
      if (data && Array.isArray(data)) {
        data.map((item) => {
          item.activityType = { 1: 0, 3: 1, 4: 2, 2: 5 }[item.activityType];
          if (item.activityType === undefined) item.activityType = '';
          // const ary = [];
          // item.productName && ary.push(`<span class="productName">${item.productName}</span>`);
          // item.manufacturer && ary.push(`<span class="manufacturer">${item.manufacturer}</span>`);
          // item.spec && ary.push(item.spec);
          // item.approvalNumber && ary.push(item.approvalNumber);
          // item.barcode && ary.push(item.barcode);
          // item.erpCode && ary.push(item.erpCode);
          // item.productInfo = ary.join('<br/>');
          return item;
        });
        return data;
      }
    },
    tabPaneLabel(item) {
      let str = ''
      str = `${item.statusName}`
      return str
    },
    resetTipsParams() {
      this.formModel.zeroPrice = false
      this.formModel.nearTerm = false
      this.formModel.authOffShelf = false
      this.formModel.needRepair = false
      this.formModel.expired = false
      this.formModel.expiredOffShelf = false
      this.formModel.sameErpCode = false
      this.formModel.emptyGrossProfitMargin = false
      this.formModel.emptyChainGrossProfitMargin = false
      this.formModel.emptyBasePrice = false
      this.formModel.sameErpCodeFromErp = false
      this.formModel.autoSaleWithStock = false
      this.formModel.lowPrice = false
      this.formModel.giftSkuException = false
    },
    tipsSearch(key, value) {
      actionTracking('product_management_top_quick_search', {
        filter_item: {
          zeroPrice: '0_price',
          authOffShelf: 'wrong_removed',
          needRepair: 'wrong_modified_in_time',
          nearTerm: 'near_term',
          emptyEffect: 'empty_term',
          expiredOffShelf: 'overdue_removing',
          expired: 'overdue_removed',
          sameErpCode: 'duplicate_ERP_code',
          groupSkuException: 'abnormal_status',
          emptyGrossProfitMargin: 'single_gross',
          emptyChainGrossProfitMargin: 'chain_gross',
          emptyBasePrice: 'base_price',
          onSaleStockoutFlag: 'no_stock',
          giftSkuException: 'gift_abnormal_status',
          lowPrice: 'low_price'
        }[key]
      })
      this.formModel = this.$refs.searchForm.getInitialFormModel()
      this.formModel[key] = value
      this.colorTipStatus = key
      this.queryList()
    },
    handleChangeExport(info) {
      this.changeExport = false
      if (info === 'go') {
        const path = '/downloadList'
        window.openTab(path)
        // that.$router.push({ path: '/downloadList' });
      }
    },
    handleExoprClose() {
      this.changeExport = false
    },
    // 批量上下架  1:上架、2、下架
    async batchUpAndDown(value) {
      if (this.selectList.length > 0) {
        const Ids = this.selectList.map((item) => item.id)
        const params = {
          skuIds: Ids,
          status: Number(value)
        }
        const tip = []
        this.selectList.map((item) => {
          if (item.nearExpired || item.farExpired) {
            tip.push(item.id)
          }
        })
        // 批量上架不判断 商品“近效期至”或“远效期至”为空或30天内即将过期
        if (tip.length > 0 && value !== 1) {
          this.$alert(
            `商品“近效期至”或“远效期至”为空或30天内即将过期，不允许${
              value === 1 ? '上架' : '下架'
            }。请到商品列表顶部查询并及时修正`,
            '温馨提示',
            {
              confirmButtonText: '确定',
              callback: () => {}
            }
          )
        } else {
          if (value === 1) {
            const conStr = '您确定要上架此商品吗?'
            this.$confirm(conStr, '提示', {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning'
            }).then(async () => {
              try {
                const res = await batchUpAndDown(params)
                if (res.code === 0) {
                  this.$message.success({ message: res.message, offset: 100 })
                  this.handleFormSubmit()
                } else if (res.code === 2) {
                  this.$alert(res.message, '温馨提示', {
                    confirmButtonText: '确定',
                    callback: () => {}
                  })
                } else {
                  this.$message.error({ message: res.message, offset: 100 })
                }
              } catch (e) {
                console.log(e)
              }
            })
          } else {
            // 批量下架
            this.modifyConfig = {
              title: '提示',
              modifyType: 'shelves'
            }
            this.modifyConfig.ids = Ids
            this.modifyDialogVisible = true
          }
        }
      } else {
        const str = value === 1 ? '上架' : '下架'
        this.$message({
          type: 'warning',
          message: `请先勾选要${str}的商品`,
          offset: 100
        })
        // this.$alert('请先勾选要' + str + '的商品', {type: 'warning'})
      }
    },
    selectionCallback(selet) {
      this.selectList = selet
    },
    tabHandleClick() {
      this.formModel.status = Number(this.activeName)
      this.queryList({}, 2)
    },
    tableModifyBtn(num, row) {
      if (Number(num) === 1) {
        findAct({ barcode: row.barcode }).then((res) => {
          if (res.code === 0) {
            // if (res.data) {
            //   this.$alert('商品在参与特价活动及平台拼团活动期间，不允许修改商品价格', '温馨提示', {
            //     confirmButtonText: '确定',
            //     callback: (action) => {},
            //   });
            // } else {
            this.modifyConfig = {
              title: '设置价格',
              modifyType: 'suggestPrice',
              suggestPrice: row.fob ? row.fob : null,
              chainPrice: row.chainPrice ? row.chainPrice : null,
              priceType: this.shopConfig.priceType,
              grossProfitMargin: row.grossProfitMargin
                ? row.grossProfitMargin
                : null,
              chainGrossProfitMargin: row.chainGrossProfitMargin
                ? row.chainGrossProfitMargin
                : null,
              basePrice: row.basePrice ? row.basePrice : null
            }
            this.modifyConfig.barcode = row.barcode
            this.modifyDialogVisible = true
            // }
          } else {
            this.$message.error({ message: res.message, offset: 100 })
          }
        })
      } else if (Number(num) === 2) {
        this.modifyConfig = {
          title: '设置总库存',
          modifyType: 'totalStock'
        }
        this.modifyConfig.barcode = row.barcode
        this.modifyDialogVisible = true
      } else if (Number(num) === 3) {
        // this.$confirm('您确定要下架此商品吗?', '提示', {
        //     confirmButtonText: '确定',
        //     cancelButtonText: '取消',
        //     type: 'warning',
        //   }).then(async () => {
        //     const params = {
        //       skuIds: [row.id],
        //       status: 2,
        //     };
        //     const res = await batchUpAndDown(params);
        //     if (res.code === 0) {
        //       this.$message.success({ message: res.message, offset: 100 });
        //       this.handleFormSubmit();
        //     } else {
        //       this.$message.error({ message: res.message, offset: 100 });
        //     }
        //   }).catch(() => {});
        this.modifyConfig = {
          title: '提示',
          modifyType: 'shelves'
        }
        this.modifyConfig.ids = [row.id]
        this.modifyDialogVisible = true
      }
    },
    async viewAuditFailed(row) {
      // 查看审核不通过原因
      const h = this.$createElement
      this.$msgbox({
        title: '查看原因',
        message: h('div', null, [
          row.errorTips.map((i, index) =>
            h(
              'p',
              null,
              row.errorTips.length > 1 ? `${index + 1}、${i}` : `${i}`
            )
          )
        ]),
        confirmButtonText: '确定'
      })
    },
    viewSaleTime(row) {
      // 查看售卖时间
      const path = '/support/productTimeSetting'
      window.openTab(path, { barcode: row.barcode })
    },
    operationClick(type, row, index) {
      console.log('row', row)
      const { barcode, showName, erpCode, id,categoryFirstId } = row
      switch (Number(type)) {
        case 1: // 详情
          if (barcode) {
            // window.location.href = `/product/viewSku?barcode=${barcode}`;
            // const path = process.env.VUE_APP_BASE_API + '/product/viewSku'
            // window.openTab(path, {barcode: barcode}, '商品详情')
            const path = '/support/details'
            const obj = {
              id: id,
              isEdit: 1,
              from: 'productList',
              categoryFirstId:categoryFirstId
            }
            window.openTab(path, obj)
          }
          break
        case 2: // 编辑
          if (barcode) {
            // window.location.href = `/product/toEditProduce?barcode=${barcode}`;
            // const path = process.env.VUE_APP_BASE_API + '/product/toEditProduce'
            // window.openTab(path, {barcode: barcode}, '商品编辑')
            const path = '/support/detailsEdit'
            const obj = {
              id: id,
              isEdit: 2,
              from: 'productList',
              categoryFirstId:categoryFirstId
            }
            window.openTab(path, obj)
          }
          break
        case 3: // 上架
          this.$confirm('您确定要上架此商品吗?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          })
            .then(async () => {
              const params = {
                skuIds: [row.id],
                status: 1
              }
              const res = await batchUpAndDown(params)
              if (res.code === 0) {
                this.$message.success({ message: res.message, offset: 100 })
                this.handleFormSubmit()
              } else if (res.code === 2) {
                this.$alert(res.message, '温馨提示', {
                  confirmButtonText: '确定',
                  callback: (action) => {}
                })
              } else {
                // this.$message.error(res.message)
                this.$message.error({ message: res.message, offset: 100 })
                // this.$alert(res.message, {type: 'error'})
              }
            })
            .catch(() => {})
          break

        case 6: // 查看操作日志
          this.skuLogDialogConfig.barcode = barcode
          this.skuLogDialogVisible = true
          break
        case 7: // 下架
          this.$confirm('您确定要下架此商品吗?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
          }).then(async () => {
            const params = {
              skuIds: [row.id],
              status: 2,
            };
            const res = await batchUpAndDown(params);
            if (res.code === 0) {
              this.$message.success({ message: res.message, offset: 100 });
              this.handleFormSubmit();
            } else {
              this.$message.error({ message: res.message, offset: 100 });
            }
          }).catch(() => {});
          break;
        // 查看日志
        case 8:
          this.viewingInventoryLogsConfig.barcode = barcode
          ;(this.viewingInventoryLogsConfig.showName = showName),
            (this.viewingInventoryLogsConfig.erpCode = erpCode),
            (this.viewingInventoryLogsVisible = true)
          break
      }
    },
    // 获取下载模板
    async getDownloadTemplate() {
      const that = this
      try {
        const params = {
          fileName: '商品基本信息批量修改模板.xlsx',
          priceType: that.shopConfig.priceType
        }
        const res = await apiDownloadTemplate(params)
        if (res.code === 0) {
          that.excelTemplate = res.data
        }
      } catch (e) {}
    },
    // 发布入仓商品
    releaseWarehousedGoods() {
      this.releaseWarehousedGoodDialogVisible = true
    },
    // 查看库存
    seeTotal(type, id) {
      this.showTotalModal = true
      this.actId = id
      this.actType = type
    },
    handleClose() {
      this.showTotalModal = false
      this.showRepeatTips = false
    }
  }
}
</script>

<style scoped lang="scss">
.topTip {
  padding: 5px 20px;
  background: #f3d9b2;
  opacity: 0.8;
  color: #ff2121;
}
.productList {
  //height: 100%;
  .redColor {
    color: #ff2121 !important;
  }
  .contentBox {
    //height: 100%;
    padding: 16px 16px;
    background: #fff;
    margin-bottom: 10px;

    .title {
      font-weight: 500;
      text-align: left;
      color: #000000;
      line-height: 14px;
      margin-bottom: 24px;
    }

    .title:before {
      content: '';
      display: inline-block;
      width: 3px;
      height: 13px;
      background: linear-gradient(360deg, #1d69c4 1%, #8bbdfc 99%);
      border-radius: 2px;
      margin-right: 8px;
      vertical-align: middle;
    }

    .searchForm {
      overflow: hidden;
    }

    .el-form-item {
      vertical-align: middle;
      margin-right: 16px;
      margin-bottom: 12px;
      border: 1px solid #dcdfe6;
      border-radius: 4px;

      ::v-deep   .el-form-item__label {
        font-size: 12px;
        width: 95px;
        height: 33px;
        line-height: 33px;
        border-right: 1px solid #dcdfe6;
      }

      ::v-deep   .el-form-item__content {
        width: 120px;

        .el-input__inner {
          border: none;
          font-size: 12px;
        }
      }
    }

    ::v-deep   .formItemTime .el-form-item__content {
      width: 354px;
    }

    .operation {
      margin-bottom: 12px;
    }

    .tips {
      padding: 8px 16px;
      margin-bottom: 10px;
      .div-info:hover {
        border: 1px solid #4183d5;
        background-color: #fff;
        .status-span i {
          background: #4183d5 !important;
        }
      }
      .div-info {
        display: inline-block;
        padding: 5px 10px 10px 5px;
        border-radius: 2px;
        font-size: 14px;
        font-family: PingFangSC, PingFangSC-Regular;
        font-weight: 400;
        border: 1px solid #dcdfe6;
        margin: 0 10px 10px 0;
        p {
          margin: 0;
          padding-top: 5px;
          font-size: 14px;
          font-family: PingFangSC, PingFangSC-Medium;
          font-weight: 500;
          color: #333333;
        }
        .status-span {
          padding-left: 14px;
          position: relative;
          i {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            left: 4px;
            display: inline-block;
            vertical-align: middle;
            width: 5px;
            height: 5px;
            border-radius: 50%;
          }
        }
        .refundCountBox {
          .refundCount {
            margin-left: 12px;
            font-size: 20px;
            font-weight: 500;
            color: #ff3945;
            font-family: PingFangSC, PingFangSC-Medium;
          }
          .seeCount {
            float: right;
            color: #ffffff;
          }
        }
      }
    }
  }

  .table-containter {
    height: 100%;
  }

  ::v-deep   .el-tabs__item.is-active {
    border: 1px solid #eeeeee;
    border-top-left-radius: 5px;
    border-top-right-radius: 5px;
    border-bottom: none;
  }

  ::v-deep   .el-tabs--top .el-tabs__item:last-child {
    padding-right: 20px;
  }

  ::v-deep   .el-tabs--top .el-tabs__item.is-top:nth-child(2) {
    padding-left: 20px;
  }

  .modifyProduct {
    vertical-align: middle;
    margin-left: 10px;

    ::v-deep   .el-input__inner {
      width: 110px;
      height: 32px;
      line-height: 32px;
      color: #ffffff;
      background-color: #4183d5;
      border-color: #4183d5;
    }

    ::v-deep   .el-input__icon {
      line-height: 32px;
    }
  }

  ::v-deep   #tabled th > .cell {
    text-align: left;
    padding-left: 16px;
  }

  ::v-deep   #tabled tr td .cell {
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    white-space: break-spaces;
    font-size: 12px;
    font-family: PingFangSC, PingFangSC-Regular;
    font-weight: 400;
    text-align: left;
    color: #666666;
    line-height: 22px;
    padding-left: 16px;

    .busAreaNameBox {
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .productInfo {
      text-align: left;
      font-size: 12px;
      font-family: PingFangSC, PingFangSC-Regular;
      font-weight: 400;
      color: #999999;
      line-height: 18px;

      .productName {
        font-size: 14px;
        font-family: PingFangSC, PingFangSC-Medium;
        font-weight: 500;
        color: rgba(51, 51, 51, 0.85);
        line-height: 22px;
        .name {
          display: flex;
          align-items: center;
          .icon {
            font-size: 12px;
            color: #4183d5;
            border: 1px solid #4183d5;
            padding: 0 2px;
            margin-left: 5px;
            border-radius: 2px;
          }
        }
      }

      .manufacturer {
        color: rgba(51, 51, 51, 0.85);
      }
    }

    .el-button {
      height: 22px;
      padding: 0;
      margin: 0;
      text-align: left;
      font-size: 12px;
      line-height: 22px;
    }

    .el-button:before {
      display: none;
    }
  }

  ::v-deep   .el-dialog__title {
    font-size: 16px;
  }
}
::v-deep  .el-input-group__prepend {
  background: none;
  color: #333333;
  padding: 0 10px;
}
::v-deep   .cell {
  .el-checkbox__inner {
    border: 1px solid #000000;
  }
}
</style>
