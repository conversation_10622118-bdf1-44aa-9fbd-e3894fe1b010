<template>
  <div class="top_father">
    <div class="top">
      <div class="top_left">
        <span class="sign"></span>
        <div>结算单明细</div>
      </div>
      <div class="top_right">
        <el-button size="small" type="primary" plain @click="back">返回</el-button>
      </div>
    </div>
    <div class="settleMsg">
        <span>单据号 {{ settleMsg.businessNo }}</span>
        <span>结算状态 {{ settleMsg.orderSettlementStatus == 1 ? '已结算' : settleMsg.orderSettlementStatus == 0 ? '未结算' : ''}}</span>
    </div>
    <div class="tableSty">
      <el-table
        :data="tableData"
        style="width: 100%; margin-bottom: 10px"
        border
        stripe
        v-loading="loading"
      >
        <el-table-column label="商品编码" prop="barcode" />
        <el-table-column label="skuid" prop="csuid" />
        <el-table-column label="商品erp编码" prop="erpCode" />
        <el-table-column label="商品一级分类" prop="businessFirstCategoryName" />
        <el-table-column label="商品原价" prop="productOriginPrice" />
        <el-table-column label="数量" prop="productAmount" />
        <el-table-column label="商品金额" prop="productMoney" />
        <el-table-column label="店铺总优惠" prop="shopTotalDiscount" />
        <el-table-column label="平台总优惠" prop="platformTotalDiscount" />
        <el-table-column label="商品实付金额" prop="payAmount" />
        <el-table-column prop="commissionRatio">
          <template slot="header">
            <span style="margin-right: 3px;">商品佣金比例</span>
            <el-tooltip class="item" effect="dark" content="生成订单时商品的抽佣比例" placement="top-start">
              <i class="el-icon-warning-outline"></i>
            </el-tooltip>
          </template>
          <template slot-scope="scope">
            {{ scope.row.commissionRatio }}
          </template>
        </el-table-column>
        <el-table-column prop="firstCommissionRatio">
          <template slot="header">
            <span style="margin-right: 3px;">一级分类佣金比例</span>
            <el-tooltip class="item" effect="dark" content="生成订单时商品一级分类的抽佣比例" placement="top-start">
              <i class="el-icon-warning-outline"></i>
            </el-tooltip>
          </template>
          <template slot-scope="scope">
            {{ scope.row.firstCommissionRatio }}
          </template>
        </el-table-column>
        <el-table-column prop="hireMoney">
          <template slot="header">
            <span style="margin-right: 3px;">商品佣金金额</span>
            <el-tooltip class="item" effect="dark" content="生成订单时商品的抽佣比例。商品佣金金额=（商品实付金额+平台总优惠）*订单明细佣金比例" placement="top-start">
              <i class="el-icon-warning-outline"></i>
            </el-tooltip>
          </template>
          <template slot-scope="scope">
            {{ scope.row.hireMoney }}
          </template>
        </el-table-column>
        <!-- <el-table-column prop="firstHireMoney">
          <template slot="header">
            <span style="margin-right: 3px;">一级分类佣金金额</span>
            <el-tooltip class="item" effect="dark" content="生成订单时商品一级分类的抽佣比例。一级分类佣金金额=（商品实付金额+平台总优惠）*一级分类佣金比例" placement="top-start">
              <i class="el-icon-warning-outline"></i>
            </el-tooltip>
          </template>
          <template slot-scope="scope">
            {{ scope.row.firstHireMoney }}
          </template>
        </el-table-column> -->
        <el-table-column label="佣金折扣" prop="commissionDiscount" />
        <el-table-column label="折扣原因" prop="discountReason" />
        <el-table-column prop="payableCommission">
          <template slot="header">
            <span style="margin-right: 3px;">应缴纳佣金</span>
            <el-tooltip class="item" effect="dark" placement="top-start">
              <div slot="content">
                ｜商品佣金享受折扣或不享折扣<br/>
                补贴冲抵佣金商业：应缴纳佣金=商品佣金金额*佣金折扣-平台总优惠<br/>
                补贴不冲抵佣金商业：应缴纳佣金=商品佣金金额*佣金折扣<br/>
                ｜一级分类佣金享受折扣<br/>
                补贴冲抵佣金商业：应缴纳佣金=商品佣金金额-一级分类佣金金额*（1-佣金折扣）-平台总优惠<br/>
                补贴不冲抵佣金商业：应缴纳佣金=商品佣金金额-一级分类佣金金额*（1-佣金折扣）<br/>
              </div>
              <i class="el-icon-warning-outline"></i>
            </el-tooltip>
          </template>
          <template slot-scope="scope">
            {{ scope.row.payableCommission }}
          </template>
        </el-table-column>
        <el-table-column prop="actualCommissionMoney">
          <template slot="header">
            <span style="margin-right: 3px;">实际需缴纳佣金</span>
            <el-tooltip class="item" effect="dark" content="实际需缴纳佣金=应缴纳佣金" placement="top-start">
              <i class="el-icon-warning-outline"></i>
            </el-tooltip>
          </template>
          <template slot-scope="scope">
            {{ scope.row.actualCommissionMoney }}
          </template>
        </el-table-column>
        <el-table-column prop="commissionDiscountMoney">
          <template slot="header">
            <span style="margin-right: 3px;">佣金优惠</span>
            <el-tooltip class="item" effect="dark" placement="top-start">
              <div slot="content">
                商品佣金享受折扣：佣金优惠=商品佣金金额*（1-佣金折扣）<br/>
                一级分类佣金享受折扣：佣金优惠=一级分类佣金金额*（1-佣金折扣）<br/>
              </div>
              <i class="el-icon-warning-outline"></i>
            </el-tooltip>
          </template>
          <template slot-scope="scope">
            {{ scope.row.commissionDiscountMoney }}
          </template>
        </el-table-column>
      </el-table>
      <!-- 分页 -->
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="pagination.pageNum"
        :page-sizes="[10, 20, 50]"
        :page-size="pagination.pageSize"
        background
        layout="->, total, sizes, prev, pager, next, jumper"
        :total="pagination.total"
      >
      </el-pagination>
    </div>
  </div>
</template>

<script>
import { getDetail } from '../../api/settlement/bill';

export default {
    data() {
      return {
        settleMsg: {}, // 结算单信息
        loading: false, //表格加载中
        tableData: [], // 表格数据
        businessNo: null, // 订单号
        pagination: {
          pageNum: 1,
          pageSize: 10,
          total: 0
        }, // 表格分页
      }
    },
    // mounted() {
    //   this.getList()
    //   this.loadTableData()
    // },
    activated(){
      this.getList()
      this.loadTableData()
    },
    methods: {
      getList(){
        this.tableData = this.$route.params.tableData
        this.businessNo = this.$route.params.row.businessNo
        this.pagination.total = this.$route.params.total
        this.settleMsg = this.$route.params.row
      },
      back(){
        this.$router.go(-1)
      },
      search(val) {
        this.loadTableData()
      }, // 查询逻辑
  
      loadTableData() {
        let subData = {
          businessNo: this.businessNo,
          pageNum: this.pagination.pageNum,
          pageSize: this.pagination.pageSize
        }
        this.loading = true
        getDetail(subData).then((res) => {
          if(res.code === 0) {
            if(res.data.list?.length != 0){
              this.tableData = res.data.list
              this.pagination.total = res.data.total
            }
          }
        }).finally(() => {
          this.loading = false
        })
      }, // 加载表格数据
  
      handleCurrentChange(pageNum) {
        this.pagination.pageNum = pageNum
        this.search()
      }, // 页码改变的回调
      handleSizeChange(pageSize) {
        this.pagination.pageSize = pageSize
        this.search()
      } // 每页条数改变的回调
    }

}
</script>

<style lang="scss">
.top_father{
  box-sizing: border-box;
  padding: 10px;
}
.top {
  box-sizing: border-box;
  // border: 1px solid red;
  width: 100%;
  display: flex;
  justify-content: space-between;
  padding: 10px;
  font-weight: bold;
  .top_left {
    display: flex;
    align-items: center;
  }
  .top_right{
    margin-right: 20px;
  }
  .sign {
    display: inline-table;
    width: 3px;
    height: 13px;
    background: linear-gradient(360deg, #1d69c4 1%, #8bbdfc 99%);
    border-radius: 2px;
    margin-right: 8px;
  }
}
.settleMsg{
  display: flex;
  padding-left: 10px;
  margin-bottom: 10px;
  span:nth-child(-n+1) {
      margin-right: 100px;
  }
}
.tableSty {
  width: 98%;
  //   border: 1px solid red;
  margin: 0 auto;
}
</style>