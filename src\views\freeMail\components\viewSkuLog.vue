<template>
  <el-dialog
    title="商品变更日志"
    :visible="dialogVisible"
    width="60%"
    height="300"
    :before-close="handleClose"
  >
    <div style="margin-bottom:10px">
      <span style="color:#333333;font-size:14px">{{skuConfig.showName}}</span>
      <span style="margin-left:10px">{{skuConfig.barcode}}</span>
      <span style="margin-left:10px">{{skuConfig.erpCode}}</span>
    </div>
    <el-table
      v-loading="loading"
      :data="tableConfig.data"
      border
      height="400"
    >
      <el-table-column label="变更内容">
        <template slot-scope="{row}">
          <p
            v-for="(item, index) in row.changeInfo"
            :key="index"
          >
            {{ item }}
          </p>
        </template>
      </el-table-column>
      <el-table-column label="变更类型">
        <template slot-scope="{row}">
          <div>
            {{ row.type }}
          </div>
        </template>
      </el-table-column>
      <el-table-column
        prop="createTime"
        label="变更时间"
        :formatter="formatDate"
      />
      <el-table-column
        prop="createName"
        label="操作人"
      />
    </el-table>
    <div class="explain-pag">
      <Pagination
        v-show="tableConfig.total > 0"
        :total="tableConfig.total"
        :page.sync="pageData.pageNum"
        :limit.sync="pageData.pageSize"
        @pagination="getSkulog"
      />
    </div>
  </el-dialog>
</template>

<script>
import { getSkuLogV2 } from '@/api/product';
import Pagination from '@/components/Pagination';

export default {
  name: 'VieSkuLog',
  components: { Pagination },
  props: {
    skuConfig: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      dialogVisible: true,
      loading: true,
      tableConfig: {
        data: [],
        total: 0,
      },
      pageData: {
        pageSize: 10,
        pageNum: 1,
      },
    };
  },
  mounted() {
    this.getSkulog();
  },
  methods: {
    async getSkulog() {
      try {
        const params = {
          barcode: this.skuConfig.barcode,
          ...this.pageData,
        };
        const res = await getSkuLogV2(params);
        if (res.code === 0) {
          this.tableConfig.data = res.data.list || [];
          this.tableConfig.total = res.data.total;
        } else {
          this.$alert(res.msg, { type: 'error' });
        }
      } catch (e) {
        console.log(e);
      }
      this.loading = false;
    },
    handleClose() {
      this.$emit('update:skuLogDialogVisible', false);
    },
    // 时间格式化
    formatDate(row, column, cellValue) {
      const date = new Date(cellValue);
      const y = date.getFullYear();
      let MM = date.getMonth() + 1;
      MM = MM < 10 ? `0${MM}` : MM;
      let d = date.getDate();
      d = d < 10 ? `0${d}` : d;
      let h = date.getHours();
      h = h < 10 ? `0${h}` : h;
      let m = date.getMinutes();
      m = m < 10 ? `0${m}` : m;
      let s = date.getSeconds();
      s = s < 10 ? `0${s}` : s;
      return `${y}-${MM}-${d} ${h}:${m}:${s}`;
    },
  },
};
</script>

<style scoped lang="scss">
::v-deep   .el-table thead th {
  background: #f9f9f9;
  border: none;

  .cell {
    font-family: PingFangSC, PingFangSC-Regular;
    font-weight: 400;
    text-align: left;
    color: rgba(51, 51, 51, 0.85);
    line-height: 22px;
  }
}

::v-deep   .el-table__body-wrapper {
  font-size: 12px;
  color: #666666;
}
</style>
