import layout from '@/layout';

const marketingManage = {
  path: '/marketingManage',
  name: 'marketingManage',
  component: layout,
  meta: {
    title: '营销管理',
    icon: 'el-icon-s-marketing',
  },
  children: [
    {
      path: '/storeVoucher',
      name: 'storeVoucher',
      component: () => import('@/views/marketing/storeVoucher.vue'),
      meta: { title: '店铺券设置' },
    },
    {
      path: '/collageActivity',
      name: 'collageActivity',
      component: () => import('@/views/marketing/collageActivity.vue'),
      meta: { title: '拼团活动管理' },
    },
    {
      path: '/bulkPurchaseFreeMail',
      name: 'bulkPurchaseFreeMail',
      component: () => import('@/views/freeMail/index'),
      meta: { title: '批购活动管理' },
    },
    {
      path: '/specialPrice',
      name: 'specialPrice',
      component: () => import('@/views/special/index'),
      meta: { title: '特价管理' },
    },
    {
      path: '/customerOperation',
      name: 'customerOperation',
      component: () => import('@/views/marketing/customerOperation.vue'),
      meta: { title: '客户运营' },
    },
    {
      path: '/specialPrice/operate',
      name: 'specialPriceOperate',
      component: () => import('@/views/special/addSpecialPrice'),
      meta: { noCache: true },
      hidden: true,
    },
    {
      path: '/specialPrice/detail',
      name: 'specialPriceDetail',
      component: () => import('@/views/special/specialDetail'),
      meta: { noCache: true },
      hidden: true,
    },
    {
      path: '/specialPrice/statisticInfo',
      name: 'specialPriceStatisticInfo',
      component: () => import('@/views/special/statisticInfo'),
      hidden: true,
    },
    // {
    //   path: process.env.VUE_APP_BASE_API + '/certificate/list',
    //   name: 'certificateList',
    //   meta: {title: '证书管理'}
    // },
    {
      path: '/couponList',
      name: 'couponList',
      component: () => import('@/views/marketing/marketingList.vue'),
      meta: { title: '券管理' },
    },
    {
      path: '/addCoupon',
      name: 'addCoupon',
      component: () => import('@/views/marketing/marketingAdd.vue'),
      meta: { noCache: true },
      hidden: true,
    },
    {
      path: '/couponDetail',
      name: 'couponDetail',
      component: () => import('@/views/marketing/marketingDetail.vue'),
      hidden: true,
    },
    {
      path: '/activemanage',
      name: 'activemanage',
      component: () => import('@/views/active-manage/active-manage.vue'),
      meta: { title: '活动管理' },
    },
    {
      path: '/addActive',
      name: 'addActive',
      component: () => import('@/views/active-manage/add-active.vue'),
      meta: { noCache: true },
      hidden: true,
    },
    {
      path: '/activeDetail',
      name: 'activeDetail',
      component: () => import('@/views/active-manage/active-detail.vue'),
      meta: { noCache: true },
      hidden: true,
    },
    {
      path: '/couponUserList',
      name: 'couponUserList',
      component: () => import('@/views/couponlist/coupon-user-list.vue'),
      meta: { title: '优惠券数据' },
    },
    {
      path: '/couponUserList',
      name: 'couponUserList',
      component: () => import('@/views/couponlist/coupon-user-list.vue'),
      meta: { title: '优惠券数据' },
      hidden: true,
    },
    {
      path: '/collageActivityNew',
      name: 'collageActivityNew',
      component: () => import('@/views/newMarketing/collageActivity.vue'),
      meta: { title: '拼团活动提报' },
      hidden: true
    },
    {
      path: '/groupActivityTheme',
      name: 'groupActivityTheme',
      component: () => import('@/views/marketing/groupActivityTheme.vue'),
      meta: { title: '拼团活动申请' },
      hidden: true,
    },
    {
      path: '/editCollageActivity',
      name: 'editCollageActivity',
      component: () => import('@/views/marketing/editCollageActivity.vue'),
      // meta: { noCache: true },
      meta: { title: '拼团活动编辑' },
      hidden: true,
    },
    {
      path: '/mzPromotion',
      name: 'mzPromotion',
      component: () => import('@/views/marketing/mzPromotion.vue'),
      meta: { title: '满赠促销' },
    },
    {
      path: '/addMzActive',
      name: 'addMzActive',
      component: () => import('@/views/marketing/addMzActive.vue'),
      meta: { title: '新增满赠活动', noCache: true },
      hidden: true,
    },
    {
      path: '/freeGroup',
      name: 'freeGroup',
      component: () => import('@/views/marketing/freeGroup.vue'),
      meta: { title: '随心拼管理', noCache: true },
      hidden: true,
    },
    {
      path: '/groupSalesData',
      name: 'groupSalesData',
      component: () => import('@/views/marketing/groupSalesData.vue'),
      meta: { title: '活动销售数据', noCache: true },
      hidden: true,
    },
    {
      path: '/productIssueSetFreeMail',
      name: 'productIssueSetFreeMail',
      component: () => import("@/views/freeMail/productIssueSet"),
      meta: { title: '商品上架设置' },
      hidden: true
    },
    {
      path: '/auctionProductManage/index',
      name: 'auctionProductManageIndex',
      component: () => import('@/views/auctionProductManage/index.vue'),
      meta: { title: '拼团竞价商品管理' },
    },
  ],
};
export default marketingManage;
