<template>
<div>
    <commonHeader title="催发货管理">
      <div>
        <el-form 
          label-position="right" 
          label-width="10px" 
          style="margin-top:10px;">
          <el-row>
            <el-col :xs="16" :md="12" :lg="7" :xl="7">
              <el-form-item>
                <el-input placeholder="订单编码" size="medium" v-model="urgeDeliveryFrom.orderForm" clearable>
                  <template slot="prepend">订单</template>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :xs="16" :md="12" :lg="7" :xl="7">
              <el-form-item>
                <el-input placeholder="ERP编码/客户名称" size="medium" v-model="urgeDeliveryFrom.custrom" clearable>
                  <template slot="prepend">客户</template>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :xs="16" :md="12" :lg="7" :xl="7">
              <el-form-item>
                <selectList 
                  v-model="urgeDeliveryFrom.urgeDeliveryState" 
                  :list="urgeDeliveryFrom.urgeDeliveryOption"
                  :multiple="true" 
                  label="催发货状态">
                </selectList >
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :xs="16" :md="12" :lg="10" :xl="10">
              <el-form-item>
                <span class="search-title">催发货时间</span>
                 <div class="left-input">
                    <el-date-picker
                        size="medium"
                        v-model="time"
                        type="datetimerange"
                        format="yyyy-MM-dd HH:mm:ss"
                        value-format="yyyy-MM-dd HH:mm:ss"
                        range-separator="至"
                        start-placeholder="开始日期"
                        end-placeholder="结束日期"
                        :default-time="['00:00:00', '23:59:59']"
                        style="width: 100%;"
                        @change="dateOnChange"
                    />
                 </div>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :offset="14" :span="3">
              <el-button style="width: 100%" @click="handleExport">导出</el-button>
            </el-col>
            <el-col :offset="1" :span="3">
              <el-button type="primary" style="width: 100%" @click="handleSelect">查询</el-button>
            </el-col>
          </el-row>
        </el-form>
      </div>
    </commonHeader>
    <div style="padding: 0 30px 10px 23px;">
      <el-tabs @tab-click="tabClick" :value="tagActive">
        <el-tab-pane name="all">
          <template slot="label">
            <p style="margin:0">
              <span style="padding: 0 10px">全部</span>
            </p>
          </template>
        </el-tab-pane>
        <el-tab-pane name="toBeProcessed">
          <template slot="label">
            <p style="margin:0" :title="count">
              <span style="padding: 0 3px">待处理</span>
              &nbsp;
              <!-- v-if="count.toBeProcessedCount > 0" -->
              <span class="badge">{{count.toBeProcessedCount}}</span>
            </p>
          </template>
        </el-tab-pane>
        <el-tab-pane name="inComplaint">
          <template slot="label">
            <p style="margin:0" :title="count">
              <span style="padding: 0 3px">申诉中</span>
              &nbsp;
              <span class="badge">{{count.inComplaintCount}}</span>
            </p>
          </template>
        </el-tab-pane>
      </el-tabs>
      <cfhTable :tableData="tableData" @freshTable="freshPage"></cfhTable>
      <el-pagination
        style="position:relative;left:100%;display:inline-block;transform:translateX(-100%)"
        layout="total, sizes, prev, pager, next, jumper"
        background
        :page-sizes="[10,20, 50, 100]"
        :page-size="queryList.pageSize"
        :total="queryList.total"
        :current-page="queryList.pageNum"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        >
      </el-pagination>
    </div>
    <export-tips
      :change-export="changeExport"
      @handleExoprClose="handleExoprClose"
      @handleChangeExport="handleChangeExport"
    /> 
</div>
</template>

<script>
import cfhTable from './components/cfh-table.vue'
import commonHeader from "./components/common-header.vue"
import selectList from './components/selectList.vue'
import {
  getUrgeDeliveryList,
  exportReminder,
  getCountStatus,
  getStatusType
} from "@/api/urge-delivery"
import exportTips from './components/exportTips.vue'
export default {
  name:'urge-delivery',
  components: {
    commonHeader,
    selectList,
    cfhTable,
    exportTips
  },
  created() {
    this.initDate()
    getStatusType().then(res => {
      if(res.code === 0) {
        this.urgeDeliveryFrom.urgeDeliveryOption = res.result
        this.urgeDeliveryFrom.urgeDeliveryOption.forEach(item => {
          if(item.key === '0') item.value = '请选择'
        })
      }
    })
  },
  activated() {
    this.formHomeInitPage()
  },
  mounted() {
    this.handleSelect()
  },
  data() {
    return {
      urgeDeliveryFrom: {
        endTime: "", // 催发货的时间
        startTime: "",
        custrom: "",
        orderForm: "", // 订单编码/订单ID
        urgeDeliveryState: ["10","31","32"],
        urgeDeliveryOption: []
      },
      time: [],
      queryList: { // 分页查询
        pageNum: 1,
        pageSize: 10,
        total: 0
      },
      tagActive: "toBeProcessed", // tab分页的选中页面
      count: { 
        toBeProcessedCount: 0, // 待处理的统计
        inComplaintCount: 0 // 申诉中的统计
      },
      tableData: [], // 表格数据
      changeExport: false, // 文件下载中心弹框
    }
  },
  computed: {

  },
  methods: {
    formeatDate(date) {
        let year = date.getFullYear()
        let month = date.getMonth() + 1
        let day = date.getDate()
        month = month < 10 ? (`0${month}`) : month;
        day = day < 10 ? (`0${day}`) : day;
        return `${year}-${month}-${day}`
    },
    dateOnChange(val) {
      if (!val) {
        this.urgeDeliveryFrom.startTime = '';
        this.urgeDeliveryFrom.endTime = '';
        val = '';
        this.time = []
      } else if (typeof val[0] === 'string') {
        this.urgeDeliveryFrom.startTime = new Date(val[0]).getTime();
        this.urgeDeliveryFrom.endTime = new Date(val[1]).getTime();
      }
    },
    initDate() { // 初始化时间
      const today = new Date();
      const last30Day = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000)
      this.time = [`${this.formeatDate(last30Day)} 00:00:00`,`${this.formeatDate(today)} 23:59:59`]
      this.dateOnChange(this.time)
    },
    handleExport() {
      exportReminder(this.getParams()).then(res => {
        if (res.code === 0) {
          this.changeExport = true
        } else {
          this.$message.error({
            message: res.message || "服务异常",
            offset: 100
          })
        }
      })
    },
    formHomeInitPage() {
      let {homeEnter} = this.$route.query
      if(homeEnter == 1) {
        this.urgeDeliveryFrom.urgeDeliveryState = ["10","31","32"]
        this.tagActive = 'toBeProcessed'
      }
    },
    getParams() {
      let statusList = this.urgeDeliveryFrom.urgeDeliveryState.filter(item => item !== '0')
      let params = {
        customerSearch: this.urgeDeliveryFrom.custrom,
        reminderTimeStart: this.urgeDeliveryFrom.startTime,
        reminderTimeEnd: this.urgeDeliveryFrom.endTime,
        statusList,
        pageNo: this.queryList.pageNum,
        pageSize: this.queryList.pageSize
      }
      if(/^[^a-zA-Z]*$/.test(this.urgeDeliveryFrom.orderForm)) {
        params.orderId = this.urgeDeliveryFrom.orderForm
      }else {
        params.orderNo = this.urgeDeliveryFrom.orderForm
      }
      return params
    },
    getTabStatus(statusList,target) {
      if(statusList.length === 0) return false
      if(target === '10') {
        const requiredValues = ['10','31', '32'].sort();
        const providedValues = [...new Set(statusList)].sort();
        return JSON.stringify(requiredValues) === JSON.stringify(providedValues);
      }
      return statusList.every(val => val == target)
    },
    handleSelect() {
      const params = this.getParams()
      if(this.getTabStatus(params.statusList,'10')) {
        this.tagActive = 'toBeProcessed'
      }else if(this.getTabStatus(params.statusList,'30')) {
        this.tagActive = 'inComplaint'
      }else {
        this.tagActive = 'all'
      }
      const loading = this.popLoding()
      getUrgeDeliveryList(params).then(res => {
        if(res.code === 0) {
          this.tableData = res.result.list || []
          this.tableData.pageNum = res.result.pageNum
          this.tableData.pageSize = res.result.pageSize
          this.queryList = {
            ...this.queryList,
            total: res.result.total
          }
        }else {
          this.$message.error(res.msg || res.errMsg || "服务异常")
        }
      }).finally(() => {
        loading.close()
      })
      getCountStatus(params).then(res => {
        if(res.code === 0) {
          let countArr = res.result || []
          countArr.forEach((item) => {
            if(item.status === 10) {
              this.count.toBeProcessedCount = item.count
            }
            if(item.status === 30) {
              this.count.inComplaintCount = item.count
            }
          })
        }
      })
      // 刷新路由边上的小红点
      window.flashUrgeCount()
    },
    tabClick(tab) {
      if(tab.name === 'toBeProcessed') {
        this.urgeDeliveryFrom.urgeDeliveryState = ["10","31","32"]
      }
      if(tab.name === 'inComplaint') {
        this.urgeDeliveryFrom.urgeDeliveryState = ["30"]
      }
      if(tab.name === 'all') {
        this.urgeDeliveryFrom.urgeDeliveryState = []
      }
      this.handleSelect()
    },
    handleCurrentChange(pageNum) {
			this.queryList.pageNum = pageNum;
			this.handleSelect();
		},
    handleSizeChange(pageSize) {
			this.queryList.pageSize = pageSize;
			this.handleSelect();
		},
    popLoding() { // 加载遮罩
      return this.$loading({
        lock: true,
        text: 'Loading',
        spinner: 'el-icon-loading',
        background: 'rgba(255,255,255, 0.8)'
      })
    },
    handleExoprClose() {
      this.changeExport = false
    },
    handleChangeExport(info) {
      this.changeExport = false
      if (info === 'go') {
        const path = '/downloadList'
        window.openTab(path)
      }
    },
    freshPage(index) {
      // this.handleSelect()
      const params = this.getParams()
      getCountStatus(params).then(res => {
        if(res.code === 0) {
          let countArr = res.result || []
          countArr.forEach((item) => {
            if(item.status === 10) {
              this.count.toBeProcessedCount = item.count
            }
            if(item.status === 30) {
              this.count.inComplaintCount = item.count
            }
          })
        }
      })
      // 刷新路由边上的小红点
      window.flashUrgeCount()
      this.tableData[index].status = 30
    }
  }
}
</script>

<style lang="scss" scoped>
.search-title {
	display: table-cell;
	padding: 0 20px;
	text-align: center;
	border: 1px solid #dcdfe6;
	height: 30px;
	line-height: 30px;
	vertical-align: middle;
	border-right: none;
	border-radius: 4px 0 0 4px;
	color: #909399;
	white-space: nowrap;
	background-color: #F5F7FA;
}
.left-input {
	display: table-cell;
	width: 100%;
	line-height: 24px;
	.el-date-editor {
		border-top-left-radius: 0px;
		border-bottom-left-radius: 0px;
        width: 100%;
	}
}
.badge {
	background: #ff4d4f;
	border-radius: 11px;
	color: #fff;
	font-size: 12px;
	margin-left: 2px;
	padding: 0 8px;
}
</style>