<template>
  <div>
    <el-table
      :data="detailList"
      border
      show-summary
      row-class-name="tableRowClassName"
      style="width: 100%"
      :summary-method="getSummaries"
    >
      <el-table-column label="序号" width="50" type="index" />
      <el-table-column label="商品ERP编码" prop="erpCode" />
      <el-table-column label="商品编码" prop="barcode" />
      <el-table-column label="sku编码" prop="skuId" />
      <el-table-column label="商品名称" prop="productName">
        <template slot-scope="scope">
          <div>{{ scope.row.productName }}</div>
          <div class="btnText" @click="btnClick(scope.row)">查看交易快照</div>
        </template>
      </el-table-column>
      <el-table-column label="规格（型号）" prop="spec" />
      <el-table-column label="生产厂家" prop="manufacturer" />
      <el-table-column label="参与活动" prop="activities">
        <template slot-scope="scope">
          <div>
            <div v-if="scope.row.activities && scope.row.activities.length > 0">
              <span v-for="item in scope.row.activities" :key="item">
                <span>{{ item }}</span>
                <!-- <span v-if="item.indexOf('拼团')>-1">{{ item }}</span>
                <span v-else class="btnText" @click="jumpPage(item)">{{ item }}</span> -->
              </span>
            </div>
            <div v-else>-</div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="单价" prop="productPrice">
        <template slot-scope="scope">
          <div>原价：{{ scope.row.productPrice }}</div>
          <div>采购价：{{ scope.row.productPurchasePrice }}</div>
        </template>
      </el-table-column>
      <el-table-column label="采购数量" prop="productAmount" />
      <el-table-column
        v-if="activeOrderStatus !== '2' && activeOrderStatus !== '3' && activeOrderStatus !== '4' && activeOrderStatus !== '91'"
        label="应发货数量"
        prop="canSendSkuCount"
      >
        <template slot-scope="scope">
          <div :class="(activeOrderStatus === '33' || activeOrderStatus === '2' || activeOrderStatus === '3')&& scope.row.canSendSkuCount < scope.row.productAmount ? 'classRed' : ''">
            {{ scope.row.canSendSkuCount }}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="商品金额" prop="subTotal" />
      <el-table-column label="店铺优惠" prop="shopVoucherAmount">
        <template slot-scope="scope">
          {{ scope.row.shopVoucherAmount }}
          <el-tooltip placement="top" v-if="scope.row.shopVoucherAmount">
            <div slot="content">
              <div v-for="item in scope.row.shopDiscountMsgList" :key="item">{{ item }}</div>
            </div>
            <i class="el-icon-warning-outline"></i>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column label="平台优惠" prop="crossPlatformVoucherAmount">
        <template slot-scope="scope">
          {{ scope.row.crossPlatformVoucherAmount }}
          <el-tooltip placement="top" v-if="scope.row.crossPlatformVoucherAmount">
            <div slot="content">
              <div v-for="item in scope.row.platformDiscountMsgList" :key="item">{{ item }}</div>
            </div>
            <i class="el-icon-warning-outline"></i>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column
        v-if="activeOrderStatus === '2' || activeOrderStatus === '3' || activeOrderStatus === '4' || activeOrderStatus === '91'"
        label="总优惠"
        prop="discountAmount"
      />
      <el-table-column label="实付金额" prop="realPayAmount" />
      <el-table-column
        v-if="activeOrderStatus === '2' || activeOrderStatus === '3'"
        label="实际发货数量"
        prop="batchConsNumTotal"
      >
      <template slot-scope="scope">
          <div :class="(activeOrderStatus === '33' || activeOrderStatus === '2' || activeOrderStatus === '3')&& scope.row.batchConsNumTotal < scope.row.productAmount ? 'classRed' : ''">
            {{ scope.row.batchConsNumTotal }}
          </div>
        </template>
      </el-table-column>
      <el-table-column
        v-if="activeOrderStatus === '2' || activeOrderStatus === '3'"
        label="实际发货金额"
        prop="batchPriceTotal"
      />
      <el-table-column
        v-if="activeOrderStatus !== '4' && activeOrderStatus !== '91' && activeOrderStatus !== '10'"
        label="退款数量"
        prop="refundProductQuantity"
      />
      <el-table-column
        v-if="activeOrderStatus !== '4' && activeOrderStatus !== '91' && activeOrderStatus !== '10'"
        label="退款金额"
        prop="refundProductMoney"
      />
    </el-table>
    <trading-snapshot
      v-if="snapshotVisible"
      :dialog-visible="snapshotVisible"
      :sku-id="skuId"
      :order-no="orderNo"
      @cancelDialog="cancelDialog()"
    />
  </div>
</template>
<script>
import tradingSnapshot from './tradingSnapshot.vue';
export default {
  name: 'OrderDetail',
  components: { tradingSnapshot },
  props: {
    detailList: {
      type: Array,
      default: () => [],
    },
    activeOrderStatus: {
      type: String,
      default: '',
    },
    orderNo: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      snapshotVisible: false,
      skuId:''
    };
  },
  created() {},
  methods: {
    formatNum(f, digit) {
      const m = Math.pow(10, digit);
      return Math.round(f * m, 10) / m;
    },
    getSummaries(param) {
      const { columns, data } = param;
      const sums = [];
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = '合计';
        } else if (column.property === 'productAmount' || column.property === 'canSendSkuCount' || column.property === 'subTotal' || column.property === 'shopVoucherAmount' || column.property === 'crossPlatformVoucherAmount' || column.property === 'realPayAmount' || column.property === 'refundProductQuantity' || column.property === 'refundProductMoney' || column.property === 'batchConsNumTotal' || column.property === 'batchPriceTotal' || column.property === 'discountAmount') {
          const values = data.map(item => Number(item[column.property]));
          if (!values.every(value => isNaN(value))) {
            sums[index] = values.reduce((t, c) => this.formatNum(t + c, 10), 0);
          } else {
            sums[index] = '';
          }
        } else {
          sums[index] = '';
        }
      });
      return sums;
    },
    jumpPage(id) {
      const ary = id.split(' ');
      const idStr = ary[1];
      this.$emit('setLocal');
      this.$router.push({ path: '/specialPrice/detail', query: { promotionId: idStr } });
    },
    btnClick(row) {
      this.skuId = row.skuId
      this.snapshotVisible = true;
    },
    cancelDialog() {
      this.snapshotVisible = false;
    },
  },
};
</script>
<style lang="scss" scoped>
.btnText {
  color: #4184d5;
  cursor: pointer;
}
.classRed{
  color: red;
  font-weight: bold;
}
</style>
