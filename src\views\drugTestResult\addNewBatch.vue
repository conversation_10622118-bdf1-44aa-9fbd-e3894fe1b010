<template>
  <common-header :showFold="false" v-loading="loading">
    <div class="radio-box" :class="check === 1 ? 'radio-box-active' : ''">
      <div v-if="check === 2" class="mask" @click="radioChange(1)"></div>
      <el-radio style="padding: 15px;" :value="check" :label="1">
        <span style="margin-right: 5px;">规则一：选择商品录入批号</span>
        <span style="color: red;">支持同一商品编码多批号录入</span>
      </el-radio>
      <div class="content" v-loading="data.loading">
        <el-form style="margin-top:10px;">
					<el-row>
						<el-col :xs="16" :md="12" :lg="6" :xl="6">
							<el-form-item>
                <p style="display: flex; gap: 10px;">
                  <el-input placeholder="商品编码 / ERP编码" size="small" v-model="searchCode" clearable>
                    <template slot="prepend">商品编码</template>
                  </el-input>
                  <el-button type="primary" size="small" @click="search">查询</el-button>
                </p>
              </el-form-item>
						</el-col>
          </el-row>
          <el-row>
            <el-col :xs="16" :md="12" :lg="6" :xl="6">
							<el-form-item label="通用名称：">
                <span>{{ data.commonName }}</span>
              </el-form-item>
						</el-col>
            <el-col :xs="16" :md="12" :lg="6" :xl="6">
							<el-form-item label="商品名称：">
                <span>{{ data.productName }}</span>
              </el-form-item>
						</el-col>
            <el-col :xs="16" :md="12" :lg="6" :xl="6">
							<el-form-item label="规格：">
                <span>{{ data.spec }}</span>
              </el-form-item>
						</el-col>
            <el-col :xs="16" :md="12" :lg="6" :xl="6">
							<el-form-item label="批准文号：">
                <span>{{ data.approvalNumber }}</span>
              </el-form-item>
						</el-col>
            <el-col :xs="16" :md="12" :lg="6" :xl="6">
							<el-form-item label="生产厂家：">
                <span>{{ data.manufacturer }}</span>
              </el-form-item>
						</el-col>
          </el-row>
          <el-row>
            <el-col :xs="16" :md="12" :lg="8" :xl="8">
              <p style="display: flex;align-items: center;">
                <el-input style="width: 300px;margin-right: 5px;" placeholder="请输入批号" size="small" :value="form.batchCode" @input="valueInput($event,/^(([a-zA-Z0-9]|[a-zA-Z0-9];)+[a-zA-Z0-9]*)?$/,'form.batchCode')" clearable>
                  <template slot="prepend">批号</template>
                </el-input>
                <span style="color:red;">
                  如有多个批号，请以“ ; ”隔开。
                </span>
              </p>
            </el-col>
          </el-row>
        </el-form>
        <div v-if="check === 1" style="margin: 10px;display: flex;justify-content: center;">
          <el-button size="small" @click="close">取消</el-button>
          <el-button type="primary" size="small" @click="submit">确认提交</el-button>
        </div>
      </div>
    </div>
    <div class="radio-box" :class="check === 2 ? 'radio-box-active' : ''">
      <div v-if="check === 1" class="mask" @click="radioChange(2)"></div>
      <el-radio style="padding: 15px;" :value="check" :label="2">
        <span style="margin-right: 5px;">规则二：批量录入批号信息</span>
        <span style="color: red;">支持不同商品编码多批号录入</span>
      </el-radio>
      <div class="content">
        <p style="display: flex;align-items: center;">
          <span>下载模板：</span>
          <el-link :underline="false" type="primary" @click="downloadTemplate">批量录入批号模板</el-link>
        </p>
        <div style="display: flex; gap:15px;">
          <div style="flex-shrink: 0;">
            <iFile :multiple="false" :allowType="['.xlsx', '.xls']" :maxCount="maxCount" :allowSize="[0,15000000]" v-model="form.fileList" :bucket="3">
              <!-- <span>请优先上传PDF，支持pdf和图片格式，包括jpg、png、jpeg格式</span> -->
            </iFile>
          </div>
          <div style="flex-grow: 1;overflow-x: auto;">
            <div style="min-width: 700px;">
              <i-img :maxCount="maxCount" :isEdit="true" v-model="form.fileList" :column="3"></i-img>
            </div>
          </div>
        </div>
        <div v-if="check === 2" style="margin: 10px;display: flex;justify-content: center;">
          <el-button size="small" @click="close">取消</el-button>
          <el-button type="primary" size="small" @click="multipleSubmit">确认提交</el-button>
        </div>
      </div>
    </div>
    <el-dialog title="提示" :visible.sync="dialog.result" append-to-body>
      <p>提交成功，是否继续新增批号。</p>
      <template slot="footer">
        <el-button size="small" @click="dialog.result = false;close()">返回药检报告</el-button>
        <el-button size="small" type="primary" @click="dialog.result = false;">继续添加批号</el-button>
      </template>
    </el-dialog>
    <el-dialog title="提示" :visible.sync="dialog.error" append-to-body>
      <p>校验失败，存在错误数据</p>
      <p>请下载错误说明文件并修改数据，重新上传。</p>
      <template slot="footer">
        <el-button size="small" @click="dialog.error = false;">取消</el-button>
        <el-button size="small" type="primary" @click="dialog.error = false;clear();$router.push('/downloadList');">前往下载中心</el-button>
      </template>
    </el-dialog>
  </common-header>
</template>
<script>
import CommonHeader from '../afterSaleManager/components/common-header.vue'
import iFile from '../qualificationOnline/components/i-file.vue'
import iImg from '../qualificationOnline/components/i-img.vue'
import { qualificationSearch, addBatchCodeByGood, addBatchCodeByFile, templateFile } from '../../api/qualificationOnline/index'
export default {
  name: 'addNewBatch',
  components: {
    CommonHeader,
    iFile,
    iImg
  },
  created() {
    window.clearData['/addNewBatch'] = () => {
      this.clear();
    }
  },
  data() {
    return {
      check: 1,
      maxCount: 1,
      searchCode: '',
      form: {
        fileList: [],
        batchCode: '',
        barcode: '',
        erpCode: '',
      },
      data: {
        commonName: '',
        productName: '',
        spec: '',
        approvalNumber: '',
        manufacturer: '',
        loading: false,
      },
      dialog: {
        result: false,
        error: false,
      },
      loading: false,
    }
  },
  methods: {
    downloadTemplate() {
      templateFile().then(res => {
        const blob = new Blob([res]);

        const url = URL.createObjectURL(blob)
        const a = document.createElement('a');
        a.href = url;
        a.target = "_blank";
        a.download = "批量录入批号模板.xlsx";
        a.click();
        URL.revokeObjectURL(url);
      })
    },
    radioChange(check) {
      this.check = check;
      if (this.check === 1) {
        this.form.fileList = [];
      } else if (this.check === 2) {
        this.form.erpCode = '';
        this.form.barcode = '';
        this.form.batchCode = '';
        this.data = {
          commonName: '',
          productName: '',
          spec: '',
          approvalNumber: '',
          manufacturer: '',
          loading: false,
        }
      }
    },
    search() {
      if (!this.searchCode) {
        this.$message.error('请输入商品编码!');
        return;
      }
      this.data.loading = true;
      qualificationSearch({
        code: this.searchCode,
        pageNum: 1,
        pageSize: 10
      }).then(res => {
        if (res.code === 0 && res.result.list.length > 0) {
          if (this.check === 2) return;
          for (const key in this.data) {
            this.data[key] = res.result.list[0][key];
          }
          this.form.barcode = res.result.list[0].barcode;
          this.form.erpCode = res.result.list[0].erpCode;
        } else {
          this.$message.error('未查询到商品信息');
        }
      }).finally(() => {
        this.data.loading = false;
      })
    },
    multipleSubmit() {
      if (!this.form.fileList.length > 0) {
        this.$message.error('请上传文件')
        return
      }
      this.loading = true;
      addBatchCodeByFile({ urlString: this.form.fileList[0].url }).then(res => {
        if (res.code === 0) {
          if (res.data == 'error') {
            //弹框跳下载中心
            this.dialog.error = true;
            return
          }
          this.dialog.result = true;
        } else {
          this.$message.error(res.message)
        }
      }).finally(() => {
        this.loading = false;
      })
    },
    submit() {
      if (!this.form.erpCode && !this.form.barcode) {
        this.$message.error('请选择商品');
        return;
      }
      if (!this.form.batchCode) {
        this.$message.error('请输入批号');
        return;
      }
      this.loading = true;
      addBatchCodeByGood({
        erpCode: this.form.erpCode || '',
        barcode: this.form.barcode || '',
        batchCode: this.form.batchCode,
      }).then(res => {
        if (res.code === 0) {
          this.dialog.result = true;
        } else {
          this.$message.error(res.message)
        }
      }).finally(() => {
        this.loading = false;
      })
    },
    clear() {
      this.form.fileList = [];
      this.batchCode = '';
      this.barcode = '';
      this.erpCode = '';
      this.data = {
        commonName: '',
        productName: '',
        spec: '',
        approvalNumber: '',
        manufacturer: '',
        loading: false,
      }
      this.dialog.result = false;
    },
    valueInput(val, regexp, key) {
      const keyList = key.split('.');
      let param = this;
      keyList.forEach((key, index) => {
        if (index + 1 < keyList.length) {
          param = param[key]
        }
      })
      console.log(val);

      console.log(regexp.test(val));

      if (regexp.test(val)) {
        param[keyList[keyList.length - 1]] = val;
      }
    },
    close() {
      window.closeTab(this.$route.fullPath, true);
      window.openTab('/drugTestResultManage')
    }
  }
}
</script>
<style scoped>
.content {
  padding: 15px;
  background-color: rgb(248, 248, 248);
  border-radius: 5px;
}
.radio-box {
  position: relative;
  border: transparent dotted 1px;
  border-radius: 5px;
  overflow: hidden;
  margin: 10px 0;
  transition: all 0.3s;
}
.radio-box .mask {
  position: absolute;
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
  z-index: 2;
  cursor: pointer;
}
.radio-box:hover {
  border: rgb(179, 179, 179) solid 1px;
}
.radio-box-active {
  border: #5a9ae7 solid 1px !important;
}
</style>
