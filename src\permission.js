import router, { resetRouter } from './router';
import store from './store';

const whiteList = ['/h5Dashboard', '/dataAnalysis'];
let count = 3;
router.beforeEach(async (to, from, next) => {
  if (window.tw) {
    if (window.setPvLoad && to.path !== from.path) {
      window.tw.resetPv();
    } else {
      window.setPvLoad = true;
    }
  }
  const { routers } = store.state.permission;
  if (routers && routers.length > 0) {
    next();
  } else {
    if (!whiteList.includes(to.path) && count > 0) {
      count -= 1;
      store.dispatch('permission/generateRoutes')
        .then((roles) => {
          if (roles && roles.length > 0) {
            resetRouter();
            roles.forEach((item) => {
              // router.options.routes.push(item);
              router.addRoute(item);
            });
          }
        });
    }
    next();
  }
});

router.afterEach(() => {
});

//获取红点提示
store.dispatch('permission/getChangeRedCount')
store.dispatch('permission/getAfterSalesOrderCount')