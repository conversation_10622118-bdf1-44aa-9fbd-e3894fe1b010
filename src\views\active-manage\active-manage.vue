<template>
  <div class="home">
    <div class="serch">
      <el-row
        type="flex"
        align="middle"
      >
        <span class="sign" />
        <div>查询条件</div>
      </el-row>
    </div>
    <el-row
      class="condition searchMy"
      style="padding:0 20px;"
    >
      <el-form
        ref="ruleForm"
        :inline="true"
        :model="ruleForm"
        :rules="rules"
        size="small"
      >
        <el-form-item
          prop="name"
        >
          <el-input
            v-model="ruleForm.name"
          >
            <template slot="prepend">
              券活动名称
            </template>
          </el-input>
        </el-form-item>
        <el-form-item
          prop="activityId"
        >
          <el-input
            v-model="ruleForm.activityId"
          >
            <template slot="prepend">
              券活动ID
            </template>
          </el-input>
        </el-form-item>
        <el-form-item
          prop="couponActivityType"
        >
          <span
            class="search-title"
          >活动类型</span>
          <el-select
            v-model="ruleForm.couponActivityType"
            placeholder="请选择活动类型"
          >
            <el-option
              label="全部"
              :value="null"
            />
            <el-option
              label="领券活动"
              value="1"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          prop="creatTime"
        >
          <span
            class="search-title"
          >创建时间</span>
          <div style="display: table-cell; line-height: 24px">
            <el-date-picker
              v-model.trim="ruleForm.creatTime"
              type="datetimerange"
              format="yyyy-MM-dd HH:mm:ss"
              range-separator="至"
              value-format="timestamp"
              :default-time="['00:00:00', '23:59:59']"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            />
          </div>
        </el-form-item>
        <el-form-item
          prop="logicalStatus"
        >
          <span
            class="search-title"
          >领券状态</span>
          <el-select
            v-model="ruleForm.logicalStatus"
            placeholder="请选择活动状态"
          >
            <el-option
              label="全部"
              :value="null"
            />
            <el-option
              label="未开始"
              value="0"
            />
            <el-option
              label="进行中"
              value="1"
            />
            <el-option
              label="已结束"
              value="-1"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-input v-model="ruleForm.couponId" placholder="请输入">
            <template slot="prepend">
              券ID
            </template>
          </el-input>
        </el-form-item>
        <el-form-item class="searchBtn" style="text-align: right;padding-right: 20px">
          <el-button
            type="primary"
            @click="submitForm('ruleForm')"
          >
            查询
          </el-button>
          <el-button @click="resetForm('ruleForm')">
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </el-row>
    <el-row class="actlist">
      <div class="serch">
        <el-row
          type="flex"
          align="middle"
        >
          <span class="sign" />
          <div>活动列表</div>
        </el-row>
      </div>
      <el-button
        v-permission="['marketing_activity_add']"
        class="creatActive"
        type="primary"
        size="small"
        @click="creatActive"
      >
        新建领券活动
      </el-button>
      <el-table
        v-loading="isLoading"
        :data="tableData"
        stripe
        border
        style="width: 100%"
        :header-cell-style="{ background: '#f9f9f9', color: '#666666' }"
      >
        <el-table-column
          fixed
          prop="name"
          label="券活动名称"
          width="150"
        />
        <el-table-column
          prop="id"
          label="券活动ID"
          width="100"
        >
          <template slot-scope="scope">
            <span>{{scope.row.id}}</span>
          </template>
        </el-table-column>
        <el-table-column
          width="150"
          prop="activityId"
          label="关联优惠券"
        >
          <template slot-scope="scope">
            <el-button
              v-for="id in scope.row.activityIds"
              :key="id"
              type="text"
              @click="toConectuYHQ(id)"
            >
              {{ id }}
            </el-button>
          </template>
        </el-table-column>
        <el-table-column
          width="150"
          prop="prMarketCustomerGroupId"
          label="关联人群"
        >
          <template slot-scope="scope">
            <div v-if="scope.row.prMarketCustomerGroupId===-1">
              全部人群
            </div>
            <el-button
              v-else
              type="text"
              @click="topeopel(scope.row.prMarketCustomerGroupId)"
            >
              {{ scope.row.prMarketCustomerGroupId }}
            </el-button>
          </template>
        </el-table-column>
        <el-table-column
          width="100"
          prop="couponActivityType"
          label="券活动类型"
        >
          <template slot-scope="scope">
            <div v-if="scope.row.couponActivityType === 1">
              领券活动
            </div>
            <div v-if="scope.row.couponActivityType === 2">
              发券活动
            </div>
            <div v-if="!scope.row.couponActivityType">
              -
            </div>
          </template>
        </el-table-column>
        <el-table-column
          prop="city"
          label="创建时间"
          width="180"
        >
          <template slot-scope="scope">
            <div v-if="scope.row.createTime">
              {{ scope.row.createTime | handleTime }}
            </div>
          </template>
        </el-table-column>
        <el-table-column
          prop="city"
          label="领券时间"
          width="320"
        >
          <template slot-scope="scope">
            <div v-if="scope.row.createTime">
              {{  scope.row.startTime | handleTime }} - {{scope.row.endTime | handleTime}}
            </div>
          </template>
        </el-table-column>
        <el-table-column
          prop="logicalStatusName"
          label="领券状态"
        />
        <el-table-column
          fixed="right"
          label="操作"
          align="center"
          width="120"
        >
          <template slot-scope="scope">
            <!-- <div v-if="scope.row.logicalStatus === 0">
              <el-button
                type="text"
                size="small"
                @click="handleEdit(scope.row.id)"
              >
                编辑
              </el-button>
              |
              <el-button
                type="text"
                size="small"
                style="margin-left: 0"
                @click="cancelConfirm('确认下线活动吗？', scope.row.id)"
              >
                下线
              </el-button>
            </div> -->
            <div>
              <el-button
                type="text"
                size="small"
                style="margin-left: 0"
                @click="seeDetail(scope.row)"
              >
                查看
              </el-button>
              |
              <el-button
                v-permission="['marketing_activity_down']"
                type="text"
                size="small"
                style="margin-left: 0"
                @click="cancelConfirm('确认下线活动吗？', scope.row.id)"
              >
                下线
              </el-button>
            </div>
            <!-- <div v-if="scope.row.logicalStatus == -1">
              <el-button
                type="text"
                size="small"
                style="margin-left: 0"
                @click="seeDetail(scope.row)"
              >
                查看
              </el-button>
            </div> -->
          </template>
        </el-table-column>
        <template slot="empty">
          <div class="noData">
            <p class="img-box">
              <img
                src="@/assets/image/marketing/noneImg.png"
                alt=""
              >
            </p>
            <p>暂无数据</p>
          </div>
        </template>
      </el-table>
      <div
        v-if="page.totalPage != 0"
        class="pagination-container"
      >
        <div class="pag-text">
          共 {{ page.totalCount }} 条数据，每页{{ 10 }}条，共{{
            Math.ceil(page.totalCount / 10)
          }}页
        </div>
        <el-pagination
          background
          :current-page.sync="page.pageNum"
          :page-sizes="[10]"
          prev-text="上一页"
          next-text="下一页"
          layout="sizes, prev, pager, next, jumper"
          :total="page.totalCount"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-row>
    <selectPepel ref="crowdialog" />
  </div>
</template>

<script>
import { getActivitylist, couponOffline } from '@/api/activity/index';
import selectPepel from './components/selectPepel';

export default {
  name: 'Activemanage',
  components: { selectPepel },
  filters: {
    handleTime(date) {
      return date
        ? new Date(date + 8 * 3600 * 1000)
          .toJSON()
          .substr(0, 19)
          .replace('T', ' ')
        : '';
    },
  },

  data() {
    return {
      isLoading: false,
      ruleForm: {
        name: '',
        logicalType: null,
        couponActivityType: null,
        creatTime: [],
        activityId: '',
        couponId: ''
      },
      rules: {
        name: [],
        logicalStatus: '',
      },
      tableData: [],
      page: {
        pageNum: 1,
        pageSize: 10,
        totalPage: null,
        totalCount: null,
      },
    };
  },
  created() {
    this.searchTabaleData();
  },
  activated() {
    if (this.$route.query.refresh) {
      this.searchTabaleData();
    }
  },
  methods: {
    tranferFunction(val) {
      console.log(val);
    },
    // 点击优惠券的东西
    toConectuYHQ(id) {
      this.$router.push({
        path: 'couponDetail',
        query: { id },
      });
    },
    topeopel(id) {
      this.$refs.crowdialog.open(id);
    },
    creatActive() {
      this.$router.push({ path: 'addActive' });
    },
    cancelConfirm(text, param) {
      this.$confirm(text, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(async () => {
          await this.handleQuery(param);
          this.searchTabaleData();
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消',
          });
        });
    },
    handleQuery(id) {
      return couponOffline({ id }).then((res) => {
        if (res.status === 'success') {
          this.$message({
            type: 'success',
            message: '下线成功!',
          });
        } else {
          this.$message({
            type: 'error',
            message: res.msg,
          });
        }
      });
    },
    handleEdit(id) {
      this.$router.push({
        path: 'addActive',
        query: { id },
      });
    },
    submitForm() {
      this.page.pageNum = 1;
      this.searchTabaleData();
    },
    resetForm(formName) {
      this.$refs[formName].resetFields();
      this.ruleForm.couponId = ''
      this.page.pageNum = 1;
      this.searchTabaleData();
    },
    searchTabaleData() {
      this.isLoading = true;
      const queryData = {
        name: this.ruleForm.name,
        activityId: this.ruleForm.activityId,
        logicalStatus: this.ruleForm.logicalStatus,
        couponActivityType: this.ruleForm.couponActivityType
          ? Number(this.ruleForm.couponActivityType)
          : null,
        startTime:
          this.ruleForm.creatTime.length > 0
            ? this.ruleForm.creatTime[0]
            : null,
        endTime:
          this.ruleForm.creatTime.length > 0
            ? this.ruleForm.creatTime[1]
            : null,
        pageNum: this.page.pageNum,
        pageSize: 10,
        couponId:this.ruleForm.couponId
      };
      getActivitylist(queryData).then((res) => {
        this.isLoading = false;
        if (res.code === 1000) {
          const { list, totalPage, totalCount, pageNo } = res.data;
          this.tableData = list;
          this.page.pageNum = pageNo;
          if (totalPage) {
            this.page.totalPage = totalPage;
            this.page.totalCount = totalCount;
          } else {
            this.page.totalPage = 0;
          }
        } else {
          // this.$message.error(res.msg);
        }
      });
    },
    seeDetail(row) {
      this.$router.push({
        path: '/activeDetail',
        query: { id: row.id },
      });
    },
    handleSizeChange(val) {
      console.log(`每页 ${val} 条`);
    },
    handleCurrentChange(val) {
      console.log(`当前页: ${val}`);
      this.page.pageNum = val;
      this.searchTabaleData();
    },
  },
};
</script>
<!-- Add "scoped" attribute to limit CSS to this component only -->
<style lang="scss">
.pagination-container {
  background: #fff;
  padding: 32px 16px;
}
.pag-text {
  vertical-align: middle;
  float: left;
  font-size: 12px;
  color: #999999;
  padding-top: 8px;
}
</style>
<style lang="scss" scoped>
::v-deep  .el-form-item__label {
  padding: 0;
}
::v-deep  .el-input__inner {
  border-radius: 0;
}
.serch {
  padding: 15px 20px;
  font-weight: 700;

  .sign {
    display: inline-table;
    width: 3px;
    height: 13px;
    background: linear-gradient(360deg, #1d69c4 1%, #8bbdfc 99%);
    border-radius: 2px;
    margin-right: 8px;
  }
}

.searchBtn {
  float: right;
}

::v-deep  .el-divider--horizontal {
  margin: 0;
}

.creatActive {
  margin-bottom: 10px;
}

.actlist {
  padding: 0 20px;

  .serch {
    padding: 15px 0px;
    font-weight: 700;
  }
}

.el-pagination {
  margin-top: 10px;
  text-align: right;
}

::v-deep  .el-pagination .el-pagination__total {
  float: left;
}
</style>

<style lang="scss" scoped>
.search-btn-custom {
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  width: 606px;
}
::v-deep   .my-label .el-form-item__label {
  border: 1px solid #d9d9d9;
  border-radius: 4px 0px 0px 4px;
  padding: 0 13px 0 9px;
  height: 34px;
  vertical-align: bottom;
  border-right: 0;
  font-size: 12px;
}
::v-deep   .my-label .el-input__inner {
  border-radius: 0px 4px 4px 0px;
}

::v-deep   .el-range-editor--small.el-input__inner {
  height: 34px;
}
::v-deep   .el-input--small .el-input__inner {
  height: 34px;
  line-height: 34px;
}
.searchMy ::v-deep  .el-form-item__label {
  margin-left: 20px;
  padding: 0;
}
.searchMy ::v-deep  .el-input__inner {
  border-radius: 0 4px 4px 0;
}
.searchMy ::v-deep  .el-date-editor{
  width: 100%;
}
.search-title {
  display: table-cell;
  padding: 0 10px;
  text-align: center;
  border: 1px solid #dcdfe6;
  height: 30px;
  line-height: 30px;
  vertical-align: middle;
  border-right: none;
  border-radius: 4px 0 0 4px;
  color: #333333;
  white-space: nowrap;
}
.searchMy ::v-deep  .el-select {
  display: table-cell;
  width: 100%;
}
.searchMy ::v-deep  .el-form-item__content{
  width: 100%;
}
.searchMy ::v-deep  .el-form-item--small.el-form-item{
  margin-bottom: 10px;
  width: 32%;
}
.searchMy ::v-deep  .el-form-item--small .el-form-item__content{
  line-height: 30px;
  width: 100%;
}
.searchMy ::v-deep  .el-input-group__prepend {
  background: none;
  color: #333333;
  padding: 0 10px;
}
.Fsearch {
  padding: 0px 20px 10px;
  font-weight: 500;
  .searchMsg {
    font-weight: 700;
    width: 200px;
  }
  .sign {
    display: inline-table;
    width: 3px;
    height: 13px;
    background: linear-gradient(360deg, #1d69c4 1%, #8bbdfc 99%);
    border-radius: 2px;
    margin-right: 8px;
  }
}
::v-deep   .cell .el-button + .el-button{
  margin-left: 0;
}
</style>
