<!--created by <PERSON><PERSON><PERSON><PERSON><PERSON> in 2021/3/13
 客户列表 dialog
-->
<template>
  <el-dialog
    v-el-drag-dialog
    :title="title"
    :visible="visible"
    width="60%"
    :destroy-on-close="true"
    :before-close="handleDialogClose"
    :append-to-body="appendToBody"
    @open="open"
  >
    <el-form
      ref="form"
      :model="formData"
      size="small"
    >
      <el-row :gutter="20">
        <el-col :span="6">
          <el-form-item prop="merchantIdStr">
            <el-input
              v-model.trim="formData.merchantIdStr"
              placeholder="请输入"
            >
              <div slot="prepend">
                药店编号
              </div>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item prop="realName">
            <el-input
              v-model="formData.realName"
              placeholder="请输入"
            >
              <template slot="prepend">
                药店名称
              </template>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item prop="mobile">
            <el-input
              v-model="formData.mobile"
              placeholder="请输入"
            >
              <template slot="prepend">
                手机号
              </template>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item style="float: right;">
            <el-button
              type="primary"
              size="mini"
              @click="search"
            >
              查询
            </el-button>
            <el-button
              size="mini"
              @click="resetForm"
            >
              重置
            </el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <span v-if="customerExecuteType === customerDialogType.viewExport">
      <el-button
        :loading="deleteSelectionLoading"
        type="primary"
        @click="removeSelection"
      >移除</el-button>
      <el-button
        :loading="deleteAllLoading"
        type="primary"
        @click="removeAll"
      >全部移除</el-button>
    </span>
    <el-table
      v-loading="loading"
      max-height="397"
      :data="responseData.list"
      stripe
      style="width: 100%;"
      @selection-change="handleSelectionChange"
    >
      <el-table-column
        v-if="customerExecuteType === customerDialogType.viewExport"
        type="selection"
        width="55"
      />
      <el-table-column
        prop="merchantId"
        label="药店编号"
        width="160"
      />
      <el-table-column
        prop="realName"
        label="药店名称"
        width="200"
      />
      <el-table-column
        prop="mobile"
        label="手机号"
        width="150"
      />
      <el-table-column
        prop="statusShowName"
        label="状态"
        width="180"
      />
      <el-table-column
        prop="licenseStatusShowName"
        label="资质审核状态"
      />
      <template slot="empty">
        <!-- 没数据 -->
        <div class="noshop">
          <p class="listText">
            暂无数据
          </p>
        </div>
      </template>
    </el-table>
    <div
      v-show="responseData.total > 0"
      class="page-container"
    >
      <div class="pag-text">
        共 {{ responseData.total }} 条数据，每页{{ pageInfo.pageSize }}条，共{{
          Math.ceil(responseData.total / pageInfo.pageSize)
        }}页
      </div>
      <el-pagination
        background
        class="pager"
        :current-page.sync="pageInfo.pageNum"
        :page-size.sync="pageInfo.pageSize"
        layout="sizes, prev, pager, next, jumper"
        :page-sizes="[10, 20, 30, 50]"
        :total="responseData.total"
        prev-text="上一页"
        next-text="下一页"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
    <span slot="footer">
      <el-button
        size="medium"
        @click="cancel"
      >取消</el-button>
      <el-button
        size="medium"
        style="margin-left: 20px;"
        type="primary"
        @click="handleConfirm"
      >确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import elDragDialog from '@/components/directive/el-dragDialog';
import { customerDialogType } from './constant';
import { getImportMerchantList, getMerchantList, deleteImportMerchantList } from './fetch/fetch';
// 拖拽指令
export default {
  name: 'CustomerListDialog',
  directives: { elDragDialog },
  model: {
    prop: 'dialogVisible',
    event: 'onDialogChange',
  },
  props: {
    appendToBody: Boolean,
    customerExecuteType: {
      type: Number,
      default: 2,
    },
    appendParams: {
      type: Object,
      default: undefined,
    },
    dialogVisible: {
      type: Boolean,
      default() {
        return false;
      },
    },
    customerCount: {
      type: Number,
      default: 0,
    },
  },
  data() {
    return {
      customerDialogType,
      title: '',
      reqFunc: undefined,
      visible: false,
      tableData: [],
      deleteSelectionLoading: false,
      deleteAllLoading: false,
      loading: false,
      formData: {
        merchantIdStr: '',
        realName: '',
        mobile: '',
      },
      selection: [], // 选中的条目
      responseData: {
        list: [],
        total: 0,
      },
      pageInfo: {
        pageNum: 1,
        pageSize: 10,
      },
    };
  },
  mounted() {
    this.$nextTick(() => {
      this.visible = true;
      Object.assign(this.formData, this.appendParams);
      if (this.customerExecuteType === customerDialogType.viewDetail) {
        this.title = '客户列表（动态人群则只能查询此刻所包含客户）';
        this.reqFunc = getMerchantList;
      }
      if (this.customerExecuteType === customerDialogType.viewExport) {
        this.title = '已添加客户';
        this.reqFunc = getImportMerchantList;
        this.search();
      }
    });
  },
  methods: {
    handleSizeChange() {
      this.getTableData();
    },
    handleCurrentChange() {
      this.getTableData();
    },
    getTableData() {
      const params = { ...this.pageInfo, ...this.formData };
      this.loading = true;
      if (this.reqFunc) {
        this.reqFunc(params)
          .then((res) => {
            if (res.status === 'success') {
              const { totalCount, merchants } = res.data;
              this.responseData.total = totalCount;
              this.responseData.list = merchants;
            } else {
              this.$message({
                message: res.msg,
                type: 'error',
                duration: 1000,
              });
            }
          })
          .catch((error) => {
            console.log(error);
          })
          .finally(() => {
            this.loading = false;
          });
      }
    },
    search() {
      this.pageInfo.pageNum = 1;
      this.getTableData();
    },
    removeSelection() {
      if (this.selection.length > 0) {
        this.$confirm('是否删除该客户', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
        })
          .then(() => {
            this.deleteSelection();
          })
          .catch(() => {});
      } else {
        this.$message({
          message: '请选择数据',
          type: 'warning',
          duration: 1000,
        });
      }
    },
    deleteSelection() {
      const formData = {
        tempSetCode: this.formData.tempSetCode || '',
        merchantIds: this.selection,
      };
      deleteImportMerchantList(formData)
        .then((data) => {
          this.laodingBoole = false;
          if (data.status === 'success') {
            this.$message({
              message: '移除成功',
              type: 'success',
              duration: 1000,
            });
            this.$emit('updateCustomerCount', data.data.merchantIdSize);
            this.search();
          } else {
            this.$message({
              message: data.msg,
              type: 'error',
              duration: 1000,
            });
          }
        })
        .catch(() => {
          this.laodingBoole = false;
        });
    },
    removeAll() {
      this.$confirm('是否删除全部客户', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
      })
        .then(() => {
          this.resetCustomerCount();
        })
        .catch((error) => {
          console.log(error);
        });
    },
    resetCustomerCount() {
      this.formData.tempSetCode = '';
      this.tableData = [];
      this.$emit('updateCustomerCount', 0);
      this.search();
    },
    resetForm() {
      this.$refs.form.resetFields();
      this.search();
    },
    handleSelectionChange(selection) {
      this.selection = [];
      if (selection.length) {
        selection.forEach((element) => {
          this.selection.push(element.merchantId);
        });
      }
    },
    open() {},
    cancel() {
      this.handleDialogClose();
    },
    handleConfirm() {
      this.confirm();
    },
    confirm() {
      this.$emit('confirm');
      this.handleDialogClose();
    },
    handleDialogClose() {
      this.visible = false;
      this.$emit('onDialogChange', false);
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'style/style';
</style>
