<template>
  <div>
    <el-dialog title="提示" :visible.sync="dialogVisible" width="40%">
      <div style="text-align: center">{{ content }}</div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="toDeal">去处理</el-button>
      </span>
    </el-dialog>
    <el-dialog
      title="企业开户提醒通知"
      :visible.sync="tipOpeningAccountDialogVisible"
      :before-close="handleClose"
      width="40%"
    >
      <div >
        为保证平台业务合规性，提升商户资金安全性，请您在
        <span style="color:#ff2121;">{{deadlineDatetime}}</span> 前尽快完成“企业开户”。逾期未完成企业开户，平台将限制您的余额提现功能，企业开户成功后将恢复提现功能。
        <br />详细开户流程可参考首页的<span style="font-weight: bold;">企业开户操作说明</span>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" size="small" @click="toCompanyOpenAccount">去企业开户</el-button>
      </span>
    </el-dialog>

    <el-dialog
      title="资质效期提醒"
      :visible.sync="qualificationExpiredVisible"
      width="40%"
      :close-on-click-modal="isCanClose"
      :close-on-press-escape="isCanClose"
      :before-close="tipOpeningAccountDialogClose"
    >
      <div>
        <p>您的以下企业资质已过期/即将过期，<span style="color: red;">资质过期店铺会被自动下线</span>，请尽快更新处理！</p>
        <p>
          <span
            v-for="(item, index) in qualtificationInfos.names"
            :key="index"
          >
            {{ item }}{{ `${index === qualtificationInfos.names.length -1 ? '' : '、'}` }}
          </span>
        </p>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button :disabled="!isCanClose" @click="tipOpeningAccountDialogClose">取 消{{timeCount ? "(" + timeCount + ")" : ""}}</el-button>
        <el-button type="primary" @click="toQualtification">去处理</el-button>
      </span>
    </el-dialog>
    <el-dialog
      :title="announce.title"
      :visible.sync="announceVis"
      :show-close="!closeAnnounce"
      width="70%"
      :close-on-click-modal="!closeAnnounce"
      :close-on-press-escape="!closeAnnounce"
      :before-close="checkAnnounce">
      <div v-html="announce.content"></div>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" :disabled="closeAnnounce" @click="checkAnnounce">确认{{ announce.showTime ? "(" + announce.showTime + ")" : "" }}</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { firstPageStatistics, apiHomePageOpenAccountTips ,announceDialog, checkAnnounceDialog } from '@/api/home';
import { signTaskRemind, taskRemind } from '../../../api/home/<USER>'
import { mapState } from 'vuex';

export default {
  name: 'DialogMessage',
  data() {
    return {
      announceVis:false,
      dialog:{
        visible: {
          openShop: false,
          contract: false
        }
      },
      announce:{
        id:0,
        title:'',
        content:'',
        showTime:0,
      },
      closeAnnounce:true,
      qualificationExpiredVisible: false,
      dialogVisible: false,
      content: '',
      tipOpeningAccountDialogVisible: false,
      deadlineDatetime: '',
      timeCount: 54,
      isCanClose: true
    };
  },
  computed: { ...mapState('app', ['qualtificationInfos','shopConfig']) },
  watch: {
    'qualtificationInfos.isShow': function (val) {
      this.qualificationExpiredVisible = val;
      if (this.qualificationExpiredVisible) {
        this.beginTimeOut();
      }
    },
  },
  created() {
    taskRemind().then(res => {
      if (res.code == 0) {
        this.dialog.visible.openShop = res.result.notify == 1;
      }
    })
    this.homePageOpenAccountTips();
    // this.getFirstPageStatistics()
    // this.getAnnounceDialog()
  },
  methods: {
    tipOpeningAccountDialogClose() {
      if (this.isCanClose) {
        this.qualificationExpiredVisible = false
      }
    },
    beginTimeOut() {
      let timer = null;
      this.isCanClose = false;
      timer = setInterval(() => {
        this.timeCount --;
        if (this.timeCount == 0) {
          this.isCanClose = true;
          timer && clearInterval(timer)
          timer = null;
        }
      }, 1000)
    },
    async getFirstPageStatistics() {
      const res = await firstPageStatistics();
      if (res && Number(res.code) === 200) {
        const { showFlag, content } = res.data;
        this.dialogVisible = showFlag;
        this.content = content;
        if(!showFlag && !this.dialog.visible.contract && !this.dialog.visible.openShop){
          this.getAnnounceDialog()
        }
      }
    },
    toQualtification() {
      window.openTab('/qualtification');
      this.qualificationExpiredVisible = false;
    },
    toDeal() {
      window.openTab('/commissionRecord');
      this.dialogVisible = false;
    },
    // 开户提醒
    async homePageOpenAccountTips() {

      const res = await apiHomePageOpenAccountTips();
      console.log('homePageOpenAccountTips', res);

      if (res && Number(res.code) === 0) {
        const { showFlag, deadlineDatetime } = res.result;
        this.tipOpeningAccountDialogVisible = showFlag;
        this.deadlineDatetime = deadlineDatetime;
        if (!showFlag) {
          this.getFirstPageStatistics();
        }
      } else {
        this.getFirstPageStatistics();
      }
    },
    //获取公告信息
    async getAnnounceDialog() {

      const res = await announceDialog()
      if (res.result !== null) {
        Object.keys(this.announce).forEach((key) => {
          this.announce[key] = res.result[key]
        }, this)
        this.announceVis = true
        let timer = null ;
        this.closeAnnounce = true
        timer = setInterval(() => {
          this.announce.showTime --;
          if (this.announce.showTime == 0) {
            this.closeAnnounce = false
            timer && clearInterval(timer)
            timer = null
          }
        }, 1000)
      }

      // this.homePageOpenAccountTips();
      // const res = await announceDialog({
      //   regMobile:this.shopConfig.mobile
      // })

    },
    checkAnnounce() {
      checkAnnounceDialog({
        announceId:this.announce.id
      }).then(() => {
        this.announceVis = false
      })
    },

    announceClose() {
      this.announceVis = false
    },

    handleClose() {
      this.tipOpeningAccountDialogVisible = false;
      this.getFirstPageStatistics();
    },
    toCompanyOpenAccount() {
      this.tipOpeningAccountDialogVisible = false;
      window.openTab('/companyOpenAccount');
    },
  },
};
</script>

<style scoped>
</style>
