import request from '@/utils/request'
import { method } from 'lodash'

//店铺控销可选区域
export function areasCodes() {
  return request({
    url: '/shopSaleControl/areasCodes',
    method: 'get'
  })
}

//获取区域
export function getAreaTree() {
  return request({
    url:'areaTree',
    method:'get'

  })
}

//商品控销列表
export function listControSalePage() {
  return request({
    url: '/salesControl/listControSalePage',
    method: 'get'
  })
}

//获取可售区域
export function saleAreas() {
  return request({
    url: '/shopSaleControl/saleAreas',
    method: 'get'
  })
}

//保存店铺控销可售区域
export function saveSaleAreas(data) {
  return request({
    url: '/shopSaleControl/saveSaleAreas',
    method: 'post',
    data
  })
}

//批量删除商圈
export function batchDelete(data) {
  return request({
    url: '/busArea/batchDelete',
    method: 'post',
    data
  })
}

//新增/修改控销信息
export function addOrUpdateControlSale(data) {
  return request({
    url: '/salesControl/addOrUpdateControlSale',
    method: 'post',
    data
  })
}

export function openNationalBusinessCircleButton() {
  return request({
    url: '/shopSaleControl/openNationalBusinessCircleButton',
    method: 'get'
  })
}
export function openNationalBusinessCircle() {
  return request({
    url: '/shopSaleControl/openNationalBusinessCircle',
    method: 'get'
  })
}
