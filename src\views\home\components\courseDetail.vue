<template>
  <div class="course-detail-container">
    <div class="course-detail-title">
      <p>课程详情</p>
      <el-button type="primary" @click="$emit('back')" class="back-button">返回</el-button>
    </div>
    <video
      id="videoPlayer"
      v-if="fileType == 2"
      autoplay
      muted
      controls
      @ended="onVideoEnded"
      @play="onVideoPlay"
      @pause="onVideoPause"
      :src="courseDetail.content"
      ref="videoElement"
      @loadedmetadata="onVideoLoadedMetadata"
      @timeupdate="onTimeUpdate"
      class="course-video"
    />
    <div v-else-if="fileType == 3" v-html="courseDetail.content" class="course-text"></div>
    <div v-else class="iframe" @scroll="handlerScroll($event)">
      <iframe
        id="pdfIframe"
        class="course-iframe"
        style="width: 100%; height: 80vh"
        :src="textVisibleURl"
        frameborder="no"
        border="0"
        marginwidth="0"
        marginheight="0"
        scrolling="no"
        allowtransparency="yes"
      >
        <p>您的浏览器不支持 iframe 标签</p>
      </iframe>
    </div>
  </div>
</template>

<script>
import { getCourseDetail, saveStudyRecord } from '@/api/home'
import { mapState } from 'vuex'
export default {
  name: '',
  computed: { ...mapState('app', ['shopConfig']) },
  props: {
    isShowDetail: {
      default: false
    },
    courseId: {
      default: () => {}
    },
    fileType: {
      default: 1
    }
  },
  watch: {
    isShowDetail(val) {
      if (val) {
        this.getCourseDetail()
      }
    }
  },
  data() {
    return {
      courseDetail: {},
      videoElement: null,
      fileSuffix: '',
      textVisibleURl: '',
      curTime: 0,
      totalTime: 0,
      courseProgress: '',
      courseHeight: 0,
      lookTime:0
    }
  },
  mounted() {
    setInterval(()=>{
      this.lookTime += 1
    },1000)
    //非视频类
    const noVideo =
      document.querySelector('.course-text') || document.querySelector('.course-iframe')
    if (noVideo) {
      //  noVideo.addEventListener('scroll',hanlerScroll)
    }
    this.getCourseDetail()
  },
  beforeDestroy() {
    if (this.courseDetail.recordStatusName === '已学习') return
    if (this.courseDetail.fileType == 2) {
      // this.courseProgress = parseInt(this.curTime / this.totalTime) * 100 + '%'
      console.log('%c [ this.getStudyRecord() ]-92', 'font-size:13px; background:pink; color:#bf2c9f;', this.getStudyRecord())
      saveStudyRecord(this.getStudyRecord())

    } else  {
      this.$bus.$emit('essayAndvideo', this.courseDetail.id,this.courseDetail.fileType,this.lookTime)
    }
  },
  methods: {
    hanlerScroll(event) {
      console.log('%c [ event ]-97', 'font-size:13px; background:pink; color:#bf2c9f;', event)
    },
    getStudyRecord() {
      return {
        courseId: this.courseDetail.id,
        status: this.courseProgress == '0%' ? 0 : this.courseProgress == '100%' ? 1 : 2,


        courseProgress: this.courseProgress
      }
    },
    getCourseDetail() {
      getCourseDetail({ id: this.courseId, regMobile: this.shopConfig.mobile }).then(
        async (res) => {
          this.courseDetail = res.result
          this.courseProgress = this.courseDetail.courseProgress
          this.fileSuffix = res.result.content.split('.').reverse()[0]
          this.textVisibleURl = res.result.content
          if (this.fileSuffix == 'ppt' || this.fileSuffix == 'pptx') {
            this.textVisibleURl = `https://view.officeapps.live.com/op/embed.aspx?src=${res.result.content}`
          }
      this.$nextTick(()=>{
        this.$bus.$emit('courseProgress',this.courseDetail.courseProgress,this.courseDetail.fileType)
      })



        }
      )
    },
    //视频结束
    onVideoEnded(event) {
      if (videoElement.value) {
      }
    },
    //视频开始
    onVideoPlay(event) {},
    //视频暂停
    onVideoPause(event) {},
    //视频加载完成
    onVideoLoadedMetadata(event) {
      //获取视频总长度
      const duration = event.target.duration
      this.totalTime = duration
      this.playBySeconds((this.courseDetail.courseProgress.slice(0, -1) * this.totalTime) / 100)
    },
    //视频播放中
    onTimeUpdate(event) {
      //event.target 是触发事件的 video 元素
      const playedSeconds = event.target.currentTime
      //  console.log(`已播放: ${playedSeconds} 秒`)
      this.curTime = playedSeconds
      this.courseProgress = parseInt((playedSeconds / this.totalTime) * 100) + '%'
    },
    //设置播放起始时间
    playBySeconds(num) {
      if (num && document.getElementById('videoPlayer')) {
        let myVideo = document.getElementById('videoPlayer')
        myVideo.play()
        myVideo.currentTime = num
        // this.$message.success('已恢复到您上次学习位置')
      }
    }
  }
}
</script>

<style scoped>
.course-video {
  width: 100%;
  height: 80vh;
}
.cui-menusection {
  display: none;
}
.course-detail-title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 5px 30px 25px;
  font-weight: bold;
}
.course-text {
  padding-left: 32px;
}
.back-button {
  position: fixed;
  right: 6vw;
  top: 16vh;
  z-index:1000
}
</style>
