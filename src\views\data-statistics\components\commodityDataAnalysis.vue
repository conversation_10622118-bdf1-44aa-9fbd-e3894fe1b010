<template>
  <div class="commodityDataAnalysis">
    <div class="title_line">商品信息</div>
    <el-row style="line-height: 30px">
      <el-col :span="6">商品编码：{{ info.barcode }}</el-col>
      <el-col :span="6">商品ERP编码：{{ info.productCode }}</el-col>
      <el-col :span="6">商品名称：{{ info.productName }}</el-col>
      <el-col :span="6" style="text-align: right">
        <el-button type="primary" @click="backToList" size="small">返 回</el-button>
      </el-col>
    </el-row>
    <el-row style="line-height: 30px">
      <el-col :span="6">规格：{{ info.spec }}</el-col>
      <el-col :span="10">生产厂家：{{ info.manufacturer }}</el-col>
    </el-row>
    <el-row style="margin: 15px 0">
      <el-col :span="24">
        <el-button type="primary" plain @click="exportExcel" size="small">导出</el-button>
      </el-col>
    </el-row>
    <LineChart2
      v-for="item in lineChartList"
      :key="item.chartId"
      :titleInfo="item.titleInfo"
      :chartId="item.chartId"
      :tooltip="item.tooltip"
      :chartConfig="item.chartConfig"
      ref="lineChart"
    />
    <LineChart3
      v-for="item in lineChartList3"
      :key="item.chartId"
      :titleInfo="item.titleInfo"
      :chartId="item.chartId"
      :tooltip="item.tooltip"
      :chartConfig="item.chartConfig"
      ref="lineChart"
    />
    <dotChart
      tooltip=''
      ref="dotChart"
     />
    <export-tip
      :change-export="changeExport"
      @handleExoprClose="handleExoprClose"
      @handleChangeExport="handleChangeExport"
    />
  </div>
</template>

<script>
import {
  getHitsAndvisitorReport,
  getSaleNumAndSalesMoney,
  getPriceAndSaleNum,
  getKaPriceAndSaleNum,
  exportAnalysisDetail,
  getPriceAndSaleNumV2
} from '@/api/data-statistics/varietyAnalysis';
import LineChart2 from './lineChart2';
import LineChart3 from './LineChart3';
import dotChart from './dotChart';
import exportTip from '@/views/other/components/exportTip';
import {actionTracking} from "@/track/eventTracking";
import {ExposureTool, VNodeExposureTool} from "@/utils/exposureTools"

let exposureMonitor = null;

export default {
  name: 'commodityDataAnalysis',
  components: {
    LineChart2,
    LineChart3,
    exportTip,
    dotChart
  },
  props: {
    productInfo: {
      type: Object,
      default: () => {
        return {};
      }
    }
  },
  data() {
    return {
      info: {},
      lineChartList: [],
      lineChartList3: [],
      resDotData:{},
      changeExport: false
    };
  },
  created() {
    this.info = {...this.productInfo};
    this.getData();
  },
  destroyed() {
    exposureMonitor.end();
  },

  methods: {
    async getData() {
      const params = this.$parent.getParams();
      params.skuId = this.info.skuId
      console.log(params);
      try {
        const res = await getHitsAndvisitorReport(params);
        if (res && res.code === 0) {
          this.lineChartList.push({
            titleInfo: '点击量&访客量',
            chartId: 'hits', //ID是为了获取一个容器并设置高度
            tooltip: '点击量：商品的点击次数（加入采购单、查看商品详情）\n' +
              '访客量：通过商品列表浏览商品的客户数量',
            chartConfig: {
              legend: {data: ['点击量', '访客量']},
              abscissa: res.result.abscissa,
              datum: res.result.datum,
              datum2: res.result.datum2
            }
          });
        } else {
          this.$message.error(res.msg || '查询失败');
        }
      } catch (e) {
        console.log(e);
      }

      try {
        const res2 = await getSaleNumAndSalesMoney(params);
        if (res2 && res2.code === 0) {
          this.lineChartList.push({
            titleInfo: '销量&销售额',
            chartId: 'sales', // ID是为了获取一个容器并设置高度
            tooltip: '销量：商品采购数量（去除已取消和退款完成的该商品采购数量）\n' +
              '销售额：商品采购金额（去除已取消和退款完成的商品采购金额）',
            chartConfig: {
              legend: {data: ['销量', '销售额']},
              abscissa: res2.result.abscissa,
              datum: res2.result.datum,
              datum2: res2.result.datum2,
            },
          });
        } else {
          this.$message.error(res2.msg || '查询失败');
        }
      } catch (e) {
        console.log(e);
      }

      
      try {
        const resDotData = await getPriceAndSaleNumV2(params);
        if (resDotData && resDotData.code === 0) {
          this.$refs.dotChart.initChart(resDotData)
        } else {
          this.$message.error(resDotData.msg || '查询失败');
        }
      } catch (e) {
        console.log(e);
      }

      // try {
      //   const res3 = await getPriceAndSaleNum(params);
      //   if (res3 && res3.code === 0) {
      //     this.lineChartList3.push({
      //       titleInfo: '单体采购销量&价格',
      //       chartId: 'monomer', // ID是为了获取一个容器并设置高度
      //       tooltip: '销量：以单体采购价购买的商品采购量（去除已取消和退款完成的且商品价格为单体采购价的商品采购量）',
      //       chartConfig: {
      //         legend: {data: ['价格', '销量']},
      //         abscissa: res3.result.abscissa,
      //         datum: res3.result.datum,
      //         datum2: res3.result.datum2,
      //       },
      //     });
      //   } else {
      //     this.$message.error(res3.msg || '查询失败');
      //   }
      // } catch (e) {
      //   console.log(e);
      // }

      // try {
      //   const res4 = await getKaPriceAndSaleNum(params);
      //   if (res4 && res4.code === 0) {
      //     this.lineChartList3.push({
      //       titleInfo: '连锁采购销量&价格',
      //       chartId: 'chain', //ID是为了获取一个容器并设置高度
      //       tooltip: '销量：以连锁采购价购买的商品采购量（去除已取消和退款完成的且商品价格为连锁采购价的商品采购量）',
      //       chartConfig: {
      //         legend: {data: ['价格', '销量']},
      //         abscissa: res4.result.abscissa,
      //         datum: res4.result.datum,
      //         datum2: res4.result.datum2,
      //       },
      //     });
      //   } else {
      //     this.$message.error(res4.msg || '查询失败');
      //   }
      // } catch (e) {
      //   console.log(e);
      // }
//数据刷新完开始监听曝光
      let that = this;
      this.$nextTick(() => {
        if (exposureMonitor) {
          exposureMonitor.end();
        } else {
          exposureMonitor = new VNodeExposureTool(document.querySelector(".varietyAnalysis").parentElement, (item) => {
            actionTracking('variety_analysis_more_exposure', {
              time: new Date().getTime(),
              org_id: that.orgId,
              sales_report: {
                hits: "hit_visitors",
                sales: 'sale',
                monomer: 'monomer_drugstore',
                chain: 'ka_drugstore'
              }[item.getChartId()]
            });
          })
        }
        exposureMonitor.begin(that.$refs.lineChart);
      })
    },
    backToList() {
      this.$emit('update:showProductInfo', false);
    },
    handleExoprClose() {
      this.changeExport = false;
    },
    handleChangeExport(info) {
      this.changeExport = false;
      if (info === 'go') {
        const path = '/downloadList';
        window.openTab(path);
      }
    },
    async exportExcel() {
      actionTracking('variety_analysis_more_export', {
        time: new Date().getTime(),
        org_id: this.orgId
      })
      const params = this.$parent.getParams();
      params.skuId = this.info.skuId
      const res = await exportAnalysisDetail(params);
      if (res && res.code === 0) {
        this.changeExport = true;
      } else {
        this.$message.error(res.msg || '导出失败');
      }
    }
  }
};
</script>

<style scoped lang="scss">
.commodityDataAnalysis {
  padding: 16px 16px;
  background: #fff;
}
</style>
