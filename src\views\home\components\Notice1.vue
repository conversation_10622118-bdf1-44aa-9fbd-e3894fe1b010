<template>
  <div ref="banner" class="banner" v-if="shopConfig.indexShow&& showBanner">
    <el-carousel  @change="onBannerChange" trigger="click" :height="bannerHeight+'px'" :interval="5000" :class="carouselName">
      <el-carousel-item v-for="(item,index) in bannerList" :key="item.id" :label="index+1">
        <div class="small" @click="openNotice(item.id)">
          <!--          <img :src="item.url" alt="">-->
          <el-image style="width: 100%; height: 100%" :src="item.url" fit="scale-down"></el-image>
        </div>
      </el-carousel-item>
    </el-carousel>
    <el-dialog
      :title="title"
      :visible.sync="dialogVisible"
      width="70%">
      <div v-html="content"></div>
      <span slot="footer" class="dialog-footer">
  </span>
    </el-dialog>
  </div>
</template>

<script>
import {bannerList, bannerDetail} from '@/api/home'
import {mapState} from 'vuex'
import {actionTracking} from "@/track/eventTracking";

const exposuredBanner = [];
export default {
  name: "Notice",
  data() {
    return {
      bannerHeight: 0,
      bannerWidth: 0,
      showBanner: false,
      activeId: '',
      bannerList: [],
      dialogVisible: false,
      title: '公告详情',
      content: ''
    }
  },
  computed: {
    ...mapState('app', ['shopConfig']),
    carouselName() {
      if (this.bannerList.length === 1) {
        return 'onlyOne'
      }
      return ''
    }
  },
  watch: {
    'bannerWidth'(newValue, oldValue) {
      this.bannerHeight = (230 / 900) * newValue
    }
  },
  created() {
    this.getBannerList()
  },

  beforeMount() {
    window.addEventListener('resize', this.resizeHandler)
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.resizeHandler)
  },
  methods: {
    onBannerChange(newIndex){
      if(exposuredBanner.includes(newIndex)) return;
      exposuredBanner.push(newIndex)
      if(this.bannerList && this.bannerList.length > newIndex) {
        actionTracking('banner_exposure', {
          banner_id: this.bannerList[newIndex].id
        })
      }
    },

    async getBannerList() {
      const res = await bannerList()
      if (res && res.code === 0 && Array.isArray(res.result)) {
        if (res.result.length > 0) {
          this.showBanner = true
        }
        this.bannerList = res.result
        exposuredBanner.length = 0;//清空已曝光
        if(this.bannerList && this.bannerList.length > 0) {
          //上报第一个banner
          this.onBannerChange(0)
        }
        setTimeout(() => {
          this.resizeHandler()
        }, 300)
      }
    },
    async openNotice(id) {
      if(this.bannerList ) {
        actionTracking('banner_click', {
          banner_id: id
        })
      }
      console.log(id)
      const res = await bannerDetail(id)
      if (res && res.code === 0) {
        const {title, content} = res.result
        this.title = title
        this.content = content
        this.dialogVisible = true
      } else {
        this.$message(res.msg)
      }
    },
    resizeHandler() {
      if (this.$refs.banner) {
        this.bannerWidth = this.$refs.banner.offsetWidth
      }
    }
  }
}
</script>

<style scoped lang="scss">
.banner {
  margin-bottom: 16px;

  .small {
    width: 100%;
    height: 100%;
    background: #fff;
    border-radius: 2px;
    display: flex;
    justify-content: center;
    align-items: center;

    img {
      width: 100%;
      height: 100%;
      border: none;
      display: inline-block;
      object-fit: scale-down
    }
  }

  .onlyOne ::v-deep   .el-carousel__indicators {
    display: none;
  }

  ::v-deep   .el-carousel__indicators {
    bottom: 15px;
    text-align: right;
    padding-right: 32px;

    .el-carousel__indicator {
      margin-right: 4px;
      font-size: 12px;
      padding: 0;
      text-align: center;

      .el-carousel__button {
        color: #fff;
        width: 14px;
        line-height: 14px;
        padding: 0;
        background-color: rgba(0, 0, 0, 0.1);
      }
    }

    .el-carousel__indicator.is-active {
      .el-carousel__button {
        background-color: rgba(0, 0, 0, 0.5);
      }
    }
  }
}
</style>
