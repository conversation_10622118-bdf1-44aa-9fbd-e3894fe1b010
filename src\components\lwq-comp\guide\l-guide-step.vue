<script>
export default {
  props: {
    step: {
      type: Number,
      default: 0
    },
    title: {
      type: String,
      default: ''
    },
    content: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      random: Math.random()
    }
  },
  mounted() {
    this.$emit('getStep', {
      step: this.step,
      ref: this.$refs[`guide${this.random}`],
      title: this.title,
      content: this.content,
    })

  }
}
</script>

<template>
  <div>
    <div style="display: inline-block;" :ref="`guide${random}`">
      <slot></slot>
    </div>
  </div>
</template>

<style scoped>
</style>
