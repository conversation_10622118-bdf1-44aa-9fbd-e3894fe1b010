<template>
  <div>
    <el-dialog
      title="查看发票"
      :visible="pdfVisible"
      width="90%"
      @close="closeDialog"
    >
      <el-table
        :data="pdfData"
        border
        style="width: 100%"
      >
        <el-table-column
          label="名称"
        >
          <template slot-scope="scope">
            <span>{{ scope.row.name }}</span>
          </template>
        </el-table-column>
        <el-table-column
          label="操作"
        >
          <template slot-scope="scope">
            <el-button
              type="text"
              size="small"
              @click="handleClick(scope.row.url)"
            >
              查看
            </el-button>
            <el-button
              v-if='showDelBtn'
              type="text"
              size="small"
              @click="deletePdf(scope.row.url)"
            >
              删除
            </el-button>
            <el-button
              v-if='scope.row.url'
              type="text"
              size="small"
              v-clipboard:copy="scope.row.url"
              v-clipboard:success="onCopySuccess"
            >
              复制发票链接
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <!--      <iframe :src="originUrl + '/static/pdf/web/viewer.html?file' + pdfUrl " />-->
      <!--      <div v-if="pdfUrlList.length>0"><iframe :src="originUrl + '/static/pdf/web/viewer.html?file' + pdfUrl " /></div>-->
      <!--      <div v-else class="noDiv">暂无发票信息</div>-->
    </el-dialog>
  </div>
</template>

<script>
import { getInvoice, saveInvoice } from '@/api/order/index';

export default {
  name: 'prd',
  props: {
    pdfVisible: {
      type: Boolean,
      default: false,
    },
    orderNo: {
      type: String,
      default: null,
    },
    showDelBtn: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      pdfUrl: 'https://dakaname.oss-cn-hangzhou.aliyuncs.com/file/2018-12-28/1546003237411.pdf',
      pdfUrlList: [],
      originUrl: '',
      pdfData: [],
      tipHeight: document.documentElement.clientHeight / 3,
    };
  },
  created() {
    this.getData();
  },
  methods: {
    getData() {
      this.originUrl = window.location.origin;
      const { orderNo } = this;
      getInvoice({ orderNo }).then((res) => {
        if (res.code === 0) {
          this.pdfUrlList = res.data;
          this.pdfData = [];
          if (res.data.length > 0) {
            res.data.forEach((item, index) => {
              const num = (index) + 1;
              this.pdfData.push({ name: `发票${num}`, url: item });
            });
          }
        }
      });
    },
    closeDialog() {
      this.$emit('closeDialog');
    },
    handleClick(url) {
      window.open(url);
    },
    deletePdf(url) {
      const ind = this.pdfUrlList.indexOf(url);
      this.pdfUrlList.splice(ind, 1);
      const params = {
        orderNo: this.orderNo,
        invoiceList: this.pdfUrlList,
      };

      this.$confirm('确认删除发票信息?', '温馨提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        saveInvoice(params).then((res) => {
          if (res.code === 0) {
            this.$message.success({ message: '删除成功', offset: this.tipHeight });
            this.getData();
          } else {
            this.$message.error({ message: res.message, offset: this.tipHeight });
          }
        });
      }).catch(() => {});
    },
    downPdf(url) {
      const link = document.createElement('a');
      const fname = 'report.pdf';
      link.href = url;
      link.setAttribute('download', fname);
      document.body.appendChild(link);
      link.click();
    },
    onCopySuccess() {
      this.$message.success('内容已复制到剪切板！')
    }
  },
};
</script>

<style scoped lang="scss">
::v-deep  .el-dialog__body{
  padding: 10px 20px;
}
::v-deep  .el-dialog__header{
  padding: 10px 20px;
  background: #f9f9f9;
}
::v-deep  .el-dialog__headerbtn{
  top: 15px;
}
::v-deep  .el-dialog__title{
  font-size: 16px;
}
.noDiv{
  padding: 30px 0;
  text-align: center;
}
</style>
