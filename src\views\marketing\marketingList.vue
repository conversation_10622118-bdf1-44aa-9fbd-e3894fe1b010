<template>
  <div class="main-box">
    <div class="list-box">
      <div class="explain-box">
        <div class="Fsearch">
          <el-row
            type="flex"
            align="middle"
            justify="space-between"
            class="my-row"
          >
            <el-row
              type="flex"
              align="middle"
            >
              <span class="sign" />
              <div class="searchMsg">
                优惠券说明
              </div>
            </el-row>
          </el-row>
        </div>
        <div class="con-inner">
          <div class="img">
            <img
              src="../../assets/image/marketing/onsaleexplain.png"
              alt=""
            >
          </div>
          <div class="text">
            <h3>发劵步骤</h3>
            <p>1 创建优惠券，设置可用商品及优惠信息；</p>
            <p>2 创建领券活动，指定可用客户；</p>
            <p>3 客户获得优惠券并使用</p>
          </div>
          <div class="btn">
            <el-button v-permission="['marketing_coupon_add']" type="primary">
              <router-link
                :to="{ path: 'addCoupon', query: { form: 'add' } }"
                style="display: block;"
              >
                新建优惠券
              </router-link>
            </el-button>
          </div>
        </div>
      </div>
      <div class="explain-list">
        <div class="Fsearch">
          <el-row
            type="flex"
            align="middle"
            justify="space-between"
            class="my-row"
          >
            <el-row
              type="flex"
              align="middle"
            >
              <span class="sign" />
              <div class="searchMsg">
                优惠券列表
              </div>
            </el-row>
          </el-row>
        </div>
        <div class="explain-search searchMy">
          <el-form
            ref="ruleForm"
            size="small"
            :inline="true"
          >
            <el-form-item>
              <el-input
                v-model.trim="searchData.name"
                placeholder="请输入优惠券名称"
              >
                <template slot="prepend">
                  优惠券名称
                </template>
              </el-input>
            </el-form-item>
            <el-form-item>
              <el-input
                v-model.trim="searchData.id"
                placeholder="请输入优惠券ID"
              >
                <template slot="prepend">
                  优惠券ID
                </template>
              </el-input>
            </el-form-item>
            <el-form-item>
              <span
                class="search-title"
              >起始日期</span>
              <div style="display: table-cell; line-height: 24px">
                <el-date-picker
                  v-model.trim="searchData.time"
                  type="datetimerange"
                  format="yyyy-MM-dd HH:mm:ss"
                  :default-time="['00:00:00', '23:59:59']"
                  value-format="timestamp"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  prefix-icon="el-icon-date"
                />
              </div>
            </el-form-item>
            <el-form-item>
              <span
                class="search-title"
              >优惠券状态</span>
              <el-select
                v-model.trim="searchData.status"
                placeholder="请选择"
              >
                <el-option
                  v-for="item in statusOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item>
              <span
                class="search-title"
              >券优惠方式</span>
              <el-select
                v-model.trim="searchData.type"
                placeholder="请选择"
              >
                <el-option
                  v-for="item in couponMethodOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item>
              <div class="search-btn-custom">
                <el-button
                  type="primary"
                  @click="getList('search')"
                >
                  查询
                </el-button>
                <el-button @click="resetForm('ruleForm')">
                  重置
                </el-button>
                <el-button v-permission="['marketing_coupon_export']" @click="exportList">
                  导出
                </el-button>
              </div>
            </el-form-item>
          </el-form>
        </div>
        <div
          v-loading="isLoading"
          class="explain-table"
        >
          <el-table
            class="my-table marketing-table"
            :data="tableData"
            row-key="id"
            border
            stripe
            :row-class-name="tableRowClass"
            max-height="400px"
            :header-cell-style="{ background: '#F9F9F9', color: '#666666' }"
            style="width: 100%"
            @selection-change="handleSelectionChange"
          >
            <el-table-column
              prop="id"
              label="券ID"
              width="120"
            />
            <el-table-column
              prop="name"
              label="券名称"
              min-width="120"
            />
            <el-table-column
              label="券状态"
              width="80"
              prop="couponStatusName"
            />
            <el-table-column
              label="门槛&金额"
              width="180"
            >
              <template slot-scope="scope">
                <span
                  v-if="scope.row.type == 2"
                >
                  满{{ scope.row.minMoneyToEnable }}元，打{{ scope.row.discount }}折
                  <span v-if="scope.row.maxMoneyInVoucher">，最高减{{ scope.row.maxMoneyInVoucher }}元</span>
                </span>
                <span v-else>
                  <span v-if="Number(scope.row.reduceType)===1">
                    每满{{ scope.row.minMoneyToEnable }}元，减{{ scope.row.moneyInVoucher }}元，最高减{{ scope.row.discount }}元
                  </span>
                  <span v-else>满{{ scope.row.minMoneyToEnable }}元减{{ scope.row.moneyInVoucher }}元</span>
                </span>
              </template>
            </el-table-column>
            <el-table-column
              label="适用商品"
              width="120"
              prop="description"
            />
            <el-table-column
              label="使用时间"
              width="120"
            >
              <template slot-scope="scope">
                <div v-if="scope.row.validityType === 2">
                  <span>起：{{ scope.row.startTime | handleTime }}</span><br>
                  <span>止：{{ scope.row.endTime | handleTime }}</span>
                </div>
                <div v-else>
                  {{ scope.row.validityDays }}天
                </div>
              </template>
            </el-table-column>
            <el-table-column
              label="创建时间"
              width="180"
            >
              <template slot-scope="scope">
                <span>{{ scope.row.createTime | handleTime }}</span>
              </template>
            </el-table-column>
            <el-table-column
              label="使用量"
              width="80"
            >
              <template slot-scope="scope">
                <span v-if="scope.row.usedCount">{{
                  Number(scope.row.usedCount)
                }}</span>
                <span v-else>0</span>
              </template>
            </el-table-column>
            <el-table-column
              label="券优惠方式"
              width="120"
              prop="typeName"
            />
            <el-table-column
              label="状态"
              width="80"
            >
              <template slot-scope="scope">
                <span v-if="scope.row.state == 0">新建</span>
                <span v-else-if="scope.row.state == 1">已发布</span>
              </template>
            </el-table-column>
            <el-table-column
              label="POP运营分摊比例（%）"
              width="120"
              prop="shareProportion"
            />
            <el-table-column
              align="center"
              fixed="right"
              label="操作"
              width="120"
            >
              <template slot-scope="scope">
                <el-button
                  type="text"
                  size="small"
                  @click="handleLookDetail(scope.row.id)"
                >
                  查看
                </el-button>
                <el-button
                  v-if="scope.row.state == 0"
                  v-permission="['marketing_coupon_edit']"
                  type="text"
                  size="small"
                  @click="handleEdit(scope.row.id)"
                >
                  修改
                </el-button>
                <el-button
                  v-permission="['marketing_coupon_copy']"
                  type="text"
                  size="small"
                  @click="editFunction(scope.row.id)"
                >
                  复制
                </el-button>
                <el-button
                  v-if="scope.row.state == 0"
                  v-permission="['marketing_coupon_release']"
                  type="text"
                  size="small"
                  @click="cancelConfirm('确认发布优惠券吗？', scope.row.id)"
                >
                  发布
                </el-button>
                <el-button
                  v-if="!scope.row.shareProportion && (Number(scope.row.couponStatus) === 0 ||Number(scope.row.couponStatus) === 1)"
                  v-permission="['marketing_coupon_give']"
                  type="text"
                  size="small"
                  @click="giveCoupon(scope.row)"
                >
                  赠券
                </el-button>
                <el-button
                  v-if="(Number(scope.row.couponStatus) === 1 ||Number(scope.row.couponStatus) === -1) && Number(scope.row.usedCount)>0"
                  type="text"
                  size="small"
                  @click="viewCouponData(scope.row.id)"
                >
                  查看优惠券数据
                </el-button>
                <el-button
                  v-if="scope.row.stateName !== '新建' && Number(scope.row.couponStatus) !== -1 && scope.row.description === '指定商品可用'"
                  type="text"
                  size="small"
                  @click="handleLookDetail(scope.row.id, 'change')"
                >
                  修改商品
                  <el-tooltip placement="bottom">
                    <div slot="content">仅支持增加优惠券可用商品，不能减少</div>
                    <i class="el-icon-warning-outline" />
                  </el-tooltip>
                </el-button>
              </template>
            </el-table-column>
            <template slot="empty">
              <div class="noData">
                <p class="img-box">
                  <img
                    src="../../assets/image/marketing/noneImg.png"
                    alt=""
                  >
                </p>
                <p>暂无数据</p>
              </div>
            </template>
          </el-table>
        </div>
        <div class="explain-pag">
          <Pagination
            v-show="pageData.total > 0"
            :total="pageData.total"
            :page.sync="pageData.pageNum"
            :limit.sync="pageData.pageSize"
            @pagination="getList"
          />
        </div>
      </div>
    </div>
    <GiveCouponDialog :giveCouponDialogVisible.sync="giveCouponDialogVisible" v-if="giveCouponDialogVisible" :giveCouponTemplateId=giveCouponTemplateId />
  </div>
</template>

<script>
import { couponList, couponPublish, couponListNew, queryCouponDetail } from '@/api/coupon/index';
import Pagination from '../../components/Pagination/index.vue';
import GiveCouponDialog from './giveCoupon'

export default {
  name: 'CouponList',
  filters: {
    handleTime(time) {
      return dayjs(time).format('YYYY-MM-DD HH:mm:ss');
    },
  },
  components: { Pagination,GiveCouponDialog },
  data() {
    return {
      statusOptions: [
        {
          label: '全部',
          value: '',
        },
        {
          label: '未开始',
          value: 0,
        },
        {
          label: '进行中',
          value: 1,
        },
        {
          label: '结束',
          value: -1,
        },
      ],
      couponMethodOptions: [
        {
          label: '全部',
          value: '',
        },
        {
          label: '满减券',
          value: 1,
        },
        {
          label: '满折券',
          value: 2,
        },
      ],
      searchData: {
        status: '',
        name: '',
        time: '',
        id: '',
        type: '', // 券优惠方式
      },
      pageData: {
        pageNum: 1,
        pageSize: 10,
        total: 0,
      },
      tableData: [],
      isLoading: false,
      giveCouponDialogVisible: false,
      giveCouponTemplateId: ''
    };
  },
  created() {
    console.log('process.env.NODE_ENV:', process.env.NODE_ENV);
    this.getList();
  },
  activated() {
    if (this.$route.query.refresh) {
      this.getList();
    }
  },
  methods: {
    cancelConfirm(text, id) {
      this.$confirm(text, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(async () => {
          // let couponPublish = await this.couponPublish(id)
          await this.couponPublish(id);
          this.getList();
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '取消发布',
          });
        });
    },
    async couponPublish(id) {
      return couponPublish({ id }).then((res) => {
        if (res.status == 'success') {
          this.$message({
            type: 'success',
            message: '发布成功!',
          });
        } else {
          this.$message({
            type: 'error',
            message: res.msg,
          });
        }
      });
    },
    editFunction(id) {
      this.$router.push({
        path: 'addCoupon',
        query: {
          form: 'copy',
          id,
        },
      });
    },
    handleLookDetail(id, type) {
      this.$router.push({
        path: 'couponDetail',
        query: { id, type },
      });
    },
    handleEdit(id) {
      this.$router.push({
        path: 'addCoupon',
        query: {
          form: 'edit',
          id,
        },
      });
    },
    getList(from) {
      console.log('from:', from);

      const param = {
        name: this.searchData.name,
        couponStatus: this.searchData.status,
        pageNum: from && from.page ? from.page : 1,
        pageSize: from && from.limit ? from.limit : 10,
        id: this.searchData.id,
        type: this.searchData.type,
      };
      if (!this.searchData.time) {
        param.startTime = '';
        param.endTime = '';
      } else {
        param.startTime = this.searchData.time[0];
        param.endTime = this.searchData.time[1];
      }

      this.isLoading = true;
      couponListNew(param)
        .then((response) => {
          if (response.code == 1000) {
            const { list, pageNo, totalCount } = response.data;
            this.tableData = list;
            this.pageData.pageNum = pageNo;
            this.pageData.total = totalCount;
          } else {
            this.$message({
              message: response.message || response.msg || '列表查询失败',
              type: 'error',
            });
          }
          this.isLoading = false;
        })
        .catch((error) => {
          this.isLoading = false;
          console.log(error);
        });
    },
    handleSelectionChange(val) {
      console.log(val, 'sssss');
    },
    tableRowClass({ rowIndex }) {
      if (rowIndex % 2 !== 1) {
        return '';
      }
      return 'success-row';
    },
    resetForm() {
      this.searchData = {
        status: '',
        name: '',
        time: '',
        type: '',
      };
      this.pageData.pageNum = 1;
      this.getList();
    },
    async exportList() {
      const params = { ...this.searchData };
      if (params.time && params.time.length > 0) {
        params.startTime = params.time[0];
        params.endTime = params.time[1];
      } else {
        params.startTime = '';
        params.endTime = '';
      }
      delete params.time;
      let list = []
      Object.keys(params)
        .forEach(key => {
          list.push(key + '=' + params[key]) ;
        });
      const a = document.createElement('a');
      a.href = process.env.VUE_APP_BASE_API + '/promo/coupon/exportCoupon?' + list.join('&');
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
    },
    giveCoupon(row) {
      console.log(row);
      this.giveCouponDialogVisible = true
      this.giveCouponTemplateId = String(row.id)
      // window.openTab('/giveCoupon',{templateId:row.id});
    },
    viewCouponData(id) {
      console.log(id);
      if(this.$store.state.permission.menuGray == 1) {
        this.$emit("viewCouponData",id)
      }else {
        window.openTab('/couponUserList',{id:id});
      }
    },
  },
};
</script>


<style lang="scss" scoped>
.list-box{
  padding: 0 20px 15px;
}
.searchMy ::v-deep  .el-form-item__label {
  margin-left: 20px;
  padding: 0;
}
.searchMy ::v-deep  .el-input__inner {
  border-radius: 0 4px 4px 0;
}
.searchMy ::v-deep  .el-date-editor{
  width: 100%;
}
.search-title {
  display: table-cell;
  padding: 0 10px;
  text-align: center;
  border: 1px solid #dcdfe6;
  height: 30px;
  line-height: 30px;
  vertical-align: middle;
  border-right: none;
  border-radius: 4px 0 0 4px;
  color: #333333;
  white-space: nowrap;
}
.searchMy ::v-deep  .el-select {
  display: table-cell;
  width: 100%;
}
.searchMy ::v-deep  .el-form-item__content{
  width: 100%;
}
.searchMy ::v-deep  .el-form-item--small.el-form-item{
  margin-bottom: 10px;
  width: 32%;
}
.searchMy ::v-deep  .el-form-item--small .el-form-item__content{
  line-height: 30px;
  width: 100%;
}
.searchMy ::v-deep  .el-input-group__prepend {
  background: none;
  color: #333333;
  padding: 0 10px;
}
.Fsearch {
  padding: 0px 20px 10px;
  font-weight: 500;
  .searchMsg {
    font-weight: 700;
    width: 200px;
  }
  .sign {
    display: inline-table;
    width: 3px;
    height: 13px;
    background: linear-gradient(360deg, #1d69c4 1%, #8bbdfc 99%);
    border-radius: 2px;
    margin-right: 8px;
  }
}
.Fsearch{
  padding: 15px 0;
}
.search-btn-custom {
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
}
::v-deep   .my-label .el-form-item__label {
  border: 1px solid #d9d9d9;
  border-radius: 4px 0px 0px 4px;
  padding: 0 13px 0 9px;
  height: 34px;
  vertical-align: bottom;
  border-right: 0;
  font-size: 12px;
}
::v-deep   .my-label .el-input__inner {
  border-radius: 0px 4px 4px 0px;
}

::v-deep   .el-range-editor--small.el-input__inner {
  height: 34px;
}
::v-deep   .el-input--small .el-input__inner {
  height: 34px;
  line-height: 34px;
}
.con-inner {
  padding-bottom: 10px;
  border-bottom: 1px solid #efefef;

  div {
    display: inline-block;
  }

  .img {
    width: 92px;
    height: 92px;

    img {
      width: 100%;
      height: 100%;
    }
  }

  .text {
    padding-left: 20px;
    vertical-align: top;

    h3 {
      font-size: 14px;
      color: #000000;
      padding: 0;
      margin: 0;
    }

    p {
      padding: 0;
      margin: 0;
      font-size: 12px;
      color: #666666;
      padding-top: 5px;
    }
  }

  .btn {
    float: right;
    padding-top: 26px;

    button {
      width: 100px;
      height: 30px;
      text-align: center;
      line-height: 30px;
      padding: 0;
      background: rgba(65, 131, 213, 1);
      border-color: rgba(65, 131, 213, 1);
      border-radius: 4px;
      font-size: 14px;
    }

    a {
      color: #ffffff;
      text-decoration: none;
    }

    .router-link-active {
      color: #ffffff;
      text-decoration: none;
    }
  }
}

.pag-info {
  width: 500px;
}
</style>
