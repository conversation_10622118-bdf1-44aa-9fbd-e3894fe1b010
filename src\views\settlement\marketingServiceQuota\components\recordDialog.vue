<template>
  <div>
    <el-button size="small" @click="openDialog">购买记录</el-button>

    <!-- 购买记录的弹框 -->
    <el-dialog
      title="购买记录"
      :visible.sync="dialogVisible"
      @close="dialogVisible = false"
      width="70%"
      append-to-body
    >
      <div class="box">
        <!-- 搜索栏 -->
        <el-form label-position="right" label-width="10px" style="display: flex; flex-wrap: wrap;">
          <el-form-item>
            <span class="search-title">提交日期</span>
            <div class="left-input">
              <el-date-picker
                size="small"
                v-model="form.changeTime"
                type="daterange"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                style="width: 400px"
              />
            </div>
          </el-form-item>

          <div class="selectSty">
            <el-form-item>
              <my-select
                label="审核状态"
                :list="accountTypeList"
                :multiple="false"
                v-model="form.auditStatus"
                :value="form.auditStatus"
                @input="(val) => (form.auditStatus = val)"
              ></my-select>
            </el-form-item>
          </div>

          <div>
            <el-button size="small" @click="resetForm">重置</el-button>
            <el-button size="small" type="primary" @click="search">查询</el-button>
          </div>
        </el-form>
        <!-- <div style="color: red; margin: -10px 0 10px 10px">
          保证金首次充值，平台可提供收据。后续补交保证金平台不再提供收据
        </div> -->
        <!-- 表格 -->
        <div class="tableSty">
          <el-table
            :data="tableData"
            style="width: 100%; margin-bottom: 10px; max-height: 500px; overflow-y: auto"
            border
            stripe
            v-loading="loading"
          >
            <el-table-column label="序号" type="index" align="center" width="50" />
            <el-table-column label="购买单号" prop="flowOrderNumber"> </el-table-column>
            <el-table-column label="购买金额" prop="amount">
              <template slot-scope="scope">
                <span></span>
                <span v-if="scope.row.amount != null"> ￥{{ scope.row.amount }} </span>
              </template>
            </el-table-column>
            <el-table-column label="实际到账金额" prop="actualReceivedAmount">
              <template slot-scope="scope">
                <span></span>
                <span v-if="scope.row.actualReceivedAmount != null"> ￥{{ scope.row.actualReceivedAmount }} </span>
              </template>
            </el-table-column>
            <el-table-column label="打款凭证" prop="paymentProof">
              <template slot-scope="scope">
                <template v-if="scope.row.paymentProof">
                  <el-image 
                    v-for="(item, index) in scope.row.paymentProof.split(',')"
                    :key="index"
                    :src="item"
                    :preview-src-list="scope.row.paymentProof.split(',')"
                    style="width: 30px; height: 30px"
                  >
                  </el-image>
                </template>
              </template>
            </el-table-column>
            <el-table-column label="提交时间" prop="createTime">
              <template slot-scope="scope">
                <div>{{ formatDate(scope.row.createTime, 'YMDHMS') }}</div>
              </template>
            </el-table-column>
            <el-table-column label="提交人" prop="submitter" />
            <el-table-column label="单据状态" prop="auditStatus">
              <template slot-scope="scope">
                <span>
                  {{ { 1: "待审核", 2: "审核通过", 3: "已取消", 4: "审核驳回"}[scope.row.auditStatus] }}
                </span>
              </template>
            </el-table-column>
            <el-table-column label="审核时间" prop="auditTime">
              <template slot-scope="scope">
                <div>{{ formatDate(scope.row.auditTime, 'YMDHMS') }}</div>
              </template>
            </el-table-column>
            <el-table-column label="审核留言" prop="auditComment" />
            <el-table-column label="是否开票" prop="isInvoice">
              <template slot-scope="scope">
                <span> {{ scope.row.isInvoice == 1 ? '是' : scope.row.isInvoice == 2 ? '否' : '' }} </span>
              </template>
            </el-table-column>
            <el-table-column label="操作" prop="slot" key="slot">
              <template #default="scope">
                <span></span>
                <el-button v-if="scope.row.auditStatus == 4" type="text" @click="openRecharge('change', scope.row)">
                  修改
                </el-button>
                <el-button v-if="scope.row.auditStatus == 1" type="text" @click="overSub(scope.row)">
                  取消
                </el-button>
                <el-button v-if="scope.row.auditStatus == 2 && scope.row.isInvoice == 1 && scope.row.invoiceInformation" type="text" @click="downloadInvoice(scope.row.invoiceInformation)">
                  下载发票
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          <!-- 分页 -->
          <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="pagination.pageNum"
            :page-sizes="[20, 50, 100]"
            :page-size="pagination.pageSize"
            background
            layout="->, total, sizes, prev, pager, next, jumper"
            :total="pagination.total"
          >
          </el-pagination>
        </div>
      </div>
      <!-- 充值保证金的弹框 -->
      <rechargeDialog ref="rechargeDialog"></rechargeDialog>
    </el-dialog>
  </div>
</template>

<script>
import { pageRechargeFlow, cancel, showPdf } from '@/api/settlement/marketingServiceQuota/index'
import mySelect from '../../components/mySelect.vue'
import rechargeDialog from './rechargeDialog.vue'

export default {
  // props: ['accountData'],
  name: 'recordDialog',
  components: {
    mySelect,
    rechargeDialog
  },
  data() {
    return {
      dialogVisible: false,
      form: {
        auditStatus: null,
        changeTime: null
      },
      accountTypeList: [
        { value: '1', label: '待审核' },
        { value: '2', label: '审核通过' },
        { value: '3', label: '已取消' },
        { value: '4', label: '审核驳回' }
      ],

      loading: false, //表格加载中
      tableData: [], // 表格数据
      pagination: {
        pageNum: 1,
        pageSize: 20,
        total: 100
      } // 表格分页
    }
  },
  created() {},
  methods: {
    downloadInvoice(invoice){
      showPdf({ pdfUrl: invoice }).then(res=>{
        let pdfContent = res;
        const blob = new Blob([pdfContent], { type: "application/pdf"});
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement("a");
        a.style.display = "none";
        a.href = url;
        a.download = "发票.pdf";
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
      })
      // const downLoadUrl = `/merchantTotalFundAccount/showPdf?pdfUrl=${invoice}`;
      // window.open(downLoadUrl);
      // const a = document.createElement('a');
      // a.style.display = 'none';
      // // a.href = 'http://fp.baiwang.com/format/d?d=0C00159898D115A8A07C1F289F12C79EB5E3C2937D8A2D7335545FFA9206E972'
      // a.href = invoice
      // a.download = '发票';
      // a.setAttribute('rel','noopener noreferrer');  // 增加这句
      // document.body.appendChild(a);
      // a.click();
      // document.body.removeChild(a); // 移除元素
    },
    overSub(val){
      let subData = {
        fundPropertyStatus: 2,
        merchantFundAuditId: val.id
      }
      cancel(subData).then(res=>{
        if(res.code == 0) this.search()
      })
    },
    openDialog(value) {
      // 调用this.search函数获取表格数据
      console.log(value)
      this.search()
      this.dialogVisible = true
    }, // 打开新增弹框
    openRecharge(type, row) {
      // console.log(row)
      this.$refs.rechargeDialog.openDialog(type, row)
    },
    search() {
      // console.log(this.form)
      this.loadTableData()
    }, // 查询逻辑
    resetForm() {
      this.form = {
        auditStatus: null,
        changeTime: null
      };
    }, // 重置表单

    loadTableData() {
      // 通过this.accountData page form等，发送请求的相关参数，拿到数据渲染表格
      let subData = {
        fundPropertyStatus: 2,
        auditStatus: this.form.auditStatus,
        createStartTime: this.form.changeTime?.[0] || null,
        createEndTime: this.form.changeTime?.[1] || null,
        pageNum: this.pagination.pageNum,
        pageSize: this.pagination.pageSize
      }
      this.loading = true
      // 调用接口请求数据，替换tableData
      pageRechargeFlow(subData).then(res => {
        if(res.code == 0) {
          this.tableData = res.data.list
          this.pagination.total = res.data.total
        }
      }).finally(() => {
        this.loading = false
      })
    }, // 加载表格数据
    handleCurrentChange(pageNum) {
      this.pagination.pageNum = pageNum
      this.search(true)
    }, // 页码改变的回调
    handleSizeChange(pageSize) {
      this.pagination.pageSize = pageSize
      this.search(true)
    } // 每页条数改变的回调
  }
}
</script>

<style scoped lang="scss">
.box {
  width: 100%;
  display: flex;
  flex-direction: column;
  // justify-content: center;
  // align-items: center;
  padding: 0 10px;
}
.search-title {
  display: table-cell;
  padding: 0 20px;
  text-align: center;
  border: 1px solid #dcdfe6;
  height: 30px;
  line-height: 30px;
  vertical-align: middle;
  border-right: none;
  border-radius: 4px 0 0 4px;
  color: #909399;
  white-space: nowrap;
  background-color: #f5f7fa;
}

.left-input {
  display: table-cell;
  width: 100%;
  line-height: 24px;
  .el-date-editor {
    border-top-left-radius: 0px;
    border-bottom-left-radius: 0px;
  }
}
.selectSty {
  margin: 0 20px;
}
.tableSty {
  position: relative;
}
.tableExplainSty{
  span{
    float: right;
    cursor: pointer;
    color: #679cdd;
    padding: 4px 0;
  }
}
</style>
