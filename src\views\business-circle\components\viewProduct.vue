<template>
  <div>
    <el-dialog
      :visible="dialogVisibl"
      @open="open"
      :before-close="handleDialogClose"
      :width="dialogWidth"
      title="查看商品"
    >
      <el-form :model="formData" size="small">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item>
              <el-input placeholder="请输入" v-model="formData.erpCode">
                <template slot="prepend">商品ERP编码</template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item>
              <el-input placeholder="请输入" v-model="formData.productName">
                <template slot="prepend">商品名称</template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="4" :offset="4">
            <el-form-item style="float: right">
              <el-button type="primary" @click="searchList">查询</el-button>
              <el-button @click="resetFields">重置</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <el-table
        v-loading="isLoading"
        :data="tableData"
        stripe
        border
        style="width: 100%"
        highlight-current-row
        :header-cell-style="{ background: '#F9F9F9', color: '#666666' }"
      >
        <el-table-column prop="erpCode" label="商品ERP编号" />
        <el-table-column prop="productName" label="商品名称" />
        <el-table-column prop="approvalNumber" label="批准文号" />
        <el-table-column prop="spec" label="规格" />
        <el-table-column prop="statusName" label="状态" />
        <el-table-column prop="status" label="状态" v-if="show === true" />
      </el-table>

      <div v-if="tablePage.total != 0" class="pagination-container">
        <div class="pag-text">
          共 {{ tablePage.total }} 条数据，每页{{ tablePage.pageSize }}条，共{{
            Math.ceil(tablePage.total / tablePage.pageSize)
          }}页
        </div>
        <el-pagination
          background
          :page-sizes="pageSizes"
          :page-size="tablePage.pageSize"
          :current-page="tablePage.pageNum"
          layout="sizes, prev, pager, next, jumper"
          :total="tablePage.total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
      <el-row :gutter="20">
        <el-col :span="2" :offset="22" style="float: right">
          <el-button type="primary" style="margin-top: 10px" size="small" @click="sureBtn"
            >确 定</el-button
          ></el-col
        >
      </el-row>
    </el-dialog>
  </div>
</template>

<script>
import { getProducts } from '@/api/businessCircle'

export default {
  name: 'viewProduct',
  model: {
    prop: 'viewProductDialog',
    event: 'onDialogViewProduct'
  },
  props: {
    viewProductDialog: Boolean,
    row: Object
  },
  mounted() {
    this.$nextTick(() => {
      this.dialogVisibl = true
    })
  },
  data() {
    return {
      show: false,
      dialogWidth: '95%',
      formData: {
        erpCode: '', // 商品ERP编码
        productName: '' // 商品名称
      },
      dialogVisibl: false,
      tableData: [], // 弹窗表格
      rowIndex: '',
      pageSizes: [10, 20, 30, 40],
      tablePage: {
        // 分页
        pageNum: 1,
        pageSize: 10,
        total: 1
      },
      busAreaId: '',
      isLoading: false, // 加载
      ruleForm: {
        tempName: '',
        templateType: '',
        tempStatus: '',
        branchCode: null,
        startTime: ''
      }
    }
  },
  methods: {
    open(busAreaId) {
      this.resetFields()
      this.dialogVisibl = true
      this.busAreaId = busAreaId // 业务商圈ID
      this.searchList() // 初始化列表
    },
    handleDialogClose() {
      this.dialogVisibl = false
      this.$emit('onDialogViewProduct', false)
    },
    // 查询
    searchList() {
      const params = {
        busAreaId: this.row.id
      }
      Object.assign(params, this.formData, this.tablePage)
      this.isLoading = true
      getProducts(params).then((res) => {
        const { code, data } = res
        if (code === 0) {
          this.isLoading = false
          this.tableData = data.list
          this.tablePage.total = data.total // 总数据数量
          this.tablePage.pageNum = data.pageNum
        }
      })
    },
    resetFields() {
      this.formData.erpCode = ''
      this.formData.productName = ''
    },
    handleSizeChange(val) {
      this.tablePage.pageSize = val
      this.searchList()
    },
    handleCurrentChange(val) {
      this.tablePage.pageNum = val
      this.searchList()
    },
    sureBtn() {
      this.handleDialogClose()
    }
  }
}
</script>

<style scoped>
::v-deep  .el-dialog__header {
  background-color: #f8f8ff;
}
</style>
