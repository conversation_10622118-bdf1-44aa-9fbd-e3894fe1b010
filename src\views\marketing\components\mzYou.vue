<script>
import setGiveGoods from './setGiveGoods.vue'
export default {
  components: {
    setGiveGoods
  },
  props: {
    value: {
      default: () => {},
      type: Object,
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  watch: {
    data: {
      handler(newVal, oldVal) {
        this.data.fullReductionCond = [0, 1].includes(this.radioCheck) ? 2 : 1;
        this.data.fullReductionType = [0, 2].includes(this.radioCheck) ? 2 : 1;
        this.levelHeight()
        this.$emit('input', this.data)

      },
      deep: true
    },
    value: {
      handler(newVal) {
        if (JSON.stringify(newVal) != JSON.stringify(this.data)) {
          this.data = newVal
        }
        const radioCheckMap = {
          '22': 0,
          '21': 1,
          '11': 3,
          '12': 2
        }
        this.radioCheck = radioCheckMap[`${this.data.fullReductionCond}${this.data.fullReductionType}`];
        this.top.tempCheck = this.radioCheck;
        const timer = setTimeout(() => {
          this.levelHeight();
          const timer2 = setTimeout(() => {
            const oneRadioHeight = this.$refs.mzLwq[0].getBoundingClientRect().height + this.top.marginBottom;
            const boxHeight = oneRadioHeight * this.config.length;
            const mzHeight = this.$refs.mz.getBoundingClientRect().height;
            const levelH = this.config[this.radioCheck].isLevel ? mzHeight - this.$refs.mzLwq[this.radioCheck].getBoundingClientRect().height + 5 : 0
            this.top.box = oneRadioHeight * this.radioCheck > boxHeight - mzHeight + levelH ? boxHeight - mzHeight + levelH : oneRadioHeight * this.radioCheck;
            this.top.lwq = oneRadioHeight * this.radioCheck - this.top.box;
            this.top.box += 'px';
            this.top.lwq += 'px';
            clearTimeout(timer2);
          }, 0)
          clearTimeout(timer);
        }, 0)
      },
    }
  },
  data() {
    return {
      radioCheck: 0,
      data: {
        fullReductionCond: 2,
        fullReductionType: 2,
        giveSkuType: 1,
        fullGiveRuleDtoList: [{
          giveCount: '',
          fullCount: '',
          fullMoney: '',
        }],
        giveSkuList: []
      },
      top: {
        box: '',
        lwq: '',
        levelTop: '',
        mzWidth: '',
        marginBottom: 10,
        tempCheck: 0
      },
      //日后如果还要加优惠条件，就在这里修改就行，能设置阶梯，isLevel为true。
      config: [{
        checkValue: 0,
        label: '满数量',
        unit: '个/件',
        isLevel: true,
        levelKey: 'fullCount',
      },{
        checkValue: 1,
        label: '每满数量',
        unit: '个/件',
        levelKey: 'fullCount',
        isLevel: false,
      },{
        checkValue: 2,
        label: '满金额',
        unit: '元',
        isLevel: true,
        levelKey: 'fullMoney',
      },{
        checkValue: 3,
        label: '每满金额',
        unit: '元',
        levelKey: 'fullMoney',
        isLevel: false,
      }],
      giveSkuListTemp: {
        '1' : [],
        '2' : []
      }
    }
  },
  mounted() {

  },
  methods: {
    clickRadio() {
      //存储当前值
      const temp = this.radioCheck;
      if (this.data.fullGiveRuleDtoList.length > 1) {
        //将当前值设置为上一次的值
        this.radioCheck = this.top.tempCheck;
        const h = this.$createElement;
        this.$confirm(h('p', { style: { color: 'red' } }, '切换优惠规则将删除其他阶梯'), '温馨提示', {
          confirmButtonText: '确定',
          CancelButtonText: '取消',
        }).then(() => {
          this.data.fullGiveRuleDtoList = [{ giveCount: '', fullCount: '', fullMoney: ''}]
          this.radioCheck = temp;
          this.top.tempCheck = temp;
          this.clickRadio();
        })
        return
      }
      const timer = setTimeout(() => {
        const oneRadioHeight = this.$refs.mzLwq[0].getBoundingClientRect().height + this.top.marginBottom;
        const boxHeight = oneRadioHeight * this.config.length;
        const mzHeight = this.$refs.mz.getBoundingClientRect().height;
        const levelH = this.config[temp].isLevel ? mzHeight - this.$refs.mzLwq[this.radioCheck].getBoundingClientRect().height + 5 : 0
        this.top.box = oneRadioHeight * this.radioCheck > boxHeight - mzHeight + levelH ? boxHeight - mzHeight + levelH : oneRadioHeight * this.radioCheck;
        this.top.lwq = oneRadioHeight * this.radioCheck - this.top.box;
        this.top.box += 'px';
        this.top.lwq += 'px';
        clearTimeout(timer);
      }, 0)
    },
    addLevel($event) {
      this.data.fullGiveRuleDtoList.push({
        giveCount: '',
        fullCount: '',
        fullMoney: ''
      })
    },
    levelHeight() {
      const timer = setTimeout(() => {
        const mz = this.$refs.mz.getBoundingClientRect()
        const box = this.$refs.mzLwq[this.radioCheck].getBoundingClientRect();
        this.top.levelTop = mz.height - box.height + 'px';
        this.top.mzWidth = mz.width + 'px';
        clearTimeout(timer);
      }, 0)
    },
    deleteLevel(index) {
      this.data.fullGiveRuleDtoList = this.data.fullGiveRuleDtoList.filter((val, i) => i != index)
    },
    levelValueChange(key, i) {
      for (let index = i + 1; index < this.data.fullGiveRuleDtoList.length; index++) {
        this.data.fullGiveRuleDtoList[index][key] =
          this.data.fullGiveRuleDtoList[index][key] <= this.data.fullGiveRuleDtoList[index - 1][key] ?
            this.data.fullGiveRuleDtoList[index - 1][key] + 1 :
            this.data.fullGiveRuleDtoList[index][key];
      }

    },
    giveSkuTypeChange() {
      this.data.fullGiveRuleDtoList[0].giveCount = '';
      //切换前，缓存之前check选择的数据
      this.giveSkuListTemp[this.data.giveSkuType == 1 ? '2' : '1'] = this.data.giveSkuList;

      this.data.giveSkuList = this.giveSkuListTemp[this.data.giveSkuType];
    }
  }
}
</script>

<template>
  <div style="display: flex;gap: 15px;">
    <div style="position: relative;" ref="boxLwq">
      <div v-for="(item, index) in config" :style="{marginBottom: `${top.marginBottom}px`}">
        <div class="mz-lwq" style="display: flex;width: 250px;" ref="mzLwq">
          <div style="width: 90px;">
            <span>
              <el-radio :disabled="disabled" v-model="radioCheck" @change="clickRadio();levelHeight();" :label="item.checkValue">{{ item.label }}</el-radio>
            </span>
          </div>
          <div>
            <el-input-number
              v-model="data.fullGiveRuleDtoList[0][radioCheck == item.checkValue ? item.levelKey : '']"
              controls-position="right"
              :precision="0"
              :min="1"
              style="width: 120px"
              :disabled="radioCheck != item.checkValue || disabled"
              @change="levelValueChange(item.levelKey, 0)"
            /> {{ item.unit }}
          </div>
        </div>
        <div v-if="item.isLevel" style="position: relative;display: flex;flex-direction: column;gap: 15px;transition: all 0.3s;" :style="radioCheck == item.checkValue ? { paddingTop: top.levelTop, marginTop: '5px' } : {}">
          <template v-if="radioCheck == item.checkValue">
            <template v-for="(val, i) in data.fullGiveRuleDtoList">
              <div v-if="i >= 1 " class="level level-mp">
                <span style="width: 90px;"></span>
                <el-input-number
                  v-model="val[item.levelKey]"
                  controls-position="right"
                  :precision="0"
                  :min="data.fullGiveRuleDtoList[i - 1][item.levelKey] + 1"
                  style="width: 120px"
                  @change="levelValueChange(item.levelKey, i)"
                  :disabled="radioCheck != item.checkValue || disabled"
                />
                <span style="margin-left: 5px;">{{ item.unit }}</span>
              </div>
            </template>
          </template>
        </div>
      </div>
    </div>
    <div>
      <div style="display: flex;gap: 15px;">
        <div class="mzLWQ" ref="mz" :style="{ top:top.box }">
          <div class="lwq-zhuanshujiantou" :style="{ top:top.lwq }"></div>
          <div style="display: flex;margin: 10px 0;gap: 15px;">
            <span style="width: 100px;">
              <el-radio :disabled="disabled" v-model="data.giveSkuType" :label="1" @change="giveSkuTypeChange">送赠品包</el-radio>
            </span>
            <span>
              <el-input-number style="width: 140px;margin-right: 5px;" v-model="data.fullGiveRuleDtoList[0][data.giveSkuType == 1 ? 'giveCount' : '']" :disabled="data.giveSkuType != 1 || disabled" @change="levelValueChange('giveCount', 0)" :precision="0" :min="1" controls-position="right">
              </el-input-number>
              个
              <!-- 查看时点击此处要显示赠品弹框 -->
              <!-- <div v-if="data.giveSkuType == 1 && data.giveSkuList.length && !disabled" style="color: #4183d5;">{{  `赠品包 : ${data.giveSkuList.length}个sku，共${data.giveSkuList.map(item => item.everyQty).reduce((total, cur) => total + cur)}件` }}</div> -->
              <div v-if="data.giveSkuList.length && data.giveSkuType == 1" style="color: red;font-size: 12px;">
                (共{{ data.giveSkuList.map(item => item.everyQty).reduce((total, cur) => total + cur) * data.fullGiveRuleDtoList[0][data.giveSkuType == 1 ? 'giveCount' : ''] }}件赠品)
              </div>
            </span>
            <setGiveGoods v-if="data.giveSkuType == 1" :disabled="disabled" v-model="data.giveSkuList" title="查看赠品包" :type="0" @input="levelHeight()">
              <el-button v-if="!data.giveSkuList.length"  type="text">设置赠品包</el-button>
              <el-button v-else type="text">{{  `赠品包 : ${data.giveSkuList.length}个sku，共${data.giveSkuList.map(item => item.everyQty).reduce((total, cur) => total + cur)}件` }}
              </el-button>
            </setGiveGoods>
            <!-- <setGiveGoods v-if="data.giveSkuType == 1 && !data.giveSkuList.length" v-model="data.giveSkuList" :type="0" :maxCount="100" title="设置赠品包" @input="levelHeight()"></setGiveGoods> -->
          </div>
          <div style="display: flex;margin: 10px 0;gap: 15px;">
            <span style="width: 100px;">
              <el-radio :disabled="disabled" v-model="data.giveSkuType" :label="2" @change="giveSkuTypeChange">赠品池任选</el-radio>
            </span>
            <span>
              <el-input-number style="width: 140px;margin-right: 5px;" v-model="data.fullGiveRuleDtoList[0][data.giveSkuType == 2 ? 'giveCount' : '']" :disabled="data.giveSkuType != 2 || disabled" @change="levelValueChange('giveCount', 0)" :precision="0" :min="1" controls-position="right">
              </el-input-number>
              个/件
              <!-- <div v-if="data.giveSkuType == 2 && data.giveSkuList.length && !disabled" style="color: #4183d5;">{{  `共${data.giveSkuList.length}个sku` }}</div> -->
            </span>
            <setGiveGoods v-if="data.giveSkuType == 2" :disabled="disabled" title="查看赠品池" v-model="data.giveSkuList" :type="1" :maxCount="100">
              <el-button v-if="!data.giveSkuList.length"  type="text">设置赠品池</el-button>
              <el-button v-else type="text">赠品池：{{  `共${data.giveSkuList.length}个sku` }}</el-button>
            </setGiveGoods>
            <!-- <setGiveGoods v-if="data.giveSkuType == 2 && !disabled" v-model="data.giveSkuList" :type="1" :maxCount="100" title="设置赠品池" @input="levelHeight()"></setGiveGoods> -->
          </div>
        </div>
        <div style="font-size: 0;">
          <el-button v-if="config.some(item => item.isLevel && item.checkValue == radioCheck) && !disabled" type="primary" size="mini" :disabled="data.fullGiveRuleDtoList.length == 5" :style="{ top: top.box, position:'relative',transition: 'all 0.2s' }" @click="addLevel">+添加阶梯</el-button>
        </div>
      </div>
      <div :style="{ top:top.box }" class="level-mz">
        <div v-if="config.some(item => item.isLevel && item.checkValue == radioCheck)" style="flex-grow: 1;display: flex;flex-direction: column;gap:15px;margin-top: 5px;">
          <template v-for="(item, index) in data.fullGiveRuleDtoList" >
            <div v-if="index >= 1" style="display: flex;gap: 15px;">
              <div class="level" :style="{ width: top.mzWidth }">
                <div style="width: 100px;text-align: center;">
                  <!-- <span>
                    {{ data.giveSkuType == 1 ? '送赠品包' : '赠品池任选' }}
                  </span> -->
                </div>
                <div style="display: flex;">
                  <el-input-number
                    v-model="item.giveCount"
                    controls-position="right"
                    :precision="0"
                    :min="data.fullGiveRuleDtoList[index - 1].giveCount + 1"
                    style="width: 140px"
                    :disabled="radioCheck != 0 && radioCheck != 2 || disabled"
                    @change="levelValueChange('giveCount', index)"
                  />
                  <span style="margin: 0 10px;">{{ data.giveSkuType == 1 ? '个' : '个/件' }}</span>
                  <span v-if="data.giveSkuType == 1 && item.giveCount && data.giveSkuList.length" style="font-size: 12px;color:red;">
                    (共{{ data.giveSkuList.map(val => val.everyQty).reduce((total, cur) => total + cur) * item.giveCount }}件赠品)
                  </span>
                </div>
              </div>
              <div v-if="index > 0 &&  !disabled" style="position: relative;font-size: 0;">
                <el-button v-if="index > 0" style="position: relative;top: 50%;transform: translateY(-50%);" type="danger" size="mini" @click="deleteLevel(index)">- 删除阶梯</el-button>
              </div>
            </div>
          </template>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.mz-lwq {
  p {
    margin-top: 0;
  }
}
.lwq-zhuanshujiantou {
  position: absolute;
  width: 0;
  height: 0;
  border: 20px solid #f3f3f3;
  border-bottom-color: transparent;
  border-top-color: transparent;
  border-left-color: transparent;
  left: -30px;
  transition: all 0.3s;
}
.mzLWQ {
  padding: 0 10px;
  background-color: #f3f3f3;
  border-radius: 5px;
  position: relative;
  transition: all 0.2s;
  z-index: 1;
  height: max-content;
  min-width: 350px;
}
.level {
  display: flex;
  gap: 15px;
  padding: 7px 10px;
  background-color: #f3f3f3;
  border-radius: 5px;
  position: relative;
  z-index: 1;

}
.level-mp {
  padding: 7px 0;
  background: white;
  justify-content: start;
  gap: 0;
}
.level-mz {
  position: relative;
  display: flex;
  flex-direction: column;
  gap: 15px;
  justify-content: center;
  align-items: start;
}
</style>
