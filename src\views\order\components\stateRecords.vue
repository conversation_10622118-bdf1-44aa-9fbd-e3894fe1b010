<template>
  <div>
    <el-dialog
      title="订单处理记录"
      :visible="stateVisible"
      width="60%"
      @close="closeDialog"
    >
      <div>
        <el-table
          :data="tableData"
          border
          style="width: 100%"
        >
          <el-table-column
            label="操作时间"
          >
            <template slot-scope="scope">
              <span>{{ scope.row.operationTime | formatDate }}</span>
            </template>
          </el-table-column>
          <el-table-column
            label="操作信息"
          >
            <template slot-scope="scope">
              <div>
                <span v-if="!scope.row.orderStatus && scope.row.logType !== 2">
                  {{ scope.row.operationContent }}
                </span>
                <span v-if="scope.row.logType === 2">
                  商家备注{{ scope.row.isVisible === 1 ? '（客户可见）' : ''}}：{{ scope.row.operationContent }}<br>
                  <a
                    href="javascript:;"
                    style="color: #4184d5"
                    @click="handleDialogSend(scope.row.operationContent)"
                  >点击发送给客户和BD（客服消息形式）</a>
                </span>
                <!-- <span v-if="scope.row.orderStatus == 3">确认收货</span> -->
                <span v-if="scope.row.orderStatus == 2">{{ scope.row.operationContent }}<br> 包裹数 {{ scope.row.packagesNum }} <a
                  href="javascript:;"
                  style="color: #4184d5"
                  @click="lookWl"
                >查看物流信息</a></span>
                <span v-if="scope.row.orderStatus == 7 || scope.row.orderStatus === 32 || scope.row.orderStatus == 3 || scope.row.orderStatus === 10">
                  <!-- 审核结果：{{ scope.row.auditResult === 0? '通过' : '未通过' }} -->
                  {{ scope.row.operationContent }}
                </span>
                <span v-if="scope.row.orderStatus == 1">
                  支付方式：{{ scope.row.payTypeName }}<br>
                  {{ scope.row.bankCardInfo }}<br>
                  支付金额：{{ scope.row.payMoney }}
                </span>
                <span v-if="scope.row.orderStatus == 4">{{ scope.row.operationContent }}<br>取消原因：{{ scope.row.cancelReason }}</span>
                <span v-if="scope.row.orderStatus == 91">整单退款完成</span>
                <!-- <span v-if="scope.row.orderStatus == 32">开单成功</span> -->
                <span v-if="scope.row.orderStatus == 33">
                  <!-- 备货完成 -->
                  {{ scope.row.operationContent }}
                  <a
                    href="javascript:;"
                    style="color: #4184d5"
                    @click="lookFa"
                  >查看发货信息</a>
                </span>

                <div v-if="scope.row.orderStatus == -1">
                  <p>
                    <span v-html="scope.row.operationContent" />
                    <!-- 关联资质状态：关联资质{{ scope.row.exceptionFlag ? '异常' : '正常' }} -->
                    <i
                      v-if="scope.row.exceptionFlag"
                      class="el-icon-search"
                      style="color: #4183d5;marginLeft:5px;"
                      @click="hadnleCheckItem(scope.row)"
                    />
                  </p>
                  <!-- <p>{{ item.exceptionTxt }}</p> -->
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column
            label="订单更新状态"
          >
            <template slot-scope="scope">
              <div>
                <span v-if="scope.row.orderStatus == 3">已完成</span>
                <span v-if="scope.row.orderStatus == 2">配送中</span>
                <span v-if="scope.row.orderStatus == 7">正在开单</span>
                <span v-if="scope.row.orderStatus == 1">待审核</span>
                <span v-if="scope.row.orderStatus == 4">已取消</span>
                <span v-if="scope.row.orderStatus == 91">已退款</span>
                <span v-if="scope.row.orderStatus == 32">分拣中</span>
                <span v-if="scope.row.orderStatus == 33">待配送</span>
                <span v-if="scope.row.orderStatus == 10">待付款</span>
                <span
                  v-if="scope.row.payChannel == 8 && scope.row.evidenceUrlList && scope.row.evidenceUrlList.length>0"
                  class="lookImg"
                  @click="lookImg(scope.row)"
                >查看电汇凭证</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column
            label="操作人"
          >
            <template slot-scope="scope">
              <span>{{ scope.row.operator || '——' }}</span>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <span slot="footer">
        <el-button
          size="mini"
          @click="closeDialog"
        >取 消</el-button>
        <el-button
          type="primary"
          size="mini"
          @click="closeDialog"
        >确 定</el-button>
      </span>
    </el-dialog>
    <logInfoDialog
      ref="logInfoDialog"
    />
    <el-image-viewer
      v-if="showViewer"
      :url-list="srcArr"
      :on-close="closeViewer"
      :z-index="100000"
    />
    <el-dialog title="关联资质异常" :visible="dialogVisible" width="60%" @close="dialogVisible = false">
      <el-table :data="list" border height="400" style="width: 100%; margin-top: 10px;">
        <el-table-column prop="content" label="异常类型">
          <template slot-scope="scope">
            <span>{{ scope.row.exceptionTxt }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="content" label="详情" width="300">
          <template slot-scope="scope">
            <p
              v-for="item in scope.row.exceptionDetailList"
              :key="item"
            >
              {{ item }}
            </p>
          </template>
        </el-table-column>
        <el-table-column prop="operateTime" label="最后操作时间">
          <template slot-scope="scope">
            <div>{{ scope.row.operateTime }}</div>
          </template>
        </el-table-column>
      </el-table>
      <span slot="footer" class="dialog-footer">
        <el-button size="small" type="primary" @click="dialogVisible = false">确认</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { getHostName, getOrderStatusrecords } from '@/api/order/index';
import ElImageViewer from 'element-ui/packages/image/src/image-viewer';
import logInfoDialog from './logInfoDialog.vue';

export default {
  name: 'StateRecords',
  components: { logInfoDialog, ElImageViewer },
  data() {
    return {
      list: [],
      dialogVisible: false,
      tableData: [],
      stateVisible: false,
      orderNoStr: '',
      merId: '',
      showViewer: false,
      srcArr: [],
      fastDfsHost: '',
    };
  },
  created() {
    getHostName().then((res) => {
      this.fastDfsHost = (res || {}).fastDfsHost || '';
    });
  },
  methods: {
    hadnleCheckItem(item) {
      this.list = item.exceptionList;
      this.dialogVisible = true;
    },
    handleDialogSend(remark) {
      this.$parent.popDialogSend(this.orderNoStr, remark);
    },
    getListData(orderNo, id) {
      this.stateVisible = true;
      this.orderNoStr = orderNo;
      this.merId = id;
      getOrderStatusrecords({ orderNo }).then((res) => {
        if (res.code === 0) {
          this.tableData = res.data;
        } else {
          this.$message.error({ message: res.message, offset: 100 });
        }
      });
    },
    closeDialog() {
      this.stateVisible = false;
    },
    lookFa() {
      this.$emit('setLocal');
      this.$router.push({ path: '/orderList/delivery', query: { orderNo: this.orderNoStr, merchantId: this.merId, detail: true } });
    },
    lookWl() {
      this.$refs.logInfoDialog.getListData(this.orderNoStr);
    },
    closeViewer() {
      this.showViewer = false;
    },
    lookImg(data) {
      this.srcArr = [];
      data.evidenceUrlList.forEach((item) => {
        this.srcArr.push(item);
      });
      // this.srcArr = data.evidenceUrlList?data.evidenceUrlList:[];
      this.showViewer = true;
    },
  },
};
</script>

<style scoped lang="scss">
.lookImg{
  padding-left: 15px;
  color: #1d69c4;
  cursor: pointer;
}
.flexBox{
  display: flex;
  align-items: center;
}
::v-deep  .el-dialog__body{
  padding: 10px 20px;
}
::v-deep  .el-dialog__header{
  padding: 10px 20px;
  background: #f9f9f9;
}
::v-deep  .el-dialog__headerbtn{
  top: 15px;
}
::v-deep  .el-dialog__title{
  font-size: 16px;
}
</style>
