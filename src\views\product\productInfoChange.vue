<template>
  <div class="product-info-change" v-loading="pageLoading">
    <div class="contentBox">
      <SearchForm
        ref="searchForm"
        :model="formModel"
        :form-items="formItems"
        @submit="handleFormSubmit"
        @reset="handleFormReset"
      >
        <template slot="form-item-status">
          <el-select v-model="formModel.status" placeholder="请选择" @change="checkIsHidden">
            <el-option
              v-for="(item, index) in productStatusOptions"
              :key="index"
              :label="item.statusName"
              :value="item.statusType"
            />
          </el-select>
        </template>
        <template slot="form-item-category">
          <el-cascader
            ref="productCategoryLevel"
            v-model="formModel.categoryId"
            :options="productCategoryLevelOptions"
            :props="{ label: 'name', value: 'id', checkStrictly: true }"
            :show-all-levels="false"
            clearable
            @change="handleCascaderVal"
          />
        </template>
      </SearchForm>
      <div class="operation">
        <el-button
          type="primary"
          size="small"
          @click="useThenCurrent"
          >使用当前标品信息更新商品</el-button
        >
        <el-button
          type="primary"
          size="small"
          @click="rebind"
          >重新绑定其他标品</el-button
        >

        <el-button
          type="primary"
          size="small"
          @click="correction"
          >标品信息有误进行纠错</el-button
        >
      </div>
      <el-tabs v-model="activeName" @tab-click="tabHandleClick">
        <el-tab-pane
          v-for="item in tabStatusOptions"
          :key="item.statusType"
          :label="tabPaneLabel(item)"
          :name="String(item.statusType)"
        />
      </el-tabs>
      <xyyTable
        ref="dproductListTable"
        v-loading="tableLoading"
        :data="tableConfig.data"
        :col="tableConfig.col"
        :has-selection="true"
        :list-query="listQuery"
        @selectionCallback="selectionCallback"
        @get-data="queryList"
      >
        <template :slot="doc.slot" v-for="doc in tableConfig.col">
          <el-table-column label="商品编码" v-if="doc.slot === 'barcode'" width="160">
            <template slot-scope="{ row }">
              <div>
                <div>{{ row.barcode }}</div>
                <div>
                  <span class="red-font">当前链接数：{{ row.childProductNum }}</span>
                  <i class="el-icon-question" title="当前链接数包含同原品下的拼团商品、批购包邮商品和赠品数量" />
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="商品图片" v-else-if="doc.slot === 'imageUrl'">
            <template slot-scope="{ row }">
              <div>
                <div>
                  商家：<el-image
                    style="width: 80px; height: 73px; margin-bottom: 4px"
                    :src="row.fullImageUrl"
                    :preview-src-list="row.allImageList || []"
                    @click.prevent
                    v-if="row.fullImageUrl"
                  />
                </div>
                <div>
                  平台：<el-image
                    style="width: 80px; height: 73px"
                    :src="getImageUrlNew(row,0)"
                    :preview-src-list="getImageUrlNew(row,1)"
                    @click.prevent
                    v-if="getImageUrlNew(row,0)"
                  />
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column :label="doc.name" v-else>
            <template slot-scope="{ row }">
              <div :class="{ 'red-font': compareDifferences(row[doc.sjKey], row.standardProduct[doc.ptKey]) }">
                <div :class="{ 'red-font': !row[doc.sjKey] && row.standardProduct[doc.ptKey] }">商家：{{ row[doc.sjKey] }}</div>
                <div :class="{ 'red-font': row[doc.sjKey] && !row.standardProduct[doc.ptKey] }">平台：{{ row.standardProduct[doc.ptKey] }}</div>
              </div>
            </template>
          </el-table-column>
        </template>
      </xyyTable>
      <goodsMoreCheckAlert ref="goodsMoreCheckAlert" :fromType="'goods-update-alert'" @finish="refreshData" />
    </div>
  </div>
</template>
<script>
import xyyTable from './components/table'
import SearchForm from '@/components/searchForm'
import goodsMoreCheckAlert from './components/goodsMoreCheckAlert'
import { searchItem } from './config'
import { mapState } from 'vuex'
import {
  loadStatusCounts,
  categoryTree,
  getProductList,
  batchUpdateOnStandardProduct,
  warnInfo,
  compareFields
} from '@/api/product'
const SHOW_STATUS_OPTIONS = ['销售中','缺货下架','下架','待上架']
export default {
  data() {
    return {
      formModel: {
        showName: '', // 商品名称
        erpCode: '', // erp编码
        manufacturer: '', // 生产厂家
        approvalNumber: '', // 批准文号
        status: -99, // 状态:1-销售中，4-下架，6-待上架，8-待审核，9-审核未通过，20-删除
        categoryId: '', // 商品分类id
        saleType: '', // 药品类型
        spec: '' //商品规格
      },
      formItems: searchItem.formItemsChange,
      productStatusOptions: [],
      activeName: '-99',
      productCategoryLevelOptions: [],
      tableConfig: {
        data: [],
        col: [
          {
            name: '商品编码',
            index: 'barcode',
            slot: 'barcode',
          },
          {
            name: '商品名称',
            index: 'productName',
            slot: 'productName',
            ptKey: 'productName',
            sjKey: 'productName'
          },
          {
            name: '通用名称',
            index: 'commonName',
            slot: 'commonName',
            ptKey: 'commonName',
            sjKey: 'commonName'
          },
          {
            name: '品牌',
            index: 'brand',
            slot: 'brand',
            ptKey: 'brand',
            sjKey: 'brand'
          },
          {
            name: '批准文号',
            index: 'approvalNumber',
            slot: 'approvalNumber',
            ptKey: 'approvalNumber',
            sjKey: 'approvalNumber'
          },
          {
            name: '生产厂家',
            index: 'manufacturer',
            slot: 'manufacturer',
            ptKey: 'manufacturer',
            sjKey: 'manufacturer'
          },
          {
            name: '规格',
            index: 'spec',
            slot: 'spec',
            ptKey: 'spec',
            sjKey: 'spec'
          },
          {
            index: 'producer',
            name: '产地',
            slot: 'producer',
            ptKey: 'producer',
            sjKey: 'producer'
          },
          {
            index: 'productUnit',
            name: '单位',
            slot: 'productUnit',
            ptKey: 'productUnit',
            sjKey: 'productUnit'
          },
          {
            index: 'imageUrl',
            name: '商品图片',
            slot: 'imageUrl'
          }
        ],
        operation: [
          {
            name: '详情',
            type: 1
          },
          {
            name: '编辑',
            type: 2
          },
          {
            name: '上架',
            type: 3
          },
          {
            name: '删除',
            type: 4
          },
          {
            name: '预览',
            type: 5
          },
          {
            name: '查看操作日志',
            type: 6
          }
        ]
      },
      listQuery: {
        pageSize: 10,
        page: 1,
        total: 0
      },
      selectList: [],
      pageLoading: false,
      tableLoading: false
    }
  },
  computed: {
    ...mapState('app', ['shopConfig']),
    tabStatusOptions() {
      const options = this.productStatusOptions.map((item) => ({
        statusName: item.statusType === -99 ? '全部商品' : item.statusName,
        statusType: item.statusType,
        count: item.count
      }))
      return options
    }
  },
  components: {
    SearchForm,
    xyyTable,
    goodsMoreCheckAlert
  },
  created() {
    this.getCategoryTree()
    this.refreshData()
  },
  methods: {
    refreshData() {
      this.getList()
      this.loadStatusCounts()
    },
    tabHandleClick() {
      this.formModel.status = Number(this.activeName)
      this.listQuery.page = 1
      this.refreshData()
      this.checkIsHidden()
    },
    tabPaneLabel(item) {
      let str = ''
      if (item.count || item.count === 0) {
        str = `${item.statusName}(${item.count})`
      } else {
        str = `${item.statusName}`
      }
      return str
    },
    handleFormSubmit() {
      this.activeName = String(this.formModel.status)
      this.listQuery.page = 1
      this.refreshData()
    },
    handleFormReset(obj) {
      this.formModel = obj
      this.listQuery = {
        page: 1,
        pageSize: 10,
        total: 0
      }
      this.activeName = String(this.formModel.status)
      this.$nextTick(()=>{
        this.refreshData()
      })
    },
    async loadStatusCounts() {
      try {
        const res = await loadStatusCounts({...this.getRequestParams(false,false)})
        if (res) {
          if (this.shopConfig.isFbp) {
            const arr = res.filter((i) => i.statusType != 2)
            const arr1 = arr.filter(i=>SHOW_STATUS_OPTIONS.includes(i.statusName))
            this.productStatusOptions = [
              {
                statusName: '全部',
                statusType: -99
              },
              ...arr1
            ]
          } else {
            const arr1 = res.filter(i=>SHOW_STATUS_OPTIONS.includes(i.statusName))
            this.productStatusOptions = [
              {
                statusName: '全部',
                statusType: -99
              },
              ...arr1
            ]
          }
        }
      } catch (e) {
        console.log(e)
      }
    },
    async getCategoryTree() {
      try {
        const res = await categoryTree()
        if (res.code === 0) {
          this.productCategoryLevelOptions = res.data.children
        }
      } catch (e) {
        console.log(e)
      }
    },
    handleCascaderVal(value) {
      this.formModel.businessFirstCategory = value[0] || ''
      this.formModel.businessSecondCategory = value[1] || ''
    },
    checkIsHidden() {
      const list = [...this.formItems]
      list.forEach((item, index) => {
        if (item.prop == 'trackingCode' && this.formModel.status === 20) {
          list[index].isHidden = true
        } else if (item.prop == 'trackingCode') {
          list[index].isHidden = false
        }
      })
      this.formItems = list
    },
    selectionCallback(selet) {
      this.selectList = selet
    },
    async queryList(listQuery) {
      if (this.formModel.status === 20) {
        // 删除条件下不显示是否有追溯码查询字段并设置默认值全部
        this.formModel.tracingCode = null
      }
      if (listQuery) {
        const { pageSize, page } = listQuery
        this.listQuery.pageSize = pageSize
        this.listQuery.page = page
      }
      this.refreshData()
    },
    async getList() {
      this.tableLoading =  true
      try {
        let res = await getProductList(this.getRequestParams())
        const {
          result: { list, total }
        } = res
        this.tableConfig.data = list
        this.listQuery.total = total
      } catch {
        this.tableConfig.data = []
        this.listQuery.total = 0
      }
      this.selectList = []
      if(this.$route.meta.showRedDotNum)
        this.$route.meta.showRedDotNum = false
      this.tableLoading =  false
    },
    useThenCurrent() {
      if (this.selectList.length === 0) {
        return this.$message.warning('请选择需要处理的商品')
      } else if (this.selectList.length > 10) {
        return this.$message.error('每次最多处理10个商品')
      } else {
        this.$confirm(
          '使用标品信息更新商品后会更新商品原品&复制品的商品信息，请确认是否使用标品信息更新商品！',
          '商品信息更新提醒',
          {
            confirmButtonText: '确认使用标品信息',
            cancelButtonText: '暂不处理',
            type: 'warning'
          }
        )
          .then(async () => {
            this.batchUpdateOnStandardProduct()
          })
          .catch(() => {
          })
      }
    },
    rebind() {
      if (this.selectList.length === 0) {
        return this.$message.warning('请选择需要处理的商品')
      } else if (this.selectList.length > 1) {
        return this.$message.warning('不支持批量绑定其他标品，请选择一个商品')
      } else {
        let row = this.selectList[0]
        this.hasUpdateGoods(row)
      }
    },
    correction() {
      if (this.selectList.length === 0) {
        return this.$message.warning('请选择需要处理的商品')
      } else if (this.selectList.length > 1) {
        return this.$message.warning('不支持批量纠错标品，请选择一个商品')
      } else {
        let row = this.selectList[0]
        this.jumpAgain(row, 'edit')
      }
    },
    jumpAgain(row, type) {
      const path = '/product/errorEdit'
      const obj = {
        from: 'productInfoChange',
        verify: true,
        barCode: row.barcode,
        id: row.id,
        type: type,
        firstCategory: row.businessFirstCategoryCode
      }
      window.openTab(path, obj)
    },
    removeSpecialCharactersAndToLower(str) {
      if (!str) return str
      // 定义需要去除的字符集合
      const specialCharactersRegex =
        /[\s（）()"'：:；，\-+\/\\&|？#^*￥$@。~,{}\[\]【】]/g
      // 去除特殊字符
      const withoutSpecialCharacters = str.replace(specialCharactersRegex, '')
      // 将所有英文字母转换为小写
      const lowerCaseString = withoutSpecialCharacters.toLowerCase()
      // 返回结果
      return lowerCaseString
    },
    compareDifferences(str1, str2) {
      return str1 && str2 && this.removeSpecialCharactersAndToLower(str1) != this.removeSpecialCharactersAndToLower(str2)
    },
    batchUpdateOnStandardProduct() {
      this.pageLoading =  true
      batchUpdateOnStandardProduct({
        barcodes: this.selectList.map((item) => item.barcode)
      }).then(res=>{
        if(res.code == 0){
          let timeout = res?.data || 0
          setTimeout(() => {
            this.$message.success('更新成功！')
            this.pageLoading =  false
            this.refreshData()
          }, timeout);
        }else {
          this.pageLoading =  false
          return this.$message.error(res.message)
        }
      }).catch(err=>{
        console.warn(err)
        this.pageLoading =  false
      })     
    },
    getImageUrlNew(data, type=0) {
      const arr = data?.standardProduct?.imageUrl?.split(',') || []
      if(type == 0){
        return arr[0] || ""
      }else if(type == 1){
        return arr || []
      }
    },
    getRequestParams(haveStatus=true,havePage=true){
      const params = { ...this.formModel }
      params.status === -99 ? (params.status = '') : ''
      if(havePage){
        const { pageSize, page } = this.listQuery
        params.page = page
        params.rows = pageSize
      }
      params.activityTypes = 0
      params.partStandardChangeMark = 1
      params.queryStandardProduct = 1
      params.statuses='1,2,3,4,6'
      try{
        const { data: { level, id } } = this.$refs.productCategoryLevel.getCheckedNodes(true)[0]
        if (level && id) {
          params.categoryLevel = level
          params.categoryId = id
        }
      }catch{}
      if(!haveStatus) delete params.status
      return params
    },
    hasUpdateGoods(row) {
      compareFields().then((ress) => {
        if (ress.code != 0) {
          this.$message.warning(ress.message)
          return
        }
        warnInfo({ barcode: row.barcode, source: row.source }).then((res) => {
          if (res.code !== 0) {
            this.$message.error(res.message)
            return
          }
          let data = res.data.meSkuInfo
          let oldData = res.data.popSkuInfo
          let oldIMGUrl = this.getImageUrl(oldData)
          let newImgUrl = this.getImageUrl(data)
          oldData.imagesList = {
            urlVal: oldIMGUrl ? oldIMGUrl : ''
          }
          data.imagesList = newImgUrl ? newImgUrl : undefined
          data.source = row.source
          oldData.source = row.source
          const keys = this.getFirstCategoryData(
            ress.data,
            data.businessFirstCategoryCode
          )
          oldData['firstCategory'] = oldData.businessFirstCategoryCode
          oldData['barCode'] = row.barcode
          this.$refs.goodsMoreCheckAlert.open(oldData, row.barcode, row)
        })
      })
    },
    getImageUrl(data) {
      if (data.imageUrl) {
        return data.imageUrl.split(',')[0]
      }
      if (data.instrutionImageUrl) {
        return data.instrutionImageUrl.split(',')[0]
      }
      return ''
    },
    getFirstCategoryData(data, categoryid) {
      if (
        categoryid.toString() === '100001' ||
        categoryid.toString() === '100007' ||
        categoryid.toString() === '100008'
      ) {
        return data['nomalList']
      } else if (
        categoryid.toString() === '100004' ||
        categoryid.toString() === '100010' ||
        categoryid.toString() === '262683'
      ) {
        return data['chiMedList']
      } else if (
        categoryid.toString() === '100002' ||
        categoryid.toString() === '100009'
      ) {
        return data['drugList']
      } else if (
        categoryid.toString() === '100003' ||
        categoryid.toString() === '262104'
      ) {
        return data['healthList']
      } else if (categoryid.toString() === '100005') {
        return data['instrumentList']
      } else {
        return []
      }
    },
  }
}
</script>
<style lang="scss" scoped>
.product-info-change {
  .contentBox {
    padding: 16px 16px;
    background: #fff;
    margin-bottom: 10px;

    .operation {
      margin-bottom: 12px;
    }
  }

  ::v-deep .el-tabs__item.is-active {
    border: 1px solid #eeeeee;
    border-top-left-radius: 5px;
    border-top-right-radius: 5px;
    border-bottom: none;
  }

  ::v-deep .el-tabs--top .el-tabs__item:last-child {
    padding-right: 20px;
  }

  ::v-deep .el-tabs--top .el-tabs__item.is-top:nth-child(2) {
    padding-left: 20px;
  }

  ::v-deep #tabled tr td .cell {
    height: unset;
    line-height: 1.5;
    white-space: wrap;
    color: #606266;
    text-align: left;
    .el-icon-question {
      color: #666666;
      margin-left: 6px;
    }
    .red-font {
      color: red;
    }
  }
}
</style>
