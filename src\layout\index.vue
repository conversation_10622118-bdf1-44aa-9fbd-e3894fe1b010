<template>
  <div
    class="app-wrapper"
    :class="classObj"
  >
    <Head-part />
    <Slide-part class="sidebar-container" />
    <div class="main-container hasTagsView">
      <Tabs-part />
      <App-main />

    </div>
    <CustomerService />
    <DialogMessage />

  </div>
</template>

<script>
import { mapState } from 'vuex';
import HeadPart from './components/HeadPart';
import SlidePart from './components/slidePart';
import TabsPart from './components/tabsPart';
import CustomerService from '@/views/home/<USER>/customerService';
import DialogMessage from '@/views/home/<USER>/dialogMessage';
import AppMain from './AppMain';


export default {
  name: 'Index',
  components: {
    HeadPart,
    SlidePart,
    TabsPart,
    AppMain,
    CustomerService,
    DialogMessage,
    
  },
  computed: {
    ...mapState({ sidebar: state => state.app.sidebar }),
    classObj() {
      return {
        hideSidebar: !this.sidebar.opened,
        openSidebar: this.sidebar.opened,
      };
    },
  },
};
</script>

<style scoped lang="scss">
@import "~@/styles/mixin.scss";
@import "~@/styles/variables.scss";

.app-wrapper {
  @include clearfix;
  position: relative;
  height: calc(100% - 120px);
  width: 100%;
}
</style>
