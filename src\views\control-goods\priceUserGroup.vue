<template>
  <div class="main-box">
    <div class="explain-search searchMy">
      <el-form ref="ruleForm" :inline="true" >
        <el-form-item >
            <el-input v-model.trim="searchData.keyword" size="medium" style="width: 350px;" placeholder="可输入用户组ID或用户组名称">
              <template slot="prepend">用户组ID/名称</template>
            </el-input>
        </el-form-item>
        <el-form-item >
          <el-button @click="resetForm()"  size="medium">重置</el-button>
          <el-button type="primary" @click="getGroup('search')" size="medium">查询</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="list-box">
      <div style="padding: 10px 0px">
        <el-row style="padding-bottom: 20px">
          <addGroup @search="getGroup"></addGroup>
          <el-button type="primary" size="small" style="margin-left: 5px;" @click="exportGroup">导出</el-button>
        </el-row>

        <div class="customer-tabs">
          <el-table
            border
            v-loading="laodingBoole"
            max-height="397"
            :data="tableData.list"
            stripe
            style="width: 100%"
            :header-cell-style="{ background: '#F9F9F9', color: '#666666' }"
          >
            <el-table-column prop="id" label="用户组ID" min-width="120" align="center"/>
            <el-table-column prop="groupName" label="用户组名称" min-width="200" align="center"/>
            <el-table-column prop="areaNames" label="地域" min-width="350" align="center"  >
              <template #default="scope">
                <el-tooltip
                  v-if="isTextOverflow(scope.row.areaNames)"
                  class="item"
                  effect="dark"
                  :content="scope.row.areaNames"
                  placement="top">
                  <div class="text-ellipsis">{{ scope.row.areaNames }}</div>
                </el-tooltip>
                <div v-else class="text-ellipsis">{{ scope.row.areaNames }}</div>
              </template>
            </el-table-column>
            <el-table-column prop="customerTypeNames" label="客户类型" min-width="400" align="center">
              <template #default="scope">
                <el-tooltip
                  v-if="isTextOverflow(scope.row.customerTypeNames)"
                  class="item"
                  effect="dark"
                  :content="scope.row.customerTypeNames"
                  placement="top">
                  <div class="text-ellipsis">{{ scope.row.customerTypeNames }}</div>
                </el-tooltip>
                <div v-else class="text-ellipsis">{{ scope.row.customerTypeNames }}</div>
              </template>
            </el-table-column>
            <el-table-column prop="createDate" label="创建时间" min-width="180" align="center">
            </el-table-column>
            <el-table-column
              align="center"
              fixed="right"
              label="操作"
              min-width="150">
              <template slot-scope="scope">
                <!-- <el-button type="text" size="small" style="margin-right: 10px;" @click="editGroup(scope.row)">编辑 </el-button> -->
                <addGroup @search="getGroup" :group-message="groupMessage">
                  <el-button type="text" size="small" style="margin-right: 10px;" @click="editGroup(scope.row)">编辑</el-button>
                </addGroup>
                <el-button type="text" size="small" style="margin-right: 10px;" @click="viewChange(scope.row)" > 查看日志 </el-button>
                <el-button type="text" size="small" style="color: red;" @click="openDelDia(scope.row)"> 删除</el-button>
              </template>
            </el-table-column>
          </el-table>
          <div class="explain-pag">
            <Pagination
              v-show="tableData.total > 0"
              :total="tableData.total"
              :page.sync="pageData.pageNum"
              :limit.sync="pageData.pageSize"
              @pagination="getGroup"
            />
          </div>
        </div>
      </div>
    </div>
    <viewChangeRecords
      v-if="changeRecordsVis"
      v-model="changeRecordsVis"
      :row="rowData"
    />
    <export-tip
      :change-export="changeExport"
      @handleExoprClose="handleExoprClose"
      @handleChangeExport="handleChangeExport"
    />
    <el-dialog
      title="提示"
      :visible.sync="delDialogVis"
      width="30%"
      class="delDialog"
      >
      <div style="margin-bottom:5px">用户组名称:{{ groupName}}</div>
      <div v-if="relevantCount>0" style="color: red;margin-top:5px">
        当前用户组已被{{relevantCount}}个商品引用，若删除价格用户组，系统会同步将以上对应商品的用户组价格删除
      </div>
      <div>确认删除当前用户组吗？</div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="delDialogVis = false">取 消</el-button>
        <el-button type="primary" @click="deleteGroupAction()">确 定</el-button>
      </span>
    </el-dialog>

  </div>
</template>
<script>
import Pagination from '@/components/Pagination';
import exportTip from '@/views/other/components/exportTip'
import utils from '@/utils/filter';
import viewChangeRecords from './components/viewChangeRecords.vue';
import addGroup from './components/addGroup.vue';
import { getUserGroupList,checkGroupCount ,deleteGroup,exportUserGroup} from '@/api/product';
export default {
  name: 'priceUserGroup',
  components: {
    exportTip,
    viewChangeRecords,
    addGroup,
    Pagination,
  },
  data() {
    return {
      changeExport: false,
      groupName:'',
      id:'',
      relevantCount:0,//关联商品数量
      delDialogVis:false,//删除弹框
      changeRecordsVis: false, // 查看变更记录弹窗
      rowData:undefined,
      groupMessage:undefined,
      showAdd:false,
      searchData: {
        keyword:''
      },
      tableData: {
        total: 0,
        list: [],
      },
      pageData: {
        pageSize: 10,
        pageNum: 1,
      },
      laodingBoole: false
    }
  },
  watch:{
    //弹窗关闭后刷新列表
    showAdd:function(val){
      if(!val){
        this.pageData.pageNum = 1
        this.getGroup()
      }

    }
  },
  created() {
  // 获取列表数据
  this.getGroup()
  },
  methods: {
    isTextOverflow(text) {
      // 创建一个隐藏的临时元素来计算高度
      const el = document.createElement("div");
      el.className = "text-ellipsis";
      el.style.position = "absolute";
      el.style.visibility = "hidden";
      el.style.width = "200px";
      el.innerText = text;
      document.body.appendChild(el);

      // 检查内容高度是否超过两行
      const isOverflow = el.scrollHeight > el.offsetHeight;
      document.body.removeChild(el);
      return isOverflow;
    },
    //打开删除弹框判断关联用户组
    openDelDia(row) {
      this.delDialogVis = true
      this.groupName = row.groupName
      this.id = row.id
      checkGroupCount({groupId:this.id}).then((res) => {
        this.relevantCount = res.result
      })
    },

    //删除用户组
    deleteGroupAction() {
      deleteGroup({groupId:this.id}).then((res)=>{
        console.log(res);
        this.$message({
              message: res.result,
              type:res.msg,
              })
        this.getGroup()
        this.delDialogVis = false
      })
    },
    //查看日志
    viewChange(row){
      console.log(row);
      this.rowData = row;
      this.changeRecordsVis = true

    },
    // 获取用户组列表
    getGroup(type) {
      const that = this
      if(type === 'search') {
        this.pageData.pageNum = 1
      }
      this.laodingBoole = true
      const param = {
        ...this.searchData,
        ...this.pageData
      }
      getUserGroupList(param).then((res) => {
        // console.log('数据',res);
        if(res.code === 0){
          this.laodingBoole = false
          if(res.result) {
            that.tableData.list = res.result.list
          } else {
            that.tableData.list = []
          }
          that.tableData.total = res.result.total
        } else {
          this.laodingBoole = false;
          this.$message({
            message: res.message,
            type: 'error',
          });
        }
      }).catch(() => {
        this.laodingBoole = false;
      });

    },
    // 新增用户组
    addGroup() {
      this.showAdd = true
    },
    // 编辑用户组
    editGroup(row) {
      console.log(row);

      this.groupMessage = {...row}

    },
    // 重置列表数据
    resetForm() {
      this.searchData = {
        keyword: ''
      };
      this.pageData = {
        pageSize: 10,
        pageNum: 1,
      };
      this.getGroup();
    },
    // 导出
    async exportGroup() {
      const params = this.keyword
      exportUserGroup(params).then((res) => {
        if (res.code !== 0) {
            this.$message.error(res.result)
            return
          }
        this.changeExport= true
      })
    },
    handleChangeExport(info) {
      this.changeExport = false
      if (info === 'go') {
        const path = '/downloadList'
        window.openTab(path)
        // that.$router.push({ path: '/downloadList' });
      }
    },
    handleExoprClose() {
      this.changeExport = false
    },

  }
}
</script>

<style lang="scss" scoped>
.delDialog ::v-deep   .el-dialog__header{
  background-color:  #f5f5f580;
  .el-dialog__title{
    color: red;
  }

}
.searchMy ::v-deep  .el-form-item__label {
  margin-left: 20px;
  padding: 0;
}
.searchMy ::v-deep  .el-input__inner {
  border-radius: 0 4px 4px 0;
}
.searchMy ::v-deep  .el-form-item__content {
  width: 100%;
}
.searchMy ::v-deep  .el-form-item--small.el-form-item {
  margin-bottom: 10px;
  width: 32%;
}
.searchMy ::v-deep  .el-form-item--small .el-form-item__content {
  line-height: 30px;
  width: 100%;
}
.searchMy ::v-deep  .el-input-group__prepend {
  background: none;
  color: #333333;
  padding: 0 10px;
}
.searchMy ::v-deep   .el-form-item--small.el-form-item {
  width: 24%;
}
.blueText {
  color: #4183d5;
  cursor: pointer;
}
.main-box {
  padding: 20px;
  .list-box {
    .customer-tabs {
      .el-button + .el-button {
        margin-left: 0px;
      }
    }
  }
}

</style>

<style>
.text-ellipsis {
  display: -webkit-box;
  -webkit-line-clamp: 2; /* 限制显示2行 */
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: normal;
}


</style>
