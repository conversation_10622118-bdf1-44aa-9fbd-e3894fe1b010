<template>
  <div class="contentWrapper">
    <el-form v-if="importType === 2">
      <span v-if="batchApplyInfo.applyType === 2" key="product_report">
        <!-- 供货方式配置信息 -->
        <SupplyTypeConfig :dis="false" :iseditNew="activityInfo.limitCustomerStatus" :baseCustomerGroupName="activityInfo.limitCustomerGroupName" :baseCustomerGroupId="activityInfo.limitCustomerGroupId"  ref="supplyTypeInfo" />
      </span>
      <el-row class="importBottom">
        <el-button type="primary" class="xyy-blue importBtn" size="small" :loading="btnLoadingPageTwo" @click="determineDialog()">提取活动信息</el-button>
      </el-row>
    </el-form>
    <el-form v-if="importType === 3" key="product">
      <el-form-item 
        label='批量导入活动ID'
        prop="applyType"
        label-width="150px">
            <div style="display:flex;align-items:flex-start;">
              <el-upload action="xxx" ref="excludeImport" :http-request="uploadFile" :show-file-list="true"  :auto-upload="false" :on-change="handleChange" :on-remove="handleRemove" :file-list="activityFileList" :before-upload="beforeImportData" accept=".xls, .xlsx, .XLS, .XLSX">
                <el-button size="small" type="primary" :disabled="batchApplyInfo.applyType == 1" style="margin-right: 10px;">选择文件</el-button>
                <span style="margin:0 20px 0 6px;color: #4183d5;cursor: pointer;" @click.stop="download()">
                <i class="el-icon-download"></i>
                模板下载
              </span>
              </el-upload>
             
            </div>
            <p style="margin: 0;height: 30px;line-height: 30px;color: #939393" v-if="activityFileList.length==0">未选择任何文件</p>
          <div class="tipBox">
            <p>1、请上传编辑好的xlsx,xls文件</p>
            <p>2、单次最大支持导入1000条</p>
            <p>3、导入成功后将复用原活动拼团价格，起拼数量，虚拟供应商，单店限购，总限购等信息。</p>
          </div>
      </el-form-item>
      <span v-if="batchApplyInfo.applyType === 2" key="import_activity_id">
        <!-- 供货方式配置信息 -->
        <SupplyTypeConfig :dis="false" :iseditNew="activityInfo.limitCustomerStatus" :baseCustomerGroupName="activityInfo.limitCustomerGroupName" :baseCustomerGroupId="activityInfo.limitCustomerGroupId"  ref="supplyTypeInfo" />
      </span>
      <el-row class="importBottom">
        <el-button type="primary" class="xyy-blue importBtn" size="small" :loading="btnLoadingPageThree" @click="determineDialog()">提取活动信息</el-button>
      </el-row>
    </el-form>

    <el-form v-if="importType === 4">
      <el-form-item 
        label='批量导入商品'
        prop="applyType"
        label-width="150px">
          <div style="display:flex;align-items:flex-start;">
              <el-upload action="xxx" ref="excludeImport" :http-request="uploadFile" :show-file-list="true"  :auto-upload="false" :on-change="handleChange" :on-remove="handleRemove" :file-list="productFileList" :before-upload="beforeImportData" accept=".xls, .xlsx, .XLS, .XLSX">
                <el-button size="small" type="primary" :disabled="batchApplyInfo.applyType == 1" style="margin-right: 10px;">选择文件</el-button>
                <span style="margin:0 20px 0 6px;color: #4183d5;cursor: pointer;" @click.stop="download()">
                <i class="el-icon-download"></i>
                新模板下载
              </span>
              </el-upload>
             
          </div>
          <p style="margin: 0;height: 30px;line-height: 30px;color: #939393" v-if="productFileList.length==0">未选择任何文件</p>
          <div class="tipBox">
            <p>1、请上传编辑好的xlsx文件，其中商品编码为普通商品编码；</p>
            <p>2、人群ID或业务商圈、供货对象和黑白名单相同的拼团活动可批量提交，否则请分批提交；</p>
            <!-- <p>3、模板文件 
              <span style="margin:0 20px 0 6px;color: #4183d5;cursor: pointer;" @click="download()">旧模板下载</span>
            </p> -->
          </div>
      </el-form-item>
       <span v-if="batchApplyInfo.applyType === 2" key="import_product_id">
          <!-- 供货方式配置信息 -->
          <SupplyTypeConfig :dis="false" :iseditNew="activityInfo.limitCustomerStatus == 1" :isFour="true" :baseCustomerGroupName="activityInfo.limitCustomerGroupName" :baseCustomerGroupId="activityInfo.limitCustomerGroupId"  ref="supplyTypeInfo" />
        </span>
        <el-row class="importBottom">
          <el-button type="primary" class="xyy-blue importBtn" size="small" :loading="btnLoading" @click="determineDialog()">提取活动信息</el-button>
        </el-row>
    </el-form>

    <!-- 未选数据范围的弹窗 -->
    <el-dialog
      title="提示"
      :visible.sync="dialogVisible"
      width="30%">
      <span>请选择数据范围</span>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="dialogVisible = false">我知道了</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import {
  getTimeStr,
  uploadGoodsCustomer,
  getSkuByBarcode,
  checkImportGroupProduct,
  dynamicProducts,
  batchImportActivities,
  batchImportProducts,
  productsCheck
} from '@/api/market/collageActivity';
import SupplyTypeConfig from './components/supplyTypeConfig.vue';

export default {
  components: { SupplyTypeConfig },
  props: {
    importFormat:{
      type: Number,
    
    },
    importType: {
      type: Number,
      default: 1
    },
    productDataRange: {
      type: Number,
      default: 0
    },
    frameReportId: {
      default: ""
    },
    activityInfo: {
      type: Object,
      default: function() {
        return {
          registrationStime: '',
          registrationEtime: '',
          auditStime: '',
          auditEtime: '',
          validStime: '',
          validEtime: '',
          description: '',
          isAutoAudit: 0, // 是否自动审核 1是自动审核 0是不自动审核
          preheatStime: '',
          limitCustomerGroupId: '',
        }
      }
    },
    importFormat: {
      type: Number,
      default: 1
    }
  },
  data() {
    return {
      customerGroupVO: null,
      // activityInfo: {
      //   registrationStime: '',
      //   registrationEtime: '',
      //   auditStime: '',
      //   auditEtime: '',
      //   validStime: '',
      //   validEtime: '',
      //   description: '',
      //   isAutoAudit: 0, // 是否自动审核 1是自动审核 0是不自动审核
      //   preheatStime: '',
      //   limitCustomerGroupId: '',
      // },
      // frameReportId: '',
      groupBuyIngApplyImportProductVos: [],
      importLoading: false,
      btnLoading: false,
      btnLoadingPageTwo: false,
      btnLoadingPageThree: false,
      weekObj: {
        1: '周一',
        2: '周二',
        3: '周三',
        4: '周四',
        5: '周五',
        6: '周六',
        7: '周日'
      },
      activityFileList: [],
      productFileList: [],
      batchApplyInfo: {
        tempBarcode: '',
        applyType: 2, // 拼团活动申请方式
      },
      dialogVisible: false // 是否选择活动数据范围
    }
  },
  computed: {},
  watch: {

  },
  // created() {
  //   const { query } = this.$route
  //   this.frameReportId = query.frameReportId ? query.frameReportId : '';
  //   getTimeStr({ frameReportId: this.frameReportId }).then((res) => {
  //     if (res.code === 1000) {
  //       this.activityInfo = { ...res.data.activityReportBaseResDTO };
  //     } else {
  //       this.$message.error(res.msg)
  //     }
  //   });
  //   this.initData();
  // },
  activated() {
    console.log('zzz',this.activityInfo);
    this.initData();
  },
  methods: {
    initData() {
      this.batchApplyInfo.applyType = 2;
      this.batchApplyInfo.tempBarcode = '';
      this.importType === 3 ? this.activityFileList = [] : this.productFileList = []
    },
    handleChange(file) {
      if (file.status === 'ready') {
        console.log(file)
        this.importType === 3 ? this.activityFileList = [file] : this.productFileList = [file]
      }
    },
    handleRemove() {
      this.importType === 3 ? this.activityFileList = [] : this.productFileList = []
    },
    //获取供货对象的函数
    getSuppluInfo(target) {
      let supplyInfo = this.$refs['supplyTypeInfo'].getAllSupplyInfo();
      if(supplyInfo.isCopySaleArea ==1){
        supplyInfo.busAreaId="";
        supplyInfo.controlUserTypes = "";
        supplyInfo.controlGroupId=""
        supplyInfo.controlGroupName=""
        supplyInfo.isCopyBusArea=""
        supplyInfo.isCopyControlUser=""
        supplyInfo.isCopyControlRoster = ""
        supplyInfo.controlRosterType=""
        // supplyInfo.customerGroupId="";
      }
      if (supplyInfo.isCopySaleArea ==2) {
        supplyInfo.busAreaId="";
        supplyInfo.controlUserTypes = "";
        supplyInfo.controlGroupId=""
        supplyInfo.controlGroupName=""
        supplyInfo.isCopyBusArea=""
        supplyInfo.isCopyControlUser=""
        supplyInfo.isCopyControlRoster = ""
        supplyInfo.controlRosterType=""
         
      }
      if (supplyInfo.isCopyBusArea === 3) {
        // supplyInfo.customerGroupId="";
      
      }
   
      target.baseId = this.frameReportId;
      target.isCopySaleArea = supplyInfo.isCopySaleArea;
      Object.keys(supplyInfo).forEach((key) => {
        target[key] = supplyInfo[key];
      });
      
    },
    //动销
    importProductReport() {
      this.btnLoadingPageTwo = true
      const productReportData = {};
      productReportData.dataRange = this.productDataRange;
      this.getSuppluInfo(productReportData)
      dynamicProducts(productReportData).then(res => {
       if(res.code==1000){
       
         this.$emit("select",7)
        this.$message.success("提取成功")
       }else{
        this.$message.error(res.msg)
       }
      }).finally(_ => {
        this.btnLoadingPageTwo = false
      })
    },
    importActivity(params) {
      batchImportActivities(params).then(res => {
        // this.$emit("select",7)
        if(res.code==1000&&!res.data.failNum){
          this.$message.success(`导入成功${res.data.successNum}条`)
          this.$emit("select",7)
        }else if(res.code==1000&&res.data.failNum){
          this.$confirm(`成功${res.data.successNum}条，失败${res.data.failNum}条<a style="color: blue"  href="${res.data.failUrl}">下载<a/>错误文件`,{dangerouslyUseHTMLString: true})
          .then(_ => {
            done();
          })
          .catch(_ => {});
        }else if(res.code!=1000){
          this.$message.error(res.msg)
        }
      }).finally(e => {
        this.btnLoadingPageThree = false
      })
    },
    importProductId(params) {
      // this.btnLoadingPageTwo = true
      productsCheck(params).then(res=>{
       
      }).finally(res=>{
        this.btnLoading = false
      })
      batchImportProducts(params).then(res => {
        this.btnLoading = false
        if(res.code==1000&&!res.data.groupBuyingBatchCreatesResult.failureNum){
          this.$message.success(`导入成功${res.data.groupBuyingBatchCreatesResult.successNum}条`)
          this.$emit("select",7)
        }else if(res.code==1000&&res.data.groupBuyingBatchCreatesResult.failureNum){
          this.$confirm(`成功${res.data.groupBuyingBatchCreatesResult.successNum}条，失败${res.data.groupBuyingBatchCreatesResult.failureNum}条<a style="color: blue"  href="${res.data.groupBuyingBatchCreatesResult.failureExcelFileDownloadUrl}" download="${res.data.groupBuyingBatchCreatesResult.errorFileName}">下载<a/>错误文件`,{dangerouslyUseHTMLString: true})
          .then(_ => {
            done();
          })
          .catch(_ => {});
        }else if(res.code!=1000){
          this.$message.error(res.msg)
        }
        }).catch(_ => {
          this.btnLoading = false
        }).finally(e => {
          this.btnLoading = false
        })   
    },
    // 上传文件处理逻辑
    uploadFile(file,importType) {
      const fileFormData = {};
      this.getSuppluInfo(fileFormData)
      if(importType === 3) {
        fileFormData.importType = this.importFormat
        let params = new FormData()
        Object.keys(fileFormData).forEach(item=>{
          params.append(item,fileFormData[item])
        })
        params.append('file',file)
        this.importActivity(params)
        return
      }
      let params = new FormData()
      Object.keys(fileFormData).forEach(item=>{
        params.append(item,fileFormData[item])
      })
      params.append('file',file)
      this.importProductId(params)
      // this.checkRepeat(fileFormData);
    },
    checkRepeat(params) {
      checkImportGroupProduct(params).then((res) => {
        if (res.code === 1000) {
          this.btnLoading = false;
          const { existExcelFileDownloadUrl } = res.data.groupBuyingBatchCreatesResult || {};
          if (existExcelFileDownloadUrl) {
            const h = this.$createElement;
            const baseUrl = process.env.VUE_APP_BASE_API;
            this.$confirm('提示', {
              title: '提示',
              message: h('div', [
                h(
                  'span',
                  null,
                  '以下导入商品在未结束的拼团活动中，存在'
                ),
                h(
                  'span',
                  { style: 'color: red;' },
                  '业务商圈、供货对象、拼团价格、起拼数量一致的拼团活动，上架后将会降低APP商品搜索顺序'
                ),
                h(
                  'span',
                  null,
                  '，请确认是否继续上架？'
                ),
                h('a', {
                  style: 'color: #4183d5;',
                  attrs: {
                    href: existExcelFileDownloadUrl,
                    download: '下载重复性文件',
                  }
                }, '下载重复性文件'),
              ]),
              confirmButtonText: '确定',
              cancelButtonText: '取消'
            })
              .then(() => {
                this.confirmSubmit(params);
              })
              .catch(() => {

              })
          } else {
            this.confirmSubmit(params);
          }
        } else {
          this.btnLoading = false;
          this.$message({
            showClose: true,
            message: res.msg,
            type: 'error',
            duration: 2000
          })
        }
      }).catch(() => {
        this.btnLoading = false;
      })
    },
    // 商品导入成功与否的信息
    confirmSubmit(params) {
      const loading = this.$loading({
        lock: true,
        text: 'Loading',
        spinner: 'el-icon-loading',
        background: 'rgba(255,255,255, 0.8)'
      })
      uploadGoodsCustomer(params)
        .then((res) => {
          loading.close()
          if (res.code === 1000) {
            const excelRowNumLimitFailureMsg =
              ((res.data || {}).groupBuyingBatchCreatesResult || {})
                .excelRowNumLimitFailureMsg || ''
            if (excelRowNumLimitFailureMsg) {
              this.$message.error(excelRowNumLimitFailureMsg)
            } else {
              let con = ''
              if (
                res.data.groupBuyingBatchCreatesResult &&
                res.data.groupBuyingBatchCreatesResult.failureNum > 0
              ) {
                con = `<p>${res.data.groupBuyingBatchCreatesResult.successNum}个商品发布成功，${res.data.groupBuyingBatchCreatesResult.failureNum}个商品导入失败，原因见错误文件<br><a style="color: #ff0021" href="${res.data.groupBuyingBatchCreatesResult.failureExcelFileDownloadUrl}" download="批量导入拼团活动">批量导入拼团活动</a></p>`
              } else {
                con = `<p>${res.data.groupBuyingBatchCreatesResult.successNum}个商品发布成功，${res.data.groupBuyingBatchCreatesResult.failureNum}个商品导入失败</p>`
              }
              this.groupBuyIngApplyImportProductVos = res.data
                .groupBuyingBatchCreatesResult
                ? res.data.groupBuyingBatchCreatesResult
                    .groupBuyIngApplyImportProductVos
                : []
              this.$confirm(con, '提示', {
                confirmButtonText: '确定',
                dangerouslyUseHTMLString: true,
                cancelButtonText: '取消'
              })
                .then(() => {
                  this.handleRemove()
                  const path = this.$route.fullPath;
                  window.closeTab(path);
                  window.closeTab('/groupActivityTheme');
                  this.$router.push({
                    path: '/collageActivity',
                    query: { refresh: true }
                  })
                })
                .catch((err) => {
                  this.handleRemove()
                })
            }
          } else {
            this.handleRemove()
            this.$message({
              showClose: true,
              message: res.msg,
              type: 'error',
              duration: 2000
            })
          }
        })
        .catch((error) => {
          loading.close();
          this.$message({
            message: '请求失败',
            type: 'error',
          });
        })
        .finally(() => {
          this.importLoading = false;
          this.btnLoading = false;
          if (this.uploadRef) {
            this.$refs[this.uploadRef].clearFiles();
          }
        });
    },
    beforeImportData(uploadInfo) {
      const fileName = uploadInfo.name;
      const fileType = fileName.substring(fileName.lastIndexOf('.') + 1);
      if (fileType !== 'xlsx' && fileType !== 'xls') {
        this.$message.warning('选择的文件类型不对');
        return false;
      }
      return true;
    },
    download(from) {
      let url = '';
      if(this.importType === 3){
        if(!this.importFormat){
          this.$message.warning('请选择导入类型')
          return
        }
        url = `${process.env.VUE_APP_BASE_API}/groupbuying/auto/report/download/template?type=${this.importFormat}`
      }
      else if (from === 1) {
        url = `${process.env.VUE_APP_BASE_API}/report/groupbuying/apply/canReportSku?frameReportId=${this.frameReportId}`
      } else {
        url = `${process.env.VUE_APP_BASE_API}/report/groupbuying/apply/downloadTemplateForImport`
      }
      window.open(url);
    },
    getFileList(importType) {
      let fileList = this.importType === 3 ? this.activityFileList : this.productFileList
      if (fileList.length > 0) {
        importType === 3 ? this.btnLoadingPageThree = true : this.btnLoading = true
        fileList.forEach((obj) => {
          this.uploadFile(obj.raw,importType);
        });
      } else {
        this.$message.warning('请选择上传文件!');
      }
    },
    determineDialog() {
      if(this.importType === 2) {
        if(!this.productDataRange) {
          this.dialogVisible = true
          this.btnLoadingPageTwo = false
          return false
        }
        this.importProductReport()
        return
      }
      if([3,4].includes(this.importType)) {
        this.getFileList(this.importType)
      }
    },
    //单个商品提报
    // createApply() {
    //   if (this.batchApplyInfo.tempBarcode === '') {
    //     this.$message.error('请填写普通商品编码');
    //     return;
    //   }
    //   getSkuByBarcode({
    //     baseId: this.frameReportId,
    //     barcode: this.batchApplyInfo.tempBarcode,
    //   }).then((res) => {
    //     if (res.code === 1000) {
    //       window.sessionStorage.removeItem("singleCreateInfo") // 商业反馈重新选择新的商品创建拼团活动，进入编辑页面还是上一次所选的商品信息。先删再存看看
    //       sessionStorage.setItem('singleCreateInfo', JSON.stringify({
    //         ...res.data.csu,
    //         baseId: this.frameReportId,
    //         auditTime: [this.activityInfo.auditStime, this.activityInfo.auditEtime],
    //         activityTime: [this.activityInfo.validStime, this.activityInfo.validEtime],
    //         preheatTime: this.activityInfo.preheatStime,
    //         topicTitle: this.activityInfo.topicTitle,
    //         registrationTime: [this.activityInfo.registrationStime, this.activityInfo.registrationEtime],
    //         customerGroupVO: this.customerGroupVO,
    //         baseCustomerGroupId: this.activityInfo.limitCustomerGroupId,
    //       }));
    //       const path = '/editCollageActivity';
    //       const obj = {
    //         fromType: 'singleCreate'
    //       }
    //       sessionStorage.setItem('collageActType', 'singleCreate');
    //       sessionStorage.removeItem('createCollageActivity')
    //       sessionStorage.setItem('createCollageActivity', 'true')
    //       window.openTab(path, obj);
    //       // this.$router.push('/editCollageActivity?fromType=singleCreate');
    //     } else {
    //       this.btnLoading = false;
    //       this.$message.error(res.msg);
    //     }
    //   }).catch((err) => {
    //     this.btnLoading = false;
    //     this.$message.error(err.msg || '系统错误');
    //   });
    //   // this.$router.push({ path: '/editCollageActivity', query: { frameReportId: '' } });
    // },
    // cancelPage() {
    //   const path = this.$route.fullPath;
    //   window.closeTab(path);
    //   window.closeTab('/groupActivityTheme');
    // },
    // timeRangeFormat(start, end, type) {
    //   start = this.formatDate(start, type)
    //   end = this.formatDate(end, type)
    //   return start && end ? `${start} 至 ${end}` : ''
    // },
    // cycleTimeFormat(obj) {
    //   if (obj) {
    //     return `${this.weekObj[obj.stime.cycleNum]} ${
    //       Number(obj.stime.hour) < 10 ? '0' + obj.stime.hour : obj.stime.hour
    //     }:${
    //       Number(obj.stime.minute) < 10
    //         ? '0' + obj.stime.minute
    //         : obj.stime.minute
    //     } 至 ${this.weekObj[obj.etime.cycleNum]} ${
    //       Number(obj.etime.hour) < 10 ? '0' + obj.etime.hour : obj.etime.hour
    //     }:${
    //       Number(obj.etime.minute) < 10
    //         ? '0' + obj.etime.minute
    //         : obj.etime.minute
    //     }`
    //   }
    // }
  }
}
</script>

<style lang="scss" scoped>
.contentWrapper {
  // padding: 16px;
  background: #fff;
  margin-bottom: 20px;
  .titleBox {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  .searchMsg {
    font-weight: 700;
    width: 100%;
    font-size: 16px;
  }
  .sign {
    display: inline-table;
    width: 3px;
    height: 13px;
    background: linear-gradient(360deg, #1d69c4 1%, #8bbdfc 99%);
    border-radius: 2px;
    margin-right: 8px;
  }
  .el-form {
    &.activity-info {
      margin-top: 20px;
      .el-form-item {
        margin-bottom: 10px;
        ::v-deep  .el-form-item__label {
          line-height: 30px;
        }
        ::v-deep  .el-form-item__content {
          line-height: 30px;
          color: #606266;
          a {
            color: #409eff;
            cursor: pointer;
          }
        }
      }
      .time-info {
        font-size: 14px;
        font-family: PingFangSC, PingFangSC-Regular;
        font-weight: 400;
        text-align: left;
        color: #ff3024;
      }
      // .redText {
      //   color: #ff3024;
      // }
    }
  }
.tipBox {
   color: #909399;
  font-size: 12px;
  p {
    line-height: 18px;
  }
}
  // .customerType {
  //   border: 1px solid #eeeeee;
  //   border-radius: 4px;
  //   max-height: 260px;
  //   overflow-y: auto;
  //   ::v-deep  .el-checkbox {
  //     width: 14%;
  //     margin-left: 10px;
  //   }
  //   ::v-deep  .checkedall {
  //     width: 100%;
  //     padding: 10px;
  //     margin-left: 0;
  //     margin-bottom: 10px;
  //   }
  //   ::v-deep  .el-checkbox__input.is-checked + .el-checkbox__label {
  //     color: #333333;
  //   }
  // }
}
.importBottom {
  // 只给盒子的上边设置阴影
  box-shadow: 0px -2px 2px #e6e6e6;
  width: 100%;
  height: 63px;
  display: flex;
  justify-content: center;
  align-items: center;
  // 实现居中
  .importBtn {
    width: 120px;
    height: 32px;
    background: #4184D5;
    border-radius: 4px;
  }
}
</style>
