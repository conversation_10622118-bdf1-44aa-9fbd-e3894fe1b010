<template>
  <div class="settlement-box">
    <el-row :gutter="20" class="price-box">
      <el-col :span="4">
        待商业付款
        <span>
          {{
          info.waitCommercialPaymentNum ? info.waitCommercialPaymentNum : 0
          }}条
        </span>
      </el-col>
      <el-col :span="6">
        审核未通过，待商业处理
        <span>{{ info.failedAuditNum ? info.failedAuditNum : 0 }}条</span>
      </el-col>
      <el-col :span="6">
        待商业上传发票
        <span>{{ info.commercialUploadPendingNum ? info.commercialUploadPendingNum : 0 }}条</span>
      </el-col>
    </el-row>
    <p>
      佣金缴纳规则：
      <br />1、当企业信息中的佣金结算方式为
      <b>“月结”</b>时，系统
      <b>每月1号凌晨</b>生成上个月的佣金缴纳记录
      <br />2、针对
      <b>“待商业付款”</b>的记录需要您在约定的
      <b>最后付款时间前</b>足额缴纳，并
      <b>上传付款凭证</b>
      <br />3、针对
      <b>“审核未通过”</b>的记录需要您在约定的
      <b>最后付款时间前</b>处理完毕
      <br />4、针对
      <b>“待商业上传发票”</b>的记录需要您按照实际需缴纳佣金金额上传发票，平台财务审核通过后系统会自动生成调账单，调账单入账后金额会进入您的可提现账户，您发起提现后财务将安排为您打款
      <br />5、佣金缴纳记录在约定时间内未处理完成，记录将标记为
      <b>“已逾期”</b>。若您存在已逾期的佣金缴纳记录，系统将
      <b>限制您的申请提现功能</b>。当佣金缴纳记录处理完成后，系统将自动恢复您的申请提现功能。为了保障您的使用体验，请按时足额缴纳佣金，并及时处理缴纳记录
      <br />6、请将“实际需缴纳佣金金额”转账至下面的平台的对公账户，平台财务将在
      <b>1个工作日内确认完成</b>
      <br />
      <b style="color:#ff2121;">公司名称： {{ bankAccountInfo.companyName }}</b>
      <br />
      <b style="color:#ff2121;">开户行：{{ bankAccountInfo.bankName }}</b>
      <br />
      <b style="color:#ff2121;">银行账户：{{ bankAccountInfo.account }}</b>
    </p>
    <div class="con-title">
      <span class="line" />
      <span>应缴纳佣金记录</span>
    </div>
    <div class="searchMy">
      <el-form ref="listQuery" :model="listQuery" :inline="true" size="small">
        <el-form-item prop="createdTime">
          <span class="search-title">生成时间</span>
          <div style="display: table-cell; line-height: 24px">
            <el-date-picker
              v-model="listQuery.createdTime"
              type="datetimerange"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              :default-time="['00:00:00', '23:59:59']"
            />
          </div>
        </el-form-item>
        <el-form-item prop="paymentTime">
          <span class="search-title">确认收款时间</span>
          <div style="display: table-cell; line-height: 24px">
            <el-date-picker
              v-model="listQuery.paymentTime"
              type="datetimerange"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              :default-time="['00:00:00', '23:59:59']"
            />
          </div>
        </el-form-item>
        <el-form-item prop="state">
          <span class="search-title">状态</span>
          <el-select v-model="listQuery.state">
            <el-option label="全部" :value="0" />
            <el-option label="待商业付款" :value="1" />
            <el-option label="待平台审核" :value="2" />
            <!-- <el-option label="审核通过" :value="3"></el-option> -->
            <el-option label="审核未通过" :value="4" />
            <el-option label="已完成" :value="3" />
            <el-option label="待结转" :value="5" />
            <el-option label="待商业上传发票" :value="7" />
            <el-option label="待平台打款" :value="8" />
            <el-option label="平台已打款" :value="9" />
          </el-select>
        </el-form-item>
        <el-form-item prop="overdue">
          <span class="search-title">是否逾期</span>
          <el-select v-model="listQuery.overdue">
            <el-option label="全部" :value="0" />
            <el-option label="已逾期" :value="1" />
            <el-option label="未逾期" :value="2" />
          </el-select>
        </el-form-item>
        <el-form-item></el-form-item>
        <el-form-item class="btn-item" style="text-align: right;padding-right: 20px">
          <el-button type="primary" @click="getList(listQuery, true)">查询</el-button>
          <el-button @click="reset">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    <el-row :gutter="20" class="price-box mb15">
      <el-col :span="12">
        <div class="btn-item">
          <el-button v-permission="['settle_commission_exportList']" plain @click="exportList">导出应缴纳佣金记录</el-button>
          <el-button v-permission="['settle_commission_exportDetail']" plain @click="exportDetail">导出应缴纳佣金明细</el-button>
        </div>
      </el-col>
    </el-row>
    <xyy-table
      :data="list"
      :list-query="listQuery"
      :col="col"
      :headerCellStyle="headerCellStyle"
      @get-data="getList"
      @handlePicture-click="handlePictureCardPreview"
    >
      <template slot="paymentCertificate" slot-scope="{col}">
        <el-table-column
          :label="col.name"
          :width="col.width"
        >
          <template slot-scope="scope">
            <!-- 测试图片路径: 'https://fuss10.elemecdn.com/e/5d/4a731a90594a4af544c0c25941171jpeg.jpeg' -->
            <span v-if="['ofd', 'pdf'].includes(checkFileExtension(scope.row.paymentCertificate))" class="pdfTextSty" @click="viewAnnexUrl(scope.row)">{{ scope.row.invoiceFilename }}</span>
            <div v-else style="height: 100%; display: flex; align-items: center; justify-content: center;">
              <el-image 
                v-if="scope.row.paymentCertificate"
                style="width: 30px; height: 30px"
                :src="scope.row.paymentCertificate"
                :preview-src-list="[scope.row.paymentCertificate]"
              />
            </div>
          </template>
        </el-table-column>
      </template>
      <template slot="remarks" slot-scope="{col}">
        
        <el-table-column
          :label="col.name"
          :width="col.width"
        >
          <template slot-scope="scope">
            <el-tooltip class="item" effect="dark" :content="scope.row.remarks" placement="top-start">
              <span>{{ scope.row.remarks }}</span>
            </el-tooltip>
          </template>
        </el-table-column>
      </template>
      <template slot="operation" slot-scope="{col}">
        <el-table-column
          :key="col.index"
          :prop="col.index"
          :label="col.name"
          :width="col.width"
          fixed="right"
        >
          <template slot-scope="scope">
            <el-button size="small" type="text" @click="operationClick(1,scope.row)">查看详情</el-button>
            <!-- <el-button  size="small" type="text" @click="operationClick(0,scope.row)">处理</el-button> -->
            <el-button v-permission="['settle_commission_payment']" v-if="scope.row.state == 1 || scope.row.state == 4 || scope.row.state == 7" size="small" type="text" @click="operationClick(0,scope.row)">处理</el-button>
          </template>
        </el-table-column>
      </template>
    </xyy-table>
    <el-dialog :visible.sync="withdrawStatus" custom-class="withdraw-dialog" title="佣金缴纳处理">
      <el-form :model="withdraw" ref="withdraw" :rules="rules" label-width="160px">
        <el-form-item label="佣金收取月份：" prop="hireMonths">
          {{
          withdraw.hireMonths
          }}
        </el-form-item>
        <el-form-item label="实际需缴纳佣金金额：" prop="actualHireMoney">{{ withdraw.actualHireMoney }}</el-form-item>
        
        <el-form-item v-if="withdraw.state == 4 || withdraw.state == 7" label="发票文件：" prop="paymentCertificate" class="with-btn">
          <el-upload
            ref="uploadRef"
            action="xxx"
            :before-upload="beforeUpload"
            :before-remove="beforeRemove"
            :on-change="handleChange"
            :auto-upload="false"
            :on-remove="handleRemove"
            :file-list="fileList"
            accept=".pdf,.ofd"
          >
            <el-button
              size="mini"
              type="primary"
              :loading="uploadLoading"
            >
              上传文件
            </el-button>
          </el-upload>
          <!-- <p v-show="invoiceList.length == 0">只允许上传pdf、ofd格式的文件, 单个文件大小限制10M</p> -->
          <p v-show="invoiceList.length == 0">只允许上传pdf、ofd格式的增值税专用发票文件, 单个文件大小限制10M</p>
        </el-form-item>
        <el-form-item v-else label="付款凭证：" prop="paymentCertificate" class="with-btn">
          <el-upload
            class="avatar-uploader"
            action
            :http-request="uploadImg"
            :before-upload="beforeAvatarUpload"
            :show-file-list="false"
          >
            <img
              v-if="withdraw.paymentCertificate"
              :src="withdraw.paymentCertificate"
              class="avatar"
            />
            <i class="el-icon-plus avatar-uploader-icon">上传图片</i>
            <div slot="tip" class="el-upload__tip">请上传jpg、png、bmp格式的图片，大小不超过5M</div>
          </el-upload>
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button @click="withdrawStatus = false">取消</el-button>
        <el-button type="primary" @click="applyWithdraw" :disabled="withdrawDisabled">确定</el-button>
      </div>
    </el-dialog>
    <el-dialog :visible.sync="dialogVisible" custom-class="preview-dialog">
      <img width="100%" :src="dialogImageUrl" alt />
    </el-dialog>
    <export-tip
      :change-export="changeExport"
      @handleExoprClose="handleExoprClose"
      @handleChangeExport="handleChangeExport"
    />
    <CollectionAccountTip
      v-if="collectionAccountTipVis"
      :collection-account-tip-vis.sync="collectionAccountTipVis"
      :info="bankAccountInfo"
    />
  </div>
</template>

<script>
import {
  getCommissionRecordList,
  getStatistic,
  exportData,
  exportDataDetail,
  sendWithdraw,
  getBankAccountInfo,
  exportCommission,
  exportCommissionDetail
} from '../../api/settlement/commission'
import exportTip from '@/views/other/components/exportTip';
import { getHostName,uploadFile } from '../../api/qual/index';
import { uploadFDFS } from '@/api/order/index';
import { showPdf } from '@/api/settlement/marketingServiceQuota/index'
import CollectionAccountTip from './components/collectionAccountTip.vue';

export default {
  components: { exportTip, CollectionAccountTip },
  data() {
    return {
      uploadLoading: false, // 上传文件loading
      collectionAccountTipVis: false,
      hostName: '',
      dialogImageUrl: '',
      dialogVisible: false,
      listQuery: {
        pageSize: 10,
        page: 1,
        total: 0,
        createdTime: [],
        paymentTime: [],
        state: 0, // 0：全部 1：待商业付款 2-待平台审核 3：已完成 4：审核未通过 5：待结转 6:已结转
        overdue: 0 // 0：全部 1：逾期  2：不逾期
      },
      info: {
        waitCommercialPaymentNum: '', // 待商业付款总条数
        failedAuditNum: '', // 审核未通过总条数
        commercialUploadPendingNum: '' //待商业上传发票数
      },
      withdraw: {
        hireMonths: '',
        actualHireMoney: '',
        paymentCertificate: '',
        state: null,
      },
      rules: {
        applyAmount: [
          {
            required: true,
            message: '请上传付款凭证',
            trigger: ['blur', 'change']
          }
        ]
      },
      withdrawStatus: false, // 处理佣金
      withdrawDisabled: false, // 是否可提现
      list: [],
      col: [
        {
          index: 'hireMonths',
          name: '佣金收取月份',
          width: 200,
          ellipsis: true
        },
        {
          index: 'deductedCommission',
          name: '应缴纳佣金金额',
          width: 150,
          formatter: (row, col, cell) =>
            (cell || cell === 0 ? cell.toFixed(2) : '')
        },
        // {
        //   index: 'commissionDiscount',
        //   name: '佣金优惠折扣',
        //   width: 150,
        //   formatter: (row, col, cell) => (cell || cell === 0 ? cell.toFixed(2) : ''),
        // },
        {
          index: 'discountHireMoney',
          name: '佣金优惠金额',
          width: 150,
          formatter: (row, col, cell) => (cell || cell === 0 ? cell.toFixed(2) : ''),
        },
        {
          index: 'actualHireMoney',
          name: '实际需缴纳佣金金额',
          width: 150,
          formatter: (row, col, cell) => (cell || cell === 0 ? cell.toFixed(2) : ''),
        },
        {
          index: 'createTime',
          name: '生成时间',
          width: 200,
          formatter: (row, col, cell) =>
            cell
              ? new Date(cell + 8 * 3600 * 1000)
                  .toJSON()
                  .substr(0, 19)
                  .replace('T', ' ')
              : ''
        },
        {
          index: 'paymentTerm',
          name: '最后付款时间',
          width: 200,
          formatter: (row, col, cell) =>
            cell
              ? new Date(cell + 8 * 3600 * 1000)
                  .toJSON()
                  .substr(0, 19)
                  .replace('T', ' ')
              : ''
        },
        {
          index: 'overdue',
          name: '是否逾期',
          width: 150,
          formatter: (row, col, cell) => {
            if (cell === 2) {
              return '未逾期'
            }
            if (cell === 1) {
              return '已逾期'
            }
            return ''
          }
        },
        {
          index: 'paymentCertificate',
          name: '付款凭证/发票',
          width: 150,
          height: 100,
          slot: true,
        },
        {
          index: 'paymentTime',
          name: '确认收款时间',
          width: 200,
          formatter: (row, col, cell) =>
            cell
              ? new Date(cell + 8 * 3600 * 1000)
                  .toJSON()
                  .substr(0, 19)
                  .replace('T', ' ')
              : ''
        },
        {
          index: 'state',
          name: '状态',
          width: 150,
          formatter: (row, col, cell) => {
            if (cell === 1) {
              return '待商业付款'
            }
            if (cell === 2) {
              return '待平台审核'
            }
            if (cell === 3) {
              return '已完成'
            }
            if (cell === 4) {
              return '审核未通过'
            }
            if (cell === 5) {
              return '待结转'
            }
            if (cell === 6) {
              return '已结转'
            }
            if (cell === 7) {
              return '待商业上传发票'
            }
            if (cell === 8) {
              return '待平台打款'
            }
            if (cell === 9) {
              return '平台已打款'
            }
            return ''
          }
        },
        {
          index: 'remarks',
          name: '备注/原因',
          width: 260,
          slot: true,
        },
        {
          index: 'operation',
          name: '操作',
          width: 160,
          slot: true,
        },
      ],
      // operation: [
      //   {
      //     name: '查看详情',
      //     type: 1
      //   },
      //   {
      //     name: '处理',
      //     type: 0,
      //     condition: true,
      //     conditions: (row) => {
      //       if (row.state === 1 || row.state === 4) {
      //         return true
      //       }
      //       return false
      //     }
      //   },
      // ],
      id: '',
      bankAccountInfo: {
        companyName: '',
        bankName: '',
        account: ''
      },
      changeExport: false,
      fileName: '',
      fileList: [],
      invoiceList: [],
    }
  },
  methods: {
    checkFileExtension(val = ''){
      let fileType = val?.substring(val?.lastIndexOf('.') + 1);
      fileType = fileType?.toLocaleLowerCase();
      return fileType
    }, // 判断文件类型
    viewAnnexUrl(val) {
      var val = {url: val.paymentCertificate, name: val.invoiceFilename};
      let {url, name} = val
      let temp = this.checkFileExtension(url)
      if(temp == 'pdf'){
        window.open(url, '_blank')
      }else if (temp == 'ofd') {
        showPdf({ pdfUrl: url }).then(res=>{
          let pdfContent = res;
          const blob = new Blob([pdfContent], { type: "application/pdf"});
          const url = window.URL.createObjectURL(blob);
          const a = document.createElement("a");
          a.style.display = "none";
          a.href = url;
          a.download = name;
          document.body.appendChild(a);
          a.click();
          window.URL.revokeObjectURL(url);
        })
      }
    }, // 点击预览pdf&下载ofd
    handleUploadFile() {
      const subData = {}
      subData.file = this.fileList[0].raw
      const isPass = this.beforeUpload(subData.file)
      if (isPass) {
        this.uploadLoading = true
      uploadFDFS(subData).then((res) => {
        if (res.code === '200') {
          // this.invoiceList.push(`${this.hostName}${res.data}`);
          this.invoiceList = [`${this.hostName}${res.data}`];
          this.fileName = subData.file.name;
        } else {
          this.$message.error({ message: '上传失败', offset: this.tipHeight });
          this.fileList = []
          this.fileName = ''
        }
      }).finally(()=>this.uploadLoading = false)
      }else {
        this.fileList = []
        this.fileName = ''
      }
    }, // 上传
    beforeUpload(uploadInfo) {
      const fileName = uploadInfo.name;
      let fileType = fileName.substring(fileName.lastIndexOf('.') + 1);
      fileType = fileType.toLocaleLowerCase();
      if (!['pdf','ofd'].includes(fileType)) {
        this.$message.warning({ message: '请上传PDF格式文件', offset: this.tipHeight });
        return false;
      }
      const fileSize = uploadInfo.size / 1024 / 1024;
      if (fileSize > 10) {
        this.$message.warning({ message: '文件格式不正确或超过10M，请重新选择上传', offset: this.tipHeight });
        return false;
      }
      return true;
    },
    handleChange(file, fileList) {
      if (fileList.length > 0) {
        this.fileList = [fileList[fileList.length - 1]]
      }
      this.handleUploadFile()
    },
    handleRemove(file, fileList) {
      // const index = this.fileList.findIndex(i => i.name === file.name);
      // this.invoiceList.splice(index, 1);
      this.invoiceList = [];
      this.fileName = '';
      this.fileList = fileList;
    },
    beforeRemove(file, fileList) {
      // this.fileList = fileList;
      // beforeImportData方法返回false时会自动触发beforeRemove方法，因此需要再次判断满足以下条件才可执行
      let fileType = file.name.substring(file.name.lastIndexOf('.') + 1);
      fileType = fileType.toLocaleLowerCase();
      const fileSize = file.size / 1024 / 1024;
      if (((fileType === 'pdf'  || fileType === 'ofd')  && (fileSize < 1 || fileSize === 1))) {
        return this.$confirm(`确定删除 ${file.name}？`);
      }
    },
    handleExceed() {
      this.$message.warning({ message: '最多可上传1个文件', offset: this.tipHeight });
    },
    headerCellStyle({ columnIndex }) {
      return columnIndex === 3 ? { color: '#f00' } : {};
    },
    // 银行账户信息
    getBankAccountInfo() {
      getBankAccountInfo()
        .then((res) => {
          if (res.code === '200') {
            if (res.data) {
              this.bankAccountInfo.companyName = res.data.companyName
              this.bankAccountInfo.bankName = res.data.bankName
              this.bankAccountInfo.account = res.data.account
            }
          } else {
            this.$message.error({
              message: res.errorMsg || res.msg,
              customClass: 'center-msg'
            })
          }
        })
        .catch(() => {})
    },
    handlePictureCardPreview(row) {
      this.dialogImageUrl = row.paymentCertificate
      this.dialogVisible = true
    },
    uploadImg(file) {
      uploadFile(file).then((res) => {
        if (res.code === '200') {
          this.withdraw.paymentCertificate = `${this.hostName}/${res.data}`
          console.log(this.withdraw, 'pppp')
        } else {
          this.withdraw.paymentCertificate = ''
        }
      })
    },
    beforeAvatarUpload(file) {
      if (!file) {
        this.$message.error('请上传付款凭证')
        return false
      }
      const isJPG =
        file.type === 'image/jpeg' ||
        file.type === 'image/png' ||
        file.type === 'image/bmp'
      const isLt5M = file.size / 1024 / 1024 < 5

      if (!isJPG || !isLt5M) {
        this.$message.error('付款凭证不满足上传要求，请重新上传')
        return false
      }
      return isJPG && isLt5M
    },
    // 获取佣金记录列表
    getList(listQuery, reset) {
      const loading = this.$loading({
        lock: true,
        text: 'Loading',
        spinner: 'el-icon-loading',
        background: 'rgba(255,255,255, 0.8)'
      })
      const { page, pageSize } = listQuery
      const params = this.getCommonParams()
      getCommissionRecordList({
        pageNum: reset ? 1 : page,
        pageSize,
        ...params
      })
        .then((res) => {
          loading.close()
          if (res.code === '200') {
            const { list, total, pageNum } = res.data
            this.list = list || []
            this.listQuery = {
              ...this.listQuery,
              total,
              page: pageNum
            }
          } else {
            this.$message.error({
              message: res.errorMsg || res.msg,
              customClass: 'center-msg'
            })
          }
        })
        .catch(() => {
          loading.close()
        })
    },
    // 获取待商业付款条数
    getStatistic() {
      const params = this.getCommonParams()
      getStatistic({ ...params })
        .then((res) => {
          if (res.code === '200') {
            const { waitCommercialPaymentNum, failedAuditNum, commercialUploadPendingNum } = res.data
            this.info.waitCommercialPaymentNum = waitCommercialPaymentNum
            this.info.failedAuditNum = failedAuditNum
            this.info.commercialUploadPendingNum = commercialUploadPendingNum
          } else {
            this.$message.error({
              message: res.errorMsg || res.msg,
              customClass: 'center-msg'
            })
          }
        })
        .catch(() => {})
    },
    /**
     * 导出应缴纳佣金记录
     */
    exportList() {
      const params = this.getCommonParams()
      exportData(params)
        .then((res) => {
          if (res.code === '200') {
            if (res.data === 0) {
              this.$message.error({
                message: '暂无应缴纳佣金记录',
                customClass: 'center-msg'
              })
            } else if (res.data > 5000) {
              this.$message.error({
                message: `导出上限为5000条，当前搜索结果导出数据为${res.data}条，超出导出上限`,
                customClass: 'center-msg'
              })
            } else {
              this.exportData()
            }
          } else {
            this.$message.error({
              message: res.errorMsg || res.msg,
              customClass: 'center-msg'
            })
          }
        })
        .catch(() => {})
    },
    /**
     * 导出应缴纳佣金明细
     */
    exportDetail() {
      const params = this.getCommonParams()
      exportDataDetail(params)
        .then((res) => {
          if (res.code === '200') {
            if (res.data === 0) {
              this.$message.error({
                message: '暂无应缴纳佣金明细数据',
                customClass: 'center-msg'
              })
            } else if (res.data > 5000) {
              this.$message.error({
                message: `导出上限为5000条，当前搜索结果导出数据为${res.data}条，超出导出上限`,
                customClass: 'center-msg'
              })
            } else {
              this.exportData('detail')
            }
          } else {
            this.$message.error({
              message: res.errorMsg || res.msg,
              customClass: 'center-msg'
            })
          }
        })
        .catch(() => {})
    },
    /**
     * 导出数据
     */
    exportData(link) {
      const params = this.getCommonParams()
      // const url = `${process.env.VUE_APP_BASE_API}${link}${this.getParams(
      //   params
      // )}`
      // const a = document.createElement('a')
      // a.href = url
      // a.click()
      if(link == 'detail'){
        exportCommissionDetail(params).then((res) => {
          if (res.code !== 0) {
            this.$message.error(res.message);
            return;
          }
          this.changeExport = true;
        });
      }else{
         exportCommission(params).then((res) => {
          if (res.code !== 0) {
            this.$message.error(res.message);
            return;
          }
          this.changeExport = true;
        });
      }
    },
    handleChangeExport(info) {
      this.changeExport = false;
      if (info === 'go') {
        const path = '/downloadList'
        window.openTab(path)
        //that.$router.push({ path: '/downloadList' });
      }
    },
    handleExoprClose() {
      this.changeExport = false;
    },
    /**
     * 获取get请求参数
     */
    getParams(params) {
      let queryStr = '?'
      Object.keys(params).forEach((key) => {
        queryStr += `${key}=${params[key]}&`
      })
      queryStr = queryStr.substr(0, queryStr.length - 1)
      return queryStr
    },
    /**
     * 重置数据
     */
    reset() {
      this.$refs.listQuery.resetFields()
      this.listQuery.paymentTime = []
      this.listQuery.state = 0
      this.listQuery.overdue = 0
      this.getList(this.listQuery, true)
    },
    /**
     * 操作选项
     */
    operationClick(type, row) {
      if (type === 1) {
        // 查看明细
        this.$router.push(`/commissionDetail?hireNo=${row.hireNo}`)
      }
      // 处理
      if (type === 0) {
        this.withdrawStatus = true
        this.withdraw.hireMonths = row.hireMonths
        this.withdraw.actualHireMoney = row.actualHireMoney
        this.withdraw.paymentCertificate = row.paymentCertificate
        // this.withdraw.state = row.state
        this.withdraw.state = row.state
        this.id = row.id
      }
    },
    /**
     * 获取公共参数
     */
    getCommonParams() {
      const { state, overdue, createdTime, paymentTime } = this.listQuery
      const createStartTime =
        createdTime && createdTime.length
          ? this.formatDate(createdTime[0].getTime())
          : ''
      const createEndTime =
        createdTime && createdTime.length
          ? this.formatDate(createdTime[1].getTime())
          : ''
      const paymentStartTime =
        paymentTime && paymentTime.length
          ? this.formatDate(paymentTime[0].getTime())
          : ''
      const paymentEndTime =
        paymentTime && paymentTime.length
          ? this.formatDate(paymentTime[1].getTime())
          : ''
      const params = {
        state,
        overdue,
        createStartTime,
        createEndTime,
        paymentStartTime,
        paymentEndTime
      }
      return params
    },
    /**
     * 佣金缴纳处理提交
     */
    applyWithdraw() {
      this.$refs.withdraw.validate((valid) => {
        if (valid) {
          let { paymentCertificate } = this.withdraw
          if(this.withdraw.state == 4 || this.withdraw.state == 7) paymentCertificate = this.invoiceList?.[0]
          const params = { paymentCertificate, id: this.id, invoiceFilename: this.fileName }
          sendWithdraw(params)
            .then((res) => {
              if (res.code === '200') {
                // 刷新列表
                this.getList(this.listQuery, true)
                this.getStatistic()
                this.$message.success({
                  message: '佣金缴纳处理成功',
                  customClass: 'center-msg'
                })
                this.withdrawStatus = false
              } else {
                this.$message.error({
                  message: res.errorMsg || res.msg,
                  customClass: 'center-msg'
                })
              }
            })
            .catch(() => {})
        }
      })
    },
    /**
     * 格式化日期
     */
    formatDate(date) {
      return Number(date)
        ? new Date(date + 8 * 3600 * 1000)
            .toJSON()
            .substr(0, 19)
            .replace('T', ' ')
        : ''
    },
    isFirstLogin() {
      if (this.util.getCookie('isLogged')) {
        this.collectionAccountTipVis = false;
      } else {
        this.util.setCookie('isLogged', true);
        this.collectionAccountTipVis = true;
      }
    }
  },
  created() {
    getHostName().then((res) => {
      if (res.hostName) {
        this.hostName = res.hostName;
      }
    });
    this.getStatistic()
    this.getList(this.listQuery, true)
    this.getBankAccountInfo();
    this.isFirstLogin();
  }
}
</script>

<style lang="scss" scoped>
.settlement-box {
  padding: 15px;
  .price-box {
    font-size: 16px;
    font-family: PingFangSC, PingFangSC-Semibold;
    font-weight: 600;
    color: #303133;
    line-height: 40px;
    overflow: hidden;
    &.mb15 {
      margin-bottom: 15px;
    }
    span {
      font-size: 16px;
      color: #ff2121;
    }
    .el-button {
      padding: 0 12px;
      line-height: 30px;
      &.is-plain {
        color: #4183d5;
        border-color: #4183d5;
      }
    }
  }
  > p {
    padding: 8px;
    background: #f9f9f9;
    border-radius: 2px;
    font-size: 14px;
    font-family: PingFangSC, PingFangSC-Regular;
    font-weight: 400;
    color: #666666;
    margin: 10px 0 10px;
    line-height: 24px;
  }
  .con-title {
    padding-left: 0;
    color: #000000;
    font-size: 14px;
    height: 38px;
    line-height: 38px;
  }
  .con-title span {
    display: inline-block;
    vertical-align: middle;
    font-weight: bold;
  }
  .con-title .line {
    width: 3px;
    height: 13px;
    background: -webkit-gradient(
      linear,
      left bottom,
      left top,
      from(#1d69c4),
      to(#8bbdfc)
    );
    background: linear-gradient(360deg, #1d69c4 0%, #8bbdfc 100%);
    border-radius: 2px;
    margin-right: 8px;
  }
  .el-form--inline .el-form-item__content {
    height: 30px;
  }
}
.el-dialog.withdraw-dialog {
  width: 500px;
  top: 30%;
  transform: translateY(-50%);
  .el-dialog__header {
    padding: 0 20px;
    line-height: 50px;
    .el-dialog__headerbtn {
      top: 17px;
    }
  }
  .el-dialog__body {
    box-sizing: border-box;
    padding: 10px 20px;
    > p {
      padding: 8px;
      background: #f9f9f9;
      border-radius: 2px;
      font-size: 14px;
      font-family: PingFangSC, PingFangSC-Regular;
      font-weight: 400;
      color: #666666;
      margin: 10px 0;
    }
  }
  .el-button {
    padding: 0 20px;
    line-height: 30px;
    height: 30px;
  }
}
.el-dialog.preview-dialog {
  width: 500px;
  top: 50%;
  transform: translateY(-50%);
  .el-dialog__header {
    padding: 0 20px;
    line-height: 50px;
    .el-dialog__headerbtn {
      top: 17px;
    }
  }
  .el-dialog__body {
    padding: 10px 20px;
  }
}
.searchMy ::v-deep  .el-form-item__label {
  margin-left: 20px;
  padding: 0;
}
.searchMy ::v-deep  .el-input__inner {
  border-radius: 0 4px 4px 0;
}
.searchMy ::v-deep  .el-date-editor {
  width: 100%;
}
.search-title {
  display: table-cell;
  padding: 0 10px;
  text-align: center;
  border: 1px solid #dcdfe6;
  height: 30px;
  line-height: 30px;
  vertical-align: middle;
  border-right: none;
  border-radius: 4px 0 0 4px;
  color: #333333;
  white-space: nowrap;
}
.searchMy ::v-deep  .el-select {
  display: table-cell;
  width: 100%;
}
.searchMy ::v-deep  .el-form-item__content {
  width: 100%;
}
.searchMy ::v-deep  .el-form-item--small.el-form-item {
  margin-bottom: 10px;
  width: 32%;
}
.searchMy ::v-deep  .el-form-item--small .el-form-item__content {
  line-height: 30px;
  width: 100%;
}
.searchMy ::v-deep  .el-input-group__prepend {
  background: none;
  color: #333333;
  padding: 0 10px;
}
.Fsearch {
  padding: 0px 20px 10px;
  font-weight: 500;
  .searchMsg {
    font-weight: 700;
    width: 200px;
  }
  .sign {
    display: inline-table;
    width: 3px;
    height: 13px;
    background: linear-gradient(360deg, #1d69c4 1%, #8bbdfc 99%);
    border-radius: 2px;
    margin-right: 8px;
  }
}
.avatar {
  width: 200px;
  height: 200px;
}
::v-deep    .el-upload-list__item{
   transition:none !important;
   -webkit-transition:nonne !important;
}
::v-deep     .el-upload-list__item-name{
    transition:none !important;
    -webkit-transition:nonne !important;
}
.pdfTextSty{
  color: #52a7cb;
  cursor: pointer;
}
</style>
