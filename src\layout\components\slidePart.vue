<template>
  <div>
    <el-scrollbar wrap-class="scrollbar-wrapper">

      <div :class="{'commonOperation':true,'menu-top':isCollapse}" v-if="shopConfig.indexShow&&useMenu.length>0 && !menuGray">
        <template v-if="!isCollapse">
          <el-row>
            <el-col :class="'commonTitle'" :span="24">       <i  class="el-icon-s-management" style="margin-right: 12px"></i>  {{ isCollapse ? '' : '常用操作' }}</el-col>
          </el-row>

          <el-row class="operation"
          >
            <el-col v-show="!isCollapse" :span="12" v-for="item in useMenu" :key="item.path"
                    @click.native="operationFn(item.path)"
                     :style="{color: curPath == item.path?'#4184d5':'',backgroundColor: curPath == item.path?'rgb(236, 243, 251)':''}"
                    >
                    <span>{{ item.name }}</span>
            </el-col>
          </el-row>
        </template>
        <template v-else>
          <el-popover
            :class="useMenu"
            placement="right"
            width="200"
            :visible-arrow="false"
            offset="40"
            trigger="hover">
            <el-row slot="reference">
              <el-col :class="'commonTitle'" :span="12"><i class="el-icon-s-management"
                                                           style="margin-right: 12px"></i>{{ isCollapse ? '' : '常用操作' }}
              </el-col>
            </el-row>
            <el-row class="operation">
              <el-col :span="12" v-for="item in useMenu" :key="item.path"
                      @click.native="operationFn(item.path)">
                {{ item.name }}
              </el-col>
            </el-row>
          </el-popover>
        </template>
      </div>
      <el-menu
        :class="commissionRecordBadge?'':'noBadge'"
        :collapse="isCollapse"
        :unique-opened="true"
        :collapse-transition="false"
        :default-active="currentMenu"
        mode="vertical"
        @select="menuSelect"
      >
        <sidebar-item v-for="route in routers"  :key="route.path" :item="route" :base-path="route.path" :count="pendingShipmentReminderQuantity"/>
      </el-menu>
    </el-scrollbar>
    <div :class="isCollapse?'el-icon-s-unfold el-menu--collapse':'el-icon-s-fold'" class="collapse"
         @click="toggleCollapse"></div>
  </div>
</template>

<script>
import {isExternal} from '@/utils/util'
import {mapState, mapMutations} from "vuex";
import SidebarItem from './SidebarItem'
export default {
  name: "slide-part",
  components: {
    SidebarItem
  },
  data() {
    return {
      isCollapse: false,
      selectedMenu: '',
      curPath:'',

    }
  },
  watch: {
    // 监听$route对象的变化
    $route(to, from) {
      this.curPath = to.path
      const openMenu = document.querySelector('.is-active .menu-round')
      console.log('%c [ openMenu ]-89', 'font-size:13px; background:pink; color:#bf2c9f;', openMenu)
      // openMenu.style.backgroundColor = '#4184d5'
      // console.log('%c [ openMenu ]-89', 'font-size:13px; background:pink; color:#bf2c9f;', openMenu)
    }
  },
  mounted() {
    if (this.$route.path !== '/home') {
      this.operationFn(this.$route.path);
    }
  },
  computed: {
    ...mapState('permission', ['routers', 'currentMenu', 'useMenu', 'commissionRecordBadge','pendingShipmentReminderQuantity','menuGray']),
    ...mapState('app', ['shopConfig','isGrayUser']),

  },
  beforeRouteUpdate (to, from, next) {
    console.log('%c [ to ]-101', 'font-size:13px; background:pink; color:#bf2c9f;', to)
    // 在当前路由改变，但是该组件被复用时调用
    // 举例来说，对于一个带有动态参数的路径 /foo/:id，在 /foo/1 和 /foo/2 之间跳转的时候，
    // 由于会渲染同样的 Foo 组件，因此组件实例会被复用。而这个钩子就会在这个情况下被调用。
    // 可以访问组件实例 `this`
  },
  methods: {
    ...mapMutations('permission', ['SET_IFRAME', 'ADD_TABLIST', 'SET_CURRENTTAB']),
    toggleCollapse() {
      this.isCollapse = !this.isCollapse
      this.$store.dispatch('app/toggleSideBar')
    },
    menuSelect(path) {
      this.ADD_TABLIST({path})
      this.SET_CURRENTTAB(path)
      const obj = {
        showIframe: false,
        iframeSrc: '',
      }
      if (isExternal(path)) {
        obj.showIframe = true
        obj.iframeSrc = path
        this.SET_IFRAME(obj)
      } else {
        obj.showIframe = false
        obj.iframeSrc = ''
        this.SET_IFRAME(obj)
      }
    },
    operationFn(path) {
      console.log('%c [ path ]-112', 'font-size:13px; background:pink; color:#bf2c9f;', path)
      window.openTab(path)
    }
  }
}
</script>

<style scoped lang="scss">

::v-deep   .noBadge .menuBadge {
  display: none;
}

.commonOperation {
  width: 100%;
  .commonTitle {
    font-family: PingFangSC, PingFangSC-Medium;
    font-weight: 500;
    color: #333333;
    line-height: 56px;
    font-size: 14px;
    padding: 0 20px;
    display: flex;
    align-items: center;
  }

  .commonTitle:hover {
    background-color: #ecf3fb;
  }
}
.menu-top{
  padding-top:15px;
}
.collapse {
  //position: relative;
  //bottom: 0;
  width: 100%;
  height: 40px;
  line-height: 40px;
  z-index: 1001;
  font-size: 16px;
  text-align: center;
  border-top: 1px solid rgba(0, 0, 0, 0.2);
}

.operation {
  .el-col {
    color: #222222;
    font-size: 14px;
    cursor: pointer;
    padding-left: 20px;
    line-height: 46px;

  }

  .el-col:hover {
    background-color: #ecf3fb;
  }
}
.menu-round{
  display: inline-block;
  width: 12px;
  height: 12px;
  border-radius: 12px;
  background-color: #DCDCDC;
  margin-right: 4px;
}


</style>
