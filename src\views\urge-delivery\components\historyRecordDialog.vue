<template>
<div>
    <el-dialog 
        title="历史记录" 
        :visible="histroyDialogVisible" 
        width="50%"
        class="histroyArea" 
        @close="closeDialog">
        <el-timeline>
            <el-timeline-item
                v-for="(item,index) in histroyDialog.data"
                :key="index"
                :timestamp="`【${item.eventStatusStr}】${item.historyCreateTime}`"
                placement="top">
                    <div class="item-left" v-if="item.customFields.appealCategory">
                      <span>申诉类别</span>
                      <span style="margin: 0 5px"> : </span>
                      <span>{{item.customFields.appealCategory}}</span>
                    </div>
                    <div class="item-left" v-if="item.customFields.appealDescription">
                      <span>申诉说明</span>
                      <span style="margin: 0 5px"> : </span>
                      <span>{{item.customFields.appealDescription}}</span>
                    </div>
                    <div class="item-left" v-if="(item.customFields.appealEvidence || []).length > 0">
                      <span>申诉凭证</span>
                      <span style="margin: 0 5px"> : </span>
                      <div style="width: 400px;margin-top: 10px">
                        <i-img :value="imageClick(item.customFields.appealEvidence)" :maxShowLength="9"></i-img>
                      </div>
                    </div>
                    <div class="item-left" v-if="item.customFields.auditDescription">
                      <span>审核说明</span>
                      <span style="margin: 0 5px"> : </span>
                      <span>{{item.customFields.auditDescription}}</span>
                    </div>
                    <div class="item-left" v-if="item.customFields.expireTime">
                      <span>最迟发货时间</span>
                      <span style="margin: 0 5px"> : </span>
                      <span>{{formatTime(item.customFields.expireTime)}}</span>
                    </div>
                    <div><span style="width:20px;display: inline-block"></span><span>{{item.customFields.prompt}}</span></div>
            </el-timeline-item>
        </el-timeline>
        <div slot="footer">
            <el-button type="primary" @click="closeDialog">确认</el-button>
        </div>
    </el-dialog>
    <el-dialog :visible.sync="imageDialogVisible">
        <img width="100%" :src="dialogImageUrl" alt="">
    </el-dialog>
</div>
</template>

<script>
import iImg from "./i-img.vue"
import {getHistroy} from "@/api/urge-delivery"
export default {
    props: {
        histroyDialogVisible: {
            type: Boolean,
            default: false
        },
        reminderId: {
            default: ""
        }
    },
    components: {
        iImg
    },
    created() {
        const loading = this.popLoding()
        getHistroy({reminderId: this.reminderId}).then(res => {
            if(res.code === 0) {
                this.histroyDialog.data = (res.result || {}).shippingReminderHistory || []
            }else {
                this.$message.error(res.msg || res.errMsg || "服务异常")
            }
        }).finally(() => {
            loading.close()
        })
    },
    data() {
        return {
            histroyDialog: {
                data: []
            },
            dialogImageUrl: "",// 缩略图的图片路径
            imageDialogVisible: false, // 缩略图弹窗
        }
    },
    methods: {
        closeDialog() {
            this.$emit('cancelDialog');
        },
        imageClick(target) {
            let newArr = []
            target.forEach(element => {
               let url = {url: element}
               newArr.push(url) 
            });
            return newArr
        },
        popLoding() { // 加载遮罩
          return this.$loading({
            lock: true,
            text: 'Loading',
            spinner: 'el-icon-loading',
            background: 'rgba(255,255,255, 0.8)'
        })
        },
        formatTime(d) {
            var date = new Date(d);
            var YY = date.getFullYear() + '-';
            var MM =
            (date.getMonth() + 1 < 10
                ? '0' + (date.getMonth() + 1)
                : date.getMonth() + 1) + '-';
            var DD = date.getDate() < 10 ? '0' + date.getDate() : date.getDate();
            var hh =
            (date.getHours() < 10 ? '0' + date.getHours() : date.getHours()) + ':';
            var mm =
            (date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes()) +
            ':';
            var ss =
            date.getSeconds() < 10 ? '0' + date.getSeconds() : date.getSeconds();
            return YY + MM + DD + ' ' + hh + mm + ss;
      },
    }
}
</script>

<style lang="scss" scoped>
::v-deep   .el-timeline-item__timestamp{
    color: #000;
    font-size: 16px;
}
.title {
    padding: 10px 0;
    margin-left: 10px;
}
.item-left {
        width: 100%;
        display: flex;
        align-items: flex-start;
        margin: 5px 0;
        > span:first-child,
        > button:first-child {
                flex-shrink: 0;
                width: 85px;
                text-align: end;
                color:#8d8d8d;
        }
        > span:nth-child(2) {
                flex-grow: 0;
                display: -webkit-box;//对象作为弹性伸缩盒子模型显示 
        -webkit-box-orient: vertical;//设置或检索伸缩盒对象的子元素的排列方式 
        -webkit-line-clamp: 100;//溢出省略的界限
                overflow: hidden;
                text-overflow: ellipsis;
                text-align: start !important;
        }
}
::v-deep   .el-dialog {
  border-radius: 5px;
  overflow: hidden;
}
::v-deep   .el-dialog__header {
  padding: 10px 15px;
  background-color: #f3f3f3;
}
::v-deep   .el-dialog__header span {
  font-size: 14px;
  line-height: normal;
}
::v-deep   .el-dialog__header button {
  right: 15px;
  top: 15px;
}
::v-deep   .el-dialog__body {
  max-height: 500px;
  overflow: auto;
  padding: 5px 15px;
  border-bottom: solid 1px #e4eaf1;
}
::v-deep   .el-dialog__footer {
  padding: 15px;
}
::v-deep   .el-timeline {
    .el-timeline-item__node {
      background-color: rgb(72, 147, 255);
    }
    .el-timeline-item__tail {
        border-left: 2px solid #0087d4;
    }
}
</style>