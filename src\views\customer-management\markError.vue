<template>
  <el-dialog title="标记证件异常" :visible="dialogVisible" width="80%" @close="handleClose">
    <div style="margin-bottom:10px">
      <div>你正在标记该客户的<span style="color: red">{{ current.credentialName }}</span>证件异常</div>
      <div>标记后，该订单的 <span style="color:red">"关联资质状态"会变更为"关联资质异常"，同时客户及业务员将收到消息提醒。请详细描述异常原因：</span> </div>
    </div>
    <div style="margin-top: 20px">
      <el-checkbox-group
        v-model="checkboxGroup"
        class="check_out"
        @change="handleCheckBox"
      >
        <el-checkbox
          v-for="(item,index) in provinceAndMerchantType"
          :key="index"
          :label="item.label"
          :value="item.label"
        >{{ item.label }}</el-checkbox>
      </el-checkbox-group>
    </div>
    <div>
      <el-input
        v-model="textarea"
        class="dialog_inp"
        type="textarea"
        placeholder="请输入证件异常原因"
        maxlength="300"
        show-word-limit
      />
    </div>
    <div style="text-align:right;margin-top:10px">
      <el-button type="primary" @click="handleSubmit">提交</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { marketList, addMarket } from '@/api/customer-management/index';

export default {
  name: 'ViewingInventoryLogs',
  props: {
    dialogVisible: {
      type: Boolean,
      default: false,
    },
    current: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      checkboxGroup: [],
      textarea: '',
      provinceAndMerchantType: [],
    };
  },
  created() {
    this.getList();
  },
  methods: {
    // 点击提交
    handleSubmit() {
      if (!this.textarea) {
        this.$message.warning('请输入证件异常原因');
        return false;
      }
      const params = {
        orderNo: this.$route.query.orderNo,
        exceptionText: `${this.current.credentialName}${this.textarea}`,
      };
      addMarket(params).then((res) => {
        if (res.code === 0) {
          this.$confirm('证件异常已标记成功，可到对应订单查询或删除异常原因', '提示', {
            confirmButtonText: '确定',
            showCancelButton: false,
          })
            .then(() => {
              this.$emit('update:handleChange', false);
            }).catch(() => {
              this.$emit('update:handleChange', false);
            });
        }
      });
    },
    // 获取列表
    getList() {
      marketList().then((res) => {
        if (res.code === 0) {
          res.result.forEach((item) => {
            item.content.forEach((item2) => {
              this.provinceAndMerchantType.push({ label: `【${item.title}】${item2}` });
            });
          });
        }
      });
    },
    handleCheckBox(val) {
      this.textarea = val.join(',');
    },
    handleClose() {
      this.$emit('update:handleChange', false);
    },
  },
};
</script>

<style scoped lang="scss">
::v-deep   .el-table thead th {
  background: #f9f9f9;
  border: none;

  .cell {
    font-family: PingFangSC, PingFangSC-Regular;
    font-weight: 400;
    text-align: left;
    color: rgba(51, 51, 51, 0.85);
    line-height: 22px;
  }
}

::v-deep   .el-table__body-wrapper {
  font-size: 12px;
  color: #666666;
}
::v-deep   .dialog_inp .el-textarea__inner{
  height: 100px !important;
  margin-top: 10px;
}
::v-deep   .check_out .el-checkbox{
  display: block;
  line-height: 28px;
}
</style>
