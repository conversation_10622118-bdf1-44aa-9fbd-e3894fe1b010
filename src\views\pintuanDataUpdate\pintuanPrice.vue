<script>
import { apiApplyList, getIsApplyStepPrice, saveActivity,updateCheck } from '@/api/market/collageActivity'
import SupplyTypeConfig from '../marketing/components/supplyTypeConfig.vue';
import { checkSkuScope } from '@/api/product';
export default {
  props: {
    value: {
      type: Object,
      default: () => {}
    },
    isNew:{
      type:Boolean,
      default: false,
    }

  },
  components: {
    SupplyTypeConfig
  },
  watch: {
    value: {
      handler(newVal) {
        if (!this.value.visible) return;
        this.loading = true;
        this.getIsApplyStepPrice();
        apiApplyList({ idStr: this.value.actId, pageNum : 1, pageSize: 10 }).then(res => {
          if (res.code == 1000) {
            this.originData = res.data.data.list[0];
          }
        }).finally(() => {
          this.loading = false;
          this.initData(this.originData);
        })
      },
      deep:true
    }
  },
  data() {
    return {
      originData: {},
      loading: false,
      titleMap: {
        '1' : '修改价格',
        '2' : '修改虚拟供应商',
        '3' : '修改供货信息',
        '4' : '修改活动库存'
      },
      rules: Object.freeze({
        groupPrice: [{ required: true, message: '请填写拼团价格',trigger: 'blur' }],
        groupNum: [
          { required: true, message: '请填写起拼数量',trigger: 'blur' },
          { pattern: /^[1-9]\d*$/, message: '请维护正整数', trigger: ['blur', 'change'] }
        ],
        totalLimitNum: [
          { required: false, message: '请填写拼团活动采购上限',trigger: 'blur' },
          { pattern: /^[1-9]\d*$/, message: '请维护正整数', trigger: ['blur', 'change'] }
        ],
        personalLimitNum: [{ required: false, message: '请填写单个药店参团数量上限',trigger: 'blur' }],
      }),
      ruleForm: {
        activityReportGroupLevelPriceDTOList: [],
        stepPriceStatus: -1,
        isCopyCsuModel: 1,
        // isCopySaleArea: 1,
        personalLimitType: '0',
        groupPrice: '',
        groupNum: '',
        customerGroupId: undefined,
        customerGroupName: '',
        totalLimitNum: '',
        personalLimitNum: '',
        preheatTime: '',
        activityTime: [],
        auditTime: [],
        registrationTime: [],
        isVirtualShop: '',
        onTheWayStock: '',
        topicTitle: '',
        baseCustomerGroupId: '',
        customerGroupVO: null,
      },
      submitLoading: false,
      disabled: false,
      otherRuleDisabled: false,
      judgeIsApplyStepPrice: false, //
      totalLimitNumDisabled: false,
      pintuanDisabled: false,
      subOtherDisabled: false,
      saleScopeDTO: {},
      swtich: {
        value1: false,
        value2: false,
        personalLimitTypeList: [{
          code: "1", value: "活动期间限购"
        }, {
          code: "2", value: "每天（每天00:00至24:00）"
        }, {
          code: "3", value: "单笔订单限购"
        }, {
          code: "4", value: "每周（周一00:00至周日24:00）"
          }, {
          code: "5", value: "每月（每月1号00:00至每月最后一天24:00）"
        }]
      },
      statusList: {
        status1: false,   //是否不允许修改单个药店限购
        status2: false,  //是否不允许修改限购类型
        status3: false,  //是否不允许修改限购数量
        status4: false,  //是否不允许修改拼团活动限购
        status5: false,  //是否不允许修改拼团活动总数量
      },
      isHidden: true,
    }
  },
  methods: {
    close() {
      const result = this.value;
      result.visible = false;
      this.$emit('input', result);
    },
    initData(data) {
      this.disabled = false;
      this.pintuanDisabled = false;
      this.statusList = {
        status1: false,   //是否不允许修改单个药店限购
        status2: false,  //是否不允许修改限购类型
        status3: false,  //是否不允许修改限购数量
        status4: false,  //是否不允许修改拼团活动限购
        status5: false,  //是否不允许修改拼团活动总数量
      }
      //判断是否有平台
      if (data.activityReportGroupAmountDtos && data.activityReportGroupAmountDtos.some(item => item.name == '平台')) {
        //有则判断当前编辑的活动状态  3 未启动 不可切换是否限购和限购类型     4进行中
        this.pintuanDisabled = true;
        if (data.status == 3 || data.status == 4) {
          this.statusList.status1 = true;
          this.statusList.status2 = true;
          this.statusList.status4 = true;
          this.statusList.status5 = true;
        }
      } else {
        if (data.status == 4) {
          /* this.statusList.status3 = true; */
          /* this.statusList.status4 = true; */
          /* this.statusList.status5 = true; */
        }
      }
      Object.keys(this.ruleForm).forEach((key) => {
        this.ruleForm[key] = data[key];
      }, this);
      this.swtich.value1 = (!isNaN(Number(this.ruleForm.personalLimitType)) && Number(this.ruleForm.personalLimitType) > 0)
      this.swtich.value2 = (!isNaN(Number(this.ruleForm.totalLimitNum)) && Number(this.ruleForm.totalLimitNum) > 0)
      this.ruleForm.personalLimitType = this.ruleForm.personalLimitType.toString();
      this.ruleForm.auditTime = [data.auditStime, data.auditEtime];
      this.ruleForm.activityTime = [data.actStartTime, data.actEndTime];
      this.ruleForm.preheatTime = data.preheatTime;
      this.ruleForm.isCopyCsuModel = data.isCopyCsuModel;
      this.ruleForm.activityType = data.activityType
      // this.ruleForm.isCopySaleArea = data.isCopySaleArea;
      this.ruleForm.customerGroupId = data.customerGroupId;
      this.ruleForm.isVirtualShop = String(data.isVirtualShop);
      this.ruleForm.stepPriceStatus = data.stepPriceStatus;
      this.ruleForm.activityReportGroupLevelPriceDTOList = data.activityReportGroupLevelPriceDTOList;
      // this.ruleForm.radioCheck = true;
      // if (data.isCopyCsuModel === 2 || (data.isCopyCsuModel === 1 && data.isCopySaleArea === 2)) {
      //   this.ruleForm.radioCheck = false;
      // }
      if (data.status == 1 && data.registrationEtime && data.registrationEtime <= new Date().getTime()) {
        this.disabled = true;
      }
      this.otherRuleDisabled = true;
      this.subOtherDisabled = true;
      this.totalLimitNumDisabled = false;
      if (data.status === 3 || data.status === 4) {
        this.disabled = true;
        if (data.subsidyInitiatorJson) {
          this.totalLimitNumDisabled = true;
        }
        if (data.activityPriceStrategyList) {
          this.totalLimitNumDisabled = true;
        }
        // if (data.subsidyInitiatorJson === null || data.activityPriceStrategyList === null) {
        //   this.otherRuleDisabled = false;
        // }
        this.otherRuleDisabled = false;
        if (data.subsidyInitiatorJson === null && data.activityPriceStrategyList === null) {
          this.subOtherDisabled = false;
        }
      }
      console.log('xy',data.saleScopeDTO);
      if(!data.saleScopeDTO.controlUserTypes){
        data.saleScopeDTO.controlUserTypes=""
      }
      this.saleScopeDTO = {
        ...data.saleScopeDTO,
        customerGroupName: data.customerGroupName,
      };
      console.log(this);
    },
    getIsApplyStepPrice() {
      getIsApplyStepPrice().then((res) => {
        if (res.success) {
          const { data } = res;
          this.judgeIsApplyStepPrice = (data || {}).judgeIsApplyStepPrice;
        }
      });
    },
    handleSetGroupLevelPriceDTOList(type, index) {
      if (this.disabled && this.otherRuleDisabled) return;
      if (type === 'add') {
        if (
          this.ruleForm.activityReportGroupLevelPriceDTOList.length === 3
        ) {
          this.$message.warning('最多添加3条数据');
          return;
        }
        const obj = { ...this.addInitiatorDTOItem };
        this.ruleForm.activityReportGroupLevelPriceDTOList.push(obj);
        return;
      }
      this.ruleForm.activityReportGroupLevelPriceDTOList.splice(index, 1);
    },
    submit() {
      if (this.submitLoading) return true;
      this.$refs.ruleForm.validate(async (valid) => {
        if (valid) {
          // if (!this.ruleForm.radioCheck && !this.ruleForm.customerGroupId) {
          //   this.$message.warning('请选择人群');
          //   return;
          // }
          //
          if (this.value.type == 1 && this.ruleForm.groupPrice > this.originData.groupPrice && this.originData.rankFirst) {
            this.$confirm('商品为竞价成功商品，不允许调高价格', '提示', {
              confirmButtonText: '确定',
              showCancelButton: false,
              type: 'warning'
            }).then(async () => {
              this.close();
            })
            return;
          }
          if ((this.ruleForm.groupPrice && isNaN(this.ruleForm.groupPrice))
              || (this.ruleForm.groupNum && isNaN(this.ruleForm.groupNum))
              || (this.ruleForm.totalLimitNum && isNaN(this.ruleForm.totalLimitNum))
              || (this.ruleForm.personalLimitNum && isNaN(this.ruleForm.personalLimitNum))
              || (this.ruleForm.onTheWayStock && isNaN(this.ruleForm.onTheWayStock))
          ) {
            this.$message.warning('请填写数字');
            return;
          }
          let supplyInfo = this.$refs['supplyTypeInfo'].getAllSupplyInfo();
          if (this.ruleForm.stepPriceStatus === 1) {
            if (
              this.ruleForm.activityReportGroupLevelPriceDTOList
            ) {
              this.ruleForm.activityReportGroupLevelPriceDTOList.forEach(
                (item, index) => {
                  item.level = index + 1;
                },
              );
            }
          }
          const editCollageItem = this.ruleForm;
          if (this.swtich.value1 && (!this.ruleForm.personalLimitType || !this.ruleForm.personalLimitNum)) {
            this.$message.warning("请填写单个药店限购类型及参团数量上限")
            return
          }
          console.log(this.originData,11111)
          // let ver=()=>{
          //   if(this.ruleForm.stepPriceStatus != 1||this.originData.stepPriceStatus != 1){
          //     return false
          //   }
          //   let next=false
          //   this.ruleForm.activityReportGroupLevelPriceDTOList.forEach((res,index)=>{
          //     if(this.originData.activityReportGroupLevelPriceDTOList[index]&&Number(res.discountPrice)>Number(this.originData.activityReportGroupLevelPriceDTOList[index].discountPrice)){
          //       next=true
          //     }
          //   })
          //   return next
          // }
          // if(this.originData.activityReportGroupAmountDtos.some(item => item.name == '平台')&&(Number(this.ruleForm.groupPrice)>Number(this.originData.groupPrice)||ver())){
          //   const confirmRes = await this.$confirm(
          //     `当前拼团活动存在平台补贴${this.originData.activityReportGroupAmountDtos.find(item => item.name == '平台').amount}元，调高价格后补贴将自动失效，请确认是否继续调高拼团价格？`,
          //       '提示', {
          //           confirmButtonText: '确定',
          //           cancelButtonText: '取消',
          //           type: 'warning'
          //       }
          //   ).catch(err => console.log(err))
          //   // 判断confirmRes的值
          //   if (confirmRes !== 'confirm') {
          //       return
          //   }
          // }

          const query = {
            frameReportId: this.originData.frameReportId || null,
            barcode: this.originData.barcode,
            version: this.originData.version || null,
            groupPrice: this.ruleForm.groupPrice,
            groupNum: this.ruleForm.groupNum,
            totalLimitNum: this.swtich.value2 ? this.ruleForm.totalLimitNum : '',
            personalLimitNum: this.swtich.value1 ? this.ruleForm.personalLimitNum : '',
            personalLimitType: this.swtich.value1 ? this.ruleForm.personalLimitType : '0',
            // isCopySaleArea: this.ruleForm.radioCheck ? 1 : 2,
            // customerGroupId: !this.ruleForm.radioCheck ? this.ruleForm.customerGroupId : '',
            isVirtualShop: this.ruleForm.isVirtualShop,
            onTheWayStock: this.ruleForm.onTheWayStock,
            stepPriceStatus: this.ruleForm.stepPriceStatus,
            activityReportGroupLevelPriceDTOList: this.ruleForm.activityReportGroupLevelPriceDTOList || null,
          };
          query.baseId = this.originData.baseId;
          query.saleScopeDTO = { isCopySaleArea: supplyInfo.isCopySaleArea };

          if (supplyInfo.isCopySaleArea === 3) {
            let { customerGroupId } = supplyInfo;
            query.saleScopeDTO = { ...supplyInfo ,customerGroupId};
            if (supplyInfo.isCopyControlUser === 2 && supplyInfo.controlRosterType != 2) {
              const controlUserTypes = supplyInfo.controlUserTypes ? supplyInfo.controlUserTypes : [];
              query.saleScopeDTO.controlUserTypes = controlUserTypes.join();
            } else {
              query.saleScopeDTO.controlUserTypes = '';
            }
          } else if (supplyInfo.isCopySaleArea === 2) {
            let { customerGroupId } = supplyInfo;
            query.saleScopeDTO = {
              isCopySaleArea: 2,
              customerGroupId,
            };
          }else if(supplyInfo.isCopySaleArea === 1){
            let { customerGroupId } = supplyInfo;
            query.saleScopeDTO = {
              isCopySaleArea: 1,
              customerGroupId,
            };
          }
          /* if (this.value.type == 3) {
            checkSkuScope({
              barcode: this.originData.barcode,
              skuId: this.originData.csuid
            }).then(res => {
              if (res.msg == '1') {
                this.$confirm('商品竞价中标，修改后将影响竞价，确认是否继续?', '提示', {
                  confirmButtonText: '确定',
                  cancelButtonText: '取消',
                  type: 'warning'
                }).then(async () => {
                  this.confirmSubmit(query);
                })
              } else {
                this.confirmSubmit(query);
              }
            })
          } else {
            this.confirmSubmit(query);
          } */
          this.confirmSubmit(query);
        } else {
          return false;
        }
        return false;
      });
    },
    valueChange(value, key) {
      const arr = value.toString().match(/^[0-9]*[1-9][0-9]*$/);
          this.ruleForm[key] = arr ? arr[0] : null;
    },
    handleChangeItemStepPriceStatus(val) {
      if (val === 2) {
        this.ruleForm.activityReportGroupLevelPriceDTOList = null;
      } else if (
        this.ruleForm.activityReportGroupLevelPriceDTOList === null || (this.ruleForm.activityReportGroupLevelPriceDTOList || []).length === 0
      ) {
        const arr = [];
        const obj = { ...this.addInitiatorDTOItem };
        arr[0] = obj;
        this.ruleForm.activityReportGroupLevelPriceDTOList = arr;
      }
    },
   async confirmSubmit(query) {
    this.submitLoading = true;
     let res=await updateCheck(query)
     this.submitLoading = false;
     if(res.code!=1000){
        const confirmRes = await this.$confirm(
              res.msg,
                '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }
            ).catch(err => console.log(err))
            // 判断confirmRes的值
            if (confirmRes !== 'confirm') {
                return
            }

     }
      this.submitLoading = true;
      saveActivity(query)
      .then((res) => {
        if (res.success) {
          this.$message.success('提交成功');
          setTimeout(() => {
            this.$emit("getList","")
            this.close();
            // this.$router.replace({
            //   path: '/collageActivity',
            //   query: { refresh: true },
            // });
          }, 500);
        } else if (res.msg) this.$message.warning(res.msg);
      }).finally(() => {
        this.submitLoading = false;
      })
    },
  },
}
</script>

<template>
  <el-dialog :title="titleMap[value.type]" :visible="value.visible" @close="close">
    <el-form ref="ruleForm" :model="ruleForm" :rules="rules" size="small" label-width="170px">
      <div v-show="value.type == 1">
        <el-form-item label="拼团价格" prop="groupPrice">
          <el-input :disabled="disabled && otherRuleDisabled" v-model="ruleForm.groupPrice" placeholder="请输入" style="width: 200px" />
          <span v-if="originData.buyMostPrice" style="color: #ff2121">热销拼团价格：{{ originData.buyMostPrice }}</span>
        </el-form-item>
        <el-form-item label="起拼数量" prop="groupNum">
          <el-input :disabled="disabled && otherRuleDisabled" v-model="ruleForm.groupNum" placeholder="请输入" style="width: 200px" />
          <span v-if="originData.buyMostStartQty" style="color: #ff2121">热销起拼数量：{{ originData.buyMostStartQty }}</span>
        </el-form-item>
          <el-form-item v-if="judgeIsApplyStepPrice" label="是否设置阶梯价" prop="stepPriceStatus">
            <el-radio-group v-model="ruleForm.stepPriceStatus" style="margin-right: 10px" :disabled="(disabled && otherRuleDisabled) || pintuanDisabled" @change="handleChangeItemStepPriceStatus">
              <el-radio :label="2">否</el-radio>
              <el-radio :label="1">是</el-radio>disabled && pintuanDisabled
            </el-radio-group>
            <el-tooltip placement="bottom">
              <div slot="content">
                1.阶梯价最多支持3个阶梯。<br />
                2. 设置阶梯价时 最大阶梯起拼数量≤活动个人限购数量≤活动总限购数量，不满足则提交失败。<br />
                3.商品列表中展示的起拼数量为默认起拼数量，一个拼团活动中最多可支持4个拼团价格。<br />
                4.药店买满起始促销数量后，成团价自动改为对应阶梯价；<br />
                起拼数量＜促销起始数量1＜促销起始数量2＜促销起始数量3≤活动个人限购数量，拼团价格＞阶梯价1＞阶梯价2＞阶梯价3，不符合则提交失败；
              </div>
              <i class="el-icon-warning-outline" />
            </el-tooltip>
          </el-form-item>
		      <el-form-item v-if="judgeIsApplyStepPrice && ruleForm.stepPriceStatus === 1" label="" >
            <div v-for="(item, index) in ruleForm.activityReportGroupLevelPriceDTOList" :key="index" style="display: flex; align-items: center;margin-bottom: 10px;align-items: center;">
              <span style="margin-right: 10px;">促销起始数量{{ index + 1 }}</span>
              <el-input v-model.number="item.startQty" :precision="0" :disabled="disabled && otherRuleDisabled" placeholder="请输入" style="width: 200px" />
              <span style="margin: 0 10px;">阶梯价{{ index + 1 }}</span>
              <el-input-number v-model="item.discountPrice" :precision="2" :disabled="disabled && otherRuleDisabled" placeholder="请输入" style="width: 200px" />
              <div v-if="isHidden">
                <i v-if="index === 0" style="font-size: 30px; cursor: pointer;margin-left: 10px;" class="el-icon-circle-plus-outline" @click="handleSetGroupLevelPriceDTOList('add', index)" />
                <i v-if="index !== 0" style="font-size: 30px; cursor: pointer;margin-left: 10px;" class="el-icon-remove-outline" @click="handleSetGroupLevelPriceDTOList('reduce', index)"/>
              </div>
            </div>
          </el-form-item>
      </div>
      <div v-show="value.type == 2">
        <el-form-item label="设置虚拟供应商" prop="isVirtualShop">
          <el-radio :disabled="originData.status == 5" v-model="ruleForm.isVirtualShop" label="1">是</el-radio>
          <el-radio :disabled="originData.status == 5" v-model="ruleForm.isVirtualShop" label="2">否</el-radio>
          <span style="color: #ff2121;display: inline-block;width: 80%;vertical-align: top">
            • 选“是”，则该拼团活动支付前都只显示虚拟供应商信息，支付成功后才显示真实供应商
            信息<br>
            • 选“否”，则正常显示供应商信息
          </span>
        </el-form-item>
      </div>
      <div v-show="value.type == 4">
        <el-form-item label="单个药店是否限购">
            <el-switch :disabled="(disabled && otherRuleDisabled) || statusList.status1" v-model="swtich.value1" active-color="#4183d5" inactive-color="#b1b1b1" active-text="限制" inactive-text="不限制" @change="ruleForm.personalLimitType = ''; ruleForm.personalLimitNum = ''" style="vertical-align: top;transform: translateY(7px);">
            </el-switch>
            <div style="display: inline-block;margin-left: 10px;">
              <span style="color: rgb(255, 33, 33);">
                单个药店在设定的周期内可购买的最大采购数量，需为正整数。限购数量需≥起拼数量;
                <br>
                系统会从修改那一刻重新计算限购数量。
              </span>
            </div>
        </el-form-item>
        <el-form-item label="单个药店限购类型" prop="personalLimitType" v-if="swtich.value1">
          <el-select v-model="ruleForm.personalLimitType" :disabled="(disabled && otherRuleDisabled) || statusList.status2" placeholder="请选择">
            <el-option v-for="item in swtich.personalLimitTypeList" :key="item.code" :label="item.value" :value="item.code"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item v-if="swtich.value1" label="单个药店参团数量上限" prop="personalLimitNum">
          <el-input :disabled="(disabled && otherRuleDisabled) || statusList.status3" v-model="ruleForm.personalLimitNum" placeholder="请输入正整数" style="width: 200px" @input="valueChange(ruleForm.personalLimitNum, 'personalLimitNum')"/>
          <span style="color: #ff2121"> 每家参团药店活动期间最高采购数量</span>
        </el-form-item>
        <el-form-item label="是否限制活动总限购">
            <el-switch :disabled="(disabled && totalLimitNumDisabled) || statusList.status4" v-model="swtich.value2" @change="ruleForm.totalLimitNum = ''" active-color="#4183d5" inactive-color="#b1b1b1" active-text="限制" inactive-text="不限制">
            </el-switch>
        </el-form-item>
        <el-form-item label="拼团活动采购上限" prop="totalLimitNum" v-if="swtich.value2">
          <el-input :disabled="(disabled && totalLimitNumDisabled) || statusList.status5" v-model="ruleForm.totalLimitNum" placeholder="请输入" style="width: 200px" />
          <span style="color: #ff2121"> 所有参团药店采购总数量不能大于该数值</span>
        </el-form-item>
      </div>
      <div v-show="value.type == 3">
        <SupplyTypeConfig ref="supplyTypeInfo" :dis="false" :baseCustomerGroupName="ruleForm.customerGroupVO ? ruleForm.customerGroupVO.tagName : ''" :isPT="value.type == 3 && ruleForm.activityType == 22"  :baseCustomerGroupId="ruleForm.baseCustomerGroupId" :disabled="disabled" :subOtherDisabled="subOtherDisabled" :sale-scope-dto="saleScopeDTO" />
      </div>
    </el-form>
    <template slot="footer">
      <el-button type="primary" @click="submit">提交</el-button>
      <el-button @click="close">取消</el-button>
    </template>
  </el-dialog>
</template>
<style scoped>
</style>
