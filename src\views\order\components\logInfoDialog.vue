<template>
  <div>
    <el-dialog
      title="物流信息详情"
      :visible="stateVisible"
      width="60%"
      @close="closeDialog"
    >
      <div class="titleDiv"><span>订单编号：{{info.orderNo}}</span> <span>配送方式：{{info.logisticsWayDesc}}</span> <span>包裹数：{{editableTabs.length}}</span></div>
      <div>
        <el-tabs v-model="editableTabsValue" type="border-card">
          <el-tab-pane
            v-for="(item, index) in editableTabs"
            :label="'包裹'+(index+1)"
            :name="'type'+(index+1)"
            :key="index"
          >
            <div class="tipDiv">
              <span>运单号：{{item.logisticsNo}}</span>
              <span>状态：{{item.logisticsStatusDesc}}</span>
              <!-- <span style="color:red;">若发顺丰快递，出现物流未能同步的情况，请提供发件人手机号/手机号后四位，联系店铺对接人配置</span> -->
            </div>
            <div v-for="(items,indexs) in item.logisticsNodeList" :key="indexs" class="lineDiv">
              <i :class="indexs==0?'iBlue':''"></i> <span style="color: #666666;width: 27%;padding-left: 15px">{{items.time | formatDate}}</span> <span style="width: 68%">{{items.description}}</span>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getQueryLogisticsInfo } from '@/api/order/index';

export default {
  name: 'LogInfoDialog',
  data() {
    return {
      stateVisible: false,
      editableTabs: [],
      editableTabsValue: 'type1',
      info: {
        orderNo: null,
        logisticsWayDesc: '',
      },
      tipHeight: document.documentElement.clientHeight / 3,
    };
  },
  methods: {
    getListData(orderNo) {
      this.stateVisible = true;
      getQueryLogisticsInfo({ orderNo }).then((res) => {
        if (res.code === 0) {
          this.info.logisticsWayDesc = res.result.logisticsCompany
          this.info.orderNo = res.result.orderNo
          this.editableTabs = res.result.logisticsPackages;
        } else {
          this.$message.error({ message: res.message, offset: this.tipHeight });
        }
      });
    },
    closeDialog() {
      this.stateVisible = false;
    },
    handleTabsEdit() {},
  },
};
</script>

<style scoped lang="scss">
.titleDiv{
  color: rgba(51,51,51,0.85);
  padding-bottom: 10px;
  span{
    padding-right: 30px;
  }
}
.tipDiv{
  font-size: 12px;
  color: #999999;
  padding: 0 10px 20px 10px;
  span{
    padding-right: 30px;
  }
}
.lineDiv{
  //height: 28px;
  border-left: 1px solid #d8d8d8;
  position: relative;
  margin: 0 10px;
  color: #333333;
  font-size: 12px;
  padding: 5px 0 10px 0;
  i{
    position: absolute;
    top: -2px;
    left: -3px;
    width: 5px;
    height: 5px;
    opacity: 1;
    background: #cccccc;
    border-radius: 50%;
  }
  .iBlue{
    position: absolute;
    top: -2px;
    left: -4px;
    width: 7px;
    height: 7px;
    opacity: 1;
    background: #4184d5;
    border-radius: 50%;
    box-shadow: 0px 0px 4px 0px #4184d5;
  }
  span{
    //padding-left: 20px;
    //position: absolute;
    //top: -6px;
    display: inline-block;
    margin-top: -13px;
    vertical-align: text-top;
    line-height: 20px;
  }
}
.flexBox{
  display: flex;
  align-items: center;
}
::v-deep  .el-dialog__body{
  padding: 10px 20px;
}
::v-deep  .el-dialog__header{
  padding: 10px 20px;
  background: #f9f9f9;
}
::v-deep  .el-dialog__headerbtn{
  top: 15px;
}
::v-deep  .el-dialog__title{
  font-size: 16px;
}
</style>
