<template>
  <div>
    <el-dialog
      title="查看业务商圈"
      :visible="show"
      :width="dialogWidth"
      ref="viewproduct"
      :before-close="handleDialogClose"
      @open="open"
    >
      <business-circle-detail :detailId="row.id" :isPage="false" />
      <span slot="footer">
        <el-button type="primary" style="margin-top: 10px" size="small" @click="sureBtn"
          >确 定</el-button
        >
      </span>
    </el-dialog>
  </div>
</template>

<script>
import businessCircleDetail from '../business-circle-detail.vue'

export default {
  components: { businessCircleDetail },
  name: 'viewBusinessCircle',
  data() {
    return {
      show: false,
      dialogWidth: '95%'
    }
  },
  model: {
    prop: 'dialogShow',
    event: 'onDialogChange'
  },
  props: {
    dialogShow: Boolean,
    row: Object
  },
  mounted() {
    this.$nextTick(() => {
      this.show = true
    })
  },
  methods: {
    handleDialogClose() {
      this.show = false
      this.$emit('onDialogChange', false)
    },
    open() {
      this.show = true
    },

    sureBtn() {
      this.handleDialogClose()
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep  .el-dialog__header {
  background-color: #f8f8ff;
}
</style>
