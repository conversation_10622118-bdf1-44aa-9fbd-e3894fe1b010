/**
 * 格式化日期
 * @param {*} date
 * @param {*} fmt
 */
function padLeftZero(str) {
  return `00${str}`.substr(str.length);
}
export function formatDate(date, fmt) {
  if (!date) return '';
  date = typeof date === 'string' ? date.replace(/-/g, '/') : date;
  date = new Date(date);
  if (/(y+)/.test(fmt)) {
    fmt = fmt.replace(RegExp.$1, `${date.getFullYear()}`.substr(4 - RegExp.$1.length));
  }
  const o = {
    'M+': date.getMonth() + 1,
    'd+': date.getDate(),
    'h+': date.getHours(),
    'm+': date.getMinutes(),
    's+': date.getSeconds(),
  };
  for (const k in o) {
    if (new RegExp(`(${k})`).test(fmt)) {
      const str = `${o[k]}`;
      fmt = fmt.replace(RegExp.$1, RegExp.$1.length === 1 ? str : padLeftZero(str));
    }
  }
  return fmt;
}
