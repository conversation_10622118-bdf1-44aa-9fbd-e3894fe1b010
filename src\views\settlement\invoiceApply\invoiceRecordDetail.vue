<template>
  <div class="invoiceRecord-detail">
    <el-button type="primary" @click="toBack">返回</el-button>
    <h4>发票申请单信息</h4>
    <el-form :inline="true">
      <el-row>
        <el-col :span="12">
          <el-form-item label="申请单号：">{{ info.invoiceApplyNo }}</el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="开票状态：">{{ info.invoiceStatusDesc }}</el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="收入月份：">{{ info.hireMonths }}</el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="失败原因：">{{ info.failReason }}</el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="发票类型：">{{ info.invoiceTypeDesc }}</el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="发票号码：">{{ info.invoiceNo }}</el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="开票金额：">{{ info.invoiceMoney || info.invoiceMoney === 0 ? info.invoiceMoney.toFixed(2) : ''}}</el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="发票代码：">{{ info.invoiceCode }}</el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label="申请时间：">{{ info.lastApplyTime | formatDate }}</el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label="开票时间：">{{ info.billingTime | formatDate }}</el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <h4>发票抬头信息</h4>
    <el-form :inline="true">
      <el-row>
        <el-col :span="12">
          <el-form-item label="发票抬头：">{{ info.invoiceTitle }}</el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="开户行：">{{ info.bankName }}</el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="地址：">{{ info.invoiceAddress }}</el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="收件人：">{{ info.contactor }}</el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="电话：">{{ info.invoicePhone }}</el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="联系电话：">{{ info.contactorPhone }}</el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="税号：">{{ info.taxNo }}</el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="收件地址：">{{ info.contactorAddress }}</el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label="开户账号：">{{ info.bankAccount }}</el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <h4>发票明细</h4>
    <xyy-table :data="list" :list-query="listQuery" :col="col" @get-data="getList"></xyy-table>
  </div>
</template>

<script>
import {
  getInvoiceRecordDetailList,
  getInvoiceRecordDetailInfo,
} from '../../../api/settlement/invoiceApply';

export default {
  name: 'invoiceRecordDetail',
  data() {
    return {
      list: [],
      listQuery: {
        pageSize: 10,
        page: 1,
        total: 0
      },
      col: [
        {
          index: 'billNo',
          name: '账单号',
          width: 200
        },
        {
          index: 'settlementTypeDesc',
          name: '佣金结算方式',
          width: 150
        },
        {
          index: 'productMoney',
          name: '商品金额（元）',
          width: 150,
          formatter: (row, col, cell) => (cell || cell === 0 ? cell.toFixed(2) : '')
        },
        {
          index: 'freightAmount',
          name: '运费金额（元）',
          width: 150,
          formatter: (row, col, cell) => (cell || cell === 0 ? cell.toFixed(2) : '')
        },
        {
          index: 'totalMoney',
          name: '单据总额含运费（元）',
          width: 200,
          formatter: (row, col, cell) => (cell || cell === 0 ? cell.toFixed(2) : '')
        },
        {
          index: 'shopTotalDiscount',
          name: '店铺总优惠（元）',
          width: 150,
          formatter: (row, col, cell) => (cell || cell === 0 ? cell.toFixed(2) : '')
        },
        {
          index: 'platformTotalDiscount',
          name: '平台总优惠（元）',
          width: 150,
          formatter: (row, col, cell) => (cell || cell === 0 ? cell.toFixed(2) : '')
        },
        {
          index: 'money',
          name: '实付金额（元）',
          width: 150,
          formatter: (row, col, cell) => (cell || cell === 0 ? cell.toFixed(2) : '')
        },
        {
          index: 'billHireMoney',
          name: '佣金金额（元）',
          width: 150,
          formatter: (row, col, cell) => (cell || cell === 0 ? cell.toFixed(2) : ''),
          renderHeader: (h, { column }) => {
            return h(
              'div', [
                h('span', column.label),
                h('el-tooltip', {
                  props: {
                    content: '账单的佣金金额=账单中所有单据的佣金金额求和',
                    placement: 'right',
                  },
                }, [
                  h('i', { class: 'el-icon-warning-outline' }),
                ]),
              ],
            );
          },
        },
        {
          index: 'statementTotalMoney',
          name: '应结算金额（元）',
          width: 150,
          formatter: (row, col, cell) => (cell || cell === 0 ? cell.toFixed(2) : ''),
          renderHeader: (h, { column }) => {
            return h(
              'div', [
                h('span', column.label),
                h('el-tooltip', {
                  props: {
                    content: '账单的应结算金额=账单中所有单据的应结算金额求和',
                    placement: 'right',
                  },
                }, [
                  h('i', { class: 'el-icon-warning-outline' }),
                ]),
              ],
            );
          },
        },
        {
          index: 'deductedCommission',
          name: '应缴纳佣金（元）',
          width: 150,
          formatter: (row, col, cell) => (cell || cell === 0 ? cell.toFixed(2) : ''),
          renderHeader: (h, { column }) => {
            return h(
              'div', [
                h('span', column.label),
                h('el-tooltip', {
                  props: {
                    content: '账单的应缴纳佣金=账单中所有单据的应缴纳佣金求和。单据平台补贴金额冲抵佣金金额后，对应账单商业应给平台缴纳的佣金金额',
                    placement: 'right',
                  },
                }, [
                  h('i', { class: 'el-icon-warning-outline' }),
                ]),
              ],
            );
          },
        },
        {
          index: 'actualCommissionMoney',
          name: '实际需缴纳佣金',
          width: 150,
          formatter: (row, col, cell) => (cell || cell === 0 ? cell.toFixed(2) : ''),
          renderHeader: (h, { column }) => {
            return h(
              'div', [
                h('span', column.label),
                h('el-tooltip', {
                  props: {
                    content: '账单的实际需缴纳佣金=账单中所有单据的实际需缴纳佣金求和。单据享受佣金折扣政策优惠后，对应账单商业实际需给平台缴纳的佣金金额',
                    placement: 'right',
                  },
                }, [
                  h('i', { class: 'el-icon-warning-outline' }),
                ]),
              ],
            );
          },
        },
        {
          index: 'commissionDiscountMoney',
          name: '佣金优惠',
          width: 150,
          formatter: (row, col, cell) => (cell || cell === 0 ? cell.toFixed(2) : ''),
          renderHeader: (h, { column }) => {
            return h(
              'div', [
                h('span', column.label),
                h('el-tooltip', {
                  props: {
                    content: '账单的佣金优惠=账单中所有单据的佣金优惠求和。单据因享受佣金折扣政策而产生的佣金优惠',
                    placement: 'right',
                  },
                }, [
                  h('i', { class: 'el-icon-warning-outline' }),
                ]),
              ],
            );
          },
        },
        {
          index: 'billPaymentStatus',
          name: '账单状态',
          formatter: (row, col, cell) => (cell === 0 ? '未入账' : '已入账')
        },
        {
          index: 'billCreateTime',
          name: '生成时间',
          width: 200,
          formatter: (row, col, cell) =>
            cell ? new Date(cell + 8 * 3600 * 1000).toJSON().substr(0, 19).replace('T', ' ') : ''
        },
        {
          index: 'billPaymentTime',
          name: '入账时间',
          width: 200,
          formatter: (row, col, cell) =>
            cell ? new Date(cell + 8 * 3600 * 1000).toJSON().substr(0, 19).replace('T', ' ') : ''
        }
      ],
      info: {
        invoiceApplyNo: '',
        invoiceStatusDesc: '',
        hireMonths: '',
        failReason: '',
        invoiceTypeDesc: '',
        invoiceNo: '',
        invoiceMoney: '',
        invoiceCode: '',
        lastApplyTime: '',
        billingTime: '',
        invoiceTitle: '',
        bankName: '',
        invoiceAddress: '',
        contactor: '',
        invoicePhone: '',
        contactorPhone: '',
        taxNo: '',
        contactorAddress: '',
        bankAccount: ''
      },
      routerObj: ''
    }
  },
  methods: {
    /**
     * 获取发票索取明细列表
     */
    getList() {
      this.routerObj = this.$route.query
      this.listQuery.invoiceApplyNo = this.routerObj.invoiceApplyNo
      const loading = this.$loading({
        lock: true,
        text: 'Loading',
        spinner: 'el-icon-loading',
        background: 'rgba(255,255,255, 0.8)'
      })
      this.getInfo()
      const { page, pageSize } = this.listQuery
      getInvoiceRecordDetailList({
        pageNum: page,
        pageSize,
        invoiceApplyNo: this.listQuery.invoiceApplyNo
      })
        .then((res) => {
          loading.close()
          if (res.code === '200') {
            const { list, total, pageNum } = res.data
            this.list = list ? list :[]
            this.listQuery = {
              ...this.listQuery,
              total,
              page: pageNum
            }
          } else {
            this.$message.error({ message: res.errorMsg || res.msg, customClass: 'center-msg' })
          }
        })
        .catch(() => {
          loading.close()
        })
    },
    /**
     * 获取发票索取信息
     */
    getInfo() {
      getInvoiceRecordDetailInfo({
        invoiceApplyNo: this.listQuery.invoiceApplyNo
      })
        .then((res) => {
          if (res.code === '200') {
            Object.keys(this.info).forEach((key) => {
              this.info[key] = res.data[key]
            }, this)
          } else {
            this.$message.error({ message: res.errorMsg || res.msg, customClass: 'center-msg' })
          }
        })
        .catch(() => {})
    },
    toBack() {
      this.$router.go(-1);
    }
  },
  filters: {
    formatDate(date) {
      return date ? new Date(date + 8 * 3600 * 1000).toJSON().substr(0, 19).replace('T', ' ') : ''
    }
  },
  created() {
    if (this.$route.query.invoiceApplyNo) {
      this.getList()
    }
  },
  activated() {
    if (this.routerObj && JSON.stringify(this.routerObj) !== JSON.stringify(this.$route.query)) {
      this.getList()
    }
  },
}
</script>

<style lang="scss" scoped>
.invoiceRecord-detail {
  padding: 15px;
  position: relative;
  .el-button {
    padding: 0 12px;
    line-height: 30px;
    position: absolute;
    right: 15px;
  }
  h4 {
    margin: 0 0 15px 0;
    font-size: 14px;
    font-family: PingFangSC, PingFangSC-Medium;
    font-weight: 500;
    color: #333333;
    &:first-child {
      margin-top: 0;
    }
    &:before {
      content: '';
      background-image: linear-gradient(0deg, #1d69c4 0%, #8bbdfc 99%);
      width: 3px;
      height: 13px;
      float: left;
      margin: 3px 7px 3px 0;
    }
  }
  .el-form {
    width: 95%;
    padding: 0 10px;
    .el-form-item {
      margin-bottom: 5px;
      font-size: 12px;
      font-family: PingFangSC, PingFangSC-Regular;
      font-weight: 400;
      color: #333333;
      ::v-deep  .el-form-item__label {
        height: 30px;
        line-height: 30px;
      }
      ::v-deep  .el-form-item__content {
        line-height: 30px;
        width: 354px;
      }
    }
  }
}
</style>
