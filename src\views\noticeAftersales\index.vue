<template>
    <div class="storeVoucher">
      <div class="sticky-tabs">
          <el-tabs v-model="activeName" @tab-click="handleClick">
              <el-tab-pane label="退货须知设置" name="aftersales"/>
              <el-tab-pane label="退货地址管理" name="addressIndex"/>
          </el-tabs>
      </div>
      <aftersales v-show="activeName === 'aftersales'" />
      <div style="width: 100%;" v-if="activeName === 'addressIndex'">
        <iframe :src="iframeUrl" frameborder="0" style="width: 100%;height: 100vh;"></iframe>
      </div>
    </div>
  </template>
  
  <script>
  import aftersales from "@/views/noticeAftersales/afterSales"
  export default {
      name: "exchangeManage",
      components: {
        aftersales,
      },
      created() {
        this.iframeUrl = `${process.env.VUE_APP_BASE_API}/address/index`
        if(this.$route.query.to) {
            this.selectComponents(this.$route.query.to)
        }
      },
      data() {
          return {
              activeName: "aftersales",
              iframeUrl: '',
          }
      },
      methods: {
          handleClick(tab, event) {
              this.$router.replace({
                  path: 'exchangeManage',
                  query: { to: tab.name },
              });
              this.selectComponents(tab.name)
          },
          selectComponents(target) {
              if(target) {
                  this.activeName = target
              }
          }
      },
  }
  </script>
  
  <style>
  .storeVoucher {
      margin-top: 10px;
      padding-left: 10px;
  }
  .sticky-tabs {
    position: sticky;
    top: 0;
    z-index: 1000;
    background: #fff;
    padding: 10px 0;
  }
  </style>