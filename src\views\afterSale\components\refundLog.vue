<template>
  <div>
    <el-dialog
      title="退款审核记录"
      :visible="stateVisible"
      width="60%"
      @close="closeDialog"
    >
      <div>
        <el-table
          :data="tableData"
          border
          style="width: 100%"
        >
          <el-table-column
            label="操作时间"
            align="center"
          >
            <template slot-scope="scope">
              <span>{{ scope.row.refundAuditTime | formatDate }}</span>
            </template>
          </el-table-column>
          <el-table-column>
          <template slot="header">
            <div style="text-align:center">
              操作信息
            </div>
          </template>
            <template slot-scope="scope">
              <div>
                <!-- <span>{{ scope.row.refundChannel }}</span> -->
                <p v-if="scope.row.refundChannelDesc">{{scope.row.refundChannelDesc }}</p>
                <p v-if="scope.row.refundAuditOperateDesc">{{scope.row.refundAuditOperateDesc }}</p>
                <p v-if="scope.row.refundAuditStatusDesc">{{scope.row.refundAuditStatusDesc }}</p>
                <p v-if="scope.row.refundAuditRefuseReason">{{scope.row.refundAuditRefuseReason }}</p>
                <p v-if="scope.row.bankCardInfo">{{scope.row.bankCardInfo }}</p>
              </div>
              <div>
                <!-- <span>{{ scope.row.refundAuditStatusDesc }}</span> -->
                <span class="lookImg" v-if="scope.row.evidenceImages && scope.row.evidenceImages.length > 0" @click="lookImg(scope.row)">查看电汇凭证</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column
            label="操作人"
            align="center"
          >
            <template slot-scope="scope">
              <div>
                <span>{{scope.row.refundAuditor ||''}}</span>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <span slot="footer">
        <el-button
          size="mini"
          @click="closeDialog"
        >关 闭</el-button>
      </span>
    </el-dialog>
    <el-image-viewer
        v-if="showViewer"
        :url-list="srcArr"
        :on-close="closeViewer"
        :z-index="100000"
    />
  </div>
</template>

<script>
import { getListRefundAuditLogs } from "@/api/afterSale/index";
import ElImageViewer from "element-ui/packages/image/src/image-viewer";
import { getHostName } from '@/api/order';
export default {
  name: 'refundLog',
  components: { ElImageViewer },
  data() {
    return {
      tableData: [],
      stateVisible: false,
      showViewer: false,
      srcArr: [],
      hostUrl: ''
    };
  },
  created() {
    this.getHostName()
  },
  methods: {
    async getHostName() {
      const res = await getHostName();
      if (res) {
        this.hostUrl = res;
      }
    },
    getListData(refundOrderId) {
      this.stateVisible = true;
      getListRefundAuditLogs({ refundOrderId:refundOrderId }).then((res) => {
        if (res.code === 0) {
          this.tableData = res.data||[];
        } else {
          this.$message.error({ message: res.message, offset: 100 });
        }
      });
    },
    closeDialog() {
      this.stateVisible = false;
    },
    closeViewer() {
      this.showViewer = false;
    },
    lookImg(data) {
      this.srcArr = [];
      data.evidenceImages.forEach((item) => {
        const itemUrl = item.replace(' ','');
        this.srcArr.push(`${this.hostUrl.hostName}${itemUrl}`)
      });
      console.log(this.srcArr,'this.srcArr');
      //this.srcArr = data.evidenceImages?data.evidenceImages:[];
      this.showViewer = true;
    }
  },
};
</script>

<style scoped lang="scss">
.lookImg{
  padding-left: 15px;
  color: #1d69c4;
  cursor: pointer;
}
.flexBox{
  display: flex;
  align-items: center;
}
::v-deep  .el-dialog__body{
  padding: 10px 20px;
  ::v-deep  .el-table td {
    border-bottom: 1px solid #ebeef5;;
  }
}
::v-deep  .el-dialog__header{
  padding: 10px 20px;
  background: #f9f9f9;
}
::v-deep  .el-dialog__headerbtn{
  top: 15px;
}
::v-deep  .el-dialog__title{
  font-size: 16px;
}
</style>
