<template>
  <div class="boxDiv">
    <el-row>
      <el-form
        :model="supplyInformationVo"
        :rules="supplyInformationVoRules"
        ref="supplyInformationVo"
        label-width="130px"
        class="demo-ruleForm"
        :append-to-body="true"
      >
        <el-row>
          <el-col :span="24" v-if="supplyInformationVo.controlRosterType != 2">
            <el-form-item label="业务商圈:">
              <el-button v-if="!disabled" plain @click="selectBusinessCircle">选择商圈</el-button>
              <div class="div-info">
                <span v-if="supplyInformationVo.busAreaConfigName">当前已选：{{supplyInformationVo.busAreaConfigName}}</span>
                <el-button v-if="!disabled && supplyInformationVo.busAreaConfigName" type="text" class="btn-info" @click="checkDetails">查看详情</el-button>
                <el-button v-if="!disabled" type="text" class="btn-info" @click="addBusinessDistrict">新建商圈</el-button>
              </div>
            </el-form-item>
          </el-col>
          <!-- <el-col :span="24">
            <el-form-item label="商品控销:">
              <el-button v-if="!disabled" plain @click="viewAndEditControl">查看/设置商品控销</el-button>
            </el-form-item>
          </el-col> -->
        </el-row>
        <el-row>
          <el-col>
            <el-form-item label="供货对象:" prop="controlUserTypes" v-if="supplyInformationVo.controlRosterType != 2">
              <div class="customerType">
                <el-checkbox
                  :indeterminate="isIndeterminate"
                  v-model="checkAll"
                  :disabled="disabled"
                  @change="handleCheckAllChange"
                  class="checkedall"
                >全选</el-checkbox>
                <el-checkbox-group
                  v-model="supplyInformationVo.controlUserTypes"
                  @change="handleCheckedTypesChange"
                >
                  <el-checkbox
                    v-for="(item,index) in customerTypes"
                    :label="item.id"
                    :disabled="disabled"
                    style="marginBottom:10px;"
                    :key="index"
                  >{{item.name}}</el-checkbox>
                </el-checkbox-group>
              </div>
              <div style='color: red;font-size: 12px;'>
                注意：被勾选的客户类型对应的客户可购买当前商品。未勾选的客户类型对应的客户不可购买当前商品。
              </div>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col>
            <el-form-item label="黑白名单:" prop="controlRosterType">
              <el-radio-group v-model="supplyInformationVo.controlRosterType" :disabled="disabled">
                <el-radio :label="1" @click.native.prevent="clickBlackNameList">黑名单控销组</el-radio>
                <el-radio :label="2" @click.native.prevent="clickWhiteNameList">白名单控销组</el-radio>
              </el-radio-group>
              <div class="div-info">
                <span v-if="supplyInformationVo.controlGroupName && supplyInformationVo.controlRosterType">
                  <span>已选“{{supplyInformationVo.controlGroupName}}”</span>
                  <el-button type="text" class="btn-info" @click="seeMerchantDetail">查看药店明细</el-button>
                </span>
                <el-button v-if="supplyInformationVo.controlRosterType === 1 || supplyInformationVo.controlRosterType === 2" size="small" :disabled="disabled" plain @click="toSelectControlGroups">选择控销组</el-button>
              </div>
              <div style='color: red;font-size: 12px;'>
                <span>
                  商品维度可见性分为三种场景：<br />
                  场景1：未选择黑白名单控销组。则仅商圈区域内指定客户类型可购买此商品。<br />
                  场景2：选择了黑名单控销组。则仅商圈区域内指定客户类型且非黑名单客户可购买此商品。<br />
                  场景3：选择了白名单控销组。则仅白名单的客户可购买此商品。
                </span>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <el-divider />
    </el-row>
    <view-business-circle
      :row="{id:supplyInformationVo.busAreaId}"
      v-if="viewBusinessDialog"
      @onDialogChange="onDialogChange"
    ></view-business-circle>
    <select-business-circle
      v-if="isSelectBusinessCircle"
      :selected.sync="supplyInformationVo.busAreaId"
      :saleType.sync="supplyInformationVo.saleType"
      @onDialogChange="onDialogChange"
      @selectChange="selectChange"
    ></select-business-circle>
    <!-- 查看药店明细 -->
    <ListOfControlGroups
      v-if="showMerchantInfo"
      :control-group-id="supplyInformationVo.controlGroupId"
      @cancelDialog="cancelDialog"
    />
    <!-- 选择控销组 -->
    <SelectControlGroups
      v-if="showSelectControlGroups"
      :show-select-control-groups.sync="showSelectControlGroups"
      :selected-id="supplyInformationVo.controlGroupId"
      @selectGroupChange="selectGroupChange"
    />
  </div>
</template>

<script>
import viewBusinessCircle from '@/views/business-circle/components/viewBusinessCircle'
import selectBusinessCircle from "@/views/product/components/selectBusinessCircle";
import ListOfControlGroups from './listOfControlGroups';
import SelectControlGroups from './selectControlGroups';
import { findUserTypes } from '@/api/product';
export default {
  name: 'SupplyInformation',
  components: { viewBusinessCircle , selectBusinessCircle, ListOfControlGroups, SelectControlGroups },
  props: {
    basicData: {
      type: Object,
      default() {
        return {};
      },
    },
    formModel:{
      type: Object,
      default() {
        return {};
      },
    }
  },
  data() {
    return {
      disabled: false,
      viewBusinessDialog: false, // 查看业务商圈
      isSelectBusinessCircle:false,
      provinceList: [],
      propsData: {},
      supplyInformationVoRules: {
        controlUserTypes: [{ required: true, message: '供货对象不能为空', trigger: 'blur' }],
      },
      supplyInformationVo: {
        busAreaConfigName: '',
        busAreaId: '',
        saleType: '',
        controlRosterType: 0, // 名单类型：0-无，1-黑名单，2-白名单
        controlUserTypes: [], // 供货对象
        controlGroupId: '', // 用户组id
        controlGroupName: '', // 用户组名称
      },
      businessEndTimeDisabled: false,
      showMerchantInfo: false, // 查看药店明细
      showSelectControlGroups: false, // 选择控销组
      isIndeterminate: false,
      checkAll: false,
      customerTypes: [],
    };
  },
  watch: {
    basicData: {
      immediate: true,
      handler(newVale) {
        this.$nextTick(() => {
          this.propsData = JSON.parse(JSON.stringify(newVale));
          this.initData();
        });
      },
    },
    formModel: {
      handler(newVal) {
        var base = JSON.parse(JSON.stringify(newVal));
        this.disabled = base.isEdit == 1;
      },
      immediate: true,
      deep: true,
    },
  },
  mounted() {},
  created() {
    window.sessionStorage.setItem('supplyInformationVoRules',JSON.stringify(this.supplyInformationVoRules))
    this.findUserTypes();
  },
  methods: {
    // 获取用户类型
    findUserTypes() {
      findUserTypes().then((res) => {
        if (res.code == 0) {
          this.customerTypes = res.data;
        } else {
          this.$message({
            message: res.message,
            type: 'warning',
          });
        }
      });
    },
    initData() {
      const that = this;
      this.supplyInformationVo.busAreaConfigName = this.propsData.busAreaConfigName;
      this.supplyInformationVo.busAreaId = this.propsData.busAreaId;
      this.supplyInformationVo.saleType = this.propsData.saleType;
      this.supplyInformationVo.controlGroupId = this.propsData.controlGroupId;
      this.supplyInformationVo.controlGroupName = this.propsData.controlGroupName;
      this.supplyInformationVo.controlRosterType = this.propsData.controlRosterType || 0;
      this.isIndeterminate = false;
      this.checkAll = false;

      const { controlUserTypes } = this.propsData;
      if (controlUserTypes) {
        this.supplyInformationVo.controlUserTypes = controlUserTypes || [];
        this.handleCheckedTypesChange(this.supplyInformationVo.controlUserTypes);
      } else {
        this.supplyInformationVo.controlUserTypes = [];
        this.isIndeterminate = false;
        this.checkAll = false;
      }
    },
    // 选择商圈
    selectBusinessCircle() {
      this.isSelectBusinessCircle = true
    },
     // 查看商圈详情
    checkDetails() {
      this.viewBusinessDialog = true
    },
    onDialogChange() {
      this.viewBusinessDialog = false
      this.isSelectBusinessCircle = false
    },
    selectChange(row) {
      if(row){
        this.supplyInformationVo.busAreaId = row.id;
        this.supplyInformationVo.busAreaConfigName = row.busAreaName;
      }
    },
    addBusinessDistrict() {
      if(this.$store.state.permission.menuGray == 1) {
        window.openTab('/shopBusinessManage?to=businessCircle');
      }else {
        window.openTab('/businessCircle');
      }
      this.$emit("supplyDialogClose");
    },
    viewAndEditControl(code){
      console.log(this.propsData);
      window.openTab('/controlIndex', { barcode: this.propsData.barcode });
    },
    handleCheckAllChange(val) {
      const checkAllId = this.customerTypes.map((item => item.id));
      this.supplyInformationVo.controlUserTypes = val ? checkAllId : [];
      this.isIndeterminate = false;
    },
    handleCheckedTypesChange(value) {
      const checkedCount = value.length;
      this.checkAll = checkedCount === this.customerTypes.length;
      this.isIndeterminate = checkedCount > 0 && checkedCount < this.customerTypes.length;
    },
    clickBlackNameList(e) {
      this.supplyInformationVo.controlGroupId = '';
      this.supplyInformationVo.controlGroupName = '';
      if (this.supplyInformationVo.controlRosterType == 1) {
        this.supplyInformationVo.controlRosterType = 0;
      } else {
        this.supplyInformationVo.controlRosterType = 1;
        this.supplyInformationVoRules.controlUserTypes.forEach((itemRules) => {
          if (Object.prototype.hasOwnProperty.call(itemRules, 'required')) {
            itemRules.required = true;
          }
        });
      }
    },
    clickWhiteNameList(e) {
      if (this.supplyInformationVo.controlRosterType == 2) {
        this.supplyInformationVo.controlRosterType = 0;
      } else {
        this.$confirm('选择白名单控销组后，仅白名单控销组内的客户可以购买此商品，确定使用白名单控销组吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
        })
          .then(() => {
            this.supplyInformationVo.controlGroupId = '';
            this.supplyInformationVo.controlGroupName = '';
            this.supplyInformationVo.controlRosterType = 2;
            this.supplyInformationVoRules.controlUserTypes.forEach((itemRules) => {
              if (Object.prototype.hasOwnProperty.call(itemRules, 'required')) {
                itemRules.required = false;
              }
            });
          })
          .catch(() => {});
        }
    },
    seeMerchantDetail() {
      this.showMerchantInfo = true;
    },
    selectGroupChange(row) {
      this.supplyInformationVo.controlGroupId = row.id; // 用户组id
      this.supplyInformationVo.controlGroupName = row.name;
    },
    cancelDialog() {
      this.showMerchantInfo = false;
      this.showSelectControlGroups = false;
    },
    toSelectControlGroups() {
      this.showSelectControlGroups = true;
    }
  }
};
</script>

<style scoped lang="scss">
.el-button {
  padding: 8px 20px;
}
.el-button.is-circle {
  padding: 7px;
  border: none;
}
.el-button {
  padding: 0 12px;
  line-height: 30px;
  &.is-plain {
    color: #4183d5;
    border: 1px solid #4183d5;
    border-radius: 4px;
  }
}
.boxDiv {
  width: 100%;
  height: 100%;
  overflow-y: auto;
  padding: 0 0 0 10px;

  ::v-deep   .el-form {
    width: 100%;

    .el-select {
      margin-right: 14px;
    }

    .el-form-item__label {
      font-size: 12px;
      line-height: 30px;
    }

    .el-form-item__content {
      line-height: 30px;
    }

    .el-input__inner {
      line-height: 30px;
      height: 30px;
      font-size: 12px;
    }

    .el-input__icon {
      line-height: 30px;
    }
  }
  .div-info {
    font-size: 12px;
    font-family: PingFangSC, PingFangSC-Regular;
    font-weight: 400;
    text-align: left;
    color: #333333;
    .btn-info {
      font-size: 12px;
      font-family: PingFangSC, PingFangSC-Regular;
      font-weight: 400;
      text-decoration: underline;
      text-align: left;
      color: #4184d5;
    }
  }
}

.boxDiv::-webkit-scrollbar {
  width: 0 !important;
}
.customerType {
  padding-left: 10px;
  border: 1px solid #eeeeee;
  border-radius: 4px;
  max-height: 260px;
  overflow-y: auto;
  ::v-deep  .el-checkbox {
    width: 14%;
    margin-left: 10px;
  }
  ::v-deep  .checkedall {
    width: 100%;
    padding: 10px;
    margin-left: 0;
    margin-bottom: 10px;
  }
  ::v-deep  .el-checkbox__input.is-checked + .el-checkbox__label {
    color: #333333;
  }
}
</style>
