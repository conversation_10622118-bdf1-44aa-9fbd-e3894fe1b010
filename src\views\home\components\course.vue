<template>
          <div style="position: absolute;">
            123
            <video
        autoplay
        muted
        controls
        @ended="onVideoEnded"
        @play="onVideoPlay"
        @pause="onVideoPause"
        :src="detailData.content"
        ref="videoElement"
        @loadedmetadata="onVideoLoadedMetadata"
        @timeupdate="onTimeUpdate"
        class="course-video"
      />
          </div>
</template>

<script>
export default {
    data(){
      return{
        videoDialog:false,
      }
    },
    methods:{
      onVideoEnded(event) {
      console.log('%c [ data ]-109', 'font-size:13px; background:pink; color:#bf2c9f;', event)
    },
    onVideoPlay(event) {
      console.log('%c [ data ]-113', 'font-size:13px; background:pink; color:#bf2c9f;', event)
    },
    onVideoPause(event) {
      console.log('%c [ data ]-116', 'font-size:13px; background:pink; color:#bf2c9f;', event)
    },
    onVideoLoadedMetadata(event) {
      if (videoElement.value) {
        const duration = videoElement.value.duration
        console.log(`视频长度: ${duration} 秒`)
      }
    },
    onTimeUpdate(event) {
      // event.target 是触发事件的 video 元素
      const playedSeconds = event.target.currentTime
      console.log(`已播放: ${playedSeconds} 秒`)
    },
    //设置播放点，续播
    playBySeconds(num) {
      if (num && document.getElementById('videoPlayer')) {
        let myVideo = document.getElementById('videoPlayer')
        myVideo.play()
        myVideo.currentTime = num
      }
    },
    }
}
</script>

<style>

</style>
