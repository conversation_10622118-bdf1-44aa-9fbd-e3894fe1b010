<template>
  <div class="invoiceRecord-box">
    <el-form ref="listQuery" :model="listQuery" :inline="true" label-width="80px" size="small">
      <el-form-item label="开票状态" prop="invoiceStatus">
        <el-select v-model="listQuery.invoiceStatus">
          <el-option label="全部" value />
          <el-option label="开票中" :value="0" />
          <el-option label="开票成功" :value="1" />
          <el-option label="开票失败" :value="2" />
          <el-option label="发票作废" :value="3" />
        </el-select>
      </el-form-item>
      <el-form-item class="btn-item">
        <el-button type="primary" @click="getList(listQuery, true)">查询</el-button>
      </el-form-item>
    </el-form>
    <xyy-table
      :data="list"
      :list-query="listQuery"
      :col="col"
      :addition="addition"
      :has-index="true"
      @get-data="getList"
      @addition-click="additionClick"
    >
      <template
        slot="operation"
        slot-scope="{col}"
      >
        <el-table-column
          :key="col.index"
          :prop="col.index"
          :label="col.name"
          :width="col.width"
          fixed="right"
        >
          <template slot-scope="scope">
            <el-button size="small" type="text" @click="operationClick(0,scope.row)">查看明细</el-button>
            <el-button
              v-permission="['settle_invoice_againApply']"
              v-if="scope.row.invoiceStatus == 2 || scope.row.invoiceStatus == 3"
              size="small"
              type="text"
              @click="operationClick(1,scope.row)"
            >重新申请开票</el-button>
            <el-button
              v-if="(scope.row.invoiceStatus == 1 && scope.row.invoiceType != 1)"
              size="small"
              type="text"
              @click="operationClick(2,scope.row)"
            >查看发票</el-button>
          </template>
        </el-table-column>
      </template>
    </xyy-table>
    <el-dialog
      :visible.sync="invoiceTipBox"
      custom-class="invoice-dialog"
      top="0"
      :title="invoiceTipTitle"
    >
      <p>{{ invoiceTip }}</p>
      <div slot="footer">
        <el-button type="primary" @click="invoiceTipBox = false">确定</el-button>
      </div>
    </el-dialog>
    <el-dialog
      title="发票链接"
      :visible.sync="dialogVisible"
      custom-class="invoice-dialog"
      top="0"
      width="600px"
    >
      <div v-if="historyList.length > 0">
        <div style="font-size: 12px;color: #999999;padding-bottom: 10px">由于浏览器下载安全限制，需您手动复制链接下载发票</div>
        <div v-for="(item, index) in historyList" :key="index" style="margin-bottom: 10px">
          <div class="dis-div">
            <span style="width: 80%">{{ item }}</span>
            <span
              style="color: #4183d5; text-align: center; cursor: pointer"
              @click="util.copyUrl(item)"
            >复制</span>
          </div>
        </div>
      </div>
      <div v-else>暂无发票信息</div>
    </el-dialog>
  </div>
</template>

<script>
import { getInvoiceRecordList, reApplyInvoice } from '@/api/settlement/invoiceApply';

export default {
  name: 'record',
  data() {
    return {
      activeTab: 'record',
      listQuery: {
        pageSize: 10,
        page: 1,
        total: 0,
        invoiceStatus: '',
      },
      list: [],
      col: [
        // {
        //   index: 'index',
        //   name: '序号',
        //   width: 100,
        // },
        {
          index: 'invoiceApplyNo',
          name: '申请发票单号',
          width: 200,
        },
        {
          index: 'hireMonths',
          name: '收入月份',
          width: 150,
        },
        {
          index: 'invoiceTitle',
          name: '发票抬头',
          width: 150,
        },
        {
          index: 'invoiceTypeDesc',
          name: '发票类型',
          width: 200,
        },
        {
          index: 'invoiceMoney',
          name: '开票金额(元)',
          width: 150,
          formatter: (row, col, cell) => (cell || cell === 0 ? cell.toFixed(2) : ''),
        },
        {
          index: 'lastApplyTime',
          name: '申请时间',
          width: 200,
          formatter: (row, col, cell) => (cell
            ? new Date(cell + 8 * 3600 * 1000)
              .toJSON()
              .substr(0, 19)
              .replace('T', ' ')
            : ''),
        },
        {
          index: 'billingTime',
          name: '开票时间',
          width: 200,
          formatter: (row, col, cell) => (cell
            ? new Date(cell + 8 * 3600 * 1000)
              .toJSON()
              .substr(0, 19)
              .replace('T', ' ')
            : ''),
        },
        {
          index: 'invoiceStatusDesc',
          name: '开票状态',
          width: 250,
          addition: true,
        },
        {
          index: 'invoiceCode',
          name: '发票代码',
          width: 150,
          ellipsis: true,
        },
        {
          index: 'invoiceNo',
          name: '发票号码',
          width: 150,
          ellipsis: true,
        },
        {
          index: 'logisticsCompanyName',
          name: '物流公司',
          width: 150,
          ellipsis: true,
        },
        {
          index: 'trackingNo',
          name: '运单号',
          width: 150,
          ellipsis: true,
        },
        {
          index: 'operation',
          name: '操作',
          width: 210,
          slot: true,
        },
      ],
      // operation: [
      //   {
      //     name: '查看明细',
      //     type: 0,
      //   },
      //   {
      //     name: '重新申请开票',
      //     type: 1,
      //     condition: true,
      //     conditions: (row) => {
      //       if (row.invoiceStatus === 2 || row.invoiceStatus === 3) {
      //         return true;
      //       }
      //       return false;
      //     },
      //   },
      //   {
      //     name: '查看发票',
      //     type: 2,
      //     condition: true,
      //     conditions: (row) => {
      //       if (row.invoiceStatus === 1 && row.invoiceType === 0) {
      //         return true;
      //       }
      //       return false;
      //     },
      //   },
      // ],
      addition: [
        {
          name: '开票中',
          type: 0,
          condition: false,
          canClick: true,
          canClicks: (row) => {
            if (row.invoiceStatus === 0) {
              return true;
            }
          },
        },
        {
          name: '开票成功',
          type: 2,
          canClick: true,
          condition: false,
          canClicks: (row) => {
            if (row.invoiceStatus === 1) {
              return true;
            }
          },
        },
        {
          name: '开票失败',
          type: 3,
          condition: false,
          canClick: true,
          canClicks: (row) => {
            if (row.invoiceStatus === 2) {
              return true;
            }
          },
        },
        {
          name: '发票作废',
          type: 4,
          condition: false,
          canClick: true,
          canClicks: (row) => {
            if (row.invoiceStatus === 3) {
              return true;
            }
          },
        },
        {
          name: '(查看失败原因)',
          type: 1,
          condition: true,
          conditions: (row) => {
            if (row.invoiceStatus === 2) {
              return true;
            }
            return false;
          },
        },
      ],
      invoiceTipBox: false,
      invoiceTipTitle: '开票失败原因',
      invoiceTip: '开票信息提示',
      historyList: [],
      dialogVisible: false,
    };
  },
  created() {
    this.getList(this.listQuery, true);
  },
  methods: {
    // 获取发票记录列表
    getList(listQuery, reset) {
      const loading = this.$loading({
        lock: true,
        text: 'Loading',
        spinner: 'el-icon-loading',
        background: 'rgba(255,255,255, 0.8)',
      });
      const { page, pageSize, invoiceStatus } = listQuery;
      getInvoiceRecordList({
        pageNum: reset ? 1 : page,
        pageSize,
        invoiceStatus,
      })
        .then((res) => {
          loading.close();
          if (res.code === '200') {
            const { list, total, pageNum } = res.data;
            this.list = list || [];
            this.listQuery = {
              ...this.listQuery,
              total,
              page: pageNum,
            };
          } else {
            this.$message.error({
              message: res.errorMsg || res.msg,
              customClass: 'center-msg',
            });
          }
        })
        .catch(() => {
          loading.close();
        });
    },
    // 申请开票
    invoiceSure(invoiceApplyNo) {
      reApplyInvoice({ invoiceApplyNo })
        .then((res) => {
          if (res.code === '200') {
            this.invoiceTipBox = true;
            this.invoiceTipTitle = '提示';
            this.invoiceTip = '开票成功';
            this.getList(this.listQuery, true);
          } else {
            this.$message.error({
              message: res.errorMsg || res.msg,
              customClass: 'center-msg',
            });
          }
        })
        .catch(() => {});
    },
    /**
     * 重置数据
     */
    reset() {
      this.$refs.listQuery.resetFields();
      this.getList(this.listQuery, true);
    },
    /**
     * 选项操作
     */
    operationClick(type, row) {
      if (type === 0) {
        // 查看明细
        this.$router.push(`/invoiceRecordDetail?invoiceApplyNo=${row.invoiceApplyNo}`);
      }
      if (type === 1) {
        // 重新申请开票
        this.invoiceSure(row.invoiceApplyNo);
      }
      if (type === 2) {
        // 查看发票
        const urlArr = row.invoiceUrl.split(',');
        const isChrome =  navigator.userAgent.indexOf('Chrome') > -1;
        if(isChrome){
          this.historyList = urlArr;
          this.dialogVisible = true;
        }else{
          urlArr.forEach((element) => {
            this.openwin(element)
          });
        }
      }
    },
    openwin(url) {
      const a = document.createElement('a');
      a.setAttribute('href', url);
      a.setAttribute('target', '_blank');
      a.setAttribute('id', url);
      document.body.appendChild(a);
      a.click();
    },
    additionClick(type, row) {
      this.invoiceTipBox = true;
      this.invoiceTip = row.failReason;
    },
  },
};
</script>

<style lang="scss" scoped>
.invoiceRecord-box {
  padding: 0 15px 15px;
  .price-box {
    font-size: 16px;
    font-family: PingFangSC, PingFangSC-Semibold;
    font-weight: 600;
    color: #303133;
    line-height: 40px;
    overflow: hidden;
    &.mb15 {
      margin-bottom: 15px;
    }
    span {
      font-size: 28px;
    }
    .el-button {
      padding: 0 12px;
      line-height: 30px;
    }
  }
  > p {
    padding: 8px;
    background: #f9f9f9;
    border-radius: 2px;
    font-size: 14px;
    font-family: PingFangSC, PingFangSC-Regular;
    font-weight: 400;
    color: #666666;
    margin: 15px 0 25px;
  }
}
</style>
<style lang="scss">
.dis-div {
  display: flex;
  justify-content: space-between;
}
.el-dialog.invoice-dialog {
  width: 400px;
  top: 50%;
  transform: translateY(-50%);
  .el-dialog__header {
    padding: 0 20px;
    line-height: 50px;
    .el-dialog__headerbtn {
      top: 17px;
    }
  }
  .el-dialog__body {
    box-sizing: border-box;
    padding: 0 20px 10px 20px;
    max-height: 400px;
    overflow-y: auto;
  }
  .el-button {
    padding: 0 20px;
    line-height: 30px;
    height: 30px;
  }
}
</style>
