<!--created by <PERSON><PERSON><PERSON><PERSON><PERSON> in 2021/3/13-->
<template>
  <div class="con-title">
    <span class="line"></span>
    <span>{{ title }}</span>
  </div>
</template>

<script>
export default {
  name: 'part-title',
  props: {
    title: String
  }
}
</script>

<style lang="scss" scoped>
.con-title {
  color: #000000;
  font-size: 14px;
  height: 38px;
  line-height: 38px;
}

.con-title span {
  display: inline-block;
  vertical-align: middle;
  font-weight: bold;
}

.con-title .line {
  width: 3px;
  height: 13px;
  background: linear-gradient(
    360deg,
    rgba(29, 105, 196, 1) 0%,
    rgba(139, 189, 252, 1) 100%
  );
  border-radius: 2px;
  margin-right: 8px;
}
</style>
