<template>
  <el-dialog
    class="m-more-el-dialog"
    title="手动绑定标品"
    width="1300px"
    :visible.sync="mvisible"
    modal-append-to-body
    :close-on-click-modal="false"
  >
    <el-row>
      <el-col :span="6">
        通用名称：{{ row.showName ? row.showName : '-' }}
      </el-col>
      <el-col :span="6">
        规格：{{ row.spec ? row.spec : '-' }}
      </el-col>
      <el-col :span="6">
        批准文号：{{ row.approvalNumber ? row.approvalNumber : '-' }}
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="6">
        生产厂家：{{ row.manufacturer ? row.manufacturer : '-' }}
      </el-col>
      <el-col :span="6">
        产地：{{ row.producer ? row.producer : '-' }}
      </el-col>
      <el-col :span="6">
        条码：{{ row.code ? row.code : '-' }}
      </el-col>
      <el-col :span="6">
        品牌：{{ row.brand ? row.brand : '-' }}
      </el-col>
    </el-row>

    <div class="more-contentBox"><div class="title">查询商品</div></div>

    <div class="topTip">注：您可以通过查询，从标准库中选择商品</div>

    <SearchForm
      ref="searchForm"
      :model="formModel"
      :form-items="formItems"
      :has-open-btn="false"
      @submit="handleFormSubmit"
      @reset="handleFormReset"
    ></SearchForm>

    <div
      v-if="pageDatas && pageDatas.length"
      class="fk-more-el-dialog-imgcontent"
      v-infinite-scroll="loadMore"
      :infinite-scroll-disabled="
        pageDatas.length >= listQuery.total ||
        pageDatas.length >= 200 ||
        loading
      "
      :infinite-scroll-immediate="false"
    >
      <ChooseList
        :prop-data="pageDatas"
        @chooseGoods="chooseGoods"
      ></ChooseList>
    </div>

    <div class="more-contentBox" v-if="pageDatas.length === 0">
      <div class="title">商品信息纠错</div>
    </div>

    <div class="topTip" v-if="pageDatas.length === 0">
      注：未在标准库中找到商品？您可以针对当前已绑定标品信息进行纠错。
      <el-button type="text" @click="gotoErroEditPage()"
        >商品信息纠错</el-button
      >
    </div>

    <span slot="footer">
      <el-button
        v-if="fromType === 'goods-update-alert'"
        size="medium"
        type="primary"
        @click="normalSend"
        >现在进行绑定</el-button
      >
      <el-button
        size="medium"
        style="margin-left: 20px"
        type="primary"
        @click="normalSend"
        v-if="fromType !== 'goods-update-alert'"
        >一键使用标品信息，发布商品</el-button
      >
      <el-button
        size="medium"
        style="margin-left: 20px"
        @click="selfSend"
        v-if="fromType !== 'goods-update-alert'"
        >商家自建，发布商品</el-button
      >
    </span>
  </el-dialog>
</template>

<script>
import ChooseList from '../components/chooseList'
import SearchForm from '@/components/searchForm'
import { searchItem, Deduplication } from '../config'
import { meProduceList, onekeybind, apiConfig} from '@/api/product'

export default {
  components: { ChooseList, SearchForm },
  props: {
    fromType: {
      type: String
    },
    finishto: {
      type: String
    }
  },
  data() {
    return {
      data: undefined,
      mvisible: false,
      loading: false,
      pageDatas: [],
      formItems: searchItem.formItemsIn,
      chooseData: undefined,
      formModel: {
        code: '',
        productName: '',
        manufacturer: '',
        approvalNumber: ''
      },
      row: '',
      listQuery: {
        pageSize: 50,
        pageNum: 1,
        total: 0
      },
      bigImgUrlPrefix: '',
      barcode: undefined
    }
  },
  mounted(){
    this.getbigImgUrlPrefix()
  },
  methods: {
    open(datas, barcode, row) {
      this.row = row;
      this.pageDatas = [];
      this.formModel.productName = datas.productName ? datas.productName : ''
      this.data = datas
      this.barcode = barcode
      this.getList()
      this.mvisible = true
    },
    getbigImgUrlPrefix() {
      apiConfig().then((res) => {
        if (res.data) {
          this.bigImgUrlPrefix = res.data.bigImgUrlPrefix // 商品大图地址前缀
        }
      })
    },
    normalSend() {
      if (!this.chooseData || Object.keys(this.chooseData).length === 0) {
        this.$message.warning('请先选择商品')
        return
      }
      onekeybind({
        barcode: this.barcode ? this.barcode : this.data.barcode,
        standardProductId: this.chooseData.standardProductId
      }).then((res) => {
        if (res.code === 0) {
          this.$message.success('绑定成功')
          this.mvisible = false
          this.$emit('finish');
        } else {
          this.$message.error(res.message)
        }
      })
    },
    selfSend() {
      this.$emit('selfSend', this.data)
    },
    chooseGoods(data) {
      this.chooseData = data
    },
    gotoErroEditPage() {
      this.mvisible = false
      const path = '/product/errorEdit'
        const obj = {
          productId: this.data.standardProductId,
          from: 'detailsEdit',
          verify: true,
          source: this.data.source,
          firstCategory: this.data.firstCategory,
          barCode: this.barcode
        }
        window.openTab(path, obj)
    },
    loadingFun() {
      const loading = this.$loading({
        lock: true,
        text: 'Loading',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      return loading
    },
    handleFormSubmit() {
      this.checkedName = ''
      this.selectLevelCategory = ''
      this.itemData = {}
      this.isSubmit = true
      this.isDisable = false
      this.listQuery = {
        pageNum: 1,
        pageSize: 50,
        total: 0
      }
      this.pageDatas = []
      this.getList()
    },
    handleFormReset(obj) {
      this.formModel = obj
    },
    getList() {
      const params = { ...this.formModel }
      const { pageSize, pageNum } = this.listQuery
      params.pageSize = pageSize
      params.pageNum = pageNum
      const load = this.loadingFun()
      this.loading = true
      meProduceList(params).then((res) => {
        load.close()
        this.loading = false
        if (res.code === 0) {
          res.data.list = res.data.list.map(item => {
            item.showName = item.showName?item.showName:item.commonName;
            return item;
          })
          this.pageDatas.push(...(res.data.list ? res.data.list : []))
          this.pageDatas = Deduplication(this.pageDatas);
          this.listQuery.total = res.data.total ? res.data.total : 0
          this.isShow = true
        } else {
          this.$message.error(res.message)
        }
      })
    },
    // 加载更多
    loadMore() {
      this.listQuery.pageNum++
      this.getList()
    }
  }
}
</script>

<style scoped>
.goods-check-alert-conten {
  padding: 0 10px;
}

.goods-check-alert-conten-title {
  width: 100px;
}

.goods-check-alert-conten-row {
  display: flex;
  flex-direction: row;
  margin-left: 20px;
}

.goods-check-alert-conten-col {
  margin-top: 15px;
  display: flex;
  flex-direction: row;
}
.line-row {
  display: flex;
  flex-direction: row;
}
.red-content {
  color: red;
}
.fk-more-el-dialog-imgcontent {
  height: 300px;
  overflow: auto;
}
</style>

<style lang="scss" scoped>
.more-contentBox {
  //height: 100%;
  padding: 16px 0px;
  background: #fff;

  .title {
    font-size: 16px;
    font-weight: 500;
    text-align: left;
    color: #000000;
    line-height: 14px;
    margin-bottom: 10px;
  }

  .title:before {
    content: '';
    display: inline-block;
    width: 3px;
    height: 16px;
    background: linear-gradient(360deg, #1d69c4 1%, #8bbdfc 99%);
    border-radius: 2px;
    margin-right: 8px;
    vertical-align: middle;
  }
}

.topTip {
  height: 36px;
  background: rgba(255, 180, 0, 0.05);
  border-radius: 4px;
  margin-bottom: 15px;
  padding-left: 5px;
  font-size: 12px;
  font-family: PingFangSC, PingFangSC-Regular;
  font-weight: 400;
  text-align: left;
  color: #ff9500;
  line-height: 36px;
}
</style>
