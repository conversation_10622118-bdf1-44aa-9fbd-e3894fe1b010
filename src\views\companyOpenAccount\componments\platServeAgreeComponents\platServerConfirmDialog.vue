<template>
  <el-dialog
    class="flat-server-dialog"
    title="平台服务协议信息确认"
    :visible.sync="isShow"
    v-if="isShow"
    width="35%"
    :before-close="onCancel"
    >
    <div class="flat-server-info-confirm" v-loading="isConfirmLoading">
      <div class="flat-server-info-tip">
        <p>以下信息将被带入到平台服务协议中，请补充空缺信息并仔细核对所有信息！</p>
        <p>如有信息错误，请及时联系您的招商经理沟通处理。信息确认无误后可点击下方的【信息已确认】进行平台服务协议的提交；</p>
        <p>平台服务协议签署流程：商家确认——平台审核——商家签署——平台签署。</p>
      </div>
      <div class="flat-server-info-business">
        <el-row type="flex" align="middle">
          <span class="sign" />
          <div class="info-bus-title">企业基本信息</div>
        </el-row>
        <div class="info-bus-form">
          <el-form
            label-width="130px"
            size="small"
            label-position="right"
            :rules="platDialogRules"
            :model="businessForm"
            ref="businessForm"
          >
            <el-form-item
              ref="partyName"
              label="乙方（商家）:"
              class="width50"
              prop="partyName"
            >
              <el-input
                v-model.trim="businessForm.partyName"
                type="text"
                maxlength="200"
                placeholder="请输入乙方（商家）名称"
              />
            </el-form-item>
            <el-form-item
              ref="addr"
              label="企业注册地址:"
              class="width50"
              prop="addr"
            >
              <el-input
                v-model.trim="businessForm.addr"
                maxlength="500"
                type="text"
                placeholder="请输入注册地址"
              />
            </el-form-item>
            <el-form-item
              ref="corporat"
              label="法定代表人:"
              class="width50"
              prop="corporat"
            >
              <el-input
                v-model.trim="businessForm.corporat"
                type="text"
                maxlength="20"
                placeholder="请输入法定代表人"
              />
            </el-form-item>
            <el-form-item
              ref="contactsName"
              label="企业联系人:"
              class="width50"
              prop="contactsName"
            >
              <el-input
                v-model.trim="businessForm.contactsName"
                type="text"
                maxlength="20"
                placeholder="请输入企业联系人"
              />
            </el-form-item>
            <el-form-item
              ref="contactsPhone"
              label="联系人手机号:"
              class="width50"
              prop="contactsPhone"
            >
              <el-input
                v-model.trim="businessForm.contactsPhone"
                type="Number"
                maxlength="11"
                placeholder="请输入联系人手机号"
              />
            </el-form-item>
          </el-form>
        </div>
      </div>
      <div class="flat-server-info-server">
        <el-row type="flex" align="middle">
          <span class="sign" />
          <div class="info-bus-title">待缴纳保证金及技术服务费费率</div>
        </el-row>
        <div class="info-server-form">
          <el-form
            label-width="130px"
            size="small"
            label-position="right"
          >
            <el-form-item
              ref="money"
              label="保证金金额:"
              class="width50"
              prop="money"
            >
              <el-input
                v-model.trim="businessForm.money"
                disabled
                type="text"
              />
            </el-form-item>
            <el-form-item
              label="技术服务费费率:"
              class="width50"
            >
              <el-table
                :data="businessForm.technicalList"
                border
                style="width: 100%">
                <el-table-column
                  prop="categoryName"
                  label="商品一级类目"
                >
                </el-table-column>
                <el-table-column
                  prop="rate"
                  label="技术服务费率"
                >
                </el-table-column>
              </el-table>
            </el-form-item>
          </el-form>
        </div>
      </div>
      <div class="flat-server-info-account-info">
        <el-row type="flex" align="middle">
          <span class="sign" />
          <div class="info-bus-title">企业结算收款账户信息</div>
        </el-row>
        <div class="account-info-form">
          <p class="account-info-form-tips">
            *实际收款账户信息，以您在【企业开户】中的结算信息为准
          </p>
          <el-form
            label-width="130px"
            size="small"
            label-position="right"
            :rules="platTwoDialogRules"
            :model="businessForm"
            ref="businessTwoForm"
          >
            <el-form-item
              ref="companyName"
              label="公司名称:"
              class="width50"
              prop="companyName"
            >
              <el-input
                v-model.trim="businessForm.companyName"
                type="text"
                maxlength="200"
                placeholder="请输入公司名称"
              />
            </el-form-item>
            <el-form-item
              ref="bankName"
              label="开户银行:"
              class="width50"
              prop="bankName"
            >
              <el-input
                v-model.trim="businessForm.bankName"
                type="text"
                maxlength="200"
                placeholder="请输入开户银行"
              />
            </el-form-item>
            <el-form-item
              ref="bankAccount"
              label="银行账户:"
              class="width50"
              prop="bankAccount"
            >
              <el-input
                v-model.trim="businessForm.bankAccount"
                maxlength="50"
                type="text"
                placeholder="请输入银行账户"
              />
            </el-form-item>
          </el-form>
        </div>
      </div>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button @click="onCancel">稍后确认</el-button>
      <el-button type="primary" @click="onConfirm">信息已确认</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { platServerQueryBusiness, submitAgreement } from '@/api/platformServer';
export default {
  props: {
    isShow: Boolean,

  },
  data() {
    return {
      isConfirmLoading: false,
      businessForm: {
        partyName: '',
        addr: '',
        corporat: '',
        contactsName: '',
        contactsPhone: '',
        money: '',
        technicalList: [],
        companyName: '',
        bankName: '',
        bankAccount: '',
      },
      platDialogRules: {
        partyName: [
          { required: true, message: '请输入企业名称', trigger: 'blur' },
        ],
        addr: [
          { required: true, message: '请输入企业注册地址', trigger: 'blur' },
        ],
        corporat: [
          { required: true, message: '请输入企业法人姓名', trigger: 'blur' },
        ],
        contactsName: [
          { required: true, message: '请输入企业的常用联系人姓名', trigger: 'blur' },
        ],
        contactsPhone: [
          { required: true, message: '请输入企业的常用联系人手机号', trigger: 'blur' },
          { min: 11, max: 11, message: '请输入11位数字', trigger: 'blur' },
        ],
      },
      platTwoDialogRules: {
        companyName: [
          { required: true, message: '请输入公司名称', trigger: 'blur' },
        ],
        bankName: [
          { required: true, message: '请输入开户银行及支行', trigger: 'blur' },
        ],
        bankAccount: [
          { required: true, message: '请输入对公银行卡号', trigger: 'blur' },
        ],
      }
    }
  },
  mounted() {
    this.getData();
  },
  methods: {
    getData() {
      platServerQueryBusiness().then(res => {
        console.log(res, "res");
        if (res.code === 0) {
          this.businessForm = res.result;
        }
      })
    },
    onCancel() {
      this.$emit("onCancel")
    },
    onConfirm() {
      this.$refs.businessForm.validate((valid) => {
        this.$refs.businessTwoForm.validate((valid1) => {
          if (valid && valid1) {
            this.isConfirmLoading = true;
            submitAgreement(this.businessForm).then(res => {
              if(res && res.code == 0) {
                this.$message.success('确认成功!');
                this.onCancel();
                this.$emit("onGetList");
              } else {
                this.$message.error(res.msg);
              }
              this.isConfirmLoading = false;
            })
          }
        })
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.flat-server-dialog {
  .flat-server-info-confirm {
    .flat-server-info-tip {
      p {
        font-family: PingFangSC-Regular;
        font-weight: 400;
        font-size: 14px;
        color: #F5222D;
        letter-spacing: 0;
        text-align: justify;
        margin: 0;
        margin-bottom: 5px;
      }
    }
    .flat-server-info-business {
      margin-top: 20px;
      
    }
    .sign {
      display: inline-table;
      width: 3px;
      height: 13px;
      background: linear-gradient(360deg, #1d69c4 1%, #8bbdfc 99%);
      border-radius: 2px;
      margin-right: 8px;
    }
    .info-bus-title {
      font-family: PingFangSC-Medium;
      font-weight: 500;
      font-size: 14px;
      color: #333333;
      letter-spacing: 0;
    }
    .flat-server-info-server {
      .info-server-form {
        margin-top: 10px;
      }
    }
    .flat-server-info-account-info {
      .account-info-form-tips {
        margin-left: 10px;
        font-family: PingFangSC-Regular;
        font-weight: 400;
        font-size: 12px;
        margin-bottom: 20px;
      }
    }
  }
}
</style>
<style lang="scss">
.info-server-form {
  .el-table {
    .el-table__header {
      .el-table__cell {
        padding: 0;
      }
      th {
        background: #F9F9F9;
      }
      .cell {
        height: 32px;
        line-height: 32px;
        font-family: PingFangSC-Medium;
        font-weight: 500;
        font-size: 12px;
        color: #000000;
      }
    }
    .el-table__body {
      .el-table__cell {
        padding: 0;
      }
      .el-table__row:nth-child(2n+2) {
        background: #F8F8F8;
      }
      .cell {
        height: 24px;
        line-height: 24px;
        font-weight: 400;
        font-size: 12px;
        color: #606266;
      }
    }
  }
}
</style>