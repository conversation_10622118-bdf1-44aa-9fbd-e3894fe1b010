import { useStore } from 'vuex';
import Vue from 'vue';

/**
 * 埋点主方法
 * @param {事件名称} eventName
 * @param {埋点参数} params
 * @param {启用状态} status
 * @param {回调函数} callback
 */
const tracking = function (
  eventName,
  params = {},
  status = true,
  callback = () => { },
) {
  // console.log(12121212121, params);
  if (status) {
    window.webSdk.track(eventName, params, callback);
  }
};
/**
 * 设置用户id
 * @param {String} id
 */
export const trackingIdentify = (id) => {
  window.webSdk.identify(id);
  window.webSdk.orgId = id;
};

/**
 * h5 行为触发 埋点
 */
export const actionTracking = (eventName, params, status = true, callback) => {
  if (!params) { params = {}; }
  params.time = new Date().getTime();
  params.org_id = Vue.prototype.orgId;
  console.log(eventName, params);
  params.url = location.href;

  tracking(eventName, params, status, callback);
};
