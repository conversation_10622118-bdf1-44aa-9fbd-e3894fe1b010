<template>
  <div>
    <el-dialog
      class="fk-el-dialog"
      title="当前商品已经匹配平台信息，请确认是否应用平台商品信息"
      width="800px"
      :visible.sync="visible"
      :modal-append-to-body="false"
      :close-on-click-modal="false"
    >
      <div class="goods-check-alert-conten">
        <el-row>
          <el-col
            v-for="(item, index) in pageDatas"
            :key="item.key"
            :span="item.row"
          >
            <div class="goods-check-alert-conten-col">
              <div class="goods-check-alert-conten-title">
                {{ item.name }}
              </div>
              <div class="goods-check-alert-conten-row">
                <div v-if="Object.keys(item.data).length > 1">
                  <div class="line-row">
                    <div>商家:</div>
                    <div
                      v-if="item.key !== 'imagesList'"
                      style="margin-left: 5px"
                      :class="
                        item.data.pt === item.data.sj ? '' : 'red-content'
                      "
                    >
                      {{ item.data.sj ? item.data.sj : '-' }}
                    </div>
                    <div v-else>
                      <el-image
                        v-if="item.data.sj && item.data.sj.length > 0"
                        :src="showImage(item.data.sj[0])"
                        style="width: 80px; height: 80px; margin-left: 10px"
                      >
                        <div slot="placeholder" class="image-slot">
                          加载中<span class="dot">...</span>
                        </div>
                      </el-image>
                    </div>
                  </div>
                  <div class="line-row">
                    <div>平台:</div>
                    <div
                      v-if="item.key !== 'imagesList'"
                      style="margin-left: 5px"
                      :class="
                        item.data.pt === item.data.sj ? '' : 'red-content'
                      "
                    >
                      {{ item.data.pt ? item.data.pt : '-' }}
                    </div>
                    <div v-else>
                      <el-image
                        v-if="item.data.pt"
                        :src="showImage(item.data.pt)"
                        style="width: 80px; height: 80px; margin-left: 10px"
                      >
                        <div slot="placeholder" class="image-slot">
                          加载中<span class="dot">...</span>
                        </div>
                      </el-image>
                    </div>
                  </div>
                </div>
                <div v-else>
                  {{ item.data.pt }}
                </div>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
      <div class="red-content" style="margin-top: 20px">
        提示：<br />
        1、建议您检查商品实物是否与当前展示信息相符，若不符合，可一键使用标品信息。一键使用后商品可操作商家;<br />2、商品自建发布商品，需平台审核通过后才能上架售卖;
      </div>
      <span slot="footer">
        <el-button
          size="medium"
          style="margin-left: 20px"
          type="primary"
          v-if="type !== 'editOrDetail'"
          @click="normalSend"
          >一键使用标品信息，发布商品</el-button
        >
        <el-button
          size="medium"
          style="margin-left: 20px"
          @click="selfSend()"
          v-if="type !== 'editOrDetail'"
          >商家自建，发布商品</el-button
        >
        <el-button
          size="medium"
          style="margin-left: 20px"
          type="primary"
          @click="useGoodsDetai()"
          v-if="type === 'editOrDetail'"
          >使用当前标品信息更新商品</el-button
        >

        <el-button
          size="medium"
          style="margin-left: 20px"
          @click="useOtherGoods()"
          v-if="type === 'editOrDetail'"
          >重新绑定其他标品</el-button
        >

        <!-- <el-button
          size="medium"
          style="margin-left: 20px"
          @click="jumpErrorEditPage()"
          >标品信息有误进行纠错</el-button
        > -->
      </span>
    </el-dialog>

    <goodsMoreCheckAlert
      ref="goodsMoreCheckAlert"
      :fromType="'goods-update-alert'"
      @finish="moreFinish"

    ></goodsMoreCheckAlert>
  </div>
</template>

<script>
import { apiConfig, onekeybind } from '@/api/product'
import goodsMoreCheckAlert from './goodsMoreCheckAlert.vue'
export default {
  components:{goodsMoreCheckAlert},
  data() {
    return {
      visible: false,
      pageDatas: [],
      oldData: undefined,
      data: undefined,
      type: undefined,
      bigImgUrlPrefix: ''
    }
  },
  created() {
    apiConfig().then((res) => {
      if (res.data) {
        this.bigImgUrlPrefix = res.data.bigImgUrlPrefix // 商品大图地址前缀
      }
    })
  },
  methods: {
    open(datas, oldDatas, keys, type) {
      this.type = type
      this.data = datas
      this.oldData = oldDatas
      this.pageDatas = this.getDatas(datas, oldDatas, keys)

      this.visible = true
    },

    moreFinish(){
      this.$emit('finish');
    },

    showImage(img){
      if(!img){
        return ''
      }else{
        if(img.indexOf('http') !== -1){
          return img
        }else{
          return `${this.bigImgUrlPrefix}${img}`
        }
      }
    },
    filterNonEmptyValues(obj) {
      const filteredObj = {};
      Object.keys(obj).forEach(key => {
        const value = obj[key];
        if (value !== null && value !== undefined && value !== "" && value !== 0 && value !== false && value !== []) {
          filteredObj[key] = value;
        }
      });
      return filteredObj;
    },
    useGoodsDetai() {
      onekeybind({
        barcode: this.data.barcode,
        standardProductId: this.oldData.standardProductId
      }).then((res) => {
        if (res.code === 0) {
          this.$message.success('绑定成功')
          this.visible = false
          this.$emit('finish');
        } else {
          this.$message.error(res.message)
        }
      })
    },
    useOtherGoods() {
      this.visible = false
      this.oldData.firstCategory = this.oldData.erpFourthCategoryId;
      this.$refs.goodsMoreCheckAlert.open(this.oldData, this.data.barcode)
    },
    normalSend() {
      let that = this
      this.oldData.drugClassification = this.oldData.drugClassificationCode
      // let newData = Object.assign({}, this.data, this.oldData)
      let newData = {...this.filterNonEmptyValues(this.oldData), ...this.filterNonEmptyValues(this.data)}
      console.log('this.data:', this.data)
      console.log('this.oldData:', this.oldData)
      console.log('newData:', newData)
      newData.isSplit = this.data.isSplit
      newData.purchaseType = this.data.purchaseType
      newData.drugClassification = this.oldData.drugClassification
      this.$emit('backRquest', newData, function () {
        that.visible = false
      })
    },
    selfSend() {
      let that = this
      this.data.drugClassification = this.data.drugClassificationCode
      this.$emit('backRquest', this.data, function () {
        that.visible = false
      })
    },

    jumpErrorEditPage() {
      this.$emit('gotoErrorEdit', this.oldData)
      this.visible = false
    },

    checkDataWithKey(arr, keys){
      let arrCopy = arr.filter(item => {
        if(item.key === 'imagesList'){
          return true;
        }
        return keys.includes(item.key);
      })
      return arrCopy;
    },

    getDatas(datas, oldDatas, keys) {
      let datasc = [
        {
          key: 'erpcode',
          name: 'ERP编码',
          row: 24,
          data: { pt: datas.erpcode }
        },
        {
          key: 'commonName',
          name:
            datas.firstCategory.toString() === '100005'
              ? '医疗器械名称'
              : '通用名称',
          row: 12,
          data: { sj: datas.commonName, pt: oldDatas.commonName }
        },
        {
          key: 'productName',
          name: '商品名称',
          row: 12,
          data: { sj: datas.productName, pt: oldDatas.productName }
        },
        {
          key: 'spec',
          name: '规格',
          row: 12,
          data: { sj: datas.spec, pt: oldDatas.spec }
        },
        {
          key: 'manufacturer',
          name: '生产厂家',
          row: 12,
          data: { sj: datas.manufacturer, pt: oldDatas.manufacturer }
        },
        {
          key: 'approvalNumber',
          name: '批准文号',
          row: 12,
          data: { sj: datas.approvalNumber, pt: oldDatas.approvalNumber }
        },
        {
          key: 'code',
          name: '条码',
          row: 12,
          data: { sj: datas.code, pt: oldDatas.code }
        },
        {
          key: 'brand',
          name: '品牌',
          row: 12,
          data: { sj: datas.brand, pt: oldDatas.brand }
        },
        {
          key: 'drugClassification',
          name: '处方类型',
          row: 12,
          data: {
            sj: datas.drugClassificationName,
            pt: oldDatas.drugClassificationName
          }
        },
        {
          key: 'manufacturingLicenseNo',
          name:
            datas.firstCategory.toString() === '100005'
              ? '医疗器械注册证或备案凭证编号'
              : '生产许可证或备案凭证编号',
          row: 12,
          data: {
            sj: datas.manufacturingLicenseNo,
            pt: oldDatas.manufacturingLicenseNo
          }
        },
        {
          key: 'marketAuthor',
          name: '上市许可持有人',
          row: 12,
          data: { sj: datas.marketAuthor, pt: oldDatas.marketAuthor }
        },
        {
          key: 'dosageForm',
          name: '剂型',
          row: 12,
          data: { sj: datas.dosageForm, pt: oldDatas.dosageForm }
        },
        {
          key: 'storageCondition',
          name: '存储条件',
          row: 12,
          data: { sj: datas.storageCondition, pt: oldDatas.storageCondition }
        },
        {
          key: 'producer',
          name: '产地',
          row: 12,
          data: { sj: datas.producer, pt: oldDatas.producer }
        },
        {
          key: 'firstCategoryName',
          name: '一级分类',
          row: 12,
          data: { sj: datas.firstCategoryName, pt: oldDatas.firstCategoryName }
        },
        {
          key: 'productUnit',
          name: '包装单位',
          row: 12,
          data: { sj: datas.productUnit, pt: oldDatas.productUnit }
        },
        {
          key: 'imagesList',
          name: '商品图片',
          row: 24,
          data: {
            sj: datas.imagesList ? datas.imagesList : undefined,
            pt:
              oldDatas.imageUrl
                ? oldDatas.imageUrl
                : undefined
          }
        }
      ]
      return this.checkDataWithKey(datasc, keys)
    }
  }
}
</script>
<style>
.fk-el-dialog .el-dialog {
  margin-top: 5vh !important;
}
</style>

<style scoped>
.goods-check-alert-conten {
  padding: 0 10px;
}

.goods-check-alert-conten-title {
  width: 100px;
}

.goods-check-alert-conten-row {
  display: flex;
  flex-direction: row;
  margin-left: 20px;
}

.goods-check-alert-conten-col {
  margin-top: 15px;
  display: flex;
  flex-direction: row;
}
.line-row {
  display: flex;
  flex-direction: row;
}
.red-content {
  color: red;
}
</style>
