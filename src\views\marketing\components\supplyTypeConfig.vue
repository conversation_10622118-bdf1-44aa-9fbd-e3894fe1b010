<template>
  <div>
    <el-form label-width="150px" class="activity-info">
      <el-form-item label="供货信息配置方式：" prop="isCopySaleArea">
        <!-- :disabled="fromType === 'edit' || dis" -->
        <el-radio-group  v-model="supplyInfo.isCopySaleArea" @change="isCopySaleAreaChange">
          <el-radio :label="1">复用原品销售范围</el-radio>
          <el-radio :label="2">人群</el-radio>
          <el-radio :label="3">配置业务商圈、供货对象和黑白名单</el-radio>
        </el-radio-group>
        <div class="tipBox">
          <!-- <p>1、选“复制原品销售范围”，活动商品创建成功后，活动商品和原商品商圈、供货对象、黑白名单相同，活动投放范围和原品一致。</p>
          <p>2、选“人群”，活动投放范围按照人群进行生效。</p>
          <p>3、选“配置业务商圈、供货对象和黑白名单”，按照页面所选信息生效活动。</p> -->
          <p>1、选“复制原品销售范围”，活动商品创建成功后，活动商品和原商品商圈、供货对象、黑白名单相同，最终售卖范围是人群和原品销售范围取交集。示例：人群限制仅售卖湖北、单体；店铺已选范围为全国，则最终取交集后售卖范围为“湖北单体“</p>
          <p>2、选“人群”，活动投放范围按照人群进行生效。</p>
          <p>3、选“配置业务商圈、供货对象和黑白名单”，最终售卖范围是人群限制区域和原品销售范围取交集。示例：人群限制仅售卖湖北、单体；店铺已选范围为全国，则最终取交集后售卖范围为“湖北单体“</p>
        </div>
      </el-form-item>
      <el-form-item v-if="supplyInfo.isCopySaleArea === 2" label="指定人群：">
        <el-button
          size="small"
          type="primary"
          plain
          :disabled="isedit === false || (baseCustomerGroupId && !isShopUpdate) || (disabled && subOtherDisabled)"
          @click="dialogVisible = true"
        >选择人群</el-button>
        <span style="padding-left: 10px" v-if="baseCustomerGroupId || peopleInfo.id">
          已选：人群ID：{{ peopleInfo.id || baseCustomerGroupId }} ；人群名称：{{ peopleInfo.name || baseCustomerGroupName }}
        </span>
        <el-button
          v-if="baseCustomerGroupId || peopleInfo.id"
          type="text"
          @click="seeCrowd(peopleInfo.id)"
        >查看详情</el-button>
      </el-form-item>
      <el-form-item v-else-if="supplyInfo.isCopySaleArea === 1 && ispgby" label="指定人群：">
        <el-button
          size="small"
          type="primary"
          plain
          :disabled="isedit === false || (disabled && subOtherDisabled)"
          @click="dialogVisible = true"
        >选择人群</el-button>
        <span style="padding-left: 10px" v-if=" baseCustomerGroupId || peopleInfo.id">
          <span v-if="isedit === false">平台指定人群：</span>  已选：人群ID：{{ peopleInfo.id || baseCustomerGroupId }} ；人群名称：{{ peopleInfo.name || baseCustomerGroupName }}
        </span>
        <el-button
          v-if="baseCustomerGroupId || peopleInfo.id"
          type="text"
          @click="seeCrowd(peopleInfo.id)"
        >查看详情</el-button> 
        <el-button
          size="small"
          type="primary"
          plain
          v-if="isedit === true && peopleInfo.id"
          @click="clear"
        >清空</el-button>
      </el-form-item>
      <el-form-item v-if="supplyInfo.isCopySaleArea === 3 && supplyInfo.controlRosterType != 2" label="复用原品商圈：" prop="isCopyBusArea" label-width="180px">
        <el-radio-group v-model="supplyInfo.isCopyBusArea" :disabled="disabled">
          <el-radio :label="1">是</el-radio>
          <el-radio :label="2">否</el-radio>
        </el-radio-group>
        <div class="tipBox">
          <p class="redText">注意：选择“是”则拼团商品业务商圈和原品相同，选择“否”则在所选商圈范围内的客户可购买拼团商品。</p>
        </div>
      </el-form-item>
      <el-form-item v-if="supplyInfo.isCopyBusArea === 2 && supplyInfo.isCopySaleArea === 3 && supplyInfo.controlRosterType != 2" label="业务商圈：">
        <el-button size="small" type="primary" :disabled="disabled && subOtherDisabled" @click="selectBusinessCircle">选择商圈</el-button>
        <span style="padding-left: 10px" v-if="businessCircleInfo.busAreaName">
          已选：{{ businessCircleInfo.busAreaName }}
          <el-button v-if="businessCircleInfo.busAreaName" type="text" class="btn-info" @click="checkDetails">查看商圈</el-button>
        </span>
      </el-form-item>
      <el-form-item v-if="supplyInfo.isCopySaleArea === 3 && supplyInfo.controlRosterType != 2" label="复用原品供货对象：" prop="isCopyControlUser" label-width="210px">
        <el-radio-group v-model="supplyInfo.isCopyControlUser" :disabled="disabled">
          <el-radio :label="1">是</el-radio>
          <el-radio :label="2">否</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="供货对象:" prop="controlUserTypes" v-if="supplyInfo.isCopyControlUser === 2 && supplyInfo.isCopySaleArea === 3 && supplyInfo.controlRosterType != 2">
        <div class="customerType">
          <el-checkbox
            :indeterminate="isIndeterminate"
            v-model="checkAll"
            :disabled="disabled && subOtherDisabled"
            @change="handleCheckAllChange"
            class="checkedall"
          >全选</el-checkbox>
          <el-checkbox-group
            v-model="supplyInfo.controlUserTypes"
            :disabled="disabled && subOtherDisabled"
            @change="handleCheckedTypesChange"
          >
            <el-checkbox
              v-for="(item,index) in customerTypes"
              :label="item.id"
              style="marginBottom:10px;"
              :key="index"
            >{{item.name}}</el-checkbox>
          </el-checkbox-group>
        </div>
        <div style='color: red;font-size: 12px;'>
          注意：被勾选的客户类型对应的客户可购买当前商品。未勾选的客户类型对应的客户不可购买当前商品。
        </div>
      </el-form-item>
      <!-- <el-form-item v-if="supplyInfo.isCopySaleArea === 3" label="是否复制对应原品的黑白名单：" prop="isCopyControlRoster" label-width="210px">
        <el-radio-group v-model="supplyInfo.isCopyControlRoster">
          <el-radio :label="1">是</el-radio>
          <el-radio :label="2">否</el-radio>
        </el-radio-group>
      </el-form-item> -->
      <el-form-item v-if="supplyInfo.isCopyControlRoster === 2 && supplyInfo.isCopySaleArea === 3" label="黑白名单：" prop="controlRosterType">
        <el-radio-group v-model="supplyInfo.controlRosterType" :disabled="disabled && subOtherDisabled">
          <el-radio :label="1" @click.native.prevent="clickBlackNameList">黑名单控销组</el-radio>
          <el-radio :label="2" @click.native.prevent="clickWhiteNameList">白名单控销组</el-radio>
        </el-radio-group>
        <div class="div-info">
          <el-button type="primary" v-if="supplyInfo.controlRosterType === 1 || supplyInfo.controlRosterType === 2" :disabled="disabled && subOtherDisabled" size="small" @click="toSelectControlGroups">选择控销组</el-button>
          <span style="padding-left: 10px" v-if="supplyInfo.controlGroupName && supplyInfo.controlRosterType">
            <span>已选“{{supplyInfo.controlGroupName}}”</span>
            <el-button type="text" class="btn-info" @click="seeMerchantDetail">查看药店明细</el-button>
          </span>
        </div>
        <div class="tipBox redText">
          <p>活动维度可见性分为三种场景：</p>
          <p>场景1：未选择黑名单控销组，则商圈内指定客户类型可享受该活动</p>
          <p>场景2：选择了黑名单控销组，则仅商圈内指定客户类型-黑名单客户可享受该活动。</p>
          <p>场景:3：选择了白名单控销组，则仅白名单的客户可享受该活动。</p>
        </div>
      </el-form-item>
      <el-form-item v-if="supplyInfo.isCopySaleArea === 3 && ispgby" label="指定人群：">
        <el-button
          size="small"
          type="primary"
          plain
          :disabled="isedit === false || (disabled && subOtherDisabled)"
          @click="dialogVisible = true"
        >选择人群</el-button>
        <span style="padding-left: 10px" v-if=" baseCustomerGroupId || peopleInfo.id">
          <span v-if="isedit === false">平台指定人群：</span>  已选：人群ID：{{ peopleInfo.id || baseCustomerGroupId }} ；人群名称：{{ peopleInfo.name || baseCustomerGroupName }}
        </span>
        <el-button
          v-if="baseCustomerGroupId || peopleInfo.id"
          type="text"
          @click="seeCrowd(peopleInfo.id)"
        >查看详情</el-button> 
        <el-button
          size="small"
          type="primary"
          plain
          v-if="isedit === true && peopleInfo.id"
          @click="clear"
        >清空</el-button>
      </el-form-item>
    </el-form>
    <!-- 选择人群 -->
    <crowd-selector-dialog
      ref="changePeople"
      @onSelect="sendPeopleData"
      v-if="dialogVisible"
      v-model="dialogVisible"
      :selected="peopleInfo.id"
    ></crowd-selector-dialog>
    <!-- 查看客户信息 -->
    <CustomerInfoLog
      v-if="showCustomer"
      :market-customer-group-id="peopleInfo.id || baseCustomerGroupId"
      @cancelModal="cancelDialog"
    />
    <!-- 选择商圈 -->
    <select-business-circle
      v-if="isSelectBusinessCircle"
      :selected.sync="businessCircleInfo.busAreaId"
      fromComp="groupActivityTheme"
      @onDialogChange="onDialogChange"
      @selectChange="selectChange"
    ></select-business-circle>
    <!-- 查看商圈 -->
    <view-business-circle
      :row="{id:businessCircleInfo.busAreaId}"
      v-if="viewBusinessDialog"
      @onDialogChange="onDialogChange"
    ></view-business-circle>
    <!-- 查看药店明细 -->
    <ListOfControlGroups
      v-if="showMerchantInfo"
      :control-group-id="supplyInfo.controlGroupId"
      @cancelDialog="cancelDialog"
    />
    <!-- 选择控销组 -->
    <SelectControlGroups
      v-if="showSelectControlGroups"
      :show-select-control-groups.sync="showSelectControlGroups"
      :selected-id="supplyInfo.controlGroupId"
      @selectGroupChange="selectGroupChange"
    />
  </div>
</template>
<script>
import CrowdSelectorDialog from '@/components/xyy/customerOperatoin/crowd-selector-dialog.vue'
import selectBusinessCircle from "@/views/product/components/selectBusinessCircle";
import viewBusinessCircle from '@/views/business-circle/components/viewBusinessCircle';
import ListOfControlGroups from '@/views/product/components/listOfControlGroups';
import SelectControlGroups from '@/views/product/components/selectControlGroups';
import { findUserTypes } from '@/api/product';
import { getCrowdList } from '@/components/xyy/customerOperatoin/fetch/fetch';
import CustomerInfoLog from '@/components/customer/customerInfoLog.vue';

export default {
  components: { CrowdSelectorDialog, selectBusinessCircle, viewBusinessCircle, SelectControlGroups, ListOfControlGroups, CustomerInfoLog },
  props: {
    dis: Boolean,
    saleScopeDto: Object,
    baseCustomerGroupId: Number,
    baseCustomerGroupName: String,
    isShopUpdate: {
      type: Boolean,
      default: false
    },
    isGroupActivityTheme:{
      type:Number,
      default: 1,
    },
    isedit:{
      type: Boolean,
      default: true,
    },
    ispgby:{
      type: Boolean,
      default: true,
    },
    isPT:{
      type: Boolean,
      default: false
    },
  },
  data() {
    return {
      isSelectBusinessCircle: false,
      viewBusinessDialog: false,
      businessCircleInfo: { // 商圈信息
        busAreaId: '',
        busAreaName: '',
      },
      dialogVisible: false,
      peopleInfo: { // 人群信息
        name: '',
        id: '',
      },
      peopleInfoList:[
        {
          name: '',
          id: '',
        },
        {
          name: '',
          id: '',
        },
        {
          name: '',
          id: '',
        },
      ],
      showCustomer: false,
      showMerchantInfo: false, // 查看药店明细
      showSelectControlGroups: false, // 选择控销组
      isIndeterminate: false,
      checkAll: false,
      customerTypes: [],
      supplyInfo: {
        isCopyBusArea: 2,
        controlRosterType: 0,
        isCopySaleArea: 1,
        controlGroupId: '',
        controlGroupName: '',
        controlUserTypes: [],
        isCopyControlRoster: 2,
        isCopyControlUser: 2,
      },
      isCopySaleArea:1,
      fromType: '',
      disabled: false,
      subOtherDisabled: false,
      newSubOtherDisabled: false,
    };
  },
  watch: {
    isedit:{
      handler(val){
        console.log(val,'qiyu');
      }
    },
    saleScopeDto: { // 深度监听，可监听到对象、数组的变化
      handler(val) {
        if (!this.isShopUpdate) {
          if (Object.keys(val).length) {
            // this.saleScopeDto = val;
            this.rest();
            this.initData();
          } else {
            this.supplyInfo = {
              isCopyBusArea: 2,
              controlRosterType: 0,
              isCopySaleArea: 1,
              controlGroupId: '',
              controlGroupName: '',
              controlUserTypes: [],
              isCopyControlRoster: 2,
              isCopyControlUser: 2,
            };
            this.isCopySaleArea = 1;
            this.businessCircleInfo = { // 商圈信息
              busAreaId: '',
              busAreaName: '',
            };
            this.peopleInfo = { // 人群信息
              name: '',
              id: '',
            };
          }
        }
      },
      deep: true, // true 深度监听
    },
  },
  activated() {
    // this.findUserTypes();
    this.rest();
    // this.getDisable();
    this.fromType = this.$route.query.fromType;
  },
  created() {
    this.findUserTypes();
    this.fromType = this.$route.query.fromType;
  },
  mounted() {
    this.rest();
    if (this.isShopUpdate) {
      this.initData();
    }
  },
  methods: {
    /**清空人群数据 */
    clear(){
      this.peopleInfo = { // 人群信息
        name: '',
        id: '',
      }
      this.peopleInfoList[this.isCopySaleArea - 1] = {
        name: '',
        id: '',
      }
    },
    rest(){
      this.peopleInfoList = [
        {
          name: '',
          id: '',
        },
        {
          name: '',
          id: '',
        },
        {
          name: '',
          id: '',
        },
      ]
      this.peopleInfo = {
        name: '',
        id: '',
      }
    },
    isCopySaleAreaChange(){
      if(this.isedit === true){
        this.peopleInfoList[this.isCopySaleArea -1] = {...this.peopleInfo};
        this.peopleInfo = {...this.peopleInfoList[ this.supplyInfo.isCopySaleArea - 1]};
        console.log(this.peopleInfoList,this.peopleInfo,'lwq4',this.isCopySaleArea,this.supplyInfo.isCopySaleArea);
        this.isCopySaleArea = this.supplyInfo.isCopySaleArea;
      }
    },
    getDisable(){
      const params = {
          pageNum: 1,
          pageSize: 20,
          createStartTime:'',
          groupName:'',
          id: "",
        };
      getCrowdList(params).then((res) => {
        if (res.code === 1000) {
          let list = res.data.list ? res.data.list : [];
          // if(this.saleScopeDto && this.saleScopeDto.baseCustomerGroupId){
          // }
          if(this.peopleInfo.id || this.baseCustomerGroupId){
            let id = this.peopleInfo.id ? this.peopleInfo.id : this.baseCustomerGroupId;
            if(list.filter(item=>item.id == id).length > 0){
              this.newSubOtherDisabled = false;
            }else{
              this.newSubOtherDisabled = true;
            }
          }
        } else {
          this.$message({
            message: '请求失败',
            type: 'error',
          });
        }
      })
      .catch((error) => {
      })
      .finally(() => {
      });
    },
    initData() {
      Object.keys(this.supplyInfo).forEach((key) => {
        this.supplyInfo[key] = this.saleScopeDto[key];
      });
      console.log(this.supplyInfo,'qiyu2');
      if (this.saleScopeDto.controlUserTypes) {
        this.supplyInfo.controlUserTypes = this.saleScopeDto.controlUserTypes.split(',').map(i => Number(i));
      }else{
        this.supplyInfo.controlUserTypes=[]
      }
      this.businessCircleInfo = { // 商圈信息
        busAreaId: this.saleScopeDto.busAreaId || '',
        busAreaName: this.saleScopeDto.busAreaName || '',
      };
      this.peopleInfo = {
        id: this.saleScopeDto.customerGroupId,
        name: this.saleScopeDto.customerGroupName,
      };
      console.log('zmt111',this.peopleInfo);
      
      this.isCopySaleArea = this.supplyInfo.isCopySaleArea;
      if(this.saleScopeDto.isedit === false){
        this.isedit = false;
        this.peopleInfoList.forEach(item=>{
          item.id = this.saleScopeDto.customerGroupId;
          item.name = this.saleScopeDto.customerGroupName;
        })
      }else{
        this.peopleInfoList[this.isCopySaleArea - 1] = {
          id: this.saleScopeDto.customerGroupId,
          name: this.saleScopeDto.customerGroupName,
        }
        this.isedit = true;
      }
    },
    // 添加人群*
    sendPeopleData(value) {
      this.peopleInfo = {
        name: value.tagName,
        id: value.id,
      };
    },
    seeCrowd() {
      this.showCustomer = true;
    },
    // 选择商圈
    selectBusinessCircle() {
      this.isSelectBusinessCircle = true;
    },
    // 查看商圈详情
    checkDetails() {
      this.viewBusinessDialog = true;
    },
    onDialogChange() {
      this.isSelectBusinessCircle = false;
      this.viewBusinessDialog = false;
    },
    selectChange(row) {
      if (row) {
        this.businessCircleInfo.busAreaId = row.id;
        this.businessCircleInfo.busAreaName = row.busAreaName;
      }
    },
    // 获取用户类型
    findUserTypes() {
      findUserTypes().then((res) => {
        if (res.code == 0) {
          this.customerTypes = res.data;
        } else {
          this.$message({
            message: res.message,
            type: 'warning',
          });
        }
      });
    },
    // 供货对象
    handleCheckAllChange(val) {
      const checkAllId = this.customerTypes.map((item => item.id));
      this.supplyInfo.controlUserTypes = val ? checkAllId : [];
      this.isIndeterminate = false;
    },
    handleCheckedTypesChange(value) {
      const checkedCount = value.length;
      this.checkAll = checkedCount === this.customerTypes.length;
      this.isIndeterminate = checkedCount > 0 && checkedCount < this.customerTypes.length;
    },
    // 点击黑名单
    clickBlackNameList(e) {
      if (this.disabled) {
        return;
      }
      this.supplyInfo.controlGroupId = '';
      this.supplyInfo.controlGroupName = '';
      if (this.supplyInfo.controlRosterType === 1) {
        this.supplyInfo.controlRosterType = 0;
      } else {
        this.supplyInfo.controlRosterType = 1;
        // this.supplyInformationVoRules.controlUserTypes.forEach((itemRules) => {
        //   if (Object.prototype.hasOwnProperty.call(itemRules, 'required')) {
        //     itemRules.required = true;
        //   }
        // });
      }
    },
    clickWhiteNameList(e) {
      if (this.disabled) {
        return;
      }
      if (this.supplyInfo.controlRosterType == 2) {
        this.supplyInfo.controlRosterType = 0;
      } else {
        this.$confirm('选择白名单控销组后，仅白名单控销组内的客户可以购买此商品，确定使用白名单控销组吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
        })
          .then(() => {
            this.supplyInfo.controlGroupId = '';
            this.supplyInfo.controlGroupName = '';
            this.supplyInfo.controlRosterType = 2;
            this.supplyInfo.isCopyBusArea = 2;
            this.businessCircleInfo = { // 商圈信息
              busAreaId: '',
              busAreaName: '',
            };
            this.supplyInfo.isCopyControlUser = 2;
            this.supplyInfo.controlUserTypes = [];
          })
          .catch(() => {});
      }
    },
    seeMerchantDetail() {
      this.showMerchantInfo = true;
    },
    selectGroupChange(row) {
      this.supplyInfo.controlGroupId = row.id; // 用户组id
      this.supplyInfo.controlGroupName = row.name;
    },
    cancelDialog() {
      this.showMerchantInfo = false;
      this.showSelectControlGroups = false;
      this.showCustomer = false;
    },
    toSelectControlGroups() {
      this.showSelectControlGroups = true;
    },
    getAllSupplyInfo() {
      let params = {
        ...this.supplyInfo,
        customerGroupId: this.peopleInfo.id || this.baseCustomerGroupId,
        busAreaId: this.businessCircleInfo.busAreaId,
      }
      console.log(this.businessCircleInfo, '???o')
      // if (!this.peopleInfo.id) { //this.supplyInfo.isCopySaleArea !== 2 || !this.peopleInfo.id
      //   delete params.customerGroupId;
      // }
      if (this.supplyInfo.isCopyBusArea === 1) {
        delete params.busAreaId;
      }
      if (this.supplyInfo.isCopyControlUser === 1) {
        this.supplyInfo.controlUserTypes = [];
      }
      if(params.controlRosterType === null){
        params.controlRosterType = 0;
      } 
      return params;
    },
  },
};

</script>
<style lang="scss" scoped>
  .tipBox {
    color: #909399;
    font-size: 12px;
    p {
      line-height: 18px;
    }
  }
  .redText {
    color: #ff3024;
  }
  .customerType {
    border: 1px solid #eeeeee;
    border-radius: 4px;
    max-height: 260px;
    overflow-y: auto;
    ::v-deep  .el-checkbox {
      width: 14%;
      margin-left: 10px;
    }
    ::v-deep  .checkedall {
      width: 100%;
      padding: 10px;
      margin-left: 0;
      margin-bottom: 10px;
    }
    ::v-deep  .el-checkbox__input.is-checked + .el-checkbox__label {
      color: #333333;
    }
  }
</style>
