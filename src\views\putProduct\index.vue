<template>
	<div>
		<common-header :shouHeightLine="false" :showFold="false">
			<span slot="title" style="font-weight:500;fontSize:16px;margin:0">
				<span>非单品包邮类</span>
				<span style="marginLeft:10px;color:#8a8a8a;fontSize:14px;">买满起配金额可配送</span>
			</span>
			<div class="putProduct-card">
				<div class="card-title">
					<div class="icon">
						<img src="../../assets/image/putProduct/normal.png" />
					</div>
					<div>
						<p>普通商品</p>
						<p style="color:#8e8e8e;">可以使用平台券、商家券</p>
					</div>
				</div>
				<div style="marginTop:10px;">
					<el-button class="btn" size="small" @click="go('normal')">去上架</el-button>
				</div>
			</div>
		</common-header>
		<common-header title="单品包邮类" :shouHeightLine="false" :showFold="false">
			<span slot="title" style="font-weight:500;fontSize:16px;margin:0">
				<span>单品包邮类</span>
				<span style="marginLeft:10px;color:#8a8a8a;fontSize:14px;">拼团、批购包邮</span>
			</span>
			<div style="display:flex;gap:10px;">
				<div class="putProduct-card">
					<div class="card-title">
						<div class="icon">
							<img src="../../assets/image/putProduct/pintuan.png" />
						</div>
						<div>
							<p>拼团</p>
							<p style="color:#8e8e8e;">包括周末拼团、夜间拼团等。商家需在系统指定时间参与，低于参考价才可显示在拼团会场。</p>
						</div>
					</div>
					<div style="marginTop:10px;">
						<span style="color:red;margin-left:60px;">{{ num }}个拼团主题可参与</span>
						<el-button class="btn" size="small" @click="go('pintuan')">去上架</el-button>
					</div>
				</div>
				<div class="putProduct-card">
					<div class="card-title">
						<div class="icon">
							<img src="../../assets/image/putProduct/piGou.png" />
						</div>
						<div>
							<p>批购包邮</p>
							<p style="color:#8e8e8e;">无需价格审核，商家可自由设置活动时间、供货对象、库存及下架。</p>
						</div>
					</div>
					<div style="marginTop:10px;">
						<el-button class="btn" size="small" @click="go('multiple')">去上架</el-button>
					</div>
				</div>
			</div>
		</common-header>
	</div>
</template>

<script>
import commonHeader from '../afterSaleManager/components/common-header.vue';
import { apiGetFrameActBaseForReport } from "../../api/market/collageActivity"
export default {
	name: 'putProduct',
	components: {
		commonHeader
	},
	data() {
		return {
			num: ''
		}
	},
	mounted() {
		apiGetFrameActBaseForReport().then(res => {
			if (res.code == 1000) {
				if (res.data.activityReportBaseResDTOS) {
					this.num = res.data.activityReportBaseResDTOS.length
				}
			} else {
				this.$message.error("查询拼团活动主题失败")
			}
		})
	},
	methods: {
		go(type) {
			switch(type) {
				case 'normal': {
					// window.openTab("/product/initialize", {from: 'toInitialize'})
					this.$router.push({ path: '/product/initialize', query: {from: 'toInitialize'} }); // 在当前页跳转
					// window.closeTab(this.$route.fullPath, true);
					// setTimeout(() => {
					// 	window.openTab("/product/initialize", {from: 'toInitialize'})
					// }, 0)
					break;
				}
				case 'pintuan': {
					window.openTab('/collageActivity')
					break;
				}
				case 'multiple': {
					window.openTab('/putProduct/multiple', {
						fromType: 'edit'
					})
					break;
				}
			}
		}
	}
}
</script>

<style lang="scss" scoped>
.card-title {
	position: relative;
	display: flex;
	align-items: flex-start;
	gap: 10px;
}
.putProduct-card {
	width: 400px;
	padding: 10px;
	background-color: #efefef87;
	border: solid 1px #d4d4d478;
	display: flex;
	flex-direction: column;
	justify-content: space-between;
}
.card-title .icon {
	flex-shrink: 0;
	flex-grow: 0;
	border-radius: 50%;
	width: 60px;
	height: 60px;
	border-radius: 50%;
	overflow: hidden;
}
.card-title p {
	margin: 0 !important;
}
.putProduct-card .btn {
	position: relative;
	display: inline-block;
	float: right;
	color: #4183d5;
	border-color: #c6daf2;
	background-color: #ecf3fb;
}
.putProduct-card .btn:hover {
	color: #5396e7;
	border-color: #dae9fc;
	background-color: #f6faff;
}
.putProduct-card .btn:active {
	color: #4183d5;
	border-color: #c6daf2;
	background-color: #ecf3fb;
}
</style>