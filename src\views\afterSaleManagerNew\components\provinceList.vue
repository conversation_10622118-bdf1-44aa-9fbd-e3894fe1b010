<template>
<!-- 	<el-autocomplete v-model="data" :fetch-suggestions="query" @select="selected" :size="size" @clear="clear" @blur="data = temp" :placeholder="temp" clearable>
		<template slot="append">

		</template>
	</el-autocomplete> -->
	<el-input class="input-with-select" :size="size">
		<template slot="prepend">
			<span>{{ label }}</span>
		</template>
		<template slot="append">
			<el-select style="margin-left:0px;" :value="value" @input="change" :multiple="multiple" collapse-tags :size="size" clearable>
				<el-option v-for="item in list" :key="item[valueProp]" :value="item[valueProp]" :label="item[labelProp]"></el-option>
			</el-select>
		</template>
	</el-input>
</template>

<script>
export default {
	props: {
		value: '',
		size: {
			default: "small",
			type: String
		},
		label: {
			default: '',
			type: String
		},
		list: {
			default: () => [],
			type: Array
		},
		multiple: {
			default: false,
			type: Boolean
		},
		valueProp: {
			default: 'value',
			type: String
		},
		labelProp: {
			default: 'label',
			type: String
		}
	},
	methods: {
		change(val) {
			this.$emit("input", val)
		}
	}
}
</script>

<style scoped>
.input-with-select ::v-deep   input {
  padding: 0 !important;
	border-right: none;
}
.input-with-select > ::v-deep   input {
  width: 0px !important;
}
::v-deep   .el-input-group__prepend {
  width: auto;
}
::v-deep   .el-input-group__append {
  background: white;
  width: 100%;
}
::v-deep   .el-input-group__append > div {
  width: 100%;
}
</style>

