<!-- 首页公告列表 -->
<template>
  <div class="courseListContainer" v-if="contactInfo">
    <div class="courseListTitle">药帮忙联系人</div>
    <div class="contactInfo">
    <el-image style="width: 100px; height: 100px" :src="contactInfo.qrcodeImage" :previewSrcList="previewSrcList" fit="contain"></el-image>
      <div class="namePhone">
        <div class="name">{{ contactInfo&&contactInfo.accountName }}</div>
        <div class="phone">{{ contactInfo&&contactInfo.phone }}</div>
      </div>
    </div>
  </div>
</template>

<script>
import merchantCourseList from './merchantCourseList.vue'
import { getQrcodeDto } from '@/api/home'
getQrcodeDto
export default {
  components: { merchantCourseList },
  data() {
    return {
      previewSrcList: [],
      merchantCourseList: null,
      contactInfo: {}
    }
  },
  computed: {},
  watch: {},
  methods: {
    moreCourse() {
      this.$refs.merchantCourseList.dialogVisible = true
    }
  },
  created() {
    getQrcodeDto().then((res) => {
      this.contactInfo = res.result
      this.previewSrcList = [res.result.qrcodeImage]
    })
  },
  mounted() {}
}
</script>
<style lang="scss" scoped>
.courseListContainer {
  background-color: #fff;
  padding: 16px;
  margin-bottom: 16px;
  border-radius: 3px;
  .courseListTitle {
    display: flex;
    justify-content: space-between;
    font-weight: bold;
    font-size: 16px;
    margin-bottom: 16px;
  }

  .contactInfo {
    display: flex;
  }
  .contactInfo img{
    width:100px;
    height:100px;
  }
  .namePhone {
    margin: 7px 14px;
  }
  .name {
    font-weight: bold;
    margin-bottom: 7px;
    margin-top:13px;
  }
  .wxcode {
    height: 83px;
    width: 83px;
  }
}
</style>
