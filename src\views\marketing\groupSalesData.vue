<template>
  <div class="line-div">
    <div
      class="serch"
      style="display: flex;justify-content: space-between;"
    >
      <el-row
        type="flex"
        align="middle"
      >
        <span class="sign" />
        <div>查询条件</div>
      </el-row>
      <!-- <el-button
        size="small"
        type="primary"
        @click="handleBack"
      >
        返回
      </el-button> -->
    </div>
    <div class="box-div">
      <el-form
        ref="listQuery"
        :model="listQuery"
        :inline="true"
        size="small"
      >
        <el-row
          :gutter="20"
          class="my-search-row"
        >
          <el-col :span="6">
            <el-input
              v-model="listQuery.productCodeSearchKeyword"
              placeholder="sku编码/商品编码/erp编码/商品原编码"
              size="small"
            >
              <template slot="prepend">
                编码信息
              </template>
            </el-input>
          </el-col>
          <el-col :span="6">
            <el-input
              v-model="listQuery.orderNo"
              placeholder="请输入"
              size="small"
            >
              <template slot="prepend">
                订单号
              </template>
            </el-input>
          </el-col>
          <el-col :span="6">
            <el-input
              v-model="listQuery.merchantId"
              placeholder="请输入"
              size="small"
            >
              <template slot="prepend">
                药店编码
              </template>
            </el-input>
          </el-col>
          <el-col :span="6">
            <el-input
              v-model="listQuery.merchantName"
              placeholder="请输入"
              size="small"
            >
              <template slot="prepend">
                药店名称
              </template>
            </el-input>
          </el-col>
        </el-row>
        <el-row
          :gutter="20"
          class="my-search-row"
          style="margin-top: 12px"
        >
          <el-col :span="6">
            <span class="search-title">药店地区</span>
            <el-select
              v-model="listQuery.provinceCode"
              placeholder="请选择"
              size="small"
            >
              <el-option
                v-for="item in provincesOptions"
                :key="item.key"
                :label="item.value"
                :value="item.key"
              />
            </el-select>
          </el-col>
          <el-col :span="6">
            <span class="search-title">订单状态</span>
            <el-select
              v-model="listQuery.orderStatus"
              placeholder="请选择"
              size="small"
            >
              <el-option
                v-for="item in orderStatusOptions"
                :key="item.key"
                :label="item.value"
                :value="item.key"
              />
            </el-select>
          </el-col>
          <el-col :span="12">
            <span class="search-title">下单时间</span>
            <div class="time-box">
              <el-date-picker
                v-model.trim="time"
                size="small"
                type="datetimerange"
                format="yyyy-MM-dd HH:mm:ss"
                range-separator="至"
                value-format="timestamp"
                :default-time="['00:00:00', '23:59:59']"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :picker-options="pickerOptions"
                @change="handleChangeTime"
              />
            </div>
          </el-col>
        </el-row>
      </el-form>
      <el-row
        :gutter="20"
        class="my-search-row"
        style="margin-top: 12px; text-align: right;"
      >
        <el-button
          size="small"
          type="primary"
          @click="handleSearch"
        >
          查询
        </el-button>
        <el-button
          size="small"
          @click="reset"
        >
          重置
        </el-button>
        <el-button
          size="small"
          @click="handleExport"
        >
          导出
        </el-button>
      </el-row>
    </div>
    <div class="texts">
      <p>
        <!-- <el-tooltip
          class="item"
          content="销售数据剔除未支付、已取消、已退款且部分退中活动商品应发货数量等于0的订单数据，仅统计已支付的有效订单"
          placement="top"
        >
          <i class="el-icon-warning-outline" />
        </el-tooltip> -->
        1、采购店数：{{ statisticsInfo.purchaseMerchantNum }} 采购订单数：{{ statisticsInfo.purchaseOrderNum }} 采购数量：{{ statisticsInfo.purchaseProductNum }} 采购金额：{{ statisticsInfo.purchaseAmount }}
      </p>
      <p>
        <el-tooltip
          class="item"
          content="已付款统计的有效订单采购数据；待付款为未支付的订单数据"
          placement="top"
        >
          <i class="el-icon-warning-outline" />
        </el-tooltip>
        2、{{ `已付款：共${statisticsInfo.paidPurchaseMerchantNum}个药店${statisticsInfo.paidPurchaseOrderNum}笔订单购买${statisticsInfo.paidPurchaseAmount}元　　　　　待付款：共${statisticsInfo.unpaidPurchaseMerchantNum}个药店${statisticsInfo.unpaidPurchaseOrderNum}笔订单购买${statisticsInfo.unpaidPurchaseAmount}元` }}
      </p>
    </div>
    <div class="">
      <el-tabs
        v-model="activePane"
        @tab-click="tabHandleClick"
      >
        <el-tab-pane
          v-for="item in tabStatusOptions"
          :key="item.tabStatus"
          :name="item.tabStatus"
        >
          <span slot="label">
            {{ item.tabName }}({{ item.tabCount || 0 }})
          </span>
        </el-tab-pane>
      </el-tabs>
      <xyy-table
        :data="list"
        :list-query="listQuery"
        :col="col"
        @get-data="getList"
      >
        <template
          slot="productInfo"
          slot-scope="{col}"
        >
          <el-table-column
            :key="col.index"
            :label="col.name"
            :width="col.width"
          >
            <template slot-scope="{row}">
              <p>sku编码：{{ row.csuId }}</p>
              <p>商品编码：{{ row.csuBarcode }}</p>
              <p>商品ERP编码：{{ row.csuErpCode }}</p>
              <p>商品原编码：{{ row.originalCsuBarcode }}</p>
              <p>商品名称：{{ row.csuProductName }}</p>
              <p>规格：{{ row.csuSpec }}</p>
              <p>厂家：{{ row.csuManufacturer }}</p>
            </template>
          </el-table-column>
        </template>
        <template
          slot="counts"
          slot-scope="{col}"
        >
          <el-table-column
            :key="col.index"
            :label="col.name"
            :width="col.width"
          >
            <template slot-scope="{row}">
              <p>商品原价：{{ row.csuOriginalPrice }}</p>
              <p>购买单价：{{ row.buyUnitPrice }}</p>
              <p>购买数量：{{ row.buyNum }}</p>
              <p>已退数量：{{ row.refundNum }}</p>
              <p>总金额：{{ row.totalAmount }}</p>
              <p>优惠金额：{{ row.discountAmount }}</p>
              <p>实付金额：{{ row.actualAmount }}</p>
            </template>
          </el-table-column>
        </template>
        <template
          slot="orderNo"
          slot-scope="{col}"
        >
          <el-table-column
            :key="col.index"
            :label="col.name"
            :width="col.width"
          >
            <template slot-scope="{row}">
              <span>{{ row.orderNo }}</span>
              <span
                style="color: #4183d5; cursor: pointer"
                @click="handleGoOrderList(row.orderNo)"
              >查看</span>
            </template>
          </el-table-column>
        </template>
        <template
          slot="name"
          slot-scope="{col}"
        >
          <el-table-column
            :key="col.index"
            :label="col.name"
            :width="col.width"
          >
            <template slot-scope="{row}">
              {{ row.merchantName }}
              <p>药店编码：{{ row.merchantId }}</p>
            </template>
          </el-table-column>
        </template>
        <template
          slot="consignee"
          slot-scope="{col}"
        >
          <el-table-column
            :key="col.index"
            :label="col.name"
            :width="col.width"
          >
            <template slot-scope="{row}">
              <p>收货人：{{ row.consignee }}</p>
              <p>手机号：{{ row.consigneeMobile }}</p>
              <p>收货人详细地址：{{ row.consigneeAddress }}</p>
            </template>
          </el-table-column>
        </template>
      </xyy-table>
    </div>
    <export-tip
      :change-export="changeExport"
      @handleExoprClose="handleExoprClose"
      @handleChangeExport="handleChangeExport"
    />
  </div>
</template>
<script>
import exportTip from '@/views/other/components/exportTip';
import { getSalesDataList, listProvinces, listOrderStatus, getStatisticsInfo, getStatusCountInfo, exportData, getSearchInfo } from '@/api/market/groupSalesData';

export default {
  name: 'GroupSalesData',
  components: { exportTip },
  data() {
    return {
      changeExport: false,
      list: [],
      col: [
        {
          index: 'productInfo',
          name: '商品信息',
          slot: true,
        },
        {
          index: 'counts',
          name: '数量&金额',
          slot: true,
        },
        {
          index: 'orderNo',
          name: '订单号',
          slot: true,
        },
        {
          index: 'name',
          name: '客户名称',
          slot: true,
        },
        {
          index: 'consignee',
          name: '收货信息',
          slot: true,
        },
        {
          index: 'orderCreateTimeStr',
          name: '下单时间',
        },
        {
          index: 'orderStatusName',
          name: '订单状态',
        },
      ],
      tabStatusOptions: [
        {
          tabName: '已付款',
          tabStatus: '1',
          tabCount: 0,
        }, {
          tabName: '未付款',
          tabStatus: '2',
          tabCount: 0,
        },
      ],
      activePane: '1',
      statisticsInfo: {
        purchaseMerchantNum: 0,
        purchaseOrderNum: 0,
        purchaseProductNum: 0,
        purchaseAmount: 0,
        paidPurchaseMerchantNum: 0,
        paidPurchaseOrderNum: 0,
        paidPurchaseAmount: 0,
        unpaidPurchaseMerchantNum: 0,
        unpaidPurchaseOrderNum: 0,
        unpaidPurchaseAmount: 0,
      },
      provincesOptions: [],
      orderStatusOptions: [],
      time: [],
      activityInfo: {
        activityType: '',
        marketingIdStr: '',
        marketingId: '',
        shopCode: '',
        defaultStartOrderCreateTime: '',
        defaultEndOrderCreateTime: '',
      },
      listQuery: {
        total: 0,
        page: 1,
        pageNum: 1,
        pageSize: 10,
        productCodeSearchKeyword: '',
        orderNo: '',
        merchantId: '',
        merchantName: '',
        provinceCode: -1,
        orderStatus: -1,
        startOrderCreateTime: '',
        endOrderCreateTime: '',
        logicalOrderStatus: 1,
      },
      pickerOptions: {
        disabledDate(time) {
          // 获取选中时间
          // eslint-disable-next-line no-undef
          const { timeOptionRange } = thisVue;
          // 获取时间范围(90天的毫秒数)
          const secondNum = 90 * 24 * 60 * 60 * 1000;
          if (timeOptionRange) {
            // 如果有选中时间 设置超过选中时间后的90天
            return (
              time.getTime() > timeOptionRange.getTime() + secondNum
            );
          }
        },
        onPick(maxDate) {
          // 当选中了第一个日期还没选第二个
          // 只选中一个的时候自动赋值给minDate，当选中第二个后组件自动匹配，将大小日期分别赋值给maxDate、minDate
          if (maxDate.minDate && !maxDate.maxDate) {
            // eslint-disable-next-line no-undef
            thisVue.timeOptionRange = maxDate.minDate;
          }
          // 如果有maxDate属性，表示2个日期都选择了，则重置timeOptionRange
          if (maxDate.maxDate) {
            // eslint-disable-next-line no-undef
            thisVue.timeOptionRange = null;
          }
        },
      },
    };
  },
  created() {
    listProvinces().then((res) => {
      if (res.success) {
        const { list } = res.data;
        this.provincesOptions = list || [];
      }
    });
    listOrderStatus().then((res) => {
      if (res.success) {
        const { list } = res.data;
        this.orderStatusOptions = list || [];
      }
    });
    const csuId=this.$route.query.csuId
    if(csuId){
      this.listQuery.productCodeSearchKeyword=csuId
    }
    const { activityType, marketingIdStr } = this.$route.query;
    if (activityType && marketingIdStr) {
      this.activityInfo.activityType = activityType;
      this.activityInfo.marketingIdStr = marketingIdStr;
    }
    getSearchInfo({
      activityType: this.activityInfo.activityType,
      marketingIdStr: this.activityInfo.marketingIdStr,
    }).then((res) => {
      if (res.success) {
        const { data } = res;
        if (data.searchInfo) {
          Object.keys(this.activityInfo).forEach((item) => {
            if (data.searchInfo[item]) {
              this.activityInfo[item] = data.searchInfo[item];
            }
          });
          this.listQuery.startOrderCreateTime = data.searchInfo.defaultStartOrderCreateTime || '';
          this.listQuery.endOrderCreateTime = data.searchInfo.defaultEndOrderCreateTime || '';
          this.time = [data.searchInfo.defaultStartOrderCreateTime || '', data.searchInfo.defaultEndOrderCreateTime || ''];
          this.handleSearch();
        }
      }
    });
  },
  activated() {
    this.listQuery = {
      total: 0,
      page: 1,
      pageNum: 1,
      pageSize: 10,
      productCodeSearchKeyword: '',
      orderNo: '',
      merchantId: '',
      merchantName: '',
      provinceCode: -1,
      orderStatus: -1,
      startOrderCreateTime: '',
      endOrderCreateTime: '',
      logicalOrderStatus: 1,
    };
    this.time = [];
    const csuId=this.$route.query.csuId
    if(csuId){
      this.listQuery.productCodeSearchKeyword=csuId
    }
    const { activityType, marketingIdStr } = this.$route.query;
    if (activityType && marketingIdStr) {
      this.activityInfo.activityType = activityType;
      this.activityInfo.marketingIdStr = marketingIdStr;
    }
    getSearchInfo({
      activityType: this.activityInfo.activityType,
      marketingIdStr: this.activityInfo.marketingIdStr,
    }).then((res) => {
      if (res.success) {
        const { data } = res;
        if (data.searchInfo) {
          Object.keys(this.activityInfo).forEach((item) => {
            if (data.searchInfo[item]) {
              this.activityInfo[item] = data.searchInfo[item];
            }
          });
          this.listQuery.startOrderCreateTime = data.searchInfo.defaultStartOrderCreateTime || '';
          this.listQuery.endOrderCreateTime = data.searchInfo.defaultEndOrderCreateTime || '';
          this.time = [data.searchInfo.defaultStartOrderCreateTime || '', data.searchInfo.defaultEndOrderCreateTime || ''];
          this.handleSearch();
        }
      }
    });
  },
  mounted() {
    window.thisVue = this;
  },
  methods: {
    handleBack() {
      this.$router.go(-1);
    },
    handleGoOrderList(orderNo) {
      // this.$router.push({ path: '/orderList', query: { orderNoFromGroupSalesData: orderNo } });
      window.openTab('/orderList', { orderNoFromGroupSalesData: orderNo });
    },
    handleSearch() {
      this.getStatisticsInfo();
      this.getStatusCountInfo();
      this.getList(this.listQuery, true);
    },
    tabHandleClick() {
      this.listQuery.logicalOrderStatus = this.activePane;
      this.getList(this.listQuery, true);
    },
    getStatusCountInfo() {
      getStatusCountInfo(Object.assign(this.listQuery, this.activityInfo)).then((res) => {
        if (res.success) {
          const { statusCountInfo } = res.data;
          this.tabStatusOptions[0].tabCount = statusCountInfo.paidCount;
          this.tabStatusOptions[1].tabCount = statusCountInfo.unpaidCount;
        }
      });
    },
    getStatisticsInfo() {
      getStatisticsInfo(Object.assign(this.listQuery, this.activityInfo)).then((res) => {
        if (res.success) {
          const { statisticsInfo } = res.data;
          if (statisticsInfo) {
            this.statisticsInfo = statisticsInfo;
          }
        }
      });
    },
    handleExport() {
      exportData(Object.assign(this.listQuery, this.activityInfo)).then((res) => {
        if (!res.success) {
          this.$message.error(res.msg);
          return false;
        }
        this.changeExport = true;
      });
    },
    handleChangeTime(val) {
      this.listQuery.startOrderCreateTime = val ? val[0] : '';
      this.listQuery.endOrderCreateTime = val ? val[1] : '';
    },
    reset() {
      this.listQuery = {
        total: 0,
        page: 1,
        pageNum: 1,
        pageSize: 10,
        productCodeSearchKeyword: '',
        orderNo: '',
        merchantId: '',
        merchantName: '',
        provinceCode: -1,
        orderStatus: -1,
        startOrderCreateTime: this.activityInfo.defaultStartOrderCreateTime || '',
        endOrderCreateTime: this.activityInfo.defaultEndOrderCreateTime || '',
        logicalOrderStatus: 1,
      };
      this.time = [this.activityInfo.defaultStartOrderCreateTime || '', this.activityInfo.defaultEndOrderCreateTime || ''];
      this.handleSearch();
    },
    getList(listQuery, reset) {
      const loading = this.$loading({
        lock: true,
        text: 'Loading',
        spinner: 'el-icon-loading',
        background: 'rgba(255,255,255, 0.8)',
      });
      const { page, pageSize } = listQuery;
      this.listQuery.pageSize = pageSize;
      this.listQuery.pageNum = reset ? 1 : page;
      this.listQuery.page = reset ? 1 : page;
      getSalesDataList(Object.assign(this.listQuery, this.activityInfo)).then((res) => {
        loading.close();
        if (res.success) {
          const { data } = res;
          this.list = data.pageInfo.list || [];
          this.listQuery = {
            ...this.listQuery,
            total: data.pageInfo.total,
            page: data.pageInfo.pageNum,
            pageNum: data.pageInfo.pageNum,
          };
        } else {
          this.$message.error(res.msg || '请求失败');
        }
      }).catch(() => {
        loading.close();
      });
    },
    handleChangeExport(info) {
      this.changeExport = false;
      if (info === 'go') {
        const path = '/downloadList';
        window.openTab(path);
        // that.$router.push({ path: '/downloadList' });
      }
    },
    handleExoprClose() {
      this.changeExport = false;
    },
  },
};
</script>
<style scoped lang="scss">

.line-div {
  padding: 15px 20px;
  // border-bottom: 1px solid #efefef;
  .search-title {
    display: table-cell;
    padding: 0 10px;
    text-align: center;
    border: 1px solid #dcdfe6;
    height: 30px;
    line-height: 30px;
    vertical-align: middle;
    border-right: none;
    border-radius: 4px 0 0 4px;
    color: #333333;
    white-space: nowrap;
  }
  .serch {
    font-weight: bold;
    .sign {
      display: inline-table;
      width: 3px;
      height: 13px;
      background: linear-gradient(360deg, #1d69c4 1%, #8bbdfc 99%);
      border-radius: 2px;
      margin-right: 8px;
    }
  }
  .my-search-row ::v-deep  .el-input__inner {
    border-radius: 0 4px 4px 0;
  }
  .my-search-row ::v-deep  .el-select {
    display: table-cell;
  }
  .my-search-row ::v-deep  .el-input__inner {
    border-radius: 0 4px 4px 0;
  }
  .my-search-row {
    .time-box {
      display: table-cell;
      width: 100%;
      .time-text {
        padding: 0 5px;
      }
    }
    .picker-time {
      width: 45%;
    }
  }
  .my-search-row ::v-deep  .el-select {
    display: table-cell;
    width: 100%;
  }
  .box-div {
    padding-top: 15px;
  }
  ::v-deep  .el-input-group__prepend {
    background: none;
    color: #333333;
    padding: 0 10px;
  }
  .texts {
    padding: 8px;
    background: #f9f9f9;
    border-radius: 2px;
    font-size: 14px;
    font-family: PingFangSC, PingFangSC-Regular;
    font-weight: 400;
    color: #666666;
    margin: 15px 0 25px;
  }
  .countBox {
    background: #ff4d4f;
    border-radius: 11px;
    color: #fff;
    font-size: 12px;
    margin-left: 2px;
    padding: 0 8px;
  }
  ::v-deep   .el-table tr td .cell {
    height: auto;
    line-height: normal;
    text-overflow: inherit;
    white-space: break-spaces;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    white-space: break-spaces;
    font-size: 12px;
    font-family: PingFangSC, PingFangSC-Regular;
    font-weight: 400;
    text-align: left;
    line-height: 22px;
    padding: 8px 16px;
    .el-radio-group {
      text-align: left;
    }
    p {
      margin: 0;
    }
  }
}
</style>
