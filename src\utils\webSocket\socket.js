
  /**
   *
   * @param { String } url
   * @returns { { sendMsg: (target: string, data: any, keep: Boolean, callback: (data: any) => void) => void, getMsg: (target: string, keep: Boolean, callback: (data: any) => void) => void } }
   */
  export const initWebSocket = (url) => {
    if (!url) {
      return {
        sendMsg: () => {},
        getMsg: () => {}
      }
    }
    const infomationBucket = {}; //一次性消息
    const keepInfomationBucket = {}; //永久消息
    const baseData = {
      merchantId: getCookie('org_id'),
      deviceId: getCookie('web_socket_token'),
      token: getCookie('web_socket_token'),
    }
    const maxHeartCount = 6;
    let worker = null;
    let socket = {};
    let count = maxHeartCount;
    let sendMsgBucket = [];   //发送消息队列
    /**
     * @param { String } target
     * @param { any } data
     * @param { Boolean } keep //是否永久接收消息
     * @param { (data: any) => void } callback
     */
    const sendMsg = (target, data, keep, callback) => {
      if (socket.readyState === socket.OPEN) {
        if (keep) {
          keepInfomationBucket[target] = callback;
        } else {
          infomationBucket[target] = callback;
        }
        socket.send(JSON.stringify({ ...baseData, messageType: 15, data: data, notificationType: target }));
      } else {
        sendMsgBucket.push(() => {
          if (keep) {
            keepInfomationBucket[target] = callback;
          } else {
            infomationBucket[target] = callback;
          }
          socket.send(JSON.stringify({ ...baseData, messageType: 15, data: data, notificationType: target }));
        })
      }
    }
    const getMsg = (target, keep, callback) => {
      if (!keep) {
        infomationBucket[target] = callback;
      } else {
        keepInfomationBucket[target] = callback;
      }
    }
    const heartbeat = () => {
      if (count <= 0) {
        //断连
        console.log('WebSocket 断开')
        count = maxHeartCount;
        worker.postMessage({ type: 'close' })
        worker.terminate();
        socket.close();
        //重连
        connect();
        return;
      }
      count--;
      socket.send(JSON.stringify({ ...baseData, messageType: 12 }))
    }
    const connect = () => {
      console.log('socket 连接');
      socket = new WebSocket(url);
      addListener(socket);
    }
    const addListener = (socket) => {
      socket.onopen = () => {
        console.log('连接成功')
        //权限校验
        socket.send(JSON.stringify({...baseData, messageType: 10}))
      }
      socket.onerror = (error) => {
        console.log('WebSocket 发生了错误', error)
      }
      socket.onmessage = (event) => {

        const data = JSON.parse(event.data)
        if (data.messageType === 31) {
          console.log('权限校验失败')
          //权限验证失败
        } else if (data.messageType === 21) {
          //权限验证成功
          //开启一个worker
          if (worker) {
            worker.terminate();
          }
          worker = createHeartBeatWorker();
          worker.postMessage({ type: 'openHeartbeat' });
          worker.onmessage = (e) => {
            if (e.data.type === 'heartbeat') {
              heartbeat();
            }
          }
          sendMsgBucket.forEach(item => item())
        } else if (data.messageType === 22){
          //正式消息
          if (infomationBucket[data.notificationType]) {
            infomationBucket[data.notificationType](data.notification);
            delete infomationBucket[data.notificationType];
          } else if (keepInfomationBucket[data.notificationType]) {
            keepInfomationBucket[data.notificationType](data.notification);
          }
        } else if (data.messageType === 20) {
          //心跳回复
          count = maxHeartCount;
        }

      }
    }
    connect();
    return { sendMsg, getMsg };
  }

  const createHeartBeatWorker = () => {
    return new Worker('static/js/worker.js');
  }

  const getCookie = (key) => {
    console.log(document.cookie);

    const arr = document.cookie.split(`${key}=`);
    if (arr.length <= 1) return '';
    return arr[1].split(';')[0];
  }
