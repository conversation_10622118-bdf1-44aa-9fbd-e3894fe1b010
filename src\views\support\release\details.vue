<template>
  <div class="divBox">
    <div class="bottom-info">
      <div class="topBox">
        <span v-if="formModel.isEdit == 1">商品详情</span>
        <span v-else>商品编辑</span>
        <el-button
          v-if="formModel.isEdit == 1"
          size="small"
          type="primary"
          @click="onCancel"
          >关闭</el-button
        >
        <div v-else>
          <el-button size="small" @click="onCancel">取消</el-button>
          <el-button size="small" type="primary" @click="onSubmit"
            >提交</el-button
          >
        </div>
      </div>
      <div class="tabBox">
        <div
          v-for="(item, index) in tabList"
          :class="clickIndex === index ? 'active' : ''"
          @click="tabClick(item.idName, index)"
        >
          {{ item.name }}
        </div>
      </div>
    </div>
    <div class="conBox">
      <div id="basic" class="contentBox">
        <div class="title">基本信息</div>
        <goodsBasicCom
          :basic-data="allData"
          :form-model="formModel"
          ref="goodsBasicComp"
        ></goodsBasicCom>
      </div>
      <div id="price" class="contentBox">
        <div class="title">价格库存</div>
        <priceInventory
          :basic-data="allData"
          :form-model="formModel"
          ref="priceInventoryComp"
        ></priceInventory>
      </div>
      <!-- <div id="purchase" class="contentBox">
        <div class="title">起购限购</div>
        <upForPurchase
          :basic-data="allData"
          :form-model="formModel"
          ref="upForPurchaseComp"
        ></upForPurchase>
      </div>
      <div id="supply" class="contentBox">
        <div class="title">供货信息</div>
        <supplyInformation
          :basic-data="allData"
          :form-model="formModel"
          ref="supplyInformationComp"
        ></supplyInformation>
      </div> -->
      <div id="other" class="contentBox">
        <div class="title">其他信息</div>
        <otherInformation
          :basic-data="allData"
          :form-model="formModel"
          ref="otherInformationComp"
        ></otherInformation>
      </div>
    </div>
    <el-dialog
      title="信息"
      width="400px"
      :visible.sync="visible"
      :modal-append-to-body="false"
      :close-on-click-modal="false"
      @close="onCancel"
    >
      提交成功，可在"商品列表"-
      <span>"{{ statusName }}"</span>查看。
      <el-button
        style="color: #4184d5"
        size="small"
        type="text"
        @click="toProduct"
        >点击查看</el-button
      >

      <span slot="footer">
        <el-button
          size="medium"
          style="margin-left: 20px"
          type="primary"
          @click="onCancel"
          >关闭</el-button
        >
      </span>
    </el-dialog>
  </div>
</template>

<script>
// import {
//   apiEditSku,
//   apiProductInit,
//   apiAddSku,
//   loadStatusCounts
// } from '@/api/product'
import {getSupportEdit,getSupportDetail} from '@/api/support'
import goodsBasicCom from '@/views/support/components/goodsBasicCom'
import priceInventory from '@/views/support/components/priceInventory'
import upForPurchase from '@/views/support/components/upForPurchase'
import supplyInformation from '@/views/support/components/supplyInformation'
import otherInformation from '@/views/support/components/otherInformation'

export default {
  name: 'Details',
  components: {
    goodsBasicCom,
    priceInventory,
    upForPurchase,
    supplyInformation,
    otherInformation
  },
  data() {
    return {
      clickIndex: 0,
      formModel: {},
      allData: {},
      visible: false,
      statusName: '',
      statusType: '',
      barcode: '',
      productStatusOptions: [],
      tabList: [
        { name: '基础信息', idName: 'basic' },
        { name: '价格库存', idName: 'price' },
        // { name: '起购限购', idName: 'purchase' },
        // { name: '供货信息', idName: 'supply' },
        { name: '其它信息', idName: 'other' }
      ],
      formRefs: [
        'goodsBasicComVo',
        'priceInventoryVo',
        'upForPurchaseVo',
        'supplyInformationVo',
        'otherInformationVo'
      ],
      paramsId:{
        categoryFirstId:''
      }
    }
  },
  created() {
    this.loadStatusCounts()
  },
  activated() {
    this.activate()
  },
  methods: {
    activate() {
      const { query } = this.$route
      console.log('query',query);
        this.paramsId.categoryFirstId = query.categoryFirstId
      if (query && Object.keys(query).length > 0) {
        this.formModel = {}
        this.allData = {}
        this.visible = false
        this.statusName = ''
        this.statusType = ''
        this.formModel = Object.assign({}, this.formModel, query)
        if (Object.prototype.hasOwnProperty.call(this.formModel, 'from')) {
          if (this.formModel.from === 'productList') {
            // 详情
            this.getDetail()
          } else if (
            this.formModel.from === 'supplement' ||
            this.formModel.from === 'create' ||
            this.formModel.from === 'initialize'
          ) {
            // 批量发布-补充，重新创建
            this.getProductInit()
          }
        }
      }
    },
    loadingFun() {
      const loading = this.$loading({
        lock: true,
        text: 'Loading',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      return loading
    },
    async loadStatusCounts() {
      try {
        const params = {}
        const res = await loadStatusCounts(params)
        console.log('res', res)
        if (res) {
          this.productStatusOptions = [
            {
              statusName: '全部',
              statusType: -99
            },
            ...res
          ]
        }
      } catch (e) {
        console.log(e)
      }
    },
    statusNameStr(value) {
      let name = ''
      this.productStatusOptions.forEach((item) => {
        if (item && Number(item.statusType) === Number(value)) {
          name = item.statusName
        }
      })
      return name || ''
    },
    getDetail() {
      const params = { id: this.formModel.id }
      const load = this.loadingFun()
      getSupportDetail(params).then((res) => {
        load.close()
        if (res.code === 0) {
          if (res.data) {
            console.log('--allData--', res.data)
            this.allData = { ...res.data,...this.paramsId }
            console.log(this.paramsId);
          } else {
            this.$message.error('获取商品信息失败')
          }
        } else {
          this.$message.error(res.message)
        }
      })

    },
    getProductInit() {
      const params = {
        erpFirstCategoryId: this.formModel.erpFirstCategoryId,
        erpSecondCategoryId: this.formModel.erpSecondCategoryId,
        erpThirdCategoryId: this.formModel.erpThirdCategoryId,
        erpFourthCategoryId: this.formModel.erpFourthCategoryId,
        standardProductId: this.formModel.standardProductId,
        erpSkuId: this.formModel.erpSkuId,
        type: this.formModel.from === 'create' ? 1 : null
      }
      // if(this.formModel.from !== "create"){
      //   params.erpSkuId = this.formModel.erpSkuId;
      // }
      const load = this.loadingFun()
      apiProductInit(params).then((res) => {
        load.close()
        if (res.code === 0) {
          if (res.data) {
            this.allData = { ...res.data }
          } else {
            this.$message.error('获取商品信息失败')
          }
        } else {
          this.$message.error(res.message)
        }
      })
    },
    onSubmit() {
      const that = this
      // 获取到组件中的form
      const goodsBasicForm = this.$refs.goodsBasicComp.$refs.goodsBasicComVo
      const priceInventoryForm =
        this.$refs.priceInventoryComp.$refs.priceInventoryVo

      const otherInformationForm =
        this.$refs.otherInformationComp.$refs.otherInformationVo

      const errStr = this.$refs.priceInventoryComp.checkData()
      console.log('errStr', errStr)
      if (errStr) {
        this.$message.error(errStr)
        return false
      }
      // 使用Promise.all去校验结果
      Promise.all(
        [
          goodsBasicForm,
          priceInventoryForm,
          otherInformationForm
        ].map(this.getFormPromise)
      ).then((res) => {
        console.log('all', res)
        const validateResult = res.every((item) => !!item)
        if (validateResult) {
          console.log('都校验通过')
          const params = this.getParams(
            goodsBasicForm.model,
            priceInventoryForm.model,
            otherInformationForm.model
          )
          console.log('params', params)
          console.log(1112222, params)
          if (params.isInstrument) {
            if (
              params.instrumentLicenseImageList.length &&
              params.instrumentLicenseEffect === ''
            ) {
              this.$message.warning(
                '医疗器械注册证及其有效期至需同时填写或同时不填写，请修改后提交'
              )
              return false
            }
            if (
              !params.instrumentLicenseImageList.length &&
              params.instrumentLicenseEffect
            ) {
              this.$message.warning(
                '医疗器械注册证及其有效期至需同时填写或同时不填写，请修改后提交'
              )
              return false
            }
            // 如果 医疗器械注册证或备案凭证编号 含"国械备|进|许" 则不校验 生产许可证号或备案凭证编号 相关
            if (new RegExp('国械备|进|许').test(params.instrumentNumber)) {

              console.log(
                '医疗器械注册证或备案凭证编号 含"国械备|进|许" 则不校验 生产许可证号或备案凭证编号 相关'
              )
            } else {
              if (
                params.manufacturingLicenseImageList.length &&
                params.manufacturingLicenseEffect === ''
              ) {
                this.$message.warning(
                  '生产许可证书及其有效期至需同时填写或同时不填写，请补充后提交'
                )
                return false
              }
              if (
                !params.manufacturingLicenseImageList.length &&
                params.manufacturingLicenseEffect
              ) {
                this.$message.warning(
                  '生产许可证书及其有效期至需同时填写或同时不填写，请补充后提交'
                )
                return false
              }
            }

            if (params.newProDate && params.instrumentLicenseEffect) {
              if (!params.newProDate < params.instrumentLicenseEffect) {
                this.$message.warning(
                  '最新生产日期需小于医疗器械注册证的有效期至'
                )
                return false
              }
            }
            if (params.newProDate && params.manufacturingLicenseEffect) {
              if (!params.newProDate < params.manufacturingLicenseEffect) {
                this.$message.warning('最新生产日期需小于生产许可证的有效期至')
                return false
              }
            }
            // this.goodsBasicComVo.instrumentImageValid = this.propsData.instrumentImageValid || true;
            // this.goodsBasicComVo.manufacturingImageValid = this.propsData.manufacturingImageValid || true;
            if (
              params.instrumentImageValid &&
              !params.instrumentLicenseImageList.length
            ) {
              this.$message.warning('医疗器械注册证及有效期至不能为空')
              return false
            }
            if (
              params.manufacturingImageValid &&
              !params.manufacturingLicenseImageList.length
            ) {
              this.$message.warning('生产许可证书及有效期至不能为空')
              return false
            }
          }
          if (that.formModel.from === 'productList') {
            this.editSku(params)
            console.log('66666');
          } else {
            console.log('7777');
            params.erpSkuId = that.formModel.erpSkuId
            params.imageUrlStandard = this.allData.imageUrlStandard
            params.instrutionImageUrlStandard =
              this.allData.instrutionImageUrlStandard
            this.addSku(params)
          }
        } else {
          console.log('未校验通过')
          this.$message.error('部分必填项未填写或填写不规范')
        }
      })
    },
    addSku(params) {
      const load = this.loadingFun()
      apiAddSku(params).then((res) => {
        load.close()
        if (res.code === 0) {
          // 批量
          if (
            this.formModel.from === 'supplement' ||
            this.formModel.from === 'create'
          ) {
            this.barcode = res.data.barcode
            this.statusType = res.data.status || -99
            this.statusName = this.statusNameStr(res.data.status || -99)
            this.visible = true
          } else {
            this.$message.success('发布成功')
            this.onCancel()
          }
        } else {
          this.$message.error(res.message)
        }
      })
    },
    editSku(params) {
      const load = this.loadingFun()
      // 代码中是数组[] 重新赋值改为String 传空 目前没用到
      getSupportEdit({...params,userType:'',userName:'',userTypeName:'',id:this.formModel.id})
        .then((res) => {
          load.close()
          if (res.code === 0) {
            this.$message.success('发布成功')
            this.onCancel()
          } else {
            this.$message.error(res.message)
          }
        })
        .catch(() => {
          load.close()
        })
    },
    toProduct() {
      if (
        this.formModel.from === 'supplement' ||
        this.formModel.from === 'create'
      ) {
        const obj = {}
        obj.status = this.statusType
        obj.barcode = this.barcode
        const path = '/productList'
        window.openTab(path, obj)
        // 批量
        // setTimeout(() => {
        //   const path = '/batchGoods'
        //   window.closeTab(path)
        // }, 100);
      }
      // else if(this.formModel.from === "initialize"){
      //   const obj = {}
      //   const path = '/productList'
      //   window.openTab(path, obj)
      //   // 自建
      //   setTimeout(() => {
      //     const path = '/product/initialize'
      //     window.closeTab(path);
      //   }, 100);
      // }
      setTimeout(() => {
        this.onCancel()
      }, 200)
    },
    tabClick(name, index) {
      this.clickIndex = index
      document.querySelector(`#${name}`).scrollIntoView(true)
    },
    onCancel() {
      this.visible = false
      let path = ''
      if (this.formModel.isEdit) {
        // path = '/support/details';
        window.closeTab('/support/details')
        // path = '/support/detailsEdit';
        window.closeTab('/support/detailsEdit')
        const path = this.$route.fullPath
        window.closeTab(path)
      }
    },
    getFormPromise(form) {
      return new Promise((resolve) => {
        console.log('form', form)
        form.validate((res) => {
          resolve(res)
        })
      })
    },
    // 拼接参数
    getParams(
      goodsBasicForm,
      priceInventoryForm,
      // upForPurchaseForm,
      // supplyInformationForm,
      otherInformationForm
    ) {
      let params = {}
      params = Object.assign(
        {},
        goodsBasicForm,
        priceInventoryForm,
        // upForPurchaseForm,
        // supplyInformationForm,
        otherInformationForm
      )

      let imagesList = []
      if (params.imagesList && params.imagesList.urlVal) {
        imagesList = params.imagesList.urlVal.map((item) => item.name)
      }

      let instrutionImagesList = []
      if (params.instrutionImagesList && params.instrutionImagesList.urlVal) {
        instrutionImagesList = params.instrutionImagesList.urlVal.map(
          (item) => item.name
        )
      }

      let instrumentLicenseImageList = []
      if (
        params.instrumentLicenseImageList &&
        params.instrumentLicenseImageList.urlVal
      ) {
        instrumentLicenseImageList =
          params.instrumentLicenseImageList.urlVal.map((item) => item.name)
      }

      let manufacturingLicenseImageList = []
      if (
        params.manufacturingLicenseImageList &&
        params.manufacturingLicenseImageList.urlVal
      ) {
        manufacturingLicenseImageList =
          params.manufacturingLicenseImageList.urlVal.map((item) => item.name)
      }

      let userType = []
      if (params.userType) {
        userType = params.userType.join(',')
      }

      let userTypeName = []
      if (params.userTypeName) {
        userTypeName = params.userTypeName.join(',')
      }

      let oldestProDate = '';
      let newProDate = '';
      if(params.opProDate){
        oldestProDate = params.opProDate[0]
        newProDate = params.opProDate[1]
      }

      let nearEffect = '';
      let farEffect = '';
      if(params.opEffect){
        nearEffect = params.opEffect[0]
        farEffect = params.opEffect[1]
      }

      let purchaseTimeStart = ''
      let purchaseTimeEnd = ''
      if (params.opPurchaseTime) {
        purchaseTimeStart = params.opPurchaseTime[0]
        purchaseTimeEnd = params.opPurchaseTime[1]
      }

      if (this.allData.erpFirstCategoryId) {
        params.erpFirstCategoryId = this.allData.erpFirstCategoryId
      }

      if (this.allData.source) {
        params.source = this.allData.source
      }

      params = Object.assign(
        {},
        params,
        { imagesList },
        { userType },
        { userTypeName },
        { instrutionImagesList },
        { instrumentLicenseImageList },
        { manufacturingLicenseImageList },
        { purchaseTimeStart },
        { purchaseTimeEnd }
      )
      return params
    }
  }
}
</script>

<style scoped lang="scss">
.divBox {
  width: 100%;
  height: 100%;
  position: relative;
  padding-top: 105px;
  .bottom-info {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 105px;
    background: #fff;
    // display: flex;
    justify-content: space-between;
    align-items: center;
    z-index: 100;
    .topBox {
      padding: 16px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      span {
        font-size: 20px;
        font-weight: 500;
        text-align: left;
        color: #333333;
      }
    }
    .tabBox {
      border-bottom: 1px solid #efefef;
      display: flex;
      align-items: center;
      div:first-child {
        margin-left: 16px;
        border-left: 1px solid #efefef;
      }
      div {
        height: 40px;
        padding: 0 18px;
        font-size: 14px;
        line-height: 40px;
        font-weight: 400;
        text-align: left;
        color: rgba(0, 0, 0, 0.65);
        text-decoration: none;
        border-right: 1px solid #efefef;
        border-top: 1px solid #efefef;
        background: rgba(239, 239, 239, 0.3);
        cursor: pointer;
      }
      div.active {
        border-bottom: none;
        background: #ffffff;
        opacity: 1;
        color: #1890ff;
      }
    }
  }

  .conBox {
    width: 100%;
    height: 100%;
    overflow-y: auto;
  }

  .conBox::-webkit-scrollbar {
    width: 0 !important;
  }

  .contentBox {
    //height: 100%;
    padding: 16px 16px;
    background: #fff;

    .title {
      font-weight: 500;
      text-align: left;
      color: #000000;
      line-height: 14px;
      margin-bottom: 24px;
    }

    .title:before {
      content: '';
      display: inline-block;
      width: 3px;
      height: 13px;
      background: linear-gradient(360deg, #1d69c4 1%, #8bbdfc 99%);
      border-radius: 2px;
      margin-right: 8px;
      vertical-align: middle;
    }
  }
}
</style>
