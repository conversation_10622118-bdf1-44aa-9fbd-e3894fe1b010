<template>
  <div class="baseInfoBox">
    <div v-for="(item, index) in bussinessList" :key="item.orgId" class="bussinessItem">
      <div class="baseBox">
        <div class="nameTop">
          <div class="nameBox">
            <img class="shopNameIcon" src="../../assets/h5/shopIcon.png" alt="">
            <span class="nameTitle">{{ item.companyName }}</span>
          </div>
          <div class="btnBox" @click="showOrHide(index)">
            <span class="btn">{{ item.showMore ? '收起' : '展开' }}</span>
            <img class="btnIcon" :class="!item.showMore ? 'btnIcon2' : ''" src="../../assets/h5/more.png" alt="">
          </div>
        </div>
        <div class="commonTop">企业编码：{{ item.orgId }}</div>
        <div class="commonTop">店铺名称：{{ item.name }}</div>
      </div>
      <div class="detailInfo" v-show="item.showMore">
        <div class="itemTitle">
          <div class="leftBorder"></div>
          服务质量
          <span class="titleTip">（近7天）</span>
        </div>
        <div class="numberBox">
          <div class="itemBox">
            <div class="valueText">{{ item.serviceQuality.sendNum }}%</div>
            <div class="labelText">48小时发货率</div>
          </div>
          <div class="itemBox">
            <div class="valueText">{{ item.serviceQuality.respondCt }}%</div>
            <div class="labelText">客服在线响应率</div>
          </div>
          <div class="itemBox">
            <div class="valueText">{{ item.serviceQuality.refundNum }}%</div>
            <div class="labelText">商家原因退款率</div>
          </div>
          <div class="itemBox">
            <div class="valueText">{{ item.serviceQuality.refundOrderNum }}%</div>
            <div class="labelText">商家原因退货率</div>
          </div>
        </div>
        
      </div>
    </div>
  </div>
</template>
<script>
import { listCorpServiceByPoiId } from '../../api/storeManagement/index';
export default {
  name: 'ServiceQuality',
  data() {
    return {
      bussinessList: [],
    };
  },
  created() {
    this.getBascInfo();
  },
  methods: {
    getBascInfo() {
      const poiId = this.util.getUrlParam('poiId');
      listCorpServiceByPoiId({ poiId }).then((res) => {
        this.bussinessList = res.data || [];
        this.bussinessList.forEach((item, index) => {
          if (index === 0) {
            this.$set(item, 'showMore', true);
          } else {
            this.$set(item, 'showMore', false);
          }
        })
      })
    },
    showOrHide(actIndex) {
      this.bussinessList.forEach((item, index) => {
        if (index === actIndex) {
          item.showMore = !item.showMore;
        } else {
          item.showMore = false;
        }
      })
    }
  },
};
</script>

<style scoped lang="scss">
  .baseInfoBox {
    margin: 0 0.2rem;
    .bussinessItem {
      background: #fff;
      border-radius: 0.2rem;
      padding: 0.2rem;
      margin-bottom: 0.2rem;
      .baseBox {
        .nameTop {
          display: flex;
          align-items: center;
          justify-content: space-between;
          .nameBox, .btnBox {
            display: flex;
            align-items: center;
          }
          .shopNameIcon {
            width: 0.4rem;
            height: 0.4rem;
            margin-right: 4px;
          }
          .nameTitle {
            color: #333333;
            font-weight: bold;
            font-size: 0.28rem;
          }
          .btn {
            color: #9494A6;
            font-size: 0.28rem;
            margin-right: 4px;
          }
          .btnIcon {
            width: 0.26rem;
            height: 0.26rem;
          }
          .btnIcon2 {
            transform: rotate(180deg);
          }
        }
        .commonTop {
          color: #292933;
          font-size: 0.2rem;
          margin-top: 0.1rem;
        }
      }
      .detailInfo {
        background: #fff;
        border-top: 1px solid #EEEFF0;
        margin-top: 0.2rem;
        padding-top: 0.2rem;
        .itemTitle {
          display: flex;
          align-items: center;
          color: #292933;
          margin-bottom: 0.2rem;
          font-weight: bold;
          .leftBorder {
            width: 0.06rem;
            height: 0.4rem;
            background: #00B377;
            border-radius: 3.5px;
            margin-right: 0.1rem;
          }
          .titleTip {
            color: #9494A6;
            font-size: 0.2rem;
          }
        }
        .numberBox {
          display: flex;
          align-items: center;
          justify-content: space-around;
          .itemBox {
            text-align: center;
            .valueText {
              color: #292933;
              font-weight: bold;
              font-size: 0.36rem;
            }
            .labelText {
              color: #676773;
              font-size: 0.2rem;
              width: 1rem;
              margin-top: 0.1rem;
            }
          }
        }
      }
    }
  }
</style>
