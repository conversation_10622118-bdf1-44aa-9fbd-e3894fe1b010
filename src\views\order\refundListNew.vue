<template>
  <div class="divBox">
    <div class="orderTitle">
      <span>申请退款</span>
      <el-button type="primary" size="small" @click="goBack"> 返回 </el-button>
    </div>
    <div style="padding: 15px 0; border-bottom: 1px solid #dddddd; border-top: 1px solid #dddddd">
      <el-row>
        <span>注意：发起退款前请务必与客户沟通，避免引起客诉</span>
      </el-row>
      <el-row class="paddingDiv">
        <span>订单编号：{{ orderNo }}</span>
        <span style="padding-left: 30px">客户名称：{{ merchantName }}</span>
      </el-row>
    </div>
    <el-row style="padding-top: 20px">
      <div>
        <!-- 退款类型 -->
        <el-form
          label-width="140px"
          :rules="rules.refundTypeRules"
          ref="refundTypeForm"
          :model="subForm.refundTypeForm"
        >
          <el-form-item label="退款类型：" prop="refundType" key="refundType">
            <el-radio-group
              v-model="subForm.refundTypeForm.refundType"
              @change="changeRefundType"

            >
              <el-radio label="2">仅退款（无需退货）</el-radio>
              <el-radio label="1">退货退款</el-radio>
              <el-radio label="3">退运费</el-radio>
              <el-radio label="4">小额打款</el-radio>
            </el-radio-group>
            <div
              v-if="
                subForm.refundTypeForm.refundType == 2 || subForm.refundTypeForm.refundType == 1
              "
              style="color: red"
            >
              该订单申请退款被用户拒绝2次后，再次重复申请将会附带额外赔付（货款的{{ pageData.merchantInitiatedAdditionalCompensationRate * 100 }}%，100元封顶）,请及时与客户沟通，避免不必要的成本！
            </div>
          </el-form-item>
        </el-form>
        <!-- 仅退款（无需退货）/退货退款 -->
        <template
          v-if="subForm.refundTypeForm.refundType == 2 || subForm.refundTypeForm.refundType == 1"
        >
          <!-- 选择退货退款时，校验当前订单状态必须为配送中/已完成，如不是这两个状态，提示‘订单还未发货，无可退货数量’ -->
          <span
            v-if="
              subForm.refundTypeForm.refundType == 1 &&
              pageData.curOrderStatus != 2 &&
              pageData.curOrderStatus != 3
            "
          >
            订单还未发货，无可退货数量
          </span>
          <template v-else>
            <!-- 仅退款（无需退货）/退货退款 表单 -->
            <el-form
              label-width="140px"
              :rules="rules.refundRules"
              ref="refundForm"
              :model="subForm.refundForm"
            >
              <el-form-item label="是否退运费：" prop="isRefundShipFee" key="isRefundShipFee">
                <el-select
                  v-model="subForm.refundForm.isRefundShipFee"
                  placeholder="请选择"
                  size="small"
                >
                  <el-option label="是" :value="true" />
                  <el-option label="否" :value="false" />
                </el-select>
                <span v-if="subForm.refundForm.isRefundShipFee" style="margin-left: 10px">
                  订单运费
                  <span style="color: red">{{ pageData.freightRefundAmount }}</span
                  >元
                </span>
              </el-form-item>
              <el-form-item label="退款原因：" prop="refundReason" key="refundReason">
                <el-cascader
                  v-model="subForm.refundForm.refundReason"
                  :options="plainList"
                  :props="{ label: 'showText', value: 'showText', checkStrictly: true }"
                  :show-all-levels="false"
                  clearable
                />
                <!-- <el-select
                  v-model="subForm.refundForm.refundReason"
                  placeholder="请选择退款原因"
                  size="small"
                >
                  <el-option
                    v-for="(item, index) in plainList"
                    :key="index"
                    :label="item.showText"
                    :value="item.showText"
                  />
                </el-select> -->
              </el-form-item>
              <el-form-item label="退款说明：" prop="refundInstructions" key="refundInstructions">
                <el-input
                  v-model="subForm.refundForm.refundInstructions"
                  style="width: 300px"
                  type="textarea"
                  placeholder="300字以内"
                  maxlength="300"
                  show-word-limit
                />
              </el-form-item>
              <div style="display: flex">
                <el-form-item
                  label="额外赔付："
                  prop="additionalCompensation"
                  key="additionalCompensation"
                >
                  <el-input
                    v-model="subForm.refundForm.additionalCompensation"
                    style="width: 300px"
                    type="number"
                    :max="maxAdditionalCompensation"
                    :min="0"
                  />
                </el-form-item>
                <div style="margin-left: 10px">
                  赔付额度不可高于
                  <span style="color: red">{{ maxAdditionalCompensation }}</span>
                  元，赔付额度将从<span style="color: red">营销服务额度</span>扣除<br />并发放至客户购物金
                </div>
              </div>
            </el-form>
            <!-- 申请退款的商品表格 -->
            <el-row style="padding-top: 20px">
              <el-table
                ref="multipleTable"
                :data="refundData"
                border
                style="width: 100%"
                :row-key="(row) => row.orderNo"
                @selection-change="handleSelectionChange"
                @select-all="(val) => (freightRefund.checked = val.length > 0)"
              >
                <el-table-column type="selection" width="55" :reserve-selection="true" />
                <el-table-column label="商品名称" prop="productName" />
                <el-table-column label="规格" prop="spec" />
                <el-table-column label="厂家" prop="manufacturer" width="200" />
                <el-table-column label="中包装数量" prop="mediumPackageNum" />
                <el-table-column label="是否可拆零" prop="isSplitDesc" />
                <el-table-column label="采购数量" prop="productAmount" />

                <!-- 下面三个待后端于接口新增加字段 -->
                <el-table-column label="商品单价" prop="productPrice" />
                <el-table-column label="商品优惠金额" prop="discountAmount" />

                <el-table-column label="发货数量" prop="realSendNum">
                  <template slot-scope="scope">
                    <span
                      :style="scope.row.realSendNum < scope.row.productAmount ? 'color:red' : ''"
                      >{{ scope.row.realSendNum }}</span
                    >
                  </template>
                </el-table-column>
                <el-table-column label="可退数量" prop="canRefundQuantity"/>
                <el-table-column label="本次退货数量"  width="120">
                  <template slot-scope="scope">
                    <el-input
                      v-model="scope.row.thisRefundQuantity"
                      :max="scope.row.canRefundQuantity"
                      :min="0"
                      type="number"
                      size="small"
                      @change="changeNum1(scope.row)"
                    />
                  </template>
                </el-table-column>
                <el-table-column >
                  <template slot="header">
                    <span style="margin-right: 3px;">退款金额</span>
                    <el-tooltip class="item" effect="dark" content="计算公式：商品退款金额 = 商品单价*退货数量 - 商品优惠金额*(退货数量/采购数量)" placement="top-start">
                      <i class="el-icon-warning-outline"></i>
                    </el-tooltip>
                  </template>
                  <template slot-scope="scope">
                    {{ scope.row.refundFee }}
                    <!-- 后面调用接口动态赋值 -->
                    <!-- {{ (scope.row.productPrice * scope.row.thisRefundAmount).toFixed(2) }} -->
                  </template>
                </el-table-column>
              </el-table>
            </el-row>
          </template>
        </template>

        <!-- 退运费 表单 -->
        <template v-if="subForm.refundTypeForm.refundType == 3">
          <span v-if="!pageData.freightRefundType">
            该订单无待退运费，请选择其他退款方式
          </span>
          <el-form
            v-else
            label-width="140px"
            :rules="rules.shipFreeRules"
            ref="shipFreeForm"
            :model="subForm.shipFreeForm"
          >
            <el-form-item label="运费金额: " prop="shipFree" key="shipFree">
              <div>{{ pageData.freightRefundAmount }}</div>
            </el-form-item>
            <el-form-item label="退款原因：" prop="shipReason" key="shipReason">
              <el-cascader
                v-model="subForm.shipFreeForm.shipReason"
                :options="plainList"
                :props="{ label: 'showText', value: 'showText', checkStrictly: true }"
                :show-all-levels="false"
                clearable
              />
            </el-form-item>

            <el-form-item label="退款说明：" prop="shipExplain" key="shipExplain">
              <el-input
                v-model="subForm.shipFreeForm.shipExplain"
                style="width: 300px"
                type="textarea"
                placeholder="300字以内"
                maxlength="300"
                show-word-limit
              />
            </el-form-item>
          </el-form>
        </template>
        <!-- 小额打款 表单 -->
        <el-form
          v-if="subForm.refundTypeForm.refundType == 4"
          label-width="140px"
          :rules="rules.smallPayRules"
          ref="smallPayForm"
          :model="subForm.smallPayForm"
        >
          <div style="display: flex">
            <el-form-item label="打款金额: " prop="payAmount" key="payAmount">
              <el-input
                v-model="subForm.smallPayForm.payAmount"
                style="width: 300px"
                type="number"
                :max="pageData.smallAmountPayment"
                :min="0"
              />
            </el-form-item>
            <div style="margin-left: 10px">
              <template>
                打款金额不可高于 <span style="color: red">{{ pageData.smallAmountPayment }}</span> 元 <br />
                客户确认后，打款额度将发放至客户购物金
              </template>
            </div>
          </div>
          <el-form-item label="打款原因：" prop="payReason" key="payReason">
            <el-select v-model="subForm.smallPayForm.payReason" placeholder="请选择" size="small">
              <el-option
                v-for="(item, index) in plainList"
                :key="index"
                :label="item.showText"
                :value="item.showText"
              />
            </el-select>
          </el-form-item>

          <el-form-item label="打款说明：" prop="payExplain" key="payExplain">
            <el-input
              v-model="subForm.smallPayForm.payExplain"
              style="width: 300px"
              type="textarea"
              placeholder="300字以内"
              maxlength="300"
              show-word-limit
            />
          </el-form-item>
        </el-form>
      </div>
    </el-row>

    <el-row style="padding: 20px 0; text-align: right">
      <el-button
        v-if="
          !(subForm.refundTypeForm.refundType == 3 && pageData.freightRefundType == 0) &&
          !(
            subForm.refundTypeForm.refundType == 1 &&
            pageData.curOrderStatus != 2 &&
            pageData.curOrderStatus != 3
          )
        "
        type="primary"
        size="mini"
        @click="confirmD"
      >
        提 交
      </el-button>
    </el-row>
    <el-dialog
      :visible="refundReason.status"
      title="退款提示"
      width="400px"
      @close="refundReason.status = false"
    >
      <div style="padding: 0 20px">
        <template v-if="subForm.refundTypeForm.refundType == 4">
          <p style="display: flex; justify-content: space-between">
            <span>订单金额:{{ refundReason.orderAmount }}</span>
            <span></span>
          </p>
          <p style="display: flex; justify-content: space-between">
            <span style="color: red">小额打款金额:{{ refundReason.indemnityMoney || 0 }}</span>
            <span></span>
          </p>
        </template>
        <template v-else>
          <p style="display: flex; justify-content: space-between">
            <span>退款商品种类数:{{ refundReason.refundVarietyNum }}</span>
            <span>退款商品总数量:{{ refundReason.refundTotalCountDecimal }}</span>
          </p>
          <p style="display: flex; justify-content: space-between">
            <span>商品退款金额:{{ refundReason.skuRefundFee }}</span>
            <span style="color: red">运费退款金额:{{ refundReason.freightRefundFee }}</span>
          </p>
          <p style="display: flex; justify-content: space-between">
            <span style="color: red">退款总金额:{{ refundReason.refundFee }}</span>
            <span></span>
          </p>
          <p style="display: flex; justify-content: space-between">
            <span style="color: red">
              额外赔偿金额:{{ refundReason.indemnityMoney || 0 }} 
              <template v-if="refundReason.indemnityMoney">({{ refundReason.indemnitySource==1 ? '营销服务额度' : '保证金'}})</template>
            </span>
            <span></span>
          </p>
        </template>
      </div>
      <div slot="footer">
        <el-button
          type="primary"
          size="small"
          @click="
            sendData(refundReason.params)
            refundReason.status = false
          "
          >确定</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  queryCanRefundProductDetail,
  listRefundReason,
  getOrderRefundAmount,
  submitApplyRefundBySeller,
  getOrderRefundDetailAmount,
  getAfterSaleRefundConfigProperties
} from '@/api/order/index'

export default {
  name: 'orderListRefundNew',
  props: {
    // orderNo: { type: String, default: '' }, // 订单编号
    // refundData: [], // 退款表格数据
    // plainList: [], // 退款原因列表
    activeOrderStatus: {
      type: String,
      default: '',
    },
  },
  computed: {
    maxAdditionalCompensation() {
      let refundFee = 0
      // refundFee += this.subForm.refundForm.isRefundShipFee ? 10 : 0
      let temp1 = this.selectList?.reduce((total, item) => {
        return total + item.refundFee
      }, 0)
      let tempNum = this.pageData.merchantInitiatedAdditionalCompensationRate
      let temp2 = this.subForm.refundForm.isRefundShipFee ? (this.pageData.freightRefundType ? this.pageData.freightRefundAmount : 0) : 0

      // refundFee = ((temp1 + temp2) * tempNum > 100 ? 100 : (temp1 + temp2) * tempNum).toFixed(2)
      refundFee = ((temp1 + temp2) * tempNum).toFixed(2)

      // if (this.pageData.refuseNum >= 2) this.subForm.refundForm.additionalCompensation = refundFee

      return refundFee
    } // 最多额外赔付金额
  },
  data() {
    var checkPayAmount = (rule, value, callback) => {
      if(value === null || value === ''){
        callback()
        return
      }

      const that = this
      const regex = /^\d+(\.\d{1,2})?$/
      const amount = parseFloat(value+'')
      
      // console.log(rule)
      // const regex = /^(-?\d+(\.\d+)?)$/ const regex = /^\d+(\.\d{1,4})?$/ regex = /^(-?\d+(\.\d{1,2})?)$/

      // 如果不匹配正则表达式，说明输入不符合要求
      if (!regex.test(amount)) {
        resetValue()
        callback(new Error('输入必须为大于0的数字，不超过2位小数'))
        return
      }

      // 尝试将输入转换为浮点数
      if (isNaN(amount)) {
        resetValue()
        callback(new Error('输入的金额必须为数字'))
        return
      }

      // 如果数值合法，将数值格式化为两位小数
      const formattedValue = amount

      if (rule.field == 'payAmount') {
        if (formattedValue - this.pageData.smallAmountPayment > 0 || formattedValue === 0) {
          resetValue()
          callback(new Error('输入的金额必须大于0，且不能大于' + this.pageData.smallAmountPayment + '元'))
          return
        }
        this.subForm.smallPayForm.payAmount = formattedValue
      }

      if (rule.field == 'additionalCompensation') {
        if (formattedValue - this.maxAdditionalCompensation > 0) {
          resetValue()
          callback(new Error('输入的金额不能大于' + this.maxAdditionalCompensation + '元'))

          return
        }
        this.subForm.refundForm.additionalCompensation = formattedValue
      }

      function resetValue() {
        if (rule.field == 'additionalCompensation')
          that.subForm.refundForm.additionalCompensation = null
        if (rule.field == 'payAmount') that.subForm.smallPayForm.payAmount = null
      }
      callback()
    } // 校验两位小数金额
    return {
      pageData: {
        curType: null, // 售后类型
        curOrderStatus: 1, // 当前订单状态 2/3 配送中/已完成
        refuseNum: 3, // 该订单历史商家发起且被客户拒绝的仅退款/退货退款售后单次数
        smallAmountPayment: null, // 小额打款金额上限 售后类型为小额打款时有值
        freightRefundType: null, // 0:运费不可退 1:运费可退	
        freightRefundAmount: 0, // 运费可退金额	
        merchantInitiatedAdditionalCompensationRate: 0, // 商家主动发起额外赔偿：不超退款金额的10%
      }, // 页面数据
      subForm: {
        refundTypeForm: {
          refundType: '2' // 退款类型，默认仅退款
        },
        refundForm: {
          isRefundShipFee: true, // 是否退运费
          refundReason: '', // 退款原因
          refundInstructions: '', // 退款说明
          additionalCompensation: null // 额外赔付
        },
        smallPayForm: {
          payAmount: null, // 打款金额
          payReason: '', // 打款原因
          payExplain: '' // 打款说明
        },
        shipFreeForm: {
          shipFree: 10, // 运费金额
          shipReason: '', // 退款原因
          shipExplain: '' // 退款说明
        }
      }, // 表单集合
      rules: {
        refundTypeRules: {
          refundType: [{ required: true, message: '请选择退款类型', trigger: 'change' }]
        },
        refundRules: {
          isRefundShipFee: [{ required: true, message: '请选择是否退运费', trigger: 'change' }],
          refundReason: [{ required: true, message: '请选择退款原因', trigger: 'change' }],
          refundInstructions: [
            // { required: true, message: '请填写退款说明', trigger: 'blur' },
            { min: 0, max: 300, message: '300字以内', trigger: 'blur' }
          ],
          additionalCompensation: [{ validator: checkPayAmount, trigger: 'blur' }]
        },
        smallPayRules: {
          payAmount: [
            { required: true, message: '请填写打款金额', trigger: 'blur' },
            { validator: checkPayAmount, trigger: 'blur' }
          ], // 打款金额
          payReason: [{ required: true, message: '请选择打款原因', trigger: 'change' }], // 打款原因
          payExplain: [
            // { required: true, message: '请填写打款说明', trigger: 'blur' },
            { min: 0, max: 300, message: '300字以内', trigger: 'blur' }
          ] // 打款说明
        },
        shipFreeRules: {
          shipFree: [{ required: true, message: '请填写运费金额', trigger: 'blur' }], // 运费金额
          shipReason: [{ required: true, message: '请选择退款原因', trigger: 'change' }], // 退款原因
          shipExplain: [
            // { required: true, message: '请填写退款说明', trigger: 'blur' },
            { min: 0, max: 300, message: '300字以内', trigger: 'blur' }
          ] // 退款说明
        }
      }, // 校验规则
      // orderNo: this.orderNo,
      // refundData: this.refundData, // 退款表格数据
      // plainList: this.plainList,
      orderNo: '',
      refundData: [],
      plainList: [],
      merchantName: '',

      selectList: [],
      tipHeight: document.documentElement.clientHeight / 3,
      routerObj: '',
      refundReason: {
        status: false,
        freightRefundFee: '',
        refundFee: '',
        refundTotalCount: '',
        refundVarietyNum: '',
        skuRefundFee: '',
        params: {}
      },
      freightRefund: {
        type: 0,
        amount: '',
        checked: true
      }
    }
  },
  created() {
    // 获取页面数据
    this.queryInfo()
  },
  activated() {
    // if (this.$refs.multipleTable) {
    //   this.$refs.multipleTable.clearSelection()
    // }
    // this.selectList = []
    if (this.routerObj && JSON.stringify(this.routerObj) !== JSON.stringify(this.$route.query)) {
      this.queryInfo()
    }
  },
  methods: {
    goBack() {
      if (this.$refs.multipleTable) {
        this.$refs.multipleTable.clearSelection()
      }
      this.selectList = []
      this.$router.push('/orderList')
    },
    queryInfo() {
      this.selectList = []
      this.routerObj = this.$route.query
      this.pageData.curOrderStatus = this.routerObj.activeOrderStatus
      console.log(this.routerObj.activeOrderStatus); // 2/3 配送中/已完成
      const { orderNo } = this.routerObj
      this.orderNo = orderNo
      if(this.subForm.refundTypeForm.refundType == 2 && (this.pageData.curOrderStatus == 2 || this.pageData.curOrderStatus == 3)){
        this.pageData.curType = 1
      } else this.pageData.curType = 2
      listRefundReason({ orderNo }).then((res) => {
        if (res.code === 0) {
          this.plainList = res.result
        }
      })
      queryCanRefundProductDetail({ orderNo, refundMode: this.subForm.refundTypeForm.refundType }).then((res) => {
        if (res.code === 0) {
          if (res.result) {
            this.merchantName = res.result.merchantName
            this.refundData = res.result.orderDetails?.map((item) => {
              if(!item.thisRefundQuantity || item.thisRefundQuantity==0) item.thisRefundQuantity = null
              return item
            })

            this.pageData.freightRefundType = res.result.freightRefundType
            this.pageData.freightRefundAmount = res.result.freightRefundAmount
          }
        }
      })
      getAfterSaleRefundConfigProperties().then((res) => {
        if (res.code === 0) {
          if (res.result) {
            this.pageData.merchantInitiatedAdditionalCompensationRate = res.result.merchantInitiatedAdditionalCompensationRate
          }
        }
      })
    },
    handleSelectionChange(val) {
      this.selectList = val
      console.log(this)
    },
    changeRefundType(val) {
      if(val == 2 && (this.pageData.curOrderStatus == 2 || this.pageData.curOrderStatus == 3)){
        this.pageData.curType = 1
      } else this.pageData.curType = 2

      if (this.$refs.multipleTable) {
        this.$refs.multipleTable.clearSelection()
        this.selectList = []
      }
      // this.$nextTick(() => {
      // });
      this.$refs.refundForm?.resetFields();
      // this.subForm.refundForm.additionalCompensation = 0
      if(val == 2) this.subForm.refundForm.isRefundShipFee = true 
      if(val == 1) this.subForm.refundForm.isRefundShipFee = false 

      // if(val == 4 && this.pageData.smallAmountPayment == null){
      //   queryCanRefundProductDetail({ orderNo: this.orderNo, refundMode: val }).then((res) => {
      //     if (res.code === 0) {
      //       if (res.result) {
      //         this.pageData.smallAmountPayment = res.result.smallAmountPayment
      //       }
      //     }
      //   })
      // }
      queryCanRefundProductDetail({ orderNo: this.orderNo, refundMode: val }).then((res) => {
        if (res.code === 0) {
          if (res.result) {
            this.merchantName = res.result.merchantName
            // this.refundData = res.result.orderDetails
            this.refundData = res.result.orderDetails?.map((item) => {
              if(!item.thisRefundQuantity || item.thisRefundQuantity==0) item.thisRefundQuantity = null
              return item
            })
            this.pageData.freightRefundType = res.result.freightRefundType
            this.pageData.freightRefundAmount = res.result.freightRefundAmount
            this.pageData.smallAmountPayment = res.result.smallAmountPayment
          }
        }
      })
    },
    confirmD() {
      const { orderNo } = this.$route.query
      let params = { refundMode: this.subForm.refundTypeForm.refundType, orderNo, customerName: this.merchantName }
      let temp = false;
      if ( this.subForm.refundTypeForm.refundType == 2 || this.subForm.refundTypeForm.refundType == 1 ) {
        if (this.selectList.length < 1) {
          this.$message.warning({ message: '请先选择退款商品', offset: this.tipHeight })
          return
        }
        this.$refs.refundForm.validate((valid) => {
          if (valid) {
            console.log(this.subForm.refundForm)
            params = {
              ...params,
              detailList: this.selectList,
              refundExplain: this.subForm.refundForm.refundInstructions,
              refundReason: this.subForm.refundForm.refundReason[this.subForm.refundForm.refundReason.length - 1],
              refundCensusType:
                this.subForm.refundForm.isRefundShipFee && this.selectList.length == 0
                  ? 2
                  : !this.subForm.refundForm.isRefundShipFee && this.selectList.length != 0
                    ? 1
                    : 3,
              isRefundFreight: this.subForm.refundForm.isRefundShipFee ? 1 : 0,
              indemnityMoney: this.subForm.refundForm.additionalCompensation
              // refundCensusType:
              //   this.freightRefund.checked && this.selectList.length < 1
              //     ? 2
              //     : !this.freightRefund.checked && this.selectList.length >= 1
              //     ? 1
              //     : 3
            }
            temp = true;
            return true
          } else {
            this.$message.error('请检查表单内容！')
            return false
          }
        })
      }
      else if ( this.subForm.refundTypeForm.refundType == 3 ){
        this.$refs.shipFreeForm.validate((valid) => {
          if (valid) {
            params = {
              ...params,
              refundExplain: this.subForm.shipFreeForm.shipExplain,
              refundReason: this.subForm.shipFreeForm.shipReason[this.subForm.shipFreeForm.shipReason.length - 1],
            }
            temp = true;
            return true
          } else {
            this.$message.error('请检查表单内容！')
            return false
          }
        })
      }
      else if ( this.subForm.refundTypeForm.refundType == 4 ){
        this.$refs.smallPayForm.validate((valid) => {
          if (valid) {
            params = {
              ...params,
              indemnityMoney: this.subForm.smallPayForm.payAmount,
              refundExplain: this.subForm.smallPayForm.payExplain,
              refundReason: this.subForm.smallPayForm.payReason,
            }
            temp = true;
            return true
          } else {
            this.$message.error('请检查表单内容！')
            return false
          }
        })
      }
      if(temp) {
        getOrderRefundAmount(params).then((res) => {
            if (res.code === 0) {
              this.refundReason = {
                ...res.result,
                params: params,
                status: true
              }
            } else {
              this.$message.error({ message: res.result, offset: this.tipHeight })
            }
        })
      }
    },
    sendData(data) {
      submitApplyRefundBySeller(data).then((res) => {
        if (res.code === 0) {
          if (this.$refs.multipleTable) {
            this.$refs.multipleTable.clearSelection()
          }
          this.selectList = []
          this.$message.success({ message: '提交成功', offset: this.tipHeight })
          window.openTab('/orderList')
        } else {
          this.$message.error({ message: res.msg, offset: this.tipHeight })
        }
      })
    },
    changeNum1(value) {
      const that = this
      function resetAmount(){
        value.thisRefundQuantity = null
        value.refundFee = 0
      }
      function setAmount(){
        value.thisRefundQuantity = tempAmount
        // if (that.pageData.curType == 1) value.thisRefundQuantity = tempAmount.toFixed(4)
        // if (that.pageData.curType == 2) value.thisRefundQuantity = tempAmount
      }
      const tempAmount = parseFloat(value.thisRefundQuantity + '')
      console.log(tempAmount);
      if(tempAmount === '') {
        resetAmount()
        return;
      }
      if (tempAmount - 0 <= 0) {
        this.$message.warning({ message: '本次退货数量需为正数', offset: this.tipHeight })
        resetAmount()
        return;
      }
      if (tempAmount - value.canRefundQuantity > 0) {
        this.$message.warning({ message: '本次退货数量应该<=可退数量', offset: this.tipHeight })
        resetAmount()
        return;
      }

      if (this.pageData.curType == 1) {
        // 订单发货后仅退款：0<退货数量＜=最大可退数量，支持小数点，最多4位小数
        const regex = /^\d+(\.\d{1,4})?$/
        if (!regex.test(tempAmount)) {
          // 如果不匹配正则表达式，说明输入不符合要求
          resetAmount()
          this.$message.warning({ message: '请检查输入格式，且最多不超过4位小数', offset: this.tipHeight })
        } else {
          // const temp = parseFloat(tempAmount)
          // if(isNaN(temp)) resetAmount()
          setAmount()
        }
      } else {
        // 其它情况必须为正整数
        const regex = /^\d+$/
        if (!regex.test(tempAmount)) {
          resetAmount()
          this.$message.warning({ message: '请输入正整数', offset: this.tipHeight })
        } else {
          setAmount()
        }
      }


      if(value.thisRefundQuantity && value.thisRefundQuantity != ''){
        getOrderRefundDetailAmount({detailList: [value], orderNo: this.orderNo, refundMode: this.subForm.refundTypeForm.refundType}).then((res)=>{
          if(res.code == 0){
            value.refundFee = res.result[0].refundFee
          }else{
            this.$message.error({ message: res.msg, offset: this.tipHeight })
            resetAmount()
          }
        })
      }
      // getOrderRefundDetailAmount
    },
    // changeNum1(value) {
    //   function resetAmount(){
    //     value.thisRefundQuantity = null
    //     value.refundFee = 0
    //   }
    //   function setAmount(){
    //     value.thisRefundQuantity = parseFloat(value.thisRefundQuantity)
    //   }
    //   const tempAmount = parseFloat(value.thisRefundQuantity + '')
    //   console.log(parseFloat(value.thisRefundQuantity));
    //   if(value.thisRefundQuantity == null || value.thisRefundQuantity == '') {
    //     resetAmount()
    //     return;
    //   }
    //   if (value.thisRefundQuantity <= 0) {
    //     this.$message.warning({ message: '本次退货数量需为正数', offset: this.tipHeight })
    //     resetAmount()
    //     return;
    //   }
    //   if (value.thisRefundQuantity > value.canRefundQuantity) {
    //     this.$message.warning({ message: '本次退货数量应该<=可退数量', offset: this.tipHeight })
    //     resetAmount()
    //     return;
    //   }

    //   if (this.pageData.curType == 1) {
    //     // 订单发货后仅退款：0<退货数量＜=最大可退数量，支持小数点，最多4位小数
    //     const regex = /^\d+(\.\d{1,4})?$/
    //     if (!regex.test(value.thisRefundQuantity)) {
    //       // 如果不匹配正则表达式，说明输入不符合要求
    //       resetAmount()
    //       this.$message.warning({ message: '最多不超过4位小数', offset: this.tipHeight })
    //     } else {
    //       // const temp = parseFloat(value.thisRefundQuantity)
    //       // if(isNaN(temp)) resetAmount()
    //       setAmount()
    //     }
    //   } else {
    //     // 其它情况必须为正整数
    //     const regex = /^\d+$/
    //     if (!regex.test(value.thisRefundQuantity)) {
    //       resetAmount()
    //       this.$message.warning({ message: '请输入正整数', offset: this.tipHeight })
    //     } else {
    //       setAmount()
    //     }
    //   }


    //   if(value.thisRefundQuantity && value.thisRefundQuantity != ''){
    //     getOrderRefundDetailAmount({detailList: [value], orderNo: this.orderNo, refundMode: this.subForm.refundTypeForm.refundType}).then((res)=>{
    //       if(res.code == 0){
    //         value.refundFee = res.result[0].refundFee
    //       }else{
    //         this.$message.error({ message: res.msg, offset: this.tipHeight })
    //         resetAmount()
    //       }
    //     })
    //   }
    //   // getOrderRefundDetailAmount
    // },
    handleCascaderVal(value) {
      console.log(value)
    }
  }
}
</script>

<style scoped lang="scss">
.paddingDiv {
  padding-top: 15px;
}
.divBox {
  padding: 0 20px;
  .orderTitle {
    padding: 10px 0;
    display: flex;
    justify-content: space-between;
    span {
      font-size: 20px;
      color: #333333;
      font-weight: bold;
    }
  }
}
</style>
