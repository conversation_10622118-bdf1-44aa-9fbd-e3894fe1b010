<template>
    <div class="release-shop">
        <!-- 非包邮 -->
        <div class="release-shop-list">
            <div class="release-shop-list-title">
                <p>非单品包邮类</p>
                <span>买满起配金额可配送</span>
            </div>
            <div class="release-card-list">
                <div class="release-shop-card">
                    <div class="card-img">
                        <img src="" alt="">
                    </div>
                    <div class="card-info">
                        <p>普通商品</p>
                        <span>可以使用平台券、商家券</span>
                    </div>
                    <div class="card-button"><el-button type="primary" @click="goInitialize" plain>去上架</el-button></div>
                </div>
            </div>
        </div>
        <!-- 包邮 -->
        <div class="release-shop-list">
            <div class="release-shop-list-title">
                <p>单品包邮类</p>
                <span>买满起订数量可配送</span>
            </div>
            <div class="release-card-list">
                <div class="release-shop-card">
                    <div class="card-img">
                        <img src="" alt="">
                    </div>
                    <div class="card-info">
                        <p>拼团</p>
                        <span>包括周末拼团、夜间拼团等。商家需在系统指定时间参与，低于参考价才显示在拼团会场。</span>
                        <div class="card-theme">14个拼团主题可参与</div>
                    </div>
                    <div class="card-button"><el-button type="primary" plain>去上架</el-button></div>
                </div>
                <div class="release-shop-card">
                    <div class="card-img">
                        <img src="" alt="">
                    </div>
                    <div class="card-info">
                        <p>批购包邮</p>
                        <span>无需价格审核，商家可自由设置活动时间、供货对象、库存及下架。</span>
                    </div>
                    <div class="card-button"><el-button type="primary" @click="goWholesale" plain>去上架</el-button></div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    methods: {
        goInitialize() {
            this.$router.push("/product/initialize")
        },
        goWholesale() {
            this.$router.push("/wholesale")
        }
    }
}
</script>

<style lang="scss" scoped>
.release-shop {
    padding: 20px;
    box-sizing: border-box;
    .release-shop-list-title {
        display: flex;
        align-items: flex-end;
        p {
            font-size: 18px;
            margin: 0;
        }
        span {
            font-size: 14px;
            color: #999;
            margin-left: 10px;
        }
    }
    .release-card-list {
        display: flex;
        margin-bottom: 40px;
        .release-shop-card {
            height: 220px;
            margin-top: 20px;
            padding: 20px;
            box-sizing: border-box;
            background: #f7f7f7;
            border-radius: 5px;
            width: 25%;
            margin-right: 20px;
            display: flex;
            position: relative;
            .card-img {
                width: 100px;
                height: 100px;
                background: red;
            }
            .card-info {
                flex: 1;
                margin-left: 10px;
                position: relative;
                p {
                    font-size: 16px;
                    margin: 0;
                }
                span {
                    font-size: 14px;
                    color: #999;
                    margin-top: 5px;
                }
                .card-theme {
                    color: red;
                    position: absolute;
                    bottom: 20px;
                }
            }
            
            .card-button {
                position: absolute;
                right: 20px;
                bottom: 20px;
            }
        }
    }
}
</style>