<template>
  <div class="step-container">
    <el-steps :active="active" align-center :space="175">
      <template v-for="(step, index) in steps">
        <el-step :title="step.name" :key="step.name">
          <i :class="'status-'+ index" slot="icon"></i>
        </el-step>
      </template>
    </el-steps>
  </div>
</template>

<script>
export default {
  props: {
    active: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      steps: [
        { name: '设置控销信息' },
        { name: '选择控销商品' },
        { name: '添加控销用户' }
      ],
    };
  },
};
</script>
<style lang="scss" scoped>
.step-container {
  position: relative;
  padding: 10px 0px;

  .status-0,
  .status-1,
  .status-2 {
    width: 28px;
    height: 28px;
    background-size: 100% 100%;
  }
  .status-0 {
    background-image: url('~@/assets/image/control/icon_control_01.png');
  }
  .status-1 {
    background-image: url('~@/assets/image/control/icon_control_03.png');
  }
  .status-2 {
    background-image: url('~@/assets/image/control/icon_control_07.png');
  }
  ::v-deep  .el-steps {
    .el-step__line {
      //   top: 50%;
      //   height: 1px;
      //   background: #999999;
      //   margin: 0 24px !important;
      top: 50%;
      border-top: 1px dashed #999999;
      height: 0;
      background-color: transparent;
      margin: 0 24px !important;
    }

    .el-step__head.is-finish {
      .el-step__icon.is-text {
        border-color: #ffffff;
      }
      .el-step__line {
        border-top: 1px dashed #4184d5;
        .el-step__line-inner {
          border-style: none;
        }
      }
    }

    .el-step__head{
      .el-step__icon {
        border-color: #ffffff;
        background-color: #ffffff;
      }
    }

    .el-step__head.is-process {
      .el-step__icon.is-text {
        border-color: #4184d5;
        background-color: #4184d5;
      }
    }

    .el-step__head.is-wait {
      .el-step__icon.is-text {
        border-color: #ffffff;
      }
    }

    .el-step__title.is-process {
      color: #333333;
      font-size: 14px;
    }
    .el-step__title.is-finish {
      color: #333333;
      font-size: 14px;
    }
    .el-step__title.is-wait {
      color: #999999;
      font-size: 14px;
    }

    .el-step__icon {
      width: 48px;
      height: 48px;
    }

    .is-finish .status-1 {
      background-image: url('~@/assets/image/control/icon_control_04.png');
    }
    .is-finish .status-2 {
      background-image: url('~@/assets/image/control/icon_control_07.png');
    }

    .is-process .status-0 {
      background-image: url('~@/assets/image/control/icon_control_02.png');
    }
    .is-process .status-1 {
      background-image: url('~@/assets/image/control/icon_control_05.png');
    }
    .is-process .status-2 {
      background-image: url('~@/assets/image/control/icon_control_06.png');
    }
  }
}
</style>
