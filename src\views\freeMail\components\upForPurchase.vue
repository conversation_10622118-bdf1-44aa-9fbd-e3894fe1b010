<template>
  <div class="boxDiv">
    <el-row>
      <el-form
        ref="upForPurchaseVo"
        :model="upForPurchaseVo"
        :rules="upForPurchaseVoRules"
        label-width="130px"
        class="demo-ruleForm"
      >
        <el-row>
          <el-col :span="12">
            <el-form-item label="客户起购数量:" prop="minPurchaseCount">
              <el-input
                v-model="upForPurchaseVo.minPurchaseCount"
                placeholder="支持输入正整数"
                :disabled="disabled"
                type="text"
                onkeyup="value=value.replace(/[^\d]/g,'')"
                @blur="upForPurchaseVo.minPurchaseCount=$event.target.value"
              ></el-input>
            </el-form-item>
          </el-col>
          <span class="checkBox-info">起购：药店单次购买的最小采购数量，需为正整数。若商品不可拆零，起购数量需为中包装的倍数</span>
        </el-row>
        <el-row>
          <el-col :span="5.5">
            <el-form-item label="是否限购:">
              <el-switch
                :width="70"
                v-model="isSwitch"
                @change="purchaseTypeChange"
                active-text="不限购"
                inactive-text="限购"
                :disabled="disabled"
              ></el-switch>
            </el-form-item>
          </el-col>
          <span class="checkBox-info">限购：药店在设定的周期内可购买的最大采购数量，需为正整数。限购数量需≥起购数量，限购数量需≥中包装数量</span>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="客户限购数量:" prop="limitedQty" v-if="upForPurchaseVo.purchaseType!==0">
              <el-input
                v-model="upForPurchaseVo.limitedQty"
                placeholder="支持输入正整数"
                :disabled="disabled"
                type="text"
                onkeyup="value=value.replace(/[^\d]/g,'')"
                @blur="upForPurchaseVo.limitedQty=$event.target.value"
              ></el-input>
            </el-form-item>
          </el-col>
          <span
            v-if="upForPurchaseVo.purchaseType!==0"
            class="checkBox-info"
          >限购：药店在设定的周期内可购买的最大采购数量，需为正整数。限购数量需>=起购数量，限购数量需>=中包装数量</span>
          <!-- <el-checkbox
            class="checkBox-info"
            v-model="upForPurchaseVo.purchaseType==0"
            @change="purchaseTypeChange"
            :disabled="disabled"
          >不限购</el-checkbox>
          <span class="checkBox-info">限购：药店在设定的周期内可购买的最大采购数量，需为正整数。限购数量需>=起购数量，限购数量需>=中包装数量</span>-->
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="限购类型:" prop="purchaseType" v-if="upForPurchaseVo.purchaseType!==0">
              <el-select
                class="select-info"
                v-model="upForPurchaseVo.purchaseType"
                @change="setSelectChange"
                placeholder="请选择"
                :disabled="disabled"
              >
                <el-option label="时间范围" :value="1" />
                <el-option label="单笔" :value="2" />
                <el-option label="每天（每天 00:00 至 24:00）" :value="3" />
                <el-option label="每周（周一 00:00 至 周日 24:00）" :value="4" />
                <el-option label="每月（每月1号 00:00 至 每月最后一天 24:00）" :value="5" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item
              v-if="+upForPurchaseVo.purchaseType === 1"
              label="限购生效周期:"
              prop="opPurchaseTime"
            >
              <el-date-picker
                v-model.trim="upForPurchaseVo.opPurchaseTime"
                popper-class="install-contr-cell-class"
                range-separator="至"
                size="small"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
                placeholder="请选择生效周期"
                type="daterange"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                prefix-icon="el-icon-date"
                style="width: 100%;"
                :disabled="disabled"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="限购客户类型:" prop="userType" v-if="upForPurchaseVo.purchaseType!==0">
              <div class="customerType">
                <el-checkbox
                  :indeterminate="isIndeterminate"
                  v-model="checkAll"
                  :disabled="disabled"
                  @change="handleCheckAllChange"
                  class="checkedall"
                >全选</el-checkbox>
                <el-checkbox-group
                  v-model="upForPurchaseVo.userType"
                  @change="handleCheckedTypesChange"
                >
                  <el-checkbox
                    v-for="(item,index) in customerTypes"
                    :label="item.id"
                    :disabled="disabled"
                    style="marginBottom:10px;"
                    :key="index"
                  >{{item.name}}</el-checkbox>
                </el-checkbox-group>
              </div>
              <div style='color: red;font-size: 12px;'>
                1、此处为指定客户类型执行限购政策，非控销。如需设置商品控销请到 <el-button type="text" @click='jumpUrl'>控销管理》</el-button><br>
                2、已勾选的客户类型将执行商品的限购政策，未勾选的客户类型不限购
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <el-divider />
    </el-row>
  </div>
</template>

<script>
import { findUserTypes } from '@/api/product';

export default {
  name: 'UpForPurchase',
  props: {
    basicData: {
      type: Object,
      default() {
        return {};
      },
    },
    formModel: {
      type: Object,
      default() {
        return {};
      },
    },
  },
  data() {
    return {
      disabled: false,
      customerTypes: [],
      isIndeterminate: false,
      checkAll: false,
      propsData: {},
      upForPurchaseVoRules: {
        limitedQty: [
          { required: false, message: '请填写限购数量', trigger: 'blur' },
        ],
        purchaseType: [
          { required: false, message: '请选择限购类型', trigger: 'blur' },
        ],
        opPurchaseTime: [
          { required: false, message: '请选择生效周期', trigger: 'change' },
        ],
        userType: [
          { required: false, message: '请选择客户类型', trigger: 'blur' },
        ],
      },
      upForPurchaseVo: {
        minPurchaseCount: '', // 客户起购数量
        limitedQty: '', // 限购数量
        purchaseType: 0, // 限购类型 0：不限购 1: 时间范围 2: 单笔
        opPurchaseTime: [],
        userType: [],
        userTypeName: [],
      },
      isSwitch: true,
    };
  },
  watch: {
    basicData: {
      immediate: true,
      handler(newVale) {
        this.$nextTick(() => {
          this.propsData = JSON.parse(JSON.stringify(newVale));
          this.initData();
        });
      },
    },
    formModel: {
      handler(newVal) {
        const base = JSON.parse(JSON.stringify(newVal));
        this.disabled = base.isEdit == 1;
      },
      immediate: true,
      deep: true,
    },
  },
  created() {
    window.sessionStorage.setItem('upForPurchaseVoRules', JSON.stringify(this.upForPurchaseVoRules))
    this.findUserTypes();
  },
  mounted() {
  },
  methods: {
    jumpUrl(){
      window.openTab('/controlIndex')
    },
    initData() {
      this.upForPurchaseVo.minPurchaseCount = this.propsData.minPurchaseCount;
      this.upForPurchaseVo.limitedQty = this.propsData.limitedQty;
      this.upForPurchaseVo.purchaseType = this.propsData.purchaseType;
      this.isSwitch = this.upForPurchaseVo.purchaseType == 0 ? true : false;
      this.setSelectChange();
      const { userType } = this.propsData;
      if (userType) {
        const userTypeStr = userType.split(',');
        this.upForPurchaseVo.userType = userTypeStr.map((item) => {
          return Number(item);
        });
        this.handleCheckedTypesChange(this.upForPurchaseVo.userType);
      } else {
        this.upForPurchaseVo.userType = [];
        this.upForPurchaseVo.userTypeName = [];
        this.isIndeterminate = false;
        this.checkAll = false;
      }
      if (this.propsData.purchaseTimeStart && this.propsData.purchaseTimeEnd) {
        const purchaseTimeStart = this.formatDate(this.propsData.purchaseTimeStart, 'YMD');
        const purchaseTimeEnd = this.formatDate(this.propsData.purchaseTimeEnd, 'YMD');
        this.upForPurchaseVo.opPurchaseTime = [purchaseTimeStart, purchaseTimeEnd];
      } else {
        this.upForPurchaseVo.opPurchaseTime = [];
      }
    },
    handleCheckAllChange(val) {
      const checkAllId = this.customerTypes.map((item => item.id));
      const checkAllName = this.customerTypes.map((item => item.name));
      this.upForPurchaseVo.userType = val ? checkAllId : [];
      this.upForPurchaseVo.userTypeName = val ? checkAllName : [];
      this.isIndeterminate = false;
    },
    handleCheckedTypesChange(value) {
      const checkedCount = value.length;
      this.checkAll = checkedCount === this.customerTypes.length;
      this.isIndeterminate = checkedCount > 0 && checkedCount < this.customerTypes.length;

      const nameGroup = [];
      this.upForPurchaseVo.userType.forEach((item) => {
        this.customerTypes.forEach((itemGroup) => {
          if (item == itemGroup.id) {
            nameGroup.push(itemGroup.name);
          }
        });
      });
      this.upForPurchaseVo.userTypeName = nameGroup;
    },
    purchaseTypeChange(type) {
      if (type) {
        this.upForPurchaseVo = {
          limitedQty: '', // 限购数量
          purchaseType: 0, // 限购类型 0：不限购 1: 时间范围 2: 单笔
          opPurchaseTime: [],
          userType: [],
          userTypeName: [],
        };
      } else {
        this.upForPurchaseVo.purchaseType = 2;
        this.upForPurchaseVo.opPurchaseTime = [];
        this.setSelectChange();
      }
    },
    setSelectChange() {
      // 若限购数量为具体值，如果限购类型为“指定时间区间”，需选择“限购生效周期”、“客户类型”。如果限购类型为“单笔”，需选择“客户类型”。
      ['limitedQty', 'purchaseType'].forEach((item) => {
        this.upForPurchaseVoRules[item].forEach((itemRules) => {
          if (Object.prototype.hasOwnProperty.call(itemRules, 'required') && itemRules.required === false) {
            itemRules.required = true;
          }
        });
      });
      if (this.upForPurchaseVo.purchaseType == 1) {
        ['opPurchaseTime', 'userType'].forEach((item) => {
          this.upForPurchaseVoRules[item].forEach((itemRules) => {
            if (Object.prototype.hasOwnProperty.call(itemRules, 'required') && itemRules.required === false) {
              itemRules.required = true;
            }
          });
        });
      } else if (this.upForPurchaseVo.purchaseType == 2) {
        ['opPurchaseTime', 'userType'].forEach((item) => {
          this.upForPurchaseVoRules[item].forEach((itemRules) => {
            if (Object.prototype.hasOwnProperty.call(itemRules, 'required')) {
              // eslint-disable-next-line default-case
              switch (item) {
                case 'opPurchaseTime':
                  itemRules.required = false;
                  break;
                case 'userType':
                  itemRules.required = true;
                  break;
              }
            }
          });
        });
      }
    },
    // 获取用户类型
    findUserTypes() {
      findUserTypes().then((res) => {
        if (res.code == 0) {
          this.customerTypes = res.data;
        } else {
          this.$message({
            message: res.message,
            type: 'warning',
          });
        }
      });
    },
  },
};
</script>

<style scoped lang="scss">
.el-button {
  padding: 8px 20px;
}
.el-button.is-circle {
  padding: 7px;
  border: none;
}
.boxDiv {
  width: 100%;
  height: 100%;
  overflow-y: auto;
  padding: 0 0 0 10px;

  ::v-deep   .el-form {
    width: 100%;

    .el-select {
      margin-right: 14px;
    }

    .el-form-item__label {
      font-size: 12px;
      line-height: 30px;
    }

    .el-form-item__content {
      line-height: 30px;
    }

    .el-input__inner {
      line-height: 30px;
      height: 30px;
      font-size: 12px;
    }

    .el-input__icon {
      line-height: 30px;
    }
  }

  ::v-deep   .el-table__body .el-form-item {
    padding: 20px 0;
  }

  .my-label .el-form-item {
    display: inline-block;
    width: 442px;
  }

  .checkBox-info {
    margin-left: 10px;
    justify-content: center;
    align-items: center;
    line-height: 30px;
    font-size: 12px;
    font-family: PingFangSC, PingFangSC-Regular;
    font-weight: 400;
    text-align: left;
    color: #333333;
  }
  .select-info {
    width: 100%;
  }

  .avatar-uploader ::v-deep   .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
  }

  .avatar-uploader ::v-deep   .el-upload:hover {
    border-color: #409eff;
  }

  .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 64px;
    height: 64px;
    line-height: 64px;
    text-align: center;
  }

  .avatar {
    width: 64px;
    height: 64px;
    display: block;
  }
}

.boxDiv::-webkit-scrollbar {
  width: 0 !important;
}

.tree-box {
  border: 1px solid #eaeaea;
  border-radius: 4px;
  padding: 6px 12px;
}
.my-tree-box ::v-deep  .el-tree-node__children {
  white-space: normal;
}
.user-type ::v-deep  .el-tree-node {
  display: inline-block;
  margin-right: 15px;
}
.my-tree-box ::v-deep  .el-tree-node__children .el-tree-node {
  white-space: inherit !important;
  outline: 0;
  display: inline-block !important;
}
.my-tree-box ::v-deep   .el-tree-node__content {
  display: flex;
  align-items: center;
  height: 26px;
  cursor: pointer;
  font-size: 12px;
}
::v-deep   .el-checkbox__label {
  display: inline-block;
  padding-left: 10px;
  line-height: 19px;
  font-size: 12px;
}
::v-deep   .el-checkbox__input.is-checked + .el-checkbox__label {
  color: #606266;
}

.customerType {
  border: 1px solid #eeeeee;
  border-radius: 4px;
  max-height: 260px;
  overflow-y: auto;
  ::v-deep  .el-checkbox {
    width: 14%;
    margin-left: 10px;
  }
  ::v-deep  .checkedall {
    width: 100%;
    padding: 10px;
    margin-left: 0;
    margin-bottom: 10px;
  }
  ::v-deep  .el-checkbox__input.is-checked + .el-checkbox__label {
    color: #333333;
  }
}
</style>
<style lang="scss">
.el-switch__label {
  position: absolute;
  display: none;
  font-size: 10px !important;
  color: #fff !important;
}
.el-switch__label * {
  font-size: 10px !important;
}
/*打开时文字位置设置*/
.el-switch__label--right {
  z-index: 1;
  right: 23px; // 这里是重点
  top: 0.5px;
}
/*关闭时文字位置设置*/
.el-switch__label--left {
  z-index: 1;
  left: 28px; // 这里是重点
  top: 0.5px;
}
/*显示文字*/
.el-switch__label.is-active {
  display: block;
}
.el-switch__core {
  width: 74px;
  height: 22px;
  border: 2px solid #dcdfe6;
  border-radius: 13px;
}
</style>
