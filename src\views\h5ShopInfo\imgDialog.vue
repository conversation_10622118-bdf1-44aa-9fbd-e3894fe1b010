
<template>
  <div class="imgDialogBox" @touchstart.stop="touchstart" @touchmove.stop="touchmove" @touchend.stop="touchend">
    <div class="closeIcon" @click="closeDialog"><img src="../../assets/h5/close.png" alt=""></div>
    <div class="imgBox">
      <img class="img" :src="imgItem.urls[activeIndex]" alt="">
    </div>
    <div class="bottomBox">
      <div class="lastBox" @click="changeImg('last')"><img src="../../assets/h5/last.png" alt=""></div>
      <div class="bottomText">{{ imgItem.name }}({{ activeIndex + 1 }}/{{imgItem.urls.length}})</div>
      <div class="nextBox" @click="changeImg('next')"><img src="../../assets/h5/next.png" alt=""></div>
    </div>
  </div>
</template>

<script>
  export default {
    name: 'ImgDialog',
    props: {
      imgItem: {
        type: Object,
        default: {},
      },
    },
    data() {
      return {
        activeIndex: 0,
        isStatus: false,
        startX: 0,
				startY: 0,
				endX: 0,
				endY: 0,
      }
    },
    methods: {
      changeImg(type) {
        if (type === 'last') {
          if (this.activeIndex != 0) {
            this.activeIndex--;
          }
        } else if (type === 'next') {
          if (this.activeIndex != this.imgItem.urls.length - 1) {
            this.activeIndex++;
          }
        }
      },
      /* 监听滑动开始 */
			touchstart(e) {
				this.startX = e.touches[0].pageX;
				this.startY = e.touches[0].pageY;
			},
			/* 监听滑动移动 */
			touchmove(e) {
				this.isStatus= true;
				this.endX = e.touches[0].pageX;
				this.endY = e.touches[0].pageY;
			},
      /* 监听滑动结束 */
			touchend(e) {
				/* 判断移动方向 */
				let X = this.endX - this.startX,
					Y = this.endY - this.startY;
				/* 判断是否移动还是点击 */
				if (this.isStatus) {
					if (X > 0 && Math.abs(X) > Math.abs(Y)) {
            // 向右
            this.changeImg('last');
					} else if (X < 0 && Math.abs(X) > Math.abs(Y)) {
						// 向左
            this.changeImg('next');
					}
				}
			},
      closeDialog() {
        this.$emit('handleCancel');
      }
    }

  }
</script>
<style lang="scss" scoped>
.imgDialogBox {
  position: fixed;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  background: #000;
  z-index: 101;
  display: flex;
  .closeIcon {
    width: .6rem;
    height: .6rem;
    position: fixed;
    top: .5rem;
    right: .5rem;
    img {
      width: 100%;
      height: 100%;
    }
  }
  .imgBox {
    width: 100%;
    .img {
      height: 100%;
      width: 100%;
      object-fit: contain;
    }
  }
  .bottomBox {
    width: 100%;
    position: fixed;
    bottom: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    .lastBox, .nextBox {
      width: 0.5rem;
      height: 0.5rem;
      img {
        width: 100%;
        height: 100%;
      }
    }
    .bottomText {
      width: 4rem;
      height: 1.2rem;
      padding: 0 0.2rem;
      margin: 0 0.5rem;
      text-align: center;
      display: flex;
      align-items: center;
      justify-content: center;
      background: rgba(0, 0, 0, 0.5);
      border: 0.5px solid #CCCCCC;
      border-radius: 3rem;
      color: #fff;
    }
  }
}
</style>
