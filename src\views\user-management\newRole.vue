<template>
  <div>
    <div class="serch">
      <div class="Fsearch">
        <el-row type="flex" align="middle" justify="space-between" class="my-row">
          <el-row type="flex" align="middle">
            <span class="sign" />
            <div class="searchMsg">{{titleName}}</div>
          </el-row>
          <el-button type="primary" size="small" @click="$router.go(-1)">返回</el-button>
        </el-row>
      </div>
      <div class="fromBlock">
        <el-form ref="ruleForm" :model="ruleForm" :rules="rules" size="small" label-width="120px">
          <div class="level1Title">
            <span>角色信息</span>
            <el-row>
              <el-col :span="24">
                <el-form-item label="角色名称" prop="roleName">
                  <el-input
                    v-model="ruleForm.roleName"
                    maxlength="10"
                    placeholder="请输入角色名称"
                    show-word-limit
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="24">
                <el-form-item label="角色说明" prop="description">
                  <el-input
                    v-model="ruleForm.description"
                    placeholder="您可在此填写角色说明，便于记录"
                    type="textarea"
                    maxlength="200"
                    show-word-limit
                    :autosize="{ minRows: 5, maxRows: 8}"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <span>权限信息</span>
            <el-row>
              <el-col :span="24">
                <el-form-item label prop="menuIds">
                  <div class="tree-box">
                    <el-tree
                      :data="classList"
                      :props="props"
                      show-checkbox
                      check-strictly
                      :default-checked-keys="ruleForm.menuIds"
                      :default-expanded-keys="expandedKeys"
                      :expand-on-click-node="true"
                      node-key="id"
                      ref="menuTree"
                      class="my-tree-box"
                      :render-content="renderContent"
                      @check="handleCheckedMenuChange"
                      @node-expand="handleExpand"
                    >
                      <div class="custom-tree-node" slot-scope="{ node, data }">
                        <div class="total_info_box clearfix">
                          <span>{{ data.name }}</span>
                        </div>
                      </div>
                    </el-tree>
                  </div>
                <span class="my-tree-tip">注意：直接勾选某个二级菜单，不会全选该菜单下所有操作按钮，请认真勾选权限。（例如，勾选“商品列表”，该菜单下所有操作按钮均为未勾选状态，如需操作按钮权限，请逐一勾选）</span>
                </el-form-item>
              </el-col>
            </el-row>
          </div>
          <div class="level1Title">
            <el-form-item class="btn-group">
              <el-button @click="resetForm('ruleForm')">取消</el-button>
              <el-button type="primary" :loading="submitLoading" @click="submitForm('ruleForm')">提交</el-button>
            </el-form-item>
          </div>
        </el-form>
      </div>
    </div>
  </div>
</template>

<script>
import { apiQueryRoleInfoByRoleId , apiSaveRole , apiUpdateRole , getMenuList } from '@/api/userManagement';
export default {
  name: 'newRole',
  components: { },
  data() {
    return {
      isLoading: false,
      ruleForm: {
        description: '',
        roleName: '',
        menuIds: []
      },
      rules: Object.freeze({
        roleName: [
          {
            required: true,
            message: '请填写角色名称，角色名称不能为空',
            trigger: 'blur'
          }
        ]
      }),
      classList: [],
      props: {
        label: 'name', // 需要指定的节点渲染对象属性
        children: 'childs', // 指定的子级
        isLeaf: "leaf"
      },
      titleName:'新建角色',
      submitLoading: false,
      roleId:'',
      roleType:'',
      expandedKeys:[]
    };
  },
  created() {
  },
  mounted() {
  },
  activated() {
    this.getMenuList();
    this.activate();
  },
  methods: {
    activate(){
      const query = this.$route.query
      if (query && Object.keys(query).length > 0) {
        const { roleId,roleType } = this.$route.query;
        this.roleType = roleType;
        this.roleId = roleId;
        if (roleType==='1') {
          this.titleName = '新建角色';
          this.ruleForm = this.$options.data().ruleForm;
        } else if (roleType==='2'){
          this.titleName = '编辑角色';
          this.queryRoleInfoByRoleId();
        }else if(roleType==='3'){
          this.titleName = '新建角色';
          this.queryRoleInfoByRoleId();
        }
      }
    },
    // 查询角色详情
    queryRoleInfoByRoleId(){
      const that = this;
      apiQueryRoleInfoByRoleId({roleId:this.roleId}).then((res) => {
        if (res.code == 0) {
          this.ruleForm.roleName = res.data.roleName;
          this.ruleForm.description = res.data.description;
          this.ruleForm.menuIds = res.data.selectedMenus;
        }else {
           that.$message({
              message: res.message,
              type: 'error'
            })
          }
      }) .catch(() => {});
    },
    filterGrayScaleMenus(menus, grayResult) {
      let targetValue;
      if (grayResult === 0) {
        targetValue = 1;  // 移除 isGrayscale === 1 的节点
      }else if (grayResult === 1) {
        targetValue = -1;  // 移除 isGrayscale === -1 的节点
      }else {
        return menus;
      }

      return menus.map(menu => {
        const newMenu = { ...menu };
        if (newMenu.childs && newMenu.childs.length > 0) {
            newMenu.childs = newMenu.childs.filter(child => child.isGrayscale !== targetValue);
        }
        return newMenu;
      });
    },
    getMenuList(resolve) {
      let that = this;
      getMenuList().then((res) => {
        if (res.code == 0) {
          if(res.result){
            const grayMune = this.filterGrayScaleMenus(res.result.menuList || [], this.$store.state.permission.menuGray);
            that.classList = grayMune;
            that.expandedKeys = that.classList.map(item=>{
              return item.id;
            })
          }
        }
      })
    },
    // 传递给 data 属性的数组中该节点所对应的对象、节点本身是否被选中、节点的子树中是否有被选中的节点
    // handleCheckedMenuChange(val,check,children){
    //   let menuData = JSON.parse(
    //     JSON.stringify(this.$refs.menuTree.getCheckedNodes())
    //   )
    //   this.ruleForm.menuIds =  menuData.map(item=>{
    //     return item.id;
    //   });
    // },
  // 内容区渲染后执行 判断底层 赋 class
    renderContent(h, { node, data, store }) {
      let classname = ''
      if (node.level === 3) {
        classname = 'levelname'
      }
      return h('p',{ class:classname },node.label);
    },
    changeCss() {
      var levelName = document.getElementsByClassName('levelname') // levelname是上面的最底层节点的名字
      for (var i = 0; i < levelName.length; i++) {
        levelName[i].parentNode.style.cssFloat = 'left' // 最底层的节点，包括多选框和名字都让他左浮动
        levelName[i].parentNode.style.styleFloat = 'left'
        levelName[i].parentNode.onmouseover = function(){
          this.style.backgroundColor = '#fff'
        }
      }
    },
    handleExpand(){
      this.$nextTick(()=>{
        this.changeCss();
      });
    },
    handleCheckedMenuChange (currentNode, treeStatus) {
      console.log(currentNode, treeStatus)
      /**
       * @des 根据父元素的勾选或取消勾选，将所有子级处理为选择或非选中状态
       * @param { node: Object }  当前节点
       * @param { status: Boolean } （true ： 处理为勾选状态 ； false： 处理非选中）
       */
      const setChildStatus = (node, status) => {
        this.$refs.menuTree.setChecked(node.id, status)
        if (node.childs) {
          if(node.level === 3 || !status){
            /* 循环递归处理子节点 */
            for (let i = 0; i < node.childs.length; i++) {
              setChildStatus(node.childs[i], status)
            }
          }
          // if(node.level !== 2 || !status){
          //   /* 循环递归处理子节点 */
          //   for (let i = 0; i < node.childs.length; i++) {
          //     setChildStatus(node.childs[i], status)
          //   }
          // }
        }
      }
      /* 设置父节点的选中状态 */
      const setParentStatus = (nodeObj, status) => {
        /* 拿到tree组件中的node,使用该方法的原因是第一次传入的 node 没有 parent */
        const node = this.$refs.menuTree.getNode(nodeObj)
        if (node.parent.key) {
          if(node.level === 3 && status){
            // 是三级分类并且是选中状态
            this.$refs.menuTree.setChecked(node.parent, status)
            setParentStatus(node.parent, status)
          }
          // if(node.level !== 3){
          //   // 不是三级分类并且是选中状态
          //   if(status){
          //     this.$refs.menuTree.setChecked(node.parent, status)
          //     setParentStatus(node.parent, status)
          //   }else{
          //     /* 循环递归处理子节点 */
          //     if(node.parent.childNodes){
          //       let isCheck = false;
          //       for (let i = 0; i < node.parent.childNodes.length; i++) {
          //         if(node.parent.childNodes[i].checked){
          //           isCheck = true;
          //         }
          //       }
          //       this.$refs.menuTree.setChecked(node.parent, isCheck)
          //       setParentStatus(node.parent, status)
          //     }
          //   }
          // }else{
          //   // 是三级分类并且是选中状态
          //   if(status){
          //     this.$refs.menuTree.setChecked(node.parent, status)
          //     setParentStatus(node.parent, status)
          //   }
          // }
        }
      }
      /* 判断当前点击是选中还是取消选中操作 */
      if (treeStatus.checkedKeys.includes(currentNode.id)) {
        setParentStatus(currentNode, true)
        setChildStatus(currentNode, true)
      } else {
        /* 取消选中 */
        if (currentNode.childs) {
          setParentStatus(currentNode, false)
          setChildStatus(currentNode, false)
        }
      }
      this.ruleForm.menuIds = [...this.$refs.menuTree.getCheckedKeys()]
    },
    submitForm(formName) {
      const that = this;
      this.$refs[formName].validate((valid) => {
        if (valid) {
          that.submitLoading = true;
          let param = {
            ...that.ruleForm
          }
          if(that.roleType!=='2'){
            apiSaveRole(param).then((res) => {
              that.submitLoading = false;
              if (res.code === 0) {
                that.$message.success('提交成功');
                if(this.$store.state.permission.menuGray == 1) {
                  that.$router.replace({
                    path: '/shopAccountNumManage',
                    query: { to:"roleManagement",refresh: true }
                  });
                }else {
                  that.$router.replace({
                    path: '/roleManagement',
                    query: { refresh: true }
                  });
                }
              } else {
                that.$message.error(res.message);
              }
            });
          }else{
            param.roleId = that.roleId;
            apiUpdateRole(param).then((res) => {
              that.submitLoading = false;
              if (res.code === 0) {
                that.$message.success('提交成功');
                if(this.$store.state.permission.menuGray == 1) {
                  that.$router.replace({
                    path: '/shopAccountNumManage',
                    query: { to:"roleManagement",refresh: true }
                  });
                }else {
                  that.$router.replace({
                    path: '/roleManagement',
                    query: { refresh: true }
                  });
                }
              } else {
                that.$message.error(res.message);
              }
            });
          }
          return true;
        } else {
          return false;
        }
      });
    },
    resetForm(formName) {
      this.$refs[formName].resetFields();
      this.$router.go(-1)
      // this.$router.push({ path: '/activemanage' });
    }
  }
};
</script>

<style lang="scss" scoped>
.serch {
  background: rgba(255, 255, 255, 1);
  padding: 15px 15px 10px 10px;
  width: 100%;
  height: 100%;
  overflow-y: auto;
  .sign {
    display: inline-table;
    width: 3px;
    height: 13px;
    background: linear-gradient(360deg, #1d69c4 1%, #8bbdfc 99%);
    border-radius: 2px;
    margin-right: 8px;
  }

  .fromBlock {
    margin-top: 10px;
    .level1Title {
      margin: 0 90px 0 45px;
      font-size: 14px;
      color: #606266;
    }
  }

  .btn-group {
    display: flex;
    justify-content: flex-end;
  }

  ::v-deep   .el-form {
    width: 100%;

    .el-select {
      margin-right: 14px;
    }

    .el-form-item__label {
      font-size: 12px;
      line-height: 30px;
    }

    .el-form-item__content {
      line-height: 30px;
    }

    .el-input__inner {
      line-height: 30px;
      height: 30px;
      font-size: 12px;
    }

    .el-input__icon {
      line-height: 30px;
    }
  }

  ::v-deep   .el-table__body .el-form-item {
    padding: 20px 0;
  }

  .my-tree-box ::v-deep   .el-tree-node__content .el-checkbox {
    display: none;
  }
  .my-tree-box ::v-deep   .el-tree-node__children .el-checkbox {
    display: inline-block;
  }

  .my-tree-box ::v-deep   .el-tree-node__content {
    display: flex;
    align-items: center;
    height: 26px;
    cursor: pointer;
    font-size: 12px;
  }
  ::v-deep   .el-checkbox__label {
    display: inline-block;
    padding-left: 10px;
    line-height: 19px;
    font-size: 12px;
  }
  ::v-deep   .el-checkbox__input.is-checked + .el-checkbox__label {
    color: #606266;
  }

  .list-box ::v-deep  .el-button + .el-button {
    margin-left: 0px;
  }

  .tree-box {
    border: 1px solid #eaeaea;
    border-radius: 4px;
    padding: 6px 12px;
    max-height: 367px;
    margin-top: 10px;
    overflow-y: auto;
  }
  .my-tree-tip{
    color:#ff2400;
    font-size: 12px;
  }
}
</style>
