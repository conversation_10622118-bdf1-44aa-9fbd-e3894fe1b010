<template>
  <div>
    <div class="searchCourse" >
      <div>筛选课程</div>

      <el-input placeholder="请输入课程名称" v-model="courseName" size="mini" style="width: 200px" @change="searchCourse">
        <i slot="suffix" class="el-input__icon el-icon-search"></i
      ></el-input>
    </div>
    <div class="courseList">
      <div :class="{'courseItem':true,'courseItem-active':item.id==activeCourseId&&!isAll}" v-for="(item, index) in showCourseList" :key="index" @click="changeCourse(item.id)">
        <img class="courseIcon" :src="item.imageUrl" alt="" />
        <div style="margin-left: 8px">
          <img src="@/assets/image/home/<USER>" alt="" style="height:20px;position:absolute;top:0;right:0;" v-if="item.id == activeCourseId&&!isAll">
          <div class="course-desc">{{ item.categoryName }}</div>
          <div style="font-size: 12px; color: #777777; margin-top: 8px">{{ item.count }}</div>
        </div>
      </div>
    </div>
    <div class="courseTable">
      <el-table

        :data="tableData"
        style="width: 100%"
        :default-sort="{ prop: 'date', order: 'descending' }"
        @sort-change="sortChange"
        @row-click="toDetailPage"
      >
        <el-table-column prop="name" label="课程列表" >
          <template slot-scope="scope">
            <div style="display: flex;justify-content: space-between;align-items: center;">
              <div style="display: flex;align-items: center;">
                <!-- <span
              class="course-label"
              :style="{
                color: getCourseLabel(scope.row.fileType) == '视频' ? '#F07800' : '#146DFF'
              }"
              >{{ getCourseLabel(scope.row.fileType) }}
            </span> -->
            <img src="@/assets/image/home/<USER>" alt="" v-if="scope.row.fileType == 2">
            <img src="@/assets/image/home/<USER>" alt="" v-else>
            <span class="course-name">{{ scope.row.title }}</span>
              </div>

            <span
              class="study-status"
              :style="{
                color:
                  scope.row.recordStatusName == '学习中'
                    ? '#146DFF'
                    : scope.row.recordStatusName == '已学习'
                    ? '#03C261'
                    : '#999999'
              }"
              >{{ scope.row.recordStatusName }}</span
            >
            </div>

          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="时间" sortable width="170px;">
        </el-table-column>
      </el-table>
      <!-- <el-dialog title="123" :visible.sync="videoDialog" width="width"  :before-close="dialogBeforeClose">
            <div style="position: absolute;left:50%;transform: translateX(-50%)">
              <video
          autoplay
          muted
          controls
          @ended="onVideoEnded"
          @play="onVideoPlay"
          @pause="onVideoPause"
          :src="detailData.content"
          ref="videoElement"
          @loadedmetadata="onVideoLoadedMetadata"
          @timeupdate="onTimeUpdate"
          class="course-video"
        />
            </div>
        </el-dialog> -->

      <div class="el-page">
        <span>共{{ total }}条，每页{{ pageSize }}条，共{{ Math.ceil(total / pageSize) }}页</span>
        <el-pagination
          background
          @current-change="handleCurrentChange"
          :current-page.sync="currentPage"
          :page-size="pageSize"
          layout="prev, pager, next, jumper"
          :total="total"
        >
        </el-pagination>
      </div>
    </div>
  </div>
</template>

<script>
import { queryCourse, getCategoryList, getCourseDetail, saveStudyRecord } from '@/api/home'
import { mapState } from 'vuex'
export default {
  components: {},
  data() {
    return {
      videoDialog: false,
      videoElement: null,
      total: 0,
      pageSize: 10,
      currentPage: 1,
      order: 1,
      courseName: '',
      courseList: [],
      showCourseList:[],
      tableData: [],

      res: [],
      detailData: {},
      videoUrl: '',
      clickselfcount:0,
      activeCourseId:'',
      lastSearchId:'',
      isAll:true
    }
  },
  computed: { ...mapState('app', ['shopConfig']) },
  created() {
   // this.getCourseData()
  //  this.$bus.$on('setCurPage',(curPage)=>{
  //   console.log('%c [ curPage ]-129', 'font-size:13px; background:pink; color:#bf2c9f;', curPage)
  //   this.currentPage = curPage
  //  })
  },
  watch: {
    shopConfig: {
      handler() {
      ///  this.getCourseData()
      }
    },
    deep: true
  },
  mounted() {
    this.getCourseData()
  },
  methods: {


    changeCourse(id){
       //点击自己偶数次 刷新数据
       if(this.activeCourseId == id){
         this.clickselfcount++
         if(this.clickselfcount%2==0){
          this.isAll = true
          queryCourse(this.getQueryCourseParams()).then((res) => {
            this.tableData = res.result.list
            this.total = res.result.total
            // this.currentPage = res.result.pageNum
            // this.pageSize = res.result.pageSize
          })
          return
         }
       }
       this.clickselfcount = 1
      this.activeCourseId = id
this.isAll = false
      let params = this.getQueryCourseParams()
      params.categoryIdList = [id]
      queryCourse(params).then((res) => {
            this.tableData = res.result.list
            this.total = res.result.total
            // this.currentPage = res.result.pageNum
            // this.pageSize = res.result.pageSize
          })
    },
    searchCourse(){
      let params = this.getQueryCourseParams()
      queryCourse(params).then((res) => {
            this.tableData = res.result.list || []
            this.total = res.result.total
            // this.currentPage = res.result.pageNum
            // this.pageSize = res.result.pageSize
          })
    },
    onVideoEnded(event) {
      console.log(
        '%c [ 结束时间 ]-109',
        'font-size:13px; background:pink; color:#bf2c9f;',
        videoElement.value.duration
      )
    },
    onVideoPlay(event) {
      console.log('%c [ 开始播放 ]-113', 'font-size:13px; background:pink; color:#bf2c9f;', event)
    },
    onVideoPause(event) {
      console.log('%c [ 暂停播放 ]-116', 'font-size:13px; background:pink; color:#bf2c9f;', event)
    },
    onVideoLoadedMetadata(event) {
      if (videoElement.value) {
        const duration = videoElement.value.duration
        console.log(`视频长度: ${duration} 秒`)
      }
    },
    onTimeUpdate(event) {
      // event.target 是触发事件的 video 元素
      const playedSeconds = event.target.currentTime
      console.log(`已播放: ${playedSeconds} 秒`)
    },
    //设置播放点，续播
    playBySeconds(num) {
      if (num && document.getElementById('videoPlayer')) {
        let myVideo = document.getElementById('videoPlayer')
        myVideo.play()
        myVideo.currentTime = num
      }
    },
    getCourseData() {
      getCategoryList({ categoryName: this.courseName }).then((res) => {
        this.courseList = res.result
        this.showCourseList = res.result
        queryCourse(this.getQueryCourseParams()).then((res) => {
            this.tableData = res.result.list
            this.total = res.result.total
            // this.currentPage = res.result.pageNum
          })
      })

    },
    toDetailPage(row) {
      // getCourseDetail({ id: row.id, regMobile: this.shopConfig.mobile }).then((res) => {
      //   this.detailData = res.result
      //   this.videoUrl = res.result.content
      //   this.videoDialog = true
      // })
      this.$emit('toDetailPage', row,this.currentPage)
    },
    sortChange(val) {
      this.order = val.order === 'ascending' ? 0 : 1
    },
    getQueryCourseParams() {
      let categoryIdList = this.courseList.map((item) => item.id)
      return {
        pageNum: this.currentPage,
        pageSize: this.pageSize,
        title: this.courseName,
        categoryIdList: categoryIdList,
        sortType: this.order
        // regMobile: this.shopConfig.mobile
      }
    },
    formatter(row, column) {
      return row.address
    },
    getCourseLabel(type) {
      if (type === 2) return '视频'
      if(type ===1) return '文档'
      return '文章'
    },
    getStudyStatus() {
      return '已完成'
    },
    handleCurrentChange(val) {
      localStorage.setItem('curPage',this.currentPage)
      queryCourse(this.getQueryCourseParams()).then((res) => {

            this.tableData = res.result.list
            this.total = res.result.total
            // this.currentPage = res.result.pageNum
            // this.order = res.result.order
          })

    }
  }
}
</script>

<style scoped>
.courseList {
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  gap: 10px;
  padding: 0px 16px;
  margin-top:10px;
}
.courseTableHeader {
  display: flex;
  justify-content: space-between;
}
.courseItem {
  display: flex;
  align-items: center;
  border: 1px solid #e9e9e9;
  border-radius: 12px;
  padding: 8px;
  cursor: pointer;
  background-color: #f5f5f5;
  position: relative;
}
.courseItem-active{
  border:1px solid #4184D5;
}
.courseIcon {
  height: 40px;
  width:40px;
  object-fit: cover;
}
.courseTable {
  margin-top: 22px;
  padding: 8px;
}
.searchCourse {
  display: flex;
  justify-content: space-between;
  padding: 20px 16px 0;
}
::v-deep   .el-table_1_column_2 {
  text-align: end;
}
.course-label {
  border: 1px solid #e7f0ff;
  margin-right: 5px;
  padding: 2px;
  font-size: 14px;
  padding: 2px;
  border-radius: 4px;

}
.course-name {
  font-size: 14px;
  color:#111111
}
.course-desc{
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  font-size: 14px;
  margin-top: 5px;

  color:#111111;
}
.study-status {
  font-size: 12px;
  float: right;
  border: 1px solid;
  border-radius: 2px;
  line-height: 12px;
  padding: 2px;
  border-radius: 4px;
}

::v-deep   .el-pagination.is-background .el-pager li:not(.disabled):hover {

  cursor: pointer;
}

.el-page {
  display: flex;
  align-items: center;
  justify-content: end;
  margin-top: 10px;
}
::v-deep   .el-table__row {
  cursor: pointer;
}
.course-video {
  background-color: #fff;
  height: 100%;
  border-radius: 5px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.5);
}
::v-deep   .el-dialog__body {
  padding: 0;
}

::v-deep   .el-dialog__header {
  display: none;
}
::v-deep   .cell{
  color:#333333
}
.courseTable img{
  height:16px;
  margin-right:6px;
}
</style>
