<template>
  <div class="orderWrap">
    <div class="Fsearch">
      <el-row type="flex" align="middle" justify="space-between" class="my-row">
        <el-row type="flex" align="middle">
          <span class="sign" />
          <div class="searchMsg">订单管理</div>
        </el-row>
      </el-row>
    </div>
    <div style="font-size: 16px;padding: 0 0 10px 0;" v-if="shopConfig.shopPatternCode !== 'ybm'">
      {{ `${partialShipmentStatus === 1 ? '订单部分发货，系统提醒' : '订单部分发货，未发货商品自动生成退款单'}` }}
      <span
        style="color: #4184d5; cursor: pointer"
        @click="partialShipmentEditVis = true; partialShipmentStatusEdit = partialShipmentStatus;"
      >编辑</span>
    </div>
    <div style="font-size: 16px;padding: 0 0 10px 0;" v-if="shopConfig.shopPatternCode !== 'ybm'">
      {{ `${customerChangeStopOrderStatus === 1 ? '客户信息变更卡单' : '客户信息变更订单正常下发'}` }}
      <span
        style="color: #4184d5; cursor: pointer"
        @click="customerInformationCardSlipVis = true;customerChangeStopOrderStatusEdit = customerChangeStopOrderStatus;"
      >编辑
      </span>
      <span style="color: #4184d5; cursor: pointer;margin-left:10px" @click="descriptionChange">
        客户信息变更卡单功能说明
      </span>
    </div>
    <div>
      <div v-if="refundCount != 0" class="div-info">
        <p>
          <span class="status-span">
            <i style="background: #dcdfe6" />待处理退款单
          </span>
          <el-tooltip effect="dark" placement="top">
            <template #content>部分订单发生退款，请及时处理</template>
            <i class="el-icon-warning-outline"></i>
          </el-tooltip>
        </p>
        <p class="refundCountBox">
          <span class="refundCount">{{ refundCount }}</span>
          <el-button type="primary" size="mini" class="seeCount" @click="jumpPage('refund')">去处理</el-button>
        </p>
      </div>
      <div v-if="unshippedOrdersCount != 0" class="div-info" 
        :class="!isShowUnshippedOrders?'activediv-info':''" 
        @click="searchUnshippedOrders">
        <p>
          <span class="status-span">
            <i style="background: #dcdfe6" />超48小时未发货
          </span>
          <el-tooltip effect="dark" placement="top">
            <template #content>订单超过48小时未发货，请及时处理，以免造成售后问题</template>
            <i class="el-icon-warning-outline"></i>
          </el-tooltip>
        </p>
        <!-- <p style="margin-left:10px">订单</p> -->
        <p class="refundCountBox" >
          <span class="refundCount">{{ unshippedOrdersCount }}</span>          
        </p>
      </div>
      <div v-if="over48hCollectingOrder != 0 " class="div-info" 
        :class="!isShowover48hCollectingOrder?'activediv-info':''" 
        @click="searchover48hCollectingOrder">
        <p>
          <span class="status-span">
            <i style="background: #dcdfe6" />超48h未揽收
          </span>
          <el-tooltip effect="dark" placement="top">
            <template #content>支付后超48h未揽收订单</template>
            <i class="el-icon-warning-outline"></i>
          </el-tooltip>
        </p>
        <!-- <p style="margin-left:10px">订单</p> -->
        <p class="refundCountBox">
          <span class="refundCount">{{ over48hCollectingOrder }}</span>
        </p>
      </div>
      <div v-if="over48hCollectingOrderAfterDelivery != 0 " class="div-info" 
        :class="!isShowover48hCollectingOrderAfterDelivery?'activediv-info':''" 
        @click="searchover48hCollectingOrderAfterDelivery">
        <p>
          <span class="status-span">
            <i style="background: #dcdfe6" />发货后超48h未揽收
          </span>
          <el-tooltip effect="dark" placement="top">
            <template #content>当前状态为配送中，且发货后超48h未揽收的订单（不含商家自配订单）</template>
            <i class="el-icon-warning-outline"></i>
          </el-tooltip>
        </p>
        <!-- <p style="margin-left:10px">订单</p> -->
        <p class="refundCountBox">
          <span class="refundCount">{{ over48hCollectingOrderAfterDelivery }}</span>
          <!-- <el-button
            type="primary"
            size="mini"
            class="seeCount"
            @click="searchover48hCollectingOrderAfterDelivery"
          >{{ isShowover48hCollectingOrderAfterDelivery ? '筛选' : '取消' }}</el-button> -->
        </p>
      </div>
      <div v-if="pullFailedOrderCount != 0" class="div-info" 
          :class="!isShowErpOrders?'activediv-info':''" 
          @click="searchErpOrder">
        <p>
          <span class="status-span">
            <i style="background: #dcdfe6" />下推ERP失败
          </span>
          <el-tooltip effect="dark" placement="top">
            <template #content>订单下推ERP失败，请及时查看并处理</template>
            <i class="el-icon-warning-outline"></i>
          </el-tooltip>
        </p>
        <!-- <p style="margin-left:10px">订单</p> -->
        <p class="refundCountBox">
          <span class="refundCount">{{ pullFailedOrderCount }}</span>
          <!-- <el-button
            type="primary"
            size="mini"
            class="seeCount"
            @click="searchErpOrder"
          >{{ isShowErpOrders ? '筛选' : '取消' }}</el-button> -->
        </p>
      </div>
      <div v-if="evidenceToExamine > 0" class="div-info"
        :class="!isEvidenceToExamine?'activediv-info':''" 
          @click="searchEvidenceToExamine(1)">
        <p>
          <span class="status-span">
            <i style="background: #dcdfe6" />待电汇审核
          </span>
          <el-tooltip effect="dark" placement="top">
            <template #content>订单需要电汇审核，请及时处理</template>
            <i class="el-icon-warning-outline"></i>
          </el-tooltip>
        </p>
        <!-- <p style="margin-left:10px">订单</p> -->
        <p class="refundCountBox">
          <span class="refundCount">{{ evidenceToExamine }}</span>
          <!-- <el-button
            type="primary"
            size="mini"
            class="seeCount"
            @click="searchEvidenceToExamine(1)"
          >{{ isEvidenceToExamine ? '筛选' : '取消' }}</el-button> -->
        </p>
      </div>
      <div v-if="evidenceHaveImage > 0" class="div-info"
        :class="!isEvidenceHaveImage?'activediv-info':''" 
          @click="searchEvidenceToExamine(2)">
        <p>
          <span class="status-span">
            <i style="background: #dcdfe6" />待电汇审核(已上传电汇凭证)
          </span>
          <el-tooltip effect="dark" placement="top">
            <template #content>订单需要电汇审核，客户已上传电汇凭证，请及时处理</template>
            <i class="el-icon-warning-outline"></i>
          </el-tooltip>
        </p>
        <!-- <p style="margin-left:10px">(已上传电汇凭证)</p> -->
        <p class="refundCountBox">
          <span class="refundCount">{{ evidenceHaveImage }}</span>
          <!-- <el-button
            type="primary"
            size="mini"
            class="seeCount"
            @click="searchEvidenceToExamine(2)"
          >{{ isEvidenceHaveImage ? '筛选' : '取消' }}</el-button> -->
        </p>
      </div>
      <div v-if="countCustomerQualification > 0" class="div-info"
         :class="!isCountCustomerQualification?'activediv-info':''" 
          @click="searchCustomerQualification">
        <p>
          <span class="status-span">
            <i style="background: #dcdfe6" />客户信息有变更
          </span>
          <el-tooltip effect="dark" placement="top">
            <template #content>未配送完成的订单且对应的客户资料有更新</template>
            <i class="el-icon-warning-outline"></i>
          </el-tooltip>
        </p>
        <!-- <p style="margin-left:10px">订单</p> -->
        <p class="refundCountBox">
          <span class="refundCount">{{ countCustomerQualification }}</span>
          <!-- <el-button
            type="primary"
            size="mini"
            class="seeCount"
            @click="searchCustomerQualification"
          >{{ isCountCustomerQualification ? '筛选' : '取消' }}</el-button> -->
        </p>
      </div>

      <div
        v-if="logisticsTrackFailCount > 0"
        class="div-info"
        :class="ruleForm.logisticsTrackFail?'activediv-info':''" 
          @click="handleScreenLogistics('logisticsTrackFail')">
        <p>
          <span class="status-span">
            <i style="background: #dcdfe6" />物流轨迹获取失败
          </span>
          <el-tooltip
            effect="dark"
            placement="top"
          >
            <template #content>
              订单发货物流轨迹获取失败，请检查快递公司和单号是否填写正确，如有问题及时修正。订单无物流轨迹可能会影响后期结算，请及时关注处理
            </template>
            <i class="el-icon-warning-outline" />
          </el-tooltip>
        </p>
        <!-- <p style="margin-left:10px">订单</p> -->
        <p class="refundCountBox">
          <span class="refundCount">{{ logisticsTrackFailCount }}</span>
          <!-- <el-button
            type="primary"
            size="mini"
            class="seeCount"
            @click="handleScreenLogistics('logisticsTrackFail')"
          >
            {{ !ruleForm.logisticsTrackFail ? '筛选' : '取消' }}
          </el-button> -->
        </p>
      </div>

      <div
        v-if="logisticsTrackIllegalCount > 0"
        class="div-info"
        :class="ruleForm.logisticsTrackIllegal?'activediv-info':''" 
          @click="handleScreenLogistics('logisticsTrackIllegal')">
        <p>
          <span class="status-span">
            <i style="background: #dcdfe6" />物流轨迹异常提醒
          </span>
          <el-tooltip
            effect="dark"
            placement="top"
          >
            <template #content>
              订单发货快递的揽收或签收时间&lt订单支付时间，请检查快递公司或运单号是否填写错误，如有问题及时修正。订单异常物流轨迹可能会影响后期结算，请及时关注处理
            </template>
            <i class="el-icon-warning-outline" />
          </el-tooltip>
        </p>
        <!-- <p style="margin-left:10px">订单</p> -->
        <p class="refundCountBox">
          <span class="refundCount">{{ logisticsTrackIllegalCount }}</span>
          <!-- <el-button
            type="primary"
            size="mini"
            class="seeCount"
            @click="handleScreenLogistics('logisticsTrackIllegal')"
          >
            {{ !ruleForm.logisticsTrackIllegal ? '筛选' : '取消' }}
          </el-button> -->
        </p>
      </div>

      <div
        v-if="partialShipmentOrderCount > 0"
        class="div-info"
        :class="ruleForm.partialShipment?'activediv-info':''" 
          @click="handleScreenLogistics('partialShipment')">
        <p>
          <span class="status-span">
            <i style="background: #dcdfe6" />部分发货待处理
          </span>
          <el-tooltip
            effect="dark"
            placement="top"
          >
            <template #content>
              订单有商品部分发货，请及时关注处理
            </template>
            <i class="el-icon-warning-outline" />
          </el-tooltip>
        </p>
        <!-- <p style="margin-left:10px">订单</p> -->
        <p class="refundCountBox">
          <span class="refundCount">{{ partialShipmentOrderCount }}</span>
          <!-- <el-button
            type="primary"
            size="mini"
            class="seeCount"
            @click="handleScreenLogistics('partialShipment')"
          >
            {{ !ruleForm.partialShipment ? '筛选' : '取消' }}
          </el-button> -->
        </p>
      </div>

      <div
        v-if="nonInvoiceOrderCount > 0"
        class="div-info"
        :class="ruleForm.invoiceState == 0?'activediv-info':''" 
          @click="handleScreenLogistics('invoiceState')">
        <p>
          <span class="status-span">
            <i style="background: #dcdfe6" />未上传电子发票
          </span>
          <el-tooltip
            effect="dark"
            placement="top"
          >
            <template #content>
              订单未上传电子发票，请尽快上传
            </template>
            <i class="el-icon-warning-outline" />
          </el-tooltip>
        </p>
        <!-- <p style="margin-left:10px">订单</p> -->
        <p class="refundCountBox">
          <span class="refundCount">{{ nonInvoiceOrderCount }}</span>
          <!-- <el-button
            type="primary"
            size="mini"
            class="seeCount"
            @click="handleScreenLogistics('invoiceState');"
          >
            {{ ruleForm.invoiceState == 0 ? '取消' : '筛选' }}
          </el-button> -->
        </p>
      </div>
	  <div v-if="afterSaleCount != 0" class="div-info">
        <p>
          <span class="status-span">
            <i style="background: #dcdfe6" />待处理售后单
          </span>
          <el-tooltip effect="dark" placement="top">
            <template #content>请及时处理售后单据</template>
            <i class="el-icon-warning-outline"></i>
          </el-tooltip>
        </p>
        <p class="refundCountBox">
          <span class="refundCount">{{ afterSaleCount }}</span>
          <el-button type="primary" size="mini" class="seeCount" @click="jumpPage('afterSale')">去处理</el-button>
        </p>
      </div>
    </div>
    <el-row>
      <el-form ref="form" :model="ruleForm" size="small" class="searchMy">
        <el-row :span="24" :gutter="10">
          <el-col :span="7">
            <el-form-item prop="orderNo">
              <el-input v-model="ruleForm.orderNo" placeholder="请输入" clearable>
                <template slot="prepend">订单号</template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="7">
            <el-form-item prop="customerName">
              <el-input v-model="ruleForm.customerName" placeholder="请输入" clearable>
                <template slot="prepend">客户名称</template>
              </el-input>
            </el-form-item>
          </el-col>

          <el-col :span="7" v-if="shopConfig.shopPatternCode !== 'ybm'">
            <el-form-item prop="customerErpCode">
              <el-input v-model="ruleForm.customerErpCode" placeholder="请输入" clearable>
                <template slot="prepend">客户ERP编码</template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="7" v-if="shopConfig.shopPatternCode === 'ybm'">
            <el-form-item prop="trackingNo">
              <el-input v-model="ruleForm.trackingNo" placeholder="请输入" clearable>
                <template slot="prepend">快递单号</template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="3">
            <div v-if="!showMore" class="showMore" @click="showMore = true">
              展开
              <i class="el-icon-arrow-down" />
            </div>
            <div v-else class="showMore" @click="showMore = false">
              收起
              <i class="el-icon-arrow-up" />
            </div>
          </el-col>
        </el-row>
        <el-row :span="24" :gutter="10">
            <el-col :span="7">
              <el-form-item prop="phone">
                <el-input v-model="ruleForm.phone" placeholder="请输入" clearable>
                  <template slot="prepend">手机号码</template>
                </el-input>
              </el-form-item>
            </el-col>

            <el-col :span="9">
              <el-form-item>
                <span class="search-title">下单时间</span>
                <div style="display: table-cell; line-height: 24px">
                  <el-date-picker
                    v-model="time"
                    style="width: 100%;"
                    type="datetimerange"
                    format="yyyy-MM-dd HH:mm:ss"
                    value-format="yyyy-MM-dd HH:mm:ss"
                    range-separator="至"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                    :default-time="['00:00:00', '23:59:59']"
                    @change="dateOnChange"
                    :clearable= 'false'
                    :picker-options="pickerOptions"
                    popper-class="date-style noClear"
                    align="center"
                  />
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="4" style="margin-left: 20px">
              <el-form-item prop="openAccountStatus"  v-show="shopConfig.shopPatternCode !== 'ybm'">
                <div class="flex">
                <span class="search-title">开户状态</span>
                <el-select v-model="ruleForm.openAccountStatus" size="small" placeholder="全部">
                  <el-option label="全部" value />
                  <el-option label="未开户" value="0" />
                </el-select>
              </div>
              </el-form-item>
            </el-col>
          <el-col :span="7" v-if="shopConfig.shopPatternCode === 'ybm'">
            <el-form-item prop="trackingNo">
              <el-input v-model="ruleForm.skuId" placeholder="请输入" clearable>
                <template slot="prepend">sku编码</template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :class="shopConfig.shopPatternCode === 'ybm'?'show_right':''" v-show="!showMore" :span="3" style="text-align: right">
            <el-button size="small" @click="resetForm">重置</el-button>
            <el-button type="primary" size="small" @click="toSerachForm()">查询</el-button>
          </el-col>
        </el-row>
        <el-row v-show="showMore" :span="24" :gutter="10">
          <el-col :span="7">
            <el-form-item prop="customerRemarkStatus">
              <div class="flex">
              <span class="search-title">客户备注</span>
              <el-select v-model="ruleForm.customerRemarkStatus" size="small" placeholder="全部">
                <el-option label="全部" value />
                <el-option label="无备注" value="2" />
                <el-option label="有备注" value="1" />
              </el-select>
            </div>
            </el-form-item>
          </el-col>
          <el-col :span="7">
            <el-form-item prop="merchantRemarkStatus">
              <div class="flex">
              <span class="search-title">商家备注</span>
              <el-select v-model="ruleForm.merchantRemarkStatus" size="small" placeholder="全部">
                <el-option label="全部" value />
                <el-option label="无备注" value="2" />
                <el-option label="有备注" value="1" />
              </el-select>
            </div>
            </el-form-item>
          </el-col>
          <el-col :span="7">
            <el-form-item prop="provinceCodes">
              <div class="flex">
              <span class="search-title">省份</span>
              <el-select v-model="ruleForm.provinceCodes" multiple size="small" placeholder="全部" clearable>
                <el-option
                  v-for="(item,index) in provinceList"
                  :key="index"
                  :label="item.regionName"
                  :value="item.regionCode"
                />
              </el-select>
            </div>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-show="showMore" :span="24" :gutter="10">
          <el-col :span="7">
            <el-form-item prop="firstOrderFlag">
              <div class="flex">
              <span class="search-title">是否首单</span>
              <el-select v-model="ruleForm.firstOrderFlag" size="small" placeholder="全部">
                <el-option label="全部" value />
                <el-option label="是" value="1" />
                <el-option label="否" value="2" />
              </el-select>
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="7">
            <el-form-item prop="orderType">
              <div class="flex">
              <span class="search-title">订单类型</span>
              <el-select v-model="ruleForm.orderType" size="small" placeholder="全部">
                <el-option label="全部" value />
                <el-option label="拼团订单" value="7" />
                <el-option label="批购包邮订单" value="10" />
                <el-option label="其他" value="-1" />
              </el-select>
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="7">
            <el-form-item prop="barcode">
              <el-input v-model="ruleForm.barcode" placeholder="请输入" clearable>
                <template slot="prepend">商品编码</template>
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :span="24" v-show="showMore" :gutter="10">
          <el-col :span="4">
            <el-form-item prop="payType">
              <div class="flex">
              <span class="search-title">支付方式</span>
              <el-select v-model="ruleForm.payType" size="small" placeholder="全部">
                <el-option label="全部" value="" />
                <el-option label="在线支付" :value="1" />
                <el-option label="线下转账" :value="3" />
              </el-select>
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="9">
            <el-form-item>
              <span class="search-title">支付时间</span>
              <div style="display: table-cell; line-height: 24px">
                <el-date-picker
                  v-model="orderPayTime"
                  type="datetimerange"
                  style="width: 100%;"
                  format="yyyy-MM-dd HH:mm:ss"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  :default-time="['00:00:00', '23:59:59']"
                  :picker-options="pickerOptions"
                  popper-class="date-style"
                  @change="orderPayTimeChange"
                />
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="7">
            <el-form-item prop="productName">
              <el-input v-model="ruleForm.productName" placeholder="请输入" clearable>
                <template slot="prepend">商品名称</template>
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row v-show="showMore" :span="24" :gutter="10" v-if="shopConfig.shopPatternCode !== 'ybm'">
          <el-col :span="7" v-show="shopConfig.shopPatternCode !== 'ybm'">
            <el-form-item prop="payChannel">
              <div class="flex">
              <span class="search-title">支付渠道</span>
              <el-select v-model="ruleForm.payChannel" size="small" placeholder="全部">
                <el-option label="全部" value="" />
                <el-option label="微信" :value="2" />
                <el-option label="支付宝" :value="1" />
                <el-option label="电汇平台" :value="7" />
                <el-option label="电汇商业" :value="8" />
                <el-option label="平安ePay" :value="10" />
                <el-option label="JD银行卡支付" :value="11" />
                <el-option label="京东采购融资" :value="12" />
                <el-option label="小雨点白条" :value="14" />
                <el-option label="金蝶信用付" :value="15" />
              </el-select>
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="7" v-show="shopConfig.shopPatternCode !== 'ybm'">
            <el-form-item prop="productErpCode">
              <el-input v-model="ruleForm.productErpCode" placeholder="请输入" clearable>
                <template slot="prepend">商品ERP编码</template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="7">
            <el-form-item prop="trackingNo">
              <el-input v-model="ruleForm.trackingNo" placeholder="请输入" clearable>
                <template slot="prepend">快递单号</template>
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
      <el-row :span="24" :gutter="10">
          <el-col :span="7" v-if="shopConfig.shopPatternCode !== 'ybm'">
            <el-form-item prop="trackingNo">
              <el-input v-model="ruleForm.skuId" placeholder="请输入" clearable>
                <template slot="prepend">sku编码</template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="7">
            <el-form-item prop="orderNo">
              <el-input v-model="ruleForm.id" placeholder="请输入" clearable>
                <template slot="prepend">订单ID</template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="7">
            <el-form-item prop="payChannel">
              <div class="flex">
              <span class="search-title">关联资质状态</span>
              <el-select v-model="ruleForm.exceptionFlag" size="small" placeholder="全部">
                <el-option label="全部" value="" />
                <el-option label="无异常" :value="0" />
                <el-option label="有异常" :value="1" />
              </el-select>
            </div>
            </el-form-item>
          </el-col>
          <el-col :span="7" v-show="shopConfig.shopPatternCode !== 'ybm'">
            <el-form-item prop="payChannel">
              <div class="flex">
              <span class="search-title">随心拼订单</span>
              <el-select v-model="ruleForm.isRandom" size="small" placeholder="全部">
                <el-option label="全部" value="" />
                <el-option label="是" :value="1" />
                <el-option label="否" :value="0" />
              </el-select>
            </div>
            </el-form-item>
          </el-col>
          <el-col :span="7">
            <el-form-item prop="nextDayDelivery">
              <div class="flex">
              <span class="search-title">次日达订单</span>
              <el-select v-model="ruleForm.nextDayDelivery" size="small" placeholder="全部">
                <el-option label="全部" :value="null" />
                <el-option label="是" :value="1" />
                <el-option label="否" :value="0" />
              </el-select>
            </div>
            </el-form-item>
          </el-col>
          <el-col :span="7">
            <el-form-item v-show="showMore" prop="businessTypeIds" >
              <div class="flex">
              <span class="search-title">客户类型</span>
              <el-select v-model="ruleForm.businessTypeIds" multiple size="small" clearable collapse-tags placeholder="全部">
                <el-option
                  v-for="item in businessTypeList"
                  :key="item.businessTypeId"
                  :label="item.businessTypeName"
                  :value="item.businessTypeId"
                />
              </el-select>
            </div>
            </el-form-item>
          </el-col>
          <el-col :span="7" v-show="showMore">
            <el-form-item prop="billType">
              <div class="flex">
              <span class="search-title">发票类型</span>
              <el-select v-model="ruleForm.billType" size="small" placeholder="全部">
                <el-option label="全部" value="" />
                <el-option label="普票" :value="1" />
                <el-option label="专票" :value="2" />
              </el-select>
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="7" v-show="showMore">
            <el-form-item prop="collectStatus">
              <div class="flex">
              <span class="search-title">揽收情况</span>
              <el-select v-model="ruleForm.collectStatus" size="small" placeholder="全部">
                <el-option label="全部" value="" />
                <el-option label="已揽收" :value="1" />
                <el-option label="未揽收" :value="0" />
              </el-select>
              </div>
            </el-form-item>
          </el-col>
      </el-row>
      <el-row v-show="showMore" :span="24">
        <el-col :span="24" style="text-align: right">
          <el-button size="small" @click="resetForm">重置</el-button>
          <el-button type="primary" size="small" @click="toSerachForm()">查询</el-button>
        </el-col>
      </el-row>
      </el-form>
    </el-row>
    <el-row style="marginTop: 20px">
      <el-tabs v-model="activeOrderStatus" @tab-click="handleClick">
        <el-tab-pane v-for="item in tabData" :key="item.tabStatus" :name="item.tabStatus">
          <span slot="label">
            {{ item.tabName }}
            <span v-if="item.tabName == '资质审核'">
              <el-tooltip effect="dark" placement="top">
                <template #content>因客户信息变更卡单、未开户，而虚拟客户下发的订单。客户信息确认、开户后，系统会自动重新下推正确客户信息</template>
                <i class="el-icon-warning-outline"></i>
              </el-tooltip>
            </span>
            <span
              class="countBox"
            >{{ countObj[item.tabCount] > 999 ? '999+' : countObj[item.tabCount] || 0 }}</span>
          </span>
          <tab-component
            v-if="item.tabStatus == activeOrderStatus"
            ref="tabCom"
            :active-order-status="activeOrderStatus"
            :logistics-company-list="logisticsCompanyList"
            :show-confirm-finish-button="showConfirmFinishButton"
            :table-data="tableData"
            :rule-form="ruleForm"
            :list-query="listQuery"
            :loading="loading"
            :tab-data="tabData"
            @refreshList="refreshList"
          />
        </el-tab-pane>
      </el-tabs>
    </el-row>
    <el-row>
      <div class="pagination-container">
        <div
          class="pag-text"
        >共 {{ listQuery.total }} 条数据，每页{{ listQuery.pageSize }}条，共{{ Math.ceil(listQuery.total / listQuery.pageSize) || 0 }}页</div>
        <el-pagination
          :page-size="listQuery.pageSize"
          :page-sizes="[10, 20, 30, 50, 100, 200]"
          prev-text="上一页"
          next-text="下一页"
          layout="sizes, prev, pager, next, jumper"
          :total="listQuery.total"
          :current-page="listQuery.pageNum"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-row>

    <el-backtop target=".app-main" :visibility-height="400">

    </el-backtop>

    <el-dialog
      title="编辑"
      :visible="partialShipmentEditVis"
      width="60%"
      class="partialShipmentDialog"
      @close="partialShipmentEditVis = false"
    >
      <div>
        <el-radio-group
          v-model="partialShipmentStatusEdit"
          style="line-height: 40px;"
        >
          <el-radio :label="1">
            订单部分发货，系统提醒
          </el-radio>
          <el-radio :label="2">
            订单部分发货，未发货商品自动生成退款单
          </el-radio>
        </el-radio-group>
      </div>
      <span slot="footer">
        <el-button
          size="mini"
          @click="partialShipmentEditVis = false"
        >取 消</el-button>
        <el-button
          type="primary"
          size="mini"
          @click="updateConfiguration"
        >确 定</el-button>
      </span>
    </el-dialog>
    <el-dialog
      title="编辑"
      :visible="customerInformationCardSlipVis"
      width="60%"
      class="partialShipmentDialog"
      @close="customerInformationCardSlipVis = false"
    >
      <div>
        <el-radio-group
          v-model="customerChangeStopOrderStatusEdit"
          style="line-height: 40px;margin-left:15px"
        >
          <el-radio :label="1">
            客户信息变更卡单
          </el-radio>
          <el-radio :label="0">
            客户信息变更订单正常下发
          </el-radio>
        </el-radio-group>
        <div>
          <p>如担心客户核心证件变更，造成开错票、发错货的情况，您可选择客户信息变更卡单</p>
          <p>a.已对接未开户先占库存，当客户核心证件变更后，订单会以虚拟客户的形式下发先</p>
          <p>占库存，待在卖家中心确认客户信息更新后，更新正确的客户ERP</p>
          <p>b.未对接未开户先占库存，当客户核心证件变更后，订单会先卡单，不下发ERP</p>
        </div>
      </div>
      <span slot="footer">
        <el-button
          size="mini"
          @click="customerInformationCardSlipVis = false"
        >取 消</el-button>
        <el-button
          type="primary"
          size="mini"
          @click="updateConfigurationCardSlipVis"
        >确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import { listOrder, orderStatusCount, getRefundCount, queryTimeoutOrderCount,queryTimeoutReceiptOrderCount,queryTimeoutReceiptOrderCountAfterShip,queryPartialShipmentOrderCount, queryPullFailedOrderCount, evidenceCount, apiQueryCustomerChangedOrderCount, queryLogisticsRemind, selectConfigurationByOrgId, updateConfiguration, apiQueryNonInvoiceOrderCount, getAfterSaleCount } from '@/api/order/index';
import { getProvince,getBusinessType } from '@/api/customer-management/index';
import { mapState } from 'vuex';
import { actionTracking } from '@/track/eventTracking';
import tabComponent from './components/tabComponent';

export default {
  name: 'OrderList',
  components: { tabComponent },
  data() {
    return {
      partialShipmentEditVis: false,
      partialShipmentStatus: 1,
      partialShipmentStatusEdit: 1,
      customerChangeStopOrderStatus: 1,
      customerChangeStopOrderStatusEdit: 1,
      customerInformationCardSlip:1,
      customerInformationCardSlipVis:false,
      activeOrderStatus: '10',
      time: [],
      orderPayTime: [],
      countObj: {},
      tableData: [],
      showMore: false,
      businessTypeList: [],
      editRow: {},
      refundCount: 0,
      loading: false,
      listQuery: {
        total: 0,
        pageNum: 1,
        pageSize: 10,
      },
      tabData: [{
        tabName: '待付款',
        tabStatus: '10',
        tabCount: 'waitPayCount',
      }, {
        tabName: '待审核',
        tabStatus: '1',
        tabCount: 'waitExamineCount',
      }, {
        tabName: '资质审核',
        tabStatus: 'qualificationRemindCount',
        tabCount: 'qualificationRemindCount',
      }, {
        tabName: '正在开单',
        tabStatus: '7',
        tabCount: 'waitDeliveryCount',
      }, {
        tabName: '分拣中',
        tabStatus: '32',
        tabCount: 'sortingCount',
      }, {
        tabName: '待配送',
        tabStatus: '33',
        tabCount: 'deliveryCount',
      }, {
        tabName: '配送中',
        tabStatus: '2',
        tabCount: 'inDeliveryCount',
      }, {
        tabName: '已完成',
        tabStatus: '3',
        tabCount: 'completedCount',
      }, {
        tabName: '已取消',
        tabStatus: '4',
        tabCount: 'cancelledCount',
      }, {
        tabName: '已退款',
        tabStatus: '91',
        tabCount: 'refundedCount',
      }],
      ruleForm: {
        id: '',
        orderNo: '',
        openAccountStatus: '',
        status: 10,
        orderCreateTimeStart: '',
        orderCreateTimeEnd: '',
        customerRemarkStatus: '',
        merchantRemarkStatus: '',
        customerName: '',
        customerErpCode: '',
        phone: '',
        provinceCodes: [],
        statusList: '',
        firstOrderFlag: '',
        orderType: '',
        orderSyncStatus: '',
        evidenceToExamine: '',
        evidenceHaveImage: '',
        qualificationChanged: null,
        barcode: '',
        productErpCode: '',
        productName: '',
        trackingNo: '',
        payType: '',
        payChannel: '',
        orderPayTimeStart: '',
        orderPayTimeEnd: '',
        logisticsTrackFail: false,
        logisticsTrackIllegal: false,
        partialShipment: null,
        invoiceState: null,
        over48hCollectingOrder:null,
        over48hCollectingOrderAfterDelivery:null,
        isRandom: '',
        nextDayDelivery:null,//次日达
        exceptionFlag: '',
        businessTypeIds:[],//客户类型
        billType:'',//发票类型
        collectStatus:'',//揽收情况
      },
      provinceList: [],
      unshippedOrdersCount: 0,
      pullFailedOrderCount: 0,
      over48hCollectingOrder:0,
      over48hCollectingOrderAfterDelivery:0,
	    afterSaleCount: 0,
      countCustomerQualification: 0,
      logisticsTrackFailCount: 0,
      logisticsTrackIllegalCount: 0,
      isShowUnshippedOrders: true,
      isShowover48hCollectingOrder: true,
      isShowover48hCollectingOrderAfterDelivery: true,
      isShowErpOrders: true,
      orderCountTimer: '', // 48小时超时计时器
      showConfirmFinishButton: false,
      logisticsCompanyList: [],
      isFirst: true,
      evidenceToExamine: 0,
      isEvidenceToExamine: true,
      evidenceHaveImage: 0,
      isEvidenceHaveImage: true,
      isCountCustomerQualification: true,
      partialShipmentOrderCount: 0,
      nonInvoiceOrderCount: 0,
      pickerOptions: {
        shortcuts: [
          {
            text: '近6个月',
            onClick(picker) {
              let now = dayjs();
              let last6Months = now.subtract(6, 'month');
              // 证传入的日期数组的日期为Date类型
              now = new Date(now)
              last6Months = new Date(last6Months)
              picker.$emit('pick', [last6Months, now]);  
            }
          },
           {
            text: '近1年',  
            onClick(picker) {  
              let now = dayjs();
              let last1Year = now.subtract(1, 'year'); 
              // 证传入的日期数组的日期为Date类型
              now = new Date(now)
              last1Year = new Date(last1Year)
              picker.$emit('pick', [last1Year, now]);  
            }  
          },
          {  
            text: '一年前',  
            onClick(picker) {
              let now = dayjs();  
              let last1Year = now.subtract(1, 'year');
              let last3Year = now.subtract(2, 'year');
              last1Year = new Date(last1Year)
              last3Year = new Date(last3Year)
              picker.$emit('pick', [last3Year, last1Year]);  
            }  
          }  
        ]
      }
    };
  },
  computed: { ...mapState('app', ['shopConfig']) },
  activated() {
    this.activate();
  },
  mounted() {
    this.toSerachForm();
  },
  created() {
    this.selectConfigurationByOrgId();
    this.initDate();
    this.getProvince();
    this.getBusinessType();
    if (Number(this.$route.query.homeEnter) !== 1) {
      this.refreshList(1);
    }
    //  10分钟刷新
    this.orderCountTimer = setInterval(() => {
      this.queryPartialShipmentOrderCount();
      this.queryTimeoutOrderCount();
      this.queryTimeoutReceiptOrderCountAfterShip();
      this.queryTimeoutReceiptOrderCount();
      this.queryPullFailedOrderCount();
      this.queryLogisticsRemind();
    }, 1000 * 60 * 10);
    if (this.$route.query.orderNoFromGroupSalesData) {
      this.ruleForm.orderNo = this.$route.query.orderNoFromGroupSalesData;
    }
    window.clearData['/orderList'] = () => {
      this.resetForm();
    }
  },
  beforeDestroy() {
    // 注销前
    clearInterval(this.orderCountTimer);
  },
  methods: {
    /**客户信息变更说明 */
    descriptionChange(){
      const h = this.$createElement;
      this.$msgbox({
        title: '功能说明',
        message: h('div', null, [
          h('div', { style: 'font-weight:bold;' }, '如果开启了客户信息变更卡单功能且做了ERP对接'),
          h('div', { style: 'font-weight:bold;' }, '1、未开通未开户先占库存'),
          h('span', null, '有“信息有变更”标识的客户下单并支付后，订单会在待审核页面不下推，点击订单上红色“信息'),
          h('span', null, '有变更”图标并点击“确认已更新”后，系统才会定时下推订单信息'),
          h('div', { style: 'font-weight:bold;' }, '2、开通了未开户先占库存'),
          h('span', null, '有“信息有变更”标识的客户下单并支付后，订单会先虚拟客户下推，并在资质审核页面，点击'),
          h('span', null, '订单上红色“信息有变更”图标并点击“确认已更新”后，系统会定时重新下推正确的客户信息。'),
          h('span', null, '此时订单会跳转到正在开单页面'),
        ]),
        confirmButtonText: '我知道了',
      }).then(action => {
      })
    },
    // 获取药店类型
    getBusinessType() {
      getBusinessType().then((res) => {
        // (res.data || []).unshift({
        //   businessTypeName: '全部',
        //   businessTypeIds: '',
        // });
        this.businessTypeList = res.data || [];
      });
    },
	getAfterSaleCount() {
		getAfterSaleCount().then(res => {
			this.afterSaleCount =  (res || {}).result || 0;
		})
	},
    updateConfiguration() {
      updateConfiguration({ partialShipmentStatus: this.partialShipmentStatusEdit }).then((res) => {
        if (res.code === 0) {
          this.$message.success(res.result || '修改成功');
          this.partialShipmentStatus = this.partialShipmentStatusEdit;
          this.partialShipmentEditVis = false;
        }
      });
    },
    /**客户信息变更卡单编辑 */
    updateConfigurationCardSlipVis(){
      updateConfiguration({ customerChangeStopOrderStatus: this.customerChangeStopOrderStatusEdit }).then((res) => {
        if (res.code === 0) {
          this.$message.success(res.result || '修改成功');
          this.customerChangeStopOrderStatus = this.customerChangeStopOrderStatusEdit;
          this.customerInformationCardSlipVis = false;
        }
      });
    },
    selectConfigurationByOrgId() {
      selectConfigurationByOrgId().then((res) => {
        // console.log(23232323232, res);
        if (res.code === 0) {
          this.partialShipmentStatus = res.result.partialShipmentStatus;
          this.customerChangeStopOrderStatus = res.result.customerChangeStopOrderStatus;
        }
      });
    },
    handleScreenLogistics(key) {
      this.isShowUnshippedOrders = true;
      this.isShowover48hCollectingOrder = true;
      this.isShowover48hCollectingOrderAfterDelivery = true;
      this.isShowErpOrders = true;
      this.isEvidenceToExamine = true;
      this.isEvidenceHaveImage = true;
      this.isCountCustomerQualification = true;
      this.ruleForm.statusList = '';
      this.ruleForm.orderSyncStatus = '';
      this.ruleForm.qualificationChanged = null;
      this.ruleForm.evidenceToExamine = '';
      this.ruleForm.evidenceHaveImage = '';
      this.ruleForm.over48hCollectingOrder = null;
      this.ruleForm.over48hCollectingOrderAfterDelivery = null;
      // this.ruleForm.invoiceState= null ;

      if (key !== 'partialShipment' && key !== 'invoiceState') { // 不需要翻转invoiceState...
        this.ruleForm[key] = !this.ruleForm[key];
      }
      if (key === 'logisticsTrackFail' && this.ruleForm.logisticsTrackFail) {
        this.ruleForm.logisticsTrackIllegal = false;
        this.ruleForm.partialShipment = null;
        this.ruleForm.invoiceState= null ;
      }
      if (key === 'logisticsTrackIllegal' && this.ruleForm.logisticsTrackIllegal) {
        this.ruleForm.logisticsTrackFail = false;
        this.ruleForm.partialShipment = null;
        this.ruleForm.invoiceState= null ;
      }
      if (key === 'partialShipment') {
        this.ruleForm[key] = this.ruleForm[key] ? null : 1;
        this.ruleForm.invoiceState= null ;
        if (this.ruleForm[key]) {
          this.ruleForm.logisticsTrackIllegal = false;
          this.ruleForm.logisticsTrackFail = false;
        }
      }
      if (key ==='invoiceState') {
        this.ruleForm.partialShipment = null;
        actionTracking('order_management_top_quick_search', { filter_item: 'no_invoice' });
        this.ruleForm.invoiceState = (this.ruleForm.invoiceState == 0 ? null : 0);
      }
      this.refreshList();
    },
    queryLogisticsRemind() {
      queryLogisticsRemind().then((res) => {
        if (res.code === 0) {
          this.logisticsTrackFailCount = (res.result || {}).logisticsTrackFailCount || 0;
          this.logisticsTrackIllegalCount = (res.result || {}).logisticsTrackIllegalCount || 0;
        }
      });
    },
    activate() {
      const { query } = this.$route;
      if (query && Object.keys(query).length > 0) {
        this.resetForm('clearParams');
		    this.time = [];
        Object.keys(query).map((key) => {
          if (key === 'time') {
            this.orderPayTime = query[key];
          } else if (key === 'status') {
            this.activeOrderStatus = String(query[key]);
            this.$set(this.ruleForm, 'status', Number(query[key]));
          } else if(key === 'createTime') {
            this.time = [`${query[key]}`, `${new Date().getFullYear()}-${new Date().getMonth() + 1}-${new Date().getDate()} 23:59:59`];
          }else {
            this.$set(this.ruleForm, key, query[key]);
          }

          if (key === 'orderSyncStatus' && Number(query[key]) === 2) {
            this.isShowErpOrders = false;
          }
          if (key === 'statusList' && query[key] === '[1,7]') {
            this.isShowUnshippedOrders = false;
          }
          if (key === 'logisticsTrackFail' || key === 'logisticsTrackIllegal') {
            this.ruleForm[key] = true;
          }
          if (key === 'partialShipment') {
            this.ruleForm[key] = 1;
          }
          if (key === 'orderNoFromGroupSalesData') {
            this.ruleForm.orderNo = query[key];
          }
          if(key === 'orderNo') {
            this.ruleForm.orderNo = query[key];
            return this.getStatusCount(0, 'orderNo')
          }
        });
        this.$nextTick(() => {
          this.toSerachForm(1);
        });
      }
    },
    getProvince() {
      getProvince().then((res) => {
        // (res.data || []).unshift({
        //   regionName: '全部',
        //   regionCode: '',
        // });
        this.provinceList = res.data || [];
      });
    },
    refreshList(from) {
      this.getDataList();
      this.getStatusCount(from);
      if (this.$refs.tabCom) {
        this.$refs.tabCom.map((item) => {
          if (item.clearSelection) {
            item.clearSelection();
          }
        });
      }
    },
    initDate() {
      const date = new Date();
      date.setMonth(date.getMonth() - 3);
      date.toLocaleDateString();
      const y = date.getFullYear();
      let m = date.getMonth() + 1;
      m = m < 10 ? (`0${m}`) : m;
      let d = date.getDate();
      d = d < 10 ? (`0${d}`) : d;
      const time = `${y}-${m}-${d}`;
      this.time = [`${time} 00:00:00`, `${new Date().getFullYear()}-${new Date().getMonth() + 1}-${new Date().getDate()} 23:59:59`];
    },

    handleClick(tab) {
      this.tableData = [];
      this.ruleForm = {
        ...this.ruleForm,
        status: tab.name == 'qualificationRemindCount' ? 'qualificationRemindCount': Number(tab.name),
        qualificationAudit: tab.name == 'qualificationRemindCount' ? 1: null,
      };
      this.listQuery = {
        total: 0,
        pageNum: 1,
        pageSize: this.listQuery.pageSize ? this.listQuery.pageSize : 10,
      };
      this.util.clearLoacl('orderListLocal');
      this.getDataList();
    },
    getStatusCount(from,haveOrderNo) {
      const params = { ...this.ruleForm };
      delete params.status;
      params.qualificationAudit = null;
      params.provinceCodes = params.provinceCodes.length > 0 ? JSON.stringify(params.provinceCodes) : null;
      params.businessTypeIds = params.businessTypeIds.length > 0 ? params.businessTypeIds.join(',') : null;
      orderStatusCount({ ...params }).then((res) => {
        this.countObj = (res || {}).data || [];
        if (!res.data || res.data.length < 1) {
          return;
        }
        const { query } = this.$route;
        if ((query && query.openAccountStatus === '0') || this.ruleForm.logisticsTrackFail || this.ruleForm.logisticsTrackIllegal) {
          const hasCount = this.tabData.find((item) => {
            if (res.data[item.tabCount] > 0) {
              return item.tabStatus;
            }
          });
          if (hasCount) {
            this.activeOrderStatus = hasCount.tabStatus;
            this.handleClick({ name: hasCount.tabStatus });
          }
        }
        if(haveOrderNo) {
          this.$nextTick(() => {
            let target
            for (const key in this.countObj) {
              if(this.countObj[key] > 0) {
                target = key
                break
              }
            }
            const gotoTabCount = this.tabData.find(tab => tab.tabCount === target);
            this.activeOrderStatus=gotoTabCount.tabStatus
            this.handleClick({ name: gotoTabCount.tabStatus });
          })
        }
      });
    },
    // 查询订单部分发货配置信息
    queryPartialShipmentOrderCount() {
      queryPartialShipmentOrderCount().then((res) => {
        this.partialShipmentOrderCount = (res || {}).result || 0;
      });
    },
    // 查询超48小时未发货订单数
    queryTimeoutOrderCount() {
      queryTimeoutOrderCount().then((res) => {
        this.unshippedOrdersCount = (res || {}).result || 0;
      });
    },
    // 查询超48小时未揽收订单数
    queryTimeoutReceiptOrderCount() {
      queryTimeoutReceiptOrderCount().then((res) => {
        this.over48hCollectingOrder = (res || {}).result || 0;
      });
    },
    // 查询发货后超48小时未发货订单数
    queryTimeoutReceiptOrderCountAfterShip() {
      queryTimeoutReceiptOrderCountAfterShip().then((res) => {
        this.over48hCollectingOrderAfterDelivery = (res || {}).result || 0;
      });
    },
    queryPullFailedOrderCount() {
      queryPullFailedOrderCount().then((res) => {
        this.pullFailedOrderCount = (res || {}).result || 0;
      });
    },
    // 查询客户资质有变更的订单数量
    queryCustomerChangedOrderCount() {
      apiQueryCustomerChangedOrderCount().then((res) => {
        this.countCustomerQualification = (res || {}).result || 0;
        if (this.countCustomerQualification > 0 && !this.isCountCustomerQualification) {
          this.ruleForm.qualificationChanged = 1;
        } else {
          this.ruleForm.qualificationChanged = null;
        }
      });
    },
    // 查询未上传电子发票数量
    queryNonInvoiceOrderCount() {
      apiQueryNonInvoiceOrderCount().then((res) => {
        if (res.code == 0) {
			this.nonInvoiceOrderCount = res.result;
		}
		console.log('nonInvoiceOrderCount', this.nonInvoiceOrderCount);
      });
    },
    evidenceCount() {
      evidenceCount().then((res) => {
        if (res.code === 0) {
          this.evidenceToExamine = res.data.evidenceToExamine ? res.data.evidenceToExamine:0;
          this.evidenceHaveImage = res.data.evidenceHaveImage ? res.data.evidenceHaveImage:0;
        }
      });
    },
    handleCurrentChange(val) {
      this.util.clearLoacl('orderListLocal');
      this.listQuery.pageNum = val;
      this.getDataList();
    },
    handleSizeChange(size) {
      this.util.clearLoacl('orderListLocal');
      this.listQuery.pageSize = size;
      this.getDataList();
    },
    getDataList() {
      getRefundCount().then((res) => {
        this.refundCount = (res || {}).result || 0;
      });
      // 查询物流异常信息
      this.queryLogisticsRemind();
      // 查询订单部分发货配置信息
      this.queryPartialShipmentOrderCount();
      // 查询超48小时未发货订单数
      this.queryTimeoutOrderCount();
      // 查询超48小时未揽收订单数
      this.queryTimeoutReceiptOrderCount();
      // 查询发货后超48小时未发货订单数
      this.queryTimeoutReceiptOrderCountAfterShip();
      // 查询下推ERP失败订单数
      this.queryPullFailedOrderCount();
      // 查询电汇审核
      this.evidenceCount();
      // 查询客户资质有变更的订单数量
      this.queryCustomerChangedOrderCount();
      // 查询未上传电子发票数量
      this.queryNonInvoiceOrderCount();
	    this.getAfterSaleCount();
      this.dateOnChange(this.time);
      this.orderPayTimeChange(this.orderPayTime);
      this.loading = true;
      let queryInfo = {};
      const localInfo = this.util.getLocal('orderListLocal');
      if (localInfo) {
        queryInfo = localInfo;
        this.time = [this.formatDate(localInfo.orderCreateTimeStart || ''), this.formatDate(localInfo.orderCreateTimeEnd || '')];
        this.orderPayTime = [this.formatDate(localInfo.orderPayTimeStart || ''), this.formatDate(localInfo.orderPayTimeStart || '')];
        this.listQuery.pageNum = localInfo.pageNum;
        this.listQuery.pageSize = localInfo.pageSize;
        this.ruleForm = { ...queryInfo };
        this.activeOrderStatus = localInfo.activeOrderStatus;
      } else {
        delete this.ruleForm.pageNum;
        delete this.ruleForm.pageSize;
        queryInfo = { ...this.listQuery, ...this.ruleForm };
      }
      queryInfo.provinceCodes = queryInfo.provinceCodes.length > 0 ? JSON.stringify(queryInfo.provinceCodes) : null;
      queryInfo.businessTypeIds = queryInfo.businessTypeIds.length > 0 ? queryInfo.businessTypeIds.join(',') : null;
      queryInfo.orderCreateTimeEnd = this.checkTime(queryInfo.orderCreateTimeEnd, "orderCreateTimeEnd");
      queryInfo.orderCreateTimeStart = this.checkTime(queryInfo.orderCreateTimeStart, "orderCreateTimeStart");
      queryInfo.orderPayTimeEnd = this.checkTime(queryInfo.orderPayTimeEnd, "orderPayTimeEnd");
      queryInfo.orderPayTimeStart = this.checkTime(queryInfo.orderPayTimeStart, "orderPayTimeStart");
      console.log(queryInfo, 'queryInfo-******last')
      if(queryInfo.over48hCollectingOrder === 1 || queryInfo.over48hCollectingOrderAfterDelivery === 1){
        queryInfo.statusList = '';
      }
      if(this.activeOrderStatus != 'qualificationRemindCount'){
        // alert(555)
        delete queryInfo.qualificationAudit;
      }else{
        queryInfo.status = 7;
        queryInfo.qualificationAudit = 1;
        // alert(444)
      }
      listOrder(queryInfo).then((res) => {
        if (res.code === 0) {
          // console.log(`request with ${JSON.stringify(res)}, info = ${JSON.stringify(queryInfo)}`)
          const { total, pageNum, pageSize } = (res || {}).data || {};
          this.tableData = ((res || {}).data || {}).list || [];
          if (res.data && !res.data.list) {
            this.tableData = [];
          }
          this.listQuery = {
            ...this.listQuery,
            total,
            pageNum: pageNum || 1,
            pageSize: pageSize || 10,
          };
          this.showConfirmFinishButton = res.data.showConfirmFinishButton;
          this.logisticsCompanyList = res.data.logisticsCompanyList ? res.data.logisticsCompanyList : [];
        } else {
          this.$message.error(res.message);
        }
        this.loading = false;
      });
    },
    checkTime(time, type) {
      if (!Number.isNaN(time) && time !== "") {
        return time;
      } else {
        // this.$message.error("参数错误！");
        console.log(time, type, '验证参数');
        return "";
      }
    },
    dateOnChange(val) {
      if (val === null) {
        this.ruleForm.orderCreateTimeStart = '';
        this.ruleForm.orderCreateTimeEnd = '';
        val = '';
        this.time = [];
      } else if (typeof val[0] === 'string') {
        this.ruleForm.orderCreateTimeStart = new Date(val[0]).getTime();
        this.ruleForm.orderCreateTimeEnd = new Date(val[1]).getTime();
      }
    },
    orderPayTimeChange(val) {
      if (Array.isArray(val) && val[0]) {
        this.ruleForm.orderPayTimeStart = new Date(val[0]).getTime();
        this.ruleForm.orderPayTimeEnd = new Date(val[1]).getTime();
      } else {
        this.ruleForm.orderPayTimeStart = '';
        this.ruleForm.orderPayTimeEnd = '';
        val = '';
        this.orderPayTime = [];
      }
    },
    toSerachForm(from) {
      // console.log(`force query `)
      this.util.clearLoacl('orderListLocal');
      this.listQuery = {
        total: 0,
        pageNum: 1,
        pageSize: this.listQuery.pageSize ? this.listQuery.pageSize : 10,
      };
      this.refreshList(from);
    },
    resetForm(str) {
      this.util.clearLoacl('orderListLocal');
      // this.$refs.form.resetFields();
      this.initDate()
      this.isShowUnshippedOrders = true;
      this.isShowover48hCollectingOrder = true;
      this.isShowover48hCollectingOrderAfterDelivery = true;
      this.isShowErpOrders = true;
      this.isEvidenceToExamine = true;
      this.isEvidenceHaveImage = true;
      this.isCountCustomerQualification = true;
      this.listQuery = {
        total: 0,
        pageNum: 1,
        pageSize: this.listQuery.pageSize ? this.listQuery.pageSize : 10,
      };
      this.orderPayTimeChange(null);
      this.ruleForm = {
        orderNo: '',
        openAccountStatus: '',
        status: this.activeOrderStatus,
        orderCreateTimeStart: '',
        orderCreateTimeEnd: '',
        customerRemarkStatus: '',
        merchantRemarkStatus: '',
        customerName: '',
        customerErpCode: '',
        phone: '',
        provinceCodes: [],
        statusList: '',
        firstOrderFlag: '',
        orderType: '',
        orderSyncStatus: '',
        evidenceToExamine: '',
        evidenceHaveImage: '',
        qualificationChanged: null,
        barcode: '',
        productErpCode: '',
        productName: '',
        trackingNo: '',
        payType: '',
        payChannel: '',
        orderPayTimeStart: '',
        orderPayTimeEnd: '',
        logisticsTrackFail: false,
        logisticsTrackIllegal: false,
        partialShipment: null,
        invoiceState: null,
        over48hCollectingOrder:null,
        over48hCollectingOrderAfterDelivery:null,
        isRandom: '',
        nextDayDelivery:null,
        exceptionFlag: '',
        businessTypeIds:[],//客户类型
        billType:'',//发票类型
        collectStatus:'',//揽收情况
      };
      // if(this.activeOrderStatus == 'qualificationRemindCount'){
      //   alert(111)
      // }
      if(this.ruleForm.status == 'qualificationRemindCount'){
        // alert(111)
        this.ruleForm.status = 7;
        this.ruleForm.qualificationAudit = 1;
      }else{
        this.ruleForm.qualificationAudit = null;
      }
      if (str !== 'clearParams') {
        this.refreshList(1);
      }
    },
    // checkPermission,
    jumpPage(key) {
      // 打开退款单管理页面
      // const mid = 999988;
      // window.top.$('#mainFrameTabs').bTabsAdd(mid, '售后管理', '/afterSales/index?auditStateListJson=true');
      // if(this.checkPermission(['90'])){
      //   this.$message.warning('您无查看权限，请联系商业主账号进行授权');
      //   return;
      // }
	  let filter_item = '';
	  let url = '';
	  let data = '';
	  switch(key) {
		case 'refund': {
			filter_item = 'pending_refund_form';
			url = '/afterSaleList';
			data = { auditStateListJson: '[0]', auditState: '0' };
			break;
		}
		case 'afterSale': {
			filter_item = 'pending_afterSale_form';
			url = '/afterSaleManager';
			data = { homeEnter: 1 }
			break;
		}
	  }
	  if (url) {
		actionTracking('order_management_top_quick_search', { filter_item: filter_item });
        window.openTab(url, data);
	  }
    },
    // 查看48小时未发货订单
    searchUnshippedOrders() {
      actionTracking('order_management_top_quick_search', { filter_item: 'no_delivery_within_48_hours' });
      this.util.clearLoacl('orderListLocal');
      if (this.unshippedOrdersCount > 0 && this.isShowUnshippedOrders) {
        this.ruleForm.statusList = JSON.stringify([1, 7, 32, 33]);
        this.ruleForm.orderSyncStatus = '';
        this.ruleForm.evidenceToExamine = '';
        this.ruleForm.evidenceHaveImage = '';
        this.isShowErpOrders = true;
        this.isEvidenceHaveImage = true;
        this.isShowover48hCollectingOrder = true;
        this.isShowover48hCollectingOrderAfterDelivery = true;
        this.ruleForm.qualificationChanged = null;
        this.isEvidenceToExamine = true;
        this.isCountCustomerQualification = true;
        this.ruleForm.logisticsTrackFail = false;
        this.ruleForm.logisticsTrackIllegal = false;
        this.ruleForm.partialShipment = null;
        this.ruleForm.invoiceState = null;
        this.ruleForm.over48hCollectingOrder = null;
        this.ruleForm.over48hCollectingOrderAfterDelivery = null;
      } else {
        this.ruleForm.statusList = '';
      }
      this.$set(this.ruleForm, 'status', this.isShowUnshippedOrders ? '1' : '10');
      this.activeOrderStatus = this.isShowUnshippedOrders ? '1' : '10';
      this.isShowUnshippedOrders = !this.isShowUnshippedOrders;

      this.refreshList(1);
    },
    // 查看4超48h未揽收订单
    searchover48hCollectingOrder() {
      // actionTracking('order_management_top_quick_search', { filter_item: 'no_delivery_within_48_hours' });
      this.util.clearLoacl('orderListLocal');
      if (this.over48hCollectingOrder > 0 && this.isShowover48hCollectingOrder) {
        this.ruleForm.statusList = JSON.stringify([1, 7, 2]);
        this.ruleForm.orderSyncStatus = '';
        this.ruleForm.evidenceToExamine = '';
        this.ruleForm.evidenceHaveImage = '';
        this.isShowErpOrders = true;
        this.isEvidenceHaveImage = true;
        this.isShowUnshippedOrders = true;
        this.isShowover48hCollectingOrderAfterDelivery = true;
        this.ruleForm.qualificationChanged = null;
        this.isEvidenceToExamine = true;
        this.isCountCustomerQualification = true;
        this.ruleForm.logisticsTrackFail = false;
        this.ruleForm.logisticsTrackIllegal = false;
        this.ruleForm.partialShipment = null;
        this.ruleForm.invoiceState = null;
        this.ruleForm.over48hCollectingOrder = 1;
        this.ruleForm.over48hCollectingOrderAfterDelivery = null;
      } else {
        this.ruleForm.statusList = '';
        this.ruleForm.over48hCollectingOrder = null;
        this.ruleForm.over48hCollectingOrderAfterDelivery = null;
      }
      // this.$set(this.ruleForm, 'status', this.over48hCollectingOrderAfterDelivery ? '1' : '10');
      // this.activeOrderStatus = this.over48hCollectingOrderAfterDelivery ? '1' : '10';
      this.isShowover48hCollectingOrder = !this.isShowover48hCollectingOrder;

      this.refreshList(1);
    },
    // 查看发货后超48h未揽收订单
    searchover48hCollectingOrderAfterDelivery() {
      // actionTracking('order_management_top_quick_search', { filter_item: 'no_delivery_within_48_hours' });
      this.util.clearLoacl('orderListLocal');
      if (this.over48hCollectingOrderAfterDelivery > 0 && this.isShowover48hCollectingOrderAfterDelivery) {
        this.ruleForm.statusList = JSON.stringify([2]);
        this.ruleForm.orderSyncStatus = '';
        this.ruleForm.evidenceToExamine = '';
        this.ruleForm.evidenceHaveImage = '';
        this.isShowErpOrders = true;
        this.isEvidenceHaveImage = true;
        this.isShowUnshippedOrders = true;
        this.isShowover48hCollectingOrder = true;
        this.ruleForm.qualificationChanged = null;
        this.isEvidenceToExamine = true;
        this.isCountCustomerQualification = true;
        this.ruleForm.logisticsTrackFail = false;
        this.ruleForm.logisticsTrackIllegal = false;
        this.ruleForm.partialShipment = null;
        this.ruleForm.invoiceState = null;
        this.ruleForm.over48hCollectingOrder = null;
        this.ruleForm.over48hCollectingOrderAfterDelivery = 1;
      } else {
        this.ruleForm.statusList = '';
        this.ruleForm.over48hCollectingOrder = null;
        this.ruleForm.over48hCollectingOrderAfterDelivery = null;
      }
      // this.$set(this.ruleForm, 'status', this.isShowUnshippedOrders ? '1' : '10');
      // this.activeOrderStatus = this.isShowUnshippedOrders ? '1' : '10';
      this.isShowover48hCollectingOrderAfterDelivery = !this.isShowover48hCollectingOrderAfterDelivery;

      this.refreshList(1);
    },
    // 查看下推erp失败订单
    searchErpOrder() {
      actionTracking('order_management_top_quick_search', { filter_item: 'failed_push_ERP' });
      this.util.clearLoacl('orderListLocal');
      this.ruleForm.invoiceState = null;
      if (this.pullFailedOrderCount > 0 && this.isShowErpOrders) {
        this.ruleForm.orderSyncStatus = 2;
        this.ruleForm.statusList = '';
        this.ruleForm.evidenceToExamine = '';
        this.ruleForm.evidenceHaveImage = '';
        this.isShowUnshippedOrders = true;
        this.isShowover48hCollectingOrder = true;
        this.isShowover48hCollectingOrderAfterDelivery = true;
        this.isEvidenceHaveImage = true;
        this.isEvidenceToExamine = true;
        this.isCountCustomerQualification = true;
        this.ruleForm.logisticsTrackFail = false;
        this.ruleForm.logisticsTrackIllegal = false;
        this.ruleForm.partialShipment = null;
        this.ruleForm.over48hCollectingOrder = null;
        this.ruleForm.over48hCollectingOrderAfterDelivery = null;
      } else {
        this.ruleForm.orderSyncStatus = '';
      }
      this.$set(this.ruleForm, 'status', this.isShowErpOrders ? '1' : '10');
      this.activeOrderStatus = this.isShowErpOrders ? '1' : '10';
      this.isShowErpOrders = !this.isShowErpOrders;
      this.refreshList();
    },
    // 查询客户资质有变更的订单数量
    searchCustomerQualification() {
      actionTracking('order_management_top_quick_search', { filter_item: 'customer_information_changed' });
      this.util.clearLoacl('orderListLocal');
      if (this.countCustomerQualification > 0 && this.isCountCustomerQualification) {
        this.ruleForm.qualificationChanged = 1;
        this.ruleForm.statusList = '';
        this.ruleForm.orderSyncStatus = '';
        this.ruleForm.evidenceToExamine = '';
        this.ruleForm.evidenceHaveImage = '';
        this.isShowUnshippedOrders = true;
        this.isShowUnshippedOrders = true;
        this.isShowover48hCollectingOrder = true;
        this.isShowover48hCollectingOrderAfterDelivery = true;
        this.isShowErpOrders = true;
        this.isEvidenceHaveImage = true;
        this.isEvidenceToExamine = true;
        this.ruleForm.logisticsTrackFail = false;
        this.ruleForm.logisticsTrackIllegal = false;
        this.ruleForm.partialShipment = null;
        this.ruleForm.invoiceState = null;
        this.ruleForm.over48hCollectingOrder = null;
        this.ruleForm.over48hCollectingOrderAfterDelivery = null;
      } else {
        this.ruleForm.qualificationChanged = null;
      }
      this.$set(this.ruleForm, 'status', this.isCountCustomerQualification ? '1' : '10');
      this.activeOrderStatus = this.isCountCustomerQualification ? '1' : '10';
      this.isCountCustomerQualification = !this.isCountCustomerQualification;
      this.refreshList();
    },
    searchEvidenceToExamine(f) {
      actionTracking('order_management_top_quick_search', { pending_refund_form: 'reviewed_telegraphic_transfer' });
      this.util.clearLoacl('orderListLocal');
      this.ruleForm.orderSyncStatus = '';
      this.ruleForm.statusList = '';
      this.ruleForm.invoiceState = null;
      if (this.evidenceToExamine > 0 && this.isEvidenceToExamine && f === 1) {
        this.isEvidenceToExamine = !this.isEvidenceToExamine;
        this.ruleForm.evidenceToExamine = true;
        this.ruleForm.isEvidenceHaveImage = '';
        this.isEvidenceHaveImage = true;
        this.isShowErpOrders = true;
        this.isShowUnshippedOrders = true;
        this.isShowover48hCollectingOrder = true;
        this.isShowover48hCollectingOrderAfterDelivery = true;
        this.isCountCustomerQualification = true;
        this.ruleForm.logisticsTrackFail = false;
        this.ruleForm.logisticsTrackIllegal = false;
        this.ruleForm.partialShipment = null;
        this.ruleForm.over48hCollectingOrder = null;
        this.ruleForm.over48hCollectingOrderAfterDelivery = null;
      } else if (f === 1 && !this.isEvidenceToExamine) {
        this.ruleForm.evidenceToExamine = '';
        this.isEvidenceToExamine = !this.isEvidenceToExamine;
      }
      if (this.evidenceHaveImage > 0 && this.isEvidenceHaveImage && f === 2) {
        this.isEvidenceHaveImage = !this.isEvidenceHaveImage;
        this.ruleForm.evidenceHaveImage = true;
        this.ruleForm.evidenceToExamine = '';
        this.isEvidenceToExamine = true;
        this.isShowErpOrders = true;
        this.isShowUnshippedOrders = true;
        this.isShowover48hCollectingOrder = true;
        this.isShowover48hCollectingOrderAfterDelivery = true;
        this.isCountCustomerQualification = true;
        this.ruleForm.logisticsTrackFail = false;
        this.ruleForm.logisticsTrackIllegal = false;
        this.ruleForm.partialShipment = null;
        this.ruleForm.over48hCollectingOrder = null;
        this.ruleForm.over48hCollectingOrderAfterDelivery = null;
      } else if (f === 2 && !this.isEvidenceHaveImage) {
        this.ruleForm.evidenceHaveImage = '';
        this.isEvidenceHaveImage = !this.isEvidenceHaveImage;
      }
      this.$set(this.ruleForm, 'status', this.isShowErpOrders ? '10' : '10');
      this.activeOrderStatus = this.isShowErpOrders ? '10' : '10';
      this.refreshList();
    },
  },
};
</script>
<style lang="scss">
.show_right{
  position: absolute;
  right: 0;
  top: 0;
}
.flex{
  display: flex;
}
.partialShipmentDialog {
  .el-radio {
    display: block;
    margin-bottom: 10px;
  }
}
/*日期时间组件不显示 清空按钮*/
.noClear .el-picker-panel__footer .el-button.el-picker-panel__link-btn.el-button--text.el-button--mini {
	display: none ;
}
.date-style .el-picker-panel__body-wrapper {
  .el-picker-panel__body {
    margin: 0;
  }
  .el-picker-panel__sidebar {
    position: absolute;
    padding: 0;
    top: auto;
    bottom: 0.6vh;
    left: 1%;
    z-index: 100;
    display: flex;
    width: 250px;
    border: none;
    align-items: center;
    .el-picker-panel__shortcut {
      border: 1px solid #DCDFE6;
      margin: 0 3px;
      border-radius: 5px;
      font-size: 12px;
      height: 27px;
      line-height: 27px;
      overflow: hidden;
      text-align: center;
    }
    .el-picker-panel__shortcut:hover {
      border-color: #1d69c4;
    }
  }
}
</style>
<style lang="scss" scoped>
::v-deep  .pagination-container {
  margin: 15px 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
::v-deep  .el-form-item__label {
  padding: 0;
}
.orderWrap {
  padding: 20px 24px 5px;
  .orderTitle {
    font-size: 20px;
    font-weight: 500;
  }
  .showMore {
    color: #4184d5;
    font-size: 14px;
    float: right;
    padding: 5px 20px;
    cursor: pointer;
  }
  .btnBox {
    float: right;
  }
}
.countBox {
  background: #ff4d4f;
  border-radius: 11px;
  color: #fff;
  font-size: 12px;
  margin-left: 2px;
  padding: 0 8px;
}
.refundCountBox {
  margin-bottom: 15px;
  .refundCount {
    color: #ff2121;
  }
  .seeCount {
    color: #4183d5;
    cursor: pointer;
  }
}
.div-info:hover {
  background: #dce6f1 !important;
  // border: 1px solid #4183d5;
  // color: #4183d5;
  // background-color: #fff;
  // .status-span i {
    
  // }
}
.activediv-info {
  background: #dce6f1 !important;
  border: 1px solid #4183d5 !important;
  color: #4183d5 !important;
  // background-color: #fff !important;
  .status-span i {
    background: #4183d5 !important;
  }
}
.div-info {
  display: inline-block;
  padding: 5px 10px 10px 5px;
  border-radius: 2px;
  font-size: 14px;
  font-family: PingFangSC, PingFangSC-Regular;
  font-weight: 400;
  border: 1px solid #dcdfe6;
  margin: 0 10px 10px 0;
  p {
    margin: 0;
    padding-top: 5px;
    font-size: 14px;
    font-family: PingFangSC, PingFangSC-Medium;
    font-weight: 500;
    color: #333333;
  }
  .status-span {
    padding-left: 14px;
    position: relative;
    i {
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
      left: 4px;
      display: inline-block;
      vertical-align: middle;
      width: 5px;
      height: 5px;
      border-radius: 50%;
    }
  }
  .refundCountBox {
    .refundCount {
      margin-left: 12px;
      font-size: 20px;
      font-weight: 500;
      color: #ff3945;
      font-family: PingFangSC, PingFangSC-Medium;
    }
    .seeCount {
      float: right;
      color: #ffffff;
    }
  }
}
.Fsearch {
  padding: 0 0 10px;
  font-weight: 500;
  .searchMsg {
    font-weight: 700;
    width: 200px;
  }
  .sign {
    display: inline-table;
    width: 3px;
    height: 13px;
    background: linear-gradient(360deg, #1d69c4 1%, #8bbdfc 99%);
    border-radius: 2px;
    margin-right: 8px;
  }
}
.searchMy ::v-deep  .el-form-item__label {
  margin-left: 20px;
  padding: 0;
}
.searchMy ::v-deep  .el-input__inner {
  border-radius: 0 4px 4px 0;
}
.searchMy ::v-deep  .el-date-editor {

}
::v-deep   .el-range-editor--small.el-input__inner{
    width: 355px;
    .el-range-input{
      // font-size: 5px;
    }
}
.search-title {
  display: table-cell;
  padding: 0 10px;
  text-align: center;
  border: 1px solid #dcdfe6;
  height: 30px;
  line-height: 30px;
  vertical-align: middle;
  border-right: none;
  border-radius: 4px 0 0 4px;
  color: #333333;
  white-space: nowrap;
}
.searchMy ::v-deep  .el-select {
  display: table-cell;
  width: 100%;
}
.searchMy ::v-deep  .el-form-item__content {
  width: 100%;
}
.searchMy ::v-deep  .el-form-item--small.el-form-item {
  margin-bottom: 10px;
  width: 32%;
}
.searchMy ::v-deep  .el-form-item--small .el-form-item__content {
  line-height: 30px;
  width: 100%;
}
.searchMy ::v-deep  .el-input-group__prepend {
  background: none;
  color: #333333;
  padding: 0 10px;
}
.Fsearch {
  //padding: 0px 20px 10px;
  font-weight: 500;
  .searchMsg {
    font-weight: 700;
    width: 200px;
  }
  .sign {
    display: inline-table;
    width: 3px;
    height: 13px;
    background: linear-gradient(360deg, #1d69c4 1%, #8bbdfc 99%);
    border-radius: 2px;
    margin-right: 8px;
  }
}
.searchMy ::v-deep  .el-form-item--small.el-form-item {
  margin-bottom: 10px;
  width: 97%;
}
</style>
