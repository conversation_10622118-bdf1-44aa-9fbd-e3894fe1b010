<template>
<div class="storeVoucher">
    <div class="sticky-tabs">
      <el-tabs v-model="activeName" @tab-click="handleClick">
        <el-tab-pane label="优惠劵管理" name="couponList"/>
        <el-tab-pane label="活动管理" name="activemanage"/>
        <el-tab-pane label="优惠券数据" name="couponUserList"/>
      </el-tabs>
    </div>
    <transition name="fade" mode="out-in">
        <keep-alive>
            <component 
                :is="currentComponent" 
                @viewCouponData="viewCouponData"
                :couponId="couponId"
                :refresh="refresh">
            </component>
        </keep-alive>
    </transition>
</div>
</template>

<script>
import couponList from "@/views/marketing/marketingList.vue"
import activemanage from "@/views/active-manage/active-manage.vue"
import couponUserList from "@/views/couponlist/coupon-user-list.vue"

export default {
name:'storeVoucher',
components: {
    couponList,
    activemanage,
    couponUserList
},
data() {
  return {
    activeName: "couponList",
    currentComponent: couponList,
    couponId: "",
    refresh: false
  }
 },
activated(){
    if(this.$route.query.to) {
        this.selectComponents(this.$route.query.to)
    }
},
methods: {
    handleClick(tab, event) {
        this.$router.replace({
            path: 'storeVoucher',
            query: { to: tab.name },
        });
        this.selectComponents(tab.name)
    },
    selectComponents(target) {
        if(target) {
            this.activeName = target
        }
        switch (target) {
            case "couponList":
                this.currentComponent = couponList
                break;
            case "activemanage":
                this.currentComponent = activemanage
                break;
            case "couponUserList":
                this.currentComponent = couponUserList
                break;
            default:
                break;
        }
    }, 
    viewCouponData(id) {
        this.currentComponent = couponUserList
        this.activeName = "couponUserList"
        this.couponId = id
    }
},
}
</script>

<style lang='scss' scoped>
.storeVoucher {
    margin-top: 10px;
    padding-left: 10px;
}
.sticky-tabs {
  position: sticky;
  top: 0;
  z-index: 1000;
  background: #fff;
  padding: 10px 0;
}
/* 定义过渡动画 */
.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-to,
.fade-leave-from {
  opacity: 1;
}
</style>