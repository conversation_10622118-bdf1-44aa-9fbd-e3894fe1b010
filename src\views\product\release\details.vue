<template>
  <div class="divBox">
    <div class="bottom-info">
      <div class="topBox">
        <span v-if="formModel.isEdit == 1">商品详情</span>
        <span v-else>商品编辑</span>
        <el-button
          v-if="formModel.isEdit == 1"
          size="small"
          type="primary"
          @click="onCancel"
          >关闭</el-button
        >
        <div v-else>
          <el-button size="small" @click="onCancel">取消</el-button>
          <el-button :disabled="goodsBasicComSbmitVal" size="small" type="primary" @click="onSubmit"
            >提交</el-button
          >
        </div>
      </div>
      <div class="tabBox">
        <div
          v-for="(item, index) in tabList"
          :class="clickIndex === index ? 'active' : ''"
          @click="tabClick(item.idName, index)"
        >
          {{ item.name }}
        </div>
      </div>
    </div>
    <!-- v-if="allData.standardProductId && source===2" -->
    <!-- <div
      class="editError-class"
      v-if="allData.standardProductId && formModel.from === 'productList' && formModel.isEdit != 1"
    >
      信息不对？<el-button type="text" @click="gotoErrorPage()"
        >开始纠错</el-button
      >
    </div> -->

    <div class="conBox">
      <div id="basic" class="contentBox">
        <div class="title">基本信息</div>
        <goodsBasicCom
          :basic-data="allData"
          :form-model="formModel"
          ref="goodsBasicComp"
          @goodsBasicComSbmit="goodsBasicComSbmitHandle"
          @initData="activate"
        ></goodsBasicCom>
      </div>
       <div id="medicine"  v-if="allData.categoryExtList && allData.categoryExtList.length"  class="contentBox">
        <div class="title">中药属性</div>
        <MedicineField
         :basic-data="allData"
         :form-model="formModel"
          ref="medicineFieldComp"
         />
      </div>
      <div id="price" class="contentBox">
        <div class="title">价格库存</div>
        <priceInventory
          :all-store-disabled="allStoreDisable"
          :basic-data="allData"
          :form-model="formModel"
          ref="priceInventoryComp"
        ></priceInventory>
      </div>
      <div id="purchase" class="contentBox">
        <div class="title">起购限购</div>
        <upForPurchase
          :basic-data="allData"
          :form-model="formModel"
          ref="upForPurchaseComp"
        ></upForPurchase>
      </div>
      <div id="supply" class="contentBox">
        <div class="title">供货信息</div>
        <supplyInformation
          :basic-data="allData"
          :form-model="formModel"
          ref="supplyInformationForm"
        ></supplyInformation>
      </div>
      <div id="other" class="contentBox">
        <div class="title">其他信息</div>
        <otherInformation
          :basic-data="allData"
          :form-model="formModel"
          ref="otherInformationComp"
        ></otherInformation>
      </div>
    </div>

    <el-dialog
      title="信息"
      width="400px"
      :visible.sync="visible"
      :modal-append-to-body="false"
      :close-on-click-modal="false"
      @close="onCancel"
    >
      提交成功，可在"商品列表"-
      <span>"{{ statusName }}"</span>查看。
      <el-button
        style="color: #4184d5"
        size="small"
        type="text"
        @click="toProduct"
        >点击查看</el-button
      >

      <span slot="footer">
        <el-button
          size="medium"
          style="margin-left: 20px"
          type="primary"
          @click="onCancel"
          >关闭</el-button
        >
      </span>
    </el-dialog>
    <goodsCheckAlert
      ref="goodsCheckAlert"
      @backRquest="toAddFunction"
      @gotoErrorEdit="gotoErrorEdit"
      @finish="alertfinish"
    >
    </goodsCheckAlert>
    <goodsMoreCheckAlert
      ref="goodsMoreCheckAlert"
      :finishto="'/productList'"
      @selfSend="selfSend()"
      @finish="alertfinish()"
    ></goodsMoreCheckAlert>
  </div>
</template>

<script>
import {
  apiDetail,
  apiEditSku,
  apiProductInit,
  apiAddSku,
  loadStatusCounts,
  addProductEditProp,
  getProductEditProp,
  matchMeProduceList,
  matchSwitch,
  getSkuByProductId,
  compareFields
} from '@/api/product'
import { erpFirstCategoryId } from '../config'
import goodsBasicCom from '@/views/product/components/goodsBasicCom'
import MedicineField from '@/views/product/components/medicineFiled'
import priceInventory from '@/views/product/components/priceInventory'
import upForPurchase from '@/views/product/components/upForPurchase'
import supplyInformation from '@/views/product/components/supplyInformation'
import otherInformation from '@/views/product/components/otherInformation'
import goodsCheckAlert from '@/views/product/components/goodsCheckAlert'
import goodsMoreCheckAlert from '@/views/product/components/goodsMoreCheckAlert'
import { mapState } from 'vuex'
export default {
  name: 'Details',
  components: {
    goodsBasicCom,
    MedicineField,
    priceInventory,
    upForPurchase,
    supplyInformation,
    otherInformation,
    goodsCheckAlert,
    goodsMoreCheckAlert
  },
  computed: { ...mapState('app', ['shopConfig']) },
  data() {
    return {
      allStoreDisable: false,
      goodsBasicComSbmitVal: false,
      verifyArray: [],
      clickIndex: 0,
      formModel: {},
      allData: {},
      visible: false,
      statusName: '',
      statusType: '',
      barcode: '',
      source: undefined,
      productStatusOptions: [],
      tabList: [
        { name: '基础信息', idName: 'basic' },
        { name: '价格库存', idName: 'price' },
        { name: '起购限购', idName: 'purchase' },
        { name: '供货信息', idName: 'supply' },
        { name: '其它信息', idName: 'other' }
      ],
      formRefs: [
        'goodsBasicComVo',
        'priceInventoryVo',
        'upForPurchaseVo',
        'supplyInformationVo',
        'otherInformationVo'
      ],
      components: [
        {
          ref: 'goodsBasicComp',
          sessionStorageKey: 'goodsBasicComVoRules'
        },
        {
          ref: 'priceInventoryComp',
          sessionStorageKey: 'priceInventoryVoRules'
        },
        {
          ref: 'upForPurchaseComp',
          sessionStorageKey: 'upForPurchaseVoRules'
        },
        {
          ref: 'supplyInformationForm',
          sessionStorageKey: 'supplyInformationVoRules'
        }
      ],
      temp: {
        barcode: 'xxx',
        firstCategory: 'xxx',
        standardProductId: 'xxx',
        erpSkuId: 'xxx'
      }
    }
  },
  created() {
    this.loadStatusCounts()
    this.source = this.$route.query.source
    window.clearData['/product/detailsEdit'] = () => {
      // 在页面关闭时清除sessionStorage createProduct
      sessionStorage.removeItem('createProduct')
    }
  },
  activated() {
    console.log(this.temp);
    console.log(this.$route.query);
    if(this.$route.query.from=='create' || this.$route.query.from=='initialize'){
      // 如果是自建商品，选择商品大类或者已有标识，点击现在发布商品，不再缓存直接重新加载
      // 如果是单纯从其它页面回来而不是跳转过来，照常缓存信息
      let data = sessionStorage.getItem('createProduct')
        ? JSON.parse(sessionStorage.getItem('createProduct'))
        : null
      if (data) {
        this.activate()
        this.handleBeforeUnload()
        sessionStorage.removeItem('createProduct')
      }
    }
    else if (this.temp.barcode != this.$route.query.barcode || this.temp.firstCategory != this.$route.query.erpFirstCategoryId || this.temp.standardProductId != this.$route.query.standardProductId || this.temp.erpSkuId != this.$route.query.erpSkuId) {
      this.activate()
      this.handleBeforeUnload()
    } else if(this.$route.query.from == "productList") {
      this.activate()
      this.handleBeforeUnload()
    }
    this.temp.barcode = this.$route.query.barcode;
    this.temp.firstCategory = this.$route.query.erpFirstCategoryId;
    this.temp.standardProductId = this.$route.query.standardProductId;
    this.temp.erpSkuId = this.$route.query.erpSkuId;
  },
  deactivated() {
    // // 关闭页面 组件恢复默认rules
    /* this.components.forEach((component) => {
      let { ref, sessionStorageKey } = component
      let rules = window.sessionStorage.getItem(sessionStorageKey)
      this.$refs[ref].$data[sessionStorageKey] = JSON.parse(rules)
    }) */
  },
  methods: {

    goodsBasicComSbmitHandle(e) {
      this.goodsBasicComSbmitVal = e
    },
    gotoErrorPage() {
      if (this.allData.source === 1) {
        this.apicompareFields(this.allData, 'editOrDetail')
      } else {
        const path = '/product/errorEdit'
        const obj = {
          productId: this.allData.standardProductId,
          from: 'detailsEdit',
          verify: true,
          source: this.allData.source,
          firstCategory: this.allData.erpFirstCategoryId,
          barCode: this.allData.barcode
        }
        window.openTab(path, obj)
      }
    },
    apigetSkuByProductId(type, keys) {
      getSkuByProductId({ productId: this.allData.standardProductId }).then(
        (res) => {
          if (res.code === 0) {
            this.allData.firstCategory = this.allData.erpFirstCategoryId
            this.$refs.goodsCheckAlert.open(this.allData, res.data, keys, type)
          } else {
            this.$message.error(res.message)
          }
        }
      )
    },

    //自建商品
    selfSend(data) {
      this.addSku(data)
    },

    alertfinish() {
      this.onCancel()
    },

    //
    gotoErrorEdit(startData) {
      const path = '/product/errorEdit'
      const obj = {
        productId: startData.standardProductId,
        from: 'detailsEdit',
        verify: true,
        source: this.allData.source,
        firstCategory: startData.firstCategory,
        barCode: this.allData.barcode
      }
      window.openTab(path, obj)
    },

    handleBeforeUnload() {
      let goodsBasicForm = this.$refs.goodsBasicComp.$refs.goodsBasicComVo
      let priceInventoryForm =
        this.$refs.priceInventoryComp.$refs.priceInventoryVo
      let upForPurchaseForm = this.$refs.upForPurchaseComp.$refs.upForPurchaseVo
      let supplyInformationForm =
        this.$refs.supplyInformationForm.$refs.supplyInformationVo
      let otherInformationForm =
        this.$refs.otherInformationComp.$refs.otherInformationVo
      let forms = [
        goodsBasicForm,
        priceInventoryForm,
        upForPurchaseForm,
        supplyInformationForm,
        otherInformationForm
      ]
      for (let i = 0; i < forms.length; i++) {
        forms[i].clearValidate()
      }
    },
    activate(e) {
      const { query } = this.$route
      if (query && Object.keys(query).length > 0) {
        this.formModel = {}
        this.allData = {}
        this.visible = false
        this.statusName = ''
        this.statusType = ''
        this.formModel = Object.assign({}, this.formModel, query)
        if (Object.prototype.hasOwnProperty.call(this.formModel, 'from')) {
          if (this.formModel.from === 'productList') {
            // 列表
            this.getDetail()
          } else if (
            this.formModel.from === 'supplement' ||
            this.formModel.from === 'create' ||
            this.formModel.from === 'initialize'
          ) {
            // 批量发布-补充，重新创建
            this.getProductInit(e)
          }
        }
      }
    },
    handleRepeated(res, isEdit) {
      if (res.code == 0) {
        const { common, selling_price, base_price } = res.data
        // 售价模式
        if (this.shopConfig.priceType == 1) {
          this.verifyArray = [...common.split(','), ...selling_price.split(',')]
        } else {
          // 底价模式
          this.verifyArray = [...common.split(','), ...base_price.split(',')]
        }
        let goodsBasicForm =
          this.$refs.goodsBasicComp.$data.goodsBasicComVoRules
        let priceInventoryForm =
          this.$refs.priceInventoryComp.$data.priceInventoryVoRules
        let arr = [goodsBasicForm, priceInventoryForm]
        arr.forEach((item) => {
          for (let [key, value] of Object.entries(item)) {
            if (this.verifyArray.includes(key)) {
              if (value.length > 0) {
                for (let i = 0; i < value.length; i++) {
                  value[0].required = true
                }
              }
            }
          }
        })

        if (!this.verifyArray.includes('imagesList')) {
          delete goodsBasicForm.imagesList
        }

        // 编辑 && 售价模式
        if (this.shopConfig.priceType == 1 && isEdit) {
          // 单体采购价 必填
          for (let [key, value] of Object.entries(priceInventoryForm)) {
            if (['fob'].includes(key)) {
              if (value.length > 0) {
                for (let i = 0; i < value.length; i++) {
                  value[0].required = true
                }
              }
            }
          }
        }
        // 编辑 && 底价
        if (this.shopConfig.priceType == 2 && isEdit) {
          // 单体采购价、单体毛利率、底价 必填
          for (let [key, value] of Object.entries(priceInventoryForm)) {
            if (['fob', 'grossProfitMargin', 'basePrice'].includes(key)) {
              if (value.length > 0) {
                for (let i = 0; i < value.length; i++) {
                  value[0].required = true
                }
              }
            }
          }
        }
        // 编辑
        if (isEdit) {
          // 单体采购价必填
          for (let [key, value] of Object.entries(priceInventoryForm)) {
            // // 总库存必填
            if (['totalStock'].includes(key)) {
              if (value.length > 0) {
                for (let i = 0; i < value.length; i++) {
                  value[0].required = true
                }
              }
            }
          }
        }
        if (isEdit) {
          for (let [key, value] of Object.entries(priceInventoryForm)) {
            // 近效期 最老生产日期 必填项
            if (
              [
                '100002',
                '100009',
                '100005',
                '100008',
                '100003',
                '262104'
              ].includes(this.allData.erpFirstCategoryId)
            ) {
              if (['oldestProDate', 'nearEffect'].includes(key)) {
                if (value.length > 0) {
                  for (let i = 0; i < value.length; i++) {
                    value[0].required = true
                  }
                }
              }
            } else {
              if (['oldestProDate', 'nearEffect'].includes(key)) {
                if (value.length > 0) {
                  for (let i = 0; i < value.length; i++) {
                    value[0].required = false
                  }
                }
              }
            }
          }
        }
      } else {
        console.log(new Error(res.message))
      }
    },
    async verify() {
      if (this.formModel.prop) {
        // 添加
        let res = await addProductEditProp({
          businessFirstCategory: this.formModel.erpFirstCategoryId
        })
        this.handleRepeated(res, false)
      }
      // 编辑
      if (this.formModel.verify) {
        let res = await getProductEditProp({
          businessFirstCategory:
            this.formModel.erpFirstCategoryId || this.allData.erpFirstCategoryId
        })
        this.handleRepeated(res, true)
      }
    },
    loadingFun(text) {
      const loading = this.$loading({
        lock: true,
        text: text ? text : 'Loading',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      return loading
    },
    async loadStatusCounts() {
      try {
        const params = {}
        const res = await loadStatusCounts(params)
        if (res) {
          this.productStatusOptions = [
            {
              statusName: '全部',
              statusType: -99
            },
            ...res
          ]
        }
      } catch (e) {
        console.log(e)
      }
    },
    statusNameStr(value) {
      let name = ''
      this.productStatusOptions.forEach((item) => {
        if (item && Number(item.statusType) === Number(value)) {
          name = item.statusName
        }
      })
      return name || ''
    },
    async getDetail() {
      const params = { barcode: this.formModel.barcode }
      const load = this.loadingFun()
      await apiDetail(params).then((res) => {

        // console.log('isFBP',this.shopConfig.isFbp);
        if(this.shopConfig.isFbp === 1){
          this.allStoreDisable = true
        }
        load.close()
        if (res.code === 0) {
          if (res.data) {
            console.log('--allData--', res.data)
            //设置一级分类
            this.allData = { ...res.data }
            if(this.allData.categoryExtList && this.allData.categoryExtList.length > 0){
                 const medicine = { name: '中药属性', idName: 'medicine' };
                const exists = this.tabList.some(item => item.idName === medicine.idName);
                if (!exists) {
                    this.tabList.splice(1, 0, medicine);
                }
            }
            const key = res.data.spuCategoryCode === 4 ? `${res.data.spuCategoryCode}${res.data.nonDrugCategoryCode}` : `${res.data.spuCategoryCode}`
            this.allData.erpFirstCategoryId = erpFirstCategoryId[key]
            this.verify()
          } else {
            this.$message.error('获取商品信息失败')
          }
        } else {
          this.$message.error(res.message)
        }
      })
    },
    getProductInit(e) {
      console.log('e:', e)
      // 列表选中跳转
      const params = {
        erpFirstCategoryId: this.formModel.erpFirstCategoryId,
        /* erpSecondCategoryId: this.formModel.erpSecondCategoryId,
        erpThirdCategoryId: this.formModel.erpThirdCategoryId,
        erpFourthCategoryId: this.formModel.erpFourthCategoryId, */
        spuCategoryCode: this.formModel.spuCategoryCode,
        nonDrugCategoryCode: this.formModel.nonDrugCategoryCode,
        standardProductId: this.formModel.standardProductId,
        erpSkuId: this.formModel.erpSkuId,
        type: this.formModel.from === 'create' ? 1 : null
      }
      // if(this.formModel.from !== "create"){
      //   params.erpSkuId = this.formModel.erpSkuId;
      // }
      const load = this.loadingFun()
      apiProductInit(params).then((res) => {
        load.close()
        if (res.code === 0) {
          if (res.data) {
            this.allData = { pipei: this.goodsBasicComSbmitVal, isInit: this.$route.query.isInit, ...res.data, ...e}
            // this.allData = {...res.data, ...e}
            console.log('this.allData:', this.allData)
            if(this.allData.categoryExtList && this.allData.categoryExtList.length > 0){
                 const medicine = { name: '中药属性', idName: 'medicine' };
                const exists = this.tabList.some(item => item.idName === medicine.idName);
                if (!exists) {
                    this.tabList.splice(1, 0, medicine);
                }
            }
            const key = res.data.spuCategoryCode === 4 ? `${res.data.spuCategoryCode}${res.data.nonDrugCategoryCode}` : `${res.data.spuCategoryCode}`
            this.allData.erpFirstCategoryId = erpFirstCategoryId[key]
            if (this.formModel.from == 'create') {
              this.allData.imageUrlStandard = '';
              this.allData.useStandardImage = false;
            }
            this.verify()
          } else {
            this.$message.error('获取商品信息失败')
          }
        } else {
          this.$message.error(res.message)
        }
      })
    },
    toAddFunction(params, callback) {
      this.addSku(params)
      callback()
    },
    onSubmit() {
      const that = this
      // 获取到组件中的form
      const goodsBasicForm = this.$refs.goodsBasicComp.$refs.goodsBasicComVo
      if(goodsBasicForm.model.regional) {
        goodsBasicForm.model.producer = `${goodsBasicForm.model.regional}${goodsBasicForm.model.producerCopy}`
      }
      const priceInventoryForm =
        this.$refs.priceInventoryComp.$refs.priceInventoryVo
      const upForPurchaseForm =
        this.$refs.upForPurchaseComp.$refs.upForPurchaseVo
      const supplyInformationForm =
        this.$refs.supplyInformationForm.$refs.supplyInformationVo
      const otherInformationForm =
        this.$refs.otherInformationComp.$refs.otherInformationVo

      if (
        this.checkProductName(
          goodsBasicForm.model.productName,
          '请输入正确的商品名称'
        ) === false
      ) {
        return false
      }
      if (
        this.checkProductName(
          goodsBasicForm.model.commonName,
          '请输入正确的通用名称'
        ) === false
      ) {
        return false
      }
      if (
        this.checkProductName(
          goodsBasicForm.model.showName,
          '请输入正确的展示名称'
        ) === false
      ) {
        return false
      }
      if (
        this.checkProductName(
          goodsBasicForm.model.manufacturer,
          '请输入正确的生产厂家'
        ) === false
      ) {
        return false
      }
      if (
        this.checkProductName(goodsBasicForm.model.spec, '请输入正确的规格') ===
        false
      ) {
        return false
      }


      const errStr = this.$refs.priceInventoryComp.checkData()
      console.log('errStr', errStr)
      if (errStr) {
        this.$message.error(errStr)
        return false
      }

      // 使用Promise.all去校验结果
      Promise.all(
        [
          goodsBasicForm,
          priceInventoryForm,
          upForPurchaseForm,
          supplyInformationForm,
          otherInformationForm
        ].map(this.getFormPromise)
      ).then((res) => {
        const validateResult = res.every((item) => !!item)
        if (validateResult) {
          if ((this.formModel.spuCategoryCode == 3 || this.allData.spuCategoryCode == 3) && goodsBasicForm.model.filingsAuthor) {
            if (/^[0-9]*$/.test(goodsBasicForm.model.filingsAuthor) || /^[`~!@#$%^&*()_\-+=<>?:"{}|,.\/;'\\[\]·~！@#￥%……&*（）——\-+={}|《》？：“”【】、；‘'，。、 ]*$/.test(goodsBasicForm.model.filingsAuthor)) {
              this.$message.error('医疗器械注册人/备案人名称格式错误')
              return false
            }
          }
          console.log('都校验通过')
          const params = this.getParams(
            goodsBasicForm.model,
            priceInventoryForm.model,
            upForPurchaseForm.model,
            supplyInformationForm.model,
            otherInformationForm.model
          )
          if (params.isInstrument) {
            if (
              params.instrumentLicenseImageList.length &&
              params.instrumentLicenseEffect === ''
            ) {
              this.$message.warning(
                '医疗器械注册证及其有效期至需同时填写或同时不填写，请修改后提交'
              )
              return false
            }
            if (
              !params.instrumentLicenseImageList.length &&
              params.instrumentLicenseEffect
            ) {
              this.$message.warning(
                '医疗器械注册证及其有效期至需同时填写或同时不填写，请修改后提交'
              )
              return false
            }

            // 如果 医疗器械注册证或备案凭证编号 含"国械备|进|许" 则不校验 生产许可证号或备案凭证编号 相关
            if (new RegExp('国械备|进|许').test(params.instrumentNumber)) {
              console.log(
                '医疗器械注册证或备案凭证编号 含"国械备|进|许" 则不校验 生产许可证号或备案凭证编号 相关'
              )
            } else {
              if (
                params.manufacturingLicenseImageList.length &&
                params.manufacturingLicenseEffect === ''
              ) {
                this.$message.warning(
                  '生产许可证书及其有效期至需同时填写或同时不填写，请补充后提交'
                )
                return false
              }
              if (
                !params.manufacturingLicenseImageList.length &&
                params.manufacturingLicenseEffect
              ) {
                this.$message.warning(
                  '生产许可证书及其有效期至需同时填写或同时不填写，请补充后提交'
                )
                return false
              }
            }

            if (params.newProDate && params.instrumentLicenseEffect) {
              if (!params.newProDate < params.instrumentLicenseEffect) {
                this.$message.warning(
                  '最新生产日期需小于医疗器械注册证的有效期至'
                )
                return false
              }
            }
            if (params.newProDate && params.manufacturingLicenseEffect) {
              if (!params.newProDate < params.manufacturingLicenseEffect) {
                this.$message.warning('最新生产日期需小于生产许可证的有效期至')
                return false
              }
            }
            // this.goodsBasicComVo.instrumentImageValid = this.propsData.instrumentImageValid || true;
            // this.goodsBasicComVo.manufacturingImageValid = this.propsData.manufacturingImageValid || true;
            if (
              params.instrumentImageValid &&
              !params.instrumentLicenseImageList.length
            ) {
              this.$message.warning('医疗器械注册证及有效期至不能为空')
              return false
            }
            if (
              params.manufacturingImageValid &&
              !params.manufacturingLicenseImageList.length
            ) {
              this.$message.warning('生产许可证书及有效期至不能为空')
              return false
            }
          }
          /* params.spuCategoryCode = this.allData.spuCategoryCode;
          params.nonDrugCategoryCode = this.allData.nonDrugCategoryCode; */
          if (that.formModel.from === 'productList') {
            if(this.allData.categoryExtList && this.allData.categoryExtList.length > 0 && this.$refs.medicineFieldComp) {
               params.categoryExtList = this.$refs.medicineFieldComp.getSubmitCategoryExtList(); //中药属性
            }
            this.editSku(params)
            sessionStorage.removeItem('pipeiProduct')
          } else {
            params.erpSkuId = that.formModel.erpSkuId
            params.imageUrlStandard = this.allData.imageUrlStandard
            params.instrutionImageUrlStandard =
              this.allData.instrutionImageUrlStandard
            if (this.formModel.from === 'initialize' && params.source === 2) {
               if(this.allData.categoryExtList && this.allData.categoryExtList.length > 0 && this.$refs.medicineFieldComp) {
                 params.categoryExtList = this.$refs.medicineFieldComp.getSubmitCategoryExtList(); //中药属性
              }
              this.addSku(params)
              sessionStorage.removeItem('pipeiProduct')
            } else {
              this.apicompareFields(params, 'new')
              sessionStorage.removeItem('pipeiProduct')
            }
          }
        } else {
          console.log('未校验通过')
          this.$message.error('部分必填项未填写或填写不规范')
        }
      })
    },

    setParamsDrugClassification(selfdata, resdara) {
      const arr = [
        { code: 0, name: '无' },
        { code: 1, name: '甲类OTC' },
        { code: 2, name: '乙类OTC' },
        { code: 3, name: '处方药Rx' }
      ]
      for (let index = 0; index < arr.length; index++) {
        const element = arr[index]
        if (element.code === selfdata.drugClassification) {
          selfdata['drugClassificationName'] = element.name
        }
        if (element.code === resdara.drugClassificationCode) {
          resdara['drugClassificationName'] = element.name
        }
      }
    },

    apicompareFields(params, type) {
      compareFields().then((res) => {
        if (res.code !== 0) {
          this.$message.error(res.message)
          return
        }
        const keys = this.getFirstCategoryData(
          res.data,
          params.erpFirstCategoryId
        )

        if (type === 'editOrDetail') {
          this.apigetSkuByProductId(type, keys)
        } else {
          this.apiMatchSwitch(params, type, keys)
        }
      })
    },

    getFirstCategoryData(data, categoryid) {
      if (
        categoryid.toString() === '100001' ||
        categoryid.toString() === '100007' ||
        categoryid.toString() === '100008'
      ) {
        return data['nomalList']
      } else if (
        categoryid.toString() === '100004' ||
        categoryid.toString() === '100010' ||
        categoryid.toString() === '262683'
      ) {
        return data['chiMedList']
      } else if (
        categoryid.toString() === '100002' ||
        categoryid.toString() === '100009'
      ) {
        return data['drugList']
      } else if (
        categoryid.toString() === '100003' ||
        categoryid.toString() === '262104'
      ) {
        return data['healthList']
      } else if (categoryid.toString() === '100005') {
        return data['instrumentList']
      } else {
        return []
      }
    },

    //判断开关
    apiMatchMeProduceList(params, type, keys) {
      const load = this.loadingFun('商品信息匹配中')
      const rq = this.getMatchMeProduceListRQ(params)
      matchMeProduceList(rq).then((res) => {
        load.close()
        if (res.code !== 0) {
          this.uploading = false
          this.$message.error(res.message)
          return
        }
        if (type === 'editOrDetail') {
          if (!res.data || res.data.length === 0) {
            this.$message.warning('没有标品信息')
            return
          }
          params.imagesList = params.imagesList
            ? params.imagesList
            : params.imageUrlStandard
          params.firstCategory = params.erpFirstCategoryId
          this.setParamsDrugClassification(params, res.data[0])
          this.$refs.goodsCheckAlert.open(params, res.data[0], keys, type)
          return
        }

        if (!res.data || res.data.length === 0) {
          this.addSku(params)
        } else if (res.data.length === 1) {
          this.setParamsDrugClassification(params, res.data[0])
          params['firstCategoryName'] = this.$route.query.firstCategoryName
          if (!params.firstCategory && params.erpFirstCategoryId) {
            params.firstCategory = params.erpFirstCategoryId
          }
          this.$refs.goodsCheckAlert.open(params, res.data[0], keys, 'new')
        } else {
          this.$refs.goodsMoreCheckAlert.open(params, params.barcode, 'new')
        }
      })
    },

    getMatchMeProduceListRQ(data) {
      let obj = {
        id: data.id,
        approvalNumber: data.approvalNumber,
        manufacturer: data.manufacturer,
        commonName: data.commonName,
        spec: data.spec,
        code: data.code,
        productUnit: data.productUnit
      }
      return obj
    },

    //商业自建商品是否走新的匹配标品逻辑
    apiMatchSwitch(params, type, keys) {
      const load = this.loadingFun()
      matchSwitch().then((res) => {
        load.close('商品信息匹配中')
        if (res.code !== 0) {
          this.$message.error(res.message)
          return
        }
        if (res.data.status === 1) {
          this.apiMatchMeProduceList(params, type, keys)
        } else {
          this.addSku(params)
        }
      })
    },

    //检查名字是否只有*或/或-或.
    checkProductName(productName, warningMSG) {
      if (!productName) {
        return true
      }
      var pattern = /^[*/\-.]+$/
      if (pattern.test(productName)) {
        this.$message.warning(warningMSG)
        return false
      }
      return true
    },
    addSku(params) {
      const load = this.loadingFun()
      apiAddSku(params).then((res) => {
        load.close()
        if (res.code === 0) {
          // 批量
          if (
            this.formModel.from === 'supplement' ||
            this.formModel.from === 'create'
          ) {
            this.barcode = res.data.barcode
            this.statusType = res.data.status || -99
            this.statusName = this.statusNameStr(res.data.status || -99)
            this.visible = true
          } else {
            this.$message.success('商品发布成功')
            this.onCancel()
          }
        } else {
          this.$message.error(res.message)
        }
      })
    },
    editSku(params) {
      const load = this.loadingFun()
      apiEditSku(params)
        .then((res) => {
          load.close()
          if (res.code === 0) {
            this.$message.success('发布成功')
            this.onCancel()
          } else {
            this.$message.error(res.message)
          }
        })
        .catch(() => {
          load.close()
        })
    },
    toProduct() {
      if (
        this.formModel.from === 'supplement' ||
        this.formModel.from === 'create'
      ) {
        const obj = {}
        obj.status = this.statusType
        obj.barcode = this.barcode
        const path = '/productList'
        window.openTab(path, obj)
        // 批量
        // setTimeout(() => {
        //   const path = '/batchGoods'
        //   window.closeTab(path)
        // }, 100);
      }
      // else if(this.formModel.from === "initialize"){
      //   const obj = {}
      //   const path = '/productList'
      //   window.openTab(path, obj)
      //   // 自建
      //   setTimeout(() => {
      //     const path = '/product/initialize'
      //     window.closeTab(path);
      //   }, 100);
      // }
      setTimeout(() => {
        this.onCancel()
      }, 200)
    },
    tabClick(name, index) {
      this.clickIndex = index
      document.querySelector(`#${name}`).scrollIntoView(true)
    },
    onCancel() {
      this.visible = false
      if (this.formModel.isEdit) {
        let path = this.$route.fullPath
        path = decodeURI(path)
        window.closeTab(path)
      }
    },
    getFormPromise(form) {
      console.log('form', form)
      return new Promise((resolve) => {
        form.validate((res) => {
          resolve(res)
        })
      })
    },
    // 拼接参数
    getParams(
      goodsBasicForm,
      priceInventoryForm,
      upForPurchaseForm,
      supplyInformationForm,
      otherInformationForm
    ) {
      let params = {}
      params = Object.assign(
        {},
        goodsBasicForm,
        priceInventoryForm,
        upForPurchaseForm,
        supplyInformationForm,
        otherInformationForm
      )

      let imagesList = []
      if (params.imagesList && params.imagesList.urlVal) {
        imagesList = params.imagesList.urlVal.map((item) => item.name)
      }

      let instrutionImagesList = []
      if (params.instrutionImagesList && params.instrutionImagesList.urlVal) {
        instrutionImagesList = params.instrutionImagesList.urlVal.map(
          (item) => item.name
        )
      }

      let instrumentLicenseImageList = []
      if (
        params.instrumentLicenseImageList &&
        params.instrumentLicenseImageList.urlVal
      ) {
        instrumentLicenseImageList =
          params.instrumentLicenseImageList.urlVal.map((item) => item.name)
      }

      let manufacturingLicenseImageList = []
      if (
        params.manufacturingLicenseImageList &&
        params.manufacturingLicenseImageList.urlVal
      ) {
        manufacturingLicenseImageList =
          params.manufacturingLicenseImageList.urlVal.map((item) => item.name)
      }

      let userType = []
      if (params.userType) {
        userType = params.userType.join(',')
      }

      let userTypeName = []
      if (params.userTypeName) {
        userTypeName = params.userTypeName.join(',')
      }

      // let oldestProDate = '';
      // let newProDate = '';
      // if(params.opProDate){
      //   oldestProDate = params.opProDate[0]
      //   newProDate = params.opProDate[1]
      // }

      // let nearEffect = '';
      // let farEffect = '';
      // if(params.opEffect){
      //   nearEffect = params.opEffect[0]
      //   farEffect = params.opEffect[1]
      // }

      let purchaseTimeStart = ''
      let purchaseTimeEnd = ''
      if (params.opPurchaseTime) {
        purchaseTimeStart = params.opPurchaseTime[0]
        purchaseTimeEnd = params.opPurchaseTime[1]
      }

      if (this.allData.erpFirstCategoryId) {
        params.erpFirstCategoryId = this.allData.erpFirstCategoryId
      }

      if (this.allData.spuCategoryCode) {
        params.spuCategoryCode = this.allData.spuCategoryCode
      }
      if (this.allData.nonDrugCategoryCode) {
        params.nonDrugCategoryCode = this.allData.nonDrugCategoryCode
      }

      if (this.allData.source) {
        params.source = this.allData.source
      }

      params = Object.assign(
        {},
        params,
        { imagesList },
        { instrutionImagesList },
        { instrumentLicenseImageList },
        { manufacturingLicenseImageList },
        { userType },
        { userTypeName },
        { purchaseTimeStart },
        { purchaseTimeEnd }
      )
      return params
    }
  }
}
</script>

<style scoped lang="scss">
.editError-class {
  color: red;
  position: absolute;
  top: 60px;
  right: 30px;
  z-index: 10000;
}

.divBox {
  width: 100%;
  height: 100%;
  position: relative;
  padding-top: 105px;
  .bottom-info {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 105px;
    background: #fff;
    // display: flex;
    justify-content: space-between;
    align-items: center;
    z-index: 100;
    .topBox {
      padding: 16px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      span {
        font-size: 20px;
        font-weight: 500;
        text-align: left;
        color: #333333;
      }
    }
    .tabBox {
      border-bottom: 1px solid #efefef;
      display: flex;
      align-items: center;
      div:first-child {
        margin-left: 16px;
        border-left: 1px solid #efefef;
      }
      div {
        height: 40px;
        padding: 0 18px;
        font-size: 14px;
        line-height: 40px;
        font-weight: 400;
        text-align: left;
        color: rgba(0, 0, 0, 0.65);
        text-decoration: none;
        border-right: 1px solid #efefef;
        border-top: 1px solid #efefef;
        background: rgba(239, 239, 239, 0.3);
        cursor: pointer;
      }
      div.active {
        border-bottom: none;
        background: #ffffff;
        opacity: 1;
        color: #1890ff;
      }
    }
  }

  .conBox {
    width: 100%;
    height: 100%;
    overflow-y: auto;
  }

  .conBox::-webkit-scrollbar {
    width: 0 !important;
  }

  .contentBox {
    //height: 100%;
    padding: 16px 16px;
    background: #fff;

    .title {
      font-weight: 500;
      text-align: left;
      color: #000000;
      line-height: 14px;
      margin-bottom: 24px;
    }

    .title:before {
      content: '';
      display: inline-block;
      width: 3px;
      height: 13px;
      background: linear-gradient(360deg, #1d69c4 1%, #8bbdfc 99%);
      border-radius: 2px;
      margin-right: 8px;
      vertical-align: middle;
    }
  }
}
</style>
