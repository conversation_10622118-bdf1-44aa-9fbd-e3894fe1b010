<script>
import pintuanPrice from '@/views/pintuanDataUpdate/pintuanPrice.vue';
export default {
  components: {
     pintuanPrice
  },
  props: {
    value: {
      type: Object,
      default: () => {
        return {
          list: [],
          actId: ''
        }
      }
    }
  },
  watch: {
    value: {
      handler() {
        if (this.value.list.length) {
          this.visible = true;
        }
      }
    }
  },
  data() {
    return {
      visible: false,
      pintuanPriceValue: {
        visible: false,
        actId: '',
        type: 1
      },
    }
  },
  methods: {

    getRankIcon(rank) {
      if (!rank) return '';
      const rankNum = parseInt(rank.split('/')[0]);
      if (rankNum === 1) return '🥇';
      if (rankNum === 2) return '🥈';
      if (rankNum === 3) return '🥉';
      return '';
    },

    handleEdit(row) {
      this.pintuanPriceValue = {
        visible: true,
        actId: `PT${this.value.actId}`,
        type: 1
      };
    }
  }
}
</script>

<template>
  <div>
    <el-dialog :visible.sync="visible" title="商品竞价明细" width="1000px" @close="visible = false;$emit('input', { list: [], actId: '' });">
      <el-table border fit :data="value.list" height="350px">
        <el-table-column align="center" label="竞价省份" prop="provincesStr" min-width="100">
        </el-table-column>
        <el-table-column align="center" label="竞价规则" prop="competitionId" min-width="100">
        </el-table-column>
        <el-table-column align="center" label="价格" min-width="250">
          <template slot-scope="{ row }">
            <div v-if="row.biddingSuccess" class="bid-success">
              恭喜中标
            </div>
            <div v-else class="bid-fail">
              未中标
            </div>
            <div class="price-info">
              <div class="item-row">
                <span>价格排名：</span>
                <span class="rank-value">{{ row.priceRank }}</span>
                <span class="rank-icon">{{ getRankIcon(row.priceRank) }}</span>
              </div>
              <div class="item-row">
                <span>拼团价：</span>
                <span class="value-text">{{ row.groupPrice }}</span>
                <i class="el-icon-edit-outline"
                   style="color: #4184d5; font-size: 16px; cursor: pointer;transform:translateY(5px);"
                   @click="handleEdit(row)"/>
              </div>
              <div class="item-row">
                <span>准入价：</span>
                <span class="value-text">{{ row.accessPrice }}</span>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="起拼" min-width="200">
          <template slot-scope="{ row }">
            <div class="item-row">
              <span>起拼排名：</span>
              <span class="rank-value">{{ row.startQtySortRank }}</span>
              <span class="rank-icon">{{ getRankIcon(row.startQtySortRank) }}</span>
            </div>
            <div class="item-row">
              <span>起拼数：</span>
              <span class="value-text">{{ row.startQty }}</span>
              <i class="el-icon-edit-outline"
                 style="color: #4184d5; font-size: 16px; cursor: pointer;transform:translateY(5px);"
                 @click="handleEdit(row)"/>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="库存" min-width="200">
          <template slot-scope="{ row }">
            <div class="item-row">
              <span>库存排名：</span>
              <span class="rank-value">{{ row.stockSortRamk }}</span>
              <span class="rank-icon">{{ getRankIcon(row.stockSortRamk) }}</span>
            </div>
          </template>
        </el-table-column>
      </el-table>
      <div class="dialog-footer">
        <el-button @click="visible = false;$emit('input', { list: [], actId: '' });" type="primary" size="mini">关闭</el-button>
      </div>
    </el-dialog>

    <pintuanPrice v-model="pintuanPriceValue"></pintuanPrice>
  </div>
</template>

<style scoped lang="scss">
.bid-success {
  position: absolute;
  top: 0;
  left: 0;
  padding: 0 4px;
  border-bottom-right-radius: 5px;
  background-color: #00BE48;
  color: white;
  font-size: 12px;
}

.bid-fail {
  position: absolute;
  top: 0;
  left: 0;
  padding: 0 4px;
  border-bottom-right-radius: 5px;
  background-color: #ff6b00;
  color: white;
  font-size: 12px;
}

.price-info {
  padding-top: 18px;
}

.item-row {
  display: flex;
  align-items: center;
  margin-bottom: 5px;
  flex-wrap: nowrap;

  > span:first-child {
    width: 80px;
    color: #333;
    font-size: 14px;
    flex-shrink: 0;
  }

  .rank-value {
    color: #ff0000;
    margin-right: 5px;
    white-space: nowrap;
  }

  .value-text {
    color: #333;
    margin-right: 10px;
    white-space: nowrap;
  }

  .rank-icon {
    font-size: 16px;
    flex-shrink: 0;
    margin-left: 5px;
  }
}

.dialog-footer {
  text-align: right;
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

::v-deep .el-dialog {
  border-radius: 5px;
  overflow: hidden;
}

::v-deep .el-dialog__header {
  padding: 15px;
  background-color: #f3f3f3;
  border-bottom: 1px solid #e4eaf1;
}

::v-deep .el-dialog__title {
  font-weight: bold;
  color: #333;
}

::v-deep .el-dialog__header span {
  font-size: 14px;
  line-height: normal;
}

::v-deep .el-dialog__header button {
  right: 15px;
  top: 15px;
}

::v-deep .el-dialog__body {
  padding: 20px;
}

::v-deep .el-table th {
  background-color: #f5f7fa;
  color: #333;
  font-weight: bold;
  padding: 8px 0;
}

::v-deep .el-table td {
  padding: 8px 0;
}

::v-deep .el-table__body {
  width: 100%;
  table-layout: fixed;
}
</style>