import request from '@/utils/request';

//获取控销列表
export function getControlList(params) {
    return request({
        url: '/salesControl/listControSalePage',
        method: 'post',
        data: params,
    });
}
// 获取药店类型
export function findUserTypes(params) {
    return request({
        url: '/salesControl/findUserTypes',
        method: 'get',
        data: params,
    });
}
//删除控销商品
export function deletebBatchDel(params) {
    return request({
        url: '/salesControl/removeControlSale',
        method: 'post',
        data: params,
    });
}
// 导出控销商品
export function exportControlSale(params) {
    return request({
        url: '/salesControl/async/exportControlSku',
        method: 'get',
        params: params,
    });
}
// 分页查询操作记录
export function startUploadList(params) {
    return request({
        url: '/product/upload/list',
        method: 'get',
        params: params,
    });
}
// 导入控销数据
export function importControlSaleInfos(params) {
    return request({
        url: '/salesControl/importControlSaleInfos',
        method: 'post',
        data: params,
    });
}
//获取商品列表
export function findSkuList(params) {
    return request({
        url: '/salesControl/findSku',
        method: 'get',
        params: params,
    });
}
// 添加控销设置-导出控销商品：
export function exportControlProducts(params) {
    return request({
        url: '/exportControlProducts',
        method: 'get',
        data: params,
    });
}
// 获取药店信息列表
export function searchMerchantInfo(params) {
    return request({
        url: '/salesControl/searchMerchantInfo',
        method: 'post',
        data: params,
    });
}
// 获取控销区域
export function dicAreas(params) {
    return request({
        url: '/salesControl/dicAreas',
        method: 'get',
        params: params,
    });
}
// 批量导入
export function importProduct(params) {
    return request({
        url: '/salesControl/importProduct',
        method: 'post',
        data: params,
    });
}
//获取单个详情
export function getControlOne(params) {
    return request({
        url: '/salesControl/detail',
        method: 'get',
        params: params,
    });
}
// 导出客户列表
export function listsExport(params) {
    return request({
        url: '/salesControl/listsExport',
        method: 'get',
        params: params,
    });
}
// 新增/修改控销信息
export function update(params) {
    return request({
        url: '/skuMerchantGroup/updateGroupName',
        method: 'post',
        data: params,
    });
}
//新增控销店铺
export function addGroupRelation(params) {
    return request({
        url: '/skuMerchantGroup/addGroupRelation',
        method: 'post',
        data: params,
    });
}
// 下载控销商品导入模板
export function downloadControlProductImportTemplate(params) {
    return request({
        url: '/salesControl/downloadControlProductImportTemplate',
        method: 'get',
        data: params,
    });
}
// 查询可见可买用户信息
export function whiteMerchantList(params) {
    return request({
        url: '/salesControl/whiteMerchantList',
        method: 'get',
        data: params,
    });
}
// 查询可见可买用户信息
export function shopSkuDafaultType(params) {
    return request({
        url: '/salesControl/shopSkuDafaultType',
        method: 'get',
        data: params,
    });
}

// 查询商品是否已有控销
export function hasControl(params) {
  return request({
    url: '/salesControlV2/hasControl',
    method: 'post',
    data: params,
  });
}

// 控销组列表查询
export function getSkuMerchantGroup(params) {
	return request({
		url: '/skuMerchantGroup/page',
		method: 'get',
		params,
	});
}

// 控销药店明细查询
export function merchantPage(params) {
	return request({
		url: '/skuMerchantGroup/merchantPage',
		method: 'get',
		params,
	});
}

// 控销商品明细查询
export function skuPage(params) {
	return request({
		url: '/skuMerchantGroup/skuPage',
		method: 'get',
		params,
	});
}

// 保存控销组
export function saveMerchantGroup(params) {
  return request({
    url: '/skuMerchantGroup/save',
    method: 'post',
    data: params,
  });
}

// 查看日志
export function seeLog(params) {
	return request({
		url: '/skuMerchantGroup/log',
		method: 'get',
		params,
	});
}
//新增页文件导入
export function importMerchants(params) {
	const forms = new FormData();
	forms.append('file', params.file);
	forms.append('successMerchantIds', params.successMerchantIds)
	return request({
		url: '/skuMerchantGroup/importMerchants',
		method: 'post',
		data: forms,
		headers: { 'Content-Type': 'multipart/form-data' },
	});
}

//编辑页文件导入
export function importMerchantsV2(params) {
	const forms = new FormData();
	forms.append('file', params.file);
	forms.append('groupId', params.groupId)
	return request({
		url: '/skuMerchantGroup/importMerchantsV2',
		method: 'post',
		data: forms,
		headers: { 'Content-Type': 'multipart/form-data' },
	});
}

// 查询导入的控销组
export function getMerchantInfo(params) {
  return request({
    url: '/skuMerchantGroup/searchMerchantInfo',
    method: 'post',
    data: params,
  });
}

// 控销组详情
export function detailWithMerchant(params) {
	return request({
		url: '/skuMerchantGroup/detailWithMerchant',
		method: 'get',
		params,
	});
}

//删除药品组
export function deleteMerchantGroup(params) {
  return request({
    url: '/skuMerchantGroup/deleteByMerchantIds',
    method: 'post',
    data: params,
  });
}
