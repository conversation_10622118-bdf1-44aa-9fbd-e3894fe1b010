<template>
  <div class="info-box">
    <el-form
      ref="qual"
      :model="mergeData"
      size="small"
      :rules="qualRules"
      class="my-form"
      label-position="right"
      label-width="80px"
    >
      <el-form-item
        v-for="(itemPar, indexPar) in businessData"
        :key="indexPar"
        label=""
      >
        <div
          v-if="isDetail"
          class="check-box-list"
        >
          <span>{{ itemPar.categoryName }}</span>
          <el-checkbox-group
            v-model.trim="mergeData[itemPar.categoryId].checkList"
            class="my-check"
            @change="changeBox($event, itemPar.categoryId)"
          >
            <el-checkbox
              v-for="itE in itemPar.businessScopeDtoList"
              :key="itE.id"
              :label="itE.id"
              :checked="checked"
              @change="checked = !checked"
            >
              {{ itE.dictName }}
            </el-checkbox>
          </el-checkbox-group>
        </div>
        <div
          v-else-if="!isDetail && itemPar.ckeckListData.length > 0"
          class="check-box-list"
        >
          <el-form-item :label="itemPar.categoryName">
            <span
              v-for="itC in itemPar.ckeckListData"
              :key="itC.id"
              style="width: auto"
            >{{
              itC.name
            }}</span>
          </el-form-item>
        </div>
        <div
          v-if="itemPar.qualDataList.length > 0"
          class="qual-list-box"
        >
          <el-form-item
            v-for="(item, index) in itemPar.qualDataList"
            :key="index"
            label=""
          >
            <div class="qual-list-img">
              <div
                class="list-name"
                style="display: flex;"
              >
                <i
                  v-if="item.isNeed"
                  class="red-i"
                >*</i>{{ item.name }}
                <div
                  v-if="item.expireState === 1 || item.expireState === 2"
                  class="expireState"
                  :class="{ orange: item.expireState === 1}"
                >
                  {{ item.expireState === 1 ? '即将过期' : '已过期' }}
                </div>
              </div>
              <div class="list-remark">
                {{ item.remark }}
              </div>
              <div
                v-if="
                  (mergeData[itemPar.categoryId].qualDetail[item.id] && qualStateStr === 6) ||
                    (isHistory && qualStateStr === 2)
                "
                class="list-remark"
                style="color: red"
              >
                {{
                  mergeData[itemPar.categoryId].qualDetail[item.id].remarks
                    ? '驳回原因：' + mergeData[itemPar.categoryId].qualDetail[item.id].remarks
                    : ''
                }}
              </div>
              <el-form-item :prop="item.isNeed ? item.id + 'imgList' : ''">
                <el-upload
                  :ref="item.id"
                  action=""
                  :accept="
                    item.name === '食品商标注册证及品牌授权' ||
                      item.name === '医疗器械商标注册证及品牌授权' ||
                      item.name === '消毒产品商标注册证及品牌授权' ||
                      item.name === '化妆品商标注册证及品牌授权'
                      ? '.jpg,.jpeg,.png,.JPG,.JPEG,.zip,.rar'
                      : '.jpg,.jpeg,.png,.JPG,.JPEG'
                  "
                  :class="{
                    hide: !isDetail || mergeData[itemPar.categoryId].qualDetail[item.id].isUpload
                  }"
                  :http-request="
                    (file) => {
                      return uploadImg(file, item.id, itemPar.categoryId, item.id)
                    }
                  "
                  list-type="picture-card"
                  :file-list="
                    mergeData[itemPar.categoryId].qualDetail[item.id]
                      ? mergeData[itemPar.categoryId].qualDetail[item.id].urlVal
                      : []
                  "
                  :on-preview="handlePictureCardPreview"
                  :on-remove="
                    (file) => {
                      return handleRemove(
                        file,
                        mergeData[itemPar.categoryId].qualDetail[item.id].urlVal,
                        itemPar.categoryId,
                        item.id
                      )
                    }
                  "
                  :on-change="
                    (file) => {
                      return fileChange(file, item.id)
                    }
                  "
                  :limit="item.maxImg"
                  :disabled="isDetail ? false : true"
                  class="avatar-uploader"
                >
                  <i class="el-icon-plus" />
                  <div
                    v-if="isDetail"
                    slot="tip"
                    class="el-upload__tip"
                  >
                    <span
                      v-if="
                        item.name === '食品商标注册证及品牌授权' ||
                          item.name === '医疗器械商标注册证及品牌授权' ||
                          item.name === '消毒产品商标注册证及品牌授权' ||
                          item.name === '化妆品商标注册证及品牌授权'
                      "
                    >上传jpg、png、rar、zip文件，且不超过20MB</span>
                    <span v-else>只能上传jpg/png文件，且不超过1MB</span>
                  </div>
                </el-upload>
                <el-input
                  v-show="false"
                  v-model.trim="mergeData[item.id + 'imgList']"
                />
              </el-form-item>
            </div>
            <el-form-item
              v-if="item.personNameShow"
              :label="item.personNameShowName"
              class="width60"
              :prop="item.legalPersonName ? item.id + 'name' : ''"
            >
              <!--              <span slot="label">-->
              <!--                <i v-if="item.legalPersonName" style="color: red">*</i>{{ item.personNameShowName }}-->
              <!--              </span>-->
              <el-input
                v-if="isDetail"
                v-model.trim.trim="mergeData[itemPar.categoryId].qualDetail[item.id].nameVal"
                style="width: 80%"
                type="text"
                @change="setValue($event, item.id + 'name')"
              />
              <span v-else>{{ mergeData[itemPar.categoryId].qualDetail[item.id].nameVal }}</span>
            </el-form-item>
            <el-form-item
              v-if="item.needCodeShow"
              :label="item.needCodeShowName"
              class="width60"
              :prop="item.isNeedCode ? item.id + 'code' : ''"
            >
              <!--              <span slot="label">-->
              <!--                <i v-if="item.isNeedCode" class="red-i">*</i>{{ item.needCodeShowName }}-->
              <!--              </span>-->
              <el-input
                v-if="isDetail"
                v-model.trim.trim="mergeData[itemPar.categoryId].qualDetail[item.id].codeVal"
                style="width: 80%"
                type="text"
                @change="setValue($event, item.id + 'code')"
              />
              <span v-else>{{ mergeData[itemPar.categoryId].qualDetail[item.id].codeVal }}</span>
            </el-form-item>
            <el-form-item
              v-if="item.needTimeEndShow"
              :label="item.needTimeShowName"
              :prop="item.isNeedTime ? item.id + 'time' : ''"
              class="width70"
            >
              <!--              <span slot="label">-->
              <!--                <i v-if="item.isNeedTime" class="red-i">*</i>{{ item.needTimeShowName }}-->
              <!--              </span>-->
              <div>
                <el-col :span="7">
                  <el-form-item v-if="isDetail">
                    <el-date-picker
                      :picker-options="{ disabledDate: time => time.getTime() > (new Date().getTime()) }"
                      v-model.trim="mergeData[itemPar.categoryId].qualDetail[item.id].startDateVal"
                      type="date"
                      value-format="timestamp"
                      placeholder="开始时间"
                      style="width: 100%"
                      @change="
                        changeDataTime(
                          mergeData[itemPar.categoryId].qualDetail[item.id].startDateVal,
                          mergeData[itemPar.categoryId].qualDetail[item.id].endDateVal,
                          item.id,
                          itemPar.categoryId,
                          item.id
                        )
                      "
                    />
                  </el-form-item>
                  <span v-else>{{
                    mergeData[itemPar.categoryId].qualDetail[item.id].startDateVal | formatDate
                  }}</span>
                </el-col>
                <el-col
                  class="line"
                  :span="2"
                  style="text-align: center"
                >
                  至
                </el-col>
                <el-col :span="7">
                  <el-date-picker
                    v-if="isDetail"
                    v-model.trim="mergeData[itemPar.categoryId].qualDetail[item.id].endDateVal"
                    :class="{ corComponentOnColor: item.expireState === 1 && onColor, corComponentOnColorRed: item.expireState === 2 && onColor }"
                    :picker-options="options"
                    type="date"
                    placeholder="结束时间"
                    value-format="timestamp"
                    :disabled="
                      mergeData[itemPar.categoryId].qualDetail[item.id].longTermVal ? true : false
                    "
                    style="width: 100%"
                    @change="
                      onColor = false;
                      changeDataTime(
                        mergeData[itemPar.categoryId].qualDetail[item.id].startDateVal,
                        mergeData[itemPar.categoryId].qualDetail[item.id].endDateVal,
                        item.id,
                        itemPar.categoryId,
                        item.id
                      )
                    "
                  />
                  <span v-else>{{
                    mergeData[itemPar.categoryId].qualDetail[item.id].endDateVal | formatDate
                  }}</span>
                </el-col>
                <el-col
                  v-if="item.withLongTime"
                  class="line"
                  :span="3"
                  style="text-align: center"
                >
                  <el-checkbox
                    v-if="isDetail"
                    :checked="
                      mergeData[itemPar.categoryId].qualDetail[item.id].longTermVal ? true : false
                    "
                    @change="
                      setLongTime(
                        mergeData[itemPar.categoryId].qualDetail[item.id].startDateVal,
                        mergeData[itemPar.categoryId].qualDetail[item.id].endDateVal,
                        item.id,
                        itemPar.categoryId,
                        item.id
                      )
                    "
                  >
                    长期有效
                  </el-checkbox>
                  <span v-else>{{
                    mergeData[itemPar.categoryId].qualDetail[item.id].longTermVal ? '长期有效' : ''
                  }}</span>
                </el-col>
              </div>
            </el-form-item>
            <el-form-item
              v-if="item.isNeedGainTime"
              label="获取日期"
              :prop="item.isNeedGainTime ? item.id + 'gainTime' : ''"
              class="width70"
            >
              <el-col :span="7">
                <el-date-picker
                  v-if="isDetail"
                  v-model.trim="mergeData[itemPar.categoryId].qualDetail[item.id].gainTime"
                  :picker-options="{ disabledDate: time => time.getTime() > (new Date().getTime()) }"
                  type="date"
                  placeholder="获取日期"
                  value-format="timestamp"
                  style="width: 100%"
                  @change="
                    changeGainTime(
                      mergeData[itemPar.categoryId].qualDetail[item.id].gainTime,
                      item.id,
                      itemPar.categoryId,
                      item.id
                    )
                  "
                />
                <span v-else>{{
                  mergeData[itemPar.categoryId].qualDetail[item.id].gainTime | formatDate
                }}</span>
              </el-col>
            </el-form-item>
          </el-form-item>
        </div>
      </el-form-item>
    </el-form>
    <!-- <el-dialog :visible.sync="dialogVisible">
      <img
        width="100%"
        :src="dialogImageUrl"
        alt=""
      >
    </el-dialog> -->
    <el-image-viewer
      v-if="dialogVisible"
      :url-list="[dialogImageUrl]"
      :on-close="closeImg"
      append-to-body
      :z-index="100000"
    />
  </div>
</template>

<script>
import { uploadFile } from '@/api/qual';
import ElImageViewer from 'element-ui/packages/image/src/image-viewer';

export default {
  name: 'QualComponent',
  components: { ElImageViewer },
  filters: {
    formatDate(value) {
      if (value > 0) {
        const date = new Date(value);
        const y = date.getFullYear();
        let MM = date.getMonth() + 1;
        MM = MM < 10 ? `0${MM}` : MM;
        let d = date.getDate();
        d = d < 10 ? `0${d}` : d;
        return `${y}-${MM}-${d}`;
      }
      return '';
    },
  },
  props: {
    /* 资质信息 */
    qualificationData: {
      type: Array,
      default: () => [],
    },
    /* 保存后信息 */
    corporationQualifications: {
      type: Array,
      default: () => [],
    },
    /* 保存后的经营类目 */
    checkedBussiness: {
      type: Array,
      default: () => [],
    },
    hostName: {
      type: String,
      default: 'http://t-upload.ybm100.com',
    },
    /* 经营类目 */
    businessCategory: {
      type: Array,
      default: () => [],
    },
    isDetail: {
      type: Boolean,
      default: true,
    },
    qualStateStr: {
      type: Number,
      default: null,
    },
    isHistory: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      onColor: true,
      options: { disabledDate: time => time.getTime() < Date.now() },
      checked: false,
      checkedBusinessData: [],
      mergeData: {}, // 处理以后的数据
      engageInData: [], // 资质，
      engageInDataLast: {}, // 处理后的资质信息
      businessData: [], // 经营类目
      qualData: [], // 保存后的数据
      dialogImageUrl: '',
      dialogVisible: false,
      qualRules: {}, // 验证规则
    };
  },
  watch: {
    qualificationData: {
      immediate: true,
      handler(newVale) {
        this.$nextTick(() => {
          this.engageInData = newVale ? JSON.parse(JSON.stringify(newVale)) : [];
          this.initData();
        });
      },
    },
    businessCategory: {
      immediate: true,
      handler(newVale) {
        this.$nextTick(() => {
          this.businessData = newVale;
          this.initData();
        });
      },
    },
    checkedBussiness: {
      immediate: true,
      handler(newVale) {
        this.$nextTick(() => {
          this.checkedBusinessData = newVale;
        });
      },
    },
    corporationQualifications: {
      immediate: true,
      handler(newVale) {
        this.$nextTick(() => {
          this.qualData = newVale;
        });
      },
    },
  },
  methods: {
    closeImg() {
      this.dialogVisible = false;
    },
    initData() {
      const that = this;
      /* 首先进行资质赋值 */
      this.mergeData = {};
      this.engageInDataLast = {};
      this.businessData = this.businessData.concat([]);
      // this.businessData = this.businessData.splice(0, 4);
      // console.log(88888888, this.businessData);
      // console.log(23232323, this.engageInData);
      this.engageInData.forEach((item, index) => {
        const nameStr = item.businessCategoryIdList.join(',');
        for (let i = 0; i < item.details.length; i++) {
          const data = item.details[i];
          data.nameVal = '';
          data.codeVal = '';
          data.startDateVal = null;
          data.endDateVal = null;
          data.longTermVal = null;
          data.gainTime = null;
          data.state = '';
          data.remarks = '';
          data.urlVal = [];
          data.isUpload = false;
          // console.log(***********, that.qualData);
          for (let j = 0; j < that.qualData.length; j++) {
            const ite = that.qualData[j];
            if (data.id === ite.qualificationsDetailId) {
              data.gainTime = ite.gainTime;
              data.expireState = ite.expireState;
              data.nameVal = ite.legalPersonName;
              data.codeVal = ite.code;
              data.startDateVal = ite.startDate > 0 ? ite.startDate : null;
              data.endDateVal = ite.endDate > 0 ? ite.endDate : null;
              data.longTermVal = !!ite.longTerm;
              data.state = ite.state;
              data.remarks = ite.remarks ? ite.remarks : '';
              data.urlVal = [];
              const urlAry = ite.url.split(',');
              urlAry.forEach((itAry, indAry) => {
                if (itAry) {
                  if (itAry.indexOf('.zip') > -1 || itAry.indexOf('.rar') > -1) {
                    data.urlVal.push({
                      name: itAry.substring(itAry.length - 6),
                      url:
                        'https://upload.ybm100.com/G4/M00/79/B5/CiIC2l_1oOiAcZjIAACvsLpp45A626.png',
                      dataUrl: itAry,
                      uid: index + indAry,
                    });
                  } else {
                    data.urlVal.push({
                      name: itAry.substring(itAry.length - 6),
                      url: itAry,
                      uid: index + indAry,
                    });
                  }
                }
              });
              data.urlVal.length === ite.maxImg ? (data.isUpload = true) : (data.isUpload = false);
            }
          }
          // console.log(900000000);
        }
        that.engageInDataLast[nameStr] = item;
        // console.log(1212121211, that.engageInDataLast);
      });
      this.initS();
    },
    initS() {
      const that = this;
      /* 把处理后的资质跟经营类目对应上 */
      this.businessData.forEach((item) => {
        const nameStr = item.categoryId;
        // let str = '';
        let qualAllDetailAry = [];
        item.qualDetail = {};
        item.checkList = [];
        item.qualDataList = [];
        item.ckeckListData = [];
        // const str = item.businessScopeDtoList[0].id.toString()
        let str = [];
        for (let i = 0; i < item.businessScopeDtoList.length; i++) {
          str.push(item.businessScopeDtoList[i].id);
        }
        str = str.sort();
        if (str) {
          const engageInAry = Object.keys(that.engageInDataLast);
          engageInAry.forEach((itemx) => {
            const ary = itemx.split(',').sort();
            if (that.aryIncludes(str, ary)) {
              // item.qualDetail = item.qualDetail.concat(that.engageInDataLast[itemx].details)
              qualAllDetailAry = qualAllDetailAry.concat(that.engageInDataLast[itemx].details);
            }
          });
          // for (let j = 0; j < item.qualDetail.length; j++) {
          //   that.$set(that.qualRules, `${item.qualDetail[j].id}name`, [
          //     { required: true, message: '请输入名称', trigger: 'blur' },
          //     { min: 1, max: 10, message: '长度在 1 到 10 个字符', trigger: 'blur' }
          //   ])
          //   that.$set(that.qualRules, `${item.qualDetail[j].id}code`, [
          //     { required: true, message: '请输入证件号', trigger: 'blur' },
          //     { min: 1, max: 30, message: '长度在 1 到 30 个字符', trigger: 'blur' }
          //   ])
          //   that.$set(that.qualRules, `${item.qualDetail[j].id}imgList`, [
          //     { required: true, message: '请上传基础图片', trigger: 'change' }
          //   ])
          //   that.$set(that.qualRules, `${item.qualDetail[j].id}time`, [
          //     { required: true, message: '请选择有效期', trigger: 'change' }
          //   ])
          //   that.$set(that.mergeData, `${item.qualDetail[j].id}name`, item.qualDetail[j].nameVal)
          //   that.$set(that.mergeData, `${item.qualDetail[j].id}code`, item.qualDetail[j].codeVal)
          //   that.$set(that.mergeData, `${item.qualDetail[j].id}imgList`, item.qualDetail[j].urlVal)
          //   that.$set(
          //     that.mergeData,
          //     `${item.qualDetail[j].id}time`,
          //     item.qualDetail[j].startDateVal
          //   )
          // }
          for (let j = 0; j < qualAllDetailAry.length; j++) {
            const nameIdStr = qualAllDetailAry[j].id;
            that.$set(that.qualRules, `${qualAllDetailAry[j].id}name`, [
              { required: true, message: '请输入名称', trigger: 'blur' },
              { min: 1, max: 10, message: '长度在 1 到 10 个字符', trigger: 'blur' },
            ]);
            that.$set(that.qualRules, `${qualAllDetailAry[j].id}code`, [
              { required: true, message: '请输入证件号', trigger: 'blur' },
              { min: 1, max: 30, message: '长度在 1 到 30 个字符', trigger: 'blur' },
            ]);
            that.$set(that.qualRules, `${qualAllDetailAry[j].id}imgList`, [
              { required: true, message: '请上传基础图片', trigger: 'change' },
            ]);
            that.$set(that.qualRules, `${qualAllDetailAry[j].id}time`, [
              { required: true, message: '请选择有效期', trigger: 'change' },
            ]);
            that.$set(that.qualRules, `${qualAllDetailAry[j].id}gainTime`, [
              { required: true, message: '请选择获取日期', trigger: 'change' },
            ]);
            that.$set(that.mergeData, `${qualAllDetailAry[j].id}name`, qualAllDetailAry[j].nameVal);
            that.$set(that.mergeData, `${qualAllDetailAry[j].id}code`, qualAllDetailAry[j].codeVal);
            that.$set(
              that.mergeData,
              `${qualAllDetailAry[j].id}imgList`,
              qualAllDetailAry[j].urlVal,
            );
            that.$set(
              that.mergeData,
              `${qualAllDetailAry[j].id}time`,
              qualAllDetailAry[j].startDateVal,
            );
            that.$set(
              that.mergeData,
              `${qualAllDetailAry[j].id}gainTime`,
              qualAllDetailAry[j].gainTime,
            );
            item.qualDetail[nameIdStr] = qualAllDetailAry[j];
          }
          for (let k = 0; k < that.checkedBusinessData.length; k++) {
            const one = that.checkedBusinessData[k];
            const pidStr = one.categoryId ? one.categoryId.toString() : one.categoryId.toString();
            if (that.aryIncludes(str, [pidStr])) {
              const ids = one.categoryId ? one.categoryId : one.categoryId;
              item.checkList.push(ids);
              that.changeBox(item.checkList, nameStr);
              item.ckeckListData.push(one);
            }
          }
        }
        item.checkList = Array.from(new Set(item.checkList));
        that.$set(that.mergeData, nameStr, JSON.parse(JSON.stringify(item)));
        that.$emit('setHeight');
      });
    },
    handlePictureCardPreview(file) {
      // console.log(file,'file111')
      // const aLink = document.createElement('a')
      // aLink.style.display = 'none'
      // aLink.href = file.url
      // aLink.setAttribute('download', file.name) // 下载的文件
      // document.body.appendChild(aLink)
      // aLink.click()
      // document.body.removeChild(aLink)
      if (file.dataUrl || file.url.indexOf('.zip') > -1 || file.url.indexOf('.rar') > -1) {
        console.log(file.url);
      } else {
        this.dialogImageUrl = file.url;
        this.dialogVisible = true;
      }
    },
    uploadImg(file, index, idStr, inds) {
      const that = this;
      uploadFile(file).then((res) => {
        if (res.code === '200') {
          if (res.data.indexOf('.zip') > -1 || res.data.indexOf('.rar') > -1) {
            that.mergeData[idStr].qualDetail[index].urlVal.push({
              name: file.file.name,
              url: 'https://upload.test.ybm100.com/G1/M00/1A/1D/Cgoz01_0BsGAXMyOAACvsLpp45A932.png',
              dataUrl: `${that.hostName}/${res.data}`,
              uid: file.file.uid,
            });
          } else {
            that.mergeData[idStr].qualDetail[index].urlVal.push({
              name: file.file.name,
              url: `${that.hostName}/${res.data}`,
              uid: file.file.uid,
            });
          }
          file.onSuccess();
          this.$set(this.mergeData, `${inds}imgList`, res.data);
          that.mergeData[idStr].qualDetail[index].urlVal.length
          === that.mergeData[idStr].qualDetail[index].maxImg
            ? (that.mergeData[idStr].qualDetail[index].isUpload = true)
            : (that.mergeData[idStr].qualDetail[index].isUpload = false);
        } else {
          file.onError();
          that.$message.error('上传失败');
        }
      });
    },
    handleRemove(file, fileList, idStr, ind) {
      fileList.forEach((item, index) => {
        if (item.uid === file.uid) {
          fileList.splice(index, 1);
        }
      });
      if (fileList.length < 1) {
        this.$set(this.mergeData, `${ind}imgList`, '');
      }
      this.mergeData[idStr].qualDetail[ind].isUpload = false;
    },
    fileChange(file) {
      console.log(file, 'file');
      console.log(this, 'this');
    },
    changeBox(val, idStr) {
      // console.log(1111111, val, idStr);
      const that = this;
      let qualDetail = [];
      if (val.length > 0) {
        const str = val.sort();
        // console.log(88889999, str);
        if (str) {
          const engageInAry = Object.keys(that.engageInDataLast);
          // console.log(34343434343434, that.engageInDataLast)
          engageInAry.forEach((itemx) => {
            const ary = itemx.split(',').sort();
            if (that.aryIncludes(str, ary)) {
              // console.log(900099999, itemx);
              qualDetail = qualDetail.concat(that.engageInDataLast[itemx].details);
            }
          });
        }
      }
      // console.log(565656565, qualDetail);
      this.businessData.forEach((item) => {
        if (item.categoryId === idStr) {
          if (val.length > 0) {
            item.qualDataList = [];
            if (item.qualDataList.length > 0) {
              item.qualDataList.forEach((items) => {
                qualDetail.forEach((ites, indexs) => {
                  if (items.id === ites.id) {
                    qualDetail.splice(indexs, 1);
                  }
                });
              });
              item.qualDataList = item.qualDataList.concat(qualDetail);
            } else {
              item.qualDataList = qualDetail;
            }
          } else {
            item.qualDataList = [];
          }
        }
      });
      that.businessData = that.businessData.concat([]);
    },
    processData() {
      let mergeDataBus = [];
      let businessAll = [];
      const corporationBusiness = [];
      const qualDataListAll = [];
      let checkedQualList = [];
      const that = this;
      const sendData = [];
      const mergeDataAry = Object.keys(this.mergeData);
      mergeDataAry.forEach((itemd) => {
        const data = that.mergeData[itemd];
        if (data && data.checkList && data.businessScopeDtoList && data.qualDetail) {
          const ary = Array.from(new Set(data.checkList));
          mergeDataBus = mergeDataBus.concat(ary);
          businessAll = businessAll.concat(data.businessScopeDtoList);
          if (data.checkList.length > 0) {
            // qualDataListAll = qualDataListAll.concat()
            const quDeIt = Object.keys(data.qualDetail);
            quDeIt.forEach((itId) => {
              qualDataListAll.push(data.qualDetail[itId]);
            });
          }
        }
      });
      this.businessData.forEach((item) => {
        checkedQualList = checkedQualList.concat(item.qualDataList);
      });
      qualDataListAll.forEach((item) => {
        if (item && item.id) {
          checkedQualList.forEach((itex) => {
            if (itex && itex.id) {
              if (item.id === itex.id) {
                if (
                  item.legalPersonName
                  || (item.isNeedCode && item.codeVal)
                  || (item.isNeedTime && item.startDateVal && item.startDateVal > 0)
                  || item.urlVal
                  || (!item.longTermVal && item.endDateVal && item.endDateVal > 0)
                  || (item.isNeedGainTime && item.gainTime)
                ) {
                  const obj = {};
                  obj.qualificationsDetailId = item.id;
                  obj.longTerm = item.longTermVal ? 1 : 0;
                  obj.name = item.name;
                  obj.legalPersonName = item.nameVal;
                  obj.code = item.codeVal;
                  obj.startDate = item.startDateVal;
                  obj.endDate = item.endDateVal;
                  obj.gainTime = item.gainTime;
                  obj.url = '';
                  if (item.urlVal.length > 0) {
                    item.urlVal.forEach((itemd) => {
                      if (itemd.dataUrl) {
                        obj.url += `${itemd.dataUrl},`;
                      } else {
                        obj.url += `${itemd.url},`;
                      }
                    });
                  } else {
                    obj.url = '';
                  }
                  if (
                    (obj.legalPersonName && item.legalPersonName)
                    || (obj.code && item.isNeedCode)
                    || (obj.startDate && item.isNeedTime)
                    || (obj.endDate && !item.longTermVal)
                    || (obj.gainTime && item.isNeedGainTime)
                    || obj.url
                  ) {
                    sendData.push(obj);
                  }
                }
              }
            }
          });
        }
      });
      mergeDataBus.forEach((item) => {
        businessAll.forEach((ite) => {
          if (item === ite.id) {
            const obj = {};
            obj.categoryId = ite.id;
            obj.name = ite.dictName;
            obj.pId = ite.parentId;
            corporationBusiness.push(obj);
          }
        });
      });
      this.deWeight(corporationBusiness);
      return { sendData, corporationBusiness };
    },
    submitForm(f) {
      if (f) {
        const sendObj = this.processData();
        this.$emit('formQual', sendObj, 1);
      } else {
        this.$refs.qual.validate((valid, rule) => {
          if (valid) {
            const sendObj = this.processData();
            this.$emit('formQual', sendObj);
          } else {
            const validAry = rule[Object.keys(rule)[0]];
            const msgName = validAry[0].message;
            // const refs = validAry[0].field
            this.$message.warning({
              message: msgName,
              type: 'warning',
              offset: '60',
            });
            this.$emit('formQual', false);
          }
        });
      }
    },
    setValue(val, env) {
      this.$set(this.mergeData, env, val);
    },
    setLongTime(start, end, idStr, list, ind) {
      this.mergeData[list].qualDetail[ind].longTermVal = !this.mergeData[list].qualDetail[ind]
        .longTermVal;
      if (start) {
        this.mergeData[list].qualDetail[ind].endDateVal = '';
        this.$set(this.mergeData, `${idStr}time`, start);
      }
    },
    changeGainTime(start, idStr) {
      this.$set(this.mergeData, `${idStr}gainTime`, start);
    },
    changeDataTime(start, end, idStr, list, ind) {
      if (!start) {
        this.$set(this.mergeData, `${idStr}time`, '');
      }
      if (start && end && start > end && !this.mergeData[list].qualDetail[ind].longTermVal) {
        end = '';
        this.$message.warning('结束时间不能小于开始时间');
        this.mergeData[list].qualDetail[ind].endDateVal = '';
      } else if (start && end && !this.mergeData[list].qualDetail[ind].longTermVal) {
        this.$set(this.mergeData, `${idStr}time`, start);
      } else if (start && this.mergeData[list].qualDetail[ind].longTermVal) {
        this.$set(this.mergeData, `${idStr}time`, start);
      }
    },
    formatterS(value) {
      if (value.length > 0 && value.length < 15) {
        this.$toast('请输入正确的身份证号码');
      }
    },
    aryIncludes(ary, aryKey) {
      let num = 0;
      for (let i = 0; i < ary.length; i++) {
        for (let j = 0; j < aryKey.length; j++) {
          if (Number(ary[i]) === Number(aryKey[j])) {
            num++;
          }
        }
      }
      if (num > 0) {
        return true;
      }
      return false;
    },
    deWeight(arr) {
      for (let i = 0; i < arr.length - 1; i++) {
        for (let j = i + 1; j < arr.length; j++) {
          if (arr[i].categoryId === arr[j].categoryId) {
            arr.splice(j, 1);
            // 因为数组长度减小1，所以直接 j++ 会漏掉一个元素，所以要 j--
            j--;
          }
        }
      }
      return arr;
    },
    // formatterNum(value) {
    //   return (value = value.replace(/[\W]/g, ''))
    // },
    // formatter(value) {
    //   return (value = value.replace(
    //     new RegExp(
    //       '[\\+,\\/,\\\\,\\?,\\？,\\%,\\#,\\&,\\=,\\(,\\),\\（,\\）,\\{,\\},\\\',\\",\\<,\\>,\\@,\\!,\\！,\\$,\\.,\\，,\\、,\\:,\\：,\\;,\\；,\\￥,\\*,\\~,\\`,\\-,\\——,\\_,\\^,\\“,\\”,\\‘,\\’,\\……,\\【,\\】,\\[,\\],\\,]',
    //       'gm'
    //     ),
    //     ''
    //   ))
    // }
  },
};
</script>

<style lang="scss">
.corComponentOnColor {
  .el-input__inner {
    border-color: orange;
  }
}
.corComponentOnColorRed {
  .el-input__inner {
    border-color: red;
  }
}
</style>
<style scoped lang="scss">
.my-form {
  ::v-deep  .el-form-item__content {
    //margin-left: 0 !important;
  }
  width: 95%;
  .qual-list-box {
    width: 80%;
    padding: 15px 0;
    margin-left: 80px;
    background: #fafafa;
    .expireState {
      padding: 0 3px;
      color: red;
      border: 1px solid red;
      line-height: 26px;
      height: 26px;
      border-radius: 3px;
      margin-left: 5px;
    }
    .orange {
      color: orange;
      border-color: orange;
    }
    .qual-list-img {
      padding-left: 20px;
    }
    .list-remark {
      color: #666666;
      font-size: 12px;
    }
    .red-i {
      color: red;
      padding-right: 5px;
    }
  }
  .width60 {
    width: 70%;
  }
  .width70 {
    width: 80%;
  }
  ::v-deep   .hide .el-upload--picture-card {
    display: none;
  }
}
.check-box-list {
  span {
    display: inline-block;
    float: left;
    width: 70px;
    padding-right: 10px;
    text-align: right;
    font-weight: bold;
    color: #333333;
  }
  .my-check {
    padding-left: 80px;
  }
}
.avatar-uploader ::v-deep  .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  background: rgba(0, 0, 0, 0.02);
  width: 126px;
  height: 126px;
  line-height: 126px;
}
.avatar-uploader ::v-deep  .el-upload:hover {
  border-color: #409eff;
}
.avatar-uploader ::v-deep  .el-upload__tip {
  margin-top: 0;
  color: #999999;
  font-size: 12px;
}
.avatar-uploader-icon {
  font-size: 28px;
  color: #666666;
  width: 126px;
  height: 126px;
  line-height: 126px;
  text-align: center;
}
.avatar {
  width: 126px;
  height: 126px;
  display: block;
}
.avatar-uploader ::v-deep  .el-upload-list--picture-card .el-upload-list__item {
  width: 126px;
  height: 126px;
}
</style>
