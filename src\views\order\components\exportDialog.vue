<template>
  <div>
    <el-dialog
      :title="exportType === '1' ? '导出订单列表' : '导出订单明细'"
      :visible='true'
      width='60%'
      @close='closeDialog'
    >
      <el-checkbox :indeterminate='isIndeterminate' v-model='checkAll' @change='handleCheckAllChange'>全选
      </el-checkbox>
      <div style='margin: 15px 0;'></div>
      <el-checkbox-group v-model='checkedList' @change='handleChange'>
        <el-checkbox v-for='tab in tabDataNew' :label='tab.tabStatus'
                     :key='tab.tabStatus'>{{ tab.tabName }}
        </el-checkbox>
      </el-checkbox-group>
      <span slot='footer'>
        <el-button size='mini' @click='closeDialog'>取 消</el-button>
        <el-button size='mini' type='primary' @click='confirm'>确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>

export default {
  name: 'exportDialog',
  props: {
    tabData: {
      type: Array,
      default: () => [],
    },
    exportType: {
      type: String,
      default: '1',
    },
  },
  data() {
    return {
      tabDataNew:JSON.parse(JSON.stringify(this.tabData)),
      checkAll: false,
      checkedList: [],
      isIndeterminate: true,
    };
  },
  created() {
    this.tabDataNew = this.tabData.filter(item=>item.tabCount != 'qualificationRemindCount')
  },
  watch: {
    'formModel.status'(newVal, oldVal) {
      console.log(newVal, oldVal)
    }
  },
  methods: {
    closeDialog() {
      this.$emit('update:exportDialogVisible', false)
    },
    handleCheckAllChange(val) {
      if (val) {
        this.checkedList = this.tabDataNew.map(item => item.tabStatus)
      } else {
        this.checkedList = []
      }
      this.isIndeterminate = false
    },
    handleChange(value) {
      const len = value.length
      this.checkAll = len === this.tabDataNew.length
      this.isIndeterminate = len > 0 && len < this.tabDataNew.length
    },
    confirm() {
      if (this.checkedList.length > 0) {
        this.$emit('confirm', this.checkedList)
        this.closeDialog()
      } else {
        this.$message.warning('请勾选需要导出的订单状态!')
      }
    }
  }
}
</script>
<style lang='scss' scoped>
</style>
