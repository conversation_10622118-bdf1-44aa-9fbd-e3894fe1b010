<template>
  <div>
    <div class="main-box">
      <div class="Fsearch">
        <el-row
          type="flex"
          align="middle"
          justify="space-between"
          class="my-row"
        >
          <el-row
            type="flex"
            align="middle"
          >
            <span class="sign" />
            <div class="searchMsg">
              查询条件
            </div>
          </el-row>
        </el-row>
      </div>
      <!--      <div class="con-title explain-search">-->
      <!--        <el-row type="primary">-->
      <!--          <span class="line"></span>-->
      <!--          <span>查询条件</span>-->
      <!--          <div class="search-btn">-->
      <!--          </div>-->
      <!--        </el-row>-->
      <!--      </div>-->
      <div
        class="explain-search searchMy"
        style="padding: 10px 20px 10px;border-bottom: 1px solid #f0f2f5;"
      >
        <el-form
          ref="ruleForm"
          size="small"
          :inline="true"
        >
          <el-form-item>
            <span
              class="search-title"
            >区域</span>
            <el-select
              v-model.trim="searchData.branchCode"
              placeholder="请选择"
              clearable
            >
              <el-option
                v-for="(item, index) in regionasList"
                :key="index"
                :label="item.branchName"
                :value="item.branchCode"
              />
            </el-select>
          </el-form-item>
          <el-form-item class="my-label">
            <span
              class="search-title"
            >页面展示</span>
            <el-select
              v-model.trim="searchData.showPage"
              placeholder="请选择"
              clearable
            >
              <el-option
                label="全部"
                value
              />
              <el-option
                label="店铺首页"
                :value="1"
              />
              <!-- <el-option label="新品上架" :value="2"></el-option> -->
              <!-- <el-option label="精品推荐页" :value="3"></el-option> -->
            </el-select>
          </el-form-item>
          <el-form-item
            class="my-label my-label3"
          >
            <span
              class="search-title"
            >状态</span>
            <el-select
              v-model.trim="searchData.status"
              placeholder="请选择"
              clearable
            >
              <el-option
                label="全部"
                value
              />
              <el-option
                label="启用"
                :value="1"
              />
              <el-option
                label="停用"
                :value="0"
              />
            </el-select>
          </el-form-item>
          <el-form-item
            class="my-label"
            prop="startTime"
          >
            <span
              class="search-title"
            >开始时间</span>
            <div style="display: table-cell; line-height: 24px">
              <el-date-picker
                v-model.trim="searchData.startTime"
                size="small"
                popper-class="install-contr-cell-class"
                range-separator="至"
                format="yyyy-MM-dd HH:mm:ss"
                value-format="yyyy-MM-dd HH:mm:ss"
                type="datetimerange"
                :default-time="['00:00:00', '23:59:59']"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                prefix-icon="el-icon-date"
              />
            </div>
          </el-form-item>
          <el-form-item
            class="my-label"
            prop="endTime"
          >
            <span
              class="search-title"
            >结束时间</span>
            <div style="display: table-cell; line-height: 24px">
              <el-date-picker
                v-model.trim="searchData.endTime"
                size="small"
                popper-class="install-contr-cell-class"
                range-separator="至"
                format="yyyy-MM-dd HH:mm:ss"
                value-format="yyyy-MM-dd HH:mm:ss"
                placeholder="选择日期时间"
                type="datetimerange"
                :default-time="['00:00:00', '23:59:59']"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                prefix-icon="el-icon-date"
              />
            </div>
          </el-form-item>
          <el-form-item
            class="my-label"
            prop="opTime"
          >
            <span
              class="search-title"
            >操作时间</span>
            <div style="display: table-cell; line-height: 24px">
              <el-date-picker
                v-model.trim="searchData.opTime"
                size="small"
                popper-class="install-contr-cell-class"
                range-separator="至"
                format="yyyy-MM-dd HH:mm:ss"
                value-format="yyyy-MM-dd HH:mm:ss"
                placeholder="选择日期时间"
                type="datetimerange"
                :default-time="['00:00:00', '23:59:59']"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                prefix-icon="el-icon-date"
              />
            </div>
          </el-form-item>
        </el-form>
        <el-row style="text-align: right">
          <el-button
            size="small"
            @click="resetForm('ruleForm')"
          >
            重置
          </el-button>
          <el-button
            size="small"
            type="primary"
            @click="getList('search')"
          >
            查询
          </el-button>
        </el-row>
      </div>
      <div class="list-box">
        <div
          class="con-title explain-search Fsearch"
          style="padding: 5px 20px 0"
        >
          <el-row
              type="flex"
              align="middle"
              justify="space-between"
          >
            <el-row
                type="flex"
                align="middle"
            >
              <span class="sign" />
              <div class="searchMsg">
                广告基本信息
                <el-tooltip
                    class="item"
                    effect="dark"
                    placement="top"
                >
                  <template #content>
                    <br>1、可以设置全国所有区域通用的广告，也可以设置单独区域的
                    <br>2、药帮忙平台客户可见全国+当前区域的广告，必须为启用状态且在生效时间内
                    <br>3、按照排序号正序轮播，如多个广告排序号一致，则按照创建时间正序排序
                  </template>
                  <p class="span-tip">
                    ?
                  </p>
                </el-tooltip>
              </div>
            </el-row>
            <el-button
                v-permission="['shop_ad_add']"
                class="xyy-blue"
                type="primary"
                size="small"
                @click="addFloor"
            >
              新增广告
            </el-button>
          </el-row>
        </div>
        <div style="padding: 10px 20px">
          <div class="customer-tabs">
            <el-table
              ref="goodTable"
              v-loading="laodingBoole"
              :data="tableData.list"
              stripe
              style="width: 100%"
              :header-cell-style="{ background: '#F9F9F9', color: '#666666' }"
            >
              <el-table-column
                prop="areaNames"
                label="区域"
                width="120"
                show-overflow-tooltip
              />
              <el-table-column
                label="图片预览"
                width="120"
              >
                <template slot-scope="scope">
                  <el-image
                    class="imgInfo"
                    :src="scope.row.imageAddress"
                    :preview-src-list="[scope.row.imageAddress]"
                    @click.prevent
                  />
                </template>
              </el-table-column>
              <el-table-column label="状态">
                <template slot-scope="scope">
                  <span v-if="scope.row.status == 1">启用</span>
                  <span v-else>停用</span>
                </template>
              </el-table-column>
              <el-table-column
                prop="sort"
                label="排序"
              />
              <el-table-column label="展示页面">
                <template slot-scope="scope">
                  <span v-if="scope.row.showPage == 1">店铺首页</span>
                  <!-- <span v-else-if="scope.row.showPage == 2">新品上架</span> -->
                  <!-- <span v-else-if="scope.row.showPage == 3">精品推荐页</span> -->
                  <span v-else>全部</span>
                </template>
              </el-table-column>
              <el-table-column
                prop="startTime"
                label="开始时间"
                width="160"
              />
              <el-table-column
                prop="endTime"
                label="结束时间"
                width="160"
              />
              <el-table-column
                prop="updateTime"
                label="操作时间"
                width="160"
              />
              <el-table-column
                prop="updateUser"
                label="操作人"
                width="120"
              />
              <el-table-column
                fixed="right"
                label="操作"
                width="120"
                class-name="operation-box"
              >
                <template slot-scope="scope">
                  <el-button
                    v-permission="['shop_ad_edit']"
                    type="text"
                    size="small"
                    @click="edit(scope.row)"
                  >
                    编辑
                  </el-button>
                  <el-button
                    v-permission="['shop_ad_enable']"
                    v-if="scope.row.status == 0"
                    type="text"
                    size="small"
                    @click="enable(scope.row)"
                  >
                    启用
                  </el-button>
                  <el-button
                    v-permission="['shop_ad_disable']"
                    v-else
                    type="text"
                    size="small"
                    @click="disable(scope.row)"
                  >
                    停用
                  </el-button>
                </template>
              </el-table-column>
              <template slot="empty">
                <div class="noData">
                  <p class="img-box">
                    <img
                      src="@/assets/image/marketing/noneImg.png"
                      alt
                    >
                  </p>
                  <p>暂无数据</p>
                </div>
              </template>
            </el-table>
            <div class="explain-pag">
              <Pagination
                v-show="tableData.total > 0"
                :total="tableData.total"
                :page.sync="pageData.page"
                :limit.sync="pageData.rows"
                @pagination="getList"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
    <el-dialog
      :title="advertisingTitle"
      :visible.sync="changeDialog"
      class="my-dialog"
      :modal-append-to-body="false"
      :close-on-click-modal="false"
      width="62%"
    >
      <div class="explain-table dialogBox">
        <div style="max-height: 400px;overflow-y: auto;margin-top:10px">
          <el-form
            ref="form"
            size="small"
            :inline="true"
            :model="form"
            :rules="rules"
            label-width="130px"
          >
            <el-form-item
              class="my-label2"
              label="区域:"
              prop="areaCodes"
            >
              <el-select
                v-model.trim="form.areaCodes"
                placeholder="请选择"
                class="my-lable2-item"
                multiple
                clearable
                @change="changeSelect"
              >
                <el-option
                  v-for="(item, index) in regionasList"
                  :key="index"
                  :label="item.branchName"
                  :value="item.branchCode"
                />
              </el-select>
            </el-form-item>
            <el-form-item
              class="my-label2"
              label="展示页面:"
              prop="showPage"
            >
              <el-select
                v-model.trim="form.showPage"
                placeholder="请选择"
                class="my-lable2-item"
                clearable
              >
                <el-option
                  label="店铺首页"
                  :value="1"
                />
                <!-- <el-option label="新品上架" :value="2"></el-option> -->
                <!-- <el-option label="精品推荐页" :value="3"></el-option> -->
              </el-select>
            </el-form-item>
            <el-form-item
              label="广告图片:"
              prop="viewImage"
              class="my-label2"
            >
              <el-upload
                class="avatar-uploader my-lable2-item"
                action
                :http-request="uploadImg"
                :before-upload="beforeAvatarUpload"
                :show-file-list="false"
                :on-remove="handleRemove"
                style="height: 64px;"
              >
                <img
                  v-if="form.viewImage"
                  :src="form.viewImage"
                  class="avatar"
                >
                <div style="margin-top:5px">
                  <i class="el-icon-plus">
                    <div class="avatar-uploader-icon">上传图片</div>
                  </i>
                </div>
              </el-upload>
              <span
                class="my-lable2-info"
                style="color: #F56C6C; font-family: PingFangSC, PingFangSC-Medium;"
              >格式支持jpg，gif，png，jpeg，图片尺寸为：1200*450，图片大小不超过5M</span>
            </el-form-item>
            <el-form-item
              class="my-label2"
              label="PC页面链接:"
            >
              <el-input
                v-model.trim="form.skipLink"
                class="my-lable2-item"
                clearable
              />
              <span class="my-lable2-info">请以http://或https://开头</span>
            </el-form-item>
            <el-form-item
              class="my-label2"
              label="APP页面链接:"
            >
              <el-input
                v-model.trim="form.appLink"
                class="my-lable2-item"
                clearable
              />
              <span class="my-lable2-info">请以ybmpage://commonh5activity开头</span>
            </el-form-item>
            <el-form-item
              class="my-label2"
              label="排序值:"
              prop="sort"
            >
              <el-input
                v-model.trim="form.sort"
                class="my-lable2-item"
                clearable
              />
              <span class="my-lable2-info">按照数字从小到大排列，数字越小，越优先轮播展示</span>
            </el-form-item>
            <el-form-item
              class="my-label2"
              prop="startTime"
              label="开始时间:"
            >
              <el-date-picker
                v-model.trim="form.startTime"
                size="small"
                popper-class="install-contr-cell-class"
                type="datetime"
                value-format="yyyy-MM-dd HH:mm:ss"
                placeholder="选择日期时间"
                prefix-icon="el-icon-date"
                class="my-lable2-item"
              />
              <span class="my-lable2-info">具体到年月日时分秒</span>
            </el-form-item>
            <el-form-item
              class="my-label2"
              prop="endTime"
              label="结束时间:"
            >
              <el-date-picker
                v-model.trim="form.endTime"
                size="small"
                popper-class="install-contr-cell-class"
                type="datetime"
                value-format="yyyy-MM-dd HH:mm:ss"
                placeholder="选择日期时间"
                prefix-icon="el-icon-date"
                class="my-lable2-item"
              />
              <span class="my-lable2-info">具体到年月日时分秒</span>
            </el-form-item>
            <el-form-item
              class="my-label2"
              label="状态:"
              prop="status"
            >
              <el-radio-group v-model="form.status">
                <el-radio :label="1">
                  启用
                </el-radio>
                <el-radio :label="0">
                  停用
                </el-radio>
              </el-radio-group>
            </el-form-item>
          </el-form>
        </div>
      </div>
      <div
        slot="footer"
        class="dialog-footer"
      >
        <el-button
          size="small"
          @click="cancelDialog"
        >
          取 消
        </el-button>
        <el-button
          type="primary"
          class="xyy-blue"
          size="small"
          @click="determineDialog('form')"
        >
          确定
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>
<style>
@import '../../assets/css/changeElement.scss';
</style>
<script>
import Pagination from '@/components/Pagination';
import utils from '@/utils/filter';
import info from '@/components/info/info';
import {
  branchsList,
  getAdvertiseList,
  getAdvertiseAdd,
  getAdvertiseEdit,
  stopUsingAdvertise,
  enableAdvertise,
  getHostName,
} from '@/api/storeManagement/index';
import { uploadFile } from '@/api/qual/index';

export default {
  name: 'AdvertisingSetList',
  components: { Pagination, info },
  data() {
    return {
      searchData: {
        branchCode: '', // 区域
        showPage: '', // 页面展示
        status: '', // 状态
        startTime: [], // 开始时间
        endTime: [], // 结束时间
        opTime: [], // 操作时间
        sort: 'asc', // 排序
      },
      form: {
        areaCodes: [],
        showPage: '',
        viewImage: '',
        skipLink: '',
        appLink: '',
        sort: '',
        startTime: '',
        endTime: '',
        status: 1,
      },
      tableData: {
        total: 0,
        list: [],
      },
      pageData: {
        rows: 10,
        page: 1,
      },
      laodingBoole: false,
      sellerAry: [],
      checkListAry: [],
      detailData: {},
      visible: false,
      titleInfo: '广告设置',
      changeDialog: false,
      regionasList: [],
      rules: {
        areaCodes: [
          { required: true, message: '区域为必填项', trigger: 'blur' },
        ],
        showPage: [
          { required: true, message: '展示页面为必填项', trigger: 'blur' },
        ],
        viewImage: [
          { required: true, message: '广告图片为必填项', trigger: 'blur' },
        ],
        sort: [
          { required: true, message: '排序值为必填项', trigger: 'blur' },
          { pattern: /^[0-9]*$/, message: '只允许输入数字', trigger: 'blur' },
        ],
        startTime: [
          { required: true, message: '开始时间为必填项', trigger: 'blur' },
        ],
        endTime: [
          { required: true, message: '结束时间为必填项', trigger: 'blur' },
        ],
        status: [
          { required: true, message: '状态为必填项', trigger: 'blur' },
        ],
      },
      editOrAdd: false,
      hostName: '',
      advertisingTitle: '新增广告',
      advertisingId: '',
    };
  },
  created() {
    getHostName().then((res) => {
      if (res.hostName) {
        this.hostName = res.hostName;
      }
    });
    this.queryRegional();
    // 获取列表数据
    this.getList();
  },
  methods: {
    infodata() {
      return [
        { title: '可以设置全国所有区域通用的广告，也可以设置单独区域的' },
        { title: '药帮忙平台客户可见全国+当前区域的广告，必须为启用状态且在生效时间内' },
        { title: '按照排序号正序轮播，如多个广告排序号一致，则按照创建时间正序排序' },
      ];
    },
    // 获取列表数据
    getList(from) {
      const that = this;
      if (from == 'search') {
        this.pageData.page = 1;
      }
      this.laodingBoole = true;

      const param = {
        branchCode: this.searchData.branchCode,
        showPage: this.searchData.showPage,
        status: this.searchData.status,
        sort: 'asc',
        ...this.pageData,
      };
      if (this.searchData.startTime) {
        param.startGtTime = this.searchData.startTime[0];
        param.startLtTime = this.searchData.startTime[1];
      }
      if (this.searchData.endTime) {
        param.endGtTime = this.searchData.endTime[0];
        param.endLtTime = this.searchData.endTime[1];
      }
      if (this.searchData.opTime) {
        param.opGtTime = this.searchData.opTime[0];
        param.opLtTime = this.searchData.opTime[1];
      }
      getAdvertiseList(param).then((res) => {
        if (res.code == 0) {
          this.laodingBoole = false;
          if (res.result) {
            that.tableData.list = res.result.list;
            let updateTime = {};
            let startTime = {};
            let endTime = {};
            let imageAddress = {};
            let areaNames = {};
            this.tableData.list.forEach((item, index) => {
              updateTime = {};
              startTime = {};
              endTime = {};
              imageAddress = {};
              areaNames = {};
              if (item.updateTime == null) {
                updateTime = '-';
              } else {
                updateTime = utils.dataTime(
                  item.updateTime,
                  'yy-mm-dd HH:ss:nn',
                );
              }
              if (item.startTime == null) {
                startTime = '-';
              } else {
                startTime = utils.dataTime(
                  item.startTime,
                  'yy-mm-dd HH:ss:nn',
                );
              }
              if (item.endTime == null) {
                endTime = '-';
              } else {
                endTime = utils.dataTime(
                  item.endTime,
                  'yy-mm-dd HH:ss:nn',
                );
              }
              if (item.imageAddress != null) {
                imageAddress = this.hostName + item.imageAddress;
              }
              if (item.areaNames && item.areaNames.length > 0) {
                areaNames = item.areaNames.join(',');
              }
              this.tableData.list[index] = Object.assign({}, this.tableData.list[index], {
                updateTime,
                startTime,
                endTime,
                imageAddress,
                areaNames,
              });
            });
          } else {
            this.tableData.list = [];
          }
          this.tableData.total = res.result.total;
        } else {
          this.laodingBoole = false;
          this.$message({
            message: res.msg,
            type: 'error',
          });
        }
      }).catch(() => {});
    },
    queryRegional() {
      branchsList().then((res) => {
        this.regionasList = res.result;
      });
    },
    changeSelect(val) {
      if (val.length > 1 && val.indexOf('XS000000') > -1) {
        const last = val.length - 1;
        if (val[last] === 'XS000000') {
          this.form.areaCodes = ['XS000000'];
        } else {
          const index = val.indexOf('XS000000');
          this.form.areaCodes.splice(index, 1);
        }
      }
    },
    handleRemove(file, fileList) {
      console.log(file, fileList);
    },
    uploadImg(file) {
      uploadFile(file).then((res) => {
        if (res.code === '200') {
          this.form.viewImage = `${this.hostName}/${res.data}`;
        } else {
          this.form.viewImage = '';
        }
      });
    },
    beforeAvatarUpload(file) {
      if (!file) {
        this.$message.error('请上传图片');
        return false;
      }
      const isJPG = file.type === 'image/jpeg'
        || file.type === 'image/png'
        || file.type === 'image/bmp'
        || file.type === 'image/jpg'
        || file.type === 'image/gif';
      const isLt5M = file.size / 1024 / 1024 < 5;

      if (!isJPG || !isLt5M) {
        this.$message.error('图片不满足上传要求，请重新上传');
        return false;
      }
      return isJPG && isLt5M;
    },
    // 添加楼层
    addFloor() {
      this.form = {
        areaCodes: [],
        showPage: '',
        viewImage: '',
        skipLink: '',
        appLink: '',
        sort: '',
        startTime: '', // 开始时间
        endTime: '', // 结束时间
        status: 1,
      },
      this.advertisingId = '';
      this.advertisingTitle = '新增广告';
      this.editOrAdd = false;
      this.changeDialog = true;
    },
    // 关闭弹窗
    cancelDialog(val) {
      this.changeDialog = false;
    },
    // 编辑
    edit(val) {
      this.form = {
        areaCodes: val.areaCodes,
        showPage: val.showPage,
        viewImage: val.imageAddress,
        skipLink: val.skipLink,
        appLink: val.appLink,
        sort: val.sort,
        startTime: val.startTime, // 开始时间
        endTime: val.endTime, // 结束时间
        status: val.status,
      };
      this.advertisingId = val.id;
      this.advertisingTitle = '编辑广告';
      this.editOrAdd = true;
      this.changeDialog = true;
    },
    // 点击确定添加广告
    determineDialog(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          const that = this;
          const param = { ...this.form };
          param.areaCodes = this.form.areaCodes.join(',');
          param.viewImage = this.form.viewImage.split(this.hostName)[1];

          if (this.editOrAdd) {
            if (this.advertisingId) {
              param.id = this.advertisingId;
            }
            getAdvertiseEdit(param).then((res) => {
              if (res.code == 0) {
                this.getList();
              } else {
                this.$message({
                  message: res.msg,
                  type: 'error',
                });
              }
            }).catch(() => {});
          } else {
            getAdvertiseAdd(param).then((res) => {
              if (res.code == 0) {
                this.getList();
              } else {
                this.$message({
                  message: res.msg,
                  type: 'error',
                });
              }
            }).catch(() => {});
          }
          this.changeDialog = false;
        } else {
          return false;
        }
      });
    },
    // 启用
    enable(val) {
      enableAdvertise({ id: val.id }).then((res) => {
        if (res.code == 0) {
          this.getList();
        } else {
          this.$message({
            message: res.msg,
            type: 'error',
          });
        }
      }).catch(() => {});
    },
    // 停用
    disable(val) {
      stopUsingAdvertise({ id: val.id }).then((res) => {
        if (res.code == 0) {
          this.getList();
        } else {
          this.$message({
            message: res.msg,
            type: 'error',
          });
        }
      }).catch(() => {});
    },
    // 重置列表数据
    resetForm() {
      this.searchData = {
        branchCode: '', // 区域
        showPage: '', // 页面展示
        status: '', // 状态
        startTime: [], // 开始时间
        endTime: [], // 结束时间
        opTime: [], // 操作时间
        sort: 'asc', // 排序
      };
      this.pageData = {
        rows: 10,
        page: 1,
      };
      this.getList();
    },
  },
};
</script>

<style lang="scss" scoped>
// @import '../../../assets/css/market';
.con-inner {
  padding-top: 15px;
  padding-left: 23px;
  margin-right: 17px;
  padding-bottom: 10px;
  border-bottom: 1px solid #efefef;
  div {
    display: inline-block;
  }
  .img {
    width: 92px;
    height: 92px;
    img {
      width: 100%;
      height: 100%;
    }
  }
  .text {
    padding-left: 20px;
    vertical-align: top;
    h3 {
      font-size: 14px;
      color: #000000;
      padding: 0;
      margin: 0;
    }
    p {
      padding: 0;
      margin: 0;
      font-size: 12px;
      color: #333333;
      padding-top: 10px;
    }
  }
  .btn {
    float: right;
    padding-top: 26px;
    button {
      width: 100px;
      height: 30px;
      text-align: center;
      line-height: 30px;
      padding: 0;
      background: rgba(65, 131, 213, 1);
      border-color: rgba(65, 131, 213, 1);
      border-radius: 4px;
      font-size: 14px;
    }
    a {
      color: #ffffff;
      text-decoration: none;
    }
    .router-link-active {
      color: #ffffff;
      text-decoration: none;
    }
  }
}
.pag-info {
  width: 500px;
}
.main-box {
  .list-box {
    .customer-tabs {
      .el-button + .el-button {
        margin-left: 0px;
      }
    }
  }
}
.search-btn {
  float: right;
}
.imgInfo {
  width: 40px;
  height: 15px;
  margin-right: 16px;
}
.operation-box {
  width: auto;
  white-space: nowrap;
  letter-spacing: 0;
  margin: 0 -10px;
  .el-button {
    position: relative;
    margin: 0 10px;
  }
  .el-button::before {
    position: absolute;
    // top: 14px;
    right: -6px;
    content: '';
    display: block;
    width: 1px;
    height: 12px;
    background: #dcdfe6;
  }
  .el-button:first-child {
    margin-left: 0;
  }
  .el-button:last-child {
    margin-right: 0;
  }
  .el-button:last-child::before {
    display: none;
  }
}


::v-deep   .edit-btn {
  display: flex;
  justify-content: center;
  align-items: center;

  i.el-icon-edit-outline:before {
    font-size: 16px;
  }
}

.searchMy ::v-deep  .el-form-item__label {
  margin-left: 20px;
  padding: 0;
}
.searchMy ::v-deep  .el-input__inner {
  border-radius: 0 4px 4px 0;
}
.searchMy ::v-deep  .el-date-editor{
  width: 100%;
}
.search-title {
  display: table-cell;
  padding: 0 10px;
  text-align: center;
  border: 1px solid #dcdfe6;
  height: 30px;
  line-height: 30px;
  vertical-align: middle;
  border-right: none;
  border-radius: 4px 0 0 4px;
  color: #333333;
  white-space: nowrap;
}
.searchMy ::v-deep  .el-select {
  display: table-cell;
  width: 100%;
}
.searchMy ::v-deep  .el-form-item__content{
  width: 100%;
}
.searchMy ::v-deep  .el-form-item--small.el-form-item{
  margin-bottom: 10px;
  width: 24%;
}
.searchMy ::v-deep  .el-form-item--small .el-form-item__content{
  line-height: 30px;
}
::v-deep  .el-input-group__prepend {
  background: none;
  color: #333333;
  padding: 0 10px;
}
.searchMy ::v-deep  .el-date-editor{
  width: 100%;
}
.Fsearch {
  padding: 15px 20px 10px;
  font-weight: 500;
  .searchMsg {
    font-weight: 700;
    width: 200px;
  }
  .sign {
    display: inline-table;
    width: 3px;
    height: 13px;
    background: linear-gradient(360deg, #1d69c4 1%, #8bbdfc 99%);
    border-radius: 2px;
    margin-right: 8px;
  }
}
.span-tip{
  display: inline-block;
  width: 20px;
  height: 20px;
  font-size: 14px;
  border: 1px solid #4183d5;
  color: #4183d5;
  text-align: center;
  line-height: 20px;
  border-radius: 50%;
  margin-left: 5px;
}
.dialogBox ::v-deep  .el-form-item--small.el-form-item{
  margin-bottom: 10px;
  width: 90%;
}

::v-deep  .my-dialog .user {
  .el-tree-node__content > .el-tree-node__expand-icon {
    padding: 0px;
    font-size: 0;
  }
}
::v-deep  .explain-table {
  padding: 0 6px 0;
}

::v-deep  .avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  background: rgba(0, 0, 0, 0.02);
  width: 64px;
  height: 64px;
  line-height: 64px;
}
::v-deep  .avatar-uploader .el-upload:hover {
  border-color: #409eff;
}
::v-deep  .avatar-uploader .el-upload__tip {
  margin-top: 0;
  color: #999999;
  font-size: 12px;
}
.avatar-uploader-icon {
  opacity: 1;
  font-size: 12px;
  font-family: PingFangSC, PingFangSC-Regular;
  font-weight: 400;
  text-align: left;
  color: rgba(0, 0, 0, 0.65);
  line-height: 14px;
}
.avatar {
  width: 64px;
  height: 64px;
  display: block;
}
::v-deep  .avatar-uploader .el-upload-list--picture-card .el-upload-list__item {
  width: 64px;
  height: 64px;
}
</style>
