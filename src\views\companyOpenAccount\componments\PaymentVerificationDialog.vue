<template>
  <el-dialog
    title="打款验证"
    :visible.sync="dialogVisible"
    width="688px"
    :before-close="handleClose"
  >
    <p>请按照联系人手机号（尾号：{{ base.phone ? base.phone.substr(7) : '' }}）收到的短信提示，输入打款金额和鉴权序号请在收到短信的48小时内完成打款验证，超时短信将失效</p>
    <el-form
      ref="form"
      :model="formModel"
      :rules="formRules"
      label-width="170px"
    >
      <el-form-item
        label="打款金额（元）:"
        prop="money"
      >
        <el-input
          v-model="formModel.money"
          placeholder="请输入银行账号收到的转账金额"
          maxlength="10"
          oninput="value=value.toString().match(/^\d+(?:\.\d{0,2})?/)"
          style="width: 250px;"
        />
      </el-form-item>
      <el-form-item
        label="鉴权序号:"
        prop="authNo"
      >
        <el-input
          v-model="formModel.authNo"
          placeholder="请输入短信提示的鉴权序号"
          maxlength="20"
          style="width: 250px;"
        />
      </el-form-item>
    </el-form>
    <div slot="footer">
      <el-button
        size="small"
        @click="handleClose"
      >
        暂不验证
      </el-button>
      <el-button
        type="primary"
        size="small"
        :loading="loading"
        @click="paymentVerification"
      >
        执行打款验证
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import { paymentAuth } from '@/api/companyOpenAccount';

export default {
  name: 'PaymentVerificationDialog',
  props: {
    base: {
      type: Object,
      default: () => {},
    },
    authType: {
      type: Number,
      default: 1,
    },
  },
  data() {
    const validateMoney = (rule, value, callback) => {
      if (!value) {
        callback(new Error('请输入银行账号收到的转账金额'));
      } else {
        callback();
      }
    };
    return {
      loading: false,
      dialogVisible: true,
      formModel: {
        money: '',
        authNo: '',
      },
      formRules: {
        money: [
          { required: true, trigger: 'blur', validator: validateMoney },
        ],
        authNo: [
          { required: true, trigger: 'blur', message: '请输入短信提示的鉴权序号' },
        ],
      },
    };
  },
  methods: {
    paymentVerification() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          const params = {
            authType: this.authType,
            money: this.formModel.money,
            authNo: this.formModel.authNo,
          };
          console.log(3333333, params);
          this.loading = true;
          paymentAuth(params).then((res) => {
            this.loading = false;
            if (res.code === 0) {
              this.$message.success('打款验证成功');
              this.handleClose();
              this.$emit('updateInfo');
            } else {
              let str = res.msg;
              if (res.code === 2) {
                str = '银行卡审核失败：验证已失效，请重新发起';
              }
              this.$confirm(str, `${res.code === 1 ? '打款验证失败提醒' : '银行卡审核失败'}`, {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
              })
                .then(() => {
                  if (res.code === 2) {
                    this.handleClose();
                    this.$emit('updateInfo');
                  }
                })
                .catch(() => {});
            }
          });
        }
      });
    },
    handleClose() {
      this.$emit('update:paymentVerificationDialogVis', false);
    },
  },
};
</script>
