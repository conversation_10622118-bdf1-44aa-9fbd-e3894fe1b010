import request from '@/utils/request';

// 账号列表
export function apiListSubAccount(params) {
    return request({
      url: '/subAccount/listSubAccount',
      method: 'get',
      params: params,
    });
}
// 查询所有角色
export function apiQueryAllRoles(params) {
    return request({
      url: '/supplier/role/queryAllRoles',
      method: 'get',
      params: params,
    });
}
// 新建账号
export function apiSaveAccount(params) {
    return request({
        url: '/subAccount/saveAccount',
        method: 'post',
        params: params,
    });
}
// 编辑账号
export function apiUpdateAccount(params) {
    return request({
        url: '/subAccount/updateAccount',
        method: 'post',
        params: params,
    });
}
// 账号详情
export function apiQueryAccountInfoByUserId(params) {
    return request({
      url: '/subAccount/queryAccountInfoByUserId',
      method: 'get',
      params: params,
    });
}
// 账号启用/停用
export function apiSwitchAccountStatus(params) {
    return request({
        url: '/subAccount/switchAccountStatus',
        method: 'post',
        params: params,
    });
}
// 删除账号
export function apiDeleteAccount(params) {
    return request({
        url: '/subAccount/deleteAccount',
        method: 'post',
        params: params,
    });
}
// 重置密码
export function apiResetPassword(params) {
    return request({
        url: '/subAccount/resetPassword',
        method: 'post',
        params: params,
    });
}
// 角色列表
export function apiListRole(params) {
    return request({
      url: '/supplier/role/listRole',
      method: 'get',
      params: params,
    });
}
// 角色详情
export function apiQueryRoleInfoByRoleId(params) {
    return request({
      url: '/supplier/role/queryRoleInfoByRoleId',
      method: 'get',
      params: params,
    });
}
// 新建角色
export function apiSaveRole(params) {
    return request({
        url: '/supplier/role/saveRole',
        method: 'post',
        data: params,
    });
}
// 编辑角色
export function apiUpdateRole(params) {
    return request({
        url: '/supplier/role/updateRole',
        method: 'post',
        data: params,
    });
}
// 删除角色
export function apiDeleteRole(params) {
    return request({
        url: '/supplier/role/deleteRole',
        method: 'post',
        params: params,
    });
}
// 查询角色绑定账号
export function apiQueryAccountListByRoleId(params) {
    return request({
        url: '/supplier/role/queryAccountListByRoleId',
        method: 'get',
        params: params,
    });
}
// 编辑角色查询所有菜单（不包含用户管理）
export function getMenuList(params) {
    return request({
      url: '/supplier/role/menuList',
      method: 'get',
      data: params,
    });
  }
  