<template>
  <div class="serviceQuality" v-if="shopConfig.indexShow">
    <div class="task_title">
      <span @click="goJump" style="cursor: pointer;">服务质量（近7天）</span>
    </div>
    <el-row class="infoItem">
      <el-col :span="12">
        <span class="todoItem bg_orange" @click="goJump(0)">48h发货率：{{dataObj.sendNum||0}}%</span>
      </el-col>
      <!-- <el-col :span="12">
        <span class="todoItem bg_red" @click="goJump(1)">商家原因退款率：{{dataObj.refundNum||0}}%</span>
      </el-col> -->
      <el-col :span="12">
        <span class="todoItem bg_red" @click="goJump(2)">商家原因退货率：{{dataObj.refundOrderNum||0}}%</span>
      </el-col>
    </el-row>
    <el-row class="infoItem">

      <el-col :span="12">
        <span class="todoItem bg_green" @click="goJump(3)">客服在线响应率：{{dataObj.respondCt||0}}%</span>
      </el-col>
      <el-col :span="12">
        <el-tooltip placement="top">
          <div slot="content">
            <p>发票售后率=已处理发票售后订单数量/总订单数量</p>
            <p>已处理发票售后状态：商家已处理、商家已补发</p>
            <p>订单状态：待审核、待发货、配送中、已完成、已退款</p>
          </div>
          <span class="todoItem bg_red" @click="goJump(4)">发票售后率：{{dataObj.invoiceAsNum||0}}%</span>
        </el-tooltip>
      </el-col>
    </el-row>
	  <el-row class="infoItem">
      <el-col :span="12">
        <el-tooltip placement="top">
          <div slot="content">
            <p>资质售后率=已处理资质售后订单数量/总订单数量</p>
            <p>已处理资质售后状态：商家已处理、商家已补发</p>
            <p>订单状态：待审核、待发货、配送中、已完成、已退款</p>
          </div>
          <span class="todoItem bg_green" @click="goJump(5)">资质售后率：{{dataObj.credentialAsNum||0}}%</span>
        </el-tooltip>
      </el-col>
      <el-col :span="12">
        <span class="todoItem bg_red" @click="goJump(2)">客服响应时长：{{dataObj.firstRespondCt||0}}分钟</span>
      </el-col>
    </el-row>
	  <el-row class="infoItem">
      <el-col :span="12">
        <span class="todoItem bg_red" @click="jumpPage(1)">商品首营资料上传率：{{dataObj.qualificationNum||0}}%</span>
      </el-col>
      <el-col :span="12">
        <span class="todoItem bg_red" @click="jumpPage(2)">药检报告订单覆盖率：{{dataObj.drugReportNum||0}}%</span>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { apiQueryServeQuality } from '@/api/home'
import { mapState } from 'vuex';
import {actionTracking} from "@/track/eventTracking";

export default {
  name: 'ServiceQualitySeven',

  data() {
    return {
      dataObj: {}
    }
  },
  computed: {
    ...mapState('app', ['shopConfig'])
  },
  created() {
    this.queryData()
  },
  methods: {
    async queryData() {
      const res = await apiQueryServeQuality()
      if (res && res.code === 0) {
        this.dataObj = res.result || {}
      }
    },
    goJump(position) {
      actionTracking('service_quality_click', {
        service_quality : ["48send", "refund", "return", "response", "afterSale-invoice", "afterSale-qualtification"][position]
      })
      let sevenDate = []
      let startDate = ''
      let endDate = ''
      startDate = dayjs().add(-7, 'days').format('YYYY-MM-DD')
      endDate = dayjs().add(-1, 'days').format('YYYY-MM-DD')
      sevenDate = [startDate, endDate]
      sessionStorage.setItem('sevenDate', JSON.stringify(sevenDate))
      window.openTab('/serviceQuality')
    },
    jumpPage(type) {
      const path = type == 1 ? '/qualificationManage' : '/drugTestResultManage'
      window.openTab(path)
    }
  }
}
</script>

<style scoped lang="scss">
.serviceQuality {
  background-color: #fff;
  padding: 16px;
  margin-bottom: 16px;
  .task_title {
    font-size: 16px;
    line-height: 22px;
    color: #333;
    font-weight: 500;
    font-family: PingFangSC, PingFangSC-Medium;
    margin-bottom: 22px;
  }
  .infoItem {
    margin-bottom: 20px;
  }
  .todoItem {
    cursor: pointer;
    padding-right: 4px;
    font-size: 14px;
    font-family: PingFangSC, PingFangSC-Regular;
    font-weight: 400;
    line-height: 20px;
  }
  .todoItem:before {
    content: '';
    display: inline-block;
    vertical-align: middle;
    width: 6px;
    height: 6px;
    border-radius: 50%;
    margin-right: 4px;
  }
  .todoItem.bg_red:before {
    background: red;
  }
  .todoItem.bg_orange:before {
    background: #f07810;
  }
  .todoItem.bg_green:before {
    background: #00a03e;
  }
}
</style>
