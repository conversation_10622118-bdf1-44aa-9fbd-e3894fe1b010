<template>
  <div class="qual-box">
    <div style="padding: 15px 20px 0">
      <el-button
        size="small"
        @click="$router.go(-1)"
      >
        返回当前企业信息
      </el-button>
    </div>
    <div class="qual-state">
      <div v-if="!isEdit">
        <span class="h">认证状态:</span>
        <span :class="stateClass">{{ stateText }}</span>
        <span
          class="t"
          v-html="stateTip"
        />
        <!--        <span>去上传</span>-->
      </div>
      <!--      <div v-show="allData.state != 1 && isBtnShow" class="color4">-->
      <!--        <span v-show="allData.state === 5 && isCheck" @click="editData"-->
      <!--          ><i class="line"></i> 修改信息</span-->
      <!--        >-->
      <!--        <span @click="getHistory"-->
      <!--          ><i class="line"></i>-->
      <!--          <img class="li-img" src="../../assets/image/marketing/lishi.png" alt="" /> 历史记录</span-->
      <!--        >-->
      <!--      </div>-->
      <!--      <span v-show="isEdit" class="t"-->
      <!--        >请修改需要更新的资质信息并提交。新修改的信息在审核通过前，仍可使用原资质继续开店，页面也将继续显示原资质信息。可在“历史记录”中查看此次修改记录的进展。</span-->
      <!--      >-->
    </div>
    <div class="serch">
      <el-row
        type="flex"
        align="middle"
      >
        <span class="sign" />
        <div>企业基本信息</div>
      </el-row>
    </div>
    <Basic
      ref="basic"
      :corporation-type="corporationType"
      :basic-data="allData"
      :is-detail="isDetail"
      :is-history="true"
      :qual-state-str="qualStateStr"
      :host-name="hostName"
      @formBasic="formBasic"
      @changeType="changeType"
    />
    <div class="serch">
      <el-row
        type="flex"
        align="middle"
      >
        <span class="sign" />
        <div>企业工商信息</div>
      </el-row>
    </div>
    <Cor
      v-if="changeShow"
      ref="cor"
      :company-qual-data="companyQualData"
      :host-name="hostName"
      :is-detail="isDetail"
      :qual-state-str="qualStateStr"
      :is-history="true"
      :corporation-qualifications="allData.checkCorporationQualifications"
      @formCor="formCor"
    />
    <div class="serch">
      <el-row
        type="flex"
        align="middle"
      >
        <span class="sign" />
        <div>企业经营范围及资质</div>
      </el-row>
    </div>
    <Qual
      v-if="changeShow"
      ref="qual"
      :qualification-data="qualificationData"
      :business-category="businessCategory"
      :host-name="hostName"
      :is-detail="isDetail"
      :qual-state-str="qualStateStr"
      :is-history="true"
      :checked-bussiness="allData.checkCorporationBusiness"
      :corporation-qualifications="allData.checkCorporationQualifications"
      @formQual="formQual"
      @setHeight="setHeight"
    />
    <div
      v-if="isDetail"
      class="footer"
    >
      <div class="statement-box">
        <el-checkbox
          :checked="statementIs"
          @change="statementIs = !statementIs"
        />
        <div style="padding-left: 10px">
          我声明，此页面所填写内容均真实有效，特别是经营地址为店铺最新可联系到的地址，同时可以作为行政机关和司法机关送达法律文件的地址。如果上述地址信息有误，愿意承担由此带来的平台处罚（处罚细则）、行政监管和司法诉讼风险。
        </div>
      </div>
      <div class="bot-box">
        <!--        <el-button size="medium" @click="submitData(1)" v-if="!qualStateStr || qualStateStr === 1"-->
        <!--          >暂存</el-button-->
        <!--        >-->
        <el-button
          type="primary"
          size="medium"
          @click="submitData(2)"
        >
          提交
        </el-button>
      </div>
    </div>
    <el-dialog
      title="修改记录"
      :visible.sync="dialogVisible"
      offset="5%"
      width="600px"
      class="his-dialog"
    >
      <div v-if="historyList.length > 0">
        <div
          v-for="(item, index) in historyList"
          :key="index"
          style="margin-bottom: 10px"
        >
          <div class="dis-div">
            <span>{{ item.createTime | formatDate }}</span>
            <span>{{ item.recordType | formatType }}</span>
            <span>{{ item.state | formatStatus }}</span>
          </div>
          <div
            v-if="item.remarks && item.state === 2"
            class="div-remarks"
          >
            {{ item.remarks }}
          </div>
        </div>
      </div>
      <div v-else>
        暂无历史记录
      </div>
    </el-dialog>
  </div>
</template>

<script>
// import $ from 'jquery'
import Basic from './basicComponent';
import Cor from './corComponent';
import Qual from './qualComponent';
import {
  getHostName,
  getCompanyHistoryInfo,
  getBusinessCategory,
  getQualificationInfo,
  getHistoryList,
  submitAddCheckCorporation,
  saveCorporationDraft,
  getCheckCorporation,
} from '../../api/qual/index';

export default {
  name: 'qualtificationHistory',
  components: {
    Basic,
    Cor,
    Qual,
  },
  filters: {
    formatDate(value) {
      const date = new Date(value);
      const y = date.getFullYear();
      let MM = date.getMonth() + 1;
      MM = MM < 10 ? `0${MM}` : MM;
      let d = date.getDate();
      d = d < 10 ? `0${d}` : d;
      let h = date.getHours();
      h = h < 10 ? `0${h}` : h;
      let m = date.getMinutes();
      m = m < 10 ? `0${m}` : m;
      let s = date.getSeconds();
      s = s < 10 ? `0${s}` : s;
      return `${y}-${MM}-${d} ${h}:${m}:${s}`;
    },
    formatStatus(value) {
      let nameStr = '';
      switch (value) {
        case 1:
          nameStr = '未认证';
          break;
        case 2:
          nameStr = '认证驳回';
          break;
        case 3:
          nameStr = '已认证';
          break;
        default:
          nameStr = '';
          break;
      }
      return nameStr;
    },
    formatType(value) {
      let typeStr = '';
      switch (value) {
        case 0:
          typeStr = '提交认证申请';
          break;
        case 1:
          typeStr = '提交认证申请';
          break;
        case 2:
          typeStr = '修改';
          break;
        default:
          typeStr = '';
          break;
      }
      return typeStr;
    },
  },
  data() {
    return {
      hostName: '',
      stateClass: 'color1',
      stateText: '待审核',
      stateTip: '已提交资质认证，请耐心等待审核结果',
      businessList: {},
      corporationType: 0, // 企业类型
      companyQualData: {}, // 企业资质信息
      qualificationData: [], // 资质信息
      businessCategory: [], // 经营类目
      allData: {}, // 企业信息
      statementIs: false,
      isEdit: false,
      isDetail: false,
      dialogVisible: false,
      historyList: [],
      sendAllData: {},
      qualIdStr: '',
      qualStateStr: null,
      isCheck: true,
      isBtnShow: true,
      changeShow: true,
    };
  },
  created() {
    getHostName().then((res) => {
      if (res.hostName) {
        this.hostName = res.hostName;
      }
    });
    this.getCom();
  },
  mounted() {
    this.setHeight();
  },
  activated() {
    if (this.$route.query.batch) {
      getHostName().then((res) => {
        if (res.hostName) {
          this.hostName = res.hostName;
        }
      });
      this.getCom();
    }
  },
  methods: {
    getCheck() {
      getCheckCorporation().then((res) => {
        if (res.code === 0) {
          this.isCheck = !res.data;
          if (res.data) {
            this.stateTip = '当前有一条待审核的<span @click="getHistory" style="color: #4183D5;cursor: pointer">企业信息修改记录</span>，请等待审核结果';
          }
        }
      });
    },
    getCom() {
      getCompanyHistoryInfo({ batch: this.$route.query.batch ? this.$route.query.batch : '' }).then(
        (res) => {
          if (res.code === 0) {
            if (res.data) {
              this.corporationType = res.data.corporationType;
              this.qualIdStr = res.data.id;
              this.qualStateStr = res.data.state;
              this.allData = { ...res.data };
              this.$refs.basic.setData(this.allData);
              if (res.data.state === 2) {
                this.stateText = '认证驳回';
                const aryTip = res.data.rejectTips ? res.data.rejectTips.split(',') : [];
                this.stateTip = `需核实和修改信息：<a style="color: #4183D5" href="javascript:;" title="${res.data.rejectTips}">${aryTip.length}项 </a> `;
                this.stateClass = '';
                this.isDetail = true;
              } else if (res.data.state === 3) {
                this.stateText = '已认证';
                this.stateTip = '您的资质已通过审核';
                this.stateClass = 'color2';
                this.isDetail = false;
                this.getCheck();
              }
            }
            this.getBus();
            this.getQual();
          }
        },
      );
    },
    setHeight() {},
    getHistory() {
      getHistoryList().then((res) => {
        if (res.code === 0) {
          this.historyList = res.data;
          this.dialogVisible = true;
        } else {
          this.historyList = [];
          this.$message.warning(res.message);
        }
      });
    },
    editData() {
      this.isEdit = true;
      this.isDetail = true;
      this.isBtnShow = false;
    },
    changeType(val) {
      this.changeShow = false;
      this.corporationType = val;
      this.allData = {};
      this.getQual();
      this.getBus();
    },
    getBus() {
      getBusinessCategory({ corporationType: this.corporationType }).then((res) => {
        if (res.code === 0) {
          this.businessCategory = res.data;
          this.changeShow = true;
          this.setHeight();
        }
      });
    },
    getQual() {
      getQualificationInfo({ corporationType: this.corporationType }).then((res) => {
        if (res.code === 0) {
          this.qualificationData = res.data.engageInQualifications
            ? res.data.engageInQualifications
            : [];
          this.companyQualData = res.data.commonQualifications ? res.data.commonQualifications : [];
          this.changeShow = true;
          this.setHeight();
        }
      });
    },
    formBasic(basic, f) {
      basic ? (this.sendAllData = { ...basic }) : '';
      if (!f) {
        basic ? this.$refs.cor.submitForm() : '';
      }
    },
    formCor(cor, f) {
      this.sendAllData.checkCorporationQualifications = [];
      cor ? (this.sendAllData.checkCorporationQualifications = cor) : '';
      cor ? this.$refs.qual.submitForm() : '';
    },
    formQual(qual, f) {
      qual
        ? (this.sendAllData.checkCorporationQualifications = this.sendAllData.checkCorporationQualifications.concat(
          qual.sendData,
        ))
        : '';
      this.sendAllData.checkCorporationBusiness = [];
      qual ? (this.sendAllData.checkCorporationBusiness = qual.corporationBusiness) : '';
      qual ? this.submitAll() : '';
    },
    submitData(f) {
      if (f === 1) {
        this.$refs.basic.submitForm(1);
        this.$refs.cor.submitForm(1);
        this.$refs.qual.submitForm(1);
        this.submitAll(1);
      } else {
        this.$refs.basic.submitForm();
      }
    },
    submitAll(f) {
      if (!f) {
        if (this.statementIs) {
          if (
            this.sendAllData.checkCorporationBusiness
            && this.sendAllData.checkCorporationBusiness.length < 1
          ) {
            this.$message.warning('请最少勾选一项经营范围');
          } else if (
            this.qualStateStr !== 5
            && this.sendAllData.checkCorporationBusiness
            && this.sendAllData.checkCorporationBusiness.length > 0
          ) {
            const loading = this.$loading({
              lock: true,
              text: 'Loading',
              spinner: 'el-icon-loading',
              background: 'rgba(0, 0, 0, 0.7)',
            });
            delete this.sendAllData.corporationBusiness;
            delete this.sendAllData.corporationQualifications;
            submitAddCheckCorporation(this.sendAllData).then((res) => {
              loading.close();
              if (res.code === 0) {
                this.$message.success(res.message);
                this.$router.go(-1);
              } else {
                this.$message.error(res.message);
              }
            });
          }
        } else {
          this.$message.warning('请仔细阅读声明并同意');
        }
      } else {
        const loading = this.$loading({
          lock: true,
          text: 'Loading',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)',
        });
        saveCorporationDraft(this.sendAllData).then((res) => {
          loading.close();
          if (res.code === 0) {
            this.$message.success(res.message);
            // window.location.reload()
          } else {
            this.$message.error(res.message);
          }
        });
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.serch {
  padding: 20px;
  font-weight: bold;
  .sign {
    display: inline-table;
    width: 3px;
    height: 13px;
    background: linear-gradient(360deg, #1d69c4 1%, #8bbdfc 99%);
    border-radius: 2px;
    margin-right: 8px;
  }
}
.his-dialog ::v-deep  .el-dialog__body {
  padding: 0 20px 20px 20px;
  .div-remarks {
    padding: 5px;
    margin: 10px 0;
    border: 1px dashed #999999;
  }
}
.qual-state {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  font-size: 16px;
  color: #f5222d;
  height: 50px;
  background: transparent !important;
  .color1 {
    color: #ff9800;
  }
  .color2 {
    color: #52c41a;
  }
  .color4 {
    color: #4183d5;
    .line {
      display: inline-block;
      width: 1px;
      height: 16px;
      background: #d8d8d8;
      vertical-align: middle;
    }
    .li-img {
      width: 16px;
      height: 16px;
      vertical-align: middle;
      margin: 0 1px 0 5px;
    }
    span {
      cursor: pointer;
      margin-left: 5px;
    }
  }
  .h {
    color: #333333;
    padding-right: 8px;
  }
  .t {
    font-size: 12px;
    color: #666666;
    padding-left: 8px;
  }
}
.avatar-uploader ::v-deep  .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  background: rgba(0, 0, 0, 0.02);
  width: 126px;
  height: 126px;
  line-height: 126px;
}
.avatar-uploader ::v-deep  .el-upload:hover {
  border-color: #409eff;
}
.avatar-uploader ::v-deep  .el-upload__tip {
  margin-top: 0;
  color: #999999;
  font-size: 12px;
}
.avatar-uploader-icon {
  font-size: 28px;
  color: #666666;
  width: 126px;
  height: 126px;
  line-height: 126px;
  text-align: center;
}
.avatar {
  width: 126px;
  height: 126px;
  display: block;
}
.avatar-uploader ::v-deep  .el-upload-list--picture-card .el-upload-list__item {
  width: 126px;
  height: 126px;
}
.footer {
  padding: 20px 0;
  border-top: 1px solid #efefef;
  .statement-box {
    width: 70%;
    margin: 0 auto;
    display: flex;
    justify-content: center;
    text-align: center;
  }
  .bot-box {
    width: 80%;
    margin: 0 auto;
    padding-top: 20px;
    text-align: center;
    button {
      padding: 8px 30px;
    }
    .el-button--primary {
      background: #4183d5;
      border-color: #4183d5;
    }
  }
}
.dis-div span {
  display: inline-block;
  width: 33%;
}
</style>
