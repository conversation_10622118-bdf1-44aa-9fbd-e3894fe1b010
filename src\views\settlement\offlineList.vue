<template>
  <div class="settlement-box">
    <el-row
      :gutter="20"
      class="price-box"
    >
      <el-col
        :span="8"
      >
        未打款金额(元)
        <span>{{ info.noMoney || info.noMoney === 0 ? info.noMoney.toFixed(2) : '' }}</span>
      </el-col>
      <el-col
        :span="8"
      >
        已打款金额（元）
        <span>{{
          info.finishMoney || info.finishMoney === 0 ? info.finishMoney.toFixed(2) : ''
        }}</span>
      </el-col>
    </el-row>
    <p>
      结算规则：<br>
      1、订单变为“已完成”状态（客户确认收货、客户未确认收货但快递妥投、卖家发货后20个自然日），订单自动进入“结算单信息”列表。退款单退款成功后自动进入“结算单信息”列表；<br>
      2、已完成订单满足T+3可进行结算，已完成退款单满足T+3可进行结算；<br>
      3、系统每日会将截止到前一日24:00所有满足结算条件的订单和退款单生成账单：若账单的“应结算金额”>=0则将账单状态记为“已入账”并将应结算金额记入“未打款金额”。若账单的“应结算金额”&lt;0则将账单状态记为“未入账”，账单“应结算金额”不记入“未打款金额”。未入账的账单会同下一笔账单的应结算金额求和，若合计应结算金额>=0则变为“已入账”状态，并将合计应结算金额记入“未打款金额”；<br>
      4、账单类型为“电汇商业”，即账单中的交易单据均为客户与商业间的线下对公转账，平台不会与商业结算，账单“应结算金额”将不会记入未打款金额；
    </p>
    <el-tabs
      v-model="activeTab"
      type="card"
    >
      <el-tab-pane
        label="账单列表"
        name="bill"
      >
        <template v-if="activeTab === 'bill'">
          <div class="searchMy">
            <el-form
              ref="listQuery"
              :model="listQuery"
              :inline="true"
              size="small"
            >
              <el-form-item
                prop="billNo"
              >
                <el-input
                  v-model="listQuery.billNo"
                  placeholder="请输入"
                >
                  <template slot="prepend">
                    账单号
                  </template>
                </el-input>
              </el-form-item>
              <el-form-item
                prop="createdTime"
              >
                <span
                  class="search-title"
                >生成时间</span>
                <div style="display: table-cell; line-height: 24px">
                  <el-date-picker
                    v-model="listQuery.createdTime"
                    type="datetimerange"
                    start-placeholder="开始时间"
                    end-placeholder="结束时间"
                    :default-time="['00:00:00', '23:59:59']"
                  />
                </div>
              </el-form-item>
              <el-form-item prop="settlementType">
                <span
                  class="search-title"
                >佣金结算方式</span>
                <el-select v-model="listQuery.settlementType">
                  <el-option
                    label="全部"
                    :value="0"
                  />
                  <el-option
                    label="月结"
                    :value="2"
                  />
                  <el-option
                    label="非月结"
                    :value="1"
                  />
                </el-select>
              </el-form-item>
              <el-form-item prop="billPaymentStatus">
                <span
                  class="search-title"
                >账单状态</span>
                <el-select v-model="listQuery.billPaymentStatus">
                  <el-option
                    label="未入账"
                    :value="0"
                  />
                  <el-option
                    label="已入账"
                    :value="1"
                  />
                </el-select>
              </el-form-item>
              <el-form-item
                prop="remitStatus"
              >
                <span
                  class="search-title"
                >打款状态</span>
                <el-select v-model="listQuery.remitStatus">
                  <el-option
                    label="未打款"
                    :value="0"
                  />
                  <el-option
                    label="已打款"
                    :value="1"
                  />
                </el-select>
              </el-form-item>
              <el-form-item
                prop="recordedTime"
              >
                <span
                  class="search-title"
                >入账时间</span>
                <div style="display: table-cell; line-height: 24px">
                  <el-date-picker
                    v-model="listQuery.recordedTime"
                    type="datetimerange"
                    start-placeholder="开始时间"
                    end-placeholder="结束时间"
                    :default-time="['00:00:00', '23:59:59']"
                  />
                </div>
              </el-form-item>
              <el-form-item
                  prop="payType"
              >
                <span
                    class="search-title"
                >账单类型</span>
                <el-select v-model="listQuery.payType">
                  <el-option
                      label="全部"
                      :value="99"
                  />
                  <el-option
                      label="电汇平台"
                      :value="3"
                  />
                  <el-option
                      label="电汇商业"
                      :value="4"
                  />
                </el-select>
              </el-form-item>
              <el-form-item prop="businessNo">
                <el-input v-model="listQuery.businessNo" placeholder="请输入订单号或退款单号">
                  <template slot="prepend">
                    单据号
                  </template>
                </el-input>
              </el-form-item>
              <el-form-item class="btn-item" style="text-align: right;padding-right: 20px">
                <el-button @click="reset">
                  重置
                </el-button>
                <el-button
                  type="primary"
                  @click="getList(listQuery, true)"
                >
                  查询
                </el-button>
              </el-form-item>
            </el-form>
          </div>
          <el-row :gutter="20" class="price-box mb15">
            <el-col :span="24">
              <div class="btn-item">
                <el-button
                  v-permission="['settle_offline_exportBill']"
                  plain
                  @click="exportList"
                >
                  导出账单
                </el-button>
                <el-button
                  v-permission="['settle_offline_exportBillDetail']"
                  plain
                  @click="exportDetail"
                >
                  导出账单明细
                </el-button>
              </div>
            </el-col>
          </el-row>
          <el-row :gutter="20" class="price-box mb15">
            <el-col :span="6">
              <el-tooltip
                class="item"
                content="当前查询结果所有单据，应结算金额求和"
                placement="left"
              >
                <i class="el-icon-warning-outline" />
              </el-tooltip>
              应结算金额合计(元)
              <span>{{
                  statementTotalMoneyTotal || statementTotalMoneyTotal === 0
                    ? statementTotalMoneyTotal.toFixed(2)
                    : ''
                }}</span>
            </el-col>

            <el-col :span="6">
              <el-tooltip
                class="item"
                content="当前查询结果所有单据，应缴纳佣金求和"
                placement="left"
              >
                <i class="el-icon-warning-outline" />
              </el-tooltip>
              应缴纳佣金合计(元)
              <span>{{ deductedCommissionTotal }}</span>
            </el-col>

            <el-col :span="6">
              <el-tooltip
                class="item"
                content="当前查询结果所有单据，实际需缴纳佣金求和"
                placement="left"
              >
                <i class="el-icon-warning-outline" />
              </el-tooltip>
              实际需缴纳佣金合计(元)
              <span>{{
                  actualCommissionMoneyTotal || actualCommissionMoneyTotal === 0
                    ? actualCommissionMoneyTotal.toFixed(2)
                    : ''
                }}</span>
            </el-col>
            <el-col :span="6">
              <el-tooltip
                class="item"
                content="当前查询结果所有单据，佣金金额求和"
                placement="left"
              >
                <i class="el-icon-warning-outline" />
              </el-tooltip>
              佣金金额合计（元）
              <span>{{
                  hireMoneyTotal || hireMoneyTotal === 0 ? hireMoneyTotal.toFixed(2) : ''
                }}</span>
            </el-col>
            <el-col :span="6">
              <el-tooltip
                class="item"
                content="当前查询结果所有单据，佣金优惠求和"
                placement="left"
              >
                <i class="el-icon-warning-outline" />
              </el-tooltip>
              佣金优惠合计（元）
              <span>{{
                  commissionDiscountMoneyTotal || commissionDiscountMoneyTotal === 0 ? commissionDiscountMoneyTotal.toFixed(2) : ''
                }}</span>
            </el-col>
          </el-row>
          <xyy-table
            :data="list"
            :list-query="listQuery"
            :col="col"
            :operation="operation"
            @get-data="getList"
            @operation-click="operationClick"
          />
        </template>
      </el-tab-pane>
      <el-tab-pane
        label="结算单信息"
        name="settlement"
      >
        <template v-if="activeTab === 'settlement'">
          <settle-list :type="2" />
        </template>
      </el-tab-pane>
    </el-tabs>
    <export-tip
      :change-export="changeExport"
      @handleExoprClose="handleExoprClose"
      @handleChangeExport="handleChangeExport"
    />
  </div>
</template>

<script>
import settleList from './components/settleList';
import exportTip from '@/views/other/components/exportTip';
import {
  getBillDatas,
  getBillDataNums,
  getBillDetailNums,
  getBillInfo,
  getPriceInfo,
  exportOfflineBillPaymemtList,
  exportOfflinePaymemtBillDetailList
} from '../../api/settlement/offline';

export default {
  name: 'SettlementOfflineList',
  components: { settleList ,exportTip },
  data() {
    return {
      activeTab: 'bill',
      listQuery: {
        pageSize: 10,
        page: 1,
        total: 0,
        billNo: '',
        createdTime: [],
        billPaymentStatus: '',
        settlementType: 0,
        remitStatus: '',
        recordedTime: [],
        payType: null,
        businessNo: '',
      },
      list: [],
      col: [
        {
          index: 'billNo',
          name: '账单号',
          width: 200,
        },
        {
          index: 'settlementType',
          name: '佣金结算方式',
          width: 150,
          formatter: (row, col, cell) => {
            if (cell === 1) {
              return '非月结';
            }
            if (cell === 2) {
              return '月结';
            }
            return '';
          },
        },
        {
          index: 'productMoney',
          name: '商品金额（元）',
          width: 150,
          formatter: (row, col, cell) => (cell || cell === 0 ? cell.toFixed(2) : ''),
        },
        {
          index: 'freightAmount',
          name: '运费金额（元）',
          width: 150,
          formatter: (row, col, cell) => (cell || cell === 0 ? cell.toFixed(2) : ''),
        },
        {
          index: 'totalMoney',
          name: '单据总额含运费（元）',
          width: 200,
          formatter: (row, col, cell) => (cell || cell === 0 ? cell.toFixed(2) : ''),
        },
        {
          index: 'shopTotalDiscount',
          name: '店铺总优惠（元）',
          width: 150,
          formatter: (row, col, cell) => (cell || cell === 0 ? cell.toFixed(2) : ''),
        },
        {
          index: 'platformTotalDiscount',
          name: '平台总优惠（元）',
          width: 150,
          formatter: (row, col, cell) => (cell || cell === 0 ? cell.toFixed(2) : ''),
        },
        {
          index: 'money',
          name: '实付金额（元）',
          width: 150,
          formatter: (row, col, cell) => (cell || cell === 0 ? cell.toFixed(2) : ''),
        },
        {
          index: 'hireMoney',
          name: '佣金金额（元）',
          width: 150,
          formatter: (row, col, cell) => (cell || cell === 0 ? cell.toFixed(2) : ''),
          renderHeader: (h, { column }) => {
            return h(
              'div', [
                h('span', column.label),
                h('el-tooltip', {
                  props: {
                    content: '账单的佣金金额=账单中所有单据的佣金金额求和',
                    placement: 'right',
                  },
                }, [
                  h('i', { class: 'el-icon-warning-outline' }),
                ]),
              ],
            );
          },
        },
        {
          index: 'statementTotalMoney',
          name: '应结算金额（元）',
          width: 150,
          formatter: (row, col, cell) => (cell || cell === 0 ? cell.toFixed(2) : ''),
          renderHeader: (h, { column }) => {
            return h(
              'div', [
                h('span', column.label),
                h('el-tooltip', {
                  props: {
                    content: '账单的应结算金额=账单中所有单据的应结算金额求和',
                    placement: 'right',
                  },
                }, [
                  h('i', { class: 'el-icon-warning-outline' }),
                ]),
              ],
            );
          },
        },
        {
          index: 'deductedCommission',
          name: '应缴纳佣金（元）',
          width: 200,
          renderHeader: (h, { column }) => {
            return h(
              'div', [
                h('span', column.label),
                h('el-tooltip', {
                  props: {
                    content: '账单的应缴纳佣金=账单中所有单据的应缴纳佣金求和。单据平台补贴金额冲抵佣金金额后，对应账单商业应给平台缴纳的佣金金额',
                    placement: 'right',
                  },
                }, [
                  h('i', { class: 'el-icon-warning-outline' }),
                ]),
              ],
            );
          },
        },
        {
          index: 'actualCommissionMoney',
          name: '实际需缴纳佣金',
          width: 200,
          renderHeader: (h, { column }) => {
            return h(
              'div', [
                h('span', column.label),
                h('el-tooltip', {
                  props: {
                    content: '账单的实际需缴纳佣金=账单中所有单据的实际需缴纳佣金求和。单据享受佣金折扣政策优惠后，对应账单商业实际需给平台缴纳的佣金金额',
                    placement: 'right',
                  },
                }, [
                  h('i', { class: 'el-icon-warning-outline' }),
                ]),
              ],
            );
          },
        },
        {
          index: 'commissionDiscountMoney',
          name: '佣金优惠',
          width: 200,
          renderHeader: (h, { column }) => {
            return h(
              'div', [
                h('span', column.label),
                h('el-tooltip', {
                  props: {
                    content: '账单的佣金优惠=账单中所有单据的佣金优惠求和。单据因享受佣金折扣政策而产生的佣金优惠',
                    placement: 'right',
                  },
                }, [
                  h('i', { class: 'el-icon-warning-outline' }),
                ]),
              ],
            );
          },
        },
        {
          index: 'billPaymentStatus',
          name: '账单状态',
          formatter: (row, col, cell) => (cell === 1 ? '已入账' : '未入账'),
        },
        {
          index: 'remitStatus',
          name: '打款状态',
          formatter: (row, col, cell) => {
            if (cell === 1) {
              return '已打款'
            }
            if (cell === 2) {
              return '无需打款'
            }
            return '未打款'
          },
        },
        {
          index: 'billCreateTime',
          name: '生成时间',
          width: 200,
          formatter: (row, col, cell) => (cell ? new Date(cell + 8 * 3600 * 1000).toJSON().substr(0, 19).replace('T', ' ') : ''),
        },
        {
          index: 'billPaymentTime',
          name: '入账时间',
          width: 200,
          formatter: (row, col, cell) => (cell ? new Date(cell + 8 * 3600 * 1000).toJSON().substr(0, 19).replace('T', ' ') : ''),
        },
        {
          index: 'remitTime',
          name: '打款时间',
          width: 200,
          formatter: (row, col, cell) => (cell ? new Date(cell + 8 * 3600 * 1000).toJSON().substr(0, 19).replace('T', ' ') : ''),
        },
        {
          index: 'payType',
          name: '账单类型',
          formatter: (row, col, cell) => {
            if (cell === 3) {
              return '电汇平台';
            }
            if (cell === 4) {
              return '电汇商业';
            }
            return '';
          },
        },
        {
          index: 'operation',
          name: '操作',
          operation: true,
          width: 150,
        },
      ],
      operation: [
        {
          name: '查看明细',
          type: 0,
        },
      ],
      info: {
        noMoney: '',
        finishMoney: '',
      },
      hireMoneyTotal: '', // 佣金金额
      statementTotalMoneyTotal: '', // 应结算金额
      deductedCommissionTotal: '', // 应缴纳佣金合计
      actualCommissionMoneyTotal: '', // 实际需缴纳佣金合计
      commissionDiscountMoneyTotal: '', // 佣金优惠合计
      changeExport: false,
    };
  },
  created() {
    this.getPriceInfo();
    this.changeTab({ name: 'bill' });
  },
  methods: {
    getList(listQuery, reset) {
      const loading = this.$loading({
        lock: true,
        text: 'Loading',
        spinner: 'el-icon-loading',
        background: 'rgba(255,255,255, 0.8)',
      });
      this.getBillPrice();
      const { page, pageSize } = listQuery;
      const {
        billNo,
        billPaymentStatus,
        remitStatus,
        createdTime,
        recordedTime,
        settlementType,
        payType,
        businessNo
      } = this.listQuery;

      getBillDatas({
        pageNum: reset ? 1 : page,
        pageSize,
        billNo,
        billPaymentStatus,
        remitStatus,
        payType,
        settlementType,
        businessNo,
        startCreateTime:
          createdTime && createdTime.length ? this.formatDate(createdTime[0].getTime()) : '',
        endCreateTime:
          createdTime && createdTime.length ? this.formatDate(createdTime[1].getTime()) : '',
        startBillPaymentTime:
          recordedTime && recordedTime.length ? this.formatDate(recordedTime[0].getTime()) : '',
        endBillPaymentTime:
          recordedTime && recordedTime.length ? this.formatDate(recordedTime[1].getTime()) : '',
      })
        .then((res) => {
          loading.close();
          if (res.code === '200') {
            const { list, total, pageNum } = res.data;
            this.list = list;
            this.listQuery = {
              ...this.listQuery,
              total,
              page: pageNum,
            };
          } else {
            this.$message.error({ message: res.errorMsg || res.msg, customClass: 'center-msg' });
          }
        })
        .catch(() => {
          loading.close();
        });
    },
    /**
     * 导出账单数据
     */
    exportList() {
      const {
        billNo,
        billPaymentStatus,
        remitStatus,
        payType,
        createdTime,
        recordedTime,
        settlementType,
        businessNo
      } = this.listQuery;
      const startCreateTime = createdTime && createdTime.length ? this.formatDate(createdTime[0].getTime()) : '';
      const endCreateTime = createdTime && createdTime.length ? this.formatDate(createdTime[1].getTime()) : '';
      const startBillPaymentTime = recordedTime && recordedTime.length ? this.formatDate(recordedTime[0].getTime()) : '';
      const endBillPaymentTime = recordedTime && recordedTime.length ? this.formatDate(recordedTime[1].getTime()) : '';
      const params = {
        billNo,
        billPaymentStatus,
        remitStatus,
        payType,
        startCreateTime,
        endCreateTime,
        startBillPaymentTime,
        endBillPaymentTime,
        settlementType,
        businessNo
      };
      getBillDataNums(params)
        .then((res) => {
          if (res.code === '200') {
            if (res.data === 0) {
              this.$message.error({ message: '暂无账单数据', customClass: 'center-msg' });
            } else if (res.data > 10000) {
              this.$message.error({
                message: `导出上限为10000条，当前搜索结果导出数据为${res.data}条，超出导出上限`,
                customClass: 'center-msg',
              });
            } else {
              this.exportData();
            }
          } else {
            this.$message.error({ message: res.errorMsg || res.msg, customClass: 'center-msg' });
          }
        })
        .catch(() => {});
    },
    /**
     * 导出账单明细
     */
    exportDetail() {
      const {
        billNo,
        billPaymentStatus,
        remitStatus,
        payType,
        createdTime,
        recordedTime,
        settlementType,
        businessNo
      } = this.listQuery;
      const startCreateTime = createdTime && createdTime.length ? this.formatDate(createdTime[0].getTime()) : '';
      const endCreateTime = createdTime && createdTime.length ? this.formatDate(createdTime[1].getTime()) : '';
      const startBillPaymentTime = recordedTime && recordedTime.length ? this.formatDate(recordedTime[0].getTime()) : '';
      const endBillPaymentTime = recordedTime && recordedTime.length ? this.formatDate(recordedTime[1].getTime()) : '';
      const params = {
        billNo,
        billPaymentStatus,
        remitStatus,
        payType,
        startCreateTime,
        endCreateTime,
        startBillPaymentTime,
        endBillPaymentTime,
        settlementType,
        businessNo
      };
      getBillDetailNums(params)
        .then((res) => {
          if (res.code === '200') {
            if (res.data === 0) {
              this.$message.error({ message: '暂无账单明细数据', customClass: 'center-msg' });
            } else if (res.data > 10000) {
              this.$message.error({
                message: `导出上限为10000条，当前搜索结果导出数据为${res.data}条，超出导出上限`,
                customClass: 'center-msg',
              });
            } else {
              this.exportData('detail');
            }
          } else {
            this.$message.error({ message: res.errorMsg || res.msg, customClass: 'center-msg' });
          }
        })
        .catch(() => {});
    },
    /**
     * 导出数据
     */
    exportData(link) {
      const {
        billNo,
        billPaymentStatus,
        remitStatus,
        payType,
        createdTime,
        recordedTime,
        settlementType,
        businessNo
      } = this.listQuery;
      const startCreateTime = createdTime && createdTime.length ? this.formatDate(createdTime[0].getTime()) : '';
      const endCreateTime = createdTime && createdTime.length ? this.formatDate(createdTime[1].getTime()) : '';
      const startBillPaymentTime = recordedTime && recordedTime.length ? this.formatDate(recordedTime[0].getTime()) : '';
      const endBillPaymentTime = recordedTime && recordedTime.length ? this.formatDate(recordedTime[1].getTime()) : '';
      const params = {
        billNo,
        billPaymentStatus,
        remitStatus,
        payType,
        startCreateTime,
        endCreateTime,
        startBillPaymentTime,
        endBillPaymentTime,
        settlementType,
        businessNo
      }
      // const url = `${process.env.VUE_APP_BASE_API}${link}${this.getParams(params)}`
      // const a = document.createElement('a')
      // a.href = url
      // a.click()
      if(link == 'detail'){
        exportOfflinePaymemtBillDetailList(params).then((res) => {
          if (res.code !== 0) {
            this.$message.error(res.message);
            return;
          }
          this.changeExport = true;
        });
      }else{
        exportOfflineBillPaymemtList(params).then((res) => {
          if (res.code !== 0) {
            this.$message.error(res.message);
            return;
          }
          this.changeExport = true;
        });
      }
    },
    handleChangeExport(info) {
      this.changeExport = false;
      if (info === 'go') {
        const path = '/downloadList'
        window.openTab(path)
        //that.$router.push({ path: '/downloadList' });
      }
    },
    handleExoprClose() {
      this.changeExport = false;
    },
    /**
     * 获取get请求参数
     */
    getParams(params) {
      let queryStr = '?';
      Object.keys(params).forEach((key) => {
        queryStr += `${key}=${params[key]}&`;
      });
      queryStr = queryStr.substr(0, queryStr.length - 1);
      return queryStr;
    },
    /**
     * 重置数据
     */
    reset() {
      this.$refs.listQuery.resetFields();
      this.initCreatedDate();
      this.listQuery.recordedTime = [];
      this.getList(this.listQuery, true);
    },
    /**
     * 选项操作
     */
    operationClick(type, row) {
      if (type === 0) {
        // 查看明细
        this.$router.push(`/settlementOfflineDetail?billNo=${row.billNo}`);
      }
    },
    /**
     * 获取入账单价格
     */
    getBillPrice() {
      const {
        billNo,
        billPaymentStatus,
        remitStatus,
        payType,
        createdTime,
        recordedTime,
        settlementType,
        businessNo
      } = this.listQuery;
      const startCreateTime = createdTime && createdTime.length ? this.formatDate(createdTime[0].getTime()) : '';
      const endCreateTime = createdTime && createdTime.length ? this.formatDate(createdTime[1].getTime()) : '';
      const startBillPaymentTime = recordedTime && recordedTime.length ? this.formatDate(recordedTime[0].getTime()) : '';
      const endBillPaymentTime = recordedTime && recordedTime.length ? this.formatDate(recordedTime[1].getTime()) : '';
      const params = {
        billNo,
        billPaymentStatus,
        remitStatus,
        payType,
        startCreateTime,
        endCreateTime,
        startBillPaymentTime,
        endBillPaymentTime,
        settlementType,
        businessNo,
      };
      getBillInfo(params).then((res) => {
        if (res.code === '200') {
          this.hireMoneyTotal = res.data.hireMoneyTotal;
          this.statementTotalMoneyTotal = res.data.statementTotalMoneyTotal;
          this.deductedCommissionTotal = res.data.deductedCommissionTotal;
          this.actualCommissionMoneyTotal = res.data.actualCommissionMoneyTotal;
          this.commissionDiscountMoneyTotal = res.data.commissionDiscountMoneyTotal;
        } else {
          this.$message.error({ message: res.errorMsg || res.msg, customClass: 'center-msg' });
        }
      });
    },
    /**
     * 切换tab
     */
    changeTab(tab) {
      if (tab.name === 'bill') {
        this.$nextTick(() => {
          this.reset();
        });
      }
    },
    /**
     * 获取打款信息
     */
    getPriceInfo() {
      getPriceInfo()
        .then((res) => {
          Object.keys(this.info).forEach((key) => {
            this.info[key] = res[key];
          }, this);
        })
        .catch(() => {});
    },
    /**
     * 格式化日期
     */
    formatDate(date) {
      return Number(date)
        ? new Date(date + 8 * 3600 * 1000).toJSON().substr(0, 19).replace('T', ' ')
        : '';
    },
    /**
     * 初始化生成时间
     */
    initCreatedDate() {
      let year = new Date().getFullYear();
      let month = new Date().getMonth() - 2;

      if (month <= 0) {
        month += 12;
        year -= 1;
      }
      const start = new Date(`${year}-0${month}-01 00:00:00`);
      const end = new Date(
        `${
          new Date(new Date().getTime() + 8 * 3600 * 1000).toJSON().substr(0, 19).split('T')[0]
        } 23:59:59`,
      );
      this.listQuery.createdTime = [start, end];
    },
  },
};
</script>

<style lang="scss" scoped>
.settlement-box {
  padding: 15px;
  .price-box {
    font-size: 16px;
    font-family: PingFangSC, PingFangSC-Semibold;
    font-weight: 600;
    color: #303133;
    line-height: 40px;
    overflow: hidden;
    &.mb15 {
      margin-bottom: 15px;
    }
    span {
      font-size: 28px;
    }
    .el-button {
      padding: 0 12px;
      line-height: 30px;
      &.is-plain {
        color: #4183d5;
        border-color: #4183d5;
      }
    }
  }
  .btn-box {
    margin-top: 15px;
    .el-button {
      padding: 0 12px;
      line-height: 30px;
      &.is-plain {
        color: #4183d5;
        border-color: #4183d5;
      }
    }
  }
  > p {
    padding: 8px;
    background: #f9f9f9;
    border-radius: 2px;
    font-size: 14px;
    font-family: PingFangSC, PingFangSC-Regular;
    font-weight: 400;
    color: #666666;
    margin: 15px 0 25px;
  }

  .el-tabs {
    .el-tab-pane {
      padding: 0 20px;
      .el-form {
        overflow: hidden;
        &.settlement-form {
          ::v-deep  .el-form-item__content {
            .el-select {
              width: 246px;
            }
          }
        }
      }
      .btn-item {
        float: right;
      }
    }
  }
}
.searchMy ::v-deep  .el-form-item__label {
  margin-left: 20px;
  padding: 0;
}
.searchMy ::v-deep  .el-input__inner {
  border-radius: 0 4px 4px 0;
}
.searchMy ::v-deep  .el-date-editor{
  width: 100%;
}
.search-title {
  display: table-cell;
  padding: 0 10px;
  text-align: center;
  border: 1px solid #dcdfe6;
  height: 30px;
  line-height: 30px;
  vertical-align: middle;
  border-right: none;
  border-radius: 4px 0 0 4px;
  color: #333333;
  white-space: nowrap;
}
.searchMy ::v-deep  .el-select {
  display: table-cell;
  width: 100%;
}
.searchMy ::v-deep  .el-form-item__content{
  width: 100%;
}
.searchMy ::v-deep  .el-form-item--small.el-form-item{
  margin-bottom: 10px;
  width: 32%;
}
.searchMy ::v-deep  .el-form-item--small .el-form-item__content{
  line-height: 30px;
  width: 100%;
}
.searchMy ::v-deep  .el-input-group__prepend {
  background: none;
  color: #333333;
  padding: 0 10px;
}
.Fsearch {
  padding: 0px 20px 10px;
  font-weight: 500;
  .searchMsg {
    font-weight: 700;
    width: 200px;
  }
  .sign {
    display: inline-table;
    width: 3px;
    height: 13px;
    background: linear-gradient(360deg, #1d69c4 1%, #8bbdfc 99%);
    border-radius: 2px;
    margin-right: 8px;
  }
}
</style>
