export const SFstyle = `<style>

.page-row img {
    padding: 0;
    margin: 0;
    object-fit: cover;
    width: 100%;
    height: 100%;
}

.content {
    width: 94mm;
    height: auto;
    margin: 0mm auto;
    font-family: 黑体;
    box-sizing: border-box;
    font-size: 12px;
    color: #000;
    font-weight: bold;
    padding: 1mm 0;
}

.page-row {
    border: 1px solid #000000;
    border-bottom: none;
    text-align: left;
    background-color: #ffffff;
    overflow: hidden;
    position: relative;
}

.page-row.flex {
    display: flex;
    justify-content: left;
    align-items: center;
}

.content .page-row:last-child {
    border-bottom: 1px solid #000000;
}

.page-row.one {
    border: none;
}

.cell_12 {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.filletStr {
    font-size: 30px;
    font-weight: bold;
    border: 1px solid #000;
    border-radius: 40px;
    padding: 2px;
}

.verticalCell_1 {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.verticalCell_1 > div {
    width: 100%;
    height: 100%;
    border-bottom: 1px solid #000;
    display: flex;
    align-items: center;
    padding-left: 5px;
}

.verticalCell_1 > div:last-child {
    border-bottom: none;
}

</style>`;
