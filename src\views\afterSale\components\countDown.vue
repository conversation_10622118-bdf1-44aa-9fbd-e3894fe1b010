<script>  
export default {  
    props: {  
        time: {  
            default: 0,
            type: Number  
        }  
    },  
    data() {  
        return {  
            remainingTime: Date.now() + this.time * 1000,
            timer: null,
            currentTime: Date.now(),
        };  
    },
    created() { 
      // 为每个 item 设置定时器  
      this.timer = setInterval(this.updateCurrentTime, 1000);
    },
     beforeDestroy() {  
      // 组件销毁前清除所有定时器  
      if (this.timer) {  
        clearInterval(this.timer);  
      }  
    },
    methods: {
        updateCurrentTime() {  
            this.currentTime = Date.now();  
        }, 
        formattedRemainingTime() {  
            const remainingMilliseconds = this.remainingTime - this.currentTime;  
            if (remainingMilliseconds <= 0) {
                clearInterval(this.timer);  
                return '已过期';  
            } 
            const totalSeconds = Math.floor(remainingMilliseconds / 1000);  
            const hours = String(Math.floor(totalSeconds / 3600)).padStart(2, '0');  
            const minutes = String(Math.floor((totalSeconds % 3600) / 60)).padStart(2, '0'); 
            return `${hours}小时${minutes}分`;  
        } 
    }
};  
</script>  
  
<template> 
    <div>
        <span>剩余时间：</span>
        <span style="color: red;font-size: 12px">
         {{ formattedRemainingTime() }}
          <el-tooltip effect="dark" placement="top">
            <template #content>超时未处理，系统将会自动同意</template>
            <i class="el-icon-warning-outline"></i>
          </el-tooltip>
        </span>
    </div>  
</template>  
  
<style scoped>  
</style>