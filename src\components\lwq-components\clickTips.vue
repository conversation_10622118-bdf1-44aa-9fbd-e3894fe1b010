<script>
export default {
  props: {
    disabled: {
      default: false,
      type: <PERSON><PERSON>an,
    }
  },
  data() {
    return {
      visible: false,
    }
  },
  methods: {
    onClick() {
      if (this.disabled) return
      this.visible = true;
    },
    ok() {
      this.$emit('ok')
      this.visible = false;
    }
  }
}
</script>

<template>
  <span>
    <div style="display: inline-block;" @click="onClick">
      <slot>
        <el-button style="display: block;" size="mini" type="primary"></el-button>
      </slot>
    </div>
    <el-dialog :visible.sync="visible"  append-to-body title="提示" width="300px">
      <slot name="content">
      </slot>
      <div slot="footer">
        <el-button type="normal" size="mini" @click="visible = false">取消</el-button>
        <el-button type="primary" size="mini" @click="ok">确定</el-button>
      </div>
    </el-dialog>
  </span>
</template>

<style scoped>
p {
  margin: 5px 0;
}
::v-deep   .el-dialog {
  border-radius: 5px;
  overflow: hidden;
}
::v-deep   .el-dialog__header {
  padding: 10px;
  background-color: #f3f3f3;
}
::v-deep   .el-dialog__header span {
  font-size: 14px;
  line-height: normal;
}
::v-deep   .el-dialog__header button {
  right: 10px;
  top: 10px;
}
::v-deep   .el-dialog__body {
  padding: 5px 10px;
  border-bottom: solid 1px #e4eaf1;
}
::v-deep   .el-dialog__footer {
  padding: 10px;
}
</style>
