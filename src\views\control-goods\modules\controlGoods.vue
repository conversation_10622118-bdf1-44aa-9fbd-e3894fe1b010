<template>
  <div class="main-box">
    <div class="list-box">
      <div class="explain-search">
        <div>
          <div class="con-title" style="padding-left: 0">
            <span class="line"></span><span>全部流程</span>
          </div>
          <steps style="padding-bottom: 5px;border-bottom: 1px solid #f0f2f5;" :active="1"></steps>
          <div class="con-title" style="padding-left: 0">
            <span class="line"></span><span>选择控销商品</span>
          </div>
          <div
            class="search-btn"
            style="padding-bottom: 20px;padding-top: 10px"
          >
            <el-button type="primary" size="small" @click="designatedGoods"
              >指定商品</el-button
            >
            <!-- <div class="btn-dib">
              <el-upload
                class="upload-demo"
                action=""
                :http-request="excelUpload"
                :before-upload="beforeExcel"
                multiple
                :limit="100"
                :show-file-list="false"
                accept=".xlsx,.xls"
                :on-exceed="handleExceed"
              >
                <el-button size="small" type="primary" >批量导入</el-button>
              </el-upload>
            </div> -->
            <el-button size="small" type="primary" @click="showDialogImport">批量导入</el-button>
            <el-button size="small" @click="removeShopLsit">移除商品</el-button>
             <!-- <el-button size="small" @click="exportExcel">导出商品</el-button> -->
          </div>
          <div style="color: red;font-weight: bold;">若商品已有控销，本次控销将覆盖原有控销，以本次控销为准。</div>
          <div class="customer-tabs">
            <el-table
              v-loading="isLoading"
              @selection-change="seleteShopHander"
              :data="tableData.list"
              stripe
              style="width: 100%"
            >
              <el-table-column
                type="selection"
                width="55"
                :selectable="selectable"
              ></el-table-column>
              <el-table-column
                prop="barcode"
                label="商品编码"
                width="150"
              ></el-table-column>
              <el-table-column prop="erpCode" label="商户ERP编码" width="150"></el-table-column>
              <el-table-column prop="showName" label="商品名称" width="150"></el-table-column>
              <el-table-column prop="approvalNumber" label="批准文号" width="150"></el-table-column>
              <el-table-column prop="code" label="条码" width="150"></el-table-column>
              <el-table-column prop="manufacturer" label="生产厂家" width="150"></el-table-column>
              <el-table-column prop="spec" label="规格" width="150"></el-table-column>
              <el-table-column prop="fob" label="售价" width="100"></el-table-column>
              <el-table-column label="状态">
                <template slot-scope="scope">
                  <span v-if="scope.row.status == 1">销售中</span>
                   <span v-else-if="scope.row.status == 2">已售罄</span>
                  <span v-else-if="scope.row.status == 4">下架</span>
                  <span v-else-if="scope.row.status == 6">待上架</span>
                  <span v-else-if="scope.row.status == 7">已录入</span>
                  <span v-else-if="scope.row.status == 8">待审核</span>
                  <span v-else-if="scope.row.status == 9">审核未通过</span>
                  <span v-else>-</span>
                </template>
              </el-table-column>
              <el-table-column label="描述" width="150">
                <template slot-scope="scope">
                  <el-tooltip v-if="scope.row.hasControl" content="注意：商品已有控销，提交后将覆盖原有控销，以本次控销为准。" placement="top">
                    <span class="describe">注意：商品已有控销，提交后将覆盖原有控销，以本次控销为准。</span>
                  </el-tooltip>
                </template>
              </el-table-column>
              <template slot="empty">
                <div class="noData">
                  <p class="img-box">
                    <img
                      src="../../../assets/image/marketing/noneImg.png"
                      alt=""
                    />
                  </p>
                  <p>暂无数据</p>
                </div>
              </template>
            </el-table>
          </div>
        </div>
        <div class="explain-pag">
          <Pagination
            v-show="tableData.total > 0"
            :total="tableData.total"
            :page.sync="pageData.pageNum"
            :limit.sync="pageData.pageSize"
            @pagination="getList"
          ></Pagination>
        </div>
      </div>
      <el-row
        class="search-btn"
        type="flex"
        justify="end"
        align="middle"
        style="padding-top: 10px;padding-right: 20px"
      >
        <el-button size="small" @click="PreStep">上一步</el-button>
        <el-button size="small" @click="toAddUser" type="info" :class="'status-'+ (tableData.allList.length<=0?0:1)">下一步</el-button>
      </el-row>
    </div>

    <el-dialog
      :visible="centerDialogImport"
      :close-on-click-modal="false"
      title="提示"
      custom-class="import-box"
      top="10%"
      @close="centerDialogImport = false"
    >
      <div class="content-body">
        <el-upload
            ref="importFile"
            class="upload-demo"
            action=""
            :before-upload="beforeExcel"
            multiple
            :limit="100"
            :show-file-list="false"
            accept=".xlsx,.xls"
            :on-exceed="handleExceed"
            style="display: inline-block"
          >
          <el-button size="small" type="primary">导入Excel文件</el-button>
           <!-- <div slot="file" slot-scope="{file}">
            <span class="file-name">{{ file.name }}</span>
          </div> -->
        </el-upload>
        <el-button size="small" @click="downLoadExcelMode" style="margin-left: 10px">下载模板</el-button>
         <span style="display: block" :class="fileName.length>0?'file-name':'upload-tips'">
          {{fileName.length>0?fileName:'请上传excel、xls、xlsx文件'}}
        </span>

      </div>
      <span>
        <h4>提示：</h4>
          <p>1、请您留意商品编码和商户ERP编码是否一一对应</p>
          <p>2、支持上传xlsx、xls文件，大小不超过3M，商品数量不超过5000个</p>
          <p>3、模板中商户ERP编码和商品编码任意一项不为空即可，若同一ERP编码对应多个商品请填写商品编码。其他需要修改字段选填，不修改字段可不填</p>
          <p>4、如果导入数据达到5000条，大概导入时间为3分钟；</p>
      </span>
      <span v-show="downloadUrl">
        <p style="color:red">共成功上传{{success}}个商品，失败{{failed}}个，下载错误文件<a :href="downloadUrl" download class="downLoader-btn" style="">批量导入商品错误文件</a>查看</p>
      </span>
      <span class="dialog-footer" slot="footer">
        <el-button size="small" @click="centerDialogImport=false">关闭</el-button>
        <el-button size="small" type="primary" @click="excelUpload">提交</el-button>
      </span>
    </el-dialog>

    <el-dialog
      title="全部商品列表"
      :visible.sync="outerVisible"
      class="my-dialog"
      :modal-append-to-body="false"
      :close-on-click-modal="false"
      width="96%"
    >
      <div class="explain-search" style="padding-top: 20px">
        <el-form size="small" :inline="true" ref="ruleForm">
          <el-form-item  class="my-label" label="商品编码">
            <el-input
              placeholder="商品编码"
              v-model.trim="productSearch.barcode"
              class="search-input"
              style="width: 164px"
            >
            </el-input>
          </el-form-item>
          <el-form-item  class="my-label" label="商品名称">
            <el-input
              placeholder="商品名称"
              v-model.trim="productSearch.showName"
              class="search-input"
              style="width: 164px"
            >
            </el-input>
          </el-form-item>
          <el-form-item  class="my-label" label="商户ERP编码">
            <el-input
              placeholder="商户ERP编码"
              v-model.trim="productSearch.erpCode"
              class="search-input"
              style="width: 140px"
            >
            </el-input>
          </el-form-item>
          <el-form-item  class="my-label my-label2" label="条码" label-width="67px">
            <el-input
              placeholder="条码"
              v-model.trim="productSearch.code"
              class="search-input"
              style="width: 164px"
            >
            </el-input>
          </el-form-item>
          <el-form-item class="my-label" label="商品状态">
            <el-select
              v-model.trim="productSearch.status"
              placeholder="请选择"
              style="width: 164px"
            >
              <el-option label="全部" value=""></el-option>
              <el-option label="待上架" :value="6"></el-option>
              <el-option label="销售中" :value="1"></el-option>
              <el-option label="已售罄" :value="2"></el-option>
              <el-option label="下架" :value="4"></el-option>

            </el-select>
          </el-form-item>
          <el-form-item  class="my-label" label="批准文号">
            <el-input
              placeholder="批准文号"
              v-model.trim="productSearch.approvalNumber"
              class="search-input"
              style="width: 164px"
            >
            </el-input>
          </el-form-item>
          <el-form-item class="search-btn" style="float: right;">
            <el-button type="primary" @click="getProductList('search')"
              >查询</el-button
            >
            <el-button @click="resetForm('ruleForm')">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
      <div class="explain-table">
        <el-table
          v-loading="isProductLoad"
          @selection-change="seleteCustomerHander"
          :data="productData.list"
          ref="dilogTable"
          stripe
          max-height="397"
          style="width: 100%"
        >
          <el-table-column type="selection" width="55"> </el-table-column>
          <el-table-column prop="barcode" label="商品编码" ></el-table-column>
          <el-table-column prop="erpCode" label="商户ERP编码" width="110"></el-table-column>
          <el-table-column prop="showName" label="商品名称" ></el-table-column>
          <el-table-column prop="approvalNumber" label="批准文号" ></el-table-column>
          <el-table-column prop="code" label="条码" ></el-table-column>
          <el-table-column prop="manufacturer" label="生产厂家" ></el-table-column>
          <el-table-column prop="spec" label="规格" ></el-table-column>
          <el-table-column prop="fob" label="售价" width="55"></el-table-column>
          <el-table-column label="状态" width="70">
            <template slot-scope="scope">
              <span v-if="scope.row.status == 1">销售中</span>
              <span v-else-if="scope.row.status == 4">下架</span>
              <span v-else-if="scope.row.status == 6">待上架</span>
              <span v-else-if="scope.row.status == 8">待审核</span>
              <span v-else-if="scope.row.status == 9">审核未通过</span>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <template slot="empty">
            <div class="noData">
              <p class="img-box">
                <img src="../../../assets/image/marketing/noneImg.png" alt="" />
              </p>
              <p>暂无数据</p>
            </div>
          </template>
        </el-table>
      </div>

      <div class="explain-pag">
        <Pagination
          v-show="productData.total > 0"
          :total="productData.total"
          :page.sync="productPage.pageNum"
          :limit.sync="productPage.pageSize"
          @pagination="getProductList"
        ></Pagination>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="outerVisibleClick" size="small">取 消</el-button>
        <el-button
          type="primary"
          class="xyy-blue"
          @click="sureInnerVisible"
          size="small"
          >确定</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>
<style scoped>
@import '../../../assets/css/changeElement.scss';
</style>
<script>
import Pagination from '@/components/Pagination'
import {
  findSkuList,
  exportControlProducts,
  importProduct,
  hasControl
} from '@/api/goods/controlGoods.js'
import steps from './../components/steps';
import {actionTracking} from "@/track/eventTracking";
export default {
  name: 'changeGoods',
  components: {
    Pagination,
    steps
  },
  props: {
    isEditData: {
      type: Boolean,
      default: true
    },
    productListE: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      allChangeList: new Set(),
      tableData: {
        list: [],
        allList: [],
        total: 0
      },
      changeProduct: [],
      handerProduct: [],
      productData: {
        list: [],
        total: 0
      },
      productSearch: {
        barcode: '',
        showName: '',
        status: '',
        erpCode:'',
        code:'',
        approvalNumber:'',
        // shopCode:'DP90110'
      },
      productPage: {
        pageNum: 1,
        pageSize: 20
      },
      pageData: {
        pageNum: 1,
        pageSize: 20
      },
      isLoading: false,
      outerVisible: false,
      isProductLoad: false,
      centerDialogImport: false, // 点击导入按钮弹出框
      downloadUrl:'',
      success:0,
      failed:0,
      downloadTemplate:process.env.VUE_APP_BASE_API+'/salesControl/downloadControlProductImportTemplate',
      fileName:'',
      files:''
    }
  },
  watch: {
    productListE: {
      deep: true,
      handler: function() {
        if (this.productListE.length > 0) {
          this.tableData.allList = JSON.parse(JSON.stringify(this.productListE))
          this.tableData.list = this.tableData.allList.slice(0, this.pageData.pageSize)
          this.tableData.total = this.tableData.allList.length
          this.getHasControl(this.tableData.list);
        } else {
          this.tableData.allList = []
          this.tableData.list = this.tableData.allList.slice(this.pageData.pageSize)
          this.tableData.total = this.tableData.allList.length
        }
      }
    }
  },
  created() {
    if (this.productListE.length > 0) {
      this.tableData.allList = JSON.parse(JSON.stringify(this.productListE))
      this.tableData.list = this.tableData.allList.slice(0, this.pageData.pageSize)
      this.tableData.total = this.tableData.allList.length
      this.getHasControl(this.tableData.list);
    }
  },
  methods: {
    selectable(row) {
      if (row.initialData) {
        return false;
      } else {
        return true;
      }
    },
    downLoadExcelMode() {
      window.open(this.downloadTemplate)
    },
    getList() {
      this.tableData.list = this.tableData.allList.slice(
        (this.pageData.pageNum - 1) * this.pageData.pageSize,
        this.pageData.pageNum * this.pageData.pageSize
      )
    },
    //选择商品
    designatedGoods() {
      actionTracking('add_controlled_goods_click', {
            add_controlled_goods : 'specify_goods'
      });
      this.outerVisible = true
      this.getProductList()
    },
    //移除商品
    removeShopLsit() {
      if (this.changeProduct.length > 0) {
        this.$confirm('确认移除已选商品?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: '',
          customClass:'delete-dialog-customClass'
        })
          .then(() => {
            let set = this.changeProduct.map((item) => item.id)
            let resArr = this.tableData.allList.filter(
              (item) => !set.includes(item.id)
            )
            this.tableData.allList = resArr
            this.tableData.list = this.tableData.allList.slice(0, this.pageData.pageSize)
            this.tableData.total = this.tableData.allList.length
          })
          .catch(() => {})
      } else {
        this.$message({
          message: '请先选择商品',
          type: 'warning'
        })
      }
    },
    //导入商品
    handleExceed(files, fileList) {
      this.$message({
        message: '超过上传限制',
        type: 'error',
        duration: 1000
      })
    },
    beforeExcel(file) {

      let extension = file.name.split('.')[1] === 'xls';
      let extension2 = file.name.split('.')[1] === 'xlsx';
      let isLt2M = file.size / 1024 / 1024 < 200
      if (!extension && !extension2) {
          this.$message.warning('上传模板只能是 xls、xlsx格式!');
          return
      }
      if(!isLt2M){
          this.$message.warning('文件过大，最大支持200M!');
          return
      }
      this.files = file;
      this.fileName=file.name;
      return false;
    },
    excelUpload() {
      let that = this
      let fromData;
      if(this.files&&this.files.name!=""){
          fromData = new FormData()
          fromData.append('file', this.files, this.fileName);
          importProduct(fromData).then((res) => {
            if (res.code == 0) {
              if (res.data.rows.length > 0) {
                res.data.rows.map(function(item) {
                  that.tableData.allList.push(item)
                })
                that.tableData.allList = that.removeDuplicateObject(
                  that.tableData.allList
                )
                that.tableData.list = that.tableData.allList.slice(0, this.pageData.pageSize)
                that.tableData.total = that.tableData.allList.length
                that.getHasControl(that.tableData.list);
              }
              if(res.data.failed>0){
                  if (res.data && res.data.downloadUrl) {
                    this.downloadUrl = process.env.VUE_APP_BASE_API + res.data.downloadUrl;
                    console.log(this.downloadUrl)
                    this.success = res.data.success;
                    this.failed = res.data.failed;
                  }
                  this.files = "";
                  this.fileName = "";
                  this.$refs['importFile'].uploadFiles = [];
              }else{
                  this.downloadUrl = '';
                  this.centerDialogImport = false;
                  this.files = "";
                  this.fileName = "";
                  this.$refs['importFile'].uploadFiles = [];
                  this.$message({
                    message: '导入成功',
                    type: 'success',
                    duration: 1000
                  })
              }
            } else {
              this.$confirm(res.message, '提示', {
                confirmButtonText: '确定',
                type: 'warning',
                customClass:'delete-dialog-customClass'
              })
                .then(() => {})
                .catch(() => {})
            }
            this.isLoading = false
          })
      }else{
        this.$message({
          message: '请上传excel、xls、xlsx文件',
          type: 'warning',
          duration: 1000
        })
      }
    },
    // 批量导入
    showDialogImport(){
      actionTracking('add_controlled_goods_click', {
        add_controlled_goods : 'batch_import'
      });
      this.downloadUrl = '';
      this.files = "";
      this.fileName = "";
      this.centerDialogImport = true;
    },
     // 导出商品
    exportExcel(){
      const that = this;
      if (!that.tableData.allList || !that.tableData.allList.length) {
        that.$message({
          type: 'info',
          message: '无导出数据！'
        });
        return false;
      }
      let url = ``;
      exportControlProducts().then(res => {
        if (res.hasOwnProperty('code')) {
          that.$message.error(res.message);
        } else {
          // 申请接口带入参数查询数据
          url = `${process.env.VUE_APP_BASE_API}/exportControlProducts`;
          const a = document.createElement('a');
          a.href = url;
          a.click();
        }
      });
    },
    /*****批量导入 */
    handLaoderSuccess(data) {
      let that = this
      this.isLoading = false
      if (!data) {
        return false
      }
      if (data.code == 0) {
        if (data.data.length > 0) {
          this.handerProduct.map(function(item) {
            that.tableData.allList.push(item)
          })
          that.tableData.allList = this.removeDuplicateObject(
            that.tableData.allList
          )
        }

        this.$message({
          message: '导入成功',
          type: 'success',
          duration: 1000
        })
      } else {
        this.$message({
          message: data.message,
          type: 'error',
          duration: 1000
        })
      }
    },
    //选择已选商品
    seleteShopHander(val) {
      this.changeProduct = val
    },
    //重置条件
    resetForm() {
      this.productSearch = {
        barcode: '',
        showName: '',
        status: '',
        erpCode:'',
        code:'',
        approvalNumber:''
      }
      this.productPage = {
        pageNum: 1,
        pageSize: 20
      }
      this.getProductList()
    },
    //获取商品列表
    getProductList() {
      this.isProductLoad = true
      let param = {
        ...this.productSearch,
        ...this.productPage,
      }
      findSkuList(param).then((res) => {
        if (res.code == 0) {
          this.productData.list = res.data.list
          this.productData.total = res.data.total
        }else{
            this.$message({
            message: res.message,
            type: 'error',
          })
        }
        this.isProductLoad = false
        }).catch(error => {
          this.$message({
            message: "初始列表失败",
            type: 'error'
        });
      })
    },
    //选择商品
    seleteCustomerHander(val) {
      this.handerProduct = val
    },
    //取消选择商品
    outerVisibleClick() {
      this.outerVisible = false
    },
    //确定选择商品
    sureInnerVisible() {
      let that = this
      if (this.handerProduct.length > 0) {
        this.handerProduct.map(function(item) {
          that.tableData.allList.push(item)
        })
        that.tableData.allList = this.removeDuplicateObject(
          that.tableData.allList
        )
        that.tableData.list = that.tableData.allList.slice(0, this.pageData.pageSize)
        that.tableData.total = that.tableData.allList.length
        that.getHasControl(that.tableData.list);
        that.outerVisible = false
      } else {
        this.$message({
          message: '请选择要控销的商品',
          type: 'warning'
        })
      }
    },
    //上一步
    PreStep() {
      this.$emit('goPrev', { from: 'choosePro' })
    },
    //下一步
    toAddUser() {
      if (this.tableData.allList.length <= 0) {
        this.$message({
          message: '请先选择商品',
          type: 'warning'
        })
        return
      }
      this.$emit('goNext', { from: 'choosePro', data: this.tableData.allList })
    },
    //
    removeDuplicateObject(arr) {
      console.log(arr)
      const map = new Map()
      for (const item of arr){
        if (!map.has(item.id)){
          map.set(item.id,item)
        }
      }
      console.log([...map.values()])
      return [...map.values()]
      // let temp = arr.map((item) => {
      //   return JSON.stringify(item)
      // })
      // temp = Array.from(new Set(temp))
      // return temp.map((item) => {
      //   return JSON.parse(item)
      // })
    },
    async getHasControl(ary) {
      if (Array.isArray(ary) && ary.length > 0) {
        const ids = ary.map(item => {
          return item.id;
        })
          .filter(id => !!id);
        const res = await hasControl(ids);
        if (res && res.code === 0) {
          this.tableData.list = this.tableData.list.map(item => {
            item.hasControl = res.data[item.id];
            return item;
          });
          console.log(this.tableData.list);
        }
      }
    }
  }
}
</script>

<style lang="scss" scoped>
// @import '../../../assets/css/market';
.btn-dib {
  display: inline-block;
  margin: 0 10px;
}

.con-inner {
  padding-top: 15px;
  padding-left: 23px;
  margin-right: 17px;
  padding-bottom: 10px;
  border-bottom: 1px solid #efefef;

  div {
    display: inline-block;
  }

  .img {
    width: 92px;
    height: 92px;

    img {
      width: 100%;
      height: 100%;
    }
  }

  .text {
    padding-left: 20px;
    vertical-align: top;

    h3 {
      font-size: 14px;
      color: #000000;
      padding: 0;
      margin: 0;
    }

    p {
      padding: 0;
      margin: 0;
      font-size: 12px;
      color: #333333;
      padding-top: 10px;
    }
  }

  .btn {
    float: right;
    padding-top: 26px;

    button {
      width: 100px;
      height: 30px;
      text-align: center;
      line-height: 30px;
      padding: 0;
      background: rgba(65, 131, 213, 1);
      border-color: rgba(65, 131, 213, 1);
      border-radius: 4px;
      font-size: 14px;
    }

    a {
      color: #ffffff;
      text-decoration: none;
    }

    .router-link-active {
      color: #ffffff;
      text-decoration: none;
    }
  }
}

.pag-info {
  width: 500px;
}

.upload-tips {
  font-size: 12px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #999999;
  line-height: 24px;
}
.file-name {
  font-size: 12px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: rgba(51, 51, 51, 1);
  line-height: 24px;
}

::v-deep   .my-label .el-form-item__label {
  border: 1px solid #d9d9d9;
  border-radius: 4px 0px 0px 4px;
  padding: 0 9px 0 9px;
  height: 30px;
  vertical-align: bottom;
  border-right: 0;
  font-size: 12px;
  line-height: 28px;
}
::v-deep   .my-label2 .el-form-item__label {
  border: 1px solid #d9d9d9;
  border-radius: 4px 0px 0px 4px;
  padding: 0 20px 0 9px;
  height: 30px;
  vertical-align: bottom;
  border-right: 0;
  font-size: 12px;
  line-height: 28px;
}

::v-deep   .my-label .el-input__inner {
  border-radius: 0px 4px 4px 0px;
}

::v-deep   .el-range-editor--small.el-input__inner {
  height: 30px;
}
::v-deep   .el-input--small .el-input__inner {
  height: 30px;
  line-height: 30px;
  font-size: 12px;
}
.status-0 {
  border-color: #afafbf;
  background: #afafbf;
}
.status-1 {
  border-color: #4183D5;
  background: #4183D5;
}
.noData p {
  line-height: 38px;
  font-size: 13px;
  color: #999999;
  padding: 0;
}
::v-deep   .el-dialog__body {
    padding: 16px 20px;
    color: #606266;
    font-size: 14px;
    word-break: break-all;
}
::v-deep  .el-table {
  .el-checkbox__inner {
    border: 1px solid #000000;
  }
}
.describe{
  color: red;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.downLoader-btn{
  color: #409eff;
}
</style>
