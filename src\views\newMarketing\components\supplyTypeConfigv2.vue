<template>
    <div>
      <el-form label-width="150px" class="activity-info">
        <el-form-item label="供货信息配置方式：" prop="scopeType" class="is-required">
          <el-radio-group v-model="supplyInfo.scopeType"  >
            <el-radio :label="-1" @change="changeSupplyType">复制原活动</el-radio>
            <el-radio :label="1" @change="changeSupplyType">人群</el-radio>
            <el-radio :label="2" @change="changeSupplyType">商圈/供货对象</el-radio>
          </el-radio-group>

        </el-form-item>
        <el-form-item v-if="supplyInfo.scopeType === 1" label="人群范围：" class="is-required">
          <el-button
            size="small"
            type="primary"
            plain
         
            @click="dialogVisible = true"
          >选择人群</el-button>
          <span style="padding-left: 10px" v-if="baseCustomerGroupId || peopleInfo.id">
            已选：人群ID：{{ peopleInfo.id || baseCustomerGroupId }} ；人群名称：{{ peopleInfo.name || baseCustomerGroupName }}
          </span>
          <el-button
            v-if="peopleInfo.id"
            type="text"
          
            @click="seeCrowd(peopleInfo.id)"
          >查看详情</el-button>
        </el-form-item>

        <el-form-item v-if="supplyInfo.scopeType === 2 && supplyInfo.controlRosterType != 2" label="商圈：" prop="isCopyBusArea" label-width="150px" class="is-required">
          <el-radio-group v-model="supplyInfo.isCopyBusArea" >
            <el-radio :label="-1">复制原活动</el-radio>
            <el-radio :label="2">自定义</el-radio>
          </el-radio-group>
         
        </el-form-item>

        <el-form-item v-if="supplyInfo.isCopyBusArea === 2 && supplyInfo.scopeType === 2 && supplyInfo.controlRosterType != 2" label-width="170px" label="业务商圈：" class="is-required">
          <el-button size="small" type="primary"  @click="selectBusinessCircle">选择商圈</el-button>
          <span style="padding-left: 10px" v-if="businessCircleInfo.busAreaName">
            已选：{{ businessCircleInfo.busAreaName }}
            <el-button v-if="businessCircleInfo.busAreaName" type="text" class="btn-info" @click="checkDetails">查看商圈</el-button>
          </span>
        </el-form-item>

        <el-form-item v-if="supplyInfo.scopeType === 2 && supplyInfo.controlRosterType != 1" label="供货对象：" class="is-required" prop="isCopyControlUser" label-width="150px">
          <el-radio-group v-model="supplyInfo.isCopyControlUser" >
            <el-radio :label="-1">复制原活动</el-radio>
            <el-radio :label="2">自定义</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="供货对象:" label-width="170px" prop="controlUserTypes" class="is-required" v-if="supplyInfo.isCopyControlUser === 2 && supplyInfo.scopeType === 2 && supplyInfo.controlRosterType != 2">
          <div class="customerType" style="padding-left: 8px;">
            <el-checkbox
              :indeterminate="isIndeterminate"
              v-model="checkAll"
             
              @change="handleCheckAllChange"
              class="checkedall"
            >全选</el-checkbox>
            <el-checkbox-group
              v-model="supplyInfo.controlUserTypes"
              
              @change="handleCheckedTypesChange"
            >
              <el-checkbox
                v-for="(item,index) in customerTypes"
                :label="item.id"
                style="margin-bottom:10px;"
                :key="index"
              >{{item.name}}</el-checkbox>
            </el-checkbox-group>
          </div>
        </el-form-item>
        <!-- <el-form-item v-if="supplyInfo.isCopyControlRoster === 2 && supplyInfo.scopeType === 3" label="黑白名单：" prop="controlRosterType">
        <el-radio-group v-model="supplyInfo.controlRosterType" :disabled="disabled && subOtherDisabled">
          <el-radio :label="1" @click.native.prevent="clickBlackNameList">黑名单控销组</el-radio>
          <el-radio :label="2" @click.native.prevent="clickWhiteNameList">白名单控销组</el-radio>
        </el-radio-group>
        <div class="div-info">
          <el-button type="primary" v-if="supplyInfo.controlRosterType === 1 || supplyInfo.controlRosterType === 2" :disabled="disabled && subOtherDisabled" size="small" @click="toSelectControlGroups">选择控销组</el-button>
          <span style="padding-left: 10px" v-if="supplyInfo.controlGroupName && supplyInfo.controlRosterType">
            <span>已选“{{supplyInfo.controlGroupName}}”</span>
            <el-button type="text" class="btn-info" @click="seeMerchantDetail">查看药店明细</el-button>
          </span>
        </div>
        <div class="tipBox redText">
          <p>活动维度可见性分为三种场景：</p>
          <p>场景1：未选择黑名单控销组，则商圈内指定客户类型可享受该活动</p>
          <p>场景2：选择了黑名单控销组，则仅商圈内指定客户类型-黑名单客户可享受该活动。</p>
          <p>场景:3：选择了白名单控销组，则仅白名单的客户可享受该活动。</p>
        </div>
      </el-form-item> -->
      </el-form>
      <!-- 选择人群 -->
      <crowd-selector-dialog
        ref="changePeople"
        @onSelect="sendPeopleData"
        v-if="dialogVisible"
        v-model="dialogVisible"
        :selected="peopleInfo.id"
      ></crowd-selector-dialog>
      <!-- 查看客户信息 -->
      <CustomerInfoLog
        v-if="showCustomer"
        :market-customer-group-id="peopleInfo.id"
        @cancelModal="cancelDialog"
      />



      <!-- 选择商圈 -->
      <select-business-circle
        v-if="isSelectBusinessCircle"
        :selected.sync="businessCircleInfo.busAreaId"
        fromComp="groupActivityTheme"
        @onDialogChange="onDialogChange"
        @selectChange="selectChange"
      ></select-business-circle>
      <!-- 查看商圈 -->
      <view-business-circle
        :row="{id:businessCircleInfo.busAreaId}"
        v-if="viewBusinessDialog"
        @onDialogChange="onDialogChange"
      ></view-business-circle>
      <!-- 查看药店明细 -->
      <ListOfControlGroups
        v-if="showMerchantInfo"
        :control-group-id="supplyInfo.controlGroupId"
        @cancelDialog="cancelDialog"
      />
      <!-- 选择控销组 -->
      <SelectControlGroups
        v-if="showSelectControlGroups"
        :show-select-control-groups.sync="showSelectControlGroups"
        :selected-id="supplyInfo.controlGroupId"
        @selectGroupChange="selectGroupChange"
      />
    </div>
  </template>
  <script>
  import CrowdSelectorDialog from '@/components/xyy/customerOperatoin/crowd-selector-dialog.vue'
  import selectBusinessCircle from "@/views/product/components/selectBusinessCircle";
  import viewBusinessCircle from '@/views/business-circle/components/viewBusinessCircle';
  import ListOfControlGroups from '@/views/product/components/listOfControlGroups';
  import SelectControlGroups from '@/views/product/components/selectControlGroups';
  import { findUserTypes } from '@/api/product';
  import CustomerInfoLog from '@/components/customer/customerInfoLog.vue';

  export default {
    components: { CrowdSelectorDialog, selectBusinessCircle, viewBusinessCircle, SelectControlGroups, ListOfControlGroups, CustomerInfoLog },
    props: {
      dis: Boolean,
      saleScopeDto: Object,
      disabled: Boolean,
      subOtherDisabled: Boolean,
      baseCustomerGroupId: Number,
      baseCustomerGroupName: String,
      isShopUpdate: {
        type: Boolean,
        default: false
      }
    },
    data() {
      return {
        isSelectBusinessCircle: false,
        viewBusinessDialog: false,
        businessCircleInfo: { // 商圈信息
          busAreaId: '',
          busAreaName: '',
        },
        dialogVisible: false,
        peopleInfo: { // 人群信息
          name: '',
          id: '',
        },
        showCustomer: false,
        showMerchantInfo: false, // 查看药店明细
        showSelectControlGroups: false, // 选择控销组
        isIndeterminate: false,
        checkAll: false,
        customerTypes: [],
        supplyInfo: {
          isCopyBusArea: -1,
          controlRosterType: 0,
          scopeType: -1,
          controlGroupId: '',
          controlGroupName: '',
          controlUserTypes: [],
          isCopyControlRoster: 2,
          isCopyControlUser: 2,
        },
        fromType: '',
      };
    },
    watch: {
      saleScopeDto: { // 深度监听，可监听到对象、数组的变化
        handler(val) {
          if (!this.isShopUpdate) {
            if (Object.keys(val).length) {
              // this.saleScopeDto = val;
              this.initData();
            } else {
              this.supplyInfo = {
                isCopyBusArea: 2,
                controlRosterType: 0,
                scopeType: '',
                controlGroupId: '',
                controlGroupName: '',
                controlUserTypes: [],
                isCopyControlRoster: 2,
                isCopyControlUser: 2,
              };
              this.businessCircleInfo = { // 商圈信息
                busAreaId: '',
                busAreaName: '',
              };
              this.peopleInfo = { // 人群信息
                name: '',
                id: '',
              };
            }
          }
        },
        deep: true, // true 深度监听
      },
    },
    activated() {
      // this.findUserTypes();
      this.fromType = this.$route.query.fromType;
    },
    created() {
      this.findUserTypes();
      this.fromType = this.$route.query.fromType;
    },
    mounted() {
      if (this.isShopUpdate) {
        this.initData();
      }
    },
    methods: {
      initData() {
        Object.keys(this.supplyInfo).forEach((key) => {
          this.supplyInfo[key] = this.saleScopeDto[key];
        });
        if (this.saleScopeDto.controlUserTypes) {
          this.supplyInfo.controlUserTypes = this.saleScopeDto.controlUserTypes.split(',').map(i => Number(i));
        }
        this.businessCircleInfo = { // 商圈信息
          busAreaId: this.saleScopeDto.busAreaId || '',
          busAreaName: this.saleScopeDto.busAreaName || '',
        };
        this.peopleInfo = {
          id: this.saleScopeDto.customerGroupId,
          name: this.saleScopeDto.customerGroupName,
        };
   
      },
      // 添加人群*
      sendPeopleData(value) {
        this.peopleInfo = {
          name: value.tagName,
          id: value.id,
        };
      },
      seeCrowd() {
        this.showCustomer = true;
      },
      // 选择商圈
      selectBusinessCircle() {
        this.isSelectBusinessCircle = true;
      },
      // 查看商圈详情
      checkDetails() {
        this.viewBusinessDialog = true;
      },
      onDialogChange() {
        this.isSelectBusinessCircle = false;
        this.viewBusinessDialog = false;
      },
      selectChange(row) {
        if (row) {
          this.businessCircleInfo.busAreaId = row.id;
          this.businessCircleInfo.busAreaName = row.busAreaName;
        }
      },
      // 获取用户类型
      findUserTypes() {
        findUserTypes().then((res) => {
          if (res.code == 0) {
            this.customerTypes = res.data;
          } else {
            this.$message({
              message: res.message,
              type: 'warning',
            });
          }
        });
      },
      // 供货对象
      handleCheckAllChange(val) {
        const checkAllId = this.customerTypes.map((item => item.id));
        this.supplyInfo.controlUserTypes = val ? checkAllId : [];
        this.isIndeterminate = false;
      },
      handleCheckedTypesChange(value) {
        const checkedCount = value.length;
        this.checkAll = checkedCount === this.customerTypes.length;
        this.isIndeterminate = checkedCount > 0 && checkedCount < this.customerTypes.length;
      },
      // 点击黑名单
      clickBlackNameList(e) {
       
        this.supplyInfo.controlGroupId = '';
        this.supplyInfo.controlGroupName = '';
        if (this.supplyInfo.controlRosterType === 1) {
          this.supplyInfo.controlRosterType = 0;
        } else {
          this.supplyInfo.controlRosterType = 1;
          // this.supplyInformationVoRules.controlUserTypes.forEach((itemRules) => {
          //   if (Object.prototype.hasOwnProperty.call(itemRules, 'required')) {
          //     itemRules.required = true;
          //   }
          // });
        }
      },
      clickWhiteNameList(e) {
       
        if (this.supplyInfo.controlRosterType == 2) {
          this.supplyInfo.controlRosterType = 0;
        } else {
          this.$confirm('选择白名单控销组后，仅白名单控销组内的客户可以购买此商品，确定使用白名单控销组吗？', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
          })
            .then(() => {
              this.supplyInfo.controlGroupId = '';
              this.supplyInfo.controlGroupName = '';
              this.supplyInfo.controlRosterType = 2;
              this.supplyInfo.isCopyBusArea = 2;
              this.businessCircleInfo = { // 商圈信息
                busAreaId: '',
                busAreaName: '',
              };
              this.supplyInfo.isCopyControlUser = 2;
              this.supplyInfo.controlUserTypes = [];
            })
            .catch(() => {});
        }
      },
      seeMerchantDetail() {
        this.showMerchantInfo = true;
      },
      selectGroupChange(row) {
        this.supplyInfo.controlGroupId = row.id; // 用户组id
        this.supplyInfo.controlGroupName = row.name;
      },
      cancelDialog() {
        this.showMerchantInfo = false;
        this.showSelectControlGroups = false;
        this.showCustomer = false;
      },
      toSelectControlGroups() {
        this.showSelectControlGroups = true;
      },
      getAllSupplyInfo() {
        let params = {
          ...this.supplyInfo,
          customerGroupId: this.peopleInfo.id || null,
          busAreaId: this.businessCircleInfo.busAreaId,
        }
        if(this.supplyInfo.scopeType ==-1){
          params.busAreaId="";
           params.controlUserTypes = [];
           params.controlGroupId=""
           params.controlGroupName=""
           params.isCopyBusArea=""
           params.isCopyControlUser=""
           params.isCopyControlRoster = ""
           params.controlRosterType=""
           params.customerGroupId="";
    
    
        }
        if (this.supplyInfo.scopeType ==1) {
           params.busAreaId="";
           params.controlUserTypes = [];
           params.controlGroupId=""
           params.controlGroupName=""
           params.isCopyBusArea=""
           params.isCopyControlUser=""
           params.isCopyControlRoster = ""
           params.controlRosterType=""

      }
      if (this.supplyInfo.isCopyBusArea === 2) {
        params.customerGroupId="";

    

      }
      
      params.controlUserTypes=params.controlUserTypes.join(",")
        return params;
      },
      changeSupplyType(){
        this.$emit('changeSupplyTypeEvent',this.supplyInfo.scopeType)
      },
    },
  };

  </script>
  <style lang="scss" scoped>
    .tipBox {
      color: #909399;
      font-size: 12px;
      p {
        line-height: 18px;
      }
    }
    .redText {
      color: #ff3024;
    }
    .customerType {
      border: 1px solid #eeeeee;
      border-radius: 4px;
      max-height: 260px;
      overflow-y: auto;
      ::v-deep  .el-checkbox {
        width: 14%;
        margin-left: 10px;
      }
      ::v-deep  .checkedall {
        width: 100%;
        padding: 10px;
        margin-left: 0;
        margin-bottom: 10px;
      }
      ::v-deep  .el-checkbox__input.is-checked + .el-checkbox__label {
        color: #333333;
      }
    }
  </style>
