<template>
  <div class="bannerNoticeContainer">
    <div ref="banner" class="banner" v-if="shopConfig.indexShow&& showBanner">
      <el-carousel  @change="onBannerChange" trigger="click"  :interval="5000" :class="carouselName">
      <el-carousel-item v-for="(item,index) in bannerList" :key="item.id" :label="index+1">
        <div class="small" @click="openNotice(item.id)">
          <!--          <img :src="item.url" alt="">-->
          <el-image style="width: 100%; height: 100%" :src="item.url" fit="contain"></el-image>
        </div>
      </el-carousel-item>

    </el-carousel>

  </div>
<div class="task">
  <div class="task-item">
    <div class="task_title">平台待办 <i class="iconfont el-icon-arrow-right" style="cursor:pointer" @click="plaformTodoDialog = true"></i></div>
    <div class="plaformTodo">
      <div @click="qualtificationOpening" v-if="qualtificationInfos.size">
        <span class="platformTodo-title">资质过期/临期</span>

        <div class="platformTodo-num">{{qualtificationInfos.size}}</div>
      </div>
      <div   @click="dialog.visible.contract = true" v-if="dialog.contractNum">
        <span class="platformTodo-title">协议签署提醒</span>

        <div class="platformTodo-num">{{ dialog.contractNum }}</div>
      </div>
      <div @click="dialog.visible.openShop = true" v-if="dialog.openShopTaskNum">
        <span class="platformTodo-title">开店事项待办提醒</span>
        <div class="platformTodo-num">{{dialog.openShopTaskNum}}</div>
      </div>
      <div>
      </div>
    </div>
  </div>
     <div class="task-item">
      <div class="task_title">拼团活动 <i class="iconfont el-icon-arrow-right" style="cursor:pointer"></i></div>
    <div class="groupActive">
      <div    @click="collageActivityOpening(null, 'activity_Participate')" v-if="canReportBaseNum">
        <span class="groupActive-title">待参与拼团活动</span>

        <div class="groupActive-num">{{ canReportBaseNum }}</div>
      </div>
      <div      @click="collageActivityOpening({rejectNum: true}, 'activity_reject')" v-if="rejectNum">
        <span class="groupActive-title">拼团活动驳回待修改</span>

        <div class="groupActive-num">{{ rejectNum }}</div>
      </div>
      <div  @click="collageActivityOpening({priceTooLowPrice: true}, 'low_price')" v-if="priceTooLowPrice">
        <span class="groupActive-title">拼团价过低</span>

        <div class="groupActive-num">{{ priceTooLowPrice }}</div>
      </div>
      <div style="cursor:pointer" v-if="!canReportBaseNum&&!rejectNum&&!priceTooLowPrice">
        <span class="groupActive-title">暂无平台活动</span>

      </div>
      <div>

      </div>
    </div>
     </div>

    </div>




    <el-dialog
      :title="title"
      :visible.sync="dialogVisible"
      width="70%">
      <div v-html="content"></div>
      <span slot="footer" class="dialog-footer">
  </span>
    </el-dialog>
    <el-dialog title="协议签署处理提醒" :visible.sync="dialog.visible.contract" width="600px">
        <p style="color:#ff8400;">当前店铺存在待签署的任务，请在“处理截止时间”前处理完毕，逾期店铺将被自动下线。</p>
        <p style="color:#ff8400;">如需稍后出库，可到【企业管理】-【平台服务协议】进行处理</p>
        <el-table :data="dialog.contractTableData" border fit>
            <el-table-column label="任务名称" align="center" prop="taskName">
            </el-table-column>
            <el-table-column label="处理截止时间" align="center">
                <template slot-scope="scope">
                    <p style="color:#ff8400;">{{ scope.row.deadline == null ? '-' :  scope.row.deadline}}</p>
                </template>
            </el-table-column>
        </el-table>
        <div slot="footer" class="dialog-footer">
            <el-button size="small" @click="dialog.visible.contract = false">稍后处理</el-button>
            <el-button type="primary" size="small" @click="handle('/companyOpenAccount/platformServeAgreement')">去处理</el-button>
        </div>
    </el-dialog>

    <el-dialog title="开店事项处理提醒" :visible.sync="dialog.visible.openShop" width="700px">
        <p>
            <span>请按要求完成下方列表中的任务，全部完成后店铺将自动变更为“<span style="color:#00c563;">已上线</span>”可正常经营</span>
        </p>
        <p>
            <span>如需查看店铺任务完成进度，可到首页<span style="color: red;">【待办任务】—【平台活动】</span>中查询</span>
        </p>
        <el-table :data="dialog.openShopTableData" border fit>
            <el-table-column label="任务名称" align="center" prop="taskName">
            </el-table-column>
            <el-table-column label="任务说明">
                <template slot-scope="scope">
                    <p style="color:#ff8400;">{{ scope.row.taskExplain }}</p>
                </template>
            </el-table-column>
            <el-table-column label="状态" align="center">
                <template slot-scope="scope">
                    <span :class="scope.row.status == '已完成' ? 'finished' : 'doing'">{{ scope.row.status }}</span>
                    <span class="work" v-if="scope.row.status == '未完成'" @click="handle(scope.row.url)">去处理</span>
                </template>
            </el-table-column>
        </el-table>
        <div slot="footer" class="dialog-footer">
            <el-button type="primary" size="small" @click="dialog.visible.openShop = false;">知道了</el-button>
        </div>
    </el-dialog>
    <el-dialog v-if="isBankAccount || todoConfig.applyNationalBusinessCircle" title="平台待办" :visible.sync="plaformTodoDialog" width="width">
      <div v-if="isBankAccount" @click="accountOpening" style="cursor:pointer;margin:20px 0;">
        企业开户
      </div>
      <div @click="toStoreAreaSalesControl" style="cursor:pointer"  v-if="todoConfig.applyNationalBusinessCircle">
        待申请全国商圈
      </div>
        <div slot="footer">
            <el-button @click="plaformTodoDialog = false">取 消</el-button>
            <el-button type="primary" @click="plaformTodoDialog = false">确 定</el-button>
        </div>
    </el-dialog>
  </div>

</template>

<script>
import {bannerList, bannerDetail,selectPendingHandleNum,platformActivity,waitDeal} from '@/api/home'
import {mapState} from 'vuex'
import { actionTracking } from '@/track/eventTracking';

import { signTaskRemind, taskRemind } from '../../../api/home/<USER>'
let exposureTool = null;
const transactionMap = {
  waitOpenAccountOrderCount: 'no_opened', // 待开户订单数
  waitExamineOrderCount: 'no_approved', // 待审核订单数
  waitDeliveryOrderCount: 'no_shipped', // 待发货订单数
  timeoutOrderCount: '48h_no_shipped', // 超48小时未发货订单数
  waitExamineRefundOrderCount: 'no_approved_refund', // 待审核退款订单数
  pullFailedOrderCount: 'failed_ERP', // 下推erp失败订单数
  logisticsTrackFailCount: 'failed_track', // 物流轨迹获取失败
  logisticsTrackIllegalCount: 'illegal_track', // 物流轨迹异常提醒
  evidenceToExamine: 'wire_transfer', // 待审核电汇
  nonInvoiceOrderCount: 'no_invoice', // 未上传电子发票
  afterSalesCount: 'no_afterSale'
};
const exposuredBanner = [];
export default {
  name: "Notice",
  data() {
    return {
      todoConfig: {
        waitOpenAccountOrderCount: 0, // 待开户订单数
        waitExamineOrderCount: 0, // 待审核订单数
        waitDeliveryOrderCount: 0, // 待发货订单数
        timeoutOrderCount: 0, // 超48小时未发货订单数
        waitExamineRefundOrderCount: 0, // 待审核退款订单数
        pullFailedOrderCount: 0, // 下推erp失败订单数
        nearEffectSkuCount: 0, // 近效期商品数
        sellStockOutSkuCount: 0, // 在售缺货
        expireAutoOutMarketSkuCount: 0, // 即将过期自动下架商品数量
        priceOSkuCount: 0, // 售价为0
        waitPutAwaySkuCount: 0, // 待上架商品数量
        noPassSkuCount: 0, // 审核不通过商品数量
        authOffShelfCount: 0, // 信息有误下架
        errorInfoCount: 0, // 信息有误商品数
        logisticsTrackFailCount: 0, // 物流轨迹获取失败
        logisticsTrackIllegalCount: 0, // 物流轨迹异常提醒
        evidenceToExamine: 0, // 待审核电汇
        partialShipmentOrderCount: 0,
        lowPriceCount: 0, // 价格过低数量
        nonInvoiceOrderCount: 0, // 未上传电子发票数量
        autoSaleWithStockCount: 0, // 来货自动上架数量
		    afterSalesCount: 0,   //未处理售后单
        applyNationalBusinessCircle: false,   //全国商圈

        qualificationApplyUnUploadCount: 0, //客户已申请未上传商品首营资质
        drugReportApplyUnUploadCount: 0, //客户已申请未上传药检报告
      },
      isBankAccount: false, // true展示企业开户，false不展示
      plaformTodoDialog:false,
      bannerHeight: 0,
      bannerWidth: 0,
      showBanner: false,
      activeId: '',
      bannerList: [],
      dialogVisible: false,
      title: '公告详情',
      content: '',
      dialog: {           //开店任务每日提醒和协议签署每日提醒
        visible: {
          openShop: false,
          contract: false
        },

        contractTableData: [],
        openShopTableData: [],
        openShopTaskNum: 0,
        contractNum: 0
      },
       canReportBaseNum: 0, // 可报名框架数量
        rejectNum: 0, // 驳回数量
        priceTooLowPrice: 0, // 价格过低数量
    }
  },

  computed: {
    ...mapState('app', ['shopConfig', 'qualtificationInfos']),
    carouselName() {
      if (this.bannerList.length === 1) {
        return 'onlyOne'
      }
      return ''
    }
  },
  watch: {
    'bannerWidth'(newValue, oldValue) {
      this.bannerHeight = (230 / 900) * newValue
    }
  },
  created() {
    console.log(11111);
    this.GetWaitDeal();
    this.getPlatformActivity();
    signTaskRemind().then(res => {
      if (res.code == 0) {
        this.dialog.visible.contract = res.result.notify == 1;
        this.dialog.contractTableData = res.result.signTaskList;
        this.dialog.contractNum = res.result.num;
      }
    })
    this.getBannerList()
    taskRemind().then(res => {
      if (res.code == 0) {
        this.dialog.visible.openShop = res.result.notify == 1;
        this.dialog.openShopTableData = res.result.signTaskList;
        this.dialog.openShopTaskNum = res.result.num;
      }
    })
    selectPendingHandleNum().then((res) => {
        if (res.code === 1000) {
          this.rejectNum = res.data.actReportPendingHandleNumVo.rejectNum;
          this.canReportBaseNum = res.data.actReportPendingHandleNumVo.canReportBaseNum;
          this.priceTooLowPrice = res.data.actReportPendingHandleNumVo.priceTooLowPrice;
        }
      })
  },

  beforeMount() {
    window.addEventListener('resize', this.resizeHandler)
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.resizeHandler)
  },
  methods: {
    async GetWaitDeal() {
      const res = await waitDeal();
      if (res && res.code === 0) {
        Object.keys(res.result).map((key) => {
          this.$set(this.todoConfig, key, res.result[key]);
        });
        if (exposureTool) {
          exposureTool.end();
        } else {
          exposureTool = new VNodeExposureTool(document.querySelector(".home"), (item)=> {
            let name = item.$vnode.data.ref.replace('exposure_mark_', '');
            // 曝光埋点
            do {
              let content = transactionMap[name]
              if (content) {
                actionTracking('agency_task_transaction_exposure', {
                  agency_task : content
                })
                break;
              }
              content = commodityMap[name]
              if (content) {
                actionTracking('agency_task_commodity_exposure', {
                  agency_task : content
                })
                break;
              }
            } while (false)
          })
        }
        this.$nextTick(()=>{
          let refs = Object.keys(this.$refs).filter((item) => {
            return item.startsWith('exposure_mark')
          }).map((key) => {
            return this.$refs[key]
          })
          exposureTool.begin(refs)
        })
      }
    },
    async getPlatformActivity() {
      const res = await platformActivity();
      if (res && res.code === 0) {
        this.isBankAccount = res.result.bankAccount;
      }
      selectPendingHandleNum().then((res) => {
        if (res.code === 1000) {
          this.rejectNum = res.data.actReportPendingHandleNumVo.rejectNum;
          this.canReportBaseNum = res.data.actReportPendingHandleNumVo.canReportBaseNum;
          this.priceTooLowPrice = res.data.actReportPendingHandleNumVo.priceTooLowPrice;
          this.taskActivityViewTrack([{ key: 'activity_reject', value: this.rejectNum }, { key: 'activity_Participate', value: this.canReportBaseNum }, { key: 'low_price', value: this.priceTooLowPrice }]);
        }
      });
    },
    taskActivityViewTrack(valueArr) {
      valueArr.forEach((item) => {
        if (item.value) {
          actionTracking('agency_task_activity_exposure', { agency_task_activity: item.key });
        }
      });
    },
    handle(path) {
        window.openTab(path);
    },
    toStoreAreaSalesControl() {
      if(this.$store.state.permission.menuGray == 1) {
        window.openTab('/shopBusinessManage?to=storeAreaSalesControl')
      }else {
        window.openTab('/storeAreaSalesControl')
      }
    },
    accountOpening() {
      window.openTab('/companyOpenAccount');
    },
    collageActivityOpening(obj, key) {
      actionTracking('agency_task_activity_click', { agency_task_activity: key });
      window.openTab('/collageActivity', obj || '');
    },
    qualtificationOpening() {
      window.openTab('/qualtification');
    },
    onBannerChange(newIndex){
      if(exposuredBanner.includes(newIndex)) return;
      exposuredBanner.push(newIndex)
      if(this.bannerList && this.bannerList.length > newIndex) {
        actionTracking('banner_exposure', {
          banner_id: this.bannerList[newIndex].id
        })
      }
    },

    async getBannerList() {
      const res = await bannerList()
      if (res && res.code === 0 && Array.isArray(res.result)) {
        if (res.result.length > 0) {
          this.showBanner = true
        }
        this.bannerList = res.result
        exposuredBanner.length = 0;//清空已曝光
        if(this.bannerList && this.bannerList.length > 0) {
          //上报第一个banner
          this.onBannerChange(0)
        }
        setTimeout(() => {
          this.resizeHandler()
        }, 300)
      }
    },
    async openNotice(id) {
      if(this.bannerList ) {
        actionTracking('banner_click', {
          banner_id: id
        })
      }
      console.log(id)
      const res = await bannerDetail(id)
      if (res && res.code === 0) {
        const {title, content} = res.result
        this.title = title
        this.content = content
        this.dialogVisible = true
      } else {
        this.$message(res.msg)
      }
    },
    resizeHandler() {
      if (this.$refs.banner) {
        this.bannerWidth = this.$refs.banner.offsetWidth
      }
    }
  }
}
</script>

<style scoped lang="scss">
.bannerNoticeContainer{
  display:flex;
  justify-content: space-between;
  column-gap:10px;
  padding: 0 0 16px;
  border-radius:3px;
}
.banner {

  width:50%;
  flex:1;
  border-radius: 3px;
  .small {
    width: 100%;
    height: 100%;
    background: #fff;
    border-radius: 2px;
    display: flex;
    justify-content: center;
    align-items: center;

    img {
      width: 100%;
      height: 100%;
      border: none;
      display: inline-block;
      object-fit: scale-down
    }
  }

  .onlyOne ::v-deep   .el-carousel__indicators {
    display: none;
  }
  ::v-deep   .el-carousel{
    height:100%;
  }
  ::v-deep   .el-carousel__container{
    height:100%;
  }
  ::v-deep   .el-carousel__indicators {
    bottom: 15px;
    text-align: right;
    padding-right: 32px;


    .el-carousel__indicator {
      margin-right: 4px;
      font-size: 12px;
      padding: 0;
      text-align: center;

      .el-carousel__button {
        color: #fff;
        width: 14px;
        line-height: 14px;
        padding: 0;
        background-color: rgba(0, 0, 0, 0.1);
      }
    }

    .el-carousel__indicator.is-active {
      .el-carousel__button {
        background-color: rgba(0, 0, 0, 0.5);
      }
    }
  }

}
.task{
  background-color: #fff;
  flex:1;
  border-radius: 3px;
}
.task_title{
  font-weight: bold;
  color:#111111;
}
.plaformTodo{
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  margin: 18px 0;
}
.plaformTodo div{
  cursor: pointer;
}
.platformTodo-title{
  font-size: 14px;
  color:#111111
}
.groupActive-title{
  font-size: 14px;
  color:#111111
}
.groupActive{
  display: flex;
  justify-content: space-between;
  margin: 18px 0;
}
.groupActive div{
  cursor: pointer;
}
.task-item{
  margin: 18px;
}
.platformTodo-num{
  margin-top:12px;
  font-weight: bold;
  font-size: 18px;
}
.groupActive-num{
  margin-top:12px;
  font-weight: bold;
  font-size: 18px;
  }
</style>
