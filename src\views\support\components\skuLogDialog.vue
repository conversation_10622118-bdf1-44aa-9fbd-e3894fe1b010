<template>
  <el-dialog
    title="查看操作日志"
    :visible="dialogVisible"
    width="600px"
    :before-close="handleClose">
    <el-table
      v-loading="loading"
      :data="tableConfig.data"
      border
      height="400"
      style="width: 100%">
      <el-table-column
        prop="typeName"
        label="变更类型"
        width="90"/>
      <el-table-column
        prop="remarks"
        label="变更内容"
        width="200">
        <template slot-scope="{row}">
          <div v-for="(item, index) in row.remarks" :key="index">
            {{ (row.remarks || []).length > 1 ? `${index+1}、` : '' }}{{ item }}
          </div>
        </template>
      </el-table-column>
      <el-table-column
        prop="createName"
        width="90"
        label="操作人"/>
      <el-table-column
        prop="createTimeStr"
        label="操作时间"/>
    </el-table>
  </el-dialog>
</template>

<script>
import {getSkulog} from '@/api/product'

export default {
  name: "skuLogDialog",
  props: {
    modifyConfig: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      dialogVisible: true,
      loading: true,
      tableConfig: {
        data: []
      },
    }
  },
  mounted() {
    this.getSkulog()
  },
  methods: {
    async getSkulog() {
      try {
        const res = await getSkulog({barcode: this.modifyConfig.barcode})
        if (res.code === 0) {
          this.tableConfig.data = res.data
        } else {
          // this.$message.error(res.msg)
          this.$alert(res.msg, {type: 'error'})
        }
      } catch (e) {
        console.log(e)
      }
      this.loading = false
    },
    handleClose() {
      this.$emit('update:skuLogDialogVisible', false)
    }
  }
}
</script>

<style scoped lang="scss">
::v-deep   .el-table thead th {
  background: #f9f9f9;
  border: none;

  .cell {
    font-family: PingFangSC, PingFangSC-Regular;
    font-weight: 400;
    text-align: left;
    color: rgba(51, 51, 51, 0.85);
    line-height: 22px;
  }
}

::v-deep   .el-table__body-wrapper {
  font-size: 12px;
  color: #666666;
}
</style>
