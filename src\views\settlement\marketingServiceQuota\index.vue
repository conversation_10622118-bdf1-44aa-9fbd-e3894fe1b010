<!-- 营销服务额度 -->
<template>
  <div>
    <!-- 头部查询组件 -->
    <myHeader @search="search">
      <template slot="myCom">
        <myCom :account="pageData.actualReceivedTotalAmount">
          <!-- 购买记录的弹框 -->
          <template slot="recordDialog">
            <recordDialog></recordDialog>
          </template>
          <!-- 购买额度的弹框 -->
          <template slot="rechargeDialog">
            <rechargeDialog :showBtn="true"></rechargeDialog>
          </template>
        </myCom>
      </template>
    </myHeader>
    <!-- 表格 -->
    <myTable ref="myTable"></myTable>
  </div>
</template>

<script>
import { getAccountInfo } from '@/api/settlement/marketingServiceQuota/index'

import rechargeDialog from './components/rechargeDialog.vue'
import recordDialog from './components/recordDialog.vue'
import myHeader from './components/header.vue'
import myCom from './components/myCom.vue'
import myTable from './components/myTable.vue'

export default {
  name: 'marketingServiceQuota',
  components: {
    myHeader,
    myCom,
    rechargeDialog,
    recordDialog,
    myTable
  },
  mounted() {
    // 调用接口获取当前账户数据，比如账户号码
    // this.loadData()
  },
  data() {
    return {
      pageData: '' // 账户数据，进入页面时获取
    }
  },
  methods: {
    search(val) {
      this.$refs.myTable.search(val)
    },
    loadData() {
      getAccountInfo({ fundPropertyStatus: 2 }).then((res) => {
        if( res.code == 0){
          this.pageData = res.data
        }
      })
    },
    setChangeType(val) {
      this.$refs.myTable.setChangeType(val)
    }, // 查询逻辑
  }
}
</script>

<style scoped lang="scss"></style>
