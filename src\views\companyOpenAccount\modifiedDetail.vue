<template>
  <div class="companyOpenAccount">
    <div
      class="headerStatus"
    >
      <el-button
        type="primary"
        @click="handleGoCompanyOpenAccount"
      >
        返回企业开户页面
      </el-button>
    </div>
    <div
      class="leftContent"
    >
      <div class="title_line first">企业信息</div>
      <el-form :model="corBaseVo" ref="corBaseVo" label-width="100px">
        <el-form-item label="企业名称:">
          <el-input
            v-model="corBaseVo.companyName"
            placeholder="请输入营业执照上的企业名称"
            :disabled="disabled"
            maxlength="30"
          ></el-input>
        </el-form-item>
        <el-form-item label="营业执照号:">
          <el-input
            v-model="corBaseVo.businessLicenseCode"
            placeholder="请输入营业执照上的统一社会信用代码"
            :disabled="disabled"
            maxlength="30"
          ></el-input>
        </el-form-item>
        <el-form-item label="经营范围:">
          <el-input
            v-model="corBaseVo.businessScop"
            placeholder="请输入营业执照的经营范围（只需要填写经营范围的前五项，每项用“,”分隔）"
            :disabled="disabled"
            maxlength="100"
          ></el-input>
        </el-form-item>
        <el-form-item label="注册资本金:">
          <el-input
            v-model="corBaseVo.registeredCapital"
            placeholder="请输入营业执照注册资本，请填写阿拉伯数字，单位默认“元”。例如注册资本“壹佰万元整”填写：1000000"
            :disabled="disabled"
            maxlength="20"
          ></el-input>
        </el-form-item>
        <el-form-item label="企业地址:">
          <div class="addrForm">
            <el-form-item prop="prov">
              <el-select
                v-model="corBaseVo.prov"
                placeholder="请选择营业执照住所省份"
                :disabled="disabled"
                :key="corBaseVo.prov"
              >
                <el-option
                  v-for="province in provinceList"
                  :key="province.areaId"
                  :label="province.areaName"
                  :value="province.areaName"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-select
                v-model="corBaseVo.city"
                placeholder="请选择营业执照住所城市"
                :disabled="disabled"
                :key="corBaseVo.city"
              >
                <el-option
                  v-for="city in corBaseVo.cityList"
                  :key="city.areaId"
                  :label="city.areaName"
                  :value="city.areaName"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-select
                v-model="corBaseVo.area"
                placeholder="请选择营业执照住所区县"
                :disabled="disabled"
                :key="corBaseVo.area"
              >
                <el-option
                  v-for="area in corBaseVo.areaList"
                  :key="area.areaId"
                  :label="area.areaName"
                  :value="area.areaName"
                ></el-option>
              </el-select>
            </el-form-item>
          </div>
        </el-form-item>
        <el-form-item label="注册地址:">
          <el-input
            v-model="corBaseVo.addr"
            placeholder="请输入营业执照住所"
            :disabled="disabled"
            maxlength="50"
          ></el-input>
        </el-form-item>
        <el-form-item label="营业期限:">
          <el-col :span="10">
            <el-form-item>
              <el-date-picker
                value-format="timestamp"
                placeholder="请输入营业执照的开始时间"
                type="date"
                v-model="corBaseVo.businessStartTime"
                style="width: 100%;"
                :disabled="disabled"
              ></el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="10" style="margin-right: 40px">
            <el-form-item>
              <el-date-picker
                value-format="timestamp"
                placeholder="请输入营业执照的有效期至"
                type="date"
                v-model="corBaseVo.businessEndTime"
                style="width: 100%;margin: 0 16px"
                :disabled="disabled"
              ></el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="2">
            <el-form-item>
              <el-checkbox
                v-model="corBaseVo.longTerm"
                :disabled="disabled"
              >长期</el-checkbox>
            </el-form-item>
          </el-col>
        </el-form-item>
        <el-form-item label="客服电话:">
          <el-input
            v-model="corBaseVo.customerServicePhone"
            placeholder="请输入企业的客服电话"
            :disabled="disabled"
            maxlength="20"
          ></el-input>
        </el-form-item>

        <el-form-item label="股东信息列表:">
          <div style="font-size: 12px;color: #666">
            <span>请添加一位控股股东信息</span>
            <span style="margin-left:10px">
              可登录
              <el-link
                href="https://aiqicha.baidu.com"
                target="_blank"
                type="primary"
                :underline="false"
                style="color: #5d96db;font-size:12px;text-decoration:underline;"
              >爱企查》</el-link>，查询当前企业的股东信息
              <el-button
                type="text"
                style="color: #5d96db;font-size:12px;"
                @click="lookImg(shareholdersInformationUrl)"
              >股东信息示例图》</el-button>
            </span>
          </div>

          <el-table :data="corBaseVo.shareholderInfo" style="width: 100%">
            <el-table-column label="名称" class-name="table_required" width="180">
              <template slot-scope="scope">
                <el-form-item>
                  <el-input
                    v-model="scope.row.shareholderName"
                    placeholder="请输入股东名称"
                    :disabled="disabled"
                    maxlength="30"
                  />
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column
              label="证件类型"
              class-name="table_required"
              width="180"
            >
              <template slot-scope="scope">
                <el-form-item>
                  <el-select
                    v-model="scope.row.shareholderType"
                    placeholder="请选证件类型"
                    :disabled="disabled"
                    :key="scope.row.shareholderType"
                  >
                    <el-option label="身份证" value="1"></el-option>
                    <el-option label="营业执照" value="2"></el-option>
                    <!-- <el-option label="统一信息代码" value="3"></el-option> -->
                  </el-select>
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column class-name="table_required" label="证件号码">
              <template slot-scope="scope">
                <el-form-item>
                  <el-input
                    v-model="scope.row.shareholderNumber"
                    placeholder="请输入证件号码"
                    :disabled="disabled"
                    maxlength="30"
                  />
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column class-name="table_required" label="证件发证日期">
              <template slot-scope="scope">
                <el-form-item>
                  <el-date-picker
                    v-model="scope.row.shareholderStart"
                    value-format="timestamp"
                    placeholder="请选择证件开始日期"
                    type="date"
                    style="width: 100%;"
                    :disabled="disabled"
                  />
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column class-name="table_required" label="证件到期日期">
              <template slot-scope="scope">
                <el-form-item>
                  <el-date-picker
                    v-model="scope.row.shareholderEnd"
                    value-format="timestamp"
                    placeholder="请选择证件有效期至"
                    type="date"
                    style="width: 100%;"
                    :disabled="disabled"
                  />
                </el-form-item>
              </template>
            </el-table-column>
          </el-table>
        </el-form-item>

        <el-form-item label="受益人信息列表:">
          <div style="font-size: 12px;color: #666">
            请添加一位“最终受益股份”最多的收益人
            <span style="margin-left:10px">
              可登录
              <el-link
                href="https://aiqicha.baidu.com"
                target="_blank"
                type="primary"
                :underline="false"
                style="color: #5d96db;font-size:12px;text-decoration:underline;"
              >爱企查》</el-link>，查询当前企业的收益人信息
              <el-button
                type="text"
                style="color: #5d96db;font-size:12px;"
                @click="lookImg(beneficiaryInformationUrl)"
              >受益人信息示例图》</el-button>
            </span>
          </div>
          <el-table :data="corBaseVo.beneficiaryInfo" style="width: 100%">
            <el-table-column label="名称" class-name="table_required">
              <template slot-scope="scope">
                <el-form-item>
                  <el-input
                    v-model="scope.row.favoreeName"
                    placeholder="请输入受益人名称"
                    :disabled="disabled"
                    maxlength="30"
                  />
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column label="证件类型" class-name="table_required">
              <template slot-scope="scope">
                <el-form-item>
                  <el-select
                    v-model="scope.row.favoreeType"
                    placeholder="请选证件类型"
                    :disabled="disabled"
                    :key="scope.row.favoreeType"
                  >
                    <el-option label="身份证" value="1"></el-option>
                    <!-- <el-option label="营业执照" value="2"></el-option> -->
                    <!-- <el-option label="统一信息代码" value="3"></el-option> -->
                  </el-select>
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column class-name="table_required" label="证件号码">
              <template slot-scope="scope">
                <el-form-item>
                  <el-input
                    v-model="scope.row.favoreeNumber"
                    placeholder="请输入证件号码"
                    :disabled="disabled"
                    maxlength="30"
                  />
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column
              prop="favoreeStart"
              class-name="table_required"
              label="证件发证日期"
              width="190"
            >
              <template slot-scope="scope">
                <el-form-item>
                  <el-date-picker
                    v-model="scope.row.favoreeStart"
                    value-format="timestamp"
                    placeholder="请选择证件开始日期"
                    type="date"
                    style="width: 100%;"
                    :disabled="disabled"
                  />
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column
              prop="favoreeEnd"
              class-name="table_required"
              label="证件到期日期"
              width="190"
            >
              <template slot-scope="scope">
                <el-form-item>
                  <el-date-picker
                    v-model="scope.row.favoreeEnd"
                    value-format="timestamp"
                    placeholder="请选择证件有效期至"
                    type="date"
                    style="width: 100%;"
                    :disabled="disabled"
                  />
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column prop="favoreeAddress" class-name="table_required" label="地址">
              <template slot-scope="scope">
                <el-form-item>
                  <el-input
                    v-model="scope.row.favoreeAddress"
                    placeholder="请输入地址"
                    :disabled="disabled"
                    maxlength="50"
                  />
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column prop="favoreePhone" class-name="table_required" label="联系电话">
              <template slot-scope="scope">
                <el-form-item>
                  <el-input
                    v-model="scope.row.favoreePhone"
                    placeholder="请输入联系电话"
                    :disabled="disabled"
                    maxlength="20"
                  />
                </el-form-item>
              </template>
            </el-table-column>
          </el-table>
        </el-form-item>
        <el-form-item label="企业门头照:">
          <div style="font-size: 12px;color: #666">
            请上传含有公司名称的门头照片（公司前台或公司大门等）。大小不超过1.5MB，支持jpg、jpeg、png
            <br><span style="color:#ff2400">
              门头照中需要显示门框，且门框内能够显示完整的公司名称
              <el-button
                type="text"
                style="color: #5d96db;font-size:12px;"
                @click="lookImg(companyDoorUrl)"
              >企业门头照示例图》</el-button>
            </span>
          </div>
          <el-upload
            :disabled="disabled"
            class="avatar-uploader"
            :show-file-list="false"
            action
          >
            <img v-if="corBaseVo.companyUrl" :src="corBaseVo.companyUrl" class="avatar" />
            <i v-else class="el-icon-plus avatar-uploader-icon"></i>
          </el-upload>
        </el-form-item>
        <el-form-item label="营业执照:" prop="businessLicenseUrl">
          <div style="font-size: 12px;color: #666">请上传企业营业执照原件。大小不超过1.5MB，支持jpg、jpeg、png</div>
          <el-upload
            :disabled="disabled"
            class="avatar-uploader"
            :show-file-list="false"
            action
          >
            <img
              v-if="corBaseVo.businessLicenseUrl"
              :src="corBaseVo.businessLicenseUrl"
              class="avatar"
            />
            <i v-else class="el-icon-plus avatar-uploader-icon"></i>
          </el-upload>
        </el-form-item>
        <el-form-item label="开户许可证:">
          <div style="font-size: 12px;color: #666">请上传开户许可证原件。大小不超过1.5MB，支持jpg、jpeg、png</div>
          <el-upload
            :disabled="disabled"
            class="avatar-uploader"
            :show-file-list="false"
            action
          >
            <img
              v-if="corBaseVo.accountLicenceUrl"
              :src="corBaseVo.accountLicenceUrl"
              class="avatar"
            />
            <i v-else class="el-icon-plus avatar-uploader-icon"></i>
          </el-upload>
        </el-form-item>
      </el-form>
      <el-divider />
      <div class="title_line second">法人信息</div>
      <el-form :model="jpersonVo" ref="jpersonVo" label-width="100px">
        <el-form-item label="法人名称:" prop="juridicalPersonName">
          <el-input
            v-model="jpersonVo.juridicalPersonName"
            placeholder="请输入企业法人姓名"
            :disabled="disabled"
            maxlength="30"
          ></el-input>
        </el-form-item>
        <el-form-item label="法人性别:" prop="sex">
          <el-radio v-model="jpersonVo.sex" :label="1" :disabled="disabled">男</el-radio>
          <el-radio v-model="jpersonVo.sex" :label="2" :disabled="disabled">女</el-radio>
        </el-form-item>
        <el-form-item label="法人身份证号:" :disabled="disabled">
          <el-input
            v-model="jpersonVo.cardNumber"
            placeholder="请输入企业法人身份证号"
            :disabled="disabled"
            maxlength="30"
          ></el-input>
        </el-form-item>
        <el-form-item label="身份证效期:">
          <el-col :span="10">
            <el-form-item prop="cardStartTime">
              <el-date-picker
                value-format="timestamp"
                placeholder="请选择法人身份证的开始时间"
                type="date"
                v-model="jpersonVo.cardStartTime"
                style="width: 100%;"
                :disabled="disabled"
              ></el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="10" style="margin-right: 40px">
            <el-form-item prop="cardEndTime">
              <el-date-picker
                value-format="timestamp"
                placeholder="请选择法人身份证的有效期至"
                type="date"
                v-model="jpersonVo.cardEndTime"
                style="width: 100%;margin: 0 16px"
                :disabled="disabled"
              ></el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="2">
            <el-form-item>
              <el-checkbox
                v-model="jpersonVo.jpersonLongTerm"
                :disabled="disabled"
              >长期</el-checkbox>
            </el-form-item>
          </el-col>
        </el-form-item>
        <el-form-item label="法人户籍地址:" prop="permanentAddress">
          <el-input
            v-model="jpersonVo.permanentAddress"
            placeholder="请输入企业法人身份证正面地址"
            :disabled="disabled"
            maxlength="50"
          ></el-input>
        </el-form-item>
        <el-form-item label="法人身份证签发地址:" prop="cardIssueAddress">
          <el-input
            v-model="jpersonVo.cardIssueAddress"
            placeholder="请输入企业法人身份证正面地址"
            :disabled="disabled"
            maxlength="50"
          ></el-input>
        </el-form-item>
        <el-form-item label="法人手机号:" prop="phone">
          <el-input
            v-model="jpersonVo.phone"
            placeholder="请输入企业法人手机号"
            :disabled="disabled"
            maxlength="11"
          ></el-input>
        </el-form-item>
        <el-form-item label="法人身份证正面照片:" prop="cardUrl">
          <div style="font-size: 12px;color: #666">请上传法人身份证原件。大小不超过1.5MB，支持jpg、jpeg、png</div>
          <el-upload
            :disabled="disabled"
            class="avatar-uploader"
            :show-file-list="false"
            action
          >
            <img v-if="jpersonVo.cardUrl" :src="jpersonVo.cardUrl" class="avatar" />
            <i v-else class="el-icon-plus avatar-uploader-icon"></i>
          </el-upload>
        </el-form-item>
        <el-form-item label="法人身份证反面照片:" prop="cardReverseUrl">
          <div style="font-size: 12px;color: #666">请上传法人身份证原件。大小不超过1.5MB，支持jpg、jpeg、png</div>
          <el-upload
            :disabled="disabled"
            class="avatar-uploader"
            :show-file-list="false"
            action
          >
            <img v-if="jpersonVo.cardReverseUrl" :src="jpersonVo.cardReverseUrl" class="avatar" />
            <i v-else class="el-icon-plus avatar-uploader-icon"></i>
          </el-upload>
        </el-form-item>
      </el-form>
      <el-divider />
      <div class="title_line third">结算信息</div>
      <el-form :model="accountVo" ref="accountVo" label-width="100px">
        <el-form-item label="开卡人名称:" prop="registeredName">
          <el-input
            v-model="accountVo.registeredName"
            placeholder="请输入开卡人名称"
            :disabled="disabled"
            maxlength="30"
          ></el-input>
        </el-form-item>
        <el-form-item
          label="银行账号:"
          prop="registeredBankAccount"
          maxlength="30"
        >
          <el-input
            v-model="accountVo.registeredBankAccount"
            placeholder="请输入企业的对公收款账号"
            :disabled="disabled"
          ></el-input>
        </el-form-item>
        <el-form-item label="开户银行名称:" prop="bankName">
          <el-select
            v-model="accountVo.bankName"
            placeholder="请选择开户银行"
            style="width: 400px"
            :disabled="disabled"
            :filterable="true"
            :key="accountVo.bankName"
          >
            <el-option
              v-for="item in bankList"
              :key="item.bankName"
              :label="item.bankName"
              :value="item.bankName"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="开户银行支行:" prop="subBankName">
          <el-select
            ref="subBank"
            v-model="accountVo.subBankName"
            placeholder="请选择开户支行名称"
            style="width: 400px"
            :disabled="disabled"
            :filterable="true"
            :key="accountVo.subBankName"
          >
            <el-option
              v-for="item in subBankList"
              :key="item.bankName"
              :label="item.bankName"
              :value="item.bankName"
            ></el-option>
          </el-select>
          <!-- <el-button v-if="!disabled" icon="el-icon-search" circle @click="submitSearchSub"></el-button>-->
        </el-form-item>
      </el-form>
      <el-divider />
      <div class="title_line fourth">联系人信息</div>
      <el-form
        :model="accountContactsVo"
        ref="accountContactsVo"
        label-width="100px"
      >
        <el-form-item label="商户联系人姓名:" prop="contactsName">
          <el-input
            v-model="accountContactsVo.contactsName"
            placeholder="请输入企业对外的常用联系人姓名"
            :disabled="disabled"
            maxlength="30"
          ></el-input>
        </el-form-item>
        <el-form-item label="商户联系人省市区县:" required>
          <div class="addrForm">
            <el-form-item prop="prov">
              <el-select
                v-model="accountContactsVo.prov"
                placeholder="请选择省份"
                :disabled="disabled"
                :key="accountContactsVo.prov"
              >
                <el-option
                  v-for="province in provinceList"
                  :key="province.id"
                  :label="province.areaName"
                  :value="province.areaName"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item prop="city">
              <el-select
                v-model="accountContactsVo.city"
                placeholder="请选择城市"
                :disabled="disabled"
                :key="accountContactsVo.city"
              >
                <el-option
                  v-for="city in accountContactsVo.cityList"
                  :key="city.id"
                  :label="city.areaName"
                  :value="city.areaName"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item prop="area">
              <el-select
                v-model="accountContactsVo.area"
                placeholder="请选择区县"
                :disabled="disabled"
                :key="accountContactsVo.area"
              >
                <el-option
                  v-for="area in accountContactsVo.areaList"
                  :key="area.id"
                  :label="area.areaName"
                  :value="area.areaName"
                ></el-option>
              </el-select>
            </el-form-item>
          </div>
        </el-form-item>
        <el-form-item label="商户联系人地址:" prop="address">
          <el-input
            v-model="accountContactsVo.address"
            placeholder="请输入企业联系人的详细地址"
            :disabled="disabled"
            maxlength="50"
          ></el-input>
        </el-form-item>
        <el-form-item label="商户邮箱:" prop="email">
          <el-input
            v-model="accountContactsVo.email"
            placeholder="请输入企业联系人的常用邮箱。后期将接收银行的确认邮件，请认真填写"
            :disabled="disabled"
            maxlength="30"
          ></el-input>
        </el-form-item>
        <el-form-item label="商户手机号:" prop="phone">
          <el-input
            v-model="accountContactsVo.phone"
            placeholder="请输入企业联系人的手机号。银行审核资料期间，可能与该手机号进行企业信息核实，请认真填写"
            :disabled="disabled"
          ></el-input>
        </el-form-item>
      </el-form>

      <div class="title_line fourth">
        授权人信息
      </div>
      <el-form
        ref="authorizerVo"
        :model="authorizerVo"
        label-width="100px"
      >
        <el-form-item
          label="授权人姓名:"
          prop="name"
        >
          <el-input
            v-model="authorizerVo.name"
            placeholder="请输入授权人姓名"
            :disabled="disabled"
            maxlength="30"
          />
        </el-form-item>

        <el-form-item
          label="授权人身份证号:"
          prop="cardNumber"
        >
          <el-input
            v-model="authorizerVo.cardNumber"
            placeholder="请输入授权人身份证号"
            :disabled="disabled"
            maxlength="30"
          />
        </el-form-item>

        <el-form-item
          label="业务授权函:"
          prop="authLetterUrl"
        >
          <div>
            <p>1、请下载业务授权函模版文件，输入授权人姓名、授权人职务、授权人身份证号码、授权人手机号码、授权人电子邮箱，填写完成后A4纸打印。需注意：授权人姓名、授权人身份证号和授权人手机号必须为同一个人</p>
            <p>2、在打印的纸张上需授权人签字，并填写授权年月日，填写完成后需加盖法人章和企业公章</p>
            <p>3、签字盖章完成后，拍照上传。照片大小不超过5MB，支持jpg、jpeg、png</p>
            <p>
              <a
                href="/uploadFile/downloadTemplate?fileName=企富通业务授权函.docx"
                style="color: #4183d5;"
              >下载【业务授权函】模版</a>
            </p>
            <div>
              <el-upload
                :disabled="disabled"
                class="avatar-uploader"
                :show-file-list="false"
                action
              >
                <img
                  v-if="authorizerVo.authLetterUrl"
                  :src="authorizerVo.authLetterUrl"
                  class="avatar"
                >
                <i
                  v-else
                  class="el-icon-plus avatar-uploader-icon"
                />
              </el-upload>
              <el-dialog :visible.sync="dialogVisible">
                <img
                  width="100%"
                  :src="dialogImageUrl"
                  alt=""
                >
              </el-dialog>
            </div>
          </div>
        </el-form-item>

        <el-form-item
          label="授权人手机号:"
          prop="phone"
        >
          <el-input
            v-model="authorizerVo.phone"
            placeholder="请输入企业授权人的手机号。授权人手机号将用于后期申请提现时的短信验证，请认真填写"
            :disabled="disabled"
          />
        </el-form-item>
      </el-form>
    </div>
    <div
      v-show="Number(shopConfig.shopCreateStatus)!==0"
      class="rightContent"
    >
      <el-tabs
        v-model="activeName"
        tab-position="right"
        style="height: 160px;"
        @tab-click="tabClick"
      >
        <el-tab-pane label="企业信息" name="first"></el-tab-pane>
        <el-tab-pane label="法人信息" name="second"></el-tab-pane>
        <el-tab-pane label="结算信息" name="third"></el-tab-pane>
        <el-tab-pane label="联系人信息" name="fourth"></el-tab-pane>
      </el-tabs>
    </div>
    <el-image-viewer
      v-if="showViewer"
      :url-list="srcArr"
      :on-close="closeViewer"
      :z-index="100000"
    />
  </div>
</template>

<script>
import { mapState } from 'vuex';
import ElImageViewer from 'element-ui/packages/image/src/image-viewer';
import { queryBaseAccountModifiedDetail } from '@/api/companyOpenAccount';

export default {
  name: 'ModifiedDetail',
  components: { ElImageViewer },
  data() {
    return {
      shareholdersInformationUrl: require('@/assets/image/common/shareholders_information.png'),
      beneficiaryInformationUrl: require('@/assets/image/common/beneficiary_information.png'),
      companyDoorUrl: require('@/assets/image/common/company_door.jpg'),
      showViewer: false,
      srcArr: [],
      activeName: 'first',
      dialogImageUrl: '',
      dialogVisible: false,
      searchSubBank: [],
      bankList: [],
      subBankList: [],
      provinceList: [],
      disabled: true,
      authorizerVo: {
        name: '',
        cardNumber: '',
        authLetterUrl: '',
        phone: '',
      },
      corBaseVo: {
        accountLicenceUrl: '', // 开户许可证url
        addr: '', // 注册地址
        area: '', // 区名称
        areaId: '', // 区编码
        beneficiaryInfo: [
          {
            favoreeName: '',
            favoreeType: '',
            favoreeNumber: '',
            favoreeStart: '',
            favoreeEnd: '',
            favoreeAddress: '',
            favoreePhone: '',
          },
        ], // 受益人信息
        businessEndTime: '', // 营业执照结束时间
        businessLicenseCode: '', // 企业营业执照编码
        businessLicenseUrl: '', // 营业执照url
        businessScop: '', // 经营范围
        businessStartTime: '', // 营业执照开始时间
        city: '', // 市
        cityId: '', // 市编码
        companyName: '', // 企业名称
        companyUrl: '', // 公司照片url
        customerServicePhone: '', // 客服电话号码
        id: '', // 主键
        prov: '', // 省
        provId: '', // 省编码
        registeredCapital: '', // 注册资本金
        shareholderInfo: [
          {
            shareholderName: '',
            shareholderType: '',
            shareholderNumber: '',
            shareholderStart: '',
            shareholderEnd: '',
          },
        ], // 股东信息
        longTerm: '', // 是否长期
        status: 0, // 0、未开户,1、开户中，2待微信注册，3、开户成功,4,开户失败
        wxCodeUrl: '', // 微信二维码url
      },
      jpersonVo: {
        cardEndTime: '', // 身份证结束时间
        cardIssueAddress: '', // 身份证签发地址
        cardReverseUrl: '', // 身份证反面照片
        cardStartTime: '', // 身份证开始时间
        cardUrl: '', // 身份证正面照片
        id: '', // 主键id
        juridicalPersonName: '', // 法人名称
        permanentAddress: '', // 户籍地址
        phone: '', // 法人手机号
        sex: '', // 性别 1-男 2-女
        cardNumber: '', // 法人身份证号
        jpersonLongTerm: '', // 是否长期
      },
      accountVo: {
        bankCode: '', // 开户银行编码
        bankName: '', // 开户银行名称
        id: '', // id
        registeredBankAccount: '', // 银行账号
        registeredName: '', // 开卡人姓名
        subBankName: '', // 开户支行名称
        subBankCode: '', // 开户支行编码
      },
      accountContactsVo: {
        address: '', // 地址
        area: '', // 区县名称
        areaId: '', // 区县ID
        city: '', // 市
        cityId: '', // 市ID
        contactsName: '', // 联系人姓名
        email: '', // 邮件地址
        id: '', // 主键ID
        phone: '', // 联系人电话
        prov: '', // 省
        provId: '', // 省ID
      },
    };
  },
  computed: { ...mapState('app', ['shopConfig']) },
  mounted() {
    this.queryBaseAccountModifiedDetail();
    this.$nextTick(() => {
      const el = document.querySelector('.leftContent');
      el.addEventListener('scroll', this.onScroll);
    });
  },
  methods: {
    lookImg(url) {
      this.srcArr = [];
      this.srcArr.push(url);
      this.showViewer = true;
    },
    closeViewer() {
      this.showViewer = false;
    },
    handleGoCompanyOpenAccount() {
      this.$router.push({ path: '/companyOpenAccount' });
    },
    queryBaseAccountModifiedDetail() {
      queryBaseAccountModifiedDetail({ applyNo: this.$route.query.applyNo }).then((res) => {
        if (res && res.code === 0) {
          const { accountContactsVo, accountVo, corBaseVo, jpersonVo, smsPhone, authorizerVo, inWhiteList } = res.result;
          corBaseVo.beneficiaryInfo = Array.isArray(JSON.parse(corBaseVo.beneficiaryInfo)) ? JSON.parse(corBaseVo.beneficiaryInfo) : [JSON.parse(corBaseVo.beneficiaryInfo)];

          corBaseVo.beneficiaryInfo.map((item) => {
            item.favoreeStart = new Date(item.favoreeStart).getTime();
            item.favoreeEnd = new Date(item.favoreeEnd).getTime();
            return item;
          });

          corBaseVo.shareholderInfo = Array.isArray(JSON.parse(corBaseVo.shareholderInfo)) ? JSON.parse(corBaseVo.shareholderInfo) : [JSON.parse(corBaseVo.shareholderInfo)];

          corBaseVo.shareholderInfo.map((item) => {
            item.shareholderStart = new Date(item.shareholderStart).getTime();
            item.shareholderEnd = new Date(item.shareholderEnd).getTime();
            return item;
          });

          this.accountContactsVo = { ...accountContactsVo };
          this.accountVo = { ...accountVo };
          this.corBaseVo = { ...corBaseVo };
          this.jpersonVo = { ...jpersonVo };
          this.authorizerVo = { ...authorizerVo };
          this.smsPhone = smsPhone;
          this.inWhiteList = inWhiteList;
          const endTime = this.formatDate(this.corBaseVo.businessEndTime, 'YMD');
          if (endTime === '2099-12-31') {
            this.longTermChange(true);
            this.corBaseVo.longTerm = true;
          }
          const cardEndTime = this.formatDate(this.jpersonVo.cardEndTime, 'YMD');
          if (cardEndTime === '2099-12-31') {
            this.jpersonLongTermChange(true);
            this.jpersonVo.jpersonLongTerm = true;
          }
        }
      });
    },
    longTermChange(type) {
      if (type) {
        this.$set(this.corBaseVo, 'businessEndTime', '');
        this.businessEndTimeDisabled = true;
        this.$refs.corBaseVo.clearValidate(['businessEndTime']);
        this.corBaseVoRules.businessEndTime = [{ required: false }];
      } else {
        this.businessEndTimeDisabled = false;
        this.corBaseVoRules.businessEndTime = [{ required: true, message: '请输入营业执照的有效期至', trigger: 'change' }];
      }
    },
    jpersonLongTermChange(type) {
      if (type) {
        this.$set(this.jpersonVo, 'cardEndTime', '');
        this.juridicalPersonDisabled = true;
        this.$refs.jpersonVo.clearValidate(['cardEndTime']);
        this.jpersonVoRules.cardEndTime = [{ required: false }];
      } else {
        this.juridicalPersonDisabled = false;
        this.jpersonVoRules.cardEndTime = [{ required: true, message: '请选择法人身份证的有效期至', trigger: 'change' }];
      }
    },
    tabClick(tab) {
      const name = `.${tab.name}`;
      const top = document.querySelector(name).offsetTop;
      $('.leftContent').animate({ scrollTop: Number(top) - 80 }, 500);
    },
    onScroll(e) {
      const scrollTop = e.target.scrollTop + 200;
      const firstOffsetTop = document.querySelector('.first').offsetTop;
      const secondOffsetTop = document.querySelector('.second').offsetTop;
      const thirdOffsetTop = document.querySelector('.third').offsetTop;
      const fourthOffsetTop = document.querySelector('.fourth').offsetTop;
      if (scrollTop >= firstOffsetTop && scrollTop < secondOffsetTop) {
        this.activeName = 'first';
      } else if (scrollTop >= secondOffsetTop && scrollTop < thirdOffsetTop) {
        this.activeName = 'second';
      } else if (scrollTop >= thirdOffsetTop && scrollTop < fourthOffsetTop) {
        this.activeName = 'third';
      } else if (scrollTop >= fourthOffsetTop) {
        this.activeName = 'fourth';
      }
    },
  },
};
</script>

<style scoped lang="scss">

.floatIcon {
  border-radius: 50%;
  padding: 7px 10px;
  position: absolute;
  top: 0px;
  background: red;
}
.el-button {
  padding: 8px 20px;
}

.el-button.is-circle {
  padding: 7px;
  border: none;
}

.companyOpenAccount {
  //min-width: 1400px;
  width: 100%;
  height: 100%;
  position: relative;
  padding-top: 50px;

  .NoCreatedStore {
    width: 100%;
    padding-left: 30px;
    line-height: 77px;
    font-size: 18px;
    color: #ffa012;
    background: #fffaf2;
    position: absolute;
    top: 0;
    left: 0;
  }

  .headerStatus {
    position: absolute;
    top: 0;
    left: 0;
    //min-width: 1400px;
    width: 100%;
    height: 50px;
    background: #fffbf1;
    display: flex;
    justify-content: right;
    align-items: center;
    z-index: 100;

    .companyStatus {
      width: 60%;
      display: flex;
      justify-content: start;
      align-items: center;

      .status {
        width: 100px;
        color: #333333;
        font-size: 16px;
        margin-left: 16px;
      }

      .statusStr {
        margin: 0 16px;
      }
    }
  }

  .leftContent {
    width: 100%;
    height: 100%;
    overflow-y: auto;
    padding: 30px 16px 0;

    ::v-deep   .el-form {
      width: 80%;

      .el-select {
        margin-right: 14px;
      }

      .el-form-item__label {
        font-size: 12px;
        line-height: 30px;
      }

      .el-form-item__content {
        line-height: 30px;
      }

      .el-input__inner {
        line-height: 30px;
        height: 30px;
        font-size: 12px;
      }

      .el-input__icon {
        line-height: 30px;
      }
    }

    ::v-deep   .el-table__body .el-form-item {
      padding: 20px 0;
    }

    .addrForm .el-form-item {
      display: inline-block;
    }

    .avatar-uploader ::v-deep   .el-upload {
      border: 1px dashed #d9d9d9;
      border-radius: 6px;
      cursor: pointer;
      position: relative;
      overflow: hidden;
    }

    .avatar-uploader ::v-deep   .el-upload:hover {
      border-color: #409eff;
    }

    .avatar-uploader-icon {
      font-size: 28px;
      color: #8c939d;
      width: 178px;
      height: 178px;
      line-height: 178px;
      text-align: center;
    }

    .avatar {
      width: 178px;
      height: 178px;
      display: block;
    }
  }

  .leftContent::-webkit-scrollbar {
    width: 0 !important;
  }

  .rightContent {
    position: absolute;
    right: 0;
    top: 75px;
    z-index: 1001;
    background-color: #fff;
  }
}
</style>
