<script>
export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: '标题'
    },
    width: {
      type: String,
      default: '500px'
    }
  },
  watch: {
    visible: {
      handler(newVal) {
        this.isShow = newVal;
      }
    }
  },
  data() {
    return {
      isShow: false
    }
  }
}
</script>

<template>
  <el-dialog :visible.sync="isShow" :title="title" :width="width" append-to-body @close="$emit('close')">
    <template slot="title">
      <slot name="title">

      </slot>
    </template>
    <slot></slot>
    <template slot="footer">
      <slot name="footer">

      </slot>
    </template>
  </el-dialog>
</template>

<style scoped>
p {
  margin: 5px 0;
}
::v-deep   .el-dialog {
  border-radius: 5px;
  overflow: hidden;
}
::v-deep   .el-dialog__header {
  padding: 10px;
  background-color: #f3f3f3;
}
::v-deep   .el-dialog__header span {
  font-size: 14px;
  line-height: normal;
}
::v-deep   .el-dialog__header button {
  right: 10px;
  top: 10px;
}
::v-deep   .el-dialog__body {
  padding: 5px 10px;
  border-bottom: solid 1px #e4eaf1;
}
::v-deep   .el-dialog__footer {
  padding: 10px;
}
</style>
