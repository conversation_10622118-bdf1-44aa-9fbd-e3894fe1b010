<template>
    <div class="single-apply-page">
        <div class="single-header">
            <el-button
                type="primary"
                size="small"
                @click="$router.go(-1)"
            >
                返回上一步
            </el-button>
            <div class="single-tag">
                <span>当前已选品种：<el-tag type="success">商品类型：批购包邮</el-tag></span>
            </div>
        </div>
        <div class="single-info">
            <div class="single-img">
                <img src="" alt="">
            </div>
            <div class="single-info-content">
                <div class="single-info-list">
                    <div class="single-item"><span>通用名称：</span>感冒灵感冒灵</div>
                    <div class="single-item"><span>规格：</span>感冒灵</div>
                    <div class="single-item"><span>包装单位：</span>感冒灵</div>
                    <div class="single-item"><span>商品编码：</span>感冒灵</div>
                </div>
                <div class="single-info-list">
                    <div class="single-item"><span>商品名称：</span>感冒灵</div>
                    <div class="single-item"><span>生产厂家：</span>感冒灵</div>
                    <div class="single-item"><span>处方类型：</span>感冒灵</div>
                    <div class="single-item"><span>ERP编码：</span>感冒灵</div>
                </div>
                <div class="single-info-list">
                    <div class="single-item"><span>展示名称：</span>感冒灵</div>
                    <div class="single-item"><span>批准文号：</span>感冒灵</div>
                    <div class="single-item"><span>价格：</span>单体采购价，连锁采购价</div>
                </div>
            </div>
        </div>
        <div class="single-content">
            <div class="tabBox">
                <div
                v-for="(item, index) in tabList"
                :key="index"
                :class="clickIndex === index ? 'active' : ''"
                @click="tabClick(item.idName, index)"
                >
                {{ item.name }}
                </div>
            </div>
            <div class="conBox">
                <div class="form-item">
                    <p><span>*</span>上下架时间：</p>
                    <div class="form-item-content">
                        <el-date-picker
                            v-model="form.shelvesTime"
                            type="datetimerange"
                            range-separator="至"
                            start-placeholder="开始日期"
                            end-placeholder="结束日期">
                        </el-date-picker>
                    </div>
                </div>
                <div id="price" class="contentBox">
                    <div class="title">价格库存</div>
                    <div class="contentBox-main">
                        <el-form :model="priceInventoryVo" :rules="priceInventoryVoRules" ref="priceInventoryVo" label-width="160px" class="demo-ruleForm">
                            <el-row>
                                <el-col :span="12">
                                    <el-form-item label="原价:" prop="ognPrice">
                                        <el-input v-model="priceInventoryVo.ognPrice" placeholder="请输入大于0的数字，限2位小数" onkeyup="value=value.replace(/^\D*(\d*(?:\.\d{0,2})?).*$/g, '$1')"></el-input>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-row>
                                <el-col :span="12">
                                    <el-form-item label="活动价:" prop="actPrice">
                                        <el-input v-model="priceInventoryVo.actPrice" placeholder="请输入大于0的数字，限2位小数" onkeyup="value=value.replace(/^\D*(\d*(?:\.\d{0,2})?).*$/g, '$1')"></el-input>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-row>
                                <el-col :span="12">
                                    <el-form-item label="商品实时库存:" prop="totalStock">
                                    <el-input v-model="priceInventoryVo.totalStock" placeholder="请输入大于0的数字，限2位小数"  onkeyup="value=value.replace(/^\D*(\d*(?:\.\d{0,2})?).*$/g, '$1')"></el-input>
                                    </el-form-item>
                                </el-col>
                                <el-checkbox class="checkBox-info" v-model="priceInventoryVo.stockSyncErp==1" @change="stockSyncErpChange" >自动同步价格（ERP系统对接后生效）</el-checkbox>
                            </el-row>
                        </el-form>
                    </div>
                </div>
                <div id="purchase" class="contentBox">
                    <div class="title">起购限购</div>
                    <div class="contentBox-main">
                        <el-form
                            ref="upForPurchaseVo"
                            :model="upForPurchaseVo"
                            :rules="upForPurchaseVoRules"
                            label-width="160px"
                            class="demo-ruleForm"
                        >
                            <el-row>
                                <el-col :span="12">
                                    <el-form-item label="起购数量:" prop="minPurchaseCount">
                                        <el-input
                                            v-model="upForPurchaseVo.minPurchaseCount"
                                            placeholder="支持输入正整数"
                                            type="text"
                                            onkeyup="value=value.replace(/[^\d]/g,'')"
                                        ></el-input>
                                        <span style="font-size:12px">
                                            <span class="chainPrice-info">药店单次购买的最小限购数量，需为正整数。</span>
                                        </span>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-row>
                                <el-col :span="12">
                                    <el-form-item label="购买倍数:" prop="buyMultiple">
                                        <el-input
                                            v-model="upForPurchaseVo.buyMultiple"
                                            placeholder="支持输入正整数"
                                            type="text"
                                            onkeyup="value=value.replace(/[^\d]/g,'')"
                                        ></el-input>
                                        <span style="font-size:12px">
                                            <span class="chainPrice-info">购买倍数即每次点击加购的数量，起购数量必须是购买倍数的整倍数。</span>
                                        </span>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-row>
                                <el-col :span="12">
                                    <el-form-item label="活动数量上限:" prop="actLimit">
                                        <div>
                                            <el-switch
                                                :width="70"
                                                v-model="upForPurchaseVo.actLimit"
                                                active-text="不限购"
                                                inactive-text="限购"
                                            ></el-switch>
                                        </div>
                                            <!-- :disabled="disabled" -->
                                        <span style="font-size:12px">
                                            <span class="chainPrice-info">
                                                所有药店在设定的周期内可购买的最大采购数量，需为正整数<br />
                                                从不限购切换为限购，则从修改时间开始重新计算限购。
                                            </span>
                                        </span>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-row v-if="!upForPurchaseVo.actLimit">
                                <el-col :span="12">
                                    <el-form-item label="批购包邮活动总限购数量:" prop="actLimitCount">
                                        <el-input
                                            v-model="upForPurchaseVo.actLimitCount"
                                            placeholder="请输入正整数"
                                            type="text"
                                            onkeyup="value=value.replace(/[^\d]/g,'')"
                                        ></el-input>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-row>
                                <el-col :span="12">
                                    <el-form-item label="单个药店采购数量上限:" prop="singleBuyLimit">
                                        <div>
                                            <el-switch
                                                :width="70"
                                                active-text="不限购"
                                                inactive-text="限购"
                                                v-model="upForPurchaseVo.singleBuyLimit"
                                            ></el-switch>
                                        </div>
                                            <!-- :disabled="disabled" -->
                                        <span style="font-size:12px">
                                            <span class="chainPrice-info">
                                                单个药店在设定的周期内可购买的最大采购数量，需为正整数。限购数量需≥起拼数量<br />
                                                从不限购切换为限购或调整限购类型，则从修改时间开始重新计算限购。
                                            </span>
                                        </span>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-row v-if="!upForPurchaseVo.singleBuyLimit">
                                <el-col :span="12">
                                    <el-form-item label="单个药店限购类型:" prop="singleBuyType">
                                        <div>
                                            <el-select
                                                class="select-info"
                                                v-model="upForPurchaseVo.singleBuyType"
                                                placeholder="请选择"
                                            >
                                                <el-option label="活动期间" :value="1" />
                                                <el-option label="单笔" :value="2" />
                                                <el-option label="每天（每天 00:00 至 24:00）" :value="3" />
                                                <el-option label="每周（周一 00:00 至 周日 24:00）" :value="4" />
                                                <el-option label="每月（每月1号 00:00 至 每月最后一天 24:00）" :value="5" />
                                            </el-select>
                                        </div>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-row v-if="!upForPurchaseVo.singleBuyLimit">
                                <el-col :span="12">
                                    <el-form-item label="单个药店限购数量:" prop="singleBuyCount">
                                        <el-input
                                            v-model="upForPurchaseVo.singleBuyCount"
                                            placeholder="请输入正整数"
                                            type="text"
                                            onkeyup="value=value.replace(/[^\d]/g,'')"
                                        ></el-input>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                        </el-form>
                    </div> 
                </div>
                <div id="supply" class="contentBox">
                    <div class="title">供货信息</div>
                    <div class="contentBox-main">
                        <el-form
                            :model="upForPurchaseVo"
                            :rules="upForPurchaseVoRules"
                            label-width="160px"
                            class="demo-ruleForm"
                        >
                            <el-row>
                                <el-col :span="12">
                                    <el-form-item label="虚拟供应商:">
                                        <el-radio v-model="supplyInfoVo.virtually" label="1">是</el-radio>
                                        <el-radio v-model="supplyInfoVo.virtually" label="0">否</el-radio>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-row>
                                <el-col :span="12">
                                    <el-form-item label="供货信息配置方式:">
                                        <el-radio v-model="supplyInfoVo.configType" label="1">复用原品销售范围</el-radio>
                                        <el-radio v-model="supplyInfoVo.configType" label="2">配置业务商圈、供货对象、黑白名单</el-radio>
                                        <el-radio v-model="supplyInfoVo.configType" label="3">人群</el-radio>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                        </el-form>
                    </div>
                </div>
                <div id="other" class="contentBox">
                    <div class="title">其他信息</div>
                    <div class="contentBox-main other-tips">
                       商品近远效期、导单系数、商品佣金比例、商品图片、商品中包装和原品保持一致，原品变更后批购包邮商品同步变更。
                    </div>
                </div>
                <el-dialog title="提示" :visible.sync="tipsDialog" width="30%">
                    <div>
                        {{ tipsContent }}
                    </div>
                    <span slot="footer">
                    <el-button size="medium" type="primary" @click="tipsDialog = false">我知道了</el-button>
                    </span>
                </el-dialog>
            </div>
            <div class="form-button">
                <el-button>取消</el-button>
                <el-button type="primary" @click="confirmSubmit">提交</el-button>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    data() {
        return {
            tabList: [
                { name: '价格库存', idName: 'price' },
                { name: '起购限购', idName: 'purchase' },
                { name: '供货信息', idName: 'supply' },
                { name: '其它信息', idName: 'other' }
            ],
            clickIndex: 0,
            priceInventoryVoRules: {
                actPrice: [{ required: true, message: '活动价不能为空', trigger: 'blur' }],
            },
            priceInventoryVo: {
                ognPrice: "", //原价
                actPrice: "", //活动价
                totalStock: "", //商品实时库存
                stockSyncErp: 1, //自动同步价格
            },
            upForPurchaseVoRules: {
                minPurchaseCount: [{ required: true, message: '请填写起购数量', trigger: 'blur' }],
                buyMultiple: [{ required: true, message: '请填写购买倍数', trigger: 'blur' }],
                actLimitCount: [{ required: true, message: '请填写限购数量', trigger: 'blur' }],
                singleBuyType: [{ required: true, message: '请填写单个药店限购类型', trigger: 'blur' }],
                singleBuyCount: [{ required: true, message: '请填写单个药店限购数量', trigger: 'blur' }],
            },
            // 起购限购
            upForPurchaseVo: {
                minPurchaseCount: "", //起购数量
                buyMultiple: "1", // 购买倍数
                actLimit: true, // 活动数量上限
                singleBuyLimit: true, // 单个药店采购数量上限
                actLimitCount: "",
                singleBuyType: "", //单个限购类型
                singleBuyCount: "", //单个限购数量
            },
            supplyInfoVo: {
                virtually: "1",
                configType: "1"
            },
            form: {
                shelvesTime: "",
            },
            tipsDialog: false,
            tipsContent: "活动价需小于等于原价"
        }
    },
    methods: {
        tabClick(name, index) {
            this.clickIndex = index
            document.querySelector(`#${name}`).scrollIntoView(true)
        },
        checkOgnPrice() {

        },
        stockSyncErpChange(type) {
            if (type) {
                this.priceInventoryVo.stockSyncErp = 1;
            } else {
                this.priceInventoryVo.stockSyncErp = 0;
            }
        },
        getFormPromise(form) {
            console.log('form', form)
            return new Promise((resolve) => {
                form.validate((res) => {
                resolve(res)
                })
            })
        },
        checkTipsDialog() {
            if(this.priceInventoryVo.ognPrice && (this.priceInventoryVo.ognPrice < this.priceInventoryVo.actPrice)) {
                this.tipsContent = "活动价需小于等于原价";
                return true;
            } else if (this.upForPurchaseVo.buyMultiple * 1 < 1) {
                this.tipsContent = "购买倍数仅支持输入正整数";
                return true;
            }
        },
        confirmSubmit() {
            
            Promise.all(
                [
                    this.$refs.priceInventoryVo,
                    this.$refs.upForPurchaseVo,
                ].map(this.getFormPromise)
            ).then(res => {
                const validateResult = res.every((item) => !!item)
                console.log(validateResult, 'ook1');
                if (validateResult) {
                    if (this.checkTipsDialog()) {
                        this.tipsDialog = true;
                    } else {

                    }
                } else {
                    this.$message.error('部分必填项未填写或填写不规范')
                }
            })
        }
    }
}
</script>

<style lang="scss" scoped>
.single-apply-page {
    padding: 20px;
    box-sizing: border-box;
    .single-header {
        display: flex;
        align-items: center;
        .single-tag {
            margin-left: 15px;
        }
    }
    .single-info {
        display: flex;
        margin-top: 20px;
        .single-img {
            width: 150px;
            margin-right: 20px;
            background: red;
        }
        .single-info-content {
            display: flex;
            .single-info-list {
                // width: 180px;
                margin-right: 30px;
                .single-item {
                    margin-bottom: 10px;
                }
            }
        }
    }
    .single-content {
        margin-top: 40px;
        .tabBox {
            border-bottom: 1px solid #efefef;
            display: flex;
            align-items: center;
            div:first-child {
                margin-left: 16px;
                border-left: 1px solid #efefef;
            }
            div {
                height: 40px;
                padding: 0 18px;
                font-size: 14px;
                line-height: 40px;
                font-weight: 400;
                text-align: left;
                color: rgba(0, 0, 0, 0.65);
                text-decoration: none;
                border-right: 1px solid #efefef;
                border-top: 1px solid #efefef;
                background: rgba(239, 239, 239, 0.3);
                cursor: pointer;
            }
            div.active {
                border-bottom: none;
                background: #ffffff;
                opacity: 1;
                color: #1890ff;
            }
        }
        .conBox {
            width: 100%;
            height: 100%;
            overflow-y: auto;
            .form-item {
                display: flex;
                margin-top: 20px;
                >p {
                    width: 150px;
                    display: flex;
                    margin: 0;
                    justify-content: flex-end;
                    margin-right: 10px;
                    line-height: 34px;
                    span {
                        color: red;
                    }
                }
                .form-item-content {
                    flex: 1;
                    display: flex;
                    justify-content: center;
                    flex-direction: column;
                    .form-item-list {
                        display: flex;
                        align-items: center;
                        margin-bottom: 20px;
                        .sim-input {
                            width: 250px;
                            margin-right: 20px;
                        }
                    }
                    .form-item-list-direc {
                        display: flex;
                        flex-direction: column;
                        justify-content: center;
                        .form-item-title {
                            display: flex;
                            align-items: center;
                        }
                        .form-item-tips {
                            margin-top: 10px;
                            display: flex;
                            flex-direction: column;
                            p {
                                margin: 0;
                                margin-bottom: 5px;
                                span {
                                    text-decoration:underline;
                                    color: #4183d5;
                                    cursor: pointer;
                                    margin-left: 10px;
                                }
                            }
                        }
                    }
                }
            }
        }

        .conBox::-webkit-scrollbar {
            width: 0 !important;
        }
        .form-button {
            margin-top: 50px;
            padding-left: 150px;
            box-sizing: border-box;
            .el-button {
                width: 140px;
                margin-right: 40px;
            }
        }
        .contentBox {
            //height: 100%;
            padding: 16px 16px;
            background: #fff;

            .title {
                font-weight: 600;
                text-align: left;
                color: #000000;
                line-height: 14px;
                margin-bottom: 24px;
                font-size: 20px;
            }

            .title:before {
                content: '';
                display: inline-block;
                width: 3px;
                height: 15px;
                background: linear-gradient(360deg, #1d69c4 1%, #8bbdfc 99%);
                border-radius: 2px;
                margin-right: 8px;
                vertical-align: middle;
            }
            .contentBox-main {
                display: flex;
                flex-direction: column;
                ::v-deep   .el-form {
                    width: 100%;
                    .el-select {
                        margin-right: 14px;
                    }

                    .el-form-item__label {
                        font-size: 12px;
                        line-height: 30px;
                    }

                    .el-form-item__content {
                        line-height: 30px;
                    }

                    .el-input__inner {
                        line-height: 30px;
                        height: 30px;
                        font-size: 12px;
                    }

                    .el-input__icon {
                        line-height: 30px;
                    }
                }
                .checkBox-info {
                    margin-left: 10px;
                    justify-content: center;
                    align-items: center;
                    line-height: 30px;
                    font-size: 12px;
                    font-family: PingFangSC, PingFangSC-Regular;
                    font-weight: 400;
                    text-align: left;
                    color: #333333;
                }
                .chainPrice-info {
                    color: #ff2121;
                }
                .contentBox-main-item {
                    display: flex;
                    align-items: center;
                    margin-bottom: 10px;
                    >p {
                        width: 150px;
                        display: flex;
                        justify-content: flex-end;
                        white-space: nowrap;
                        margin: 0;
                        span {
                            color: red;
                        }
                    }
                    .el-input {
                        width: 300px;
                        margin-right: 20px;
                    }
                    .el-checkbox {
                        margin-right: 10px;
                    }
                    .main-item-input-tips {
                        display: flex;
                        flex-direction: column;
                        p {
                            margin: 0;
                        }
                    }
                }
            }
            .other-tips {
                color: red;
            }
            .tips {
                .contentBox-main-item {
                    align-items: flex-start;
                    >p {
                        line-height: 40px;    
                    }
                    .main-item-input-tips {
                        color: red;
                        line-height: 30px;
                    }
                }
            }
        }
    }
}
</style>
<style lang="scss">
.el-switch__label {
  position: absolute;
  display: none;
  font-size: 10px !important;
  color: #fff !important;
}
.el-switch__label * {
  font-size: 10px !important;
}
/*打开时文字位置设置*/
.el-switch__label--right {
  z-index: 1;
  right: 23px; // 这里是重点
  top: 0.5px;
}
/*关闭时文字位置设置*/
.el-switch__label--left {
  z-index: 1;
  left: 28px; // 这里是重点
  top: 0.5px;
}
/*显示文字*/
.el-switch__label.is-active {
  display: block;
}
.el-switch__core {
  width: 74px;
  height: 22px;
  border: 2px solid #dcdfe6;
  border-radius: 13px;
}
</style>