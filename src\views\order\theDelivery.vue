<template>
  <div class="divBox">
    <div class="orderTitle">
      <span>{{ titleInfo }}</span>
      <el-button
        type="primary"
        size="small"
        @click="$router.push('/orderList')"
      >
        返回
      </el-button>
    </div>
    <el-row>
      <div class="paddingDiv redDiv flex">
        注意：允许商品部分发货，未发商品若后期没有补发，请及时与客户沟通办理退款
        <el-button
          v-if="canEdit && !isEditing"
          type="primary"
          size="small"
          @click="isEditing = true; isDetail = false"
        >
          编辑
        </el-button>
      </div>
      <div class="paddingDiv">
        订单编号：{{ orderNo }}<span style="padding-left: 20px">客户名称：{{ merchantName }}</span>
      </div>
    </el-row>
    <el-row style="padding-top: 20px">
      <el-table
        ref="cargoTable"
        :data="deliveryList"
        :row-key="(row) => row.id"
        default-expand-all
        style="width: 100%"
        border
      >
        <el-table-column
          type="expand"
          width="1"
        >
          <template
            slot-scope="scope"
          >
            <div
              style="padding-bottom: 10px"
            >
              <el-row
                v-for="(item,index) in scope.row.orderConsignmentDetails"
                :key="String(index)+String(item.orderDetailId)"
                class="row-bg"
              >
                <el-col :span="5">
                  <span
                    class="font12"
                    style="padding-left: 5px"
                  >批号：</span>
                  <el-input
                    v-model.trim="item.batchCode"
                    :disabled="isDetail"
                    size="small"
                    style="width: 200px"
                  />
                </el-col>
                <el-col :span="5">
                  <span class="font12">批号生产日期：</span>
                  <el-date-picker
                    v-model="item.productionDate"
                    :disabled="isDetail"
                    type="date"
                    size="small"
                    placeholder="选择日期"
                    style="width: 160px"
                  />
                </el-col>
                <el-col :span="4">
                  <span class="font12">批号有效期：</span>
                  <el-date-picker
                    v-model="item.batchValidDate"
                    :disabled="isDetail"
                    type="date"
                    size="small"
                    placeholder="选择日期"
                    style="width: 134px"
                  />
                </el-col>
                <el-col :span="5">
                  <span class="font12"><i style="color: #ff2121">*</i>批号发货数量：</span>
                  <el-input
                    v-model.trim="item.batchConsNum"
                    :disabled="isDetail"
                    size="small"
                    style="width: 140px"
                    @blur.prevent="bulrPrice(item.batchConsNum, scope.row.productPrice, item)"
                  />
                </el-col>
                <el-col :span="5">
                  <!-- <span class="font12">批号发货金额：</span>
                  <el-input
                    v-model.trim="item.batchPrice"
                    :disabled="isDetail"
                    size="small"
                    style="width: 120px"
                  /> -->
                  <div
                    v-if="!isDetail"
                    class="inputDiv"
                    style="width: auto;display: inline-block"
                  >
                    <span
                      v-if="index === 0"
                      @click="addDomainList(scope.row.orderConsignmentDetails, scope.row.id)"
                    >+</span> <span
                      v-else
                      @click.prevent="removeDomainList(item, scope.row.orderConsignmentDetails)"
                    >-</span>
                  </div>
                </el-col>
              </el-row>
            </div>
          </template>
        </el-table-column>
        <el-table-column
          type="index"
          width="55"
        />
        <el-table-column
          label="商品名称"
          prop="productName"
          width="200"
        />
        <el-table-column
          label="规格"
          prop="spec"
          width="200"
        />
        <el-table-column
          label="单价"
          prop="productPrice"
          width="200"
        />
        <el-table-column
          label="采购数量"
          prop="productAmount"
          width="200"
        />
        <el-table-column
          label="可发货数量"
          prop="canSendSkuCount"
          width="200"
        />
        <el-table-column
          label="金额"
          prop="canSendSkuAmount"
          width="200"
        />
      </el-table>
    </el-row>
    <!-- <el-row
      v-if="Number(activeOrderStatus)!==2&&Number(activeOrderStatus)!==3&&Number(activeOrderStatus)!==91"
      style="padding-top: 20px"
    >
      <el-form
        v-if="!isReadyComplete && !isStockInformation"
        ref="logInfo"
        :inline="true"
        :model="logInfo"
        size="small"
        label-width="140px"
        class="demo-dynamic"
      >
        <el-form-item
          label="物流方式："
        >
          <el-input
            v-if="isDetail"
            v-model="logInfo.logisticsWayDesc"
            disabled
          />
          <el-select
            v-else
            v-model="logInfo.logisticsWayDesc"
            placeholder="请选择物流方式"
          >
            <el-option
              v-for="(item,index) in logisticsList"
              :key="index"
              :label="item.name"
              :value="item.name"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          label="物流单号："
        >
          <div
            v-for="(domain, index) in logInfo.trackingNoList"
            :key="index"
            class="inputDiv"
          >
            <el-input
              v-model.trim="logInfo.trackingNoList[index]"
              :disabled="isDetail"
            />
            <p
              v-if="!isDetail"
              class="conP"
            >
              <span
                v-if="index === 0"
                @click="addDomain"
              >+</span> <span
                v-else
                @click.prevent="removeDomain(domain)"
              >-</span>
            </p>
          </div>
        </el-form-item>
      </el-form>
    </el-row> -->
    <el-row
      v-if="!isDetail"
      style="text-align: right;padding: 20px 0"
    >
      <el-button
        size="mini"
        @click="handleCancel"
      >
        取 消
      </el-button>
      <el-button
        type="primary"
        size="mini"
        @click="confirmD"
      >
        确 定
      </el-button>
    </el-row>
  </div>
</template>

<script>
import { getQueryDeliveryPageInfo, saveDeliveryInfo, apiUpdateOrderStatusWaitDelivery, updateOrderConsignmentDetail } from '@/api/order/index';

export default {
  name: 'OrderListDelivery',
  data() {
    return {
      isEditing: false,
      canEdit: false,
      deliveryList: [],
      logInfo: { logisticsWayDesc: '', logisticsWay: '', trackingNoList: [], orderNo: '' },
      logisticsList: [],
      orderNo: '',
      merchantName: '',
      obj: {
        batchCode: '',
        productionDate: null,
        batchValidDate: null,
        batchConsNum: null,
        batchPrice: null,
      },
      isDetail: false,
      isReadyComplete: false,
      isStockInformation: false,
      titleInfo: '发货',
      stockInformationTitle: '发货',
      tipHeight: document.documentElement.clientHeight / 3,
      routerObj: '',
      activeOrderStatus: '',
    };
  },
  activated() {
    if (this.routerObj && JSON.stringify(this.routerObj) !== JSON.stringify(this.$route.query)) {
      this.logInfo = { logisticsWayDesc: '', logisticsWay: '', trackingNoList: [], orderNo: '' };
      this.queryInfo();
    }
  },
  created() {
    this.queryInfo();
  },
  methods: {
    handleCancel() {
      if (this.canEdit) {
        this.isDetail = true;
        this.isEditing = false;
        return false;
      }
      this.$router.go(-1);
    },
    queryInfo() {
      const _that = this;
      this.routerObj = this.$route.query;
      const { orderNo, detail, readyComplete, stockInformation, activeOrderStatus, canEdit } = this.routerObj;
      this.canEdit = canEdit || false;
      this.logInfo.orderNo = orderNo;
      this.isDetail = !!detail;
      this.isReadyComplete = readyComplete;
      this.isStockInformation = stockInformation;
      this.titleInfo = readyComplete ? '备货完成' : (stockInformation ? '备货信息' : '备货信息');
      this.activeOrderStatus = activeOrderStatus || '';
      getQueryDeliveryPageInfo({ orderNo }).then((res) => {
        if (res.code === 0) {
          const list = JSON.parse(JSON.stringify(res.result.orderDetails));
          list.forEach((item) => {
            if (item.canSendSkuCount > 0 && item.orderConsignmentDetails.length < 1) {
              const obj = JSON.parse(JSON.stringify(_that.obj));
              console.log(obj,'obj');
              obj.orderDetailId = item.id;
              obj.batchConsNum = item.canSendSkuCount;
              obj.batchPrice = item.canSendSkuAmount;
              item.orderConsignmentDetails.push(obj);
            }
          });
          this.deliveryList = list;
          this.merchantName = res.result.merchantName;
          this.orderNo = res.result.orderNo;
          this.logisticsList = res.result.logisticsList;
          this.logInfo.logisticsWayDesc = res.result.orderTrackInfo? res.result.orderTrackInfo.logisticsWayDesc : '';
          this.logInfo.logisticsWay = res.result.orderTrackInfo? res.result.orderTrackInfo.logisticsWay : '';
          if (res.result.orderTrackInfo && res.result.orderTrackInfo.trackingNoList.length > 0) {
            this.logInfo.trackingNoList = res.result.orderTrackInfo.trackingNoList;
          } else {
            this.logInfo.trackingNoList.push('');
          }
        } else {
          this.$message.error({ message: res.message, offset: this.tipHeight });
        }
      });
    },
    bulrPrice(value, priceOne, item) {
      if (value) {
        const num = ((priceOne * 100) * value) / 100;
        item.batchPrice = num;
      }
    },
    removeDomainList(data, list) {
      const index = list.indexOf(data);
      if (index !== -1) {
        list.splice(index, 1);
      }
    },
    addDomainList(list, orderDetailId) {
      const obj = JSON.parse(JSON.stringify(this.obj));
      obj.orderDetailId = orderDetailId;
      list.push(obj);
    },
    removeDomain(data) {
      const index = this.logInfo.trackingNoList.indexOf(data);
      if (index !== -1) {
        this.logInfo.trackingNoList.splice(index, 1);
      }
    },
    addDomain() {
      this.logInfo.trackingNoList.push('');
    },
    confirmD() {
      let listAry = [];
      const sAry = [];
      const dAry = [];
      this.deliveryList.forEach((item, index) => {
        listAry = listAry.concat(item.orderConsignmentDetails);
        let all = 0;
        item.orderConsignmentDetails.forEach((item) => {
          all = Number(all) + Number(item.batchConsNum);
        });
        if (all > item.canSendSkuCount) {
          dAry.push((index + 1));
        }
      });
      if (dAry.length > 0) {
        const str = `发货数量总和不能大于可发货数量,序号为${dAry[0]}的商品不符合`;
        this.$alert(str, '温馨提示', {
          confirmButtonText: '确定',
          callback: () => {},
        });
        return;
      }
      listAry.forEach((item) => {
        if (!item.batchConsNum) {
          sAry.push(item);
        }
      });
      if (sAry.length > 0) {
        this.$message.warning({ message: '请填批号发货数量', offset: this.tipHeight });
        return;
      }
      const track = JSON.parse(JSON.stringify(this.logInfo.trackingNoList));
      track.forEach((item, index) => {
        if (item === ' ' || !item) {
          track.splice(index, 1);
        }
      });
      const { orderNo, merchantId } = this.$route.query;
      const that = this;
      this.logisticsList.map((item) => {
        if (item.name == that.logInfo.logisticsWayDesc) {
          that.logInfo.logisticsWay = item.code;
        }
      });
      const params = {
        list: listAry,
        trackingNoListJson: track,
        orderNo,
        logisticsWayDesc: this.logInfo.logisticsWayDesc,
        logisticsWay: this.logInfo.logisticsWay,
        merchantId,
      };
      if (this.canEdit && this.isEditing) {
        // 备货完成修改状态为待配送
        delete params.trackingNoListJson;
        delete params.logisticsWayDesc;
        updateOrderConsignmentDetail(params).then((res) => {
          if (res.code === 0) {
            this.$message.success({ message: res.msg || '成功', offset: this.tipHeight });
            this.isDetail = true;
            this.isEditing = false;
            // window.openTab('/orderList');
          } else {
            this.$message.error({ message: res.msg, offset: this.tipHeight });
          }
        });
        return false;
      }
      if (!this.isReadyComplete) {
        saveDeliveryInfo(params).then((ress) => {
          if (ress.code === 0) {
            this.$message.success({ message: '发货成功', offset: this.tipHeight });
            window.openTab('/orderList');
          } else {
            this.$message.error({ message: ress.msg, offset: this.tipHeight });
          }
        });
      } else {
        // 备货完成修改状态为待配送
        delete params.trackingNoListJson;
        delete params.logisticsWayDesc;
        apiUpdateOrderStatusWaitDelivery(params).then((ress) => {
          if (ress.code === 0) {
            this.$message.success({ message: '发货成功', offset: this.tipHeight });
            window.openTab('/orderList');
          } else {
            this.$message.error({ message: ress.msg, offset: this.tipHeight });
          }
        });
      }
    },
  },
};
</script>

<style scoped lang="scss">
.row-bg{
  padding-top: 10px;
}
.font12{
  font-size: 12px;
}
.flex {
  display: flex;
  justify-content: space-between;
}
.paddingDiv{
  padding-top: 15px;
}
.redDiv{
  color: #FF2121;
}
.inputDiv{
  width: 243px;
  display: flex;
  align-items: center;
  padding-bottom: 20px;
  span{
    cursor: pointer;
    margin-left: 10px;
    display: flex;
    width: 21px;
    height: 18px;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    border-radius: 50%;
    border: 1px solid #5d96db;
    color: #5d96db;
  }
}
::v-deep  .el-table__expand-column {
  pointer-events: none;
}
::v-deep  .el-table__expand-column .el-icon {
  visibility: hidden;
}
::v-deep  .el-table__expanded-cell[class*=cell] {
  padding: 0;
}
.divBox{
  padding: 0 20px;
  .orderTitle{
    padding: 10px 0;
    display: flex;
    justify-content: space-between;
    border-bottom: 1px solid #dddddd;
    span{
      font-size: 20px;
      color: #333333;
      font-weight: bold;
    }
  }
}
.conP{
  display: inline-block;
  padding: 0;
  margin: 0;
}

</style>
