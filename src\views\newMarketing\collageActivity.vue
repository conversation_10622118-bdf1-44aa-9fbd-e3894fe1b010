<template>
  <div class="contentBox">
    <div class="topTip-prompt">
      <i class="el-icon-warning-outline"
        style=""></i>温馨提示：为了提升药店的拼团体验，提交拼团前请认真核对起拼数量、拼团价等内容，报名截止后不可新增商品。编辑截止时间后，拼团价、起拼数量等不可修改。
    </div>
    <el-row type="flex" align="middle">
      <span class="sign" />
      <div class="searchMsg">拼团活动</div>
    </el-row>
    <div class="topTip">
      <div v-permission="['marketing_group_apply']" v-if="isShowTheme">
        <div class="topTip-info">
          活动说明：您可参与下列拼团主题，已上线的拼团活动在活动时间内将显示在药帮忙的拼团会场、搜索页、店铺首页
        </div>
        <el-tabs v-model="tagData.activeName" @tab-click="handleClick">
          <el-tab-pane v-for="item in emnu" :label="item.label" :name="`${item.code}`"></el-tab-pane>
          <el-tab-pane label="全部活动" name="0"></el-tab-pane>
        </el-tabs>
        <div class="tag-list">
          <div v-for="tag in showListNow" :key="tag.id" @click="tagClick(tag)"
            :class="{ 'selectCss': activitySelect.frameReportId == tag.frameReportId }">
            <div style="display: flex;justify-content: space-between;align-items: center;">
              <p style="font-size: 16px;font-weight: 600;">{{ tag.actName }}</p>
              <span :class="tag.frameStatus == 1 ? 'tag-class1' : 'tag-class2'"
                style="padding: 2px 3px;border-radius: 5px;font-size: 13px;">{{ tag.frameStatusName }}</span>
            </div>
            <div style="font-size: 14px;color: rgb(147 147 147);margin: 5px 0;">{{ timestampToFormat(tag.validStime) }}
              ~ {{ timestampToFormat(tag.validEtime) }}</div>
            <div style="margin-top: 25px;">
              <span>报名截止: {{ timestampToFormat(tag.registrationEtime) }}</span>
              <span style="margin-left: 10px;color:red;">(剩{{ Math.floor((tag.registrationEtime - Date.now()) / 3600000)
                }}小时)</span>
            </div>
            <div style="margin-top: 10px;">
              <span>编辑截止: {{ timestampToFormat(tag.auditEtime ? tag.auditEtime : tag.validEtime) }}</span>
              <span style="margin-left: 10px;color:red;">(剩{{ Math.floor(((tag.auditEtime ? tag.auditEtime :
                tag.validEtime) - Date.now()) / 3600000) }}小时)</span>
            </div>
            <div style="margin-top: 10px;">
              <span v-if="tag.shopReportNum && tag.shopReportLimit">已提报数: {{ tag.shopReportNum }} / {{
                tag.shopReportLimit }}</span>
              <span v-else-if="tag.shopReportNum">已提报数: {{ tag.shopReportNum }}</span>
              <span v-else-if="tag.shopReportLimit">已提报数: 0 / {{ tag.shopReportLimit }}</span>
              <span v-else>已提报数: 0</span>
            </div>
          </div>
        </div>


        <div v-if="tagData.showList.length > 3"
          style="padding: 10px 0;display: flex;justify-content: center;margin-top: 10px;border-radius: 5px;">
          <span style="color: #4183d5;cursor: pointer;user-select: none;" @click="tagData.blod = !tagData.blod;">{{
            tagData.blod ? '收起' : '展开' }}所有活动</span>
        </div>
      </div>

    </div>
    <activityInfo :frameReportId="activitySelect.frameReportId" :activityInfo="activityInfos"></activityInfo>


    <!-- //tabs -->
    <div class="sessionAssignments" v-if="activitySelect.frameReportId">
      <div class="head-tabs" style="display: flex;">
        <button @click="select(1)" :class="{ btn: true, select: pageType == 1 }" style="border-radius: 0;">推荐参团
          <el-tooltip class="item" effect="dark"
            content="系统自动抓取店铺内平台优价商品（满足框架商品范围，活动状态为进行中及上个周期有动销的团活动），根据推荐参团设置复制原活动信息并根据平台动销自动生成拼团活动，点击“提交审核”即可快速参团"
            placement="top">
            <i class="el-icon-warning-outline"></i>
          </el-tooltip>
        </button>
        <button @click="select(2)" :class="{ btn: true, select: pageType == 2 }"
          style="margin: 0;border-radius: 0;">店铺动销商品提报
          <el-tooltip class="item" effect="dark" content="根据店铺近7天动销的拼团、批购包邮活动进行一键提报；" placement="top">
            <i class="el-icon-warning-outline"></i>
          </el-tooltip>
        </button>
        <button @click="select(3)" :class="{ btn: true, select: pageType == 3 }" style="border-radius: 0;">批量导入活动ID
          <el-tooltip class="item" effect="dark" content="根据历史拼团活动、批购包邮活动快速提报" placement="top">
            ><i class="el-icon-warning-outline"></i>
          </el-tooltip>
        </button>
        <button @click="select(4)" :class="{ btn: true, select: pageType == 4 }"
          style="margin: 0;border-radius: 0;">批量导入商品

          <el-tooltip class="item" effect="dark" content="批量导入普通商品创建拼团活动" placement="top">
            <i class="el-icon-warning-outline" title=""></i>
          </el-tooltip>
        </button>

        <button @click="select(5)" :class="{ btn: true, select: pageType == 5 }" style="border-radius: 0;">单个活动ID提报
          <el-tooltip class="item" effect="dark" content="根据历史拼团活动快速提报拼团" placement="top">
            ><i class="el-icon-warning-outline"></i>
          </el-tooltip>
        </button>


        <button @click="select(6)" :class="{ btn: true, select: pageType == 6 }" style="border-radius: 0;">单个商品提报

          <el-tooltip class="item" effect="dark" content="根据普通商品提报拼团活动" placement="top">
            <i class="el-icon-warning-outline"></i> </el-tooltip>
        </button>
        <button @click="select(7)" :class="{ btn: true, select: pageType == 7 }"
          style="margin: 0;border-radius: 0;">已提取待确认

          <el-tooltip class="item" effect="dark" content="请谨慎检查活动数据，无误后再进行提报" placement="top">
            <i class="el-icon-warning-outline"></i>
          </el-tooltip>
        </button>

      </div>
      <div class="tabs">
        <div v-if="[2, 3, 4].includes(pageType)" class="content-tab">
          <importExcel :frameReportId="activitySelect.frameReportId" :activityInfo="activityInfos" @select="select" :importType="pageType">
          </importExcel>
        </div>
        <div v-if="[5, 6].includes(pageType)" class="content-tab">
          <idOrshopReprort :importType="pageType" :frameReportId="activitySelect.frameReportId"
            :activityInfo="activityInfos" :customerGroupVO="customerGroupVO"></idOrshopReprort>
        </div>
        <div v-if="pageType == 1" class="content-tab">
          <div
            style="margin-bottom: 10px;position: relative;display: flex;justify-content:space-between;flex-wrap: wrap;">
            <div style="width: 300px;padding: 10px 0">
              <el-button type="primary" @click="openRecommend"> 推荐参团设置</el-button>
              <el-button type="primary" @click="exportData" style="background-color: white;color: #4183d5;">
                导出</el-button>
              <el-button @click="deleteComment" :disabled="selections.length <= 0"> 删除</el-button>
            </div>
            <span style="display: inline-block;width: 500px;padding: 10px 0">
              <el-select v-model="ruleForm.modifiedMark" placeholder="信息有无调整" clearable>
                <el-option label="全部" :value="''"></el-option>
                <el-option label="有信息调整的活动" :value="1"></el-option>
                <el-option label="无信息调整的活动" :value="2"></el-option>
                <el-option label="有拼团价格调整的活动" :value="3"></el-option>
                <el-option label="无拼团价格调整的活动" :value="4"></el-option>
                <el-option label="有起拼数量调整的活动" :value="5"></el-option>
                <el-option label="无起拼数量调整的活动" :value="6"></el-option>
                <el-option label="异常无法提报的活动" :value="7"></el-option>
              </el-select>
              <el-input v-model="ruleForm.queryString" style="width: 180px;margin-left: 10px;"
                @keyup.enter.native="getLists" placeholder="商品编码/CSUID/商品名称/报名ID/活动ID"></el-input>
              <el-button type="primary" @click="getLists" style="margin-left: 10px;"> 筛选</el-button>
            </span>
          </div>
          <xyy-table :data="list" v-loading="loading" :list-query="listQuery" :col="col" :has-selection="true" @get-data="getList"
            @selectionCallback="setCheckedDatas" ref="multipleTable">


            <template slot="commodityInformation" slot-scope="{ col }">
              <el-table-column :key="col.index" :label="col.name" :width="col.width">
                <template slot-scope="{ row }">

                  <div>CSUID:{{ row.skuId }}</div>
                  <div>商品编码:{{ row.barcode }}</div>
                  <div>商品ERP编码:{{ row.erpCode }}</div>
                  <div>商品名称:{{ row.productName }}</div>
                  <div>规格:{{ row.spec }}</div>
                  <div>生产厂家:{{ row.manufacturer }}</div>
                  <div v-if="row.originActId">原活动ID:PT{{ row.originActId }}</div>
                </template>
              </el-table-column>
            </template>
            <template slot="showName">
              <el-table-column label="展示信息" width="150px">
                <template slot-scope="{row}">
                  <!-- <img :src="row.imageUrl" style="width: 80px;height: 73px;margin-bottom: 4px"/> -->

                  <el-image style="width: 80px; height: 73px; margin-bottom: 4px" :src="row.imageUrl"
                    :preview-src-list="[row.imageUrl]" @click.prevent />
                  <div>{{ row.productName }}</div>
                  <div>虚拟供应商： <span style="color:#4183d5 ;"> {{ Number(row.isVirtualShop) === 1 ? '是' : '否'
                      }}&nbsp;</span>
                    <img src="@/assets/edit.png" style="width: 20px;position: relative;top: 4px;cursor: pointer;"
                      @click="updateData.actId = row; updateData.type = 2; updateData.visible = true;" alt="">
                  </div>
                </template>

              </el-table-column>
            </template>
            <template slot="groupPrice" slot-scope="{ col }">
              <el-table-column :key="col.index" :label="col.name" width="250px">
                <template slot-scope="{ row }">
                  <div>
                    <div>
                      起拼数量: <span :style="{ color: row.isStartQtyModified === 1 ? 'red' : '#4183d5' }"></span> {{ row.startQty }}
                    </div>
                    <div v-if="row.fob">
                      单体采购价：￥{{ row.fob }}
                    </div>
                    <div style="color:#4183d5">

                      提报价/拼团价：<span :style="{ color: row.isPriceModified === 1 ? 'red' : '#4183d5' }">￥{{ row.groupPrice
                        }}</span>
                      <span>
                        <img src="@/assets/edit.png" style="width: 20px;position: relative;top: 4px;cursor: pointer;"
                          @click="updateData.actId = row; updateData.type = 1; updateData.visible = true;" alt="">

                      </span>
                    </div>
                    <div v-if="row.accessGroupPrice">
                      准入价：￥ {{ row.accessGroupPrice }}
                    </div>
                    <div v-if="row.buyMostPrice&&Number(row.groupPrice)>Number(row.buyMostPrice)">
                      <span >热销价：￥ {{ row.buyMostPrice }}</span>
                    </div>
                    <div v-if="row.snapPrice">
                      <span>原提报价格：￥ {{ row.snapPrice }}</span>
                    </div>
                    <!-- <div>
                      <span>热销拼团价：￥ {{ row.buyMostPrice }}</span>
                    </div> -->
                    <div>
                      <span>降价幅度： {{row.discount}}</span>
                    </div>
                  </div>
                </template>
              </el-table-column>
            </template>
            <template slot="activityReportGroupAmountDtos" slot-scope="{ col }">
              <el-table-column :key="col.index" :label="col.name" :width="col.width">
                <template slot-scope="{ row }">
                  <div v-for="(option, index) in row.activityReportGroupAmountDtos" :key="index">
                    <div>{{ option.name }}:¥{{ option.amount }}</div>
                  </div>
                </template>
              </el-table-column>
            </template>
            <template slot="isCopyCsuModel" slot-scope="{ col }">
              <el-table-column :key="col.index" :label="col.name" :width="col.width">
                <template slot-scope="{ row }">
                  <div >
                   
                    <div style="color:#4183d5;text-align: left;" >供货信息配置方式：
                      {{ ((row.frameActReportSaleScopeDTO ? row.frameActReportSaleScopeDTO : {}).scopeType == -1 ||(row.frameActReportSaleScopeDTO ? row.frameActReportSaleScopeDTO : {}).isCopySaleArea==1)?
                        '复制原活动销售范围' : {
                          1: '人群', 2: '业务商圈、供货对象、黑白名单'
                        }[(row.frameActReportSaleScopeDTO ? row.frameActReportSaleScopeDTO : {} ||
                      {}).scopeType||(row.frameActReportSaleScopeDTO ? row.frameActReportSaleScopeDTO : {isCopySaleArea:0}).isCopySaleArea-1] }}
                      <span>
                      <img src="@/assets/edit.png" style="width: 20px;position: relative;top: 4px;cursor: pointer;"
                        @click="updateData.actId = row; updateData.type = 3; updateData.visible = true;" alt="">

                    </span>
                    </div>
                    
                  </div>
                  <div style="text-align: left;" v-if=" (row.frameActReportSaleScopeDTO ? row.frameActReportSaleScopeDTO : {}).customerGroupId || (row.frameActReportSaleScopeDTO ? row.frameActReportSaleScopeDTO : {} || {}).scopeType == 1
                  ">人群ID:
                    <span>{{ (row.frameActReportSaleScopeDTO ? row.frameActReportSaleScopeDTO : {}).customerGroupId
                      }}<i class="el-icon-view" style="color: #4183d5; font-size: 16px"
                        @click="addCroed(row)"></i></span>
                  </div>
                  <div style="text-align: left;" v-if="(row.frameActReportSaleScopeDTO ? row.frameActReportSaleScopeDTO : {}).customerGroupName || (row.frameActReportSaleScopeDTO ? row.frameActReportSaleScopeDTO : {} || {}).scopeType == 1
                  ">人群名称:{{ (row.frameActReportSaleScopeDTO ? row.frameActReportSaleScopeDTO : {}).customerGroupName }}
                  </div>
                  <div >
                    <div style="text-align: left;" v-if="(row.frameActReportSaleScopeDTO ? row.frameActReportSaleScopeDTO : {} || {}).busAreaName">业务商圈:{{ (row.frameActReportSaleScopeDTO ? row.frameActReportSaleScopeDTO : {} || {}).busAreaName }}
                      <i class="el-icon-view" style="color: #4183d5; font-size: 16px;;" @click="
                        viewBusiness({ id: (row.frameActReportSaleScopeDTO ? row.frameActReportSaleScopeDTO : {} || {}).busAreaId })
                        "></i>
                    </div>
                    <div v-if="
                      ((row.frameActReportSaleScopeDTO ? row.frameActReportSaleScopeDTO : {} || {}).controlUserTypeList || []).length
                    " style="text-align: left;">
                      <el-tooltip class="item" effect="dark" placement="top">
                        <template slot="content">
                          <div>供货对象:{{
                              (
                                (row.frameActReportSaleScopeDTO ? row.frameActReportSaleScopeDTO : {} || {}).controlUserTypeList
                                || []
                              ).join()
                            }}
                          </div>
                        </template>
                        <div class="busAreaNameBox">供货对象:{{
                          (
                            (row.frameActReportSaleScopeDTO ? row.frameActReportSaleScopeDTO : {} || {}).controlUserTypeList
                            || []
                          ).join()
                          }}</div>
                      </el-tooltip>
                    </div>
                    <div style="text-align: left;"
                      v-if="(row.frameActReportSaleScopeDTO ? row.frameActReportSaleScopeDTO : {} || {}).controlGroupName">黑白名单:{{
                        { 1: '黑名单', 2: '白名单' }[
                        (row.frameActReportSaleScopeDTO ? row.frameActReportSaleScopeDTO : {} || {}).controlRosterType
                        ]
                      }}-{{ (row.frameActReportSaleScopeDTO ? row.frameActReportSaleScopeDTO : {} || {}).controlGroupName }}
                      <i class="el-icon-view" style="color: #4183d5; font-size: 16px"
                        @click="seeNameList((row.frameActReportSaleScopeDTO ? row.frameActReportSaleScopeDTO : {} || {}).controlGroupId)"></i>
                    </div>
                  </div>

                </template>
              </el-table-column>
            </template>
            <template slot="activityInventory" slot-scope="{ col }">
              <el-table-column :key="col.index" :label="col.name" :width="col.width">
                <template slot-scope="{row}">
                  <div style="text-align: left;">商品库存:{{ row.totalAvailableQty }}</div>

                  <div style="text-align: left;">活动总限购数量: <span style="color: #4183d5;" v-if="row.totalLimitNum!=null&&row.totalLimitNum!=-1">{{ row.totalLimitNum }}</span><span style="color: #4183d5;" v-else>不限制</span><span>
                      <img src="@/assets/edit.png" style="width: 20px;position: relative;top: 4px;cursor: pointer;"
                        class="el-icon-edit-outline"
                        @click="updateData.actId = row; updateData.type = 4; updateData.visible = true;" alt="">
                    </span>
                  </div>
                  <div style="text-align: left;">单店限购类型：{{ row.personalLimitType ? personalLimitTypeList[row.personalLimitType] : personalLimitTypeList[0] }}</div>
                  <div style="text-align: left;" v-if="row.personalQty!=null&&row.personalQty!=-1">单客户限购数量:{{ row.personalQty }}</div>
                  <div style="text-align: left;" v-else>单客户限购数量:不限制</div>
                  <div style="text-align: left;">剩余活动库存:{{ row.totalLimitQty }}</div>
                  <!-- <div>在途库存:{{ row.onTheWayStock }}</div> -->
                </template>
              </el-table-column>
            </template>
            <template slot="remark" slot-scope="{ col }">
              <el-table-column :key="col.index" :label="col.name" :width="col.width">
                <template slot-scope="{ row }">
                  {{ row.remark }}
                </template>
              </el-table-column>
            </template>
          </xyy-table>
          <div
            style="text-align: center;box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1), 0 0 0 0 rgba(0, 0, 0, 0);padding:20px;border:1px solid rgb(241,243,244) ;">
            <el-button type="primary" @click="submitSelections" style="background-color: white;color: #4183d5;"
              :class="{ disabled: !selections.length }" :disabled="!selections.length"> 提交选中({{ selections.length
              }})</el-button>
            <el-button type="primary" @click="submitSelections('all')" :disabled="this.listQuery.total==0"> 提交全部（{{ listQuery.total }}）</el-button>
          </div>
        </div>
        <div v-if="pageType == 7" class="content-tab">
          <exportTable :baseId="baseId"></exportTable>
        </div>
      </div>
    </div>
    <div v-if="!activitySelect.frameReportId"
      style="text-align: center;box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1), 0 0 0 0 rgba(0, 0, 0, 0);padding:20px;border:1px solid rgb(241,243,244) ;">
      <el-button type="primary" @click="" style="background-color: white;color: #4183d5;"> 提交选中({{ selections.length
        }})</el-button>
      <el-button type="primary" @click=""> 提交全部（{{ listQuery.total }}）</el-button>
    </div>

    <pintuanPrice1 v-model:data="updateData" @getList="getList(listQuery, true)"></pintuanPrice1>
    <batch-edit v-if="bulkChangesDialog" @cancelModal="cancelModal" @refresh="getList(listQuery, true)" />

    <CustomerInfoLog v-if="crowdDialogVis" :market-customer-group-id="innerSelected" @cancelModal="cancelModal" />
    <listOperationLog v-if="listOperationLogVisible" :list-operation-log-visible.sync="listOperationLogVisible"
      :modify-config="listOperationLogConfig" />
    <!-- 查看业务商圈 -->
    <business-circle-detail-dialog :row="selectViewRow" v-if="viewBusinessDialog"
      v-model="viewBusinessDialog"></business-circle-detail-dialog>
    <!-- 查看黑白名单 -->
    <ListOfControlGroups v-if="showNameList" :control-group-id="controlGroupId" @cancelDialog="cancelDialog" />
    <el-dialog title="查看阶梯价" :visible.sync="groupLevelPriceVis" width="750">
      <p>
        起拼数量：{{ currentGroupLevelPriceItem.startQty }}
        <span style="margin-left: 10px">
          拼团价格：{{ currentGroupLevelPriceItem.groupPrice }}
        </span>
      </p>
      <el-table :data="currentGroupLevelPriceItem.activityReportGroupLevelPriceDTOList" style="margin-top: 10px" border
        :header-cell-style="{ background: '#eeeeee', color: '#666666' }">
        <el-table-column prop="startQty" align="center" label="促销起始数量" width="120" />
        <el-table-column prop="discountPrice" align="center" label="拼团价格" width="120" />
        <el-table-column prop="initiatorDTOList" align="center" label="补贴情况">
          <template slot-scope="{ row }">
            <p v-for="(item, index) in row.initiatorDTOList" :key="index">
              {{ item.name }}：{{ item.amount }}
            </p>
          </template>
        </el-table-column>
      </el-table>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="groupLevelPriceVis = false">
          关闭
        </el-button>
      </span>
    </el-dialog>
    <export-tip :change-export="changeExport" @handleExoprClose="handleExoprClose" :baseId="baseId"
      @handleChangeExport="handleChangeExport" />
    <thisTourIsRecommended @getLists="getLists" ref="thisTourIsRecommended" :isCreate="isCreate" @select="select">
    </thisTourIsRecommended>
  </div>
</template>

<script>
import exportTip from '@/views/other/components/exportTip'; //导出弹窗
import importExcel from "./components/importExcel"
import exportTable from './components/exportTable'
import thisTourIsRecommended from "./components/thisTourIsRecommended" //推荐参团s
import idOrshopReprort from "./components/idOrshopReprort"
import {
  apiGetFrameActBaseForReport,
  apiOffLine,
  apiSelectReportGroupCount,
  getStatusAndCount,
  isCanCreateFullGive,
  exportWarningLimitData,
  getBaseNameList,
  getSummaryInfo,
  getTimeStr,
  freshFrame,
  submitTableData,
  exportTableData,
  querySetting,
  createSetting,
  recommendReportDelete
} from '@/api/market/collageActivity'
import { apiApplyList } from "@/api/market/newCollageActivity"
import activityInfo from "./components/activityInfo"
import CustomerInfoLog from '@/components/customer/customerInfoLog.vue'
import { actionTracking } from '@/track/eventTracking'
import crowdSelectorDialog from './components/crowdSelectorDialog'
import batchEdit from './components/batchEdit'
import listOperationLog from './components/listOperationLog'
import BusinessCircleDetailDialog from '../business-circle/components/businessCircleDetailDialog.vue'
import ListOfControlGroups from '../product/components/listOfControlGroups'
import { mapState } from 'vuex'
import pintuanPrice1 from '../pintuanDataUpdate/pintuanPrice1.vue'
export default {
  components: {
    exportTable,
    activityInfo,
    crowdSelectorDialog,
    batchEdit,
    idOrshopReprort,
    listOperationLog,
    BusinessCircleDetailDialog,
    ListOfControlGroups,
    CustomerInfoLog,
    exportTip,
    pintuanPrice1,
    importExcel,
    thisTourIsRecommended
  },
  filters: {},
  props: {},
  data() {
    return {
      customerGroupVO:{},
      loading:false,
      shopCode: '',
      isCreate: false,
      baseId: '',
      ruleForm: {
        modifiedMark: "",
        queryString: ''
      },
      activityInfos: {},
      activitySelect: {},
      pageType: 1,
      tableId: 1,
      updateData: {
        actId: '',
        type: '',  //1:拼团价格，2：设置虚拟供应商，3：修改供货信息，4：修改活动库存
        visible: false,
        baseId: '',
        shopCode: '',
      },
      emnu: [],
      changeExport: false,
      personalLimitTypeList: ["不限制", "活动期间限购", "每天（每天00:00至24:00）", "单笔订单限购", "每周（周一00:00至周日24:00）", "每月（每月1号00:00至每月最后一天24:00）"],
      groupLevelPriceVis: false,
      currentGroupLevelPriceItem: {},
      reportThemeLists: [],
      list: [],
      listQuery: {
        stepPriceStatus: '',
        code: '',
        idStr: '',
        pageSize: 10,
        page: 1,
        total: 0,
        reportId: '',
        barcode: '',
        erpCode: '',
        productName: '',
        status: '',
        reportTheme: '',
        isPlatformSubsidy: '',
        isAgreement: '',
        isSoldOut: '',
        isSyncStock: '',
        activityTime: [],
        signUpTime: [],
        isUpdateAccessPrice: '',
        warningRateStatus: 2,
        isVirtualShop: '',
        skuId: '',
        actId: '',
        isGiveSku: '',
        warningPriceIsTooLow: '',
        warningStartPersonalQty: '',
        isOrder: ''
      },
      showMore: false,
      crowdDialogVis: false,
      selectIds: '',
      innerSelected: '',
      crowdDialog: false,
      bulkChangesDialog: false,
      selections: [],
      failed: 0,
      isShowTheme: false,
      curTagList: [],
      tagList: [],
      viewBusinessDialog: false, // 查看业务商圈
      showNameList: false, // 查看黑白名单
      selectViewRow: {},
      controlGroupId: '',
      editConfig: {
        platformSubsidyNum: 0,
        searchPlatformSubsidy: false,
        // agreementNum: 0,
        searchAgreement: false,
        rejectedStatusNum: 0,
        searchRejectedStatus: false,
        updateGroupBuyingAcceptPrice: 0,
        searchUpdateGroupBuyingAcceptPrice: false,
        warningRateStatusCount: 0,
        searchWarningRateStatusCount: false,
        warningPriceTooLowCount: 0,
        searchWarningPriceTooLowCount: false,
        warningStartPersonalQtyCount: 0,
        searchWarningStartPersonalQtyCount: false
      },
      col: [
        {
          index: 'commodityInformation',
          name: '商品信息',

          slot: true
        },
        {
          index: 'showName',
          name: '展示信息',
          width: 320,
          slot: true
        },
        {
          index: 'groupPrice',
          name: '拼团价格',
          width: 200,
          slot: true
        },
        {
          index: 'activityInventory',
          name: '库存',

          slot: true
        },
        {
          index: 'isCopyCsuModel',
          name: '供货信息',

          slot: true
        },
        {
          index: 'remark',
          name: '备注',
        
          slot: true
        }

      ],
      newTime: new Date().getTime(),
      weekObj: {
        1: '周一',
        2: '周二',
        3: '周三',
        4: '周四',
        5: '周五',
        6: '周六',
        7: '周日'
      },
      listOperationLogVisible: false,
      listOperationLogConfig: {},
      tabActiveName: '',
      tabStatusOptions: [
        {
          label: '全部',
          value: '',
          count: 0,
          name: 'totalCount'
        },
        {
          label: '待审核',
          value: 1,
          count: 0,
          name: 'waitCount'
        },
        {
          label: '审核不通过-可修改',
          value: 2,
          count: 0,
          name: 'rejectedCount'
        },
        {
          label: '未启动',
          value: 3,
          count: 0,
          name: 'unStartCount'
        },
        {
          label: '进行中',
          value: 4,
          count: 0,
          name: 'startingCount'
        },
        {
          label: '已结束/已下线',
          value: 7,
          count: 0,
          name: 'stopOrOffLineCount'
        }
      ],
      exportLoading: false,
      showGiveBtn: false,
      // customerInfo: {},
      subCount: 0,
      tagData: {
        showList: [],   //展示的活动列表
        blod: false,    //展开或折叠
        activeName: '0',
      }
    }
  },
  computed: {
    showListNow() {
      if (this.tagData.blod) {
        return this.tagData.showList
      } else {
        return this.tagData.showList.slice(0, 3)
      }
    },
    ...mapState('app', ['shopConfig'])
  },
  watch: {
    pageType() {
      if (this.pageType == 1) {
        this.getLists()

      }
    }
  },
  mounted() {
  },
  activated() {

    let showEditFlag = this.$route.query.showEdit || false;
    if (showEditFlag) {
      this.bulkChangesDialog = true;
    }
    if (this.$route.query.refresh) {
      this.getList(this.listQuery, true)
    }
    // this.getDefaultDataFromApollo()
    if (this.$route.query.rejectNum) {
      setTimeout(() => {
        this.tipsSearch('rejectedStatusNum')
      }, 500)
    }
    if (this.$route.query.priceTooLowPrice) {
      setTimeout(() => {
        this.tipsSearch('warningPriceTooLowCount')
      }, 500)
    }
  },
  created() {
    this.GetFrameActBaseForReport()
    this.selectReportGroupCount()
    // this.getList(this.listQuery, true)
    // this.getButtons()
    this.getBaseNameList()
  },
  methods: {
    //提交选中
    submitSelections(e) {
      console.log('selections', this.selections)
      const data = {
        baseId: this.baseId,
        ids: this.selections.map(item => item.id).join(',') //不知道是不是这个Id
      }
      if (e == 'all') {
        delete data.ids
      }
      console.log('data', data)
      submitTableData(data).then(
        res => {
          if (res.status === 'success') {
            if (
              !(res.data.groupBuyingBatchCreatesResult || {}).failureNum
             
             
            ) {
              this.$message({
                message: '提报成功',
                type: 'success'
              });
              window.openTab('/productList',{activeName:50});
            } else {
              let con = `
            <p>提交成功${(res.data.groupBuyingBatchCreatesResult || {}).successNum}条，
            失败${(res.data.groupBuyingBatchCreatesResult || {}).failureNum}条，
            <a style="color: #ff0021" href="${(res.data.groupBuyingBatchCreatesResult || {}).failureExcelFileDownloadUrl}" 
            download="批量导入拼团活动">失败原因请见列表</a></p>`;
              this.$confirm(con, "提示", {
                confirmButtonText: "确定",
                dangerouslyUseHTMLString: true,
                cancelButtonText: "取消",
              })
            }

            this.getLists()
          } else {
            this.$message.error(res.msg || res.errorMsg || '服务异常')
          }
        }
      )
    },
    //推荐删除
    deleteComment() {
      this.$confirm('确定删除已选中数据?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: '',
      }).then(() => {
        recommendReportDelete({ ids: this.selections.map(item => item.id).join(",") }).then(
          res => {
            if (res.code == 1000) {
              this.$message.success('删除成功')
              this.getLists()
            } else {
              this.$message.error(res.msg)
            }
          }
        )
      }).catch(e => { });
    },
    openRecommend() {
      const loading = this.$loading({
        lock: true,
        text: 'Loading',
        spinner: 'el-icon-loading',
        background: 'rgba(255,255,255, 0.8)'
      })

      querySetting({ baseId: this.baseId }).then(
        res => {
          this.$refs.thisTourIsRecommended.open(res.data.setting)
        }
      ).finally(res => {
        loading.close()
      })
      //createSetting
      //updateSetting
      // 给isCreate赋值

    },
    select(tab) {
      this.pageType = tab


    },
    handleClick(e) {
      this.tagData.showList = this.tagList.filter(item => {
        if (e.name == 0) return true;
        if (e.name == 'x') return item.recommendFlag == 1;
        return item.activityClass == e.name
      })
    },
    timestampToFormat(timestamp) {
      // 如果时间戳是秒级，则需要转换为毫秒级
      if (timestamp < 999999999999) {
        timestamp *= 1000;
      }

      let date = new Date(timestamp);
      let year = date.getFullYear();
      let month = ("0" + (date.getMonth() + 1)).slice(-2); // 月份从0开始，所以加1，并补零
      let day = ("0" + date.getDate()).slice(-2); // 补零
      let hour = ("0" + date.getHours()).slice(-2); // 补零
      let minute = ("0" + date.getMinutes()).slice(-2); // 补零
      let second = ("0" + date.getSeconds()).slice(-2); // 补零

      return `${year}-${month}-${day} ${hour}:${minute}:${second}`;
    },
    handleExoprClose() {
      this.changeExport = false;
    },
    handleChangeExport(info) {
      this.changeExport = false;
      if (info === 'go') {
        const path = '/downloadList';
        window.openTab(path);
      }
    },
    handleSeeActivitySaleDataSummaryInfo(row) {
      getSummaryInfo({
        activityType: row.activityType,
        marketingIdStr: row.actIdStr
      }).then((res) => {
        if (res.success) {
          const { data } = res
          row.activitySaleDataSummaryInfo = data.summaryInfo || null
          this.$set(row, row)
        }
      })
    },
    renderHeader(h, { column }) {
      return h('div', [
        h('span', column.label),
        h(
          'el-tooltip',
          {
            props: {
              content:
                '销售数据剔除未支付、已取消、已退款且部分退中活动商品应发货数量等于0的订单数据，仅统计已支付的有效订单。可至详情页查询未支付、已取消订单',
              placement: 'right'
            }
          },
          [h('i', { class: 'el-icon-warning-outline' })]
        )
      ])
    },
    handleGoGroupSalesData(row) {
      // this.$router.push({ path: '/groupSalesData', query: { activityType: row.activityType, marketingIdStr: row.actIdStr } });
      window.openTab('/groupSalesData', {
        activityType: row.activityType,
        marketingIdStr: row.actIdStr
      })
    },
    getBaseNameList() {
      getBaseNameList().then((res) => {
        if (res.success) {
          this.reportThemeLists = res.data.result
        }
      })
    },
    checkGroupLevelPrice(row) {
      this.currentGroupLevelPriceItem = row
      this.groupLevelPriceVis = true
    },
    handleDownWarningStartPersonalQtyCount() {
      actionTracking('group_management_top_quick_search', {
        filter_item: 'suggestion_download'
      })
      exportWarningLimitData().then((res) => {
        this.util.exportExcel(res, '起拼/限购建议.xls')
      })
    },
    getButtons() {
      isCanCreateFullGive({}).then((res) => {
        if (res.code === 1000) {
          this.showGiveBtn = true
        } else {
          this.showGiveBtn = false
        }
      })
    },
    async getStatusAndCount(params) {
      const data = { ...params }
      delete data.status
      const res = await getStatusAndCount(data)
      if (res && res.code === 1000) {
        Object.keys(res.data.reportStatusCountDto).forEach((key) => {
          this.tabStatusOptions.map((item) => {
            if (item.name === key) {
              this.$set(item, 'count', res.data.reportStatusCountDto[key])
            }
            return item
          })
        })
      } else {
        this.$message.error(res.msg || '查询不同状态的报名条数出错')
      }
    },
    getShowTime(aTime) {
      const time = new Date(aTime + 8 * 3600 * 1000)
        .toJSON()
        .substr(0, 19)
        .replace('T', ' ')
      return time
    },
    validFormat(row) {
      const start = this.getShowTime(row.actStartTime)
      const end = this.getShowTime(row.actEndTime)
      if (Number(row.segmentType) === 1) {
        return [start && end ? `${start} 至 ${end}` : '-']
      }
      if (
        Number(row.segmentType) === 2 &&
        Array.isArray(row.multiSegmentDTOList)
      ) {
        const list = []
        list.push(
          `${this.formatDate(row.actStartTime, 'YMD')} 至 ${this.formatDate(
            row.actEndTime,
            'YMD'
          )}`
        )
        row.multiSegmentDTOList.forEach((obj) => {
          list.push(
            `${this.weekObj[obj.stime.cycleNum]} ${Number(obj.stime.hour) < 10
              ? '0' + obj.stime.hour
              : obj.stime.hour
            }:${Number(obj.stime.minute) < 10
              ? '0' + obj.stime.minute
              : obj.stime.minute
            } 至 ${this.weekObj[obj.etime.cycleNum]} ${Number(obj.etime.hour) < 10
              ? '0' + obj.etime.hour
              : obj.etime.hour
            }:${Number(obj.etime.minute) < 10
              ? '0' + obj.etime.minute
              : obj.etime.minute
            }`
          )
        })
        console.log(list)
        return list
      }
    },
    registrationFormat(row) {
      const start = this.getShowTime(row.createTime)
      return start || '-'
    },
    // 跳转活动报名页面
   async tagClick(row) {
      this.shopCode = row.shopSourceType
      this.baseId = row.frameReportId
      this.updateData.baseId = row.baseType
      this.updateData.shopCode = row.shopReportNum
      this.select(1)
      this.loading=true
      console.log('fresh', this.baseId, this.shopCode)
      this.activitySelect = row
      await  freshFrame({ baseId: this.baseId })
      // this.getList(this.listQuery, true)
      getTimeStr({ frameReportId: this.activitySelect.frameReportId }).then((res) => {
        if (res.code === 1000) {
          this.activityInfos = { ...res.data.activityReportBaseResDTO };
          this.customerGroupVO=res.data.customerGroupVO
          // this.customerGroupVO = res.data.customerGroupVO;
        } else {
          this.$message.error(res.msg);
          this.loading=false
        }
        this.$nextTick(() => {
          this.getLists()
        
        })
      }).finally(res=>{
        
      });
      // const path = '/groupActivityTheme'
      // const obj = {
      //   frameReportId: id
      // }
      // window.openTab(path, obj)
      // this.$router.push({ path: '/groupActivityTheme', query: { frameReportId: id } });
    },
    reset() {
      this.$refs.listQuery.resetFields()
      this.getList(this.listQuery, true)
    },
    ex(row) {
      this.$set(row, 'isShow', !row.isShow)
      console.log(row)
    },
    exportData() {

      //导出接口
      exportTableData({ baseId: this.baseId }).then(
        res => {
          if (res.code == 1000) {
            this.changeExport = true;
          } else {
            this.$message.error(res.msg)
          }
        }
      )
    },
    /**
     * 获取get请求参数
     */
    getParams(params) {
      let queryStr = '?'
      Object.keys(params).forEach((key) => {
        queryStr += `${key}=${params[key]}&`
      })
      queryStr = queryStr.substr(0, queryStr.length - 1)
      return queryStr
    },
    downLoadXls(data, filename) {
      if (typeof window.chrome !== 'undefined') {
        // Chrome version
        const blob = new Blob([data], { type: 'application/vnd.ms-excel' })
        const link = document.createElement('a')
        link.href = window.URL.createObjectURL(blob)
        link.download = filename
        link.click()
      } else if (typeof window.navigator.msSaveBlob !== 'undefined') {
        // IE version
        const blob = new Blob([data], { type: 'application/force-download' })
        window.navigator.msSaveBlob(blob, filename)
      } else {
        // Firefox version
        const file = new File([data], filename, {
          type: 'application/force-download'
        })
        window.open(URL.createObjectURL(file))
      }
    },
    GetFrameActBaseForReport() {
      apiGetFrameActBaseForReport().then((res) => {
        if (res.status === 'success') {
          if (
            res.data &&
            res.data.activityReportBaseResDTOS &&
            res.data.activityReportBaseResDTOS.length > 0
          ) {
            this.isShowTheme = true
            this.tagList = res.data.activityReportBaseResDTOS
            this.tagData.showList = this.tagList;
            this.emnu = [];
            if (this.tagList.some(item => item.recommendFlag == 1)) {
              this.emnu.push({
                label: '平台推荐',
                code: 'x',
                sort: 0
              })
            }
            this.tagList.forEach(item => {
              if (!item.activityClass) return
              if (!this.emnu.some(val => (val.code == item.activityClass))) {
                this.emnu.push({
                  label: item.activityClassText,
                  code: item.activityClass,
                  sort: item.activityClassSort
                })
              }
            })
            this.emnu = this.emnu.sort((a, b) => {
              return a.sort - b.sort
            })
          } else {
            this.isShowTheme = false
          }
        } else {
          this.$message.error(res.msg || '获取活动主题失败')
        }
      })
    },
    getLists() {

      this.selections = []
      this.getList(this.listQuery, true)
    },
    getList(listQuery, reset) {
      const { page, pageSize } = listQuery
      this.tabActiveName = status ? String(status) : '0'
      let data = {
        baseId: this.baseId,//this.baseId ,
        modifiedMark: this.ruleForm.modifiedMark,
        queryString: this.ruleForm.queryString,
        pageNum: page,
        pageSize,
      }
      this.loading=true
      apiApplyList(data)
        .then((res) => {
          if (res.status === 'success') {
            const { list, total } = res.data.pageInfo
            this.list = list || []

            this.tableId++
            // this.list.forEach((item) => {
            //   this.reportThemeLists.push(item.reportTheme);
            // });
            this.listQuery = {
              ...this.listQuery,
              total
            }
            this.$nextTick(() => {
              this.tableEcho()
            })
            if (reset) {
              this.getStatusAndCount(params)
            }
          } else {
            this.$message.error(res.msg || res.errorMsg || '服务异常')
          }
        })
        .catch(() => {
          this.loading=false
        }).finally(res=>{
          this.loading=false
        })
    },
    tableEcho() {
      // console.log(this.selections)
      // const tableRef = this.$refs.multipleTable.$refs.table
      // const sysIds = this.selections.map(i => i.createTime)
      // this.list.forEach(i => {
      // if (sysIds.includes(i.createTime)) {
      //   tableRef.toggleRowSelection(i, true)
      // }
      // })
    },
    selectReportGroupCount() {
      apiSelectReportGroupCount({})
        .then((res) => {
          if (res.status === 'success') {
            if (res.data && res.data.data) {
              this.editConfig.rejectedStatusNum = res.data.data.rejectedCount
              this.editConfig.platformSubsidyNum = res.data.data.platformSubsidy
              // this.editConfig.agreementNum = res.data.data.noAgreementCount;
              this.editConfig.updateGroupBuyingAcceptPrice =
                res.data.data.updateGroupBuyingAcceptPrice
              this.editConfig.warningPriceTooLowCount =
                res.data.data.warningPriceTooLowCount
              this.editConfig.warningStartPersonalQtyCount =
                res.data.data.warningStartPersonalQtyCount
              this.editConfig.warningRateStatusCount =
                res.data.data.warningRateStatusCount
            }
          }
        })
        .catch(() => { })
    },
    tipsSearch(value) {
      // this.resetTipsSearch()
      if (value === 'platformSubsidyNum') {
        this.editConfig.searchPlatformSubsidy =
          !this.editConfig.searchPlatformSubsidy
        if (this.editConfig.searchPlatformSubsidy) {
          this.listQuery.isPlatformSubsidy = 0
        } else {
          this.listQuery.isPlatformSubsidy = ''
        }
        this.editConfig.searchAgreement = false
        this.listQuery.isAgreement = ''
        this.editConfig.searchRejectedStatus = false
        this.listQuery.status = ''
        this.editConfig.searchWarningRateStatusCount = false
        this.listQuery.warningRateStatus = 2
        this.editConfig.searchWarningPriceTooLowCount = false
        this.listQuery.warningPriceIsTooLow = ''
        this.editConfig.searchWarningStartPersonalQtyCount = false
        this.listQuery.warningStartPersonalQty = ''
      } else if (value === 'agreementNum') {
        this.editConfig.searchAgreement = !this.editConfig.searchAgreement
        if (this.editConfig.searchAgreement) {
          this.listQuery.isAgreement = 1
        } else {
          this.listQuery.isAgreement = ''
        }
        this.editConfig.searchPlatformSubsidy = false
        this.listQuery.isPlatformSubsidy = ''
        this.editConfig.searchRejectedStatus = false
        this.listQuery.status = ''
        this.editConfig.searchWarningRateStatusCount = false
        this.listQuery.warningRateStatus = 2
        this.editConfig.searchWarningPriceTooLowCount = false
        this.listQuery.warningPriceIsTooLow = ''
        this.editConfig.searchWarningStartPersonalQtyCount = false
        this.listQuery.warningStartPersonalQty = ''
      } else if (value === 'rejectedStatusNum') {
        this.editConfig.searchRejectedStatus =
          !this.editConfig.searchRejectedStatus
        if (this.editConfig.searchRejectedStatus) {
          this.listQuery.status = 2
        } else {
          this.listQuery.status = ''
        }
        this.editConfig.searchPlatformSubsidy = false
        this.listQuery.isPlatformSubsidy = ''
        this.editConfig.searchAgreement = false
        this.listQuery.isAgreement = ''
        this.editConfig.searchWarningRateStatusCount = false
        this.listQuery.warningRateStatus = 2
        this.editConfig.searchWarningPriceTooLowCount = false
        this.listQuery.warningPriceIsTooLow = ''
        this.editConfig.searchWarningStartPersonalQtyCount = false
        this.listQuery.warningStartPersonalQty = ''
      } else if (value === 'warningRateStatusCount') {
        this.editConfig.searchWarningRateStatusCount =
          !this.editConfig.searchWarningRateStatusCount
        if (this.editConfig.searchWarningRateStatusCount) {
          this.listQuery.warningRateStatus = 1
        } else {
          this.listQuery.warningRateStatus = 2
        }
        this.editConfig.searchPlatformSubsidy = false
        this.listQuery.isPlatformSubsidy = ''
        this.editConfig.searchAgreement = false
        this.listQuery.isAgreement = ''
        this.editConfig.searchRejectedStatus = false
        this.listQuery.status = ''
        this.editConfig.searchWarningPriceTooLowCount = false
        this.listQuery.warningPriceIsTooLow = ''
        this.editConfig.searchWarningStartPersonalQtyCount = false
        this.listQuery.warningStartPersonalQty = ''
      } else if (value === 'warningPriceTooLowCount') {
        this.editConfig.searchWarningPriceTooLowCount =
          !this.editConfig.searchWarningPriceTooLowCount
        if (this.editConfig.searchWarningPriceTooLowCount) {
          this.listQuery.warningPriceIsTooLow = 1
        } else {
          this.listQuery.warningPriceIsTooLow = ''
        }
        this.editConfig.searchPlatformSubsidy = false
        this.listQuery.isPlatformSubsidy = ''
        this.editConfig.searchAgreement = false
        this.listQuery.isAgreement = ''
        this.editConfig.searchRejectedStatus = false
        this.listQuery.status = ''
        this.editConfig.searchWarningRateStatusCount = false
        this.listQuery.warningRateStatus = 2
        this.editConfig.searchWarningStartPersonalQtyCount = false
        this.listQuery.warningStartPersonalQty = ''
      } else if (value === 'warningStartPersonalQtyCount') {
        this.editConfig.searchWarningStartPersonalQtyCount =
          !this.editConfig.searchWarningStartPersonalQtyCount
        if (this.editConfig.searchWarningStartPersonalQtyCount) {
          this.listQuery.warningStartPersonalQty = 1
        } else {
          this.listQuery.warningStartPersonalQty = ''
        }
        this.editConfig.searchPlatformSubsidy = false
        this.listQuery.isPlatformSubsidy = ''
        this.editConfig.searchAgreement = false
        this.listQuery.isAgreement = ''
        this.editConfig.searchRejectedStatus = false
        this.listQuery.status = ''
        this.editConfig.searchWarningRateStatusCount = false
        this.listQuery.warningRateStatus = 2
        this.editConfig.searchWarningPriceTooLowCount = false
        this.listQuery.warningPriceIsTooLow = ''
      }
      this.getList(this.listQuery, true)
      // this.getStatusAndCount(this.listQuery);

    },
    /**
     * 设置选中数据
     */
    setCheckedDatas(datas) {
      this.selections = datas
      // console.log(this.selections)
    },
    handerEdit(row) {
      console.log(row);
      sessionStorage.setItem('editCollageItem', JSON.stringify(row));
      const path = '/editCollageActivity';
      const obj = {
        frameReportId: row.frameReportId,
        fromType: 'edit'
      }
      sessionStorage.setItem('collageActType', 'edit')
      window.openTab(path, obj)
      // this.$router.push({ path: '/editCollageActivity', query: { frameReportId: row.frameReportId, fromType: 'edit' } });
    },
    // 下线
    handerOffline(row) {
      apiOffLine({ frameReportId: row.frameReportId })
        .then((res) => {
          if (res.status === 'success') {
            this.getStatusAndCount()
            this.getList(this.listQuery, true)
          } else {
            this.$message.error(res.msg || res.errorMsg || '服务异常')
          }
        })
        .catch(() => { })
    },
    selectChange(val) {
      this.selectIds = val
    },
    addCroed(row) {
      this.innerSelected = row.customerGroupId || row.frameActReportSaleScopeDTO.customerGroupId
      // this.customerInfo = {
      //   id: row.customerGroupId,
      //   name: row.customerGroupName,
      //   tagDef: row.tagDef,
      // }
      this.crowdDialogVis = true
    },
    cancelModal() {
      this.crowdDialogVis = false
      this.bulkChangesDialog = false
    },
    // 批量修改
    bulkChanges() {
      // this.downloadUrl = '';
      // this.files = '';
      // this.fileName = '';
      this.bulkChangesDialog = true
    },
    viewOperationLog(row) {
      this.listOperationLogConfig = { reportId: row.frameReportId }
      this.listOperationLogVisible = true
    },
    tabHandleClick() {
      this.listQuery.status =
        Number(this.tabActiveName) === 0 ? '' : Number(this.tabActiveName)
      this.getList(this.listQuery, false)
    },
    giveSku(row) {
      if (row.isGiveSku) {
        // this.$router.push(`/mzPromotion?csuid=${row.skuId}`);
        window.openTab('/mzPromotion', { csuid: row.skuId })
      } else {
        sessionStorage.setItem('pinTuanProductInfo', JSON.stringify(row))
        // window.openTab('/addMzActive', { fromType: 'pinTuan', reportId: row.frameReportId });
        this.$router.push(
          `/addMzActive?fromType=pinTuan&reportId=${row.frameReportId}`
        )
      }
    },
    // 查看业务商圈
    viewBusiness(row) {
      this.selectViewRow = row
      this.viewBusinessDialog = true
    },
    // 查看黑白名单
    seeNameList(id) {
      this.controlGroupId = id
      this.showNameList = true
    },
    cancelDialog() {
      this.showNameList = false
    }
  }
}
</script>

<style lang="scss" scoped>
.disabled {
  background-color: #e1e1e1 !important;
  color: #333333 !important;
}

.contentBox {
  padding: 16px;
  background: #fff;
  margin-bottom: 10px;

  .tooltipEllipsis {
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-box-pack: center;
    -webkit-box-align: center;
    -webkit-line-clamp: 2; //折两行后显示'...'
  }

  .topTip-prompt {
    padding: 9px 20px;
    background: rgb(255, 249, 240);
    opacity: 0.8;
    color: #B0610D;
    border: 1px solid #FFE6C0;
    border-radius: 4px;

  }

  .searchMsg {
    font-weight: 700;
    width: 200px;
    font-size: 16px;
  }

  .sign {
    display: inline-table;
    width: 3px;
    height: 13px;
    background: linear-gradient(360deg, #1d69c4 1%, #8bbdfc 99%);
    border-radius: 2px;
    margin-right: 8px;
  }

  .topTip {
    padding: 20px 0;
    margin-bottom: 10px;

    .topTip-info {
      font-size: 14px;
      font-family: PingFangSC, PingFangSC-Regular;
      font-weight: 400;
      text-align: left;
      color: #3c93ee;
    }

    .tag-list {
      margin-top: 20px;
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 10px;
      height: auto;
      transition: height 0.3s;

      p {
        margin: 0;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      >div {
        cursor: pointer;
        padding: 10px;
        background-color: #f7f7f7;
        border-radius: 3px;
        border: 1px solid #f7f7f7;
        transition: all 0.3s;
      }

      >div:hover {
        border: 1px solid #45addd;
      }
    }
  }

  .tips {
    padding: 8px 16px;
    margin-bottom: 10px;
    padding-left: 0;

    .div-info:hover {
      border: 1px solid #4183d5;
      background-color: #fff;

      .status-span i {
        background: #4183d5 !important;
      }
    }

    .div-info {
      display: inline-block;
      padding: 5px 10px 10px 5px;
      border-radius: 2px;
      font-size: 14px;
      font-family: PingFangSC, PingFangSC-Regular;
      font-weight: 400;
      border: 1px solid #dcdfe6;
      margin: 0 10px 10px 0;
      min-width: 120px;

      p {
        margin: 0;
        padding-top: 5px;
        font-size: 14px;
        font-family: PingFangSC, PingFangSC-Medium;
        font-weight: 500;
        color: #333333;
      }

      .status-span {
        padding-left: 14px;
        position: relative;

        i {
          position: absolute;
          top: 50%;
          transform: translateY(-50%);
          left: 4px;
          display: inline-block;
          vertical-align: middle;
          width: 5px;
          height: 5px;
          border-radius: 50%;
        }
      }

      .refundCountBox {
        .refundCount {
          margin-left: 12px;
          font-size: 20px;
          font-weight: 500;
          color: #ff3945;
          font-family: PingFangSC, PingFangSC-Medium;
        }

        .seeCount {
          float: right;
          color: #ffffff;
        }
      }
    }
  }

  .operation-info {
    margin-bottom: 20px;
  }

  /* table */
  ::v-deep  .el-table {

    &::before,
    ::before {
      background-color: #fff;
    }

    font-family: PingFangSC-Regular;
    font-weight: 400;
    color: #292933;
    border-radius: 2px;
    border: 1px solid #e4e4eb;

    tr {
      td {
        border-right: 1px solid #e4e4eb;

        .cell {
          padding-left: 20px;
          padding-right: 20px;
        }

        &.el-table-column--selection {
          .cell {
            padding: 0 10px;
          }
        }
      }
    }

    thead {
      color: #292933;

      th {
        height: 40px;
        padding: 0;
        font-weight: 400;
        background: #eeeeee;
        border: none;
        border-right: 1px solid #e4e4eb;
      }
    }

    tr {
      td {
        height: 40px;
        padding: 0;
        background: #fff;
        border-bottom: 1px solid #ebeef5;

        .cell {
          height: 100%;
          display: flex;
          flex-direction: column;
          justify-content: center;
          white-space: break-spaces;
          font-size: 12px;
          font-family: PingFangSC, PingFangSC-Regular;
          font-weight: 400;
          text-align: left;
          line-height: 22px;
          padding: 8px 16px;
        }
      }
    }

    .el-table__body {

      // 隔行变色
      tr.el-table__row--striped {
        td {
          background: #ffffff;
        }
      }
    }

    .table-row,
    .table-header-row {
      height: 40px;
    }
  }

  ::v-deep   .cell {
    .el-checkbox__inner {
      border: 1px solid #000000;
    }
  }
}

.searchMy {
  padding-left: 30px;
}

.searchMy ::v-deep  .el-form-item__label {
  margin-left: 20px;
  padding: 0;
}

.searchMy ::v-deep  .el-input__inner {
  border-radius: 0 4px 4px 0;
}

.searchMy ::v-deep  .el-date-editor {
  width: 100%;
}

.searchMy ::v-deep  .el-select {
  display: table-cell;
  width: 100%;
}

.searchMy ::v-deep  .el-form-item__content {
  width: 100%;
}

.searchMy ::v-deep  .el-form-item--small.el-form-item {
  margin-bottom: 10px;
  width: 32%;
}

.searchMy ::v-deep  .el-form-item--small .el-form-item__content {
  line-height: 30px;
  width: 100%;
}

.searchMy ::v-deep  .el-input-group__prepend {
  background: none;
  color: #333333;
  padding: 0 10px;
}

.searchMy ::v-deep  .el-form-item--small.el-form-item {
  margin-bottom: 10px;
  width: 97%;
}

.search-title {
  display: table-cell;
  padding: 0 10px;
  text-align: center;
  border: 1px solid #dcdfe6;
  height: 30px;
  line-height: 30px;
  vertical-align: middle;
  border-right: none;
  border-radius: 4px 0 0 4px;
  color: #333333;
  white-space: nowrap;
}

.btn-box {
  float: right;
  display: flex;
  align-items: center;

  .showMoreBtns {
    .showMore {
      color: #4184d5;
      font-size: 14px;
      float: right;
      padding: 5px 20px;
      cursor: pointer;
    }
  }
}

.tag-class1 {
  color: white;
  background-color: #22b14c;
}

.tag-class2 {
  color: rgb(243, 157, 28);
  border: 1px solid rgb(243, 157, 28);
}

.ex {
  color: #22b14c;
  cursor: pointer;
}

.sessionAssignments {
  padding: 30px 0;



  .btn {
    cursor: pointer;
    padding: 10px 15px;
    color: rgb(140, 153, 163);
    min-width: 14%;
    border-radius: 4px;
    // border: 1px solid rgb(187, 187, 187);
    border: none;
  }

  .select {
    color: rgb(61, 150, 169);
    background-color: white !important;
  }

  .title {
    color: #BB6927;
    padding: 10px 10px;
    background: rgb(255, 249, 240);
    margin: 30px 30px;
    margin-bottom: 0;
    border: 1px solid #FFE6C0;
    border-radius: 4px;
  }

  .content {
    padding: 0 30px;

    .info {
      padding-left: 28px;
      padding-top: 10px;
      color: rgb(189, 189, 189);
    }

    .demo-form-inline {


      padding-left: 40px;
    }

    .select-line {
      display: flex;

    }
  }

  .head-tabs {
    background-color: rgb(240, 240, 240);
    display: flex;
    width: 98%;
    margin-left: 0px;
    justify-content: space-around;
    padding: 5px 5px;
    border-radius: 4px;
  }
}

.content-tab {
  margin-top: 20px;
}

.selectCss {
  border: 1px solid #45addd !important;
}
.busAreaNameBox {
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
</style>
