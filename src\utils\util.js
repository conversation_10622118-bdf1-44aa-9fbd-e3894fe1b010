import { Message } from 'element-ui'
import store from '@/store'
import router from '@/router'

export default {
  install(Vue) {
    // 时间戳转时间
    Vue.prototype.formatDate = function (stamp, type) {
      if (!stamp) {
        return ''
      }
      const now = new Date(parseInt(stamp))
      const year = now.getFullYear()
      const month = now.getMonth() + 1 < 10 ? `0${now.getMonth() + 1}` : now.getMonth() + 1
      const day = now.getDate() < 10 ? `0${now.getDate()}` : now.getDate()
      const hour = now.getHours() < 10 ? `0${now.getHours()}` : now.getHours()
      const minute = now.getMinutes() < 10 ? `0${now.getMinutes()}` : now.getMinutes()
      const second = now.getSeconds() < 10 ? `0${now.getSeconds()}` : now.getSeconds()
      if (type == 'HM') {
        return `${hour}:${minute}`
      }
      if (type == 'MDHM') {
        return `${month}-${day} ${hour}:${minute}`
      }
      if (type == 'YMD') {
        return `${year}-${month}-${day}`
      }
      if (type == 'YM') {
        return `${year}-${month}`
      }
      if (type == 'Y') {
        return year
      }
      if (type == 'BR') {
        return `${hour}:${minute}:${second}`
      }
      return `${year}-${month}-${day} ${hour}:${minute}:${second}`
    }
    Vue.filter('formatDate', (stamp, type) => {
      if (!stamp) {
        return ''
      }
      const now = new Date(parseInt(stamp))
      const year = now.getFullYear()
      const month = now.getMonth() + 1 < 10 ? `0${now.getMonth() + 1}` : now.getMonth() + 1
      const day = now.getDate() < 10 ? `0${now.getDate()}` : now.getDate()
      const hour = now.getHours() < 10 ? `0${now.getHours()}` : now.getHours()
      const minute = now.getMinutes() < 10 ? `0${now.getMinutes()}` : now.getMinutes()
      const second = now.getSeconds() < 10 ? `0${now.getSeconds()}` : now.getSeconds()
      if (type == 'HM') {
        return `${hour}:${minute}`
      }
      if (type == 'MDHM') {
        return `${month}-${day} ${hour}:${minute}`
      }
      if (type == 'YMD') {
        return `${year}-${month}-${day}`
      }
      if (type == 'YM') {
        return `${year}-${month}`
      }
      if (type == 'Y') {
        return year
      }
      return `${year}-${month}-${day} ${hour}:${minute}:${second}`
    })
  },
  copyUrl(data) {
    const url = data
    const oInput = document.createElement('input')
    oInput.value = url
    document.body.appendChild(oInput)
    oInput.select() // 选择对象;
    console.log(oInput.value)
    document.execCommand('Copy') // 执行浏览器复制命令
    Message.success({
      message: '已成功复制到剪切板',
      type: 'success'
    })
    oInput.remove()
  },
  exportExcel(res, ecxelName) {
    const blob = new Blob([res]) // 接受文档流
    if ('msSaveOrOpenBlob' in navigator) {
      // IE下的导出
      window.navigator.msSaveOrOpenBlob(blob, ecxelName || '导出文件' + '.xlsx') // 设置导出的文件名
    } else {
      // 非ie下的导出
      const a = document.createElement('a')
      const url = window.URL.createObjectURL(blob)
      const filename = ecxelName || '导出文件' + '.xlsx' // 设置导出的文件名
      console.log(filename)
      a.href = url
      a.download = filename
      document.body.appendChild(a)
      a.click()
      window.URL.revokeObjectURL(url)
      document.body.removeChild(a)
    }
  },
  setLocal(name, value) {
    localStorage.setItem(name, JSON.stringify(value))
  },
  getLocal(name) {
    const str = localStorage.getItem(name)
    if (str && str !== 'undefined') {
      return JSON.parse(localStorage.getItem(name))
    }
  },
  clearLoacl(name) {
    localStorage.removeItem(name)
  },

  getNextDate() {
    const d = new Date()
    const year = d.getFullYear()
    const month = d.getMonth()
    const day = d.getDate()
    const nextDay = new Date(year, month, day + 1, '00', '00', '00')
    return nextDay
  },

  setCookie(key, value, days) {
    // 设置过期时间，不传值默认第二天0点过期
    let data = null
    if (days) {
      data = new Date(new Date().getTime() + days * 24 * 60 * 60 * 1000).toUTCString()
    } else {
      data = this.getNextDate()
    }
    let expires = 'expires=' + data
    document.cookie = key + '=' + value + '; ' + expires
  },

  getCookie(name) {
    var arr = document.cookie.match(new RegExp('(^| )' + name + '=([^;]*)(;|$)'))
    if (arr != null) {
      return arr[2]
    } else {
      return null
    }
  },

  clearCookie(name) {
    let json = {}
    json[name] = ''
    this.setCookie(json, -1)
  },

  getUrlParam(name) {
    const urlArr = window.location.href.split('?')
    if (urlArr.length < 2) {
      return ''
    }
    const tempArr = urlArr[1].split('&')
    for (let i = 0; i < tempArr.length; i++) {
      const item = tempArr[i].split('=')
      if (item[0].trim() === name) {
        return item[1]
      }
    }
    return ''
  }
}

export function openTab(path, params = {}, name) {
  // const tabList = store.getters.tabList
  store.commit('permission/ADD_TABLIST', { path, params, name })
  window.jump(path, params)
}

export function isExternal(path) {
  if (path) {
    return /^(https?:|http:|mailto:|tel:)/.test(path)
  }
  return false
}

// 判断是否数组
export function isArrayFn(value) {
  if (typeof Array.isArray === 'function') {
    return Array.isArray(value)
  } else {
    return Object.prototype.toString.call(value) === '[object Array]'
  }
}

// 深拷贝
export function deepClone(data) {
  var type = typeof data
  var obj

  if (isArrayFn(data)) {
    obj = []
  } else if (type === 'object' && !isArrayFn(data) && data !== null) {
    obj = {}
  } else {
    // 不再具有下一层次
    return data
  }
  if (isArrayFn(data)) {
    for (var i = 0, len = data.length; i < len; i++) {
      obj.push(deepClone(data[i]))
    }
  } else if (type === 'object' && !isArrayFn(data)) {
    for (var key in data) {
      obj[key] = deepClone(data[key])
    }
  }
  return obj
}

// 获取图片信息，包括图片尺寸之类的
export function getImgInfo(file) {
  return new Promise((resolve, reject) => {
    //上传文件为图片类型
    let img = new Image()
    img.onload = function () {
      resolve(img)
    }
    img.src = URL.createObjectURL(file)
  })
}

export const docTitleScroll = (() => {
  let target = false;
  const run = () => {
    const timer = setTimeout(() =>{
      document.title = document.title.substring(1, document.title.length) + document.title.substring(0, 1);
      clearTimeout(timer);
      run();
    }, 300);
  }
  return (title) => {
    document.title = title;
    if (!target) {
      target = true;
      run();
    }
  }
})()
// 创建v-disabled作为全局指令
export const disabledDirective = {
  update(el, binding) {
    if (binding.value) {
      // 添加遮罩层
      if(el.__mask__) return
      const mask = document.createElement('div');
      mask.className = 'disabled-mask';
      mask.style.position = 'absolute';
      mask.style.top = '0';
      mask.style.left = '0';
      mask.style.width = '100%';
      mask.style.height = '100%';
      mask.style.backgroundColor = 'rgba(0,0,0,0.1)';
      mask.style.zIndex = '9999';
      el.appendChild(mask);

      // 禁用元素本身
      // el.style.pointerEvents = 'none';
      el.style.cursor = 'not-allowed';
      el.style.opacity = '0.6';
      el.__mask__ = mask;
      // 阻止事件穿透遮罩层
      mask.addEventListener('click', e => e.stopPropagation());
      mask.addEventListener('mousemove', e => e.stopPropagation());
    }else {
      // 移除遮罩层
      const mask = el.__mask__;
      if (mask && mask.parentNode === el) {
        el.removeChild(mask);
        el.__mask__ = null;
        // 恢复元素状态
        el.style.pointerEvents = 'auto';
        el.style.cursor = 'auto';
        el.style.opacity = '1';
      }
    }
  }
}