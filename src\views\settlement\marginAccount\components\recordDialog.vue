<template>
  <!-- 充值保证金的弹框 -->
  <el-dialog
    title="充值记录"
    :visible.sync="dialogVisible"
    @close="dialogVisible = false"
    width="70%"
    append-to-body
  >
    <div class="box">
      <!-- 搜索栏 -->
      <el-form label-position="right" label-width="10px" style="display: flex; flex-wrap: wrap;">
        <el-form-item>
          <span class="search-title">提交日期</span>
          <div class="left-input">
            <el-date-picker
              size="small"
              v-model="form.changeTime"
              type="daterange"
              format="yyyy-MM-dd"
              value-format="timestamp"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              style="width: 360px"
            />
          </div>
        </el-form-item>

        <div class="selectSty">
          <el-form-item>
            <my-select
              label="审核状态"
              :list="accountTypeList"
              :multiple="false"
              :value="form.auditStatus"
              @input="(val) => (form.auditStatus = val)"
            ></my-select>
          </el-form-item>
        </div>

        <div>
          <el-button size="small" @click="resetForm"> 重置 </el-button>
          <el-button size="small" type="primary" @click="search">查询</el-button>
        </div>
      </el-form>
      <!-- <div style="color: red; margin: -10px 0 10px 10px">
        保证金首次充值，平台可提供收据。后续补交保证金平台不再提供收据
      </div> -->
      <!-- 表格 -->
      <div class="tableSty">
        <el-table
          :data="tableData"
          style="width: 100%; margin-bottom: 10px; max-height: 500px; overflow-y: auto"
          border
          stripe
          v-loading="loading"
        >
          <el-table-column label="序号" type="index" align="center" width="50" />
          <el-table-column label="充值单" prop="flowOrderNumber"> </el-table-column>
          <el-table-column label="金额" prop="amount">
            <template slot-scope="scope">
              <span></span>
              <span v-if="scope.row.amount != null"> ￥{{ scope.row.amount }} </span>
            </template>
          </el-table-column>
          <el-table-column label="打款凭证" prop="paymentProof">
            <template slot-scope="scope">
              <template v-if="scope.row.paymentProof">
                <el-image 
                  v-for="(item, index) in scope.row.paymentProof.split(',')"
                  :key="index"
                  :src="item"
                  :preview-src-list="scope.row.paymentProof.split(',')"
                  style="width: 30px; height: 30px"
                >
                </el-image>
              </template>
            </template>
          </el-table-column>
          <el-table-column label="提交时间" prop="createTime">
            <template slot-scope="scope">
              <div>{{ formatDate(scope.row.createTime, 'YMDHMS') }}</div>
            </template>
          </el-table-column>
          <el-table-column label="提交人" prop="submitter" />
          <el-table-column label="单据状态" prop="auditStatus">
            <template slot-scope="scope">
              <span>
                {{ { 1: "待审核", 2: "审核通过", 3: "已取消", 4: "审核驳回"}[scope.row.auditStatus] }}
              </span>
            </template>
          </el-table-column>
          <el-table-column label="审核时间" prop="auditTime">
            <template slot-scope="scope">
              <div>{{ formatDate(scope.row.auditTime, 'YMDHMS') }}</div>
            </template>
          </el-table-column>
          <el-table-column label="审核留言" prop="auditComment" />
          <el-table-column label="平台收据" prop="receipt">
            <template slot-scope="scope">
              <template v-if="scope.row.receipt">
                <el-image 
                  v-for="(item, index) in scope.row.receipt.split(',')"
                  :key="index"
                  :src="item"
                  :preview-src-list="scope.row.receipt.split(',')"
                  style="width: 30px; height: 30px"
                >
                </el-image>
              </template>
            </template>
          </el-table-column>
          <el-table-column label="操作" prop="slot" key="slot">
            <template #default="scope">
              <span></span>
              <el-button v-if="scope.row.auditStatus == 4" type="text" @click="openRecharge('change', scope.row)">
                修改
              </el-button>
              <el-button v-if="scope.row.auditStatus == 1 && scope.row.isFirstCharge == 2" type="text" @click="overSub(scope.row)">
                取消
              </el-button>
              <!-- <el-button v-if="row.auditStatus == 2 && row.isFirstCharge == 1 && row.paymentProof" type="text" @click="downloadReceipt(row.paymentProof)">
                下载收据
              </el-button> -->
            </template>
          </el-table-column>
        </el-table>
        <!-- 分页 -->
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="pagination.pageNum"
          :page-sizes="[20, 50, 100]"
          :page-size="pagination.pageSize"
          background
          layout="->, total, sizes, prev, pager, next, jumper"
          :total="pagination.total"
        >
        </el-pagination>
      </div>
    </div>
    <!-- 充值保证金的弹框 -->
    <rechargeDialog ref="rechargeDialog"></rechargeDialog>
  </el-dialog>
</template>

<script>
import { pageRechargeFlow, cancel } from '@/api/settlement/marginAccount/index'

import mySelect from '../../components/mySelect.vue'
import rechargeDialog from './rechargeDialog.vue'

export default {
  props: [],
  // inject: ['accountData'], // 来源于index组件的accountData
  components: {
    rechargeDialog,
    mySelect
  },
  data() {
    return {
      dialogVisible: false,
      showRecharge: false, // 保证金充值弹框
      form: {
        auditStatus: null,
        changeTime: null
      },
      accountTypeList: [
        { value: '1', label: '待审核' },
        { value: '2', label: '审核通过' },
        { value: '3', label: '已取消' },
        { value: '4', label: '审核驳回' }
      ],

      loading: false, //表格加载中
      tableData: [], // 表格数据
      pagination: {
        pageNum: 1,
        pageSize: 20,
        total: 100
      } // 表格分页
    }
  },
  created() {},
  methods: {
    overSub(val){
      let subData = {
        fundPropertyStatus: 1,
        merchantFundAuditId: val.id
      }
      cancel(subData).then(res=>{
        if(res.code == 0) this.search()
      })
    },
    openDialog(value) {
      // 调用this.search函数获取表格数据
      console.log(value)
      this.search()
      this.dialogVisible = true
    }, // 打开新增弹框
    openRecharge(type, row) {
      // console.log(row)
      this.$refs.rechargeDialog.openDialog(type, row)
    },
    search() {
      // console.log(this.form)
      this.loadTableData()
    }, // 查询逻辑
    resetForm() {
      this.form = {
        auditStatus: null,
        changeTime: null
      };
    }, // 重置表单

    loadTableData() {
      // 通过this.accountData page form等，发送请求的相关参数，拿到数据渲染表格
      let subData = {
        fundPropertyStatus: 1,
        auditStatus: this.form.auditStatus,
        createStartTime: this.form.changeTime?.[0] || null,
        createEndTime: this.form.changeTime?.[1] || null,
        pageNum: this.pagination.pageNum,
        pageSize: this.pagination.pageSize
      }
      this.loading = true
      // 调用接口请求数据，替换tableData
      pageRechargeFlow(subData).then(res => {
        if(res.code == 0) {
          this.tableData = res.data.list
          this.pagination.total = res.data.total
        }
      }).finally(() => {
        this.loading = false
      })
    }, // 加载表格数据
    handleCurrentChange(pageNum) {
      this.pagination.pageNum = pageNum
      this.search(true)
    }, // 页码改变的回调
    handleSizeChange(pageSize) {
      this.pagination.pageSize = pageSize
      this.search(true)
    } // 每页条数改变的回调
  }
}
</script>

<style scoped lang="scss">
.box {
  width: 100%;
  display: flex;
  flex-direction: column;
  // justify-content: center;
  // align-items: center;
  padding: 0 10px;
}
.selectSty {
  margin: 0 20px;
}
</style>
