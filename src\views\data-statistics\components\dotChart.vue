<template>
  <div class="search-info">
    <span class="sign" />
    <span class="title_line" style="margin-right: 5px">价格&销量</span>
    <el-tooltip class="item" content="已售出商品的价格和对应销量" placement="top">
      <i class="el-icon-warning-outline"></i>
    </el-tooltip>
    <div class="chartBox" :id="chartId"></div>
  </div>
</template>

<script>
import * as echarts from 'echarts'

import { getPriceAndSaleNumV2, apiSelectSubPage } from '@/api/data-statistics/varietyAnalysis'

export default {
  name: 'dotChart',
  props: {
    chartId: {
      type: String,
      default: 'dotChart'
    },
    tooltip: {
      type: String,
      default: ''
    },
    resDotData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {}
  },
  mounted() {
    this.initChart()
  },
  methods: {
    getChartId() {
      return this.chartId
    },

    initChart(dotData) {
      // let data = [
      //   [
      //     ['Other1', 77, 455, 'Australia11', 1991],
      //     ['Meals', 77.4, 66, 'Canada', 1990],
      //     ['Transportation', 68, 77, 'China', 1990],
      //     ['Transportation', 74.7, 88, 'Cuba', 1990],
      //     ['Utilities', 79.1, 99, 'Japan', 1990],
      //     ['Rent', 67.9, 1121, 'North Korea', 1990],
      //     ['Total', 117.9, 33, 'North Korea', 1990]
      //   ]
      // ]
      let chartData = []
      let xAxisData = []
      dotData.result.forEach((item) => {
        xAxisData.push(item.date)
        item.datum.forEach((dotitem, index) => {
          chartData.push([
            item.date,
            item.datum2[index],
            dotitem,
            // `${item.date}${dotitem}${item.datum2[index]}`
          ])
        })
      })
      console.log(chartData)

      const option = {
        // legend: {
        //   data: ['面积越大代表销量越大']
        // },
        xAxis: {
          type: 'category',
          data: xAxisData
        },
        yAxis: {
          type: 'value',
          name: '销量'
        },
        tooltip: {
          trigger: 'item',
          axisPointer: {
            type: 'cross'
          },
          formatter: function(params) {
            params.data[2]
            return `<div style="min-width:60px;">销量：${params.data[1]}</div><div style="min-width:60px;">价格：${params.data[2]}</div>`;
          }
        },

        series: [
          {
            name: '',
            data: chartData,
            type: 'scatter',
            symbolSize: function (data) {
              return 10 // (Math.sqrt(data[2]) / 5e2) * 4500
            },
            // label: {
            //   show: true,
            //   position: 'inside',
            //   formatter: function(params) {
            //     console.log('label:::::',params);
            //     params.data[2]
            //     return `${params.data[2]}`;
            //   }
            // },
            // tooltip: {// 悬浮框转换
            //   valueFormatter: function(value) {
            //     return value + " ml";
            //   }
            // },
          }
        ]
      }
      this.$nextTick(() => {
        const chart = echarts.init(document.getElementById(this.chartId))
        chart && chart.setOption(option)
      })
    }
  }
}
</script>

<style scoped lang="scss">
.search-info {
  padding: 10px 20px;
  font-weight: 500;

  .chartBox {
    width: 100%;
    height: 408px;
  }
}
</style>
