<template>
  <div>
    <div class="list-box">
      <div class="con-title" style="padding-left: 0">
        <span>设置控销区域、客户类型</span>
      </div>
      <div class="search-btn" style="padding-bottom: 20px">
        <el-button type="primary" size="small" @click="showList('search')"
        >添加控销方案</el-button
        >
      </div>
      <div v-if="tableList.length > 0">
        <el-table
          :data="tableList"
          stripe
          :header-cell-style="{ background: '#F9F9F9', color: '#666666' }"
          max-height="249px"
        >
          <el-table-column label="序号" type="index" :index="indexMethod" width="60">
          </el-table-column>
          <el-table-column prop="cityStr" label="控销区域" >
          </el-table-column>
          <el-table-column prop="userStr" label="客户类型" >
          </el-table-column>
          <el-table-column label="操作" class-name="operation-box" width="120">
            <template slot-scope="scope">
              <el-button
                @click="editData(scope.row, scope.$index)"
                type="text"
                size="small"
              >修改</el-button
              >
              <el-button
                @click="removeData(scope.row, scope.$index)"
                type="text"
                size="small"
              >删除</el-button
              >
            </template>
          </el-table-column>
          <template slot="empty">
            <div class="noData">
              <div>暂无数据</div>
            </div>
          </template>
        </el-table>
      </div>
    </div>

    <el-dialog
      title="添加控销区域、客户类型"
      v-if='changeDialog'
      :visible.sync="changeDialog"
      class="my-dialog"
      :modal-append-to-body="false"
      :close-on-click-modal="false"
      @close='outerVisibleQual'
      width="70%"
      v-loading="loadingDialog"
    >
      <div class="explain-table">

        <div
          class="tree-box"
          style="max-height: 357px;overflow-y: auto;margin-top:10px"
          ref="cityBox"
        >
          <div class="con-title" style="padding-left: 0;">
            <span >选择控销区域:</span>
          </div>
          <el-checkbox :indeterminate="isIndeterminateCity" v-model="checkAllCity" @change="handleCheckAllChangeCity" >全选</el-checkbox>
          <el-tree
            :data="classList"
            :props="props"
            show-checkbox
            :default-checked-keys="checkedAry"
            :default-expanded-keys="expandedAry"
            node-key="areaCode"
            ref="cityTree"
            class="my-tree-box"
            @check-change="handleCheckedCitiesChange"
          >
            <div class="custom-tree-node" slot-scope="{ node, data }">
              <div class="total_info_box clearfix">
                <span>{{ data.areaName }}</span>
              </div>
            </div>
          </el-tree>
        </div>

        <div class="tree-box user" style="max-height: 144px;overflow-y: auto;margin-top:10px">
          <div class="con-title" style="padding-left: 0;">
            <span>选择客户类型:</span>
          </div>
          <el-checkbox :indeterminate="isIndeterminateUser" v-model="checkAllUser" @change="handleCheckAllChangeUser">全选</el-checkbox>
          <el-tree
            :data="userList"
            :props="propsUser"
            :expand-on-click-node="false"
            show-checkbox
            :default-expand-all="true"
            :default-checked-keys="checkedAryUser"
            node-key="id"
            class="my-tree-box user-type"
            ref="userTree"
            @check-change="handleCheckedUserChange"
          >
            <div class="custom-tree-node" slot-scope="{ node, data }">
              <div class="total_info_box clearfix">
                <span>{{ data.name }}</span>
              </div>
            </div>
          </el-tree>
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="outerVisibleQual" size="small">取 消</el-button>
        <el-button
          type="primary"
          class="xyy-blue"
          @click="sureInnerVisibleQual"
          size="small"
        >确定</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>
<style scoped>
@import '../../../assets/css/changeElement.scss';
.my-tree-box .el-tree-node {
  white-space: inherit !important;
  outline: 0;
  display: inline-block !important;
}
</style>
<script>
import {
  dicAreas,
  findUserTypes,
} from '@/api/goods/controlGoods.js'
import {getAreaCode} from '@/api/freightTemplate'
// import { getCityList, getUserType } from '@/api/goods/newGood.js'
export default {
  name: 'typeActive',
  props: {
    provId: {
      type: String,
      default: ''
    },
    fromStr: {
      type: String,
      default: ''
    },
    firstAry: {
      type: Array,
      default: () => []
    },
    secondAry: {
      type: Array,
      default: () => []
    },
    threeAry: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      changeDialog: false,
      classList: [],
      userList: [],
      props: {
        // label: 'categoryCode', // 需要指定的节点渲染对象属性
        children: 'children', // 指定的子级
        isLeaf: (data, node) => {
          return node.areaLevel === 4;
        },
      },
      propsUser: {
        children: 'childList' // 指定的子级
      },
      checkedAry: [],
      expandedAry: [],
      checkedAryUser: [],
      tableList: [],
      isEdit: false,
      editIndex: null,
      checkAllCity: false,
      checkAllUser:false,
      areaCode:0,
      isIndeterminateCity: false,
      isIndeterminateUser:false,
      classListCount: 0,
      loadingDialog: false
    }
  },
  watch: {
    firstAry: {
      deep: true,
      handler: function(newVal) {
        this.firstAry = newVal
        this.setData()
      }
    },
    secondAry: {
      deep: true,
      handler: function(newVal) {
        this.secondAry = newVal
        this.setData()
      }
    },
    threeAry: {
      deep: true,
      handler: function(newVal) {
        this.threeAry = newVal
        this.setData()
      }
    },
    fromStr: {
      deep: true,
      handler: function(newVal) {
        this.setData()
      }
    }
  },
  created() {
    if (this.fromStr == 'first') {
      this.firstAry.length > 0 ? (this.tableList = this.firstAry) : this.tableList = []
    } else if (this.fromStr == 'second') {
      this.secondAry.length > 0 ? (this.tableList = this.secondAry) : this.tableList = []
    } else {
      this.threeAry.length > 0 ? (this.tableList = this.threeAry) : this.tableList = []
    }
  },
  methods: {
    //获取省市列表
    cityListData() {
      this.loadingDialog = true
      getAreaCode({}).then((res) => {
        this.loadingDialog = false
        if (res && res.code === 0 && Array.isArray(res.data)) {
          this.classList = res.data
          this.classListCount = this.classList.length
        }
      }).catch(() => {
        this.loadingDialog = false
      })
    },
    //显示选择区域
    setData() {
      if (this.fromStr == 'first') {
        this.firstAry.length > 0 ? (this.tableList = this.firstAry) : this.tableList = []
      } else if (this.fromStr == 'second') {
        this.secondAry.length > 0 ? (this.tableList = this.secondAry) : this.tableList = []
      } else {
        this.threeAry.length > 0 ? (this.tableList = this.threeAry) : this.tableList = []
      }
    },

    showList() {
      let that = this
      this.changeDialog = true
      this.cityListData()
      this.userType()
      this.isEdit = false;
      this.editIndex = null;
      setTimeout(function() {
        that.$refs.cityTree.setCheckedKeys([])
        that.$refs.userTree.setCheckedKeys([])
        that.$refs.cityBox.scrollTo(0, 0)
      }, 300)
    },
    //获取客户类型
    userType() {
      findUserTypes().then((res) => {
        if (res.code == 0) {
          this.userList = res.data;
          // Object.keys(res.data).forEach((item)=>{
          //   list.push({
          //     id:item,
          //     name:res.data[item]
          //   })
          // })
          // this.userList.push({ id: '', name: '全部', childList: list });
        }
      })
    },
    outerVisibleQual(val) {
      this.changeDialog = false
      this.expandedAry = []
    },
    flatten(ary) {
      const list = [];
      ary.forEach(item => {
        const {
          children,
          ...obj
        } = item;
        list.push(obj);
        if (children && children.length) {
          const childrenList = this.flatten(children);
          list.push(...childrenList);
        }
      });
      return list;
    },
    sureInnerVisibleQual() {
      let that = this
      let obj = {
        sourceCityArr: [],
        cityStr: '',
        cityAry: [],
        userStr: '',
        userAry: [],
        cityIds: '',
        userIds: ''
      }

      let cityData = JSON.parse(
        JSON.stringify(this.$refs.cityTree.getCheckedNodes())
      )
      if (cityData.length > 0) {
        // const _cityStr = []
        // const _cityIds = []
        // const _select = []
        // cityData.forEach((item) => {
        //   if(item.level == 1 || item.level == 2){
        //     _select.push(item.areaCode)
        //   }
        // })
        // console.log(_select,'sss000')
        // let i = cityData.length;
        // while(i--){
        //   if(_select.indexOf(cityData[i].parentCode) > -1){
        //     cityData.splice(i,1)
        //   }
        // }
        // cityData.forEach((item) => {
        //   _cityStr.push(item.areaName)
        //   _cityIds.push(item.areaCode)
        //   obj.cityAry.push(item.areaCode)
        // })
        // obj.cityStr = _cityStr.join(',')
        // obj.cityIds = _cityIds.join(',')
        console.log(cityData)
        const flattenCityData = this.flatten(cityData)
        const selectCode = new Map()
        cityData.forEach(item=>{
          if (!selectCode.has(item.areaCode)){
            selectCode.set(item.areaCode,true)
            if (item.parentId && selectCode.has(item.parentId)){
              selectCode.set(item.areaCode,false)
            }
          }
        })
        console.log(selectCode)
        const selectKeAry = []
        selectCode.forEach((val,key)=>{
          if (val){
            selectKeAry.push(key)
          }
        })
        obj.sourceCityArr = cityData;
        const checkedNodes = flattenCityData.filter(item=>selectKeAry.includes(item.areaCode))
        obj.cityAry = selectKeAry
        obj.cityStr = checkedNodes.map(item=>item.areaName).join(',')
        obj.cityIds = selectKeAry.join(',')
      } else {
        this.$message({
          message: '请先选择控销区域',
          type: 'warning'
        })
        return
      }
      let userData = JSON.parse(
        JSON.stringify(this.$refs.userTree.getCheckedNodes())
      )
      if (userData.length > 0) {
        const _userStr = []
        const _userIds = []
        userData.forEach((item) => {
          _userStr.push(item.name)
          _userIds.push(item.id)
          obj.userAry.push(item.id)
        })
        obj.userStr = _userStr.join(',')
        obj.userIds = _userIds.join(',')
      } else {
        this.$message({
          message: '请先选择客户类型',
          type: 'warning'
        })
        return
      }

      if (this.firstAry.length > 0 && this.fromStr != 'first') {
        console.log(this.firstAry,'[[[[[[[')
        console.log(obj,'[]]]]]]')
        for (let i = 0; i < this.firstAry.length; i++) {
          this.firstAry[i].cityAry = this.firstAry[i].cityAry.map(Number)
          if(obj.cityAry.some(r => this.firstAry[i].cityAry.indexOf(r) >= 0)){
            this.firstAry[i].userAry = this.firstAry[i].userAry.map(Number)
            if(obj.userAry.some(r => this.firstAry[i].userAry.indexOf(r) >= 0)){
              this.$message({
                message: '所选数据中包含重复计划，请重新选择',
                type: 'error'
              })
              return
            } else {
              console.log('false')
            }

          } else {
            console.log('false')
          }
        }
      }
      if (this.secondAry.length > 0 && this.fromStr != 'second') {
        for (let i = 0; i < this.secondAry.length; i++) {
          // let listStr = this.secondAry[i].cityIds + this.secondAry[i].userIds
          // if (str.indexOf(listStr) > -1) {
          //   this.$message({
          //     message: '所选数据中包含重复计划，请重新选择',
          //     type: 'error'
          //   })
          //   return
          // }
          this.secondAry[i].cityAry = this.secondAry[i].cityAry.map(Number)
          if(obj.cityAry.some(r => this.secondAry[i].cityAry.indexOf(r) >= 0)){
            this.secondAry[i].userAry = this.secondAry[i].userAry.map(Number)
            if(obj.userAry.some(r => this.secondAry[i].userAry.indexOf(r) >= 0)){
              this.$message({
                message: '所选数据中包含重复计划，请重新选择',
                type: 'error'
              })
              return
            } else {
              console.log('false')
            }

          } else {
            console.log('false')
          }
        }
      }
      if (this.threeAry.length > 0 && this.fromStr != 'three') {
        for (let i = 0; i < this.threeAry.length; i++) {
          this.threeAry[i].cityAry = this.threeAry[i].cityAry.map(Number)
          if(obj.cityAry.some(r => this.threeAry[i].cityAry.indexOf(r) >= 0)){
            this.threeAry[i].userAry = this.threeAry[i].userAry.map(Number)
            if(obj.userAry.some(r => this.threeAry[i].userAry.indexOf(r) >= 0)){
              this.$message({
                message: '所选数据中包含重复计划，请重新选择',
                type: 'error'
              })
              return
            } else {
              console.log('false')
            }

          } else {
            console.log('false')
          }
        }
      }

      if (this.isEdit) {
        this.tableList[this.editIndex] = obj
        this.tableList = this.tableList.concat([])
      } else {
        this.tableList.push(obj)
      }
      this.$emit('setCityList', this.tableList)
      this.changeDialog = false
    },
    editData(val, index) {
      let that = this
      this.isEdit = true
      this.editIndex = index
      this.changeDialog = true
      this.cityListData()
      this.userType()
      setTimeout(function() {
        that.expandedAry = val.cityAry
        console.log(that.expandedAry)
        that.$refs.cityTree.setCheckedKeys(val.cityAry)
        that.$refs.userTree.setCheckedKeys(val.userAry)
        that.$refs.cityBox.scrollTo(0, 0)
      }, 800)
    },
    removeData(val, index) {
      this.tableList.splice(index, 1)
      this.$emit('setCityList', this.tableList)
    },
    handleCheckAllChangeCity(val) {
      if (this.checkAllCity) {
        this.$refs.cityTree.setCheckedNodes(this.classList);
      } else {
        this.$refs.cityTree.setCheckedKeys([]);
      }
      this.isIndeterminateCity = false;
    },
    handleCheckAllChangeUser(val) {
      if (this.checkAllUser) {
        this.$refs.userTree.setCheckedNodes(this.userList);
      } else {
        this.$refs.userTree.setCheckedKeys([]);
      }
      this.isIndeterminateUser = false;
    },
    // 传递给 data 属性的数组中该节点所对应的对象、节点本身是否被选中、节点的子树中是否有被选中的节点
    handleCheckedCitiesChange(val, check, children) {
      console.log(val, check, children)
      let cityData = JSON.parse(
        JSON.stringify(this.$refs.cityTree.getCheckedNodes())
      )
      let checkedCount = cityData.length;
      this.checkAllCity = checkedCount === this.classListCount;
      this.isIndeterminateCity = checkedCount > 0 && checkedCount < this.classListCount;
    },
    handleCheckedUserChange(val,check,children){
      let userData = JSON.parse(
        JSON.stringify(this.$refs.userTree.getCheckedNodes())
      )
      let checkedCount = userData.length;
      this.checkAllUser = checkedCount === this.userList.length;
      this.isIndeterminateUser = checkedCount > 0 && checkedCount < this.userList.length;
    },
    indexMethod(index) {
      return  '方案' + (index+1);
    }
  }
}
</script>

<style lang="scss" scoped>
// @import '../../../assets/css/market';
.my-tree-box ::v-deep  .el-tree-node__children{
  white-space: normal;
}
.user-type ::v-deep  .el-tree-node{
  display: inline-block;
  margin-right: 15px;
}
.my-tree-box ::v-deep  .el-tree-node__children .el-tree-node {
  white-space: inherit !important;
  outline: 0;
  display: inline-block !important;
}
.my-tree-box ::v-deep   .el-tree-node__content {
  display: flex;
  align-items: center;
  height: 26px;
  cursor: pointer;
  font-size: 12px;
}
::v-deep   .el-checkbox__label {
  display: inline-block;
  padding-left: 10px;
  line-height: 19px;
  font-size: 12px;
}
::v-deep   .el-checkbox__input.is-checked + .el-checkbox__label {
  color: #606266;
}

.list-box ::v-deep  .el-button + .el-button {
  margin-left: 0px;
}
.btn-dib {
  display: inline-block;
  margin: 0 10px;
}
.tree-box {
  border: 1px solid #eaeaea;
  border-radius: 4px;
  border-radius: 4px;
  padding: 6px 12px;
}

.con-inner {
  padding-top: 15px;
  padding-left: 23px;
  margin-right: 17px;
  padding-bottom: 10px;
  border-bottom: 1px solid #efefef;

  div {
    display: inline-block;
  }

  .img {
    width: 92px;
    height: 92px;

    img {
      width: 100%;
      height: 100%;
    }
  }

  .text {
    padding-left: 20px;
    vertical-align: top;

    h3 {
      font-size: 14px;
      color: #000000;
      padding: 0;
      margin: 0;
    }

    p {
      padding: 0;
      margin: 0;
      font-size: 12px;
      color: #333333;
      padding-top: 10px;
    }
  }

  .btn {
    float: right;
    padding-top: 26px;

    button {
      width: 100px;
      height: 30px;
      text-align: center;
      line-height: 30px;
      padding: 0;
      background: rgba(65, 131, 213, 1);
      border-color: rgba(65, 131, 213, 1);
      border-radius: 4px;
      font-size: 14px;
    }

    a {
      color: #ffffff;
      text-decoration: none;
    }

    .router-link-active {
      color: #ffffff;
      text-decoration: none;
    }
  }
}

.pag-info {
  width: 500px;
}

::v-deep  .my-dialog .user{
  .el-tree-node__content > .el-tree-node__expand-icon {
    padding: 0px;
    font-size: 0;
  }
}
.operation-box {
  width: auto;
  white-space: nowrap;
  letter-spacing: 0;
  margin: 0 -10px;
  .el-button {
    position: relative;
    margin: 0 10px;
  }
  .el-button::before {
    position: absolute;
    // top: 14px;
    right: -6px;
    content: '';
    display: block;
    width: 1px;
    height: 12px;
    background: #dcdfe6;
  }
  .el-button:first-child {
    margin-left: 0;
  }
  .el-button:last-child {
    margin-right: 0;
  }
  .el-button:last-child::before {
    display: none;
  }
}
</style>
