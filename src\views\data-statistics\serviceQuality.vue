<template>
  <div class="mains-box">
    <el-form :model="form" ref="ruleForm" :rules="rules" :inline="true" size="small">
      <div class="search-info">
        <span class="sign"/>
        <span class="searchMsg">统计方式</span>
        <el-form-item prop="statisticalPlacer">
          <el-radio-group
            v-model="form.statisticalPlacer"
            @change="getTemplateRow"
            style="margin-left:30px"
          >
            <span class="radio-style">
              <el-radio :label="1" :key="1" class="radio-info">按天统计</el-radio>
              <el-tooltip effect="dark" placement="top">
                <template #content>只展示查询截止时间前31天内的数据</template>
                <p class="span-tip">!</p>
              </el-tooltip>
            </span>
            <span class="radio-style">
              <el-radio :label="2" :key="2" class="radio-info">按周统计</el-radio>
              <el-tooltip effect="dark" placement="top">
                <template #content>只展示查询截止时间前6个自然月内的数据</template>
                <p class="span-tip">!</p>
              </el-tooltip>
            </span>
            <span class="radio-style">
              <el-radio :label="3" :key="3" class="radio-info">按月统计</el-radio>
              <el-tooltip effect="dark" placement="top">
                <template #content>只展示查询截止时间前12个自然月内的数据</template>
                <p class="span-tip">!</p>
              </el-tooltip>
            </span>
            <span class="radio-style">
              <el-radio :label="4" :key="4" class="radio-info-long">按地区统计</el-radio>
              <el-tooltip effect="dark" placement="top">
                <template #content>只展示查询截止时间前12个自然月内的数据</template>
                <p class="span-tip">!</p>
              </el-tooltip>
            </span>
          </el-radio-group>
        </el-form-item>
      </div>
      <div class="search-info">
        <span class="sign"/>
        <span class="searchMsg">筛选条件</span>
        <el-form-item prop="provinceCodeList" style="margin-left:30px">
          <span class="search-title">省份</span>
          <el-select
            v-model.trim="form.provinceCodeList"
            placeholder="全部"
            clearable
            multiple
            collapse-tags
            @change="getProvince('getCity', $event)"
          >
            <el-option
              v-for="item in proviceList"
              :key="item.regionCode"
              :label="item.regionName"
              :value="item.regionCode"
            />
          </el-select>
        </el-form-item>
        <el-form-item prop="cityCodeList">
          <span class="search-title">城市</span>
          <el-select
            v-model.trim="form.cityCodeList"
            placeholder="全部"
            clearable
            multiple
            collapse-tags
            @change="getProvince('getArea', $event)"
          >
            <el-option
              v-for="item in cityList"
              :key="item.regionCode"
              :label="item.regionName"
              :value="item.regionCode"
            />
          </el-select>
        </el-form-item>
        <el-form-item prop="areaCodeList">
          <span class="search-title">区县</span>
          <el-select
            v-model.trim="form.areaCodeList"
            placeholder="全部"
            clearable
            multiple
            collapse-tags
          >
            <el-option
              v-for="item in areaList"
              :key="item.regionCode"
              :label="item.regionName"
              :value="item.regionCode"
            />
          </el-select>
        </el-form-item>
        <el-form-item prop="opTime">
          <span class="search-title">时间</span>
          <div style="display: table-cell; line-height: 24px">
            <el-date-picker
              :picker-options="pickerOptions"
              v-model.trim="form.opTime"
              popper-class="install-contr-cell-class"
              range-separator="至"
              size="small"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              placeholder="选择日期时间"
              type="daterange"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              prefix-icon="el-icon-date"
              class="timeSel"
              @focus="dateTimeFocus()"
            />
          </div>
        </el-form-item>
        <el-row>
          <el-button v-permission="['dataStatistics_serviceQuality_export']" class="btn-info"
                     size="small" @click="exportExcel">导出
          </el-button>
          <el-button class="search-btn" size="small" type="primary" @click="btnSearchHandler">查询
          </el-button>
          <el-button class="search-btn" size="small" @click="resetForm('ruleForm')">重置</el-button>
        </el-row>
      </div>
    </el-form>
    <div class="divider-info"></div>
    <template v-for="(item, index) in lineChartList">
      <line-chart
        v-if="item.chartId!='responseRate' && item.chartId != 'kfResponseTime'"
        ref="lineChart"
        :key="index"
        :titleInfo="item.titleInfo"
        :chartId="item.chartId"
        :tooltip="item.tooltip"
        :chartColor="item.chartColor"
        :asyncHandler="item.asyncHandler"
        :queryForm="form"
        :add-symbol="true"
      />
      <line-chart
        v-if="item.chartId=='responseRate'&&form.provinceCodeList<1&&form.cityCodeList<1&&form.areaCodeList<1&&form.statisticalPlacer!=4"
        ref="lineChart"
        :key="index"
        :titleInfo="item.titleInfo"
        :chartId="item.chartId"
        :tooltip="item.tooltip"
        :chartColor="item.chartColor"
        :asyncHandler="item.asyncHandler"
        :queryForm="form"
        :add-symbol="true"
      />
	  <line-chart
        v-if="item.chartId=='kfResponseTime' && form.statisticalPlacer != 4"
        ref="lineChart"
        :key="index"
        :titleInfo="item.titleInfo"
        :chartId="item.chartId"
        :tooltip="item.tooltip"
        :chartColor="item.chartColor"
        :asyncHandler="item.asyncHandler"
        :queryForm="form"
        :add-symbol="false"
      />
    </template>
    <export-tip
      :change-export="changeExport"
      @handleExoprClose="handleExoprClose"
      @handleChangeExport="handleChangeExport"
    />
  </div>
</template>
<style>
@import '../../assets/css/changeElement.scss';
</style>
<script>
import {
  apiDeliveryRate,
  apiRefundRate,
  apiReturnRate,
  apiResponseRate,
  apiServiceExport,
  getInvoiceAfterSale,
  getQuaAfterSale,
  querykfResponseTime,
  getRegionList
} from '@/api/data-statistics/index'
import lineChart from './components/lineChart';
import exportTip from '@/views/other/components/exportTip';
import {actionTracking} from '@/track/eventTracking';
import {ExposureTool, VNodeExposureTool} from "@/utils/exposureTools";

let exposureMonitor = null;
export default {
  name: 'ServiceQuality',
  components: {lineChart, exportTip},
  data() {
    return {
      form: {
        statisticalPlacer: 1,
        provinceCodeList: [],
        cityCodeList: [],
        areaCodeList: [],
        opTime: []
      },
      rules: {
        opTime: [
          {required: true, message: '日期为必填项', trigger: 'blur'}
        ]
      },
      lineChartList: [],
      proviceList: [],
      cityList: [],
      areaList: [],
      minDate: '',
      maxDate: '',
      changeExport: false,
    }
  },

  created() {
    let sevenDate = sessionStorage.getItem('sevenDate')
    if (sevenDate) {
      this.form.opTime = JSON.parse(sevenDate);
      sessionStorage.clear('sevenDate')
      //从首页右上的7日数据进入后报一个默认值
      actionTracking('service_quality_statistical_method_click', {
        service_quality_statistical_method: 'day'
      })

    } else {
      this.getNowTimeDate();
    }

    this.getProvince('getProv');
  },
  mounted() {
    this.getList();
    actionTracking('service_quality_click', { })
  },
  activated(){
    this.$nextTick(() => {
      let that = this;
      if (exposureMonitor) {
        exposureMonitor.end();
      } else {
        exposureMonitor = new VNodeExposureTool(document.querySelector(".mains-box").parentElement, (item) => {
          actionTracking('service_quality_exposure', {
            time: new Date().getTime(),
            org_id: that.orgId,
            service_quality: {
              deliveryRate : '48send',
              refundRate: 'refund',
              returnRete: 'return',
              responseRate: 'response'
            }[item.getChartId()]
          });
        })
      }
      exposureMonitor.begin(this.$refs.lineChart);
    })
  },
  deactivated(){
    exposureMonitor.end()
  },

  computed: {
    pickerOptions() {
      return {
        onPick: ({maxDate, minDate}) => {
          this.minDate = minDate;
          this.maxDate = maxDate;
        },
        disabledDate: (time) => {
          var statistical = 0;
          const curDate = new Date().getTime();
          const two = 373 * 24 * 3600 * 1000;
          const twoyear = curDate - two;
          if (this.form.statisticalPlacer == 1) {
            var statistical = 31 * 24 * 3600 * 1000;
            var pastResult = curDate - statistical;
            const oldTime = dayjs(pastResult).format('YYYY-MM-DD');
            statistical = curDate - new Date(oldTime).getTime();
          } else {
            var statistical = 372 * 24 * 3600 * 1000;
            var pastResult = curDate - statistical;
            const oldTime = dayjs(pastResult).format('YYYY-MM-DD');
            statistical = curDate - new Date(oldTime).getTime();
          }
          if (this.minDate) {
            if (this.form.statisticalPlacer == 2) {
              return (
                time.getTime() > Date.now() - 24 * 3600 * 1000 ||
                time.getTime() < twoyear ||
                time.getTime() > dayjs(this.minDate.getTime()).add(6, 'months') ||
                time.getTime() < dayjs(this.minDate.getTime()).add(-6, 'months')
              );
            } else {
              return (
                time.getTime() > Date.now() - 24 * 3600 * 1000 ||
                time.getTime() < twoyear ||
                time.getTime() > this.minDate.getTime() + (statistical - 24 * 3600 * 1000) ||
                time.getTime() < this.minDate.getTime() - (statistical - 24 * 3600 * 1000)
              );
            }
          }
          return time.getTime() > Date.now() - 24 * 3600 * 1000 || time.getTime() < twoyear;
        }
      }
    },
  },
  methods: {
    trackExposure(al) {
      console.log(al);

    },
    getTemplateRow() {
      this.getNowTimeDate();
    },
    btnSearchHandler() {
      this.$refs.ruleForm.validate(valid => {
        if (valid) {
          this.$refs.lineChart.map((item) => {
            item.getChart();
          })
        }
      });
    },
    getList(from) {
      this.lineChartList = [];
      this.getDeliveryRate();
      this.getRefundRate();
      this.getReturnRate();
      this.getResponseRate();
	  this.getInvoiceAfterSaleRate();
	  this.getQuaAfterSaleRate();
	  this.getkfResponseTimeRate();
    },
    //48h发货率
    getDeliveryRate() {
      let deliveryRate = {
        titleInfo: '48h发货率',
        chartId: 'deliveryRate', //ID是为了获取一个容器并设置高度
        chartColor: '#4184D5',
        tooltip: '48h发货率=付款后48小时内发货的订单/总订单数量<br/>48h内发货：“揽收时间- 付款时间”<=48小时<br/>订单状态：待审核、待发货、配送中、已完成、已退款',
        asyncHandler: apiDeliveryRate
      }
      this.lineChartList.push(deliveryRate);
    },
    //商家原因退款率
    getRefundRate() {
      let refundRate = {
        titleInfo: '商家原因退款率',
        chartId: 'refundRate', //ID是为了获取一个容器并设置高度
        chartColor: '#FF982C',
        tooltip: '商家原因退款率=因商业原因产生退款的订单/总订单数量<br/>订单状态：待审核、待发货、配送中、已完成、已退款<br/>商业原因退款：到货慢、发货慢、商品实物与展示不符、商品错漏发、破损或质量问题、商品缺货、商品发货少货、商品发错、商品近效期、破损及质量问题、批号不符、其他原因',
        asyncHandler: apiRefundRate
      }
      this.lineChartList.push(refundRate);
    },
    //商家原因退货率
    getReturnRate() {
      let returnRete = {
        titleInfo: '商家原因退货率',
        chartId: 'returnRete', //ID是为了获取一个容器并设置高度
        chartColor: '#BE41D5',
        tooltip: '商家原因退货率=因商业原因发起退款且退款时商品已发货的订单/总订单数量<br/>订单状态：待审核、待发货、配送中、已完成、已退款<br/>发起退款时商品已发货：“揽收时间“<=”发起退款时间”',
        asyncHandler: apiReturnRate
      }
      this.lineChartList.push(returnRete);
    },
	getInvoiceAfterSaleRate() {
		let returnRete = {
			titleInfo: '发票售后率',
			chartId: 'invoiceAfterSale', //ID是为了获取一个容器并设置高度
			chartColor: '#d57141',
			tooltip: '发票售后率=已处理发票售后订单数量/总订单数量<br/>已处理发票售后状态：商家已处理、商家已补发<br/>订单状态：待审核、待发货、配送中、已完成、已退款',
			asyncHandler: getInvoiceAfterSale
		}
		this.lineChartList.push(returnRete);
	},
	//客服响应时长
	getkfResponseTimeRate() {
		let kfResponseTime = {
			titleInfo: '客服响应时长',
			chartId: 'kfResponseTime', //ID是为了获取一个容器并设置高度
			chartColor: '#4151d5',
			tooltip: '客服平均响应时长=会话的首次响应时长之和/会话数<br/>单位：分钟',
			asyncHandler: querykfResponseTime
		}
		this.lineChartList.push(kfResponseTime);
	},
	getQuaAfterSaleRate() {
		let returnRete = {
			titleInfo: '资质售后率',
			chartId: 'quaAfterSale', //ID是为了获取一个容器并设置高度
			chartColor: '#41c7d5',
			tooltip: '资质售后率=已处理资质售后订单数量/总订单数量<br/>已处理资质售后状态：商家已处理、商家已补发<br/>订单状态：待审核、待发货、配送中、已完成、已退款',
			asyncHandler: getQuaAfterSale
		}
		this.lineChartList.push(returnRete);
	},
    //客服在线响应率
    getResponseRate() {
      let responseRate = {
        titleInfo: '客服在线响应率',
        chartId: 'responseRate',
        chartColor: '#D54141',
        tooltip: '客服在线响应率=（及时响应+超时响应）会话数/总会话数<br/>首次响应时长为0，计入“无响应”<br/>首次响应时长在180秒内回复客户的会话，计入“及时响应”<br/>首次响应时长超过180秒回复客户的会话，计入“超时响应”',
        asyncHandler: apiResponseRate
      }
      this.lineChartList.push(responseRate);
    },
    // 省市区
    getProvince(type, e) {
      let code = e && e.length ? '[' + e.join(',') + ']' : '';
      const pms = {parentCode: code || null};
      getRegionList(pms).then((res) => {
        if (type === 'getProv') {
          this.proviceList = res.data || [];
        } else if (type === 'getCity') {
          this.cityList = res.data || [];
        } else if (type === 'getArea') {
          this.areaList = res.data || [];
        }
      });
    },
    // 重置列表数据
    resetForm() {
      this.$refs.ruleForm.resetFields();
      this.getNowTimeDate();
      this.btnSearchHandler();
    },
    // 导出
    exportExcel() {
      actionTracking('service_quality_export', {
        time: new Date().getTime(),
        org_id: this.orgId
      })
      console.log(`$time = ${new Date().getTime()}, org = ${this.orgId}`);
      const that = this;
      const {
        statisticalPlacer,
        provinceCodeList,
        cityCodeList,
        areaCodeList,
        opTime
      } = this.form

      var params = {
        statisticalPlacer,
        startTime: opTime && opTime[0] ? opTime[0] : '',
        endTime: opTime && opTime[1] ? opTime[1] : '',
        provinceCodeList: provinceCodeList && provinceCodeList.length ? '[' + provinceCodeList.join(',') + ']' : '',
        cityCodeList: cityCodeList && cityCodeList.length ? '[' + cityCodeList.join(',') + ']' : '',
        areaCodeList: areaCodeList && areaCodeList.length ? '[' + areaCodeList.join(',') + ']' : '',
      };

      apiServiceExport(params).then(res => {
        if (res.code !== 0) {
          this.$message.error(res.message);
          return;
        }
        this.changeExport = true;
      });
    },
    handleExoprClose() {
      this.changeExport = false;
    },
    handleChangeExport(info) {
      this.changeExport = false;
      if (info === 'go') {
        const path = '/downloadList';
        window.openTab(path);
      }
    },
    dateTimeFocus() {
      this.minDate = undefined;
    },
    getNowTimeDate() {
      const time = new Date(8 * 3600 * 1000 + +new Date() - 24 * 3600 * 1000)
        .toJSON()
        .substr(0, 10);

      const cc = new Date().getTime();
      if (this.form.statisticalPlacer == 1) {
        var statistical = 31 * 24 * 3600 * 1000;
        var pastResult = cc - statistical;
        const oldTime = dayjs(pastResult).format('YYYY-MM-DD');
        this.form.opTime = [oldTime, time];
      } else if (this.form.statisticalPlacer == 2) {
        const oldTime = dayjs(cc).add(-6, 'months').format('YYYY-MM-DD');
        this.form.opTime = [oldTime, time];
      } else {
        var preMonth = 0;
        preMonth = 11;
        const NewTime = dayjs(cc).format('YYYY-MM-DD');
        var oldTime = this.GetPreMonthDay(NewTime, preMonth);
        this.form.opTime = [oldTime, time];
      }

      let that = this;
      const method = {
        1: 'day',
        2: 'week',
        3: 'month',
        4: 'region'
      }[this.form.statisticalPlacer];
      actionTracking('service_quality_statistical_method_click', {
        time: cc,
        org_id: that.orgId,
        service_quality_statistical_method: method
      })
    },
    GetPreMonthDay(date, monthNum) {
      var dateArr = date.split('-');
      var year = dateArr[0]; //获取当前日期的年份
      var month = dateArr[1]; //获取当前日期的月份
      var day = dateArr[2]; //获取当前日期的日
      var days = new Date(year, month, 0);
      days = days.getDate(); //获取当前日期中月的天数
      var year2 = year;
      var month2 = parseInt(month) - monthNum;
      if (month2 <= 0) {
        var absM = Math.abs(month2);
        year2 = parseInt(year2) - Math.ceil(absM / 12 == 0 ? 1 : parseInt(absM) / 12);
        month2 = 12 - (absM % 12);
      }
      // var day2 = day;
      // var days2 = new Date(year2, month2, 0);
      // days2 = days2.getDate();
      // if (day2 > days2) {
      //     day2 = days2;
      // }
      if (month2 < 10) {
        month2 = '0' + month2;
      }
      var day2 = '01';
      var t2 = year2 + '-' + month2 + '-' + day2;
      return t2;
    },
  }
}
</script>
<style lang="scss" scoped>
.mains-box {
  background: #ffffff;

  .search-info {
    padding: 10px 20px 0;
    font-weight: 500;

    .sign {
      display: inline-table;
      width: 3px;
      height: 13px;
      background: linear-gradient(360deg, #1d69c4 1%, #8bbdfc 99%);
      border-radius: 2px;
      margin-right: 8px;
      text-align: center;
    }

    .searchMsg {
      line-height: 32px;
      font-weight: 700;
    }

    .radio-style {
      padding: 0 30px 0 0;

      .radio-info {
        width: 50px;
      }

      .radio-info-long {
        width: 65px;
      }
    }

    .search-title {
      display: table-cell;
      padding: 0 10px;
      text-align: center;
      border: 1px solid #dcdfe6;
      height: 30px;
      line-height: 30px;
      vertical-align: middle;
      border-right: none;
      border-radius: 4px 0 0 4px;
      color: #333333;
      white-space: nowrap;
    }

    .search-btn {
      margin-left: 10px;
      float: right;
    }

    .btn-info {
      padding: 9px 15px;
      font-size: 12px;
      border-radius: 3px;
      border: 1px solid #4183d5;
      color: #4183d5;
      background: #fff;

      &:hover,
      &:focus {
        background: #4183d5;
        border-color: #4183d5;
        color: #ffffff;
      }
    }

    .timeSel {
      width: 280px;
    }

    ::v-deep  .el-select {
      display: table-cell;
    }

    ::v-deep  .el-form-item__content {
      width: 100%;
    }

    ::v-deep  .el-form--inline .el-form-item {
      display: inline-block;
      margin-right: 20px;
      vertical-align: top;
    }

    ::v-deep  .el-form-item--small .el-form-item__content {
      line-height: 30px;
    }

    ::v-deep  .el-form-item__label {
      margin-left: 20px;
      padding: 0;
    }

    ::v-deep  .el-input__inner {
      border-radius: 0 4px 4px 0;
    }

    // ::v-deep  .el-date-editor {
    //   width: 100%;
    // }
  }

  .span-tip {
    display: inline-block;
    width: 12px;
    height: 12px;
    font-size: 10px;
    border: 1px solid #999999;
    color: #999999;
    text-align: center;
    line-height: 12px;
    border-radius: 50%;
    margin-left: 5px;
  }

  .divider-info {
    margin: 10px 20px;
    border-bottom: 1px solid #f0f2f5;
  }

  #delivery,
  #purchase,
  #orderNum,
  #drugstoreNum,
  #drugstoreNumNew,
  #shopCouponAmount,
  #collageNetPurchase,
  #collageDrugstoreNum,
  #deliveryRate,
  #refundRate,
  #returnRete,
  #responseRate,
  #kfResponseTime {
    width: 100%;
    height: 408px;
  }
}
</style>
