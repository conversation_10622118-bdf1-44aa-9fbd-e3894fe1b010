<template>
  <el-dialog
    title="开票信息"
    :visible="dialogVisible"
    width="60%"
    @close="closeDialog"
  >
    <p>
      <span>发票类型：{{ {1: '电子普通发票', 2: '增值税专用发票', 3: '纸质普通发票', 4: '增值税电子专用发票'}[invoiceInfo.invoiceType] }}</span>
    </p>
    <p>
      <span>纳税人识别号：{{ invoiceInfo.taxRegistryNumber || '尚未登记' }}</span>
      <el-button size="mini" type="primary" v-if="invoiceInfo.taxRegistryNumber" @click="selectCopy(invoiceInfo.taxRegistryNumber)">复制</el-button>
    </p>
    <p>
      <span>开户银行：{{ invoiceInfo.bankName || '尚未登记' }}</span>
      <el-button size="mini" type="primary" v-if="invoiceInfo.bankName" @click="selectCopy(invoiceInfo.bankName)">复制</el-button>
    </p>
    <p>
      <span>银行账号：{{ invoiceInfo.bankAccount || '尚未登记' }}</span>
      <el-button size="mini" type="primary" v-if="invoiceInfo.bankAccount" @click="selectCopy(invoiceInfo.bankAccount)">复制</el-button>
    </p>
    <p>
      <span>公司名称：{{ invoiceInfo.customerName || '尚未登记' }}</span>
      <el-button size="mini" type="primary" v-if="invoiceInfo.customerName" @click="selectCopy(invoiceInfo.customerName)">复制</el-button>
    </p>
    <p>
      <span>注册地址：{{ invoiceInfo.reAddr || '尚未登记' }}</span>
      <el-button size="mini" type="primary" v-if="invoiceInfo.reAddr" @click="selectCopy(invoiceInfo.reAddr)">复制</el-button>
    </p>
    <p>
      <span>营业执照地址：{{ invoiceInfo.addr || '尚未登记' }}</span>
      <el-button size="mini" type="primary" v-if="invoiceInfo.addr" @click="selectCopy(invoiceInfo.addr)">复制</el-button>
    </p>
    <p>
      <span>电话：{{ invoiceInfo.userMobile || '尚未登记' }}</span>
      <el-button size="mini" type="primary" v-if="invoiceInfo.userMobile" @click="selectCopy(invoiceInfo.userMobile)">复制</el-button>
    </p>
  </el-dialog>
</template>
<script>
import { invoiceInfo } from '@/api/customer-management/index';


export default {
  name: 'InvoiceInfo',
  props: {
    dialogVisible: {
      type: Boolean,
      default: false,
    },
    merchantId: {
      type: Number,
      default: null,
    },
  },
  data() {
    return { invoiceInfo: {} };
  },
  mounted() {
    this.getInfo();
  },
  methods: {
    closeDialog() {
      this.$emit('cancelDialog');
    },
    getInfo() {
      invoiceInfo({ merchantId: this.merchantId }).then((res) => {
        if (res.code === 0) {
          this.invoiceInfo = res.result;
        }
      });
    },
    selectCopy(data) {
      const oInput = document.createElement('input');
      oInput.value = data;
      document.body.appendChild(oInput);
      oInput.select(); // 选择对象;
      document.execCommand('Copy'); // 执行浏览器复制命令
      this.$message({
        message: '复制成功',
        type: 'success',
      });
      oInput.remove();
    },
  },
};
</script>
<style lang="scss" scoped>
  p {
    display: flex;
    align-items: center;
    span {
      display: inline-block;
      margin-right: 20px;
      width: 80%;
      height: 40px;
      line-height: 40px;
      overflow-x: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
</style>
