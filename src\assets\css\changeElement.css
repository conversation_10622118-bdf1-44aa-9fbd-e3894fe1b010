.list-box .el-table {
  font-size: 12px !important;
}

.list-box .el-table th {
  font-size: 14px !important;
}

.el-button--text a {
  color: #4184d5;
  text-decoration: none;
  font-weight: initial;
}

.router-link-active {
  color: #4184d5;
  text-decoration: none;
}

.search-input .el-input-group__prepend {
  background-color: #fff;
  border-color: #D9D9D9;
  text-align: left;
  color: #333333;
  padding: 0 8px;
}

.search-input .el-input__inner {
  border-color: #D9D9D9;
}

.my-label .el-form-item__label {
  border: 1px solid #D9D9D9;
  border-radius: 4px 0px 0px 4px;
  padding: 0 13px 0 9px;
  height: 32px;
  vertical-align: bottom;
  border-right: 0;
  font-size: 12px;
}

.my-label .el-input__inner {
  border-radius: 0px 4px 4px 0px;
}

.search-btn .el-button--small {
  padding: 8px 15px;
  vertical-align: inherit;
}

.el-table .success-row {
  background: #F9F9F9;
}

.my-table.el-table td, .my-table.el-table th {
  padding: 8px 0;
}

.list-box .el-radio span.el-radio__label {
  color: #333333;
  font-weight: initial !important;
}

.sub-btn .el-button--primary {
  background-color: #4183D5;
  border-color: #4183D5;
  font-size: 14px;
}

.sub-btn .el-button--small {
  padding: 7px 15px;
}

.my-dialog .el-dialog {
  border-radius: 4px;
  overflow: hidden;
}

.my-dialog .el-dialog__body {
  padding: 0;
}

.my-dialog .el-dialog__header {
  padding: 0 15px;
  height: 40px;
  line-height: 40px;
  background: #F9F9F9;
}

.my-dialog .el-dialog__title {
  font-size: 16px;
  color: #333333;
}

.my-dialog .el-dialog__headerbtn {
  top: 10px;
}

.my-dialog .el-table {
  font-size: 12px !important;
}

.my-dialog .el-table th {
  font-size: 14px !important;
}

.el-message-box__wrapper div.el-message-box {
  width: 418px;
}

.el-message-box__message {
  padding-bottom: 0px;
  color: #333333;
  font-size: 14px;
  max-height: 400px;
  overflow-y: scroll;
  word-break: break-all;
}

div.el-message-box__header {
  padding: 10px 0 0 15px;
  font-size: 16px;
  color: #333333;
  height: 32px;
  background: rgba(249, 249, 249, 0.5);
}

div.el-message-box__title {
  color: #333333;
}

.el-message-box .el-button--small {
  width: 70px;
  height: 30px;
  border-radius: 4px;
}

.el-message-box__btns button:nth-child(2) {
  background: #4183d5;
}

.main-box .el-button--primary {
  background-color: #4183D5;
  border-color: #4183D5;
}

.el-button--primary {
  background-color: #4183D5;
}


.list-box .el-radio__input.is-checked .el-radio__inner {
  border-color: #4183D5;
  background: #4183D5;
}

.el-button--primary.is-plain {
  color: #4183D5;
}

.explain-table .el-checkbox__input.is-checked .el-checkbox__inner, .explain-table .el-checkbox__input.is-indeterminate .el-checkbox__inner {
  background-color: #4183D5;
  border-color: #4183D5;
}

.list-box .el-form-item--small .el-form-item__label {
  vertical-align: text-top;
}
/*# sourceMappingURL=changeElement.css.map */
