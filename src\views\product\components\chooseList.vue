<template>
  <div class="choose">
    <div>
      <ul class="goods-ul">
        <li
          v-for="(item, index) in listData"
          :key="index"
          :class="[index === chooseIndex ? 'goods-li choose-li' : 'goods-li']"
          @click="chooseList(item, index)"
        >
          <div class="content-box">
            <div class="img-box">
              <!-- @click.stop="lookImg(item.imageUrl)" -->
              <!-- <img :src="item.imageUrl" alt /> -->
              <el-image :src="item.imageUrl" :preview-src-list="[item.imageUrl]" @click.prevent>
                <div slot="error" class="image-slot">
                  <el-image src="https://oss-ec.ybm100.com/ybm/product/defaultPhoto.jpg" />
                </div>
              </el-image>
              <span v-if="item.disableType" class="disableType">{{item.disableType!= 0&& '已停用'}}
                <el-tooltip
                placement="top-start"
                :content="item.disableTypeName + ':' + item.disableNote "
                >
                  <span style="color:#4183d5">详情：</span>
                </el-tooltip>
              </span>
            </div>
            <div class="con-box">
              <h4 v-if="item.showName">{{item.brand}} {{ item.showName }}</h4>
              <p v-if="item.standardProductId">标品ID：{{ item.standardProductId }}</p>
              <p v-if="item.spec">规格：{{ item.spec }}</p>
              <el-tooltip
                v-if="item.manufacturer"
                class="item"
                effect="dark"
                :content="item.manufacturer"
                placement="top-start"
              >
                <p>
                  <span>生产厂家：{{ item.manufacturer }}</span>
                </p>
              </el-tooltip>
              <el-tooltip
                v-if="item.entrustedManufacturer && [1,3,4].includes(item.spuCategory)"
                class="item"
                effect="dark"
                :content="item.entrustedManufacturer"
                placement="top-start"
              >
                <p>
                  <span>受托生产厂家：{{ item.entrustedManufacturer }}</span>
                </p>
              </el-tooltip>
              <el-tooltip
                v-if="item.originPlace && item.spuCategory == 2"
                class="item"
                effect="dark"
                :content="item.originPlace"
                placement="top-start"
              >
                <p>
                  <span>产地：{{ item.originPlace }}</span>
                </p>
              </el-tooltip>
              <el-tooltip
                v-if="item.approvalNumber && [1,4].includes(item.spuCategory)"
                class="item"
                effect="dark"
                :content="item.approvalNumber"
                placement="top-start"
              >
                <p>批准文号：{{ item.approvalNumber }}</p>
              </el-tooltip>
              <el-tooltip
                v-if="item.qualityStandard && 2 == item.spuCategory"
                class="item"
                effect="dark"
                :content="item.qualityStandard"
                placement="top-start"
              >
                <p>质量标准：{{ item.qualityStandard }}</p>
              </el-tooltip>
              <el-tooltip
                v-if="item.approvalNumber && 3 == item.spuCategory"
                class="item"
                effect="dark"
                :content="item.approvalNumber"
                placement="top-start"
              >
                <p>注册证号/备案凭证号：{{ item.approvalNumber }}</p>
              </el-tooltip>
              <p v-if="item.productUnit">包装单位：{{ item.productUnit }}</p>
              <p v-if="item.code">条码：{{ item.code }}</p>
              <el-tooltip
                v-if="item.businessScopeMultiName"
                class="item"
                effect="dark"
                :content="item.businessScopeMultiName"
                placement="top-start"
              >
                <p>经营范围：{{ item.businessScopeMultiName }}</p>
              </el-tooltip>
              <!-- <p v-if="item.drugClassification">处方类型：{{ item.drugClassification }}</p>
              <p v-if="item.storageCondition">存储条件：{{ item.storageCondition }}</p>
              <p v-if="item.term">有效期：{{ item.term }}</p>
              <p v-if="item.size">码数：{{ item.size }}</p>
              <p v-if="item.colour">颜色：{{ item.colour }}</p>
              <p v-if="item.flavor">口味：{{ item.flavor }}</p> -->
            </div>
          </div>
          <p class="tip-box">
            <i class="el-icon-check" />
          </p>
          <p class="text-tip">
            {{
            item.result === 2
            ? '商品经营范围不在企业经营范围内'
            : item.result === 5
            ? '暂无销售权限'
            : ''
            }}
          </p>
        </li>
      </ul>
    </div>
    <el-image-viewer v-if="showViewer" :url-list="srcArr" :on-close="closeViewer" />
  </div>
</template>

<script>
import ElImageViewer from 'element-ui/packages/image/src/image-viewer';

export default {
  name: 'ChooseList',
  components: { ElImageViewer },
  props: {
    propData: {
      type: Array,
      default: () => []
    },
    isSubmit:{
      type: Boolean,
      default: () => false
    },
  },
  data() {
    return {
      sendData: {},
      listData: [],
      chooseIndex: null,
      sendSkuId: '',
      showViewer: false,
      srcArr: [],
      sendSkuIdCopy: ''
    };
  },
  watch: {
    propData: {
      handler(newVal) {
        this.listData = JSON.parse(JSON.stringify(newVal));
        this.initData();
      },
      immediate: true,
      deep: true,
    },
  },
  methods: {
    initData() {
      if (this.listData && this.sendSkuIdCopy) {
        const skuIds =  this.listData.map((item,index) => {
          return item.standardProductId;
        })
        if (skuIds.includes(this.sendSkuIdCopy)) {
          this.listData.forEach((item,index) => {
            if (this.sendSkuIdCopy == item.standardProductId) {
              this.sendSkuId = item.standardProductId;
              this.chooseIndex = index;
              this.sendData = item;
              this.sendSkuIdCopy = this.sendSkuId
            }
          });
        } else {
          this.sendSkuId = '';
          this.chooseIndex = null;
          this.sendData = {};
        }
      }
      if (this.isSubmit) {
        this.sendSkuId = '';
        this.chooseIndex = null;
        this.sendData = {};
        this.$emit('getSubmit', false)
      }
    },
    chooseList(data, index) {
      // if (data.disableType) return this.$message.error(data.disableTypeName + ":" + data.disableNote);
      if (data.disableType) return console.log('data.disableType:', data.disableType)
      this.sendData.isInit = false
      if (this.sendSkuId === data.standardProductId) {
        this.chooseIndex = null;
        this.sendSkuId = '';
        this.sendData = {};
        this.sendSkuIdCopy = this.sendSkuId
        sessionStorage.removeItem('pipeiProduct')
      } else {
        this.sendSkuId = data.standardProductId;
        this.chooseIndex = index;
        this.sendData = data;
        this.sendSkuIdCopy = this.sendSkuId
        this.sendData.isInit = true
        sessionStorage.removeItem('pipeiProduct')
        sessionStorage.setItem('pipeiProduct', 'true')
      }
      this.$emit('chooseGoods', this.sendData)
    },
    closeViewer() {
      this.showViewer = false;
    },
    lookImg(data) {
      this.srcArr.push(data);
      this.showViewer = true;
    }
  }
};
</script>

<style scoped lang="scss">
.choose {
  padding: 0 16px 16px;
  ::v-deep   .el-button--primary {
    background: #4183d5;
  }
  ::v-deep  .el-dialog__body {
    padding: 0 0 10px;
    height: 630px;
    overflow: auto;
  }
  ::v-deep   .el-dialog__header {
    padding: 10px 16px;
    background: #f9f9f9;
  }
  ::v-deep   .el-dialog__headerbtn {
    top: 13px;
  }
  ul,
  li {
    list-style: none;
    margin: 0;
  }
  .goods-detail {
    background: #fafafa;
    padding: 15px;
    margin-top: 10px;
    h3,
    p {
      padding: 0;
      margin: 0;
    }
    p {
      padding-top: 5px;
      font-size: 12px;
      color: #666666;
      span {
        margin-right: 10px;
      }
    }
  }
  .goods-tip {
    color: #ff8e00;
    font-size: 12px;
    padding: 8px 0;
    margin: 0;
  }
  .padding-box {
    padding: 0 15px;
  }
  .goods-ul {
    // box-sizing: border-box;
    position: relative;
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 10px;
    padding: 0;
    .goods-li {
      overflow: hidden;
      position: relative;
      background: #f9f9f9;
      border: solid 2px transparent;
      cursor: pointer;
      border-radius: 10px;
      .content-box {
        display: flex;
        gap: 10px;
        padding: 5px;
        height: 100%;
        .img-box {
          min-width: 100px;
          padding: 10px;
          flex-grow: 0;
          width: 25%;
          overflow: hidden;
          border-radius: 4px;
          height: max-content;
          text-align: center;
          // position: relative;
          ::v-deep   .el-image {
          background: #ffffff;
            width: 100%;
          }
        }
        .disableType {
          // bottom: 0;
          font-size: 10px;
          color: red;
        }
        .con-box {
          flex-grow: 0;
          width: 75%;
          h4,
          p {
            margin: 0;
            padding: 0;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }
          p {
            padding-top: 5px;
            font-size: 12px;
            color: #666666;
          }
        }
      }
      .tip-box {
        display: none;
        width: 20px;
        height: 20px;
        background: #4183d5;
        border-radius: 50%;
        color: #ffffff;
        position: absolute;
        top: 5px;
        right: 10px;
        margin: 0;
        text-align: center;
      }
      .text-tip {
        display: none;
        width: 100%;
        height: 24px;
        background: #eeeeee;
        font-size: 12px;
        color: #666666;
        text-align: center;
        position: absolute;
        top: 0;
        left: 0;
        margin: 0;
        line-height: 24px;
      }
    }
    li.choose-li {
      border: solid 2px #4183d5;
      background-color: #edf8ff;
      .tip-box {
        display: block;
      }
    }
    li.none-right {
      margin-right: 0;
    }
    li.disabled-li {
      .text-tip {
        display: block;
      }
    }
  }
}
</style>
