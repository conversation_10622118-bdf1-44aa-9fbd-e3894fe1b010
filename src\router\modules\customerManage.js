import layout from '@/layout';

const customerManage = {
  path: '/customerManage',
  name: 'customerManage',
  component: layout,
  meta: {
    title: '客户管理',
    icon: 'el-icon-s-custom',
  },
  children: [
    {
      path: `${process.env.VUE_APP_BASE_API}/openClientAccount/index`,
      name: 'openClientAccountIndex',
      meta: { title: '开户流程' },
    },
    {
      path: `${process.env.VUE_APP_BASE_API}/postmail/index`,
      name: 'postmailIndex',
      meta: { title: '邮寄查询' },
    },
    // {
    //   path: process.env.VUE_APP_BASE_API + '/merchant/index',
    //   name: 'merchantIndex',
    //   meta: {title: '客户管理'}
    // },
    {
      path: '/customerList',
      name: 'customerList',
      component: () => import('@/views/customer-management/customerList.vue'),
      meta: { title: '客户列表' },
    },
    {
      path: '/customerQualification',
      name: 'customerQualification',
      component: () => import('@/views/customer-management/customerQualification.vue'),
      hidden: true
    },
    {
      path: '/factoryReport',
      name: 'factoryReport',
      component: () => import('@/views/customer-management/factoryReport.vue'),
      meta: { title: '厂家查价举报' },
      hidden: true
    }
  ]
}
export default customerManage
