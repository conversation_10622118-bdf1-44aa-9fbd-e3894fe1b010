<template>
  <div class="my-dialog">
    <el-dialog
      title="选择控销组"
      :visible="true"
      ref="viewproduct"
      :modal-append-to-body="false"
      :close-on-click-modal="false"
      :before-close="handleDialogClose"
      width="80%"
    >
      <div>
        <el-form ref="ruleForm" :inline="true" size="small" label-width="100px">
          <el-form-item label="控销组ID" prop="id">
            <el-input v-model.trim="ruleForm.id" oninput="value=value.replace(/[^0-9.]/g,'')" placeholder="请输入" size="small"></el-input>
          </el-form-item>
          <el-form-item label="控销组名称" prop="name">
            <el-input v-model.trim="ruleForm.name" placeholder="请输入" size="small"></el-input>
          </el-form-item>
          <el-form-item>
            <el-button size="small" @click="resetForm()">重置</el-button>
            <el-button size="small" type="primary" @click="getList('search')">查询</el-button>
          </el-form-item>
        </el-form>
      </div>
      <div class="explain-table">
        <el-table
          v-loading="laodingBoole"
          :data="tableData.list"
          ref="dilogTable"
          stripe
          style="width: 100%"
          show-overflow-tooltip
          border
          :header-cell-style="{ background: '#F9F9F9', color: '#666666' }"
        >
          <!--单选选择-->
          <el-table-column align="right" prop="selection" width="50">
            <template #default="{ row }">
              <el-radio :value="innerSelected" :label="row.id" @input="selectChange($event, row)">
                <span />
              </el-radio>
            </template>
          </el-table-column>
          <el-table-column prop="id" label="控销组ID" />
          <el-table-column prop="name" label="控销组名称" />
          <el-table-column prop="createTime" label="创建时间" :formatter="formatDate" />
          <el-table-column prop="updateTime" label="更新时间" :formatter="formatDate" />
        </el-table>
      </div>
      <div class="explain-pag">
        <Pagination
          v-show="tableData.total > 0"
          :total="tableData.total"
          :page.sync="productPage.pageNum"
          :limit.sync="productPage.pageSize"
          @pagination="getList"
        ></Pagination>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancelDialog" size="small">取 消</el-button>
        <el-button type="primary" class="xyy-blue" @click="determineDialog" size="small">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import Pagination from '@/components/Pagination'
import { getSkuMerchantGroup } from '@/api/goods/controlGoods.js';
export default {
  name: 'SelectControlGroups',
  components: {
    Pagination
  },
  data() {
    return {
      ruleForm:{
        id: '',
        name: '',
      },
      list: [],
      show: true,
      tableData: {
        list: [],
        total: 0
      },
      laodingBoole: false, // 加载
      handerProduct: [],
      productPage: {
        pageSize: 10,
        pageNum: 1
      },
     
      selectItem: undefined,
      innerSelected:'',
    }
  },
  props: {
    selectedId: Number | String,
  },
  watch: {
    selectedId: {
      immediate: true,
      handler(newVale) {
        this.$nextTick(() => {
          this.innerSelected = JSON.parse(JSON.stringify(newVale));
        });
      },
    },
  },
  mounted() {
    this.getList();
  },
  methods: {
    handleDialogClose() {
      this.$emit('update:showSelectControlGroups', false);
    },
   
    getList(from) {
      const that = this;
      if (from == 'search') {
        this.productPage.pageNum = 1;
      }
      this.laodingBoole = true;
      const param = {
        ...this.ruleForm,
        ...this.productPage,
      };
      getSkuMerchantGroup(param).then((res) => {
         that.tableData.list = res.data.list;
        if (res.code == 0) {
          this.laodingBoole = false;
          if (res.data) {
            that.tableData.list = res.data.list;
          } else {
            this.tableData.list = [];
          }
          this.tableData.total = res.data.total;
        } else {
          this.laodingBoole = false;
          this.$message({
            message: res.message,
            type: 'error',
          });
        }
      }).catch(() => {
        this.laodingBoole = false;
      });
    },
     //重置弹窗数据
    resetForm() {
      this.ruleForm = {
        id: '',
        name: '',
      },
      this.productPage = {
        pageSize: 10,
        pageNum: 1
      }
      this.getList();
    },
    // 关闭弹窗
    cancelDialog(val) {
      this.handleDialogClose()
    },
    determineDialog() {
      if (this.selectItem) {
        this.$emit('selectGroupChange', this.selectItem);
        this.cancelDialog()
      } else {
        this.$message.error('请选择控销组');
      }
    },
    selectChange($event, row) {
      this.innerSelected = row.id;
      this.selectItem = row;
    },
    // rowClick(row) {
    //   this.selectChange(row.id, row);
    // },
    // 时间格式化
    formatDate(row, column, cellValue) {
      const date = new Date(cellValue)
      const y = date.getFullYear()
      let MM = date.getMonth() + 1
      MM = MM < 10 ? `0${MM}` : MM
      let d = date.getDate()
      d = d < 10 ? `0${d}` : d
      let h = date.getHours()
      h = h < 10 ? `0${h}` : h
      let m = date.getMinutes()
      m = m < 10 ? `0${m}` : m
      let s = date.getSeconds()
      s = s < 10 ? `0${s}` : s
      return `${y}-${MM}-${d} ${h}:${m}:${s}`
    },
  }
}
</script>
<style lang="scss" scoped>
.my-dialog {
  min-width: 400px;
}
::v-deep  .el-form-item__label {
  margin-left: 20px;
  padding: 0;
}
::v-deep  .el-input__inner {
  border-radius: 0 4px 4px 0;
}
::v-deep  .el-form-item__label {
  padding: 0;
}
::v-deep  .el-input__inner {
  border-radius: 0;
}
::v-deep  .el-dialog__body {
  padding: 0 20px;
}

::v-deep  .el-dialog__wrapper .el-dialog__header {
  background: #f9f9f9;
}

::v-deep  .el-dialog__wrapper .el-dialog__title {
  font-size: 16px;
  font-weight: 500;
  color: #333333;
}

::v-deep  .el-divider--horizontal {
  width: 104%;
  margin: 0 0 0 -20px;
}
::v-deep  .el-pagination__sizes .el-select {
  top: 0;
  left: 0;
}
</style>
