<template>
  <el-dialog
    title="商品ERP编码重复提醒"
    :visible="true"
    width="60%"
    :before-close="handleClose"
  >
    <p v-if="productAbnormal.sameErpCodeCount">
      您有<span class="redText">{{ productAbnormal.sameErpCodeCount }}</span>个普通商品的ERP编码重复，这些商品无法自动同步ERP信息（包括<span class="redText">价格</span>和<span class="redText">库存</span>等信息），请检查商品的ERP编码是否填写错误！！！
      <span
        class="blueText"
        @click="toSearch('sameErpCode', true)"
      >立即查看</span>
    </p>
    <p v-if="productAbnormal.sameErpCodeFromErpCount">
      您有<span class="redText">{{ productAbnormal.sameErpCodeFromErpCount }}</span>个普通商品的ERP编码在ERP系统里重复，这些商品无法自动同步ERP信息（包括<span class="redText">价格</span>和<span class="redText">库存</span>等信息），请检查商品的ERP编码是否填写错误！！！
      <span
        class="blueText"
        @click="toSearch('sameErpCodeFromErp', true)"
      >立即查看</span>
    </p>
    <div>提示：如果希望同一商品设置不同价格后投放给不同区域的客户，可以通过创建拼团活动实现。</div>
  </el-dialog>
</template>

<script>
export default {
  name: 'RepeatTips',
  props: {
    productAbnormal: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {};
  },
  created() {
  },
  methods: {
    handleClose() {
      this.$emit('handleClose');
    },
    toSearch(key, value) {
      this.$emit('tipsSearch', key, value);
      this.handleClose();
    },
  },
};
</script>

<style scoped>
.redText {
  color: red;
}
.blueText {
  color: #4183d5;
  cursor: pointer;
}
</style>
