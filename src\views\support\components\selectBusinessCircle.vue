<template>
  <div class="my-dialog">
    <el-dialog
      title="选择商圈"
      :visible="show"
      ref="viewproduct"
      :modal-append-to-body="false"
      :close-on-click-modal="false"
      :before-close="handleDialogClose"
      width="96%"
      @open="open"
    >
      <div>
        <el-form ref="ruleForm" :inline="true" size="small" label-width="100px">
          <el-form-item label="商圈名称">
            <el-input v-model="ruleForm.busAreaName" placeholder="请输入" size="small"></el-input>
          </el-form-item>
          <el-form-item label="商圈类型" prop="branchCode">
            <el-select v-model="ruleForm.type" size="small" placeholder="全部">
              <el-option
                v-for="item in businessType"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="商圈来源" prop="source" v-if="fromComp === 'groupActivityTheme' || fromComp === 'batchEdit'">
            <el-select v-model="ruleForm.source" size="small" placeholder="全部">
              <el-option
                v-for="item in businessSource"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="开始时间" prop="opTime">
            <div style="display: table-cell; line-height: 24px">
              <el-date-picker
                v-model.trim="ruleForm.opTime"
                size="small"
                popper-class="install-contr-cell-class"
                range-separator="至"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
                type="daterange"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                prefix-icon="el-icon-date"
              />
            </div>
          </el-form-item>
          <el-form-item>
            <el-button size="small" @click="resetForm('ruleForm')">重置</el-button>
            <el-button size="small" type="primary" @click="getList('search')">查询</el-button>
          </el-form-item>
        </el-form>
      </div>
      <div class="explain-table">
        <el-table
          v-loading="laodingBoole"
          :data="tableData.list"
          ref="dilogTable"
          stripe
          style="width: 100%"
          show-overflow-tooltip
          border
          :header-cell-style="{ background: '#F9F9F9', color: '#666666' }"
        >
          <!--单选选择-->
          <el-table-column align="right" prop="selection" width="50">
            <template #default="{ row }">
              <el-radio :value="innerSelected" :label="row.id" @input="selectChange($event, row)">
                <span />
              </el-radio>
            </template>
          </el-table-column>
          <el-table-column label="商圈名称" prop="busAreaName">
            <template slot-scope="scope">
              <div class="typeEnter">{{ scope.row.busAreaName }}</div>
            </template>
          </el-table-column>
          <el-table-column prop="busAreaDesc" label="商圈描述"></el-table-column>
          <el-table-column prop="typeName" label="商圈类型"></el-table-column>
          <el-table-column prop="updateUser" label="操作人" />
          <el-table-column prop="updateTime" label="操作时间" :formatter="formatDate"></el-table-column>
          <template slot="empty">
            <div class="noData">
              <p class="img-box">
                <img :src="emptyImg" alt />
              </p>
              <p>暂无数据</p>
            </div>
          </template>
        </el-table>
      </div>
      <div class="explain-pag">
        <Pagination
          v-show="tableData.total > 0"
          :total="tableData.total"
          :page.sync="productPage.pageNum"
          :limit.sync="productPage.pageSize"
          @pagination="getList"
        ></Pagination>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancelDialog" size="small">取 消</el-button>
        <el-button type="primary" class="xyy-blue" @click="determineDialog" size="small">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import Pagination from '@/components/Pagination'
import { apiPageQuery } from '@/api/product';
export default {
  name: 'selectBusinessCircle',
  components: {
    Pagination
  },
  data() {
    return {
      ruleForm:{
        busAreaName:'',//商圈名称
        type:'',//商圈类型
        opTime:[],// 开始时间
        source: '', // 商圈来源
      },
      list: [],
      show: false,
      tableData: {
        list: [],
        total: 0
      },
      laodingBoole: false, // 加载
      handerProduct: [],
      productPage: {
        pageSize: 10,
        pageNum: 1
      },
      changeDialog: false,
      // 商圈来源下拉框
      businessSource: [
        {
          value: '',
          label: '全部',
        },
        {
          value: '1',
          label: '系统默认',
        },
        {
          value: '2',
          label: '手工创建',
        },
      ],
      // 商圈类型下拉框
      businessType: [
        {
          value: '',
          label: '全部'
        },
        {
          value: '1',
          label: '药品'
        },
        {
          value: '2',
          label: '非药'
        },
        {
          value: '3',
          label: '药品和非药'
        }
      ],
      emptyImg:'',
      selectItem: undefined,
      innerSelected:'',
      innerSaleType:''
    }
  },
  model: {
    prop: 'dialogShow',
    event: 'onDialogChange'
  },
  props: {
    dialogShow: Boolean,
    row: Object,
    selected: Number | String,
    saleType: Number | String,
    fromComp: String,
  },
  watch: {
    selected: {
      immediate: true,
      handler(newVale) {
        if (newVale) {
          this.$nextTick(() => {
            this.innerSelected = JSON.parse(JSON.stringify(newVale));
          });
        }
      },
    },
    saleType: {
      immediate: true,
      handler(newVale) {
        if (newVale) {
          this.$nextTick(() => {
            this.innerSaleType = JSON.parse(JSON.stringify(newVale));
          });
        }
      },
    },
  },
  mounted() {
    this.$nextTick(() => {
      this.show = true
    })
  },
  methods: {
    handleDialogClose() {
      this.show = false
      this.$emit('onDialogChange', false)
    },
    open() {
      this.getList()
      this.show = true
    },
    // 查询商圈
    getList(from) {
      const that = this;
      if (from == 'search') {
        this.productPage.pageNum = 1;
      }
      this.laodingBoole = true;
      const param = {
        busAreaName: this.ruleForm.busAreaName,
        type: this.ruleForm.type,
        source: this.ruleForm.source || '',
        ...this.productPage,
      };
      if (this.fromComp === 'groupActivityTheme' || this.fromComp === 'batchEdit') {
        param.sources = '1,2';
      }
      if (this.ruleForm.opTime) {
        param.beginTime = this.ruleForm.opTime[0]||'';
        param.endTime = this.ruleForm.opTime[1]||'';
      }
      apiPageQuery(param).then((res) => {
        if (res.code == 0) {
          this.laodingBoole = false;
          if (res.result) {
            that.tableData.list = res.result.list;
          } else {
            this.tableData.list = [];
          }
          this.tableData.total = res.result.total;
        } else {
          this.laodingBoole = false;
          this.$message({
            message: res.message,
            type: 'error',
          });
        }
      }).catch(() => {});
    },
     //重置弹窗数据
    resetForm(from) {
      this.ruleForm = {
        busAreaName:'',//商圈名称
        type:'',//商圈类型
        opTime:[],// 开始时间
      },
      this.productPage = {
        pageSize: 10,
        pageNum: 1
      }
      this.getList();
    },
    // 关闭弹窗
    cancelDialog(val) {
      this.handleDialogClose()
    },
    determineDialog() {
      if (this.selectItem) {
        this.$emit('selectChange', this.selectItem);
      }
      if (!this.innerSelected) {
        this.$message.error('请选择商圈');
        return;
      }
      this.cancelDialog();
    },
    selectChange($event, row) {
      if(row.type==3 || this.innerSaleType===row.type || this.fromComp === 'groupActivityTheme' || this.fromComp === 'batchEdit'){
        this.innerSelected = row.id;
        this.selectItem = row;
      }else{
        setTimeout(() => {
          this.$message.error('商圈类型不匹配');
        }, 0);
      }
    },
    rowClick(row) {
      this.selectChange(row.id, row);
    },
    // 时间格式化
    formatDate(row, column, cellValue) {
      const date = new Date(cellValue)
      const y = date.getFullYear()
      let MM = date.getMonth() + 1
      MM = MM < 10 ? `0${MM}` : MM
      let d = date.getDate()
      d = d < 10 ? `0${d}` : d
      let h = date.getHours()
      h = h < 10 ? `0${h}` : h
      let m = date.getMinutes()
      m = m < 10 ? `0${m}` : m
      let s = date.getSeconds()
      s = s < 10 ? `0${s}` : s
      return `${y}-${MM}-${d} ${h}:${m}:${s}`
    },
  }
}
</script>
<style lang="scss" scoped>
.my-dialog {
  min-width: 400px;
}
::v-deep  .el-form-item__label {
  margin-left: 20px;
  padding: 0;
}
::v-deep  .el-input__inner {
  border-radius: 0 4px 4px 0;
}
::v-deep  .el-form-item__label {
  padding: 0;
}
::v-deep  .el-input__inner {
  border-radius: 0;
}
::v-deep  .el-dialog__body {
  padding: 0 20px;
}

::v-deep  .el-dialog__wrapper .el-dialog__header {
  background: #f9f9f9;
}

::v-deep  .el-dialog__wrapper .el-dialog__title {
  font-size: 16px;
  font-weight: 500;
  color: #333333;
}

::v-deep  .el-divider--horizontal {
  width: 104%;
  margin: 0 0 0 -20px;
}
::v-deep  .el-pagination__sizes .el-select {
  top: 0;
  left: 0;
}
</style>
