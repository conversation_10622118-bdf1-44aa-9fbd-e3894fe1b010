<template>
  <el-dialog
    v-loading="loading"
    title="批量修改活动商品"
    :visible="dialogVisible"
    width="40%"
    :before-close="handleClose"
  >
    <el-upload
      ref="uploadActivity"
      action=""
      class="upload-box"
      accept=".xls,.xlsx"
      :on-change="uploadChange"
      :on-exceed="fileBeyond"
      :before-remove="remove"
      :http-request="httpRequest"
      :limit="1"
      :file-list="fileLists"
      :auto-upload="false"
    >
      <el-button
        type="primary"
        plain
        size="small"
      >
        导入excel文件
      </el-button>
      <el-button
        slot="tip"
        class="downloadTemplate"
        plain
        size="small"
        @click="downloadTemplate"
      >
        下载模板
      </el-button>
    </el-upload>
    <p>提示：</p>
    <p>仅支持上传xlsx、xls文件，大小不好过3M，商品数量不超过1000个。</p>
    <span
      slot="footer"
      class="dialog-footer"
    >
      <el-button
        size="small"
        @click="handleClose"
      >取 消</el-button>
      <el-button
        type="primary"
        size="small"
        @click="submit"
      >
        确 定
      </el-button>
    </span>
  </el-dialog>
</template>

<script>
import { batchUpdateActivityInfoFromExcel, apiDownloadTemplate } from '@/api/product';

export default {
  name: 'BatchModifyProductActivity',
  props: {
    excelTemplate: {
      type: String,
      default: '',
    },
    priceType: {
      type: Number,
      default: 1,
    },
    handleCloseActivity: {
      type: Function,
      default: () => {}
    },
  },
  data() {
    return {
      loading: false,
      dialogVisible: true,
      fileLists: [],
    };
  },
  methods: {
    handleClose() {
      this.handleCloseActivity();
    },
    submit() {
      console.log(this.fileLists)
      if (this.fileLists.length) {
        this.$refs.uploadActivity.submit()
      } else {
        this.loading = false
        // this.$message({
        //   message: '请先导入商品！',
        //   type: 'warning'
        // })
        this.$alert('请先导入活动商品！', {type: 'warning'})
      }
    },
    uploadChange(file, fileList) {
      if (file.status === 'ready') {
        this.fileLists = fileList
      }
    },
    async httpRequest(param) {
      const fd = new FormData() // FormData 对象
      fd.append('file', param.file) // 文件对象
      const h = this.$createElement;
      try {
        this.loading = true
        const res = await batchUpdateActivityInfoFromExcel(fd)
        if (res && res.code === 0) {
          const {error, success, errorFileName, errorFileUrl} = res.data
          const baseUrl = process.env.VUE_APP_BASE_API
          this.$msgbox({
            title: '上传文件反馈',
            message: h('p', null, [
              h('span', null, `共成功上传${success}个活动商品信息，失败${error}条数据${error?'，下载错误文件':''}`),
              error?h('a', {attrs: {href: baseUrl+errorFileUrl, download: errorFileName}}, `${errorFileName}`):''
            ]),
            confirmButtonText: '确定'
          }).then(() => {
            if (success) this.$emit('refreshTable')
            this.handleClose()
          }).catch(()=>{
            if (success) this.$emit('refreshTable')
            this.handleClose()
          })
        } else {
          this.fileLists = []
          // this.$message.error(res.message || '导入失败，请重新上传！')
          this.$alert(res.message|| '导入失败，请重新上传！', {type: 'error'})
        }
        this.loading = false
      } catch (err) {
        console.log(err)
      }
      this.loading = false
    },
    // 文件超出限制
    fileBeyond() {
      this.$message({
        message: '每次最多上传1个文件',
        type: 'warning'
      })
    },
    remove() {
      this.fileLists = []
    },
    downloadTemplate() {
      // const a = document.createElement('a')
      // const filename = '上传模板.xlsx'
      // a.href = this.excelTemplate
      // a.download = filename
      // document.body.appendChild(a)
      // a.click()
      // document.body.removeChild(a)
      try {
        const params = {
          fileName: '批量修改活动品信息模板.xlsx',
        };
        apiDownloadTemplate(params).then((res) => {
          if (res.code && res.code !== 0) {
            this.$message.error(res.message || '请求异常');
          } else {
            this.util.exportExcel(res, '批量修改活动品信息模板.xlsx');
          }
        }).catch((err) => {
          console.log(err);
        });
      } catch (e) {}
    }
  }
}
</script>

<style scoped>
.downloadTemplate{
  margin-left: 20px;
}
</style>
