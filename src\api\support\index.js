import request from '../index'

/**
 * 获取商品列表
 * @param {参数} params
 */
export function getProductList(params) {
  return request.get('/self/product/list', params);
}
/**
 * 获取商品详情
 * @param {参数} params
 */
export function getSupportDetail(params) {
  return request.get('/self/product/detail', params);
}

/**
 * 编辑商品
 * @param {参数} params
 */
export function getSupportEdit(params) {
  return request.post('/self/product/editSku', params);
}
/**
 * 查看变更日志
 * @param {参数} params
 */
export function getSkuLogV2(params) {
  return request.get('/self/product/examineLog', params);
}
/**
 * 商品上下架
 */
 export function batchUpAndDown(params) {
  return request.post('/self/product/sku/batchUpAndDownV2', params);
}

