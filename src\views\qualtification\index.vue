<template>
  <div class="qual-box">
    <div class="qual-state" v-if='!ifModify'>
      <div v-if="!isEdit">
        <span class="h">认证状态:</span>
        <span :class="stateClass">{{ stateText }}</span>
        <span class="t" v-html="stateTip" />
        <!--        <span>去上传</span>-->
      </div>
      <div v-show="allData.state != 1 && isBtnShow" class="color4">
        <span v-show="allData.state === 5 && isCheck" @click="updateBank">
          <i class="line" /> 修改企业对公账户
        </span>
        <span v-show="allData.state === 5 && isCheck && shopConfig.shopPatternCode !== 'ybm'" @click="editStoreInfo(false)">
          <i class="line" /> 修改店铺LOGO & 电话
        </span>
        <span v-permission="['corporation_info_update']" v-show="allData.state === 5 && isCheck" @click="editData">
          <i class="line" /> 修改信息
        </span>
        <span @click="getHistory">
          <i class="line" />
          <img class="li-img" src="../../assets/image/marketing/lishi.png" alt /> 历史记录
        </span>
      </div>
      <span
        v-show="isEdit"
        class="t"
      >请修改需要更新的资质信息并提交。新修改的信息在审核通过前，仍可使用原资质继续开店，页面也将继续显示原资质信息。可在“历史记录”中查看此次修改记录的进展。</span>
    </div>
    <div v-if="qualtificationInfos.size" style="color: red;padding: 20px 0 0 20px;">
      *当前有 {{ qualtificationInfos.size }} 项资质已过期或即将过期（0天<=资质有效期至-当前日期<=30天），资质过期店铺会被自动下线，请尽快更新资质
    </div>
    <div v-if="qualtificationInfos.size" style="color: red;padding: 20px 0 0 20px;">
      被驳回的资质修改在右上角【历史记录】中点击【查看】可在原单据上修改并且提交
    </div>
    <div class="serch">
      <el-row type="flex" align="middle">
        <span class="sign" />
        <div>企业基本信息</div>
      </el-row>
    </div>
    <Basic
      ref="basic"
      :corporation-type="corporationType"
      :basic-data="allData"
      :is-detail="isDetail"
      :ifModify='ifModify'
      :qual-state-str="qualStateStr"
      :host-name="hostName"
      @formBasic="formBasic"
      @changeType="changeType"
    />
    <div class="serch">
      <el-row type="flex" align="middle">
        <span class="sign" />
        <div>企业工商信息</div>
      </el-row>
    </div>
    <Cor
      v-if="changeShow"
      ref="cor"
      :company-qual-data="companyQualData"
      :host-name="hostName"
      :is-detail="isDetail"
      :qual-state-str="qualStateStr"
      :corporation-qualifications="allData.corporationQualifications"
      @formCor="formCor"
    />
    <div class="serch">
      <el-row type="flex" align="middle">
        <span class="sign" />
        <div>企业经营范围及资质</div>
      </el-row>
    </div>
    <Qual
      v-if="changeShow"
      ref="qual"
      :qualification-data="qualificationData"
      :business-category="businessCategory"
      :host-name="hostName"
      :is-detail="isDetail"
      :qual-state-str="qualStateStr"
      :checked-bussiness="allData.corporationBusiness"
      :corporation-qualifications="allData.corporationQualifications"
      @formQual="formQual"
      @setHeight="setHeight"
    />
    <div v-if="isDetail||ifModify" class="footer">
      <div class="statement-box">
        <el-checkbox :checked="statementIs" @change="statementIs = !statementIs" />
        <div
          style="padding-left: 10px"
        >
          已阅读并同意
          <span
            style="color: #4184d5;cursor: pointer;"
            @click="handleCheckServiceAgreement(1)"
          >《平台服务协议》</span>
          <span
            style="color: #4184d5;cursor: pointer;"
            @click="handleCheckServiceAgreement(2)"
          >《平安银行“产业结算通”会员服务协议》</span>。保证此页面所填写内容均真实有效，特别是经营地址为店铺最新可联系到的地址，同时可以作为行政机关和司法机关送达法律文件的地址。如果上述地址信息有误，愿意承担由此带来的平台处罚（处罚细则）、行政监管和司法诉讼风险。
        </div>
      </div>
      <div class="bot-box">
        <el-button
          v-if="!qualStateStr || qualStateStr === 1"
          size="medium"
          @click="submitData(1)"
        >暂存</el-button>
        <el-button type="primary" size="medium" @click="submitData(2)">提交</el-button>
      </div>
    </div>
    <el-dialog
      title="修改记录"
      :visible.sync="dialogVisible"
      offset="5%"
      width="620px"
      class="his-dialog"
    >
      <div v-if="historyList.length > 0">
        <div v-for="(item, index) in historyList" :key="index" style="margin-bottom: 10px">
          <div class="dis-div">
            <span>{{ item.createTime | formatDate }}</span>
            <span>{{ item.recordType | formatType }}</span>
            <span>{{ item.state | formatStatus }}</span>
            <span v-if="(item.recordType === 0 || item.recordType === 1) && item.state !== 3" />
            <span
              v-else
              style="color: #4183d5; text-align: center; cursor: pointer"
              @click="lookHistory(item.batch )"
            >查看</span>
          </div>
          <div v-if="item.remarks && item.state === 2" class="div-remarks">{{ item.remarks }}</div>
        </div>
      </div>
      <div v-else>暂无历史记录</div>
    </el-dialog>
    <el-dialog
      title="修改企业对公账户"
      :visible.sync="bankVisible"
      offset="5%"
      width="620px"
      class="his-dialog"
    >
      <el-form
        ref="bankRef"
        :model="bankForm"
        size="small"
        label-position="right"
        :rules="bankRules"
        label-width="80px"
      >
        <el-form-item
          label="对公账户户名"
          prop="legalPersonName"
          class="bankLabel"
        >
          <el-input
            v-model.trim="bankForm.legalPersonName"
            placeholder="请填写企业名称"
            type="text"
          />
        </el-form-item>
        <el-form-item
          label="对公银行卡号"
          prop="code"
          class="bankLabel"
        >
          <el-input
            v-model.trim="bankForm.code"
            type="text"
            placeholder="请填写企业对公银行卡号"
          />
        </el-form-item>
        <el-form-item
          label="开户银行"
          prop="bankName"
          class="bankLabel"
        >
          <el-input
            v-model.trim="bankForm.bankName"
            placeholder="请填写企业对公账户开户银行及支行"
            type="text"
          />
        </el-form-item>
        <el-form-item
          label="开户支行"
          prop="subBankName"
          class="bankLabel"
        >
          <el-input
            v-model.trim="bankForm.subBankName"
            placeholder="请填写企业对公账户开户银行及支行"
            type="text"
          />
        </el-form-item>
      </el-form>
      <span
        slot="footer"
        class="dialog-footer"
      >
        <el-button @click="closeUpdateBankDialog">取 消</el-button>
        <el-button
          type="primary"
          @click="handleUpdateBankConfirm"
        >确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
// import $ from 'jquery'
import { mapState } from 'vuex';
import Basic from './basicComponent';
import Cor from './corComponent';
import Qual from './qualComponent';

import {
  getCorporationDraft,
  getHostName,
  getCompanyInfo,
  getBusinessCategory,
  getQualificationInfo,
  getHistoryList,
  submitAddCorporation,
  submitAddCheckCorporation,
  submitModifyCorporation,
  saveCorporationDraft,
  getCheckCorporation,
  updateCorporationInfoWithoutCheck,
  getUpdateBankInfo,
  updateBankInfo
} from '../../api/qual/index';

export default {
  name: 'Qualtification',
  components: {
    Basic,
    Cor,
    Qual,
  },
  filters: {
    formatDate(value) {
      const date = new Date(value);
      const y = date.getFullYear();
      let MM = date.getMonth() + 1;
      MM = MM < 10 ? `0${MM}` : MM;
      let d = date.getDate();
      d = d < 10 ? `0${d}` : d;
      let h = date.getHours();
      h = h < 10 ? `0${h}` : h;
      let m = date.getMinutes();
      m = m < 10 ? `0${m}` : m;
      let s = date.getSeconds();
      s = s < 10 ? `0${s}` : s;
      return `${y}-${MM}-${d} ${h}:${m}:${s}`;
    },
    formatStatus(value) {
      let nameStr = '';
      switch (value) {
        case 1:
          nameStr = '待审核';
          break;
        case 2:
          nameStr = '认证驳回';
          break;
        case 3:
          nameStr = '已认证';
          break;
        default:
          nameStr = '';
          break;
      }
      return nameStr;
    },
    formatType(value) {
      let typeStr = '';
      switch (value) {
        case 0:
          typeStr = '提交认证申请';
          break;
        case 1:
          typeStr = '提交认证申请';
          break;
        case 2:
          typeStr = '修改';
          break;
        default:
          typeStr = '';
          break;
      }
      return typeStr;
    },
  },
  data() {
    return {
      bankVisible: false,
      bankForm: {
        legalPersonName: "",
        code: "",
        bankName: "",
        subBankName: "",
      },
      bankRules: {
        legalPersonName: [
          { required: true, message: '请输入企业名称', trigger: 'blur' },
          { min: 1, max: 50, message: '长度在 1 到 50 个字符', trigger: 'blur' },
        ],
        code: [
          { required: true, message: '请输入对公银行卡号', trigger: 'blur' },
          { min: 1, max: 50, message: '长度在 1 到 50 个字符', trigger: 'blur' },
        ],
        bankName: [
          { required: true, message: '请输入企业对公账户开户银行', trigger: 'blur' },
          { min: 1, max: 300, message: '长度在 1 到 300 个字符', trigger: 'blur' },
        ],
        subBankName: [
          { required: true, message: '请输入企业对公账户开户支行', trigger: 'blur' },
          { min: 1, max: 50, message: '长度在 1 到 50 个字符', trigger: 'blur' },
        ],
      },
      hostName: '',
      stateClass: '',
      stateText: '未认证',
      stateTip: '您尚未进行资质认证，请填写以下信息',
      businessList: {},
      corporationType: 0, // 企业类型
      companyQualData: {}, // 企业资质信息
      qualificationData: [], // 资质信息
      businessCategory: [], // 经营类目
      allData: {}, // 企业信息
      statementIs: false,
      isEdit: false,
      isDetail: true,
      dialogVisible: false,
      historyList: [],
      sendAllData: {},
      qualIdStr: '',
      qualStateStr: null,
      isCheck: true,
      isBtnShow: true,
      changeShow: true,
      rolesShow: false,
      ifModify: false,
    };
  },
  computed: { ...mapState('app', ['qualtificationInfos','shopConfig']) },
  created() {
    this.initFunction();
  },
  mounted() {
    this.setHeight();
  },
  methods: {
    initFunction() {
      getHostName().then((res) => {
        if (res.hostName) {
          this.hostName = res.hostName;
        }
      });
      // 获取草稿信息
      getCorporationDraft().then((res) => {
        if (res.code === 0) {
          if (res.data) {
            this.corporationType = res.data.corporationType;
            this.qualIdStr = res.data.id;
            this.qualStateStr = res.data.state;
            this.allData = { ...res.data };
            this.$refs.basic.setData(this.allData);
            if (res.data.state === 2) {
              this.stateText = '待审核';
              this.stateTip = '已提交资质认证，请耐心等待审核结果';
              this.stateClass = 'color1';
              this.isDetail = false;
            } else if (res.data.state === 3) {
              this.stateText = '待付款';
              this.stateTip = '您的资质已通过审核，请缴纳商户保证金并上传付款证明<a href="/paymentProve/edit">去上传</a>';
              this.stateClass = 'color1';
              this.isDetail = false;
            } else if (res.data.state === 4) {
              this.stateText = '付款审核未通过';
              this.stateTip = '请在“付款证明”页面查看未通过原因';
              this.stateClass = 'color1';
              this.isDetail = false;
            } else if (res.data.state === 5) {
              this.stateText = '已认证';
              this.stateTip = '您的资质已通过审核，欢迎开店。如需更改，请点击“修改信息”按钮';
              this.stateClass = 'color2';
              this.isDetail = false;
            } else if (res.data.state === 6) {
              this.stateText = '认证驳回';
              this.stateTip = '需核实和修改信息：<span style="color: #4183D5">{{res.rejectTips}}</span>请修改后重新提交认证申请';
              this.stateClass = '';
              this.isDetail = true;
            }
            this.getBus();
            this.getQual();
          } else {
            this.getCom();
          }
        } else {
          this.getCom();
        }
      });
    },
    closeUpdateBankDialog() {
      this.bankVisible = false;
    },
    updateBank() {
      getUpdateBankInfo().then(res => {
        if (res && res.code === 0) {
          this.bankVisible = true;
          this.bankForm = res.data || {};
        } else {
          this.$message.error(res.message)
        }
      })
    },
    handleUpdateBankConfirm() {
        console.log(this.sendAllData, '??send')
      this.$refs.bankRef.validate((valid, rule) => {
        if (valid) {
          if (!this.checkBankVal(this.bankForm)) {
            this.$message.warning({
              message: "“对公账户名称”需填写企业名称，请修改后重新提交",
              type: 'warning',
              offset: '60',
            });
            return;
          };
          let qualificationDetailId = null;
          const bsData = this.companyQualData?.details.filter(item => item.name === "企业对公账户信息");
          if (bsData) {
            qualificationDetailId = bsData[0].id;
          }
          let params = this.bankForm;
          params.qualificationDetailId = qualificationDetailId;
          updateBankInfo(params).then(res => {
            if (res && res.code === 0) {
              this.$message.success("修改成功")
              this.initFunction();
              this.bankVisible = false;
            } else {
              this.$message.error(res.message||'修改失败')
            }
          })
        }
      });
    },
    checkBankVal(data) {
      if (data) {
        // basic.companyName
        const name = data.legalPersonName.substring(0, 5);
        if (this.allData.companyName && (name == this.allData.companyName.substring(0, 5))) {
          return true;
        } else {
          return false;
        }
      }
    },
    handleCheckServiceAgreement(type) {
      if (type == 1) {
        window.open('https://oss-ec.ybm100.com/pop/temp/ybmPlatformAgreement.pdf');
      } else if (type == 2) {
        window.open("https://my.orangebank.com.cn/orgLogin/hd/act/jianzb/B2BClearing.html?name=" + this.$refs.basic.basic.companyName);
      }
      // location.href = 'http://oss-ec.ybm100.com/pop/%E5%B9%B3%E5%8F%B0%E6%9C%8D%E5%8A%A1%E5%8D%8F%E8%AE%AE.pdf';
    },
    lookHistory(val) {
      this.dialogVisible = false;
      this.$router.push({ path: '/qualtification/history', query: { batch: val } });
    },
    getCheck() {
      getCheckCorporation().then((res) => {
        if (res.code === 0) {
          this.isCheck = !res.data;
          if (res.data) {
            this.stateTip = '当前有一条待审核的<span @click="getHistory" style="color: #4183D5;cursor: pointer">企业信息修改记录</span>，请等待审核结果';
          }
        }
      });
    },
    getCom() {
      getCompanyInfo().then((res) => {
        if (res.code === 0) {
          if (res.data) {
            this.corporationType = res.data.corporationType;
            this.qualIdStr = res.data.id;
            this.qualStateStr = res.data.state;
            this.allData = { ...res.data };
            console.log(*********, this.allData);
            this.$refs.basic.setData(this.allData);
            if (res.data.state === 2) {
              this.stateText = '待审核';
              this.stateTip = '已提交资质认证，请耐心等待审核结果';
              this.stateClass = 'color1';
              this.isDetail = false;
            } else if (res.data.state === 3) {
              this.stateText = '待付款';
              this.stateTip = '您的资质已通过审核，请缴纳商户保证金并上传付款证明<a href="/paymentProve/edit">去上传</a>';
              this.stateClass = 'color1';
              this.isDetail = false;
            } else if (res.data.state === 4) {
              this.stateText = '付款审核未通过';
              this.stateTip = '请在“付款证明”页面查看未通过原因';
              this.stateClass = 'color1';
              this.isDetail = false;
            } else if (res.data.state === 5) {
              this.stateText = '已认证';
              this.stateTip = '您的资质已通过审核，欢迎开店。如需更改，请点击“修改信息”按钮';
              this.stateClass = 'color2';
              this.isDetail = false;
              this.getCheck();
            } else if (res.data.state === 6) {
              this.stateText = '认证驳回';
              const aryTip = res.data.rejectTips ? res.data.rejectTips.split(',') : [];
              this.stateTip = `需核实和修改信息：<a style="color: #4183D5" href="javascript:;" title="${res.data.rejectTips}">${aryTip.length}条 </a> 请修改后重新提交认证申请`;
              this.stateClass = '';
              this.isDetail = true;
            }
          }
          this.getBus();
          this.getQual();
        }
      });
    },
    setHeight() {
      // const mains = $(window.parent.document).find('#iframeId')
      // const thisheights = $(document).height() + 200
      // mains.height(thisheights)
      // mains.css('height', thisheights)
      // mains.css('height', `${thisheights}px`)
      // $(window.parent.document)
      //   .find('#iframeId')
      //   .load(function () {
      //     const main = $(window.parent.document).find('#iframeId')
      //     console.log(main, 'main')
      //     const thisheight = $(document).height() + 30
      //     console.log($(document).height(), '$(document).height()')
      //     console.log(thisheight, 'thisheightthisheight')
      //     main.height(thisheight)
      //     main.css('height', thisheight)
      //     main.css('height', `${thisheight}px`)
      //   })
    },
    getHistory() {
      getHistoryList().then((res) => {
        if (res.code === 0) {
          this.historyList = res.data;
          this.dialogVisible = true;
        } else {
          this.historyList = [];
          this.$message.warning(res.message);
        }
      });
    },
    editData() {
      // ['currentTab', 'roles', 'rolesInfo', 'excludeList'])
      this.isEdit = true;
      this.isDetail = true;
      this.isBtnShow = false;
    },
    editStoreInfo(flag) {
      if (flag) {
        this.isEdit = false;
        this.isBtnShow = true;
        this.ifModify = false;
      } else {
        this.isEdit = true;
        this.isBtnShow = false;
        this.ifModify = true;
      }
    },
    changeType(val) {
      this.changeShow = false;
      this.corporationType = val;
      this.allData = {};
      this.getQual();
      this.getBus();
    },
    getBus() {
      getBusinessCategory({ corporationType: this.corporationType }).then((res) => {
        if (res.code === 0) {
          this.businessCategory = res.data;
          this.changeShow = true;
          this.setHeight();
        }
      });
    },
    getQual() {
      getQualificationInfo({ corporationType: this.corporationType }).then((res) => {
        if (res.code === 0) {
          this.qualificationData = res.data.engageInQualifications
            ? res.data.engageInQualifications
            : [];
          this.companyQualData = res.data.commonQualifications ? res.data.commonQualifications : [];
          this.changeShow = true;
          this.setHeight();
        }
      });
    },
    formBasic(basic, f) {
      basic ? (this.sendAllData = { ...basic }) : '';
      if (!f) {
        localStorage.setItem("basicName", basic.companyName);
        basic ? this.$refs.cor.submitForm() : '';
      }
    },
    formCor(cor, f) {
      if (this.qualStateStr !== 5) {
        this.sendAllData.corporationQualifications = [];
        cor ? (this.sendAllData.corporationQualifications = cor) : '';
      } else {
        this.sendAllData.checkCorporationQualifications = [];
        cor ? (this.sendAllData.checkCorporationQualifications = cor) : '';
      }
      if (!f) {
        cor ? this.$refs.qual.submitForm() : '';
      }
    },
    formQual(qual, f) {
      if (this.qualStateStr !== 5) {
        qual
          ? (this.sendAllData.corporationQualifications = this.sendAllData.corporationQualifications.concat(
            qual.sendData,
          ))
          : '';
      } else {
        qual
          ? (this.sendAllData.checkCorporationQualifications = this.sendAllData.checkCorporationQualifications.concat(
            qual.sendData,
          ))
          : '';
        delete this.sendAllData.corporationQualifications;
      }
      if (this.qualStateStr !== 5) {
        this.sendAllData.corporationBusiness = [];
        qual ? (this.sendAllData.corporationBusiness = qual.corporationBusiness) : '';
      } else {
        this.sendAllData.checkCorporationBusiness = [];
        qual ? (this.sendAllData.checkCorporationBusiness = qual.corporationBusiness) : '';
        delete this.sendAllData.corporationBusiness;
      }

      if (!f) {
        qual ? this.submitAll() : '';
      }
    },
    submitData(f) {
      if (f === 1) {
        this.$refs.basic.submitForm(1);
        this.$refs.cor.submitForm(1);
        this.$refs.qual.submitForm(1);
        this.submitAll(1);
      } else {
        this.$refs.basic.submitForm();
      }
    },
    submitAll(f) {
      if (!f) {
        if (this.statementIs) {
          if (
            this.sendAllData.corporationBusiness
            && this.sendAllData.corporationBusiness.length < 1
            && this.qualStateStr !== 5
          ) {
            this.$message.warning('请最少勾选一项经营范围');
          } else if (
            this.qualStateStr !== 5
            && this.sendAllData.corporationBusiness
            && this.sendAllData.corporationBusiness.length > 0
          ) {
            const loading = this.$loading({
              lock: true,
              text: 'Loading',
              spinner: 'el-icon-loading',
              background: 'rgba(0, 0, 0, 0.7)',
            });
            if (this.qualIdStr) {
              submitModifyCorporation(this.sendAllData).then((res) => {
                loading.close();
                if (res.code === 0) {
                  this.$message.success('提交成功');
                  // window.location.reload();
                  window.refreshPage('/qualtification');
                  this.getCom();
                } else {
                  this.$message.error(res.message);
                }
              });
            } else {
              submitAddCorporation(this.sendAllData).then((res) => {
                loading.close();
                if (res.code === 0) {
                  this.$message.success('提交成功');
                  // window.location.reload();
                  window.refreshPage('/qualtification');
                  this.getCom();
                } else {
                  this.$message.error(res.message);
                }
              });
            }
          } else if (
            this.sendAllData.checkCorporationBusiness
            && this.sendAllData.checkCorporationBusiness.length < 1
            && this.qualStateStr === 5
          ) {
            this.$message.warning('请最少勾选一项经营范围');
          } else if (
            this.qualStateStr === 5
            && this.sendAllData.checkCorporationBusiness
            && this.sendAllData.checkCorporationBusiness.length > 0
          ) {
            const loading = this.$loading({
              lock: true,
              text: 'Loading',
              spinner: 'el-icon-loading',
              background: 'rgba(0, 0, 0, 0.7)',
            });
            if (this.ifModify) {
              const { logoUrl, customerServicePhone, fixedPhone, email, web, brief } = this.sendAllData
              const params = { logoUrl, customerServicePhone, fixedPhone, email, web, brief }
              updateCorporationInfoWithoutCheck(params).then(res => {
                loading.close()
                if (res && res.code === 0) {
                  this.$message.success('修改成功')
                  this.editStoreInfo(true)
                  window.refreshPage('/qualtification')
                } else {
                  this.$message.error(res.message||'修改失败')
                }
              })
            } else {
              submitAddCheckCorporation(this.sendAllData).then((res) => {
                loading.close()
                if (res.code === 0) {
                  this.$message.success('提交成功');
                  // window.location.reload();
                  window.refreshPage('/qualtification')
                  this.getCom();
                } else {
                  this.$message.error(res.message)
                }
              })
            }
          }
        } else {
          this.$message.warning('请仔细阅读声明并同意');
        }
      } else {
        const loading = this.$loading({
          lock: true,
          text: 'Loading',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)',
        });
        saveCorporationDraft(this.sendAllData).then((res) => {
          loading.close();
          if (res.code === 0) {
            this.$message.success('保存成功');
            // window.location.reload()
          } else {
            this.$message.error(res.message);
          }
        });
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.serch {
  padding: 20px;
  font-weight: bold;
  .sign {
    display: inline-table;
    width: 3px;
    height: 13px;
    background: linear-gradient(360deg, #1d69c4 1%, #8bbdfc 99%);
    border-radius: 2px;
    margin-right: 8px;
  }
}
.his-dialog ::v-deep  .el-dialog__body {
  padding: 0 20px 20px 20px;
  .div-remarks {
    padding: 5px;
    margin: 10px 0;
    border: 1px dashed #999999;
  }
}
.qual-state {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  font-size: 16px;
  color: #f5222d;
  height: 50px;
  background: #fffbf1;
  .color1 {
    color: #ff9800;
  }
  .color2 {
    color: #52c41a;
  }
  .color4 {
    color: #4183d5;
    .line {
      display: inline-block;
      width: 1px;
      height: 16px;
      background: #d8d8d8;
      vertical-align: middle;
    }
    .li-img {
      width: 16px;
      height: 16px;
      vertical-align: middle;
      margin: 0 1px 0 5px;
    }
    span {
      cursor: pointer;
      margin-left: 5px;
    }
  }
  .h {
    color: #333333;
    padding-right: 8px;
  }
  .t {
    font-size: 12px;
    color: #666666;
    padding-left: 8px;
  }
}
.avatar-uploader ::v-deep  .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  background: rgba(0, 0, 0, 0.02);
  width: 126px;
  height: 126px;
  line-height: 126px;
}
.avatar-uploader ::v-deep  .el-upload:hover {
  border-color: #409eff;
}
.avatar-uploader ::v-deep  .el-upload__tip {
  margin-top: 0;
  color: #999999;
  font-size: 12px;
}
.avatar-uploader-icon {
  font-size: 28px;
  color: #666666;
  width: 126px;
  height: 126px;
  line-height: 126px;
  text-align: center;
}
.avatar {
  width: 126px;
  height: 126px;
  display: block;
}
.avatar-uploader ::v-deep  .el-upload-list--picture-card .el-upload-list__item {
  width: 126px;
  height: 126px;
}
.footer {
  padding: 20px 0;
  border-top: 1px solid #efefef;
  .statement-box {
    width: 70%;
    margin: 0 auto;
    display: flex;
    justify-content: center;
    text-align: center;
  }
  .bot-box {
    width: 80%;
    margin: 0 auto;
    padding-top: 20px;
    text-align: center;
    button {
      padding: 8px 30px;
    }
    .el-button--primary {
      background: #4183d5;
      border-color: #4183d5;
    }
  }
}
.dis-div span {
  display: inline-block;
  width: 25%;
  text-align: center;
}
</style>
<style lang="scss">
.qual-box {
  .bankLabel {
    display: flex;
    .el-form-item__label {
      width: 150px !important;
      white-space: nowrap;
    }
    .el-form-item__content {
      flex: 1;
      margin-left: 0 !important;
    }
  }
}
</style>
