<template>
  <div class="error-edit-list">
    <div class="topBox">
      <span>纠错列表</span>
    </div>
    <SearchForm
      ref="searchForm"
      :model="formModel"
      :form-items="formItems"
      @submit="handleFormSubmit"
      @reset="handleFormReset"
      :hasOpenBtn="false"
    >
    </SearchForm>
    <div class="operation">
      <el-button size="small" type="primary" @click="fuckTime()"
        >导出</el-button
      >
    </div>
    <el-table
      border
      height="400"
      stripe
      :data="tableConfig.data"
      :header-cell-style="{ background: '#f9f9f9' }"
      v-loading="tableLoading"
    >
      <el-table-column prop="goodsDetail" label="商品信息">
        <template slot-scope="scope">
          <div>
            {{ scope.row.barcode }}<br />{{ scope.row.skuProductName }}<br />
            {{ scope.row.skuSpec }}<br />{{ scope.row.skuManufacturer }}<br />{{
              scope.row.skuApprovalNumber
            }}
          </div>
        </template>
      </el-table-column>
      <el-table-column
        prop="applicationTime"
        label="申请时间"
        :formatter="formatDate"
      >
      </el-table-column>
      <el-table-column prop="applicationReason" label="申请理由">
      </el-table-column>
      <el-table-column prop="voucherImage" label="凭证" width="260px">
        <template slot-scope="{ row }">
          <div class="voucherImage-row-cell">
            <img
              v-for="(item, index) in getUrlImage(row.voucherImage)"
              :key="item"
              style="
                width: 60px;
                height: 60x;
                margin-left: 10px;
                cursor: pointer;
              "
              :src="item"
              alt=""
              @click="showImages(item)"
            />
            <!-- <el-image
              v-for="(item, index) in getUrlImage(row.voucherImage)"
              :key="item"
              style="width: 60px; height: 60x; margin-left: 10px"
              :src="item"
              :preview-src-list="srcList"
            > -->
            <!-- </el-image> -->
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="auditStatus" label="纠错状态">
        <template slot-scope="{ row }">
          <div>
            <div v-if="row.auditStatus === 0" style="color: burlywood">
              待审核
            </div>
            <div v-else-if="row.auditStatus === 1" style="color: green">
              审核通过，信息已更新
            </div>
            <div v-else-if="row.auditStatus === 2" style="color: red">
              审核不通过
            </div>
            <div v-else>-</div>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="auditReason" label="驳回原因"> </el-table-column>
      <el-table-column prop="operation" label="操作">
        <template slot-scope="{ row }">
          <div class="edit-row-cell">
            <el-button type="text" @click="jumpAgain(row, 'detail')"
              >查看详情</el-button
            >
            <el-button
              type="text"
              v-if="row.auditStatus === 2"
              @click="jumpAgain(row, 'edit')"
              >再次申请</el-button
            >
            <el-button type="text" @click="jumpGoodsList(row)"
              >查看商品信息</el-button
            >
          </div>
        </template>
      </el-table-column>
    </el-table>
    <div v-if="listQuery.total !== 0" class="pagination-container">
      <div class="pag-text">
        共 {{ listQuery.total }} 条数据，每页{{ listQuery.pageSize }}条，共{{
          Math.ceil(listQuery.total / listQuery.pageSize)
        }}页
      </div>
      <el-pagination
        background
        :page-sizes="[10, 20, 30, 40]"
        prev-text="上一页"
        next-text="下一页"
        layout="sizes, prev, pager, next, jumper"
        :total="listQuery.total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
    <el-image-viewer
      v-if="showViewer"
      :url-list="images"
      :on-close="closeViewer"
    />
  </div>
</template>

<script>
import ElImageViewer from 'element-ui/packages/image/src/image-viewer'
import { correctionPage, apiConfig, correctionExport } from '@/api/product'
import xyyTable from '@/components/table'
import SearchForm from '@/components/searchForm'
import moment from 'moment'
export default {
  components: { SearchForm, xyyTable, ElImageViewer },
  data() {
    return {
      
      images: [],
      showViewer: false,
      formItems: this.getFormItems(),
      formModel: {
        barcode: '',
        productName: '',
        spec: '',
        manufacturer: '',
        approvalNumber: '',
        auditStatus: ''
      },
      listQuery: {
        pageSize: 10,
        pageNum: 1,
        total: 0
      },
      tableLoading: false,
      tableConfig: {
        data: []
      },
      bigImgUrlPrefix: '',
      srcList: []
    }
  },
  created() {
    moment.locale('zh-cn')
    apiConfig().then((res) => {
      if (res.data) {
        this.bigImgUrlPrefix = res.data.bigImgUrlPrefix // 商品大图地址前缀
        this.queryList()
      }
    })
  },
  methods: {
    jumpGoodsList(row) {
      const path = '/productList';
      const obj = { barcode: row.barcode, from: 'eel' }
      window.openTab(path, obj)
    },
    
    jumpAgain(row, type) {
      const path = '/product/errorEdit'
      const obj = {
        from: 'errorEditList',
        verify: true,
        barCode: row.barcode,
        id: row.id,
        type: type
      }
      window.openTab(path, obj)
    },
    showImages(images) {
      this.images = [images]
      this.showViewer = true
    },
    closeViewer() {
      this.showViewer = false
    },
    getUrlImage(imgRow) {
      if(!imgRow){
        return []
      }
      let arr = imgRow.split(',')
      arr = arr.filter((item) => {
        return item
      })
      arr = arr.map((item) => {
        return `${this.bigImgUrlPrefix}${item}`
      })
      this.srcList = arr
      return arr
    },
    formatDate(row, column, cellValue) {
      const time = moment(cellValue).format('YYYY-MM-DD HH:mm:ss')
      return time
    },
    chekData(scope) {
      console.log(scope)
    },
    handleFormSubmit() {
      this.queryList()
    },
    handleFormReset(obj) {
      this.formModel = obj
      this.listQuery = {
        pageNum: 1,
        pageSize: 10,
        total: 0
      }
      setTimeout(() => {
        this.queryList()
      }, 500)
    },

    handleSizeChange(size) {
      this.listQuery.pageSize = size
      this.queryList()
    },

    handleCurrentChange(val) {
      this.listQuery.pageNum = val
      this.queryList()
    },

    async queryList() {
      const that = this
      this.tableLoading = true

      let params = Object.assign({}, this.formModel)
      const { pageSize, pageNum } = this.listQuery
      params.pageNum = pageNum
      params.pageSize = pageSize
      const res = await correctionPage(params)
      that.tableLoading = false
      if (res && res.code === 0) {
        this.tableConfig.data = []
        this.tableConfig.data = res.data.list

        this.listQuery.total = res.data.total
        this.listQuery.pageSize = res.data.pageSize
      }
    },
    apiCorrectionExport() {
      let params = Object.assign({}, this.formModel)
      correctionExport(params).then((res) => {
        this.util.exportExcel(res, '导出文件.xls')
      })
    },
    getParams(params) {
      let queryStr = '?'
      Object.keys(params).forEach((key) => {
        queryStr += `${key}=${params[key]}&`
      })
      queryStr = queryStr.substr(0, queryStr.length - 1)
      return queryStr
    },
    getFormItems() {
      return [
        {
          label: '商品编码',
          prop: 'barcode',
          component: 'el-input',
          attrs: {
            placeholder: '请输入'
          }
        },
        {
          label: '商品名称',
          prop: 'productName',
          component: 'el-input',
          attrs: {
            placeholder: '请输入'
          }
        },
        {
          label: '商品规格',
          prop: 'spec',
          component: 'el-input',
          colSpan: 6,
          attrs: {
            placeholder: '请输入'
          }
        },
        {
          label: '生产厂家',
          prop: 'manufacturer',
          component: 'el-input',
          attrs: {
            placeholder: '请输入'
          }
        },
        {
          label: '批准文号',
          prop: 'approvalNumber',
          component: 'el-input',
          attrs: {
            placeholder: '请输入'
          }
        },
        {
          label: '纠错状态',
          prop: 'auditStatus',
          component: 'el-select',
          attrs: {
            placeholder: '请输入',
            options: [
              { label: '全部', value: '' },
              { label: '待审核', value: '0' },
              { label: '审核通过', value: '1' },
              { label: '审核不通过', value: '2' }
            ]
          }
        }
      ]
    }
  }
}
</script>

<style lang="scss" scoped>
.error-edit-list {
  padding: 0px 20px;
}
.topBox {
  padding: 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  span {
    font-size: 20px;
    font-weight: 500;
    text-align: left;
    color: #333333;
  }
  span:before {
    content: '';
    display: inline-block;
    width: 3px;
    height: 20px;
    background: linear-gradient(360deg, #1d69c4 1%, #8bbdfc 99%);
    border-radius: 2px;
    margin-right: 8px;
    vertical-align: middle;
  }
}
.operation {
  margin-top: 20px;
  margin-bottom: 20px;
  // color:burlywood
}

.edit-row-cell {
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.voucherImage-row-cell {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
}

::v-deep   .el-table {
  tr {
    td {
      padding: 0;
      border: none;
      background: #ffffff;
      border-bottom: 1px solid #e0e0e0;
      border-right: 1px solid #e0e0e0;
      text-align: center;
      .cell {
        overflow-x: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        text-align: center;
      }
    }
  }
}
.pagination-container {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  margin-top: 10px;
}
</style>