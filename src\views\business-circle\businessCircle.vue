<template>
  <div class="wrap">
    <div class="Fsearch">
      <el-row type="flex" align="middle" justify="space-between" class="my-row">
        <el-row type="flex" align="middle">
          <span class="sign" />
          <div class="searchMsg">查询条件</div>
        </el-row>
      </el-row>
    </div>
    <el-row
      :gutter="20"
      style="padding: 0 20px; margin-left: 10px; margin-right: 10px"
      class="searchMy"
    >
      <el-form ref="ruleForm" :inline="true" size="small">
        <el-form-item>
          <el-input v-model="ruleForm.id" placeholder="请输入" size="small">
            <template slot="prepend"> 业务商圈<Input:datetime-local></Input:datetime-local> </template>
          </el-input>
        </el-form-item>
        <el-form-item>
          <el-input v-model="ruleForm.busAreaName" placeholder="请输入" size="small">
            <template slot="prepend"> 业务商圈名称 </template>
          </el-input>
        </el-form-item>
        <el-form-item prop="businessSource">
          <span class="search-title"> 商圈来源 </span>
          <el-select v-model="ruleForm.source" size="small" placeholder="全部">
            <el-option
              v-for="item in businessSource"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item prop="branchCode">
          <span class="search-title">商圈类型</span>
          <el-select v-model="ruleForm.type" size="small" placeholder="全部">
            <el-option
              v-for="item in businessType"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item prop="templateType">
          <span class="search-title">状态</span>
          <el-select v-model="ruleForm.status" size="small" placeholder="全部">
            <el-option
              v-for="item in state"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
      </el-form>

      <el-row>
        <el-button
          type="primary"
          size="small"
          style="float: right; margin-right: 15px; margin-top: -5px; margin-bottom: 5px"
          @click="search()"
        >
          查询
        </el-button>
        <el-button
          size="small"
          style="float: right; margin-right: 15px; margin-top: -5px; margin-bottom: 5px"
          @click="resetForm()"
        >
          重置
        </el-button>
      </el-row>
      <el-divider />
    </el-row>

    <el-row class="freightList">
      <div class="Fsearch">
        <el-row type="flex" align="middle" justify="space-between">
          <el-row type="flex" align="middle">
            <span class="sign" />
            <div class="searchMsg">商圈基本信息</div>
          </el-row>
          <div class="FsearchBtn">
            <el-button v-permission="['shop_businessArea_add']" type="primary" size="small" @click="createCircle"> 新增业务商圈 </el-button>
            <el-button v-permission="['shop_businessArea_batchDelete']" type="primary" size="small" @click="batchDelete('1')"> 批量删除 </el-button>
          </div>
        </el-row>
      </div>

      <el-table
        v-loading="isLoading"
        :data="tableData"
        row-key="id"
        stripe
        show-overflow-tooltip
        border
        fit
        style="width: 100%"
        highlight-current-row
        :header-cell-style="{ background: '#F9F9F9', color: '#666666' }"
        @selection-change="handleSelectionChange"
      >
        <el-table-column
          type="selection"
          align="center"
          width="55">
        </el-table-column>
        <el-table-column prop="id" label="业务商圈ID" width="120" />
        <el-table-column width="150" label="业务商圈名称" prop="busAreaName">
          <template slot-scope="scope">
            <div class="typeEnter">{{ scope.row.busAreaName }}</div>
          </template></el-table-column
        >
        <el-table-column width="110" prop="busAreaDesc" label="商圈描述" v-if="descriptionfalse">
        </el-table-column>
        <el-table-column prop="typeName" label="商圈类型"> </el-table-column>
        <el-table-column prop="type" label="商圈类型名" v-if="show"> </el-table-column>
        <el-table-column prop="sourceName" label="商圈来源" />
        <el-table-column prop="source" v-if="show" label="商圈来源" />
        <el-table-column prop="status" v-if="show" label="状态" />
        <el-table-column prop="statusName" label="状态" />
        <el-table-column prop="updateUser" label="操作人" />
        <el-table-column prop="updateTime" label="操作时间" width="165" :formatter="formatDate">
        </el-table-column>
        <el-table-column fixed="right" width="170" label="操作">
          <template slot-scope="scope">
            <div>
              <el-button type="text" size="small" @click="viewProduct(scope.row)">
                查看商品
              </el-button>
              <el-button type="text" size="small" @click="viewBusiness(scope.row)">
                查看业务商圈
              </el-button>
              <el-button
                type="text"
                size="small"
                @click="viewDialogRow = scope.row;changeRecordsVis = true;"
              >
                查看变更记录
              </el-button>
            </div>
            <el-button
              v-permission="['shop_businessArea_edit']"
              type="text"
              size="small"
              v-if="scope.row.source === 2"
              @click="getCircleDetail(scope.row)"
            >
              编辑
            </el-button>
            <el-button
              v-permission="['shop_businessArea_enable']"
              type="text"
              size="small"
              v-if="scope.row.status === 0 && scope.row.source === 2"
              @click="enableClick(scope.row)"
              >启用
            </el-button>
            <el-button
              v-permission="['shop_businessArea_disable']"
              type="text"
              size="small"
              v-if="scope.row.status === 1 && scope.row.source === 2"
              @click="disableClick(scope.row)"
            >
              禁用
            </el-button>
            <el-button
              v-permission="['shop_businessArea_delete']"
              type="text"
              size="small"
              v-if="scope.row.source === 2"
              @click="batchDelete('2',scope.row.id)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <div v-if="tablePage.total != 0" class="pagination-container">
        <div class="pag-text">
          共 {{ tablePage.total }} 条数据，每页{{ tablePage.pageSize }}条，共{{
            Math.ceil(tablePage.total / tablePage.pageSize)
          }}页
        </div>
        <el-pagination
          background
          :page-sizes="pageSizes"
          :page-size="tablePage.pageSize"
          :current-page="tablePage.pageNum"
          layout="sizes, prev, pager, next, jumper"
          :total="tablePage.total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-row>
    <!-- 查看商品 -->
    <view-product :row="viewDialogRow" v-if="viewDialog" v-model="viewDialog"></view-product>
    <!-- 新增、编辑业务商圈 -->
    <new-business-district
      :row="selectRow"
      v-if="districtDialog"
      @onDialogChange="onDialogChange"
      v-model="districtDialog"
    />
    <!-- 查看业务商圈 -->
    <business-circle-detail-dialog
      :row="selectViewRow"
      v-if="viewBusinessDialog"
      v-model="viewBusinessDialog"
    ></business-circle-detail-dialog>

    <viewChangeRecords
      v-if="changeRecordsVis"
      v-model="changeRecordsVis"
      :row="viewDialogRow"
    />
  </div>
</template>

<script>
import {
  getPageQuery,
  getUpdateStatus,
  getProductCount,
  getQueryBusAreaConifigById,
  batchDelete,
} from '@/api/businessCircle';
// 查看商品
import viewProduct from './components/viewProduct';
// 新增业务商圈
import newBusinessDistrict from './components/newBusinessDistrict';
// 查看业务商圈
import BusinessCircleDetailDialog from './components/businessCircleDetailDialog.vue';
// 查看变更记录
import viewChangeRecords from './components/viewChangeRecords';

export default {
  name: 'BusinessCircle',
  components: { viewProduct, newBusinessDistrict, BusinessCircleDetailDialog, viewChangeRecords },
  data() {
    return {
      changeRecordsVis: false, // 查看变更记录弹窗
      viewDialog: false, // 查看商品弹窗
      districtDialog: false, // 新增业务商圈
      viewBusinessDialog: false, // 查看业务商圈
      isLoading: false,
      ruleForm: {
        id: '', // 业务商圈id
        busAreaName: '', // 业务商圈名称
        source: '', // 商圈来源
        type: '', // 商圈类型
        status: '', // 状态
      },
      show: false, // 禁用列
      descriptionfalse: false,
      // 商圈来源下拉框
      businessSource: [
        {
          value: '',
          label: '全部',
        },
        {
          value: '1',
          label: '系统默认',
        },
        {
          value: '2',
          label: '手工创建',
        },
        {
          value: '3',
          label: '活动创建',
        },
      ],
      // 商圈类型下拉框
      businessType: [
        {
          value: '',
          label: '全部',
        },
        {
          value: '1',
          label: '药品',
        },
        {
          value: '2',
          label: '非药',
        },
        {
          value: '3',
          label: '药品和非药',
        },
      ],
      // 状态下拉框
      state: [
        {
          value: '',
          label: '全部',
        },
        {
          value: '1',
          label: '启用',
        },
        {
          value: '0',
          label: '禁用',
        },
      ],
      pageSizes: [10, 20, 30, 40],
      tablePage: {
        // 分页
        pageNum: 1,
        pageSize: 10,
        total: 1,
      },
      tableData: [],
      selectRow: undefined,
      selectViewRow: undefined,
      viewDialogRow: undefined,
      selectedList: []
    }
  },
  created() {
    this.search(); // 查询商圈
  },
  methods: {
    // 查询当前选中行详情
    getCircleDetail(row) {
      const parmas = {
        id: row.id,
      };
      getQueryBusAreaConifigById(parmas).then((res) => {
        const { code, data } = res;
        if (code === 0) {
          this.goodsQuantity(row, data);
        }
      });
    },
    // 查询商圈
    search() {
      const params = {};
      this.isLoading = true;
      Object.assign(params, this.ruleForm, this.tablePage);
      getPageQuery(params)
        .then((res) => {
          const { code, result } = res;
          if (code === 0) {
            this.isLoading = false;
            this.tableData = result.list;
            this.tablePage.total = result.total; // 总数据数量
            this.tablePage.pageNum = result.pageNum;
          }
        })
        .catch(() => {});
    },
    createCircle() {
      this.selectRow = undefined;
      this.districtDialog = true;
    },
    onDialogChange() {
      // 刷新页面
      this.search();
    },
    goodsQuantity(row, detailData) {
      if (!detailData) {
        return;
      }
      // 是否绑定商品
      getProductCount({ busAreaId: row.id })
        .then((res) => {
          if (res.code === 0) {
            if (res.data === 0) {
              this.selectRow = detailData
              this.districtDialog = true
            } else {
              const h = this.$createElement
              this.$confirm('提示', {
                title: '提示',
                message: h('div', [
                  h(
                    'p',
                    { style: 'font-size:16px;font-weight:bold' },
                    '该业务商圈已绑定商品,确定继续编辑吗？'
                  ),
                  h(
                    'div',
                    { style: 'margin-top: 10px;font-size:15px;' },
                    `若继续编辑则已绑定该商圈的${res.data}个商品将使用编辑后的商圈`
                  )
                ]),
                confirmButtonText: '确定',
                cancelButtonText: '取消'
              })
                .then(() => {
                  this.selectRow = detailData
                  this.districtDialog = true
                })
                .catch(() => {
                  this.$message({
                    type: 'info',
                    message: '已取消编辑'
                  })
                })
            }
          }
        })
        .catch(() => {})
    },
    // 查看业务商圈
    viewBusiness(row) {
      this.selectViewRow = row;
      this.viewBusinessDialog = true;
      // this.$router.push({
      //   name: 'businessCircleDetail',
      //   query: {
      //     detailId: row.id
      //   }
      // })
    },
    // 查看商品
    viewProduct(row) {
      this.viewDialogRow = row
      this.viewDialog = true
    },
    // 时间格式化
    formatDate(row, column, cellValue) {
      const date = new Date(cellValue)
      const y = date.getFullYear()
      let MM = date.getMonth() + 1
      MM = MM < 10 ? `0${MM}` : MM
      let d = date.getDate()
      d = d < 10 ? `0${d}` : d
      let h = date.getHours()
      h = h < 10 ? `0${h}` : h
      let m = date.getMinutes()
      m = m < 10 ? `0${m}` : m
      let s = date.getSeconds()
      s = s < 10 ? `0${s}` : s
      return `${y}-${MM}-${d} ${h}:${m}:${s}`;
    },
    handleSizeChange(val) {
      this.tablePage.pageSize = val;
      this.search();
    },
    handleCurrentChange(val) {
      this.tablePage.pageNum = val;
      this.search();
    },
    // 重置条件
    resetForm() {
      this.ruleForm.busAreaName = '';
      this.ruleForm.source = '';
      this.ruleForm.status = '';
      this.ruleForm.type = '';
      this.ruleForm.id = '';
      this.search();
    },
    // 启用变禁用
    disableClick(row) {
      // 点击禁用校验是否已绑定商品，若绑定商品弹窗提示  查询商圈绑定商品数量
      // “该业务商圈已绑定商品，若禁用则已绑定该商圈的**个商品会默认企业经营区域，确定是否禁用”，
      // 点击确定则禁用成功，取消则关闭弹窗；
      // 未绑定商品则弹窗提示“是否禁用该业务商圈”，确定则禁用，取消则关闭弹窗
      getProductCount({ busAreaId: row.id })
        .then((res) => {
          if (res.code === 0) {
            const params = {
              id: row.id,
              status: 0,
              type: row.type,
              orgId: row.orgId
            }
            if (res.data === 0) {
              this.$confirm('确定禁用该业务商圈吗？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消'
              })
                .then(() => {
                  // 点击启用按钮toast提示“业务商圈启用成功”；
                  getUpdateStatus(params)
                    .then((resp) => {
                      const { code } = resp
                      if (code === 0) {
                        this.search()
                        this.$message({
                          type: 'success',
                          message: '禁用成功'
                        })
                      }
                    })
                    .catch(() => {})
                })
                .catch(() => {
                  this.$message({
                    type: 'info',
                    message: '已取消禁用'
                  })
                })
            } else {
              const h = this.$createElement
              this.$confirm('提示', {
                title: '提示',
                message: h('div', [
                  h(
                    'p',
                    { style: 'font-size:16px;font-weight:bold' },
                    '该业务商圈已绑定商品，确定禁用吗？'
                  ),
                  h(
                    'p',
                    { style: 'margin-top: 10px;font-size:15px;' },
                    `若禁用则已绑定的${res.data}个商品会默认企业经营区域`
                  )
                ]),
                confirmButtonText: '确定',
                cancelButtonText: '取消'
              })
                .then(() => {
                  getUpdateStatus(params)
                    .then((resp) => {
                      const { code } = resp
                      if (code === 0) {
                        this.search()
                        this.$message({
                          type: 'success',
                          message: '禁用成功'
                        })
                      } else {
                        this.$message({
                          type: 'error',
                          message: '禁用失败'
                        })
                      }
                    })
                    .catch(() => {})
                })
                .catch(() => {
                  this.$message({
                    type: 'info',
                    message: '已取消禁用'
                  })
                })
            }
          }
        })
        .catch(() => {})
    },
    // 禁用状态变启用
    enableClick(row) {
      const params = {
        id: row.id,
        status: 1,
        type: row.type,
        orgId: row.orgId
      }
      getUpdateStatus(params)
        .then((res) => {
          const { code } = res
          if (code === 0) {
            this.$message({
              message: '业务商圈启用成功',
              type: 'success'
            })
            this.search()
          }
        })
        .catch(() => {})
    },
    handleSelectionChange(val){
      console.log(val)
      this.selectedList = val
    },
    batchDelete(type, id) {
      let params = [];
      if (type === '2') {
        //单个删除
        params = [id];
      } else {
        console.log(this.selectedList.length)
        if (!this.selectedList.length > 0) {
          this.$message.warning('请勾选需要删除的商圈');
          return false;
        }
        let flag = false;
        this.selectedList.forEach(item => {
          if (item.source !== 2) {
            flag = true;
          } else {
            params.push(item.id);
          }
        });
        if (flag) {
          this.$message.warning('系统生成的商圈无法删除，请重新勾选');
          return false;
        }
      }
      this.$confirm('确定删除此商圈吗？删除后此商圈内商品将使用系统默认药品/非药商圈。', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(async () => {
          const res = await batchDelete(params);
          console.log(res);
          if (res && res.code === 0) {
            this.$message.success('成功删除商圈');
            this.search()
          } else {
            this.$message.error(res.message || '删除失败');
          }
        });
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep  .el-form-item__label {
  margin-left: 20px;
  padding: 0;
}
::v-deep  .el-input__inner {
  border-radius: 0 4px 4px 0;
}
.search-title {
  display: table-cell;
  padding: 0 10px;
  text-align: center;
  border: 1px solid #dcdfe6;
  height: 30px;
  line-height: 30px;
  vertical-align: middle;
  border-right: none;
  border-radius: 4px 0 0 4px;
  color: #333333;
  white-space: nowrap;
}
.searchMy ::v-deep  .el-select {
  display: table-cell;
  width: 100%;
}
.searchMy ::v-deep  .el-form-item--small .el-form-item__content {
  line-height: 30px;
}
::v-deep  .el-input-group__prepend {
  background: none;
  color: #333333;
  padding: 0 10px;
}
.searchMy ::v-deep  .el-form-item__content{
  width: 100%;
}
.searchMy ::v-deep  .el-date-editor{
  width: 100%;
}

.Fsearch {
  padding: 20px;
  font-weight: 500;
  .searchMsg {
    font-weight: 700;
    width: 200px;
  }
  .sign {
    display: inline-table;
    width: 3px;
    height: 13px;
    background: linear-gradient(360deg, #1d69c4 1%, #8bbdfc 99%);
    border-radius: 2px;
    margin-right: 8px;
  }
}
.freightList {
  padding: 0 20px;
  .diaSWidth {
    width: 280px;
    margin-left: 20px;
    margin-right: 10px;
  }
  .Fradios {
    margin-left: 40px;
  }
  .footerBtn {
    padding: 20px;
    padding-top: 10px;
    text-align: right;
    box-sizing: border-box;
    padding-bottom: 0;
  }
  .Fsearch {
    padding: 20px 0;
    font-weight: 700;
    .Fradios {
      margin-left: 40px;
    }
    .diaSWidth {
      width: 280px;
      margin-left: 20px;
      margin-right: 10px;
    }
    .footerBtn {
      padding: 20px;
      padding-top: 10px;
      text-align: right;
      box-sizing: border-box;
      padding-bottom: 0;
    }
  }
  .pag-text {
    vertical-align: middle;
    float: left;
    font-size: 12px;
    color: #999999;
    padding-top: 8px;
  }
}
::v-deep  .el-divider--horizontal {
  margin: 0px;
}
::v-deep  .el-form-item__error {
  margin-left: 40px;
}
.searchMy ::v-deep  .el-form-item--small.el-form-item {
  width: 23%;
}
.Fsearch ::v-deep  .el-dialog {
  width: 60%;
}
.span-tip {
  display: inline-block;
  width: 20px;
  height: 20px;
  font-size: 14px;
  border: 1px solid #4183d5;
  color: #4183d5;
  text-align: center;
  line-height: 20px;
  border-radius: 50%;
  margin-left: 5px;
}
.typeEnter {
  overflow: hidden;
  width: 140px;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-box-pack: center;
  -webkit-box-align: center;
  -webkit-line-clamp: 2; //折两行后显示'...'
}
</style>
