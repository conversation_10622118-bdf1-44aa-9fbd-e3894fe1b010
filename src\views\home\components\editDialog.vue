<template>
  <div class="edit-dialog-container">
    <el-dialog :visible.sync="outerVisible" width="60%">
      <template #title>
        <span style="font-weight: bold">编辑指标</span>
      </template>
      <drag
        :list="dragList"
        :totalLength="list.length"
        @subItem="subItem"
        @handleSearch="handleSearch"
        @updateDragList="updateDragList"
      />
      <div class="canAdd">可添加:</div>
      <dialogList
        title="今日店铺数据"
        :type="4"
        :list="showTadayShopList"
        :isDialog="true"
        @addItem="addItem"
      />
      <dialogList
        title="售前待办"
        :type="1"
        :list="showPreTodo"
        :isDialog="true"
        @addItem="addItem"
      />
      <dialogList
        title="商品管理"
        :type="2"
        :list="showGoodsManage"
        :isDialog="true"
        @addItem="addItem"
      />
      <dialogList
        title="售后待办"
        :type="3"
        :list="showAfterTodo"
        :isDialog="true"
        @addItem="addItem"
      />

      <div slot="footer" class="dialog-footer">
        <el-button @click="outerVisible = false" size="mini" class="btn-cancel">取 消</el-button>
        <el-button type="success" @click="saveEdit" size="mini" style="background-color: #00b955;">确认</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import drag from './drag.vue'
import dialogList from './diaLogList.vue'
import { updateOrgIndexModule } from '@/api/home/<USER>'
import { mapState } from 'vuex'
import { nextTick } from 'vue'
export default {
  components: { drag, dialogList },
  props: {
    list: {
      default: []
    }
  },
  data() {
    return {
      outerVisible: false,
      innerVisible: false,
      dragList: [],
      tadayShopList: [],
      preTodo: [],
      afterTodo: [],
      goodsManage: [],
      searchValue: ''
    }
  },
  computed: {
    ...mapState('app', ['shopConfig']),
    showTadayShopList() {
      return this.tadayShopList.filter((item) => item.moduleName.includes(this.searchValue))
    },
    showPreTodo() {
      return this.preTodo.filter((item) => item.moduleName.includes(this.searchValue))
    },
    showAfterTodo() {
      return this.afterTodo.filter((item) => item.moduleName.includes(this.searchValue))
    },
    showGoodsManage() {
      return this.goodsManage.filter((item) => item.moduleName.includes(this.searchValue))
    },
  },
  watch: {
    dragList: {
      handler(val) {
        console.log('修改-father')
      }
    },
    list: {
      handler(val) {
        console.log('list修改')
        this.dragList = JSON.parse(
          JSON.stringify(val.filter((item) => item.sort !== null).sort((a, b) => a.sort - b.sort))
        )
        this.tadayShopList = JSON.parse(
          JSON.stringify(val.filter((item) => item.typeName == '今日店铺数据'))
        )
        this.preTodo = JSON.parse(JSON.stringify(val.filter((item) => item.typeName == '售前待办')))
        this.afterTodo = JSON.parse(
          JSON.stringify(val.filter((item) => item.typeName == '售后待办'))
        )
        this.goodsManage = JSON.parse(
          JSON.stringify(val.filter((item) => item.typeName == '商品管理'))
        )
      }
    }
  },
  methods: {
    updateDragList(val) {
      this.dragList = val
      this.dragList.forEach((item) => {
       this.preTodo.findIndex(i=>i.id===item.id) !== -1 ? this.preTodo[this.preTodo.findIndex(i=>i.id===item.id)].sort = item.sort : ''
       this.afterTodo.findIndex(i=>i.id===item.id) !== -1 ? this.afterTodo[this.afterTodo.findIndex(i=>i.id===item.id)].sort = item.sort : ''
       this.goodsManage.findIndex(i=>i.id===item.id) !== -1 ? this.goodsManage[this.goodsManage.findIndex(i=>i.id===item.id)].sort = item.sort : ''
       this.tadayShopList.findIndex(i=>i.id===item.id) !== -1 ? this.tadayShopList[this.tadayShopList.findIndex(i=>i.id===item.id)].sort = item.sort : ''
      })
    },
    handleSearch(val) {
      this.searchValue = val
    },
    getData() {
      this.dragList = JSON.parse(
        JSON.stringify(
          this.list.filter((item) => item.sort !== null).sort((a, b) => a.sort - b.sort)
        )
      )
      this.tadayShopList = JSON.parse(
        JSON.stringify(this.list.filter((item) => item.typeName == '今日店铺数据'))
      )
      this.preTodo = JSON.parse(
        JSON.stringify(this.list.filter((item) => item.typeName == '售前待办'))
      )
      this.afterTodo = JSON.parse(
        JSON.stringify(this.list.filter((item) => item.typeName == '售后待办'))
      )
      this.goodsManage = JSON.parse(
        JSON.stringify(this.list.filter((item) => item.typeName == '商品管理'))
      )
    },
    addItem(item) {
      let index = 0
      if (item.type === 4) {
        index = this.tadayShopList.findIndex((i) => i.id === item.id)
        if (index !== -1) {
          this.tadayShopList[index].sort = this.dragList.length + 1
          this.dragList.push(this.tadayShopList[index])
        }
      } else if (item.type === 1) {
        index = this.preTodo.findIndex((i) => i.id === item.id)
        if (index !== -1) {
          this.preTodo[index].sort = this.dragList.length + 1
          this.dragList.push(this.preTodo[index])
        }
      } else if (item.type === 3) {
        index = this.afterTodo.findIndex((i) => i.id === item.id)
        if (index !== -1) {
          this.afterTodo[index].sort = this.dragList.length + 1
          this.dragList.push(this.afterTodo[index])
        }
      } else if (item.type === 2) {
        index = this.goodsManage.findIndex((i) => i.id === item.id)
        if (index !== -1) {
          this.goodsManage[index].sort = this.dragList.length + 1
          this.dragList.push(this.goodsManage[index])
        }
      }
    },
    subItem(id) {
      let delIndex = this.dragList.findIndex((item) => item.id === id)
      this.dragList.splice(delIndex, 1)

      this.dragList.forEach((item, index) => {
        if( index >= delIndex){
          item.sort = item.sort - 1
        }
      })
      let index = this.tadayShopList.findIndex((i) => i.id === id)
      if (index !== -1) {
        this.tadayShopList[index].sort = null
      }
      index = this.preTodo.findIndex((i) => i.id === id)
      if (index !== -1) {
        this.preTodo[index].sort = null
      }
      index = this.afterTodo.findIndex((i) => i.id === id)
      if (index !== -1) {
        this.afterTodo[index].sort = null
      }
      index = this.goodsManage.findIndex((i) => i.id === id)
      if (index !== -1) {
        this.goodsManage[index].sort = null
      }
    },
    openDialog() {
      this.outerVisible = true
    },
    saveEdit() {
      let shopIndexModules = ''
      this.dragList.forEach((item) => (shopIndexModules += item.id + ','))
      shopIndexModules = shopIndexModules.substring(0, shopIndexModules.length - 1)
      this.$emit('updateShopData')
      updateOrgIndexModule({
        regMobile: this.shopConfig.mobile,
        shopIndexModules: shopIndexModules
      }).then((res) => {
        if (res.code === 0) {
          this.$message.success('保存成功')
          this.outerVisible = false
        } else {
          this.$message.error('保存失败')
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
.edit-dialog-container ::v-deep   .search-input {
  width: 200px;
  margin-top: 10px;
}
.edit-dialog-container ::v-deep   .el-dialog__header {
  border-bottom: 1px solid #ccc;
  padding: 10px;
  border-radius: 4px;

}

.edit-dialog-container ::v-deep   .el-dialog__footer {
  border-top: 1px solid #ccc;
  padding: 10px 20px;
}
::v-deep   .el-dialog__body {
  padding: 10px 20px !important;
}
.canAdd{
  font-weight: bold;
  color:#111111;
}
::v-deep   .el-dialog__headerbtn{
  font-size: 20px;
  position: absolute;
  top:10px;
  right:10px;
  color:#585757;
}
.btn-cancel:hover{
  color:#00B955;
  background-color: #ebf4ef;
}
</style>
