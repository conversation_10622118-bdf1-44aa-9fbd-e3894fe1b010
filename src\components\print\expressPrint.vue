<template>
  <div id="printPage">
    <div class="printContent" v-for="(item,index) in printFnList" :key="index">
      <JDexpress v-if="item.logisticsCompanyCode===11 || item.logisticsCompanyCode===31" :config="item" />
      <!-- <JDKDexpress v-if="item.logisticsCompanyCode===31" :config="item" /> -->
      <SFexpress v-if="item.logisticsCompanyCode===1" :config="item" />
      <ZTOexpress v-if="item.logisticsCompanyCode===8" :config="item" />
    </div>
  </div>
</template>

<script>
import { outJudgementInstallLodop, getLodop } from '@/utils/lodop';
import JDexpress from './compoment/JD/JDexpress';
import SFexpress from './compoment/SF/SFexpress';
import ZTOexpress from './compoment/ZTO/ZTOexpress';
// import JDKDexpress from './compoment/JDKD/JDKDexpress';

export default {
  name: '',
  components: {
    JDexpress,
    SFexpress,
    ZTOexpress,
    // JDKDexpress
  },
  data() {
    return {
      count: 0,
      printFnList: []
    };
  },
  computed: {},
  watch: {},
  created() {
    outJudgementInstallLodop();
  },
  mounted() {

  },
  methods: {
    printAction(params) {
      if (!params || !Object.keys(params).length > 0) {
        this.$message.warning('缺少必要参数');
        return false;
      }
      if (params.deliveryPlatform == 1) { // tms类型快递
        this.printFnList = [];
        this.count = 0;

        if (Array.isArray(params.subWaybillNoList) && params.subWaybillNoList.length > 0) {
          params.expressTotal = params.subWaybillNoList.length;
          params.subWaybillNoList.forEach(item => {
            params.expressIndex = Number(item.subSerialNumber);
            params.currentSubBiLLNo = item.subWaybillNo;
            if (Number(params.logisticsCompanyCode) === 1) {
              //顺丰
              params.currentTwoDimensionCode = params.sFFace.twoDimensionCode.replace(/SF\d*/, item.subWaybillNo);
            }
            this.printFnList.push({ ...params });
          });
        }
        this.$nextTick(function () {
          this.print();
        });
      } else if (params.deliveryPlatform == 2) { // 快递鸟
        this.lodop = getLodop();
        this.lodop.PRINT_INIT('快递单'); // 打印初始化
        const html = params.printTemplate;
        const sizeWidth = params.templateSize.split('*')[0];
        this.lodop.SET_PRINT_PAGESIZE(3, sizeWidth, '', 'CreateCustomPage');
        this.lodop.ADD_PRINT_HTM(1, 1, sizeWidth, '', html);
        if (params.skipPrintPreview) {
          this.lodop.PRINT(); // 直接打印
        } else {
          this.lodop.PREVIEW(); // 预览
        }
      }
    },
    print() {
      try {
        if (this.count < this.printFnList.length) {
          this.createTml(this.printFnList[this.count], this.count);
          if (this.printFnList[this.count].skipPrintPreview) {
            this.lodop.PRINT(); // 直接打印
          } else {
            this.lodop.PREVIEW(); // 预览
            // this.lodop.PRINT_DESIGN(); // 打印设计-直观调整
          }
          this.lodop.On_Return = (TaskID, Value) => {
            this.print();
          };
          this.count++;
        }
        // this.lodop.PRINT(); // 直接打印
        // this.lodop.PREVIEW(); // 预览
        // this.lodop.PRINT_DESIGN(); // 打印设计-直观调整
      } catch (error) {
        console.log(error);
      }
    },
    createTml(item, index) {  //打印初始化
      try {
        this.lodop = getLodop();
        this.lodop.PRINT_INIT('快递单'); // 打印初始化
        this.lodop.SET_PRINT_STYLE('FontSize', 18);
        this.lodop.SET_PRINT_STYLE('Bold', 1);
        // this.lodop.SET_PRINT_MODE('POS_BASEON_PAPER', true);
        // this.lodop.SET_PRINT_STYLEA(0,"Horient",2);
        // this.lodop.SET_PRINT_STYLEA(0,"Vorient",2);
        // 打印方向及纸张类型  纸张宽  纸张高 纸张名
        // this.lodop.SET_PRINT_PAGESIZE(3, '100mm', '0mm', 'CreateCustomPage');
        const html = document.getElementsByClassName('printContent')[index].innerHTML;
        //intTop,intLeft,intWidth,intHeight,strHtml

        if (item.logisticsCompanyCode === 11 || item.logisticsCompanyCode === 31) {  //JD物流 || 京东快递
          if (item.templateSize === '76mm*130mm') {
            this.lodop.SET_PRINT_PAGESIZE(3, '76mm', '0mm', 'CreateCustomPage');
            this.lodop.ADD_PRINT_HTM('5mm', 1, '76mm', '', `<body>${html}</body>`);
            if(item.logisticsCompanyCode === 11 ) {
              this.lodop.ADD_PRINT_BARCODE(32, 17, '200', '40', '128Auto', item.currentSubBiLLNo);
              this.lodop.SET_PRINT_STYLEA(0, 'FontSize', 10);
              this.lodop.ADD_PRINT_BARCODE(337, 25, '158', '45', '128Auto', item.waybillNo);
              this.lodop.SET_PRINT_STYLEA(0, 'FontSize', 10);
            } else {
              this.lodop.ADD_PRINT_BARCODE(33, 16, '220', '42', '128Auto', item.currentSubBiLLNo);
              this.lodop.SET_PRINT_STYLEA(0, 'FontSize', 10);
              this.lodop.ADD_PRINT_BARCODE(335, 17, '158', '45', '128Auto', item.waybillNo);
              this.lodop.SET_PRINT_STYLEA(0, 'FontSize', 10);
            }
            const imgStr = item.logisticsCompanyCode === 11 ? '<img height="25" width="103" src="https://oss-ec.ybm100.com/ybm/popDeliverLog/jingdong_logo.png" alt=""/>' : '<img  height="25" width="103" src="https://oss-ec.ybm100.com/ybm/popDeliverLog/jingdongKD_logo.png" alt=""/>';
            this.lodop.ADD_PRINT_IMAGE(288, 17, '103', '25', imgStr);
          }
          if (item.templateSize === '100mm*115mm') {
            this.lodop.SET_PRINT_PAGESIZE(3, '100mm', '0mm', 'CreateCustomPage');
            this.lodop.ADD_PRINT_HTM(1, 1, '100mm', '', `<body>${html}</body>`);
            this.lodop.ADD_PRINT_BARCODE(10, 37, '218', '40', '128Auto', item.currentSubBiLLNo);
            this.lodop.SET_PRINT_STYLEA(0, 'FontSize', 10);
            this.lodop.ADD_PRINT_BARCODE(300, 24, '161', '45', '128Auto', item.waybillNo);
            this.lodop.SET_PRINT_STYLEA(0, 'FontSize', 10);
            const imgStr = item.logisticsCompanyCode === 11 ? '<img height="25" width="110" src="https://oss-ec.ybm100.com/ybm/popDeliverLog/jingdong_logo.png" alt=""/>' : '<img  height="25" width="110" src="https://oss-ec.ybm100.com/ybm/popDeliverLog/jingdongKD_logo.png" alt=""/>';
            this.lodop.ADD_PRINT_IMAGE(266, 21, '110', '25', imgStr);
          }
          if (item.templateSize === '100mm*150mm') {
            this.lodop.SET_PRINT_PAGESIZE(3, '100mm', '0mm', 'CreateCustomPage');
            this.lodop.ADD_PRINT_HTM(1, 1, '100mm', '', `<body>${html}</body>`);
            this.lodop.ADD_PRINT_BARCODE(10, 37, '218', '40', '128Auto', item.currentSubBiLLNo);
            this.lodop.SET_PRINT_STYLEA(0, 'FontSize', 10);
            this.lodop.ADD_PRINT_BARCODE(306, 20, '161', '45', '128Auto', item.waybillNo);
            this.lodop.SET_PRINT_STYLEA(0, 'FontSize', 10);
            const imgStr = item.logisticsCompanyCode === 11 ? '<img height="25" width="110" src="https://oss-ec.ybm100.com/ybm/popDeliverLog/jingdong_logo.png" alt=""/>' : '<img  height="25" width="110" src="https://oss-ec.ybm100.com/ybm/popDeliverLog/jingdongKD_logo.png" alt=""/>';
            this.lodop.ADD_PRINT_IMAGE(267, 21, '110', '25', imgStr);
          }
        } else if (item.logisticsCompanyCode === 1) {  //SF
          if (item.templateSize === '76mm*130mm') {
            this.lodop.SET_PRINT_PAGESIZE(3, '76mm', '0mm', 'CreateCustomPage');
            this.lodop.ADD_PRINT_HTM('5mm', 1, '76mm', '', `<body>${html}</body>`);
            this.lodop.ADD_PRINT_BARCODE(85, 30, '228', '45', '128Auto', item.currentSubBiLLNo);
            this.lodop.SET_PRINT_STYLEA(0,"ShowBarText",0);//设置是否显示下方的文字
            this.lodop.ADD_PRINT_BARCODE(337, 186, '51', '45', 'QRCode', item.currentTwoDimensionCode || item.sFFace.twoDimensionCode);
            this.lodop.ADD_PRINT_BARCODE(206, 240, '28', '235', '128Auto', item.currentSubBiLLNo);
            this.lodop.SET_PRINT_STYLEA(0, 'Angle', 90);//设置旋转角度
            this.lodop.SET_PRINT_STYLEA(0,"ShowBarText",0);//设置是否显示下方的文字
          }
          if (item.templateSize === '100mm*115mm') {
            this.lodop.SET_PRINT_PAGESIZE(3, '100mm', '0mm', 'CreateCustomPage');
            this.lodop.ADD_PRINT_HTM(1, 1, '100mm', '', `<body>${html}</body>`);
            this.lodop.ADD_PRINT_BARCODE(70, 45, '304', '60', '128Auto', item.currentSubBiLLNo);
            this.lodop.SET_PRINT_STYLEA(0,"ShowBarText",0);//设置是否显示下方的文字
            this.lodop.ADD_PRINT_BARCODE(338, 204, '95', '95', 'QRCode', item.currentTwoDimensionCode || item.sFFace.twoDimensionCode);
            this.lodop.ADD_PRINT_BARCODE(200, 300, '60', '200', '128Auto', item.currentSubBiLLNo);
            this.lodop.SET_PRINT_STYLEA(0, 'Angle', 90);//设置旋转角度
            this.lodop.SET_PRINT_STYLEA(0,"ShowBarText",0);//设置是否显示下方的文字
          }
          if (item.templateSize === '100mm*150mm') {
            this.lodop.SET_PRINT_PAGESIZE(3, '100mm', '0mm', 'CreateCustomPage');
            this.lodop.ADD_PRINT_HTM(1, 1, '100mm', '', `<body>${html}</body>`);
            this.lodop.ADD_PRINT_BARCODE(70, 45, '304', '60', '128Auto', item.currentSubBiLLNo);
            this.lodop.SET_PRINT_STYLEA(0,"ShowBarText",0);//设置是否显示下方的文字
            this.lodop.ADD_PRINT_BARCODE(258, 144, '95', '95', 'QRCode', item.currentTwoDimensionCode || item.sFFace.twoDimensionCode);
          }
        } else if (item.logisticsCompanyCode === 8) {  //ZTO
          if (item.templateSize === '76mm*130mm') {
            this.lodop.SET_PRINT_PAGESIZE(3, '76mm', '0mm', 'CreateCustomPage');
            this.lodop.ADD_PRINT_HTM(1, 1, '76mm', '', `<body>${html}</body>`);
            this.lodop.ADD_PRINT_BARCODE(76, 35, '216', '55', '128Auto', item.currentSubBiLLNo);
            this.lodop.SET_PRINT_STYLEA(0, 'FontSize', 10);
            this.lodop.ADD_PRINT_BARCODE(150, 210, '60', '180', '128Auto', item.currentSubBiLLNo);
            this.lodop.SET_PRINT_STYLEA(0, 'FontSize', 10);
            this.lodop.SET_PRINT_STYLEA(0, 'Angle', 90);//设置旋转角度
          }
          if (item.templateSize === '100mm*115mm') {
            this.lodop.SET_PRINT_PAGESIZE(3, '100mm', '0mm', 'CreateCustomPage');
            this.lodop.ADD_PRINT_HTM(1, 1, '100mm', '', `<body>${html}</body>`);
            this.lodop.ADD_PRINT_BARCODE(76, 75, '216', '55', '128Auto', item.currentSubBiLLNo);
            this.lodop.SET_PRINT_STYLEA(0, 'FontSize', 10);
            this.lodop.ADD_PRINT_BARCODE(140, 290, '60', '180', '128Auto', item.currentSubBiLLNo);
            this.lodop.SET_PRINT_STYLEA(0, 'FontSize', 10);
            this.lodop.SET_PRINT_STYLEA(0, 'Angle', 90);//设置旋转角度
          }
          if (item.templateSize === '100mm*150mm') {
            this.lodop.SET_PRINT_PAGESIZE(3, '100mm', '0mm', 'CreateCustomPage');
            this.lodop.ADD_PRINT_HTM(1, 1, '100mm', '', `<body>${html}</body>`);
            this.lodop.ADD_PRINT_BARCODE(76, 75, '216', '55', '128Auto', item.currentSubBiLLNo);
            this.lodop.SET_PRINT_STYLEA(0, 'FontSize', 10);
            this.lodop.ADD_PRINT_BARCODE(115, 290, '60', '250', '128Auto', item.currentSubBiLLNo);
            this.lodop.SET_PRINT_STYLEA(0, 'FontSize', 10);
            this.lodop.SET_PRINT_STYLEA(0, 'Angle', 90);//设置旋转角度
          }
        }
      } catch (e) {
        console.log(e);
      }

    }
  }

};
</script>

<style lang="scss" scoped>

</style>
