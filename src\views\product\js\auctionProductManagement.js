export default {
    formItems: [
        {
          label: '商品编码',
          prop: 'barcode',
          component: 'el-input',
          
        },
        {
          label: '商品erp编码',
          prop: 'erpCode',
          component: 'el-input',
        },
        {
          label: '商品名称',
          prop: 'productName',
          component: 'el-input',
        },
        {
          label: '生产厂家',
          prop: 'manufacturer',
          component: 'el-input',
        },
        {
          label: '批准文号',
          prop: 'approvalNumber',
          component: 'el-input',
        },
        {
          label: '商品类型',
          prop: 'activityType',
          component: 'el-select',
          attrs: {
            options: [
              {
                label: '全部',
                value: ''
              },
              {
                label: '拼团商品',
                value: 1
              },
              {
                label: '批购包邮',
                value: 3
              }
            ]
          }
        },
        {
          label: '竞价状态',
          prop: 'biddingStatus',
          component: 'el-select',
          attrs: {
            options: [
              {
                label: '全部',
                value: ''
              },
              {
                label: '生效',
                value: 1
              },
              {
                label: '失效',
                value: 2
              },
              {
                label: '已删除',
                value: 20
              },
              
            ]
          }
        }
      ],
    col:[
        {
            index: 'productInfo',
            name: '商品信息',
            width: 150,
            slot: true
          },
          {
            index: 'showInfo',
            name: '展示信息',
            width: 100,
            slot: true
          },
          {
            index: 'price',
            name: '价格/库存',
            width: 160,
            slot: true
          },
          {
            index: 'sort',
            name: '排名',
            width: 160,
            slot: true
          },
          {
            index: 'status',
            name: '商品状态',
            width: 200,
            slot: true
          },
    ]
}