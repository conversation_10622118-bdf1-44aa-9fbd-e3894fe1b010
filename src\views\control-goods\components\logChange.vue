<template>
  <el-dialog
    title="查看日志"
    :visible="true"
    width="60%"
    :before-close="handleClose"
  >
    <div>
      <el-table
        ref="goodTable"
        max-height="397"
        :data="list"
        stripe
        style="width: 100%"
        :header-cell-style="{ background: '#F9F9F9', color: '#666666' }"
      >
        <el-table-column prop="skuNums" label="变更内容">
          <template slot-scope="scope">
            <div v-for="item in scope.row.changeInfo" :key="item">{{ item }}</div>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="变更时间" width="220">
          <template slot-scope="scope">
            <span>{{ formatDate(scope.row.createTime) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="updateUser" label="操作人" width="220" />
      </el-table>
      <div class="explain-pag">
        <Pagination
          v-show="total > 0"
          :total="total"
          :page.sync="pageData.pageNum"
          :limit.sync="pageData.pageSize"
          @pagination="getList"
        />
      </div>
    </div>
  </el-dialog>
</template>

<script>
import Pagination from '@/components/Pagination';
import { seeLog } from '@/api/goods/controlGoods.js';
export default {
  name: 'LogChange',
  components: {
    Pagination,
  },
  props: {
    id: {
      default: '',
    },
  },
  data() {
    return {
      list: [],
      total: 0,
      pageData: {
        pageSize: 10,
        pageNum: 1,
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    getList() {
      seeLog({
        groupId: this.id,
        ...this.pageData,
      }).then((res) => {
        console.log('变更日志', res);
        this.list = (res.data || {}).list || [];
        this.total = (res.data || {}).total || 0;
      })
    },
    handleClose() {
      // this.$emit('handleClose');
      this.$emit('update:showLog', false);
    },
  },
};
</script>

<style scoped>

</style>
