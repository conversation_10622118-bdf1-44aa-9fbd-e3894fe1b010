<template>
  <div class="todoTask">
    <div class="task_title">待办任务</div>
    <el-row class="trade">
      <el-col :span="24">
        <div class="tag_title">平台通知</div>
      </el-col>
    </el-row>
    <el-row class="todoList" style="display: flex">
      <el-col v-if="isBankAccount">
        <el-tooltip placement="top">
          <div slot="content">邀请您进行企业开户。企业开户完成后，已结算资金当天提现当天到账。</div>
          <el-button type="primary" plain @click="accountOpening">
            企业开户
            <i class="el-icon-warning-outline"></i>
          </el-button>
        </el-tooltip>
      </el-col>

      <el-col
        v-if="qualtificationInfos.size"
      >
        <el-tooltip placement="top">
          <div slot="content">
            <span>企企业资质已过期/即将过期，资质过期店铺会被自动下线，请尽快更新处理！</span><br>
            <span>已过期：资质有效期至-当前日期<0天</span><br>
            <span>即将过期：0天<=资质有效期至-当前日期<=30天</span>
          </div>
          <el-button
            type="primary"
            plain
            @click="qualtificationOpening"
          >
            资质已过期/即将过期({{ qualtificationInfos.size }})
            <i class="el-icon-warning-outline" />
          </el-button>
        </el-tooltip>
      </el-col>

      <el-col
        v-if="canReportBaseNum"
      >
        <el-tooltip placement="top">
          <div slot="content">
            存在可提报的拼团活动，请及时上传
          </div>
          <el-button
            type="primary"
            plain
            @click="collageActivityOpening(null, 'activity_Participate')"
          >
            待参与拼团活动({{ canReportBaseNum }})
            <i class="el-icon-warning-outline" />
          </el-button>
        </el-tooltip>
      </el-col>
      <el-col
        v-if="rejectNum"
      >
        <el-tooltip placement="top">
          <div slot="content">
            拼团活动驳回，请及时修改
          </div>
          <el-button
            type="primary"
            plain
            @click="collageActivityOpening({rejectNum: true}, 'activity_reject')"
          >
            拼团驳回待修改({{ rejectNum }})
            <i class="el-icon-warning-outline" />
          </el-button>
        </el-tooltip>
      </el-col>
      <el-col
        v-if="priceTooLowPrice"
      >
        <el-tooltip placement="top">
          <div slot="content">
            拼团价格低于近30天正常成交价过多，存在资损风险，请及时核查
          </div>
          <el-button
            type="primary"
            plain
            @click="collageActivityOpening({priceTooLowPrice: true}, 'low_price')"
          >
            拼团价格过低({{ priceTooLowPrice }})
            <i class="el-icon-warning-outline" />
          </el-button>
        </el-tooltip>
      </el-col>
      <el-col v-if="dialog.contractNum != 0">
        <el-tooltip placement="top">
          <div slot="content">
            当前有待签署或开通的任务，请尽快签署确认，逾期店铺将被自动下线
          </div>
          <el-button
            type="primary"
            plain
            @click="dialog.visible.contract = true"
          >
            <span style="color:red;">协议签署提醒({{ dialog.contractNum }})</span>
            <i class="el-icon-warning-outline" />
          </el-button>
        </el-tooltip>
      </el-col>
      <el-col  v-if="dialog.openShopTaskNum != 0">
        <el-button
          type="primary"
          plain
          @click="dialog.visible.openShop = true"
        >
          <span style="color:red;">开店事项待办提醒({{ dialog.openShopTaskNum }})</span>
        </el-button>
      </el-col>
      <el-col v-if="todoConfig.applyNationalBusinessCircle">
        <el-tooltip placement="top">
          <div slot="content">
            达标后可开通全国商圈，提高店铺曝光度、提升店铺销售额
          </div>
          <el-button
            type="primary"
            plain
            @click="toStoreAreaSalesControl"
          >
          <span style="color:red;">待申请全国商圈</span>
            <i class="el-icon-warning-outline" />
          </el-button>
        </el-tooltip>
      </el-col>
      <el-col v-if="!todoConfig.applyNationalBusinessCircle && !isBankAccount && !qualtificationInfos.size && !canReportBaseNum && !rejectNum && !priceTooLowPrice && dialog.openShopTaskNum == 0 && dialog.contractNum == 0" :span="6">
        <div style="cursor: text">暂无平台活动</div>
      </el-col>
    </el-row>
    <el-row class="trade">
      <el-col :span="24">
        <div class="tag_title">交易管理</div>
      </el-col>
    </el-row>
    <template
      v-if="(Number(todoConfig.waitExamineOrderCount) > 0 || Number(todoConfig.waitDeliveryOrderCount) > 0 || Number(todoConfig.timeoutOrderCount) > 0 || Number(todoConfig.waitExamineRefundOrderCount) > 0 || Number(todoConfig.pullFailedOrderCount) > 0 || Number(todoConfig.logisticsTrackFailCount) > 0 || Number(todoConfig.logisticsTrackIllegalCount) > 0 || Number(todoConfig.a) > 0) && shopConfig.indexShow"
    >
      <div v-permission="['deal_order']">
        <el-row class="todoList">
          <el-col
            :span="6"
            v-if="Number(todoConfig.waitOpenAccountOrderCount)>0"
            ref="exposure_mark_waitOpenAccountOrderCount"
            @click.native="jumpPage('/orderList',1);transactionClickItem('no_opened')"
          >
            <span class="todoItem bg_green">待开户订单</span>
            <el-tooltip placement="top">
              <div slot="content">订单需进行开户操作</div>
              <i class="el-icon-warning-outline"></i>
            </el-tooltip>
            <div class="todoCount">{{ todoConfig.waitOpenAccountOrderCount }}</div>
          </el-col>
          <el-col
            :span="6"
            v-if="Number(todoConfig.waitExamineOrderCount)>0"
            ref="exposure_mark_waitExamineOrderCount"
            @click.native="jumpPage('/orderList',2);transactionClickItem('no_approved')"
          >
            <span class="todoItem bg_blue">待审核订单</span>
            <el-tooltip placement="top">
              <div slot="content">
                订单需进行审核。若已完成ERP订单对接，无需手工审核。
                <br />请关注订单下发失败原因
              </div>
              <i class="el-icon-warning-outline"></i>
            </el-tooltip>
            <div class="todoCount">{{ todoConfig.waitExamineOrderCount }}</div>
          </el-col>
          <el-col
            :span="6"
            v-if="Number(todoConfig.waitDeliveryOrderCount)>0"
            ref="exposure_mark_waitDeliveryOrderCount"
            @click.native="jumpPage('/orderList',3);transactionClickItem('no_shipped')"
          >
            <span class="todoItem bg_yellow">待发货订单</span>
            <el-tooltip placement="top">
              <div slot="content">
                订单需进行发货。若已完成ERP出库单对接，无需手工发货。
                <br />请在订单支付后48小时内发货完成
              </div>
              <i class="el-icon-warning-outline"></i>
            </el-tooltip>
            <div class="todoCount">{{ todoConfig.waitDeliveryOrderCount }}</div>
          </el-col>
          <el-col
            :span="6"
            v-if="Number(todoConfig.timeoutOrderCount)>0"
            ref="exposure_mark_timeoutOrderCount"
            @click.native="jumpPage('/orderList',4);transactionClickItem('48h_no_shipped')"
          >
            <span class="todoItem bg_yellow">超48小时未发货</span>
            <el-tooltip placement="top">
              <div slot="content">订单支付成功后超48小时未发货，请尽快发货</div>
              <i class="el-icon-warning-outline"></i>
            </el-tooltip>
            <div class="todoCount">{{ todoConfig.timeoutOrderCount }}</div>
          </el-col>
          <el-col
            :span="6"
            v-if="Number(todoConfig.waitExamineRefundOrderCount)>0"
            ref="exposure_mark_waitExamineRefundOrderCount"
            @click.native="jumpPage('/afterSaleList',0);transactionClickItem('no_approved_refund')"
          >
            <span class="todoItem bg_red">待审核退款订单</span>
            <el-tooltip placement="top">
              <div slot="content">退款单需进行审核</div>
              <i class="el-icon-warning-outline"></i>
            </el-tooltip>
            <div class="todoCount">{{ todoConfig.waitExamineRefundOrderCount }}</div>
          </el-col>
          <el-col
            v-if="Number(todoConfig.pullFailedOrderCount) > 0"
            ref="exposure_mark_pullFailedOrderCount"
            :span="6"
            @click.native="jumpPage('/orderList', 6);transactionClickItem('failed_ERP')"
          >
            <span class="todoItem bg_red">下推ERP失败</span>
            <el-tooltip placement="top">
              <div slot="content">下推ERP失败的订单数量，请及时查看并处理。</div>
              <i class="el-icon-warning-outline"></i>
            </el-tooltip>
            <div class="todoCount">{{ todoConfig.pullFailedOrderCount }}</div>
          </el-col>
          <el-col
            :span="6"
            v-if="Number(todoConfig.evidenceToExamine)>0"
            ref="exposure_mark_evidenceToExamine"
            @click.native="jumpPage('/orderList',1, 'evidenceToExamine');transactionClickItem('wire_transfer')"
          >
            <span class="todoItem bg_green">待审核电汇订单</span>
            <el-tooltip placement="top">
              <div slot="content">订单需进行电汇审核，请及时核款确认。</div>
              <i class="el-icon-warning-outline"></i>
            </el-tooltip>
            <div class="todoCount">{{ todoConfig.evidenceToExamine }}</div>
          </el-col>

          <el-col
            v-if="Number(todoConfig.logisticsTrackFailCount)>0"
            ref="exposure_mark_logisticsTrackFailCount"
            :span="6"
            @click.native="jumpPage('/orderList', 7);transactionClickItem('failed_track')"
          >
            <span class="todoItem bg_red">物流轨迹获取失败</span>
            <el-tooltip placement="top">
              <div slot="content">
                订单发货物流轨迹获取失败，请检查快递公司和单号是否填写正确，如有问题及时修正。订单无物流轨迹可能会影响后期结算，请及时关注处理
              </div>
              <i class="el-icon-warning-outline" />
            </el-tooltip>
            <div class="todoCount">
              {{ todoConfig.logisticsTrackFailCount }}
            </div>
          </el-col>

          <el-col
            v-if="Number(todoConfig.logisticsTrackIllegalCount)>0"
            ref="exposure_mark_logisticsTrackIllegalCount"
            :span="6"
            @click.native="jumpPage('/orderList', 8);transactionClickItem('illegal_track')"
          >
            <span class="todoItem bg_red">物流轨迹异常提醒</span>
            <el-tooltip placement="top">
              <div slot="content">
                订单发货快递的揽收或签收时间&lt订单支付时间，请检查快递公司或运单号是否填写错误，如有问题及时修正。订单异常物流轨迹可能会影响后期结算，请及时关注处理
              </div>
              <i class="el-icon-warning-outline" />
            </el-tooltip>
            <div class="todoCount">
              {{ todoConfig.logisticsTrackIllegalCount }}
            </div>
          </el-col>

          <el-col
            v-if="Number(todoConfig.nonInvoiceOrderCount) > 0"
            ref="exposure_mark_nonInvoiceOrderCount"
            @click.native="jumpPage('/orderList','nonInvoiceOrderCount');transactionClickItem('no_invoice')"
            :span="6">
            <span class="todoItem bg_red">未上传电子发票</span>
            <el-tooltip placement="top">
              <div slot="content">
                订单未上传电子发票，请尽快上传
              </div>
              <i class="el-icon-warning-outline" />
            </el-tooltip>
            <div class="todoCount">
              {{ todoConfig.nonInvoiceOrderCount }}
            </div>
          </el-col>

          <el-col
            v-if="Number(todoConfig.partialShipmentOrderCount)>0"
            :span="6"
            @click.native="jumpPage('/orderList', 9)"
          >
            <span class="todoItem bg_red">部分发货待处理</span>
            <el-tooltip placement="top">
              <div slot="content">
                订单有商品部分发货，请及时关注处理
              </div>
              <i class="el-icon-warning-outline" />
            </el-tooltip>
            <div class="todoCount">
              {{ todoConfig.partialShipmentOrderCount }}
            </div>
          </el-col>
		  <el-col
            v-if="Number(todoConfig.afterSalesCount) > 0"
            ref="exposure_mark_nonInvoiceOrderCount"
            @click.native="jumpPage('/afterSaleManager','no_afterSale');transactionClickItem('no_afterSale')"
            :span="6"
		  >
            <span class="todoItem bg_red">待处理售后单</span>
            <el-tooltip placement="top">
              <div slot="content">
                请及时处理售后单据
              </div>
              <i class="el-icon-warning-outline" />
            </el-tooltip>
            <div class="todoCount">
              {{ todoConfig.afterSalesCount }}
            </div>
          </el-col>

          <el-col
            :span="6"
            ref="exposure_mark_urgeDelivery"
            v-if="Number(todoConfig.pendingShipmentReminderQuantity)>0"
            @click.native="jumpPage('/urgeDelivery');commidityClickItem('processed_delivery')"
          >
            <span class="todoItem bg_red">待处理催发货</span>
            <el-tooltip placement="top">
              <div slot="content">催发货待处理、催发货申诉失败、催发货申诉成功状态的单子</div>
              <i class="el-icon-warning-outline"></i>
            </el-tooltip>
            <div class="todoCount">{{ todoConfig.pendingShipmentReminderQuantity }}</div>
          </el-col>
        </el-row>
      </div>
    </template>
    <template v-else>
      <el-row class="todoList">
        <el-col :span="24" style="padding-left: 5px">暂无交易待办</el-col>
      </el-row>
    </template>
    <el-row class="trade">
      <el-col :span="24">
        <div class="tag_title">商品管理</div>
      </el-col>
    </el-row>
    <el-dialog title="协议签署处理提醒" :visible.sync="dialog.visible.contract" width="600px">
        <p style="color:#ff8400;">当前店铺存在待签署的任务，请在“处理截止时间”前处理完毕，逾期店铺将被自动下线。</p>
        <p style="color:#ff8400;">如需稍后出库，可到【企业管理】-【平台服务协议】进行处理</p>
        <el-table :data="dialog.contractTableData" border fit>
            <el-table-column label="任务名称" align="center" prop="taskName">
            </el-table-column>
            <el-table-column label="处理截止时间" align="center">
                <template slot-scope="scope">
                    <p style="color:#ff8400;">{{ scope.row.deadline == null ? '-' :  scope.row.deadline }}</p>
                </template>
            </el-table-column>
        </el-table>
        <div slot="footer" class="dialog-footer">
            <el-button size="small" @click="dialog.visible.contract = false">稍后处理</el-button>
            <el-button type="primary" size="small" @click="handle('/companyOpenAccount/platformServeAgreement')">去处理</el-button>
        </div>
    </el-dialog>
    <el-dialog title="开店事项处理提醒" :visible.sync="dialog.visible.openShop" width="700px">
        <p>
            <span>请按要求完成下方列表中的任务，全部完成后店铺将自动变更为“<span style="color:#00c563;">已上线</span>”可正常经营</span>
        </p>
        <p>
            <span>如需查看店铺任务完成进度，可到首页<span style="color: red;">【待办任务】—【平台活动】</span>中查询</span>
        </p>
        <el-table :data="dialog.openShopTableData" border fit>
            <el-table-column label="任务名称" align="center" prop="taskName">
            </el-table-column>
            <el-table-column label="任务说明">
                <template slot-scope="scope">
                    <p style="color:#ff8400;">{{ scope.row.taskExplain }}</p>
                </template>
            </el-table-column>
            <el-table-column label="状态" align="center">
                <template slot-scope="scope">
                    <span :class="scope.row.status == '已完成' ? 'finished' : 'doing'">{{ scope.row.status }}</span>
                    <span class="work" v-if="scope.row.status == '未完成'" @click="handle(scope.row.url)">去处理</span>
                </template>
            </el-table-column>
        </el-table>
        <div slot="footer" class="dialog-footer">
            <el-button type="primary" size="small" @click="dialog.visible.openShop = false;">知道了</el-button>
        </div>
    </el-dialog>
    <template
      v-if="(Number(todoConfig.lowPriceCount) > 0 || Number(todoConfig.autoSaleWithStockCount) > 0 || Number(todoConfig.nearEffectSkuCount)>0 || Number(todoConfig.nearEffectSkuCount)>0||Number(todoConfig.sellStockOutSkuCount)>0||Number(todoConfig.expireAutoOutMarketSkuCount)>0||Number(todoConfig.priceOSkuCount)>0||Number(todoConfig.waitPutAwaySkuCount)>0||Number(todoConfig.noPassSkuCount)>0||Number(todoConfig.errorInfoCount)>0||Number(todoConfig.authOffShelfCount)>0)&&shopConfig.indexShow">
      <div v-permission="['product_list']">
        <el-row class="todoList">
          <el-col
            :span="6"
            v-if="Number(todoConfig.nearEffectSkuCount)>0"
            ref="exposure_mark_nearEffectSkuCount"
            @click.native="jumpPage('/productList',1);commidityClickItem('near_term')"
          >
            <span class="todoItem bg_green">近效期商品</span>
            <el-tooltip placement="top">
              <div slot="content">
                商品近效期至距离当前时间&lt;=90天。
                <br />若近效期至距离当前时间&lt;30天将被自动下架，请及时关注
              </div>
              <i class="el-icon-warning-outline"></i>
            </el-tooltip>
            <div class="todoCount">{{ todoConfig.nearEffectSkuCount }}</div>
          </el-col>
          <el-col
            :span="6"
            v-if="Number(todoConfig.sellStockOutSkuCount)>0"
            ref="exposure_mark_sellStockOutSkuCount"
            @click.native="jumpPage('/productList',2);commidityClickItem('no_stock')"
          >
            <span class="todoItem bg_yellow">在售缺货</span>
            <el-tooltip placement="top">
              <div slot="content">当前已售罄的在售商品，请及时补货或下架</div>
              <i class="el-icon-warning-outline"></i>
            </el-tooltip>
            <div class="todoCount">{{ todoConfig.sellStockOutSkuCount }}</div>
          </el-col>
          <el-col
            :span="6"
            v-if="Number(todoConfig.expireAutoOutMarketSkuCount)>0"
            ref="exposure_mark_expireAutoOutMarketSkuCount"
            @click.native="jumpPage('/productList',3);commidityClickItem('outdate')"
          >
            <span class="todoItem bg_blue">即将过期自动下架</span>
            <el-tooltip placement="top">
              <div slot="content">
                商品近效期至距离当前时间&lt;30天，已被平台自动下架
                <br />请及时关注
              </div>
              <i class="el-icon-warning-outline"></i>
            </el-tooltip>
            <div class="todoCount">{{ todoConfig.expireAutoOutMarketSkuCount }}</div>
          </el-col>
          <el-col
            :span="6"
            v-if="Number(todoConfig.priceOSkuCount)>0"
            ref="exposure_mark_priceOSkuCount"
            @click.native="jumpPage('/productList',4);commidityClickItem('0_price')"
          >
            <span class="todoItem bg_yellow">售价为0</span>
            <el-tooltip placement="top">
              <div slot="content">
                商品售价为0，请检查并设置单价。
                <br />售价为0的商品将不允许上架
              </div>
              <i class="el-icon-warning-outline"></i>
            </el-tooltip>
            <div class="todoCount">{{ todoConfig.priceOSkuCount }}</div>
          </el-col>
          <el-col
            :span="6"
            v-if="Number(todoConfig.waitPutAwaySkuCount)>0"
            ref="exposure_mark_waitPutAwaySkuCount"
            @click.native="jumpPage('/productList',5);commidityClickItem('prepare_sale')"
          >
            <span class="todoItem bg_yellow">待上架商品</span>
            <el-tooltip placement="top">
              <div slot="content">当前可上架的商品，请关注并及时上架</div>
              <i class="el-icon-warning-outline"></i>
            </el-tooltip>
            <div class="todoCount">{{ todoConfig.waitPutAwaySkuCount }}</div>
          </el-col>
          <el-col
            :span="6"
            v-if="Number(todoConfig.noPassSkuCount)>0"
            ref="exposure_mark_noPassSkuCount"
            @click.native="jumpPage('/productList',6);commidityClickItem('fail_to_pass')"
          >
            <span class="todoItem bg_red">审核不通过</span>
            <el-tooltip placement="top">
              <div slot="content">平台审核未通过，请关注驳回原因并及时处理</div>
              <i class="el-icon-warning-outline"></i>
            </el-tooltip>
            <div class="todoCount">{{ todoConfig.noPassSkuCount }}</div>
          </el-col>
          <el-col :span="6" v-if="Number(todoConfig.authOffShelfCount)>0"
                  ref="exposure_mark_authOffShelfCount"
                  @click.native="jumpPage('/productList',7);commidityClickItem('forced_shelve')">
            <span class="todoItem bg_green">信息有误下架</span>
            <el-tooltip placement="top">
              <div slot="content">商品信息有误，已被系统自动下架，请及时修正。</div>
              <i class="el-icon-warning-outline"></i>
            </el-tooltip>
            <div class="todoCount">{{ todoConfig.authOffShelfCount }}</div>
          </el-col>
          <el-col :span="6" v-if="Number(todoConfig.errorInfoCount)>0"
                  ref="exposure_mark_errorInfoCount"
                  @click.native="jumpPage('/productList',8);commidityClickItem('incorrect_information')">
            <span class="todoItem bg_yellow">信息有误 及时修改</span>
            <el-tooltip placement="top">
              <div slot="content">商品信息有误，请及时修正。</div>
              <i class="el-icon-warning-outline"></i>
            </el-tooltip>
            <div class="todoCount">{{ todoConfig.errorInfoCount }}</div>
          </el-col>

          <el-col
            v-if="Number(todoConfig.autoSaleWithStockCount)>0"
            :span="6"
            @click.native="jumpPage('/productList', 9);"
          >
            <span class="todoItem bg_red">来货自动上架</span>
            <el-tooltip placement="top">
              <div slot="content">
                48h内由售罄变为销售中的商品个数，请及时检查价格
              </div>
              <i class="el-icon-warning-outline" />
            </el-tooltip>
            <div class="todoCount">
              {{ todoConfig.autoSaleWithStockCount }}
            </div>
          </el-col>

          <el-col
            v-if="Number(todoConfig.lowPriceCount)>0"
            :span="6"
            ref="exposure_mark_lowPriceCount"
            @click.native="jumpPage('/productList', 10);commidityClickItem('low_price')"
          >
            <span class="todoItem bg_red">价格过低</span>
            <el-tooltip placement="top">
              <div slot="content">
                商品单体价/连锁价/拼团价低于近15天成交价过多，请检查价格
              </div>
              <i class="el-icon-warning-outline" />
            </el-tooltip>
            <div class="todoCount">
              {{ todoConfig.lowPriceCount }}
            </div>
          </el-col>
        </el-row>
      </div>
    </template>
    <template v-else>
      <el-row class="todoList">
        <el-col :span="24" style="padding-left: 5px">暂无商品待办</el-col>
      </el-row>
    </template>
    <el-row class="trade">
      <el-col :span="24">
        <div class="tag_title">资质管理</div>
      </el-col>
    </el-row>
    <template
      >
      <div>
        <el-row class="todoList">
          <el-col
            :span="6"
            @click.native="jumpPage('/qualificationManage');commidityClickItem('qualificationApplyUnUploadCount')"
          >
            <span class="todoItem bg_red">客户已申请未上传商品首营资质</span>
            <div class="todoCount">{{ todoConfig.qualificationApplyUnUploadCount }}</div>
          </el-col>
          <el-col
            :span="6"
            @click.native="jumpPage('/drugTestResultManage');commidityClickItem('drugReportApplyUnUploadCount')"
          >
            <span class="todoItem bg_red">客户已申请未上传药检报告</span>
            <div class="todoCount">{{ todoConfig.drugReportApplyUnUploadCount }}</div>
          </el-col>
        </el-row>
      </div>
    </template>
    <!-- <template v-else>
      <el-row class="todoList">
        <el-col :span="24" style="padding-left: 5px">暂无资质待办</el-col>
      </el-row>
    </template> -->
  </div>
</template>

<script>
import { waitDeal, platformActivity, selectPendingHandleNum } from '@/api/home';
import { mapState } from 'vuex';
import { VNodeExposureTool } from '@/utils/exposureTools';
import { actionTracking } from '@/track/eventTracking';
import { signTaskRemind, taskRemind } from '../../../api/home/<USER>'
const transactionMap = {
  waitOpenAccountOrderCount: 'no_opened', // 待开户订单数
  waitExamineOrderCount: 'no_approved', // 待审核订单数
  waitDeliveryOrderCount: 'no_shipped', // 待发货订单数
  timeoutOrderCount: '48h_no_shipped', // 超48小时未发货订单数
  waitExamineRefundOrderCount: 'no_approved_refund', // 待审核退款订单数
  pullFailedOrderCount: 'failed_ERP', // 下推erp失败订单数
  logisticsTrackFailCount: 'failed_track', // 物流轨迹获取失败
  logisticsTrackIllegalCount: 'illegal_track', // 物流轨迹异常提醒
  evidenceToExamine: 'wire_transfer', // 待审核电汇
  nonInvoiceOrderCount: 'no_invoice', // 未上传电子发票
  afterSalesCount: 'no_afterSale'
};

const commodityMap = {
  nearEffectSkuCount: 'near_term', // 近效期商品数
  sellStockOutSkuCount: 'no_stock', // 在售缺货
  expireAutoOutMarketSkuCount: 'outdate', // 即将过期自动下架商品数量
  priceOSkuCount: '0_price', // 售价为0
  waitPutAwaySkuCount: 'prepare_sale', // 待上架商品数量
  noPassSkuCount: 'fail_to_pass', // 审核不通过商品数量
  authOffShelfCount: 'incorrect_information', // 信息有误下架
  errorInfoCount: 'forced_shelve', // 信息有误商品数
  lowPriceCount: 'low_price', // 价格过低
};

let exposureTool = null;

export default {
  name: 'TodoTask',
  data() {
    return {
      todoConfig: {
        waitOpenAccountOrderCount: 0, // 待开户订单数
        waitExamineOrderCount: 0, // 待审核订单数
        waitDeliveryOrderCount: 0, // 待发货订单数
        timeoutOrderCount: 0, // 超48小时未发货订单数
        waitExamineRefundOrderCount: 0, // 待审核退款订单数
        pullFailedOrderCount: 0, // 下推erp失败订单数
        nearEffectSkuCount: 0, // 近效期商品数
        sellStockOutSkuCount: 0, // 在售缺货
        expireAutoOutMarketSkuCount: 0, // 即将过期自动下架商品数量
        priceOSkuCount: 0, // 售价为0
        waitPutAwaySkuCount: 0, // 待上架商品数量
        noPassSkuCount: 0, // 审核不通过商品数量
        authOffShelfCount: 0, // 信息有误下架
        errorInfoCount: 0, // 信息有误商品数
        logisticsTrackFailCount: 0, // 物流轨迹获取失败
        logisticsTrackIllegalCount: 0, // 物流轨迹异常提醒
        evidenceToExamine: 0, // 待审核电汇
        partialShipmentOrderCount: 0,
        lowPriceCount: 0, // 价格过低数量
        nonInvoiceOrderCount: 0, // 未上传电子发票数量
        autoSaleWithStockCount: 0, // 来货自动上架数量
		    afterSalesCount: 0,   //未处理售后单
        applyNationalBusinessCircle: false,   //全国商圈

        qualificationApplyUnUploadCount: 0, //客户已申请未上传商品首营资质
        drugReportApplyUnUploadCount: 0, //客户已申请未上传药检报告
      },
      isBankAccount: false, // true展示企业开户，false不展示
      rejectNum: 0, // 驳回数量
      priceTooLowPrice: 0, // 价格过低数量
      canReportBaseNum: 0, // 可报名框架数量
      dialog: {           //开店任务每日提醒和协议签署每日提醒
        visible: {
          openShop: false,
          contract: false
        },
        contractTableData: [],
        openShopTableData: [],
        openShopTaskNum: 0,
        contractNum: 0
      }
    };
  },
  computed: { ...mapState('app', ['shopConfig', 'qualtificationInfos']) },
  created() {
    console.log(22222);
    this.GetWaitDeal();
    this.getPlatformActivity();
    signTaskRemind().then(res => {
      if (res.code == 0) {
        this.dialog.visible.contract = res.result.notify == 1;
        this.dialog.contractTableData = res.result.signTaskList;
        this.dialog.contractNum = res.result.num;
      }
    })
    taskRemind().then(res => {
      if (res.code == 0) {
        this.dialog.visible.openShop = res.result.notify == 1;
        this.dialog.openShopTableData = res.result.signTaskList;
        this.dialog.openShopTaskNum = res.result.num;
      }
    })
  },
  methods: {
    collageActivityOpening(obj, key) {
      actionTracking('agency_task_activity_click', { agency_task_activity: key });
      window.openTab('/collageActivity', obj || '');
    },
    transactionClickItem(itemName) {
      actionTracking("agency_task_transaction_click", {
        agency_task_transaction : itemName
      })
    },
    commidityClickItem(itemName) {
      actionTracking("agency_task_commodity_click", {
        agency_task_commodity : itemName
      })
    },
    async GetWaitDeal() {
      const res = await waitDeal();
      if (res && res.code === 0) {
        Object.keys(res.result).map((key) => {
          this.$set(this.todoConfig, key, res.result[key]);
        });
        if (exposureTool) {
          exposureTool.end();
        } else {
          exposureTool = new VNodeExposureTool(document.querySelector(".home"), (item)=> {
            let name = item.$vnode.data.ref.replace('exposure_mark_', '');
            // 曝光埋点
            do {
              let content = transactionMap[name]
              if (content) {
                actionTracking('agency_task_transaction_exposure', {
                  agency_task : content
                })
                break;
              }
              content = commodityMap[name]
              if (content) {
                actionTracking('agency_task_commodity_exposure', {
                  agency_task : content
                })
                break;
              }
            } while (false)
          })
        }
        this.$nextTick(()=>{
          let refs = Object.keys(this.$refs).filter((item) => {
            return item.startsWith('exposure_mark')
          }).map((key) => {
            return this.$refs[key]
          })
          exposureTool.begin(refs)
        })
      }
    },
    toStoreAreaSalesControl() {
      if(this.$store.state.permission.menuGray == 1) {
        window.openTab('/shopBusinessManage?to=storeAreaSalesControl')
      }else {
        window.openTab('/storeAreaSalesControl')
      }
    },
    handle(path) {
        window.openTab(path);
    },
    jumpPage(path, params, from) {
      const obj = {};
      // const YMD = this.formatDate(new Date().getTime(), 'YMD')
      // const strList = YMD.split('-')
      const s = dayjs().valueOf();
      const time = [ dayjs().subtract(3, 'month').format('YYYY-MM-DD') + ' 00:00:00', dayjs().format('YYYY-MM-DD') + ' 23:59:59']
      if (path === '/orderList') {
        switch (params) {
          case 1:
            // obj.time = time
              if(from){
                obj.status = '10'
                obj.evidenceToExamine = true
              }else{
                obj.status = '1'
                obj.openAccountStatus = '0'
              }
            break;
          case 2:
            obj.time = time
            obj.status = '1'
            break;
          case 3:
            obj.time = time
            obj.status = '7'
            break;
          case 4:
            obj.status = '1'
            obj.statusList = '[1,7,32,33]'
            break;
          case 5:
            obj.startCreateTime = time[0]
            obj.endCreateTime = time[1]
            path = process.env.VUE_APP_BASE_API + '/afterSales/index'
            obj.auditStateListJson = '[0]'
            obj.branchCode = 'XS000000'
            break;
          case 6:
            obj.time = time
            obj.status = '1'
            obj.orderSyncStatus = '2'
            break;
          case 7:
            obj.logisticsTrackFail = true
            break;
          case 8:
            obj.logisticsTrackIllegal = true
            break;
          case 9:
            obj.partialShipment = true
            break;
          case 'nonInvoiceOrderCount':
            obj.invoiceState = 0
            break;
        }
      }
      if (path === '/productList') {
        switch (params) {
          case 1:
            obj.status = ''
            obj.nearTerm = true
            break
          case 2:
            obj.status = 1
            obj.stockStatus = '0'
            break
          case 3:
            obj.status = ''
            obj.expiredOffShelf = true
            break
          case 4:
            obj.zeroPrice = true
            break
          case 5:
            obj.status = 6
            break
          case 6:
            obj.status = 9
            break
          case 7:
            obj.authOffShelf = true
            break
          case 8:
            obj.needRepair = true
            break
          case 9:
            obj.autoSaleWithStock = true
            break
          case 10:
            obj.lowPrice = true
        }
      }
      if (path === '/afterSaleList') {
        obj.auditState = '0';
        obj.auditStateListJson = '[0]';
      }
      obj.homeEnter = 1;
      window.openTab(path, obj);
    },
    taskActivityViewTrack(valueArr) {
      valueArr.forEach((item) => {
        if (item.value) {
          actionTracking('agency_task_activity_exposure', { agency_task_activity: item.key });
        }
      });
    },
    async getPlatformActivity() {
      const res = await platformActivity();
      if (res && res.code === 0) {
        this.isBankAccount = res.result.bankAccount;
      }
      selectPendingHandleNum().then((res) => {
        if (res.code === 1000) {
          this.rejectNum = res.data.actReportPendingHandleNumVo.rejectNum;
          this.canReportBaseNum = res.data.actReportPendingHandleNumVo.canReportBaseNum;
          this.priceTooLowPrice = res.data.actReportPendingHandleNumVo.priceTooLowPrice;
          this.taskActivityViewTrack([{ key: 'activity_reject', value: this.rejectNum }, { key: 'activity_Participate', value: this.canReportBaseNum }, { key: 'low_price', value: this.priceTooLowPrice }]);
        }
      });
    },
    accountOpening() {
      window.openTab('/companyOpenAccount');
    },
    qualtificationOpening() {
      window.openTab('/qualtification');
    },
  },
};
</script>

<style scoped lang="scss">
.todoTask {
  width: 100%;
  padding: 16px;
  background: #fff;
  border-radius: 2px;
  margin-bottom: 16px;

  .trade {
    margin-top: 15px;
  }

  .todoList {
    margin-top: 16px;

    .el-col {
      cursor: pointer;
      margin-bottom: 16px;
    }
  }

  .todoItem {
    padding-right: 4px;
    font-size: 14px;
    font-family: PingFangSC, PingFangSC-Regular;
    font-weight: 400;
    line-height: 20px;
  }

  .todoItem:before {
    content: '';
    display: inline-block;
    vertical-align: middle;
    width: 6px;
    height: 6px;
    border-radius: 50%;
    margin-right: 4px;
  }

  .todoItem.bg_green:before {
    background: #52c41a;
  }

  .todoItem.bg_blue:before {
    background: #1890ff;
  }

  .todoItem.bg_yellow:before {
    background: #faad14;
  }

  .todoItem.bg_red:before {
    background: #ff4d4f;
  }

  .todoCount {
    font-size: 30px;
    font-family: PingFangSC, PingFangSC-Semibold;
    font-weight: 600;
    color: #333333;
    line-height: 42px;
    padding-left: 10px;
  }
}
.finished {
    margin-right: 10px;
    color:#00c563
}
.doing {
    color:#ff8400;
    margin-right: 10px;
}
.work {
  color:#4183d5;
  text-decoration-line: underline;
  cursor: pointer;
}
</style>
