<template>
  <div>
    <el-dialog title="药帮忙业务员" :visible="dialogVisible" width="40%" @close="closeDialog">
      <div v-if="isShow">
        <div v-if="ybmSalesmanInfo&&ybmSalesmanInfo.hasOwnProperty('phone')">
          <p>
            <span>姓名:{{ ybmSalesmanInfo.name}}</span>
          </p>
          <p>
            <span>电话:{{ ybmSalesmanInfo.phone}}</span>
          </p>
        </div>
        <div v-else>
          <p>暂未分配业务员，如有问题请联系招商经理进行处理</p>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { queryBDInfoByMerchantId } from '@/api/order/index';

export default {
  name: 'YBMSalesmanDialog',
  props: {
    dialogVisible: {
      type: Boolean,
      default: false,
    },
    merchantId: {
      type: Number,
      default: null,
    },
  },
  data() {
    return {
      ybmSalesmanInfo: {},
      isShow:false
    };
  },
  mounted() {
    this.getInfo();
  },
  methods: {
    closeDialog() {
      this.$emit('cancelDialog');
    },
    getInfo() {
      queryBDInfoByMerchantId({ merchantId: this.merchantId }).then((res) => {
        if (res.code === 0) {
          this.isShow = true;
          this.ybmSalesmanInfo = res.result;
        }
      });
    }
  }
};
</script>

<style scoped lang="scss">
.flexBox {
  display: flex;
  align-items: center;
}
::v-deep  .el-dialog__body {
  padding: 10px 20px;
  height: 160px;
}
::v-deep  .el-dialog__header {
  padding: 10px 20px;
  background: #f9f9f9;
}
::v-deep  .el-dialog__headerbtn {
  top: 15px;
}
::v-deep  .el-dialog__title {
  font-size: 16px;
}
</style>
