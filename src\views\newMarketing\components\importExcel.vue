<template>
  <div>
    <el-form label-width="150px" class="importMsg" v-if="importType === 4">
      <groupActivityThemeVue 
        @select="select"
        :importType="importType" 
        :frameReportId="frameReportId"
        :activityInfo="activityInfo"/>
    </el-form>

    <el-form label-width="150px" class="importMsg" v-if="importType === 3">
      <el-form-item label="导入形式">
        <el-row>
          <el-radio v-model="importFormData.importFormat" :label="5">根据活动ID（PT/BM开头）</el-radio>
        </el-row>
        <el-row>
          <el-radio v-model="importFormData.importFormat" :label="6">根据拼团/批购包邮商品编码</el-radio>
        </el-row>
        <el-row>
          <el-radio v-model="importFormData.importFormat" :label="7">根据CSUID</el-radio>
        </el-row>
      </el-form-item>
      <groupActivityThemeVue 
        :importType="importType" 
        @select="select"
        :activityInfo="activityInfo"
        :importFormat="importFormData.importFormat"
        :frameReportId="frameReportId"/>
    </el-form>

    <el-form label-width="140px" class="importMsg" v-if="importType === 2">
      <el-form-item label="导入形式" style="margin-left: 15px">
        <el-radio-group v-model="importFormData.productDataRange" class="productDataRangeOption">
          <el-radio :label="1">店铺近7天动销拼团商品</el-radio>
          <el-radio :label="2">店铺近7天动销批购包邮商品</el-radio>
          <el-radio :label="3">店铺近7天动销拼团商品TOP200</el-radio>
          <el-radio :label="4">店铺近7天动销批购包邮商品TOP200</el-radio>
        </el-radio-group>
        <p style="color: red;margin-top: -15px">提示：选中后自动提取活动，其中单价、起拼数、采购上限、商图、虚拟供应商等数据默认沿用原活动</p>
      </el-form-item>
      <groupActivityThemeVue 
      @select="select"
        :importType="importType" 
        :productDataRange="importFormData.productDataRange"
        :importFormat="importFormData.importFormat"
        :activityInfo="activityInfo"
        :frameReportId="frameReportId"/>
    </el-form>
  </div>
</template>
<script>
import logChangeVue from '../../control-goods/components/logChange.vue'
import groupActivityThemeVue from '../groupActivityTheme.vue'

export default {
  components: {
    groupActivityThemeVue
  },
  props: {
    importType: {
      type: Number,
      default: 0
    },
    frameReportId: {
      default: ""
    },
    activityInfo: {
      type: Object,
      default: function() {return {}}
    }
  },
  watch:{
    activityInfo:{
      handler(val){
        console.log(val,'val');
        
      }
    }
  },
  data() {
    return {
      importFormData: { // 表单数据
        importFormat: 5, // 导入形式选项
        productDataRange: 1 // 店铺商品提报数据范围
      }
    }
  },
  methods: {
    select(data) {
     
      this.$emit('select', data)
    }
  }
}
</script>
<style scoped lang="scss">
.importItem {
  text-align: right;
  .importTitle {
    margin-left: -25px;
    font-weight: 600;
  }
  .importOptions {
    text-align: left;
    padding-left: 20px;
  }
  .tipsFont {
    color: #939393;
    padding: 8px 0;
  }
}
.pTop {
  padding-top: 40px;
}
.productDataRangeOption {
  .el-radio {
    display: block;
    height: 30px;
    margin-top: 10px;
  }
}
</style>