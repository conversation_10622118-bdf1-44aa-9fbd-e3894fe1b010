<template>
  <div class="main-box">
    <part-title title="控销列表" />
    <div class="explain-search searchMy">
      <el-form ref="ruleForm" size="small" :inline="true">
        <el-form-item prop="skuId">
          <el-input v-model.trim="searchData.skuId" oninput="value=value.replace(/[^0-9.]/g,'')" placeholder="sku编码">
            <template slot="prepend">sku编码</template>
          </el-input>
        </el-form-item>
        <el-form-item>
          <el-input v-model.trim="searchData.merchantId" oninput="value=value.replace(/[^0-9.]/g,'')" placeholder="药店ID">
            <template slot="prepend">药店ID</template>
          </el-input>
        </el-form-item>
        <el-form-item>
          <el-input v-model.trim="searchData.id" oninput="value=value.replace(/[^0-9.]/g,'')" placeholder="控销组ID">
            <template slot="prepend">控销组ID</template>
          </el-input>
        </el-form-item>
        <el-form-item>
          <el-input v-model.trim="searchData.name" placeholder="控销组名称">
            <template slot="prepend">控销组名称</template>
          </el-input>
        </el-form-item>
        <el-form-item class="search-btn">
          <el-button type="primary" @click="getList('search')">查询</el-button>
          <el-button @click="resetForm()">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="list-box">
      <div style="padding: 10px 0px">
        <el-row style="padding-bottom: 20px">
          <el-button type="primary" size="small" @click="addControlSell">新增控销组</el-button>
        </el-row>

        <div class="customer-tabs">
          <el-table
            ref="goodTable"
            v-loading="laodingBoole"
            max-height="397"
            :data="tableData.list"
            stripe
            style="width: 100%"
            :header-cell-style="{ background: '#F9F9F9', color: '#666666' }"
          >
            <el-table-column prop="id" label="控销组ID" />
            <el-table-column prop="name" label="控销组名称" />
            <el-table-column prop="merchantNums" label="控销药店">
              <template slot-scope="scope">
                <span class="blueText" @click="onAction('see', scope.row, 'merchantType')">{{ scope.row.merchantNums || 0 }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="skuNums" label="控销商品">
              <template slot-scope="scope">
                <span class="blueText" @click="onAction('see', scope.row, 'skuType')">{{ scope.row.skuNums || 0 }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="createTime" label="创建时间">
              <template slot-scope="scope">
                <span>{{ formatDate(scope.row.createTime) }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="updateTime" label="更新时间">
              <template slot-scope="scope">
                <span>{{ formatDate(scope.row.updateTime) }}</span>
              </template>
            </el-table-column>
            <el-table-column fixed="right" label="操作">
              <template slot-scope="scope">
                <el-button type="text" size="small" @click="onAction('see', scope.row, 'merchantType')">详情 </el-button>
                <el-button type="text" size="small" @click="onAction('edit', scope.row)"> 编辑 </el-button>
                <el-button type="text" size="small" @click="onAction('log', scope.row)"> 查看日志</el-button>
                <el-button type="text" size="small" @click="onAction('delete', scope.row)"> 删除</el-button>
              </template>
            </el-table-column>
          </el-table>
          <div class="explain-pag">
            <Pagination
              v-show="tableData.total > 0"
              :total="tableData.total"
              :page.sync="pageData.pageNum"
              :limit.sync="pageData.pageSize"
              @pagination="getList"
            />
          </div>
        </div>
      </div>
    </div>
    <LogChange
      v-if="showLog"
      :show-log.sync="showLog"
      :id="activeRow.id"
    />
  </div>
</template>
<script>
import Pagination from '@/components/Pagination';
import utils from '@/utils/filter';
import partTitle from '@/components/part-title/index';
import LogChange from './components/logChange'
import {
  getSkuMerchantGroup,
  getControlOne,
} from '@/api/goods/controlGoods.js';
import { apiDeleteControlUserGroup } from '@/api/storeManagement/blacklist.js';


export default {
  name: 'ControlList',
  components: {
    Pagination,
    partTitle,
    LogChange,
  },
  data() {
    return {
      searchData: {
        id: '',
        name: '',
        skuId: '',
        merchantId: '',
      },
      tableData: {
        total: 0,
        list: [],
      },
      pageData: {
        pageSize: 10,
        pageNum: 1,
      },
      laodingBoole: false,
      showLog: false,
      activeRow: {},
    }
  },
  created() {},
  activated() {
    // 获取列表数据
    this.getList();
  },
  methods: {
    // 获取列表数据
    getList(from) {
      const that = this;
      if (from == 'search') {
        this.pageData.pageNum = 1;
      }
      this.laodingBoole = true;
      const param = {
        showStatistics: true,
        ...this.searchData,
        ...this.pageData
      };
      getSkuMerchantGroup(param).then((res) => {
        console.log('列表数据', res);
        if (res.code === 0) {
          this.laodingBoole = false;
          if (res.data) {
            that.tableData.list = res.data.list;
          } else {
            that.tableData.list = [];
          }
          that.tableData.total = res.data.total;
        } else {
          this.laodingBoole = false;
          this.$message({
            message: res.message,
            type: 'error',
          });
        }
      }).catch(() => {
        this.laodingBoole = false;
      });
    },
    onAction(type, row, activeName) {
  this.activeRow = row;
  console.log('???', this.activeRow);
  if (type === 'log') {
    this.showLog = true;
  } else if (type === 'edit') {
    this.$router.push(`/addControlGroup?groupId=${row.id}`);
  } else if (type === 'see') {
    this.$router.push(`/checkControlGroup?activeName=${activeName}&groupId=${row.id}`);
  } else if (type === 'delete') {
    if (row.skuNums === 0) {
      this.$confirm('当前控销组未关联商品，允许删除', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.laodingBoole = true;
        apiDeleteControlUserGroup(row.id).then(res => {
          this.laodingBoole = false;
          if (res.code === 0) {
            this.$message({
              type: 'success',
              message: '删除成功'
            });
            this.getList();
          } else {
            this.$message({
              type: 'error',
              message: res.message || '删除失败'
            });
          }
        }).catch(() => {
          this.laodingBoole = false;
          this.$message({
            type: 'error',
            message: '删除失败'
          });
        });
      }).catch(() => {
      });
    } else {
      this.$message({
        type: 'error',
        message: '当前控销组已关联商品，禁止删除'
      });
    }
  }
},
    // 重置列表数据
    resetForm() {
      console.log('重置');
      this.searchData = {
        id: '',
        name: '',
        skuId: '',
        merchantId: '',
      };
      this.pageData = {
        pageSize: 10,
        pageNum: 1,
      };
      this.getList();
    },
    // 添加控销
    addControlSell() {
      this.$router.push('/addControlGroup');
    },
  }
}
</script>

<style lang="scss" scoped>
.searchMy ::v-deep  .el-form-item__label {
  margin-left: 20px;
  padding: 0;
}
.searchMy ::v-deep  .el-input__inner {
  border-radius: 0 4px 4px 0;
}
.searchMy ::v-deep  .el-form-item__content {
  width: 100%;
}
.searchMy ::v-deep  .el-form-item--small.el-form-item {
  margin-bottom: 10px;
  width: 32%;
}
.searchMy ::v-deep  .el-form-item--small .el-form-item__content {
  line-height: 30px;
  width: 100%;
}
.searchMy ::v-deep  .el-input-group__prepend {
  background: none;
  color: #333333;
  padding: 0 10px;
}
.searchMy ::v-deep   .el-form-item--small.el-form-item {
  width: 24%;
}
.blueText {
  color: #4183d5;
  cursor: pointer;
}
.main-box {
  padding: 20px;
  .list-box {
    .customer-tabs {
      .el-button + .el-button {
        margin-left: 0px;
      }
    }
  }
}
.search-btn {
  width: 100% !important;
  text-align: right;
}
</style>