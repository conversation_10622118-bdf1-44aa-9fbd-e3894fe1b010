<template>
  <div class="freeGroup">
    <div
      class="description"
      @click="descriptionVis = true"
    >
      <img src="../../assets/image/marketing/freeGroupTip.png">
    </div>
    <div style="padding: 10px 0 0 0">
      <el-form
        ref="ruleForm"
        :model="ruleForm"
        size="small"
        label-width="150px"
      >
        <el-form-item>
          <span slot="label">
            <span class="sign" />
            <div class="searchMsg">
              随心拼是否开启
            </div>
          </span>
          <span
            class="openBtns"
          >
            <!-- <span
              v-if="ruleForm.status === 2 && radioChangeDisabled"
              class="btn"
              @click="radioChangeDisabled = false;ruleForm.status === 1 ? editDisabled = false : ''"
            >开启</span> -->
            <span
              v-if="radioChangeDisabled"
              class="btn"
              @click="radioChangeDisabled = false;editDisabled = false"
            >修改</span>
            <span
              v-if="radioChangeDisabled"
              class="btn"
              style="margin-left: 20px;"
              @click="handleCheckLog(logParams, true)"
            >查看日志</span>
          </span>
        </el-form-item>
        <div
          class="more"
        >
          <el-form
            ref="ruleForm1"
            :model="ruleForm"
            :rules="rules"
            size="small"
            label-width="150px"
          >
		  	<el-form-item label="拼团商品" prop="status">
				<el-radio-group
					v-model="ruleForm.status"
					:disabled="radioChangeDisabled"
				>
					<el-radio :label="2">否</el-radio>
					<el-radio :label="1">是</el-radio>
				</el-radio-group>
			</el-form-item>
			<el-form-item label="批购包邮商品"  prop="pgStatus">
				<el-radio-group
					v-model="ruleForm.pgStatus"
					:disabled="radioChangeDisabled"
				>
					<el-radio :label="2">否</el-radio>
					<el-radio :label="1">是</el-radio>
				</el-radio-group>
			</el-form-item>
            <el-form-item
              label="转换系数"
              prop="discountRatio"
              :rules="[
                {
                  required: true,
                  validator: checkAiscountRatio,
                  trigger: 'blur',
                },
              ]"
            >
              <el-input
                v-model="ruleForm.discountRatio"
                placeholder=""
                style="width:200px;"
                :disabled="editDisabled"
              />
              <p class="text">对拼团商品、批购包邮商品均生效；</p>
            </el-form-item>
            <el-form-item
              label="启用时间"
            >
              <label slot="label"><span style="color:red">*</span>  启用时间</label>
              <el-radio-group
                v-model="ruleForm.segmentType"
                :disabled="editDisabled"
                @change="changeFullReductionType"
              >
                <el-radio :label="1">每天(开启后每天都可参与随心拼)</el-radio>
                <el-radio :label="2">每周-至周五(启用后每周一至周五参与随心拼，周末不参与)</el-radio>
                <el-radio :label="3">自定义</el-radio>
              </el-radio-group>
              <div
                v-if="ruleForm.segmentType === 3"
                style="padding-left: 24px;"
              >
                <el-checkbox-group
                  v-model="ruleForm.segmentList"
                  :disabled="editDisabled"
                >
                  <el-checkbox :label="1">周一</el-checkbox>
                  <el-checkbox :label="2">周二</el-checkbox>
                  <el-checkbox :label="3">周三</el-checkbox>
                  <el-checkbox :label="4">周四</el-checkbox>
                  <el-checkbox :label="5">周五</el-checkbox>
                  <el-checkbox :label="6">周六</el-checkbox>
                  <el-checkbox :label="7">周日</el-checkbox>
                </el-checkbox-group>
              </div>
			  <p style="color:red;margin:0px;">提示：对拼团商品、批购包邮商品均生效；</p>
            </el-form-item>
            <el-form-item
              label="指定随心拼商品"
            >
              <span>设置后将优先推荐配置的指定商品在随心拼区域展示,若配置的商品客户不可买则将不展示，建议配置商品类型为普通商品且状态为销售。(最多设置5个)</span>
              <div>
                <el-input
                  v-model="searchSkuId"
                  placeholder="商品编码"
                  style="width:200px;"
                  :disabled="editDisabled"
                />
                <el-button
                  type="primary"
                  style="margin-left: 10px;"
                  :disabled="editDisabled"
                  @click="handleAddSku"
                >
                  添加商品
                </el-button>
                <div class="talbe">
                  <xyy-table
                    :data="ruleForm.marketingGroupFollowHeartSkuTopDTOList"
                    :col="col"
                    :is-pagination="false"
                  >
                    <template
                      slot="fob"
                      slot-scope="{ col }"
                    >
                      <el-table-column
                        :key="col.index"
                        :label="col.name"
                        :width="col.width"
                      >
                        <template slot-scope="scope">
                          <p>单体采购价：{{ scope.row.fob }}</p>
                          <p>连锁采购价：{{ scope.row.guidePrice }}</p>
                        </template>
                      </el-table-column>
                    </template>
                    <template
                      slot="operate"
                      slot-scope="{ col }"
                    >
                      <el-table-column
                        :key="col.index"
                        :label="col.name"
                        :width="col.width"
                      >
                        <template v-if="!editDisabled" slot-scope="scope">
                          <p
                            style="color: #4184D5; cursor: pointer;margin: 0"
                            @click="handleDeleteSku(scope.$index)"
                          >删除</p>
                          <p
                            v-if="scope.$index > 0"
                            style="color: #4184D5; cursor: pointer;margin: 0"
                            @click="handleMove(scope.$index, scope.row, 'up')"
                          >上移</p>
                          <p
                            v-if="scope.$index > 0 && scope.$index < ruleForm.marketingGroupFollowHeartSkuTopDTOList.length -1"
                            style="color: #4184D5; cursor: pointer;margin: 0"
                            @click="handleMove(scope.$index, scope.row, 'down')"
                          >下移</p>
                        </template>
                      </el-table-column>
                    </template>
                  </xyy-table>
                </div>
              </div>
            </el-form-item>
          </el-form>
        </div>
        <div
          v-if="!radioChangeDisabled"
          style="margin: 15px 0 10px 217px;"
        >
          <el-button
            @click="handleCancel"
          >
            取消
          </el-button>
          <el-button
            type="primary"
            style="margin-left: 10px;"
            @click="handleSubmit"
          >
            提交
          </el-button>
        </div>
        <el-form-item
          prop="status"
          style="text-align: left;"
        >
          <span slot="label">
            <span class="sign" />
            <div class="searchMsg">
              随心拼黑名单
            </div>
          </span>
          <div class="black">
            <p>
              <span>预包装商品或者商业ERP无法支持一个ERP编码在一笔订单中，可设置黑名单。设置后，黑名单内的商品将不会与其他商品搭配销售，最多允许添加{{ importBlackListSkuMaxCount }}个。</span>
              示例：拼团商品在黑名单内，则在APP端拼团商品详情页不展示随心拼；若普通商品在黑名单内，则不展示在店铺随心拼或者待确认订单随心拼区域内，不参与随心拼活动。
            </p>
            <div class="searchMy">
              <el-form
                ref="listQuery"
                :model="listQuery"
                :inline="true"
                size="small"
              >
                <el-form-item>
                  <el-input
                    v-model="listQuery.code"
                    placeholder="sku编码/商品编码/ERP编码”查询，精确查询"
                  >
                    <template slot="prepend">
                      编码查询
                    </template>
                  </el-input>
                </el-form-item>
                <el-form-item>
                  <el-input
                    v-model="listQuery.showName"
                    placeholder="请输入"
                  >
                    <template slot="prepend">
                      商品名称
                    </template>
                  </el-input>
                </el-form-item>
                <el-form-item
                  class="btn-item"
                  style="width: 150px;text-align: right;padding-right: 20px"
                >
                  <el-button
                    @click="reset"
                  >
                    重置
                  </el-button>
                  <el-button
                    type="primary"
                    @click="getBlackSkuList(listQuery, true)"
                  >
                    查询
                  </el-button>
                </el-form-item>
              </el-form>
            </div>
            <div class="btns">
              <el-button
                type="primary"
                @click="handleAddBlackSku"
              >
                添加商品
              </el-button>
              <el-button
                @click="batchDialogVis = true;batchType = 'add'"
              >
                批量导入
              </el-button>
              <el-button
                @click="batchDialogVis = true;batchType = 'delete'"
              >
                批量导入删除
              </el-button>
              <el-button
                @click="handleBatchDeleteBlackSku"
              >
                批量删除
              </el-button>
              <el-button
                @click="handleExportBlackList"
              >
                导出
              </el-button>
            </div>
            <div class="talbe">
              <xyy-table
                :data="blackSkuList"
                :list-query="listQuery"
                :col="col"
                :has-selection="true"
                @selectionCallback="selectItems"
                @get-data="getBlackSkuList"
              >
                <template
                  slot="fob"
                  slot-scope="{ col }"
                >
                  <el-table-column
                    :key="col.index"
                    :label="col.name"
                    :width="col.width"
                  >
                    <template slot-scope="scope">
                      <p>单体采购价：{{ scope.row.fob }}</p>
                      <p>连锁采购价：{{ scope.row.guidePrice }}</p>
                    </template>
                  </el-table-column>
                </template>
                <template
                  slot="operate"
                  slot-scope="{ col }"
                >
                  <el-table-column
                    :key="col.index"
                    :label="col.name"
                    :width="col.width"
                  >
                    <template slot-scope="scope">
                      <span
                        style="color: #4184D5; cursor: pointer;"
                        @click="deleteBlackListSku(scope.row)"
                      >删除</span>
                    </template>
                  </el-table-column>
                </template>
              </xyy-table>
            </div>
          </div>
        </el-form-item>
      </el-form>
    </div>
    <el-dialog
      title="操作日志"
      :visible="dialogVisible"
      width="50%"
      @close="dialogVisible = false"
    >
      <xyy-table
        :data="logList"
        :list-query="logParams"
        :col="logCol"
        @get-data="handleCheckLog"
      >
        <template
          slot="operateContent"
          slot-scope="{ col }"
        >
          <el-table-column
            :key="col.index"
            :label="col.name"
            :width="col.width"
          >
            <template slot-scope="{row}">
              <div v-if="row.operateContent.length">
                <p
                  v-for="(item, index) in row.operateContent"
                  :key="index"
                >
                  {{ item.fieldName }}：<span style="color: red;">{{ item.before }}</span>调整为<span style="color: red;">{{ item.after }}</span>
                </p>
              </div>
              <div v-else>无变化</div>
            </template>
          </el-table-column>
        </template>
        <template
          slot="operate"
          slot-scope="{ col }"
        >
          <el-table-column
            :key="col.index"
            :label="col.name"
            :width="col.width"
          >
            <template slot-scope="{row}">
              {{ row.operateUserName }}<span v-if="row.operateUserRealName">/</span>{{ row.operateUserRealName }}
            </template>
          </el-table-column>
        </template>
        <template
          slot="time"
          slot-scope="{ col }"
        >
          <el-table-column
            :key="col.index"
            :label="col.name"
            :width="col.width"
          >
            <template slot-scope="{row}">
              {{ formatDate(row.operateTime,'YMD HMS') }}
            </template>
          </el-table-column>
        </template>
      </xyy-table>
      <div slot="footer">
        <el-button
          type="primary"
          size="small"
          @click="dialogVisible = false"
        >
          关闭
        </el-button>
      </div>
    </el-dialog>
    <changeGoods
      ref="changeGoods"
      @setChooseGoods="setChooseGoods"
    />
    <el-dialog
      v-loading="loading"
      :title="`批量${batchType === 'add' ? '导入' : '删除'}黑名单商品`"
      :visible="batchDialogVis"
      width="50%"
      @close="batchDialogVis = false;batchFile = '';fileList = [];"
    >
      <el-upload
        ref="upload"
        action=""
        class="upload-box"
        accept=".xls,.xlsx"
        :on-change="uploadChange"
        :before-remove="remove"
        :file-list="fileList"
        :limit="1"
        :auto-upload="false"
      >
        <el-button
          type="primary"
          size="small"
        >
          选择文件
        </el-button>
        <el-button
          slot="tip"
          style="margin-left: 50px"
          class="downloadTemplate"
          size="small"
          @click="downloadImportTemplate"
        >
          模板下载
        </el-button>
      </el-upload>
      <p
        v-if="!batchFile"
        style="padding: 10px 0 0 0"
      >
        未选择文件
      </p>
      <span slot="footer">
        <el-button
          size="small"
          @click="batchDialogVis = false;batchFile = '';fileList = [];"
        >关 闭</el-button>
        <el-button
          type="primary"
          size="small"
          @click="handleBatchSubmit"
        >提 交</el-button>
      </span>
    </el-dialog>
    <el-dialog
      title="随心拼活动配置说明"
      :visible="descriptionVis"
      width="50%"
      @close="descriptionVis = false"
    >
      <div>
        <img style="width: 100%" src="../../assets/image/marketing/descriptionNew.png">
      </div>
      <div slot="footer">
        <el-button
          type="primary"
          size="small"
          @click="descriptionVis = false"
        >
          知道了
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { selectOperationLog, addOrUpdate, selectDetailByShopCode, getCsuByBarcode, selectBlackList, addBlackListSku, deleteBlackListSku, importBlackListSku, deleteBlackListSkuByExcel } from '@/api/market/freeGroup';
import changeGoods from './components/changeGoods';

export default {
  name: 'FreeGroup',
  components: { changeGoods },
  data() {
    return {
      importBlackListSkuMaxCount: '',
      backupInfo: null,
      followHeartTopSkuMaxCount: '',
      conversionFactor: '',
      descriptionVis: false,
      batchSelectedList: [],
      batchType: '',
      loading: false,
      batchFile: '',
      fileList: [],
      batchDialogVis: false,
      marketingId: '',
      logList: [],
      logParams: {
        page: 1,
        pageNum: 1,
        pageSize: 10,
        total: 0,
      },
      dialogVisible: false,
      editDisabled: true,
      hasSeted: false,
      radioChangeDisabled: true,
      searchSkuId: '',
      blackSkuList: [],
      listQuery: {
        code: '',
        showName: '',
        page: 1,
        pageNum: 1,
        pageSize: 10,
        total: 0,
      },
      rules: {
        required: [
          { required: true, message: '请输入', trigger: 'blur' },
        ],
        discountRatio: [
          { required: true, message: '请输入', trigger: 'blur' },
        ],
      },
      ruleForm: {
        id: '',
        status: 2,
		pgStatus: 2,
        discountRatio: '',
        segmentType: 1,
        segmentList: [],
        marketingGroupFollowHeartSkuTopDTOList: [],
      },
      col: [
        {
          index: 'id',
          name: 'SKU编码',
        },
        {
          index: 'barcode',
          name: '商品编码',
        },
        {
          index: 'productCode',
          name: 'ERP编码',
        },
        {
          index: 'showName',
          name: '商品名称',
        },
        {
          index: 'spec',
          name: '规格',
        },
        {
          index: 'manufacturer',
          name: '厂商',
        },
        {
          index: 'fob',
          name: '价格',
          slot: true,
        },
        {
          index: 'productStatus',
          name: '商品状态',
        },
        {
          index: 'productType',
          name: '商品类型',
        },
        {
          index: 'operate',
          name: '操作',
          slot: true,
        },
      ],
      logCol: [
        {
          index: 'operateContent',
          name: '变更内容',
          slot: true,
        },
        {
          index: 'operate',
          name: '操作人',
          slot: true,
        },
        {
          index: 'time',
          name: '操作时间',
          slot: true,
        },
      ],
    };
  },
  created() {
    this.getDetail();
    const { followHeartTopSkuMaxCount, conversionFactor, importBlackListSkuMaxCount } = this.$route.query;
    this.followHeartTopSkuMaxCount = followHeartTopSkuMaxCount;
    this.conversionFactor = conversionFactor;
    this.importBlackListSkuMaxCount = importBlackListSkuMaxCount;
  },
  activated() {
    this.getDetail();
    const { followHeartTopSkuMaxCount, conversionFactor, importBlackListSkuMaxCount } = this.$route.query;
    this.followHeartTopSkuMaxCount = followHeartTopSkuMaxCount;
    this.conversionFactor = conversionFactor;
    this.importBlackListSkuMaxCount = importBlackListSkuMaxCount;
  },
  methods: {
    handleMove(index, row, type) {
      this.ruleForm.marketingGroupFollowHeartSkuTopDTOList[index] = this.ruleForm.marketingGroupFollowHeartSkuTopDTOList.splice((type === 'up' ? index - 1 : index + 1), 1, row)[0];
    },
    checkAiscountRatio(rule, value, callback) {
      const arr = this.conversionFactor.split('~');
      if (value === '') {
        callback(new Error('请输入转换系数'));
      }
      if (!/^(0|[1-9]\d*)(.\d{1,2})?$/.test(value)) {
        callback(new Error('仅支持填写2位小数的正数'));
      }
      if (+value >= +arr[0] && +value <= +arr[1]) {
        callback();
      } else {
        callback(new Error(`数据范围为【${arr[0]}, ${arr[1]}】`));
      }
    },
    changeFullReductionType(val) {
      if (val !== 3) {
        this.ruleForm.segmentList = [];
      }
    },
    handleExportBlackList() {
      const params = {};
      params.code = this.listQuery.code;
      params.showName = this.listQuery.showName;
      if (this.marketingId) {
        params.marketingId = this.marketingId;
      }
      // exportBlackListSku(params).then((res) => {
      //   const date = new Date();
      //   const dateStr = `${date.getFullYear()}${date.getMonth() + 1 < 10 ? `0${date.getMonth() + 1}` : date.getMonth() + 1}${date.getDay() < 10 ? `0${date.getDay()}` : date.getDay()}`;
      //   this.util.exportExcel(res, `随心拼黑名单${dateStr}.xls`);
      // });
      const url = `${process.env.VUE_APP_BASE_API}/groupFollowHeart/exportBlackListSku${this.getParams(params)}`;
      const a = document.createElement('a');
      a.href = url;
      a.click();
    },
    getParams(params) {
      let queryStr = '?';
      Object.keys(params).forEach((key) => {
        queryStr += `${key}=${params[key]}&`;
      });
      queryStr = queryStr.substr(0, queryStr.length - 1);
      return queryStr;
    },
    handleBatchDeleteBlackSku() {
      if (!this.batchSelectedList.length) {
        this.$message.warning('请先选择要删除的商品');
        return false;
      }
      this.$confirm('确定删除选择的商品？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: '',
        customClass: 'delete-dialog-customClass',
      }).then(() => {
        const params = {};
        params.actSkuIdList = this.batchSelectedList.map(item => (item.skuId || item.id)).join(',');
        if (this.marketingId) {
          params.marketingId = this.marketingId;
        }
        // params.actSkuIdList = JSON.stringify(params.actSkuIdList);
        deleteBlackListSku(params).then((res) => {
          if (res.code === 1000) {
            this.$message.success(res.msg);
            this.getBlackSkuList(this.listQuery, true);
          } else {
            this.$message.error(res.msg);
          }
        });
      }).catch(() => {
      });
    },
    downloadImportTemplate() {
      let url = '';
      if (this.batchType === 'add') {
        url = `${process.env.VUE_APP_BASE_API}/groupFollowHeart/downloadImportBlackListSkuTemplate`;
      } else {
        url = `${process.env.VUE_APP_BASE_API}/groupFollowHeart/downloadDeleteBlackListSkuTemplate`;
      }
      const a = document.createElement('a');
      a.href = url;
      a.click();
    },
    async handleBatchSubmit() {
      if (!this.batchFile) {
        this.$message.warning('请先上传文件');
        return false;
      }
      const fd = new FormData(); // FormData 对象
      fd.append('file', this.batchFile.raw); // 文件对象
      if (this.marketingId) {
        fd.append('marketingId', this.marketingId);
      }
      let res = '';
      this.loading = true;
      if (this.batchType === 'add') {
        res = await importBlackListSku(fd);
      } else {
        res = await deleteBlackListSkuByExcel(fd);
      }
      this.loading = false;
      if (res.code === 1000) {
        const { data } = res;
        if (data.marketingId) {
          this.marketingId = data.marketingId;
        }
        const { successNum, failNum, url } = data.batchImportResult;
        // const baseUrl = process.env.VUE_APP_BASE_API;
        const h = this.$createElement;
        this.$msgbox({
          title: '上传文件反馈',
          message: h('p', null, [
            h('span', null, `批量${this.batchType === 'add' ? '添加' : '删除'}黑名单成功${successNum}条，失败${failNum}条数据${failNum ? '，下载错误文件：' : ''}`),
            failNum ? h('a', { attrs: { href: url, download: '错误文件', style: 'color: #4183d5;' } }, '错误文件') : '']),
          confirmButtonText: '确定',
        }).then(() => {
          this.batchDialogVis = false;
        });
      } else {
        this.$message.error(res.msg);
      }
      this.batchFile = '';
      this.fileList = [];
      this.getBlackSkuList(this.listQuery, true);
    },
    remove() {
      this.batchFile = '';
      this.fileList = [];
    },
    uploadChange(file) {
      if (file.status === 'ready') {
        const extension = file.name.split('.')[1] === 'xls';
        const extension2 = file.name.split('.')[1] === 'xlsx';
        // file.size / 1024 / 1024 < 3
        const isLt2M = file.size / 1024 / 1024 < 3;
        if (!extension && !extension2) {
          this.fileList = [];
          this.$message.warning('上传模板只能是 xls、xlsx格式!');
          return;
        }
        if (!isLt2M) {
          this.fileList = [];
          this.$message.warning('文件过大，最大支持3M!');
          return;
        }
        this.batchFile = file;
      }
    },
    deleteBlackListSku(row) {
      this.$confirm('确定删除该商品？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: '',
        customClass: 'delete-dialog-customClass',
      }).then(() => {
        const params = {};
        params.actSkuIdList = row.id;
        if (this.marketingId) {
          params.marketingId = this.marketingId;
        }
        deleteBlackListSku(params).then((res) => {
          if (res.code === 1000) {
            this.$message.success(res.msg);
            this.getBlackSkuList(this.listQuery, true);
          } else {
            this.$message.error(res.msg);
          }
        });
      }).catch(() => {
      });
    },
    setChooseGoods(list) {
      if (list.length) {
        const params = { skuIdList: list.map(item => item.id).join(',') };
        if (this.marketingId) {
          params.marketingId = this.marketingId;
        }
        addBlackListSku(params).then((res) => {
          if (res.code === 1000) {
            const { data } = res;
            this.marketingId = data.marketingId;
            this.getBlackSkuList(this.listQuery, true);
          } else {
            this.$message.error(res.msg);
          }
        });
      }
    },
    handleAddBlackSku() {
      this.$refs.changeGoods.getList('', []);
    },
    reset() {
      this.listQuery.code = '';
      this.listQuery.showName = '';
      this.getBlackSkuList(this.listQuery, true);
    },
    handleDeleteSku(index) {
      this.$confirm('确定删除选择的商品？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: '',
        customClass: 'delete-dialog-customClass',
      }).then(() => {
        this.ruleForm.marketingGroupFollowHeartSkuTopDTOList.splice(index, 1);
      }).catch(() => {
      });
    },
    handleAddSku() {
      if (this.ruleForm.marketingGroupFollowHeartSkuTopDTOList.legnt === this.followHeartTopSkuMaxCount) {
        this.$message.warning(`最多允许指定${this.followHeartTopSkuMaxCount}个商品`);
        return false;
      }
      if (this.searchSkuId === '') {
        this.$message.warning('请输入商品编码');
        return false;
      }
      getCsuByBarcode({ barcode: this.searchSkuId }).then((res) => {
        if (res.code === 1000) {
          this.$message.success(res.msg || '添加成功');
          res.data.csu.skuId = res.data.csu.id;
          if (this.ruleForm.marketingGroupFollowHeartSkuTopDTOList.length) {
            const hasSku = this.ruleForm.marketingGroupFollowHeartSkuTopDTOList.find((item) => {
              return item.id === res.data.csu.id;
            });
            if (!hasSku) {
              this.ruleForm.marketingGroupFollowHeartSkuTopDTOList.push(res.data.csu);
            }
          } else {
            this.ruleForm.marketingGroupFollowHeartSkuTopDTOList.push(res.data.csu);
          }
          this.searchSkuId = '';
        } else {
          this.$message.error(res.msg || '添加失败');
        }
      });
    },
    handleSubmit() {
      let canSubmit = true;
      if (this.$refs.ruleForm1) {
        this.$refs.ruleForm1.validate((valid) => {
          canSubmit = valid;
        });
      }
      if (canSubmit) {
        if (this.ruleForm.segmentType === 3) {
          if (!this.ruleForm.segmentList.length) {
            this.$message.warning('请选择自定义时间范围');
            return false;
          }
          if (this.ruleForm.segmentList.length === 7) {
            this.$message.warning('请选择每天');
            return false;
          }
          let nonWeekend = true;
          this.ruleForm.segmentList.forEach((item) => {
            if (![1, 2, 3, 4, 5].includes(item)) {
              nonWeekend = false;
            }
          });
          if (nonWeekend && this.ruleForm.segmentList.length === 5) {
            this.$message.warning('请选择每周一至周五');
            return false;
          }
        }
        const params = JSON.parse(JSON.stringify(this.ruleForm));
        if (!params.id) {
          delete params.id;
        }
        params.marketingGroupFollowHeartSkuTopDTOList.forEach((item, index) => {
          item.sort = index + 1;
        });
        addOrUpdate(params).then((res) => {
          if (res.code === 1000) {
            this.$message.success(res.msg);
            this.editDisabled = true;
            this.radioChangeDisabled = true;
            this.backupInfo = JSON.parse(JSON.stringify(this.ruleForm));
            this.getDetail();
            // this.savedStatus = this.ruleForm.status;
          } else {
            this.$message.error(res.msg);
          }
        });
      }
    },
    handleCheckLog(logParams, reset) {
      const { page, pageSize } = logParams;
      this.logParams.pageSize = pageSize;
      this.logParams.pageNum = reset ? 1 : page;
      this.logParams.page = reset ? 1 : page;
      selectOperationLog({ pageNum: this.logParams.pageNum, pageSize: this.logParams.pageSize }).then((res) => {
        if (res.code === 1000) {
          const { data } = res;
          const { operationPageInfo } = data;
          logParams.total = operationPageInfo ? operationPageInfo.total : 0;
          this.logList = operationPageInfo ? operationPageInfo.list : [];
          this.dialogVisible = true;
        }
      });
    },
    handleCancel() {
      this.editDisabled = true;
      this.radioChangeDisabled = true;
      this.ruleForm = JSON.parse(JSON.stringify(this.backupInfo));
      // this.ruleForm.status = this.savedStatus;
    },
    handleChangeStatus(val) {
      if (+val === 1) {
        this.editDisabled = false;
      } else {
        this.editDisabled = true;
      }
    },
    selectItems(arr) {
      this.batchSelectedList = arr;
    },
    getBlackSkuList(listQuery, reset) {
      const loading = this.$loading({
        lock: true,
        text: 'Loading',
        spinner: 'el-icon-loading',
        background: 'rgba(255,255,255, 0.8)',
      });
      const { page, pageSize } = listQuery;
      this.listQuery.pageSize = pageSize;
      this.listQuery.pageNum = reset ? 1 : page;
      this.listQuery.page = reset ? 1 : page;
      const params = JSON.parse(JSON.stringify(this.listQuery));
      if (this.marketingId) {
        params.marketingId = this.marketingId;
      }
      delete params.total;
      delete params.page;
      selectBlackList(params).then((res) => {
        loading.close();
        if (res.code === 1000) {
          const { data } = res;
          const { pageInfo } = data;
          this.blackSkuList = pageInfo.list || [];
          this.listQuery.total = pageInfo.total;
          if (this.blackSkuList && this.blackSkuList.length) {
            this.blackSkuList.forEach((item) => {
              if (!item.id) {
                item.id = item.skuId;
              }
            });
          }
        }
      });
    },
    getDetail() {
      selectDetailByShopCode().then((res) => {
        if (res.code === 1000) {
          const { data } = res;
          const { actDetail } = data;
          if (actDetail) {
            this.ruleForm = {
              id: actDetail.id || '',
              status: actDetail.status || 2,
			  pgStatus: actDetail.pgStatus || 2,
              discountRatio: actDetail.discountRatio || '',
              segmentType: actDetail.segmentType || 1,
              segmentList: actDetail.segmentList || [],
              marketingGroupFollowHeartSkuTopDTOList: actDetail.marketingGroupFollowHeartSkuTopDTOList || [],
            };
            // this.savedStatus = actDetail.status || 2;
            this.marketingId = actDetail.id || '';
            this.getBlackSkuList(this.listQuery, true);
            if (this.ruleForm.marketingGroupFollowHeartSkuTopDTOList.length) {
              this.ruleForm.marketingGroupFollowHeartSkuTopDTOList.forEach((item) => {
                if (!item.id) {
                  item.id = item.skuId;
                }
              });
            }
          }
          this.backupInfo = JSON.parse(JSON.stringify(this.ruleForm));
        } else {
          this.$message.error(res.msg);
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.freeGroup {
  padding: 15px;
  .description {
    cursor: pointer;
    width: 420px;
    height: 92px;
    img {
      display: block;
      width: 100%;
    }
  }
  .searchMsg {
    font-weight: 700;
    width: 200px;
    font-size: 16px;
    display: inline;
  }
  .sign {
    display: inline-table;
    width: 3px;
    height: 13px;
    background: linear-gradient(360deg, #1d69c4 1%, #8bbdfc 99%);
    border-radius: 2px;
    margin-right: 8px;
  }
  .openBtns {
    padding-left: 40px;
    .btn {
      font-size: 12px;
      color: #4184D5;
      text-decoration: underline;
      cursor: pointer;
    }
  }
  ::v-deep  .el-form-item__label {
   text-align: left;
 }
 .more ::v-deep  .el-form-item__label {
  text-align: right;
 }
 .more ::v-deep  .el-radio {
  display: block;
  line-height: 30px;
 }
 .black {
  p {
    margin-top: 1px;
    font-weight: 400;
    font-size: 12px;
    letter-spacing: 0;
    line-height: 30px;
    span {
      color: #FF0F0F;
    }
  }
  .search-title {
    display: table-cell;
    padding: 0 10px;
    text-align: center;
    border: 1px solid #dcdfe6;
    height: 30px;
    line-height: 30px;
    vertical-align: middle;
    border-right: none;
    border-radius: 4px 0 0 4px;
    color: #333333;
    white-space: nowrap;
  }
  .btns {
    padding-top: 10px;
  }
 }
 .more {
    padding-left: 67px;
    .text {
      margin: 0;
      font-weight: 400;
      font-size: 12px;
      color: #F51212;
    }
  }
  .talbe {
    padding-top: 10px;
  }
  ::v-deep  .el-table {
    &::before,
    ::before {
      background-color: #fff;
    }
    font-family: PingFangSC-Regular;
    font-weight: 400;
    color: #292933;
    border-radius: 2px;
    border: 1px solid #e4e4eb;
    tr {
      td {
        border-right: 1px solid #e4e4eb;
        .cell {
          padding-left: 20px;
          padding-right: 20px;
        }
        &.el-table-column--selection {
          .cell {
            padding: 0 10px;
          }
        }
      }
    }

    thead {
      color: #292933;
      th {
        height: 40px;
        padding: 0;
        font-weight: 400;
        background: #eeeeee;
        border: none;
        border-right: 1px solid #e4e4eb;
      }
    }
    tr {
      td {
        height: 40px;
        padding: 0;
        background: #fff;
        border-bottom: 1px solid #ebeef5;
        .cell {
          height: 100%;
          display: flex;
          flex-direction: column;
          justify-content: center;
          white-space: break-spaces;
          font-size: 12px;
          font-family: PingFangSC, PingFangSC-Regular;
          font-weight: 400;
          text-align: left;
          line-height: 22px;
          padding: 8px 16px;
        }
      }
    }
    .el-table__body {
      // 隔行变色
      tr.el-table__row--striped {
        td {
          background: #ffffff;
        }
      }
    }
    .table-row,
    .table-header-row {
      height: 40px;
    }
  }
}
</style>
