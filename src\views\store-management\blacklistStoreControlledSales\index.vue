<template>
  <div class="content">
    <div class="title_line">店铺控销黑名单</div>
    <SearchForm
      ref="searchForm"
      :model="formModel"
      :form-items="formItems"
      :hasOpenBtn="false"
      @submit="handleFormSubmit"
      @reset="handleFormReset"
    />
    <div class="operation">
      <el-button v-permission="['shopBlackBuyer_add']" type="primary" size="mini" @click="newAdd">新增黑名单</el-button>
      <el-button v-permission="['shopBlackBuyer_batchAdd']" type="primary" size="mini" @click="batchAdd">批量增加黑名单
      </el-button>
      <el-button type="primary" size="mini" @click="exportData" style="margin-right: 10px;">导出</el-button>
      <multipartUpload :type="3" :size="[0, 10000000]" @end="queryList">批量删除黑名单</multipartUpload>
    </div>
    <div style="margin: 15px 0;color: red">以下黑名单中客户将无法购买店铺中的所有商品</div>
    <xyyTable
      ref="productListTable"
      v-loading="tableLoading"
      :data="tableConfig.data"
      :col="tableConfig.col"
      :hasIndex="true"
      :list-query="listQuery"
      @get-data="queryList"
    >
      <template slot="operation">
        <el-table-column label="操作" fixed="right" width="150">
          <template slot-scope="{row}">
            <el-button v-permission="['shopBlackBuyer_remove']" type="text" @click="removeBlacklist(row)">移除黑名单
            </el-button>
          </template>
        </el-table-column>
      </template>
    </xyyTable>
    <AddDialog
      v-if="addDialogVisible"
      :addDialogVisible.sync="addDialogVisible"
    />
    <BatchImport
      ref="batchImport"
      title="批量添加黑名单"
      v-if="batchImportVisible"
      :batchImportVisible.sync="batchImportVisible"
      @downloadTemplate="downloadTemplate"
      @batchImportFn="batchImportFn"
    >
      <div slot="tips">
        <p>提示：</p>
        <p>1.您可在”客户管理“-”查询药店ID、药店名称</p>
        <p>2.支持上传xlsx, xls文件，大小不超过3M，单次导入客户数量不超过5000个</p>
        <p>3.支持通过药店名称、药店id批量导入</p>
      </div>
    </BatchImport>
    <exportTips :change-export="changeExport" @handleExoprClose="changeExport = false" @handleChangeExport="handleChangeExport"></exportTips>
  </div>
</template>

<script>
import SearchForm from '@/components/searchForm';
import BatchImport from '@/components/BatchImport';
import exportTips from '../../../views/other/components/exportTip.vue'
import AddDialog from './components/addDialog';
import multipartUpload from '../../marketing/components/multipartUpload.vue';
import { apiShopBlackBuyerList, apiBatchAddBlackBuyer, apiRemoveBuyer ,apiDownloadTemplate, exportData} from '@/api/storeManagement/blacklist';

export default {
  name: 'index',
  components: {
    SearchForm,
    AddDialog,
    exportTips,
    BatchImport,
    multipartUpload
  },
  data() {
    return {
      formModel: {
        buyerId: '',
        buyerName: ''
      },
      formItems: [
        {
          label: '药店ID',
          prop: 'buyerId',
          component: 'el-input',
          attrs: {
            placeholder: '请输入'
          }
        },
        {
          label: '药店名称',
          prop: 'buyerName',
          component: 'el-input',
          attrs: {
            placeholder: '请输入'
          }
        }
      ],
      changeExport: false,
      tableLoading: false,
      tableConfig: {
        data: [],
        col: [
          {
            index: 'buyerId',
            name: '药店ID'
          },
          {
            index: 'erpCode',
            name: 'ERP编码'
          },
          {
            index: 'buyerName',
            name: '药店名称'
          },
          {
            index: 'createTime',
            name: '加入黑名单时间',
            formatter: (row, col, cell) => {
              return this.formatDate(cell) || ''
            }
          },
          {
            index: 'operation',
            name: '操作',
            slot: 'operation'
          }
        ]
      },
      listQuery: {
        pageSize: 10,
        page: 1,
        total: 0
      },
      addDialogVisible: false,
      batchImportVisible: false
    };
  },
  created() {
    this.queryList();
  },
  methods: {
    exportData() {
      if (this.tableLoading) return;
      this.tableLoading = true;
      let params = { ...this.formModel };
      const {
        pageSize,
        page
      } = this.listQuery;
      params.pageNum = page;
      params.pageSize = pageSize;
      exportData(params).then(res => {
        if (res.code !== 0) {
					this.$message.warning(res.msg);
					return;
				}
        this.changeExport = true;
      }).finally(() => {
        this.tableLoading = false;
      })
    },
    async queryList(listQuery) {
      this.tableLoading = true;
      if (listQuery) {
        const {
          pageSize,
          page
        } = listQuery;
        this.listQuery.pageSize = pageSize;
        this.listQuery.page = page;
      }
      let params = { ...this.formModel };
      const {
        pageSize,
        page
      } = this.listQuery;
      params.pageNum = page;
      params.pageSize = pageSize;
      console.log(JSON.stringify(params));
      try {
        const res = await apiShopBlackBuyerList(params);
        if (res && res.code === 0) {
          this.tableConfig.data = res.data.list;
          this.listQuery.total = res.data.total;
        }
      } catch (e) {
        console.log(e);
      }
      this.tableLoading = false;
    },
    handleFormSubmit() {
      this.queryList();
    },
    handleFormReset() {
      Object.keys(this.formModel)
        .forEach(key => {
          this.formModel[key] = '';
        });
      this.queryList();
    },
    newAdd() {
      this.addDialogVisible = true;
    },
    batchAdd() {
      this.batchImportVisible = true;
    },
    async batchImportFn(FormData) {
      console.log(FormData);
      const h = this.$createElement;
      try {
        const res = await apiBatchAddBlackBuyer(FormData);
        if (res && res.code === 0) {
          const {
            error,
            success,
            errorFileName,
            errorFileUrl
          } = res.data;
          const baseUrl = process.env.VUE_APP_BASE_API;
          this.$msgbox({
            title: '上传文件反馈',
            message: h('p', null, [
              h('span', null, `共成功上传${success}个黑名单，失败${error}条数据${error ? '，下载错误文件：' : ''}`),
              error ? h('a', {
                attrs: {
                  class:'col_bg',
                  href: baseUrl + errorFileUrl,
                  download: errorFileName
                }
              }, `${errorFileName}`) : ''
            ]),
            confirmButtonText: '确定'
          })
            .then(() => {
              this.batchImportVisible = false;
              this.queryList();
            })
            .catch(() => {
              this.batchImportVisible = false;
            });
        } else {
          this.$refs.batchImport.remove();
          this.$alert(res.message || '导入失败，请重新上传！', { type: 'error' });
        }
        this.$refs.batchImport.closeLoading();
      } catch (err) {
        console.log(err);
      }
      this.$refs.batchImport.closeLoading();
    },
    downloadTemplate() {
      const fileName = '批量添加药店黑名单.xlsx'
      apiDownloadTemplate(fileName ).then((res) => {
        if (res.code && res.code !== 0) {
          this.$message.error(res.message || '请求异常')
        } else {
          this.util.exportExcel(res, fileName)
        }
      }).catch((err) => {
        console.log(err)
      })
    },
    removeBlacklist(row) {
      console.log(row);
      this.$confirm('您确定要将此客户移出店铺维度黑名单吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(async () => {
          const res = await apiRemoveBuyer(row.id)
          if (res && res.code === 0) {
            this.$message.success('移出黑名单成功!')
            this.queryList()
          } else {
            this.$message.error(res.message || '移出失败!');
          }
        });
    },
    handleChangeExport(info) {
			this.changeExport = false;
			if (info === 'go') {
				const path = '/downloadList';
				window.openTab(path);
				// that.$router.push({ path: '/downloadList' });
			}
		},
  }
};
</script>

<style scoped lang="scss">
.content {
  padding: 16px;
}
.col_bg{
  color: #4183d5
}
</style>
