<template>
    <div class="open-elc-sign-page">
        <div class="qual-state">
            <div class="qual-title">
                开通电子签服务
            </div>
            <div class="header-btn">
              <el-button size="small" @click="isShow = true">电子签开通异常说明</el-button>
              <el-button size="small" @click="returnPage">返回平台服务协议</el-button>
            </div>
        </div>
        <div class="elc-sign-iframe">
            <iframe :src="signUrl" frameborder="0" style="width: 100%; height: 100%; border: 0px;"></iframe>
        </div>
        <el-dialog :visible.sync="isShow" width="700px">
          <p>当商家在开通电子面单服务时，如遇系统提示现经办人不是企业超级管理员，有如下三个解决办法</p>
          <p>1.按页面提示的超管名称(如*永磊)确认该人员是否在职，如在职可安排该人员走所有签约流程即可</p>
          <p>2.如原超管人员不在职，可联系其配合登录公司在法大大账户，并授权其他人员为超管账号</p>
          <p>3.如原超管联系不上或不配合，则联系法大大客服修改超管账号</p>
          <div slot="footer">
            <el-button size="small" @click="isShow = false">关闭</el-button>
          </div>
        </el-dialog>
    </div>
</template>

<script>
export default {
    data() {
        return {
            signUrl: '',
            isShow: false
        }
    },
    mounted() {
        let query = this.$route.query;
        if (query && query.signUrl) {
            this.signUrl = query.signUrl;
        }
    },
    methods: {
        returnPage() {
            this.$router.push({path: '/companyOpenAccount/platformServeAgreement'})
        }
    },
}
</script>

<style lang="scss" scoped>
.open-elc-sign-page {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    .qual-state {
        .qual-title {
            font-family: PingFangSC-Medium;
            font-weight: 500;
            font-size: 20px;
            color: #333333;
        }
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0 20px;
        height: 50px;
    }
    .elc-sign-iframe {
        flex: 1;
    }
}
</style>
