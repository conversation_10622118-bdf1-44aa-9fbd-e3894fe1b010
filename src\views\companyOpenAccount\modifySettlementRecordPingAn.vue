<template>
  <div class="companyOpenAccount">
    <div class="headerStatus">
      <div class="companyStatus">
        <div class="status">
          修改结算信息{{ type !== 'modify' ? ':' : '' }}
        </div>
        <div
          v-if="statusObj.name"
          class="statusStr"
          :style="{ color:statusObj.colorStr }"
        >
          {{ statusObj.name }}
        </div>
        <div style="font-size: 12px">
          <div>{{ statusObj.tips }}</div>
          <div style="color: #f5222d; ">{{ statusObj.tips2 }}</div>
        </div>
      </div>
      <span
        v-if="type === 'modify'"
        style="margin-right: 16px"
      >
        <el-button @click="handleBack(false)">
          返回
        </el-button>
        <el-button
          type="primary"
          :loading="submitLoading"
          @click="submit"
        >
          提交
        </el-button>
      </span>
      <span v-else>
        <el-button @click="handleBack(true)">
          返回
        </el-button>
        <el-button
          v-if="status === 3"
          type="primary"
          @click="paymentVerificationDialogVis = true"
        >
          打款验证
        </el-button>
      </span>
    </div>
    <div class="leftContent">
      <div class="title_line third">结算信息</div>
      <el-form :model="accountVo" :rules="accountVoRules" ref="accountVo" label-width="100px">
        <el-form-item label="开卡人名称:" prop="registeredName">
          <el-input
            v-model="accountVo.registeredName"
            placeholder="请输入开卡人名称"
            :disabled="true"
            maxlength="30"
          ></el-input>
        </el-form-item>
        <el-form-item
          label="银行账号:"
          prop="registeredBankAccount"
          maxlength="30"
          onkeyup="value=value.replace(/[^\d]/g,'')"
        >
          <el-input
            v-model="accountVo.registeredBankAccount"
            placeholder="请输入企业的对公收款账号"
            :disabled="disabled"
          ></el-input>
        </el-form-item>
        <el-form-item label="开户银行名称:" prop="bankName">
          <el-select
            v-model="accountVo.bankName"
            placeholder="请选择开户银行"
            style="width: 400px"
            :disabled="disabled"
            :filterable="true"
            :filter-method="getBankList"
            @change="bankChange"
            :key="accountVo.bankName"
          >
            <el-option
              v-for="item in bankList"
              :key="item.bankName"
              :label="item.bankName"
              :value="item.bankName"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="开户银行支行:" prop="subBankName">
          <el-select
            ref="subBank"
            v-model="accountVo.subBankName"
            placeholder="请选择开户支行名称"
            style="width: 400px"
            :disabled="disabled"
            :filterable="true"
            :filter-method="searchSubBank"
            @change="subBankChange"
            :key="accountVo.subBankName"
          >
            <el-option
              v-for="item in subBankList"
              :key="item.bankName"
              :label="item.bankName"
              :value="item.bankName"
            ></el-option>
          </el-select>
          <!-- <el-button v-if="!disabled" icon="el-icon-search" circle @click="submitSearchSub"></el-button>-->
        </el-form-item>
      </el-form>
      <el-divider />
      <div class="title_line second">法人信息</div>
      <el-form :model="jpersonVo" :rules="jpersonVoRules" ref="jpersonVo" label-width="100px">
        <el-form-item label="法人名称:" prop="jpersonName">
          <el-input
            v-model="jpersonVo.jpersonName"
            placeholder="请输入企业法人姓名"
            :disabled="disabled"
            maxlength="30"
          ></el-input>
        </el-form-item>
        <el-form-item label="法人性别:" prop="jpersonSex">
          <el-radio v-model="jpersonVo.jpersonSex" :label="1" :disabled="disabled">男</el-radio>
          <el-radio v-model="jpersonVo.jpersonSex" :label="2" :disabled="disabled">女</el-radio>
        </el-form-item>
        <el-form-item label="法人身份证号:" prop="jpersonCardNumber">
          <el-input
            v-model="jpersonVo.jpersonCardNumber"
            placeholder="请输入企业法人身份证号"
            :disabled="disabled"
            maxlength="30"
          ></el-input>
        </el-form-item>
        <el-form-item label="身份证效期:" required>
          <el-col :span="10">
            <el-form-item prop="jpersonCardStartTime">
              <el-date-picker
                value-format="timestamp"
                placeholder="请选择法人身份证的开始时间"
                type="date"
                v-model="jpersonVo.jpersonCardStartTime"
                style="width: 100%;"
                :disabled="disabled"
              ></el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="10" style="margin-right: 40px">
            <el-form-item prop="jpersonCardEndTime">
              <el-date-picker
                value-format="timestamp"
                placeholder="请选择法人身份证的有效期至"
                type="date"
                v-model="jpersonVo.jpersonCardEndTime"
                style="width: 100%;margin: 0 16px"
                :disabled="juridicalPersonDisabled || disabled"
              ></el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="2">
            <el-form-item>
              <el-checkbox
                v-model="jpersonVo.jpersonLongTerm"
                @change="jpersonLongTermChange"
                :disabled="disabled"
              >长期</el-checkbox>
            </el-form-item>
          </el-col>
        </el-form-item>
        <el-form-item label="法人户籍地址:" prop="jpersonPermanentAddress">
          <el-input
            v-model="jpersonVo.jpersonPermanentAddress"
            placeholder="请输入企业法人身份证正面地址"
            :disabled="disabled"
            maxlength="50"
          ></el-input>
        </el-form-item>
        <el-form-item label="法人身份证签发地址:" prop="jpersonCardIssueAddress">
          <el-input
            v-model="jpersonVo.jpersonCardIssueAddress"
            placeholder="请输入企业法人身份证正面地址"
            :disabled="disabled"
            maxlength="50"
          ></el-input>
        </el-form-item>
        <el-form-item label="法人手机号:" prop="jpersonPhone">
          <el-input
            v-model="jpersonVo.jpersonPhone"
            placeholder="请输入企业法人手机号"
            :disabled="disabled"
            maxlength="11"
            onkeyup="this.value=this.value.replace(/[^\d]/g,'')"
          ></el-input>
        </el-form-item>
        <el-form-item label="法人身份证正面照片:" prop="jpersonCardUrl">
          <div style="font-size: 12px;color: #666">请上传法人身份证原件。大小不超过1.5MB，支持jpg、jpeg、png</div>
          <el-upload
            :disabled="disabled"
            class="avatar-uploader"
            :show-file-list="false"
            action
            :http-request="(obj)=>httpRequest(obj,jpersonVo,'jpersonCardUrl')"
            :before-upload="beforeAvatarUpload"
          >
            <img v-if="jpersonVo.jpersonCardUrl" :src="jpersonVo.jpersonCardUrl" class="avatar" />
            <i v-else class="el-icon-plus avatar-uploader-icon"></i>
          </el-upload>
        </el-form-item>
        <el-form-item label="法人身份证反面照片:" prop="jpersonCardReverseUrl">
          <div style="font-size: 12px;color: #666">请上传法人身份证原件。大小不超过1.5MB，支持jpg、jpeg、png</div>
          <el-upload
            :disabled="disabled"
            class="avatar-uploader"
            :show-file-list="false"
            action
            :http-request="(obj)=>httpRequest(obj,jpersonVo,'jpersonCardReverseUrl')"
            :before-upload="beforeAvatarUpload"
          >
            <img v-if="jpersonVo.jpersonCardReverseUrl" :src="jpersonVo.jpersonCardReverseUrl" class="avatar" />
            <i v-else class="el-icon-plus avatar-uploader-icon"></i>
          </el-upload>
        </el-form-item>
      </el-form>
      <el-divider />
      <div class="title_line fourth">联系人信息</div>
      <el-form
        :model="accountContactsVo"
        :rules="accountContactsVoRules"
        ref="accountContactsVo"
        label-width="100px"
      >
        <el-form-item label="商户联系人姓名:" prop="contactsName">
          <el-input
            v-model="accountContactsVo.contactsName"
            placeholder="请输入企业对外的常用联系人姓名"
            :disabled="disabled"
            maxlength="30"
          ></el-input>
        </el-form-item>

        <el-form-item label="联系人身份证号:" prop="cardNumber">
          <el-input
            v-model="accountContactsVo.cardNumber"
            placeholder="请输入企业联系人身份证号"
            :disabled="disabled"
            maxlength="30"
          />
        </el-form-item>

        <el-form-item label="商户联系人省市区县:" required>
          <div class="addrForm">
            <el-form-item prop="prov">
              <el-select
                v-model="accountContactsVo.prov"
                placeholder="请选择省份"
                :disabled="disabled"
                @change="(val)=>provinceChange(val,'accountContactsVo','provId','provinceList')"
                :key="accountContactsVo.prov"
              >
                <el-option
                  v-for="province in provinceList"
                  :key="province.id"
                  :label="province.areaName"
                  :value="province.areaName"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item prop="city">
              <el-select
                v-model="accountContactsVo.city"
                placeholder="请选择城市"
                :disabled="disabled"
                @change="(val)=>provinceChange(val,'accountContactsVo','cityId','cityList')"
                :key="accountContactsVo.city"
              >
                <el-option
                  v-for="city in accountContactsVo.cityList"
                  :key="city.id"
                  :label="city.areaName"
                  :value="city.areaName"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item prop="area">
              <el-select
                v-model="accountContactsVo.area"
                placeholder="请选择区县"
                :disabled="disabled"
                @change="(val)=>provinceChange(val,'accountContactsVo','areaId','areaList')"
                :key="accountContactsVo.area"
              >
                <el-option
                  v-for="area in accountContactsVo.areaList"
                  :key="area.id"
                  :label="area.areaName"
                  :value="area.areaName"
                ></el-option>
              </el-select>
            </el-form-item>
          </div>
        </el-form-item>
        <el-form-item label="商户联系人地址:" prop="address">
          <el-input
            v-model="accountContactsVo.address"
            placeholder="请输入企业联系人的详细地址"
            :disabled="disabled"
            maxlength="50"
          ></el-input>
        </el-form-item>
        <el-form-item label="商户邮箱:" prop="email">
          <el-input
            v-model="accountContactsVo.email"
            placeholder="请输入企业联系人的常用邮箱。后期将接收银行的确认邮件，请认真填写"
            :disabled="disabled"
            maxlength="30"
          ></el-input>
        </el-form-item>
        <el-form-item label="商户手机号:" prop="phone">
          <el-input
            v-model="accountContactsVo.phone"
            placeholder="请输入企业联系人的手机号。银行审核资料期间，可能与该手机号进行企业信息核实，请认真填写"
            :disabled="disabled"
          ></el-input>
        </el-form-item>
        <el-form-item v-if="!disabled" label="短信验证码:" prop="smsVerificationCode">
          <el-input
            v-model="accountContactsVo.smsVerificationCode"
            placeholder="请输入商户手机号收到的验证码"
            style="width: 50%;margin-right: 30px"
            maxlength="10"
          ></el-input>
          <el-button
            type="primary"
            @click="sendVerificationCode"
            :disabled="sendDisabled"
          >发送验证码{{ sendDisabled ? `(${sendCountDown})` : '' }}</el-button>
        </el-form-item>
      </el-form>
    </div>
    <PaymentVerificationDialog
      v-if="paymentVerificationDialogVis"
      :payment-verification-dialog-vis.sync="paymentVerificationDialogVis"
      :base="accountContactsVo"
      :auth-type="authType"
      @updateInfo="paymentVerificationDialogVis = false; queryBankAccountModifyInfo()"
    />
  </div>
</template>

<script>
import { queryBankListForPA, querySubBankListForPA, sendActiveCode, updateBankAccountForPA, queryBankAccountModifyInfo } from '@/api/companyOpenAccount';
import { getAddresInfo } from '@/api/qual';
import { uploadFDFS } from '@/api/order';
import PaymentVerificationDialog from './componments/PaymentVerificationDialog';

export default {
  name: 'ModifySettlementRecordPingAn',
  components: { PaymentVerificationDialog },
  data() {
    return {
      juridicalPersonDisabled: false,
      authType: 2,
      paymentVerificationDialogVis: false,
      status: 7, // 状态 0-待审核 1-银行卡审核中 2-银行卡审核失败 3-银行卡待验证 4-成功
      failReason: '',
      provinceList: [],
      sendCountDown: 60,
      sendDisabled: false,
      bankList: [],
      subBankList: [],
      disabled: true,
      submitLoading: false,
      type: 'modify',
      corBaseVo: {},
      accountVo: {
        bankCode: '', // 开户银行编码
        bankName: '', // 开户银行名称
        id: '', // id
        registeredBankAccount: '', // 银行账号
        registeredName: '', // 开卡人姓名
        subBankName: '', // 开户支行名称
        subBankCode: '', // 开户支行编码
      },
      accountContactsVo: {
        address: '', // 地址
        area: '', // 区县名称
        areaId: '', // 区县ID
        city: '', // 市
        cityId: '', // 市ID
        contactsName: '', // 联系人姓名
        email: '', // 邮件地址
        id: '', // 主键ID
        phone: '', // 联系人电话
        prov: '', // 省
        provId: '', // 省ID
        smsVerificationCode: '', // 短信验证码
        cardNumber: '', // 企业联系人身份证号
      },
      jpersonVo: {
        jpersonCardEndTime: '', // 身份证结束时间
        jpersonCardIssueAddress: '', // 身份证签发地址
        jpersonCardReverseUrl: '', // 身份证反面照片
        jpersonCardStartTime: '', // 身份证开始时间
        jpersonCardUrl: '', // 身份证正面照片
        id: '', // 主键id
        jpersonName: '', // 法人名称
        jpersonPermanentAddress: '', // 户籍地址
        jpersonPhone: '', // 法人手机号
        jpersonSex: '', // 性别 1-男 2-女
        jpersonCardNumber: '', // 法人身份证号
        jpersonLongTerm: '', // 是否长期
      },
      jpersonVoRules: {
        jpersonName: [
          {required: true, message: '请输入企业法人姓名', trigger: 'blur'}
        ],
        jpersonSex: [
          {required: true, message: '请选择企业法人性别'}
        ],
        jpersonCardNumber: [
          {required: true, message: '请输入企业法人身份证号', trigger: 'blur'}
        ],
        jpersonCardStartTime: [
          {required: true, message: '请选择法人身份证的开始时间', trigger: 'change'}
        ],
        jpersonCardEndTime: [
          {required: true, message: '请选择法人身份证的有效期至', trigger: 'change'}
        ],
        jpersonPermanentAddress: [
          {required: true, message: '请输入企业法人身份证正面地址', trigger: 'blur'}
        ],
        jpersonCardIssueAddress: [
          {required: true, message: '请输入企业法人身份证正面地址', trigger: 'blur'}
        ],
        jpersonPhone: [
          {required: true, message: '请输入企业法人手机号', trigger: 'blur'}
        ],
        jpersonCardUrl: [
          {required: true, message: '请上传法人身份证正面照'}
        ],
        jpersonCardReverseUrl: [
          {required: true, message: '请上传法人身份证反面照片'}
        ],
      },
      accountVoRules: {
        registeredName: [
          {required: true, message: '请输入开卡人名称', trigger: 'blur'}
        ],
        registeredBankAccount: [
          {required: true, message: '请输入企业的对公收款账号', trigger: 'blur'}
        ],
        bankName: [
          {required: true, message: '请选择开户银行', trigger: 'change'}
        ],
        subBankName: [
          {required: true, message: '请选择开户支行名称', trigger: 'change'}
        ],
      },
      accountContactsVoRules: {
        contactsName: [
          {required: true, message: '请输入企业对外的常用联系人姓名', trigger: 'blur'}
        ],
        cardNumber: [
          {required: true, message: '请输入企业联系人身份证号', trigger: 'blur'}
        ],
        prov: [
          {required: true, message: '请选择省份', trigger: 'change'}
        ],
        city: [
          {required: true, message: '请选择城市', trigger: 'change'}
        ],
        area: [
          {required: true, message: '请选择区县', trigger: 'change'}
        ],
        address: [
          {required: true, message: '请输入企业联系人的详细地址', trigger: 'blur'}
        ],
        email: [
          {required: true, message: '请输入企业联系人的常用邮箱', trigger: 'blur'}
        ],
        phone: [
          {required: true, message: '请输入企业联系人的手机号', trigger: 'blur'},
          // {pattern: /^[1][3-8]\d{9}$/, message: '请输入正确的手机号'}
        ],
        smsVerificationCode: [
          {required: true, message: '请输入商户手机号收到的验证码', trigger: 'blur'},
        ]
      },
      formRefs: ['accountVo', 'jpersonVo', 'accountContactsVo'],
    };
  },
  computed: {
    statusObj() {
      const obj = {};
      if (this.type === 'modify') {
        obj.name = '';
        obj.colorStr = '';
        obj.tips = '新的结算信息会提交至银行进行人工审批，审批周期预计5-10分钟，请耐心等待！';
        obj.tips2 = '审批通过后，银行将会向您的结算信息银行账号打款，请按照页面提示进行打款验证';
      } else {
        if (this.status === 4) {
          obj.name = '修改成功';
          obj.colorStr = '#52c41a';
          obj.tips = '结算信息修改成功';
        }
        if (this.status === 1) {
          obj.name = '银行卡审核中';
          obj.colorStr = '#ff9800';
          obj.tips = '新的结算信息会提交至银行进行人工审批，审批周期预计5-10分钟，请耐心等待！';
          obj.tips2 = '审批通过后，银行将会向您的结算信息银行账号打款，请按照页面提示进行打款验证';
        }
        if (this.status === 2) {
          obj.name = '银行卡审核失败';
          obj.colorStr = '#f5222d';
          obj.tips = '您的企业信息银行审批未通过，请参考驳回原因修改信息后重新提交';
          obj.tips2 = `驳回原因：${this.failReason}`;
        }
        if (this.status === 3) {
          obj.name = '银行卡待验证';
          obj.colorStr = '#ff9800';
          obj.tips = '您的企业信息银行已审批通过！';
          obj.tips2 = '为核验身份，银行将会向您的结算信息银行账号打款，请点击“打款验证”进行打款验证操作';
        }
      }
      return obj;
    },
  },
  activated() {
    const { params } = this.$route;
    console.log(*************, params);
    this.type = params.type;
    if (this.type === 'modify') {
      this.corBaseVo = params.corBaseVo;
      this.accountContactsVo = params.accountContactsVo;
      this.accountVo = params.accountVo;
      this.jpersonVo.jpersonName = params.jpersonVo.juridicalPersonName;
      this.jpersonVo.jpersonSex = params.jpersonVo.sex;
      this.jpersonVo.jpersonCardNumber = params.jpersonVo.cardNumber;
      this.jpersonVo.jpersonCardStartTime = params.jpersonVo.cardStartTime;
      this.jpersonVo.jpersonCardEndTime = params.jpersonVo.cardEndTime;
      this.jpersonVo.jpersonPermanentAddress = params.jpersonVo.permanentAddress;
      this.jpersonVo.jpersonCardIssueAddress = params.jpersonVo.cardIssueAddress;
      this.jpersonVo.jpersonPhone = params.jpersonVo.phone;
      this.jpersonVo.jpersonCardUrl = params.jpersonVo.cardUrl;
      this.jpersonVo.jpersonCardReverseUrl = params.jpersonVo.cardReverseUrl;
      if (this.jpersonVo.jpersonCardEndTime === '') {
        this.jpersonLongTermChange(true);
        this.jpersonVo.jpersonLongTerm = true;
      }
      this.disabled = false;
      this.getBankList();
      this.getAddr(0, 'provinceList');
    } else {
      this.disabled = true;
      this.queryBankAccountModifyInfo();
    }
  },
  methods: {
    async httpRequest(obj, data, str) {
      try {
        const res = await uploadFDFS(obj);
        if (Number(res.code) === 200) {
          const imgUrl = process.env.VUE_APP_UPLOAD_API + '/' + res.data;
          this.$set(data, str, imgUrl);
          if (this.corBaseVoRules[str]) {
            this.$refs.corBaseVo.validateField([str]);
          }
          if (this.jpersonVoRules[str]) {
            this.$refs.jpersonVo.validateField([str]);
          }
        } else {
          this.$message.error(res.msg);
        }
      } catch (err) {
        console.log(err);
      }
    },
    beforeAvatarUpload(file) {
      if (file.type !== 'image/jpg' && file.type !== 'image/jpeg' && file.type !== 'image/png') {
        this.$message.error('上传附件只能是 jpg、jpeg、png 格式！');
        return false;
      }
      if (file.size > 1572864) {
        this.$message.error('上传附件大小不能超过 1.5M！');
        return false;
      }
    },
    jpersonLongTermChange(type) {
      if (type) {
        this.$set(this.jpersonVo, 'jpersonCardEndTime', '');
        this.juridicalPersonDisabled = true;
        this.$refs.jpersonVo.clearValidate(['jpersonCardEndTime']);
        this.jpersonVoRules.jpersonCardEndTime = [{ required: false }];
      } else {
        this.juridicalPersonDisabled = false;
        this.jpersonVoRules.jpersonCardEndTime = [{ required: true, message: '请选择法人身份证的有效期至', trigger: 'change' }];
      }
    },
    getAddr(code, listName, objName) {
      getAddresInfo({ parentCode: code }).then((res) => {
        if (res && res.code === 0) {
          if (listName !== 'provinceList') {
            this.$set(this[objName], listName, res.data);
            this.$forceUpdate();
          } else {
            this.provinceList = res.data;
          }
        }
      });
    },
    provinceChange(val, objName, strName, listName) {
      if (listName === 'provinceList') {
        const [targetObj] = this.provinceList.filter(item => item.areaName === val);
        if (targetObj) {
          this.$set(this[objName], strName, targetObj.areaCode);
          this.getAddr(targetObj.areaCode, 'cityList', objName);
        }
        this.areaList = [];
        const ary = ['cityId', 'city', 'areaId', 'area'];
        ary.forEach((key) => {
          this.$set(this[objName], key, '');
        });
        const list = ['cityList', 'areaList'];
        list.forEach((key) => {
          this.$set(this[objName], key, []);
        });
      } else {
        const [targetObj] = this[objName][listName].filter(item => item.areaName === val);
        if (targetObj) {
          this.$set(this[objName], strName, targetObj.areaCode);
        }
      }
      if (listName === 'cityList') {
        this.$set(this[objName], 'areaList', []);
        const [targetObj] = this[objName][listName].filter(item => item.areaName === val);
        if (targetObj) {
          this.getAddr(targetObj.areaCode, 'areaList', objName);
        }
        const ary = ['areaId', 'area'];
        ary.forEach((key) => {
          this.$set(this[objName], key, '');
        });
      }
    },
    async bankChange(val) {
      const [obj] = this.bankList.filter(item => item.bankName === val);
      this.accountVo.bankCode = obj.bankCode;
      this.accountVo.subBankName = '';
      this.accountVo.subBankCode = '';
      const res = await querySubBankListForPA({ bankName: this.accountVo.bankName, subBankName: '' });
      if (res && res.code === 0) {
        this.subBankList = res.result;
      } else {
        this.$message.error(res.msg || '获取开户支行失败');
      }
    },
    subBankChange(val) {
      const [obj] = this.subBankList.filter(item => item.bankName === val);
      this.accountVo.subBankCode = obj.bankCode;
    },
    sendVerificationCode() {
      this.$refs.accountContactsVo.validateField('phone', async (f) => {
        if (!f) {
          this.sendDisabled = true;
          this.timeOutSend();
          const res = await sendActiveCode({ phone: this.accountContactsVo.phone, codeType: 3 });
          if (res && res.code === 0) {
            this.$message.success('验证码发送成功');
          } else {
            this.sendDisabled = false;
            this.sendCountDown = 60;
            this.$message.error(res.msg);
          }
        }
      });
    },
    timeOutSend() {
      setTimeout(() => {
        if (this.sendCountDown > 0) {
          this.sendCountDown --
          this.timeOutSend();
        } else {
          this.sendDisabled = false;
          this.sendCountDown = 60;
        }
      }, 1000);
    },
    async getBankList(val) {
      // console.log(********, val);
      const res = await queryBankListForPA({ bankName: val || '' });
      if (res && res.code === 0) {
        this.bankList = res.result;
      } else {
        this.$message.error(res.msg || '获取开户行失败');
      }
    },
    async searchSubBank(val) {
      console.log(val);
      const res = await querySubBankListForPA({ bankName: this.accountVo.bankName, subBankName: val });
      if (res && res.code === 0) {
        this.subBankList = res.result;
      } else {
        this.$message.error(res.msg || '获取开户支行失败');
      }
    },
    handleBack(bool) {
      if (bool) {
        this.$router.push('/companyOpenAccount');
        return false;
      }
      this.$confirm('返回至企业开户页面，当前修改结算页面信息将不会保存，确认返回吗？', '温馨提示', {
        confirmButtonText: '确定返回',
        cancelButtonText: '再想想',
      })
        .then(() => {
          this.$router.push('/companyOpenAccount');
        })
        .catch(() => {});
    },
    getParams() {
      const accountContactsVo = { ...this.accountContactsVo };
      delete accountContactsVo.areaList;
      delete accountContactsVo.cityList;
      const accountVo = { ...this.accountVo };
      const jpersonVo = { ...this.jpersonVo };
      if (jpersonVo.jpersonLongTerm) {
        jpersonVo.jpersonCardEndTime = new Date('2099-12-31').getTime();
      }
      delete jpersonVo.jpersonLongTerm;
      const params = { ...accountContactsVo, ...accountVo, ...jpersonVo };
      delete params.id;
      delete params.orgId;
      return params;
    },
    submit() {
      this.checkForm();
    },
    checkForm() {
      const newList = [];
      this.formRefs.forEach((str) => {
        const result = new Promise((resolve, reject) => {
          this.$refs[str].validate((valid) => {
            if (valid) {
              resolve();
            } else {
              reject();
            }
          });
        });
        newList.push(result);
      });
      const params = this.getParams();
      params.codeType = 3;
      console.log(********, params);
      if (!params.subBankCode) {
        this.$alert('请在下拉列表选择开户支行', '提示', {
          confirmButtonText: '确定',
          callback: action => {},
        });
        return false;
      }
      Promise.all(newList).then(async () => {
        console.log('全部校验通过');
        this.submitLoading = true;
        const res = await updateBankAccountForPA(params);
        if (res && res.code === 0) {
          this.$message.success('提交成功');
          this.$router.push('/companyOpenAccount');
        } else {
          this.$alert(res.msg || '提交失败', '提示', {
            confirmButtonText: '确定',
            callback: action => {},
          });
        }
        this.submitLoading = false;
      }).catch((err) => {
        console.log('校验失败', err);
        this.$message.warning('请将开户信息补充完整');
      });
    },
    queryBankAccountModifyInfo() {
      queryBankAccountModifyInfo().then((res) => {
        if (res.code === 0) {
          console.log(*************, res);
          const { result } = res;
          if (result) {
            Object.keys(this.accountVo).forEach((key) => {
              this.accountVo[key] = result[key];
            });
            Object.keys(this.accountContactsVo).forEach((key) => {
              this.accountContactsVo[key] = result[key];
            });
            Object.keys(this.jpersonVo).forEach((key) => {
              this.jpersonVo[key] = result[key];
            });
            const jpersonCardEndTime = this.formatDate(this.jpersonVo.jpersonCardEndTime, 'YMD');
            if (jpersonCardEndTime === '2099-12-31') {
              this.jpersonLongTermChange(true);
              this.jpersonVo.jpersonLongTerm = true;
            }
            this.status = result.status;
            this.failReason = result.failReason;
          }
        }
      });
    },
  },
};
</script>

<style scoped lang="scss">
.el-button {
  padding: 8px 20px;
}

.el-button.is-circle {
  padding: 7px;
  border: none;
}

.companyOpenAccount {
  //min-width: 1400px;
  width: 100%;
  height: 100%;
  position: relative;
  padding-top: 50px;

  .NoCreatedStore {
    width: 100%;
    padding-left: 30px;
    line-height: 77px;
    font-size: 18px;
    color: #ffa012;
    background: #fffaf2;
    position: absolute;
    top: 0;
    left: 0;
  }

  .headerStatus {
    position: absolute;
    top: 0;
    left: 0;
    //min-width: 1400px;
    width: 100%;
    height: 50px;
    background: #fffbf1;
    display: flex;
    justify-content: space-between;
    align-items: center;
    z-index: 100;

    .companyStatus {
      width: 60%;
      display: flex;
      justify-content: start;
      align-items: center;

      .status {
        color: #333333;
        font-size: 16px;
        margin-left: 16px;
        margin-right: 16px;
      }

      .statusStr {
        margin: 0 16px;
        margin-left: 0;
      }
    }
  }

  .leftContent {
    width: 100%;
    height: 100%;
    overflow-y: auto;
    padding: 30px 16px 0;

    ::v-deep   .el-form {
      width: 80%;

      .el-select {
        margin-right: 14px;
      }

      .el-form-item__label {
        font-size: 12px;
        line-height: 30px;
      }

      .el-form-item__content {
        line-height: 30px;
      }

      .el-input__inner {
        line-height: 30px;
        height: 30px;
        font-size: 12px;
      }

      .el-input__icon {
        line-height: 30px;
      }
    }

    ::v-deep   .el-table__body .el-form-item {
      padding: 20px 0;
    }

    .addrForm .el-form-item {
      display: inline-block;
    }

    .avatar-uploader ::v-deep   .el-upload {
      border: 1px dashed #d9d9d9;
      border-radius: 6px;
      cursor: pointer;
      position: relative;
      overflow: hidden;
    }

    .avatar-uploader ::v-deep   .el-upload:hover {
      border-color: #409eff;
    }

    .avatar-uploader-icon {
      font-size: 28px;
      color: #8c939d;
      width: 178px;
      height: 178px;
      line-height: 178px;
      text-align: center;
    }

    .avatar {
      width: 178px;
      height: 178px;
      display: block;
    }
  }

  .leftContent::-webkit-scrollbar {
    width: 0 !important;
  }

  .rightContent {
    position: absolute;
    right: 0;
    top: 75px;
    z-index: 1001;
    background-color: #fff;
  }
}
</style>
