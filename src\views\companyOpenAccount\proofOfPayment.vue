<template>
  <div class="proofOfPayment">
    <div class="tipBox">
      <div class="tips">
        <i class="el-icon-warning-outline"></i>
        <div class="tipContent">
          <div
            class="tipWords"
            :class="info.state === 2 ? 'redText' : info.state === 3 ? 'greenText' : ''"
          >
            {{ stateStr}}
          </div>
          <div class="moneyText" v-if="info.state != -1">保证金金额：{{ info.money }}元</div>
        </div>
      </div>
      <el-button
        v-if="info.state === 0 || info.state === 2"
        size="small"
        type="primary"
        :loading="btnLoading"
        @click="submitInfo"
      >提交</el-button>
    </div>
    <div class="contentBox" v-if="info.state != -1">
      <el-row type="flex" align="middle">
        <span class="sign" />
        <div class="searchMsg">上传付款证明</div>
      </el-row>
      <el-upload
        v-if="info.state === 0 || info.state === 2"
        action
        :http-request="uploadImg"
        :before-upload="beforeAvatarUpload"
        class="avatar-uploader"
        list-type="picture-card"
        :file-list="uploadedImgs"
        :on-remove="handleRemove"
        :on-preview="handlePictureCardPreview"
        :limit="5"
      >
        <template #default>
          <i class="el-icon-plus" />
        </template>
      </el-upload>
      <div v-else>
        <div v-for="(item, index) in uploadedImgs" :key="index" class="receiptUrlBox imgBox">
          <img :src="item.url" alt="" @click="handlePictureCardPreview(item)">
        </div>
      </div>
      <div class="uploadTip">
        <div>温馨提示：</div>
        <div>1、请按照页面提示的保证金金额，向平台约定的对公账户打款</div>
        <div>2、打款完成后请上传付款证明，支持 jpeg、jpg、png、gif 格式，大小不超过 5M，最多上传5张</div>
      </div>
    </div>
    <div class="platformReceipt" v-if="info.receiptUrl">
      <el-row type="flex" align="middle">
        <span class="sign" />
        <div class="searchMsg">平台收据</div>
      </el-row>
      <div class="receiptUrlBox">
        <img :src="info.receiptUrl" alt="" @click="showBig">
      </div>
      <el-button size="small" type="primary" @click="downLoad">下载收据</el-button>
    </div>
    <el-image-viewer
      v-if="dialogVisible"
      :url-list="srcArr"
      :on-close="closeImg"
      append-to-body
      :z-index="100000"
    />
  </div>
</template>

<script>
import { uploadFile } from '@/api/qual/index';
import { getHostName } from '@/api/storeManagement/index';
import { getInfo, savePaymentProve } from '@/api/companyOpenAccount'
import ElImageViewer from 'element-ui/packages/image/src/image-viewer';

export default {
  name: "proofOfPayment",
  components: { ElImageViewer },
  data() {
    return {
      uploadedImgs: [],
      hostName: '',
      srcArr: [],
      dialogVisible: false,
      info: {},
      stateStr: '',
      btnLoading: false,
    }
  },
  created() {
    getHostName().then((res) => {
      if (res.hostName) {
        this.hostName = res.hostName;
      }
    });
    this.getInfo();
  },
  methods: {
    getInfo() {
      getInfo().then((res) => {
        if (res.code === 0) {
          this.info = res.result;
          let urlArr = (res.result.url || '').length ? res.result.url.split(',') : [];
          this.uploadedImgs = (urlArr || []).map((item) => {
            return { url: item || null }
          });
          const state = res.result.state;
          switch (state) {
            case -1:
              this.stateStr = '请先提交企业信息，再上传保证金付款证明';
              break;
            case 0:
              this.stateStr = '待付款';
              break;
            case 1:
              this.stateStr = '待审核，请等待平台财务审核';
              break;
            case 2:
              this.stateStr = '审核未通过' + `，${this.info.remarks || ''}`;
              break;
            case 3:
              this.stateStr = '审核通过';
              break;
          }
        }
      }).catch((err) => {
        console.log('err', err);
      })
    },
    uploadImg(file) {
      uploadFile(file).then((res) => {
        if (res.code === '200') {
          this.uploadedImgs.push({ url: `${this.hostName}/${res.data}` });
        }
      });
    },
    beforeAvatarUpload(file) {
      if (!file) {
        this.$message.error('请上传图片');
        return false;
      }
      const isJPG = file.type === 'image/jpeg'
        || file.type === 'image/png'
        || file.type === 'image/bmp'
        || file.type === 'image/jpg'
        || file.type === 'image/gif';
      const isLt5M = file.size / 1024 / 1024 < 5;
      if (!isJPG || !isLt5M) {
        this.$message.error('图片不满足上传要求，请重新上传');
        return false;
      }
      return isJPG && isLt5M;
    },
     handleRemove(file) {
      this.uploadedImgs.forEach((item, index) => {
        if (item.url === file.url) {
          this.uploadedImgs.splice(index, 1);
        }
      });
    },
    handlePictureCardPreview(file) {
      this.dialogVisible = true;
      const ary = [];
      this.srcArr = [];
      this.uploadedImgs.forEach((item) => {
        if (item.url === file.url) {
          this.srcArr.push(item.url);
        } else {
          ary.push(item.url);
        }
      });
      this.srcArr = this.srcArr.concat(ary);
    },
     closeImg() {
      this.dialogVisible = false;
    },
    submitInfo() {
      if (this.uploadedImgs.length === 0) {
        this.$message.warning('请上传付款证明');
        return;
      }
      this.btnLoading = true;
      let params = {
        cId: this.info.cid,
        id: this.info.id,
        url: this.uploadedImgs.map(i => i.url).join(),
      }
      savePaymentProve(params).then((res) => {
        if (res.code === 0) {
          this.$message.success('提交成功');
          setTimeout(() => {
            this.btnLoading = false;
            this.getInfo();
          }, 500);
        } else {
          this.btnLoading = false;
          this.$message.error(res.msg);
        }
      }).catch(() => {
        this.btnLoading = false;
      })
    },
    showBig() {
      this.dialogVisible = true;    
      this.srcArr = [this.info.receiptUrl];    
    },
    downLoad() {
      window.open(this.info.receiptUrl);
    }
  }
}
</script>

<style scoped lang="scss">
.proofOfPayment {
  margin-left: 16px;
  .tipBox {
    padding: 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: #F7EDD7;
    .tips {
      color: #F4AB0A;
      font-size: 26px;
      display: flex;
      align-items: center;
    }
    .tipContent {
      display: inline-block;
      margin-left: 10px;
      div {
        color: #F4AB0A;
        font-weight: 600;
        font-size: 18px;
      }
      .tipWords {
        color: #F4AB0A;
      }
      .redText {
        color: red;
      }
      .greenText {
        color: #67c23a;
      }
    }
  }
  .contentBox {
    margin-top: 20px;
  }
  .sign {
    display: inline-table;
    width: 3px;
    height: 13px;
    background: linear-gradient(360deg, #1d69c4 1%, #8bbdfc 99%);
    border-radius: 2px;
    margin-right: 8px;
  }
  .avatar-uploader {
    margin-top: 20px;
  }
  .uploadTip {
    margin-top: 20px;
  }
  .platformReceipt {
    margin-top: 30px;
  }
  .receiptUrlBox {
    width: 146px;
    height: 146px;
    margin: 20px 10px;
    img {
      width: 100%;
      height: 100%;
    }
  }
  .imgBox {
    display: inline-block;
  }
}
</style>

