/**
 * created by <PERSON><PERSON><PERSON><PERSON><PERSON> in 2021/3/16
 *
 *  隔离底层实际API请求，求同存异
 *    shop、pop、EC后台都会使用
 *
 */

import {
  addActivityCouponList,
  getDetail,
  getProvinceAndMerchantType as _getProvinceAndMerchantType,
  getProvinceRegion as _getProvinceRegion,
  peopleDataPost,
  getImportMerchantList as _getImportMerchantList, //
  getMerchantList as _getMerchantList, //
  deleteImportMerchantList as _deleteImportMerchantList, //
} from '@/api/market/addActivity';
import { upLoaderShopSupervise } from '@/api/market/shopSupervise';
import respTransform from '../transform/respTransform';
import reqTransform from '../transform/reqTransform';

import request from '../../../../api';

export const { post, get } = request;

// 获取人群详情
export function getCrowdDetail(data) {
  return getDetail(reqTransform.getCrowdDetail(data)).then(res => respTransform.getCrowdDetail(res));
}

// 获取人群列表
export function getCrowdList(data) {
  return addActivityCouponList(reqTransform.getCrowdList(data)).then(res => respTransform.getCrowdList(res));
}


// 获取省信息、客户类型信息
export async function getProvincesAndMerchantTypes() {
  return _getProvinceAndMerchantType(reqTransform.getProvincesAndMerchantTypes()).then(res => respTransform.provincesAndMerchantTypes(res));
}

// 获取市区信息
export function getProvinceRegions(data) {
  return _getProvinceRegion(reqTransform.getProvinceRegions(data)).then(res => respTransform.provinceRegions(res));
}

// 上传客户信息
export function uploadCrowdCustomer(data) {
  return upLoaderShopSupervise(reqTransform.uploadCrowdCustomer(data)).then(res => respTransform.uploadCrowdCustomer(res));
}

// 创建人群
export function createCrowd(data) {
  return peopleDataPost(reqTransform.createCrowd(data)).then(res => respTransform.createCrowd(res));
}


// 获取导入客户
export function getImportMerchantList(data) {
  return _getImportMerchantList(reqTransform.getImportMerchantList(data)).then(res => respTransform.getImportMerchantList(res));
}
// 获取客户列表
export function getMerchantList(data) {
  return _getMerchantList(reqTransform.getMerchantList(data)).then(res => respTransform.getMerchantList(res));
}


export function deleteImportMerchantList(data) {
  return _deleteImportMerchantList(reqTransform.deleteImportMerchantList(data)).then(res => respTransform.deleteImportMerchantList(res));
}
