<template>
  <div class="content">
    <div class="storeData">
      <div class="storeDataTitle">今日店铺数据 <i class="el-icon-warning-outline" @click="openDialog(1)"></i></div>
      <div class="orderBox">
        <div>
          <i class="actually"></i>
          <div>
            <div>订单实付</div>
            <div class="num">{{ formData.amountPayable }}</div>
          </div>
        </div>
        <div>
          <i class="onSale"></i>
          <div>
            <div>在售有货商品</div>
            <div class="num">{{ formData.sellingInStockSkuCount }}</div>
          </div>
        </div>
      </div>
      <div class="orderList">
        <div>
          <div class="listTitle">订单数量</div>
          <div class="listNum">{{ formData.newOrderCount }}</div>
        </div>
        <div class="line"></div>
        <div>
          <div class="listTitle">新增客户</div>
          <div class="listNum">{{ formData.newCustomerCount }}</div>
        </div>
        <div class="line"></div>
        <div>
          <div class="listTitle">退单数量</div>
          <div class="listNum">{{ formData.refundOrderCount }}</div>
        </div>
        <div class="line"></div>
        <div>
          <div class="listTitle">退款金额</div>
          <div class="listNum">{{ formData.refundTotalMoney }}</div>
        </div>
      </div>
    </div>
    <div class="allOrder">
      <div class="allOrderTitle">全部订单</div>
      <div class="notShipped">
        <div>
          <div class="count">{{ formData.timeoutOrderCount_48 }}</div>
          <div>超48小时未发货 <i class="el-icon-warning-outline" @click="openDialog(2)"></i></div>
        </div>
        <div class="line"></div>
        <div>
          <div class="count">{{ formData.waitExamineRefundOrderCount }}</div>
          <div>待审核退款单 <i class="el-icon-warning-outline" @click="openDialog(3)"></i></div>
        </div>
      </div>
      <div class="process">
        <div>
          <div class="audit"></div>
          <div class="p_count">{{ formData.waitExamineCount }}</div>
          <div>待审核</div>
        </div>
        <div class="rightIcon"></div>
        <div>
          <div class="bill"></div>
          <div class="p_count">{{ formData.waitDeliveryCount }}</div>
          <div>正在开单</div>
        </div>
        <div class="rightIcon"></div>
        <div>
          <div class="sorting"></div>
          <div class="p_count">{{ formData.sortingCount }}</div>
          <div>分拣中</div>
        </div>
      </div>
    </div>
    <div class="serviceQuality">
      <div>服务质量（近七天）<i class="el-icon-warning-outline" @click="openDialog(4)"></i></div>
      <div class="quality">
        <div>
          <el-progress :stroke-width="8" type="circle" :percentage="formData.sendNum"></el-progress>
          <div>48H发货率</div>
        </div>
        <div>
          <el-progress color="#FE1010" :stroke-width="8" type="circle" :percentage="formData.refundNum"></el-progress>
          <div>退款率</div>
        </div>
        <div>
          <el-progress color="#FE1010" :stroke-width="8" type="circle" :percentage="formData.refundOrderNum"
          ></el-progress>
          <div>退货率</div>
        </div>
        <div>
          <el-progress :stroke-width="8" type="circle" :percentage="formData.respondCt"></el-progress>
          <div>客服响应率</div>
        </div>
      </div>
    </div>
    <div v-show="showDialog" class="dialog">
      <div class="dialogWarp">
        <div class="dialogContent">
          <template v-if="dialogType===1">
            <div class="dialogTitle">今日店铺数据</div>
            <div class="contentBox">
              <div class="text_content">
                <span style="font-weight: bold">订单实付：</span>今日产生的有效订单实付金额（含运费，不含优惠），统计订单状态包含"待审核、出库中、配送中、已完成、已退款"。
              </div>
              <div class="text_content">
                <span style="font-weight: bold">订单数：</span>今日产生的有效订单数量，统计订单状态包含"待审核、出库中、配送中、已完成、已退款"。
              </div>
              <div class="text_content">
                <span style="font-weight: bold">新增客户数：</span>今日产生店铺首单的客户。
              </div>
              <div class="text_content">
                <span style="font-weight: bold">退款单数：</span>今日产生的所有退款单数量。
              </div>
              <div class="text_content">
                <span style="font-weight: bold">退款金额：</span>今日退款成功的所有退款金额（含运费）。
              </div>
              <div class="text_content">
                <span style="font-weight: bold">在售有货商品：</span>当前在售且有库存的商品。
              </div>
            </div>
          </template>
          <template v-else-if="dialogType===2">
            <div class="dialogTitle">超48h未发货</div>
            <div class="contentBox">
              <div class="text_content">
                <span style="font-weight: bold">超48h未发货：</span>从订单付款到当前时间，已超过48h，但仍未发货。
              </div>
            </div>
          </template>
          <template v-else-if="dialogType===3">
            <div class="dialogTitle">待审核退款单</div>
            <div class="contentBox">
              <div class="text_content">
                <span style="font-weight: bold">待审核退款单：</span>待处理的退款单数量。
              </div>
            </div>
          </template>
          <template v-else-if="dialogType===4">
            <div class="dialogTitle">服务质量（近7天）</div>
            <div class="contentBox">
              <div class="text_content">
                <span style="font-weight: bold">48h发货率</span>
                <ul>
                  <li>48h发货率=付款后48小时内发货的订单/总订单数量</li>
                  <li>48h内发货："揽收时间- 付款时间"<=48小时</li>
                  <li>订单状态：待审核、待发货、配送中、已完成、已退款</li>
                </ul>
              </div>
              <div class="text_content">
                <span style="font-weight: bold">商家原因退款率</span>
                <ul>
                  <li>商家原因退款率=因商业原因产生退款的订单/总订单数量</li>
                  <li>订单状态：待审核、待发货、配送中、已完成、已退款</li>
                  <li>商业原因退款：到货慢、发货慢、商品实物与展示不符、商品错漏发、破损或质量问题、商品缺货、商品发货少货、商品发错、商品近效期、破损及质量问题、批号不符、其他原因</li>
                </ul>
              </div>
              <div class="text_content">
                <span style="font-weight: bold">商家原因退货率</span>
                <ul>
                  <li>商家原因退货率=因商业原因发起退款且退款时商品已发货的订单/总订单数量</li>
                  <li>订单状态：待审核、待发货、配送中、已完成、已退款</li>
                  <li>发起退款时商品已发货："揽收时间" <= "发起退款时间"</li>
                </ul>
              </div>
              <div class="text_content">
                <span style="font-weight: bold">客户在线响应率</span>
                <ul>
                  <li>客服在线响应率=（及时响应+超时响应）会话数/总会话数</li>
                  <li>首次响应时长为0，计入"无响应"</li>
                  <li>首次响应时长在180秒内回复客户的会话，计入"及时响应"</li>
                  <li>首次响应时长超过180秒回复客户的会话，计入"超时响应"</li>
                </ul>
              </div>
            </div>
          </template>
          <div class="foot">
            <el-button type="text" @click="confirm">我知道了</el-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { apiQueryServeQuality, todayData } from '@/api/home';
import { orderStatusCount, queryTimeoutOrderCount } from '@/api/order/index';
import { getRefundCount } from '@/api/afterSale/index';
import {actionTracking} from "@/track/eventTracking";

export default {
  name: 'dataAnalysis',
  data() {
    return {
      showDialog: false,
      dialogType: 1,
      formData: {
        amountPayable: 0,//今日订单实付
        sellingInStockSkuCount: 0,//在售有货商品数
        newOrderCount: 0,//今日新订单
        newCustomerCount: 0,//今日新增客户数
        refundOrderCount: 0,//今日退款订单数
        refundTotalMoney: 0,//日退款金额
        timeoutOrderCount_48: 0,//48小时未发货订单数
        waitExamineRefundOrderCount: 0,//待审核退款订单数
        waitExamineCount: 0,//待审核
        waitDeliveryCount: 0,//正在开单
        sortingCount: 0,//分拣中
        sendNum: 0,//48小时发货率
        refundNum: 0,//商家原因退款率
        refundOrderNum: 0,//商家原因退货率
        respondCt: 0//客服响应率
      }
    };
  },
  created() {
    this.getFormData();
  },
  mounted() {
    setTimeout(()=>{
      actionTracking('H5_page_data_exposure', {
        is_valid_exposure : 'success'
      })
    }, 3000);
  },
  methods: {
    getFormData() {
      const loading = this.$loading({
        lock: true,
        text: 'Loading',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      });
      Promise.all([
        apiQueryServeQuality()
          .then(res => {
            if (res && res.code === 0) {
              Object.keys(res.result)
                .forEach(key => {
                  this.formData[key] = res.result[key];
                });
            }
          }),
        todayData()
          .then(res => {
            if (res && res.code === 0) {
              Object.keys(res.result)
                .forEach(key => {
                  this.formData[key] = res.result[key];
                });
            }
          }),
        queryTimeoutOrderCount()
          .then(res => {
            if (res && res.code === 0) {
              this.formData.timeoutOrderCount_48 = res.result || 0;
            }
          }),
        orderStatusCount()
          .then(res => {
            if (res && res.code === 0) {
              console.log(res);
              const {
                waitExamineCount,
                waitDeliveryCount,
                sortingCount
              } = res.data;
              this.formData.waitExamineCount = waitExamineCount;
              this.formData.waitDeliveryCount = waitDeliveryCount;
              this.formData.sortingCount = sortingCount;
            }
          }),
        getRefundCount()
          .then(res => {
            console.log(res);
            if (res && res.code === 0) {
              this.formData.waitExamineRefundOrderCount = res.result || 0;
            }
          })
      ])
        .then(() => {
          loading.close();
        })
        .catch((e) => {
          loading.close();
        });
    },
    openDialog(type) {
      this.dialogType = type;
      this.showDialog = true;
    },
    confirm() {
      this.showDialog = false;
    }
  }
};
</script>

<style scoped lang="scss">
.content {
  box-sizing: border-box;
  width: 100%;
  height: 100%;
  padding: 0.2rem;
  overflow: hidden;

  .storeData {
    width: 100%;
    background: url("../../assets/h5/storeData_bg.png");
    background-size: 100% 100%;
    box-sizing: border-box;
    padding: 0.30rem;

    .storeDataTitle {
      font-size: 0.3rem;
      color: #fff;
      font-weight: 600;
    }

    .orderBox {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-top: 0.2rem;
    }

    .orderBox > div {
      width: 3.1rem;
      height: 1.3rem;
      background-color: #fff;
      border-radius: 0.1rem;
      display: flex;
      justify-content: left;
      align-items: start;
      box-sizing: border-box;
      padding: 0.2rem;

      > div {
        width: 2rem;
      }

      .actually, .onSale {
        display: inline-block;
        width: 0.36rem;
        height: 0.36rem;
        margin-right: 0.1rem;
      }

      .actually {
        background: url("../../assets/h5/actually.png");
        background-size: 100% 100%;
      }

      .onSale {
        background: url("../../assets/h5/onSale.png");
        background-size: 100% 100%;
      }

      .num {
        font-size: 0.42rem;
        font-weight: bold;
        width: 100%;
        line-height: 0.5rem;
        margin-top: 0.1rem;
      }
    }

    .orderList {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-top: 0.3rem;

      > div {
        text-align: center;
      }

      .line {
        width: 1px;
        height: 0.5rem;
        background-color: #fff;
        opacity: 0.5;
      }

      .listTitle {
        font-size: 0.24rem;
        color: #fff;
        opacity: 0.7;
      }

      .listNum {
        font-size: 0.3rem;
        font-weight: bold;
        color: #fff;
        margin-top: 0.1rem;
      }
    }
  }

  .allOrder {
    box-sizing: border-box;
    padding: 0.3rem;
    background-color: #fff;
    margin-top: 0.2rem;
    border-radius: 0.1rem;

    .allOrderTitle {
      font-size: 0.3rem;
      font-weight: 600;
    }

    .notShipped {
      width: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      margin-top: 0.2rem;

      .line {
        width: 1px;
        height: 0.5rem;
        background-color: #ddd;
        margin: 0 0.5rem;
      }

      > div {
        text-align: center;
        font-size: 0.24rem;
        color: #777;

        .count {
          font-size: 0.38rem;
          color: #FE1010;
          line-height: 0.45rem;
          margin-bottom: 0.05rem;
          font-weight: bold;
        }
      }
    }

    .process {
      background: #F3F9FF;
      border-radius: 0.1rem;
      display: flex;
      justify-content: space-around;
      align-items: center;
      box-sizing: border-box;
      padding: 0.3rem;
      font-weight: 400;
      font-size: 0.24rem;
      color: #777777;
      margin-top: 0.3rem;
      text-align: center;

      .p_count {
        font-weight: Bold;
        font-size: 0.38rem;
        color: #2A2A34;
        line-height: 0.45rem;
        margin: 0.1rem 0;
      }

      .rightIcon {
        width: 0.36rem;
        height: 0.11rem;
        background: url("../../assets/h5/right.png");
        background-size: 100% 100%;
        margin-bottom: 1rem;
      }

      .audit, .bill, .sorting {
        width: 1rem;
        height: 1rem;
      }

      .audit {
        background: url("../../assets/h5/audit.png");
        background-size: 100% 100%;
      }

      .bill {
        background: url("../../assets/h5/Bill.png");
        background-size: 100% 100%;
      }

      .sorting {
        background: url("../../assets/h5/Sorting.png");
        background-size: 100% 100%;
      }
    }
  }

  .serviceQuality {
    box-sizing: border-box;
    padding: 0.3rem;
    background-color: #fff;
    border-radius: 0.1rem;
    margin-top: 0.2rem;

    .quality {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-top: 0.3rem;
      text-align: center;

      ::v-deep   .el-progress-circle {
        width: 1.4rem !important;
        height: 1.4rem !important;

        svg {
          transform: rotate(-90deg);
        }
      }

      ::v-deep   .el-progress__text {
        font-weight: bold;
        color: #000;
      }
    }
  }

  .dialog {
    width: 100%;
    height: 100%;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 100;
    background-color: rgba(0, 0, 0, 0.4);


    .dialogWarp {
      width: 100%;
      height: 100%;
      position: relative;
      z-index: 200;

      .dialogContent {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 6.46rem;
        background-color: #fff;
        border-radius: 0.2rem;
        z-index: 200;

        .dialogTitle {
          font-weight: 600;
          font-size: 0.36rem;
          color: #353535;
          line-height: 0.5rem;
          text-align: center;
          margin-top: 0.3rem;
        }

        .contentBox {
          box-sizing: border-box;
          padding: 0.3rem 0.4rem 0;
          max-height: 6.7rem;
          overflow-y: auto;

          .text_content {
            font-size: 0.28rem;
            color: #3C3C3C;
            line-height: 0.42rem;
            margin-bottom: 0.2rem;
          }
        }

        .foot {
          width: 100%;
          border-top: 1px solid #d0d0d0;
          line-height: 1.14rem;
          text-align: center;
        }
      }
    }


  }
}
</style>
