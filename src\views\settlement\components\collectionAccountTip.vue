<template>
  <el-dialog
    title="平台对公收款账户提醒"
    :visible.sync="dialogVisible"
    width="40%"
    :before-close="handleClose"
  >
    <b>
      请将“实际需缴纳佣金金额”转账至下方的平台对公账户
    </b>
    <div class="infoBox">
      <b>公司名称： {{ info.companyName }}</b>
      <br />
      <b>开户行：{{ info.bankName }}</b>
      <br />
      <b>银行账户：{{ info.account }}</b>
    </div>
    <div slot="footer">
      <el-button type="primary" size="small" @click="handleClose">确定</el-button>
    </div>
  </el-dialog>
</template>

<script>

export default {
  name: "collectionAccountTip",
  props: {
    collectionAccountTipVis: {
      type: Boolean,
      default: false,
    },
    info: {
      type: Object,
      default: () => {
        return {}
      }
    },
  },
  data() {
    return {
      dialogVisible: true,
    }
  },
  mounted() {
  },
  methods: {
    handleClose() {
      this.$emit('update:collectionAccountTipVis', false);
    },
    
  },
};
</script>

<style scoped lang="scss">
  .infoBox {
    margin-top: 20px;
    b {
      line-height: 24px;
      color:#ff2121;
    }
  }
</style>
