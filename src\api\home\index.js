import request from '@/utils/request';

// 获取店铺创建状态、店铺状态、erp对接状态
export function getShopInfo(params) {
  return request({
    url: '/index/v2/getShopInfo',
    method: 'get',
    data: params,
  });
}

// 左侧菜单
export function getMenuList(params) {
  return request({
    url: '/index/v2/menuList',
    method: 'get',
    data: params,
  });
}


// 获取在线客服未读消息数量
export function queryMsgCount(params) {
  return request({
    url: '/popCustomerMsg/queryMsgCount',
    method: 'get',
    data: params,
  });
}

// 卖家中心首页统计相关数量后的提示语
export function firstPageStatistics() {
  return request({
    url: '/sellerCommissionSettle/firstPageStatistics',
    method: 'get',
  });
}

// 菜单提示待处理接口
export function waitingProcessing() {
  return request({
    url: '/sellerCommissionSettle/waitingProcessing',
    method: 'get',
  });
}

// 查询token，打开客服系统url
export function queryMsgToken(params) {
  return request({
    url: '/popCustomerMsg/queryToken',
    method: 'get',
    data: params,
  });
}

// banner公告
export function bannerList(params) {
  return request({
    url: '/index/v2/bannerList',
    method: 'get',
    data: params,
  });
}

// 查看公告详情
export function bannerDetail(params) {
  return request({
    url: '/index/v2/bannerDetail?id=' + params,
    method: 'get',
  });
}

// 待办
export function waitDeal(params) {
  return request({
    url: '/index/v2/waitDeal',
    method: 'get',
    data: params,
  });
}

// 近30天经营情况
export function businessCase(params) {
  return request({
    url: '/index/v2/businessCase',
    method: 'get',
    data: params,
  });
}

// 今日数据
export function todayData(params) {
  return request({
    url: '/index/v2/todayData',
    method: 'get',
    data: params,
  });
}

// 平台活动接口
export function platformActivity(params) {
  return request({
    url: '/index/v2/platformActivity',
    method: 'get',
    data: params,
  });
}

// 待办任务
export function selectPendingHandleNum(params) {
  return request({
    url: '/report/groupbuying/apply/selectPendingHandleNum',
    method: 'get',
    data: params,
  });
}


// 卖家中心首页开户提示接口
export function apiHomePageOpenAccountTips() {
  return request({
    url: '/bank/homePageOpenAccountTips',
    method: 'get',
  });
}
// 获取首页展示的服务数据
export function apiQueryServeQuality(params) {
  return request({
    url: '/popSellerService/getHomePageService',
    method: 'post',
    data: params,
  });
}

// 获取首页展示的服务数据
export function login (params) {
  return request({
    url: '/user/ajaxLogin',
    method: 'post',
    data: params,
  });
}

// 卖家中心 首页查询资质是否过期状态 https://yapi.int.ybm100.com/project/891/interface/api/124569
export function getQualificationExpiredNum (params) {
  return request({
    url: '/corporation/v2/getQualificationExpiredNum',
    method: 'get',
    data: params,
  });
}

// 获取首页展示的服务数据
export function getBannerPageList (data) {
  return request({
    url: '/index/v2/bannerPageList',
    method: 'post',
    data
  });
}

//每日待签署提醒
export function signTaskRemind() {
  return request({
    url: '/platformServiceAgreement/signTaskRemind',
    method: 'get',
  });
}

//每日开店任务提醒
export function taskRemind() {
  return request({
    url: '/merchantStoreTask/taskRemind',
    method: 'get',
  });
}

//websocket灰度
export const websocketSwitch = () => {
  return request({
    url: '/afterSales/socket/trialMerchant',
    method: 'get',
  });
}


//首页-校验用户是不是灰度用户
export const checkGrayscaleOrg = () => {
  return request({
    url: '/index/v3/checkGrayscaleOrg',
    method: 'post',
  });
}

//首页-获取模块信息
export const getOrgIndexModule = (params) => {
  return request({
    url: '/index/v3/getOrgIndexModule',
    method: 'post',
    data:params
  });
}

//首页-保存模块信息
export const updateOrgIndexModule = (params) => {
  return request({
    url: '/index/v3/updateOrgIndexModule',
    method: 'post',
    data:params
  });
}
//首页-商家课程banner
export const queryCourseBanner = (params) => {
  return request({
    url: '/index/v3/queryCourseBanner',
    method: 'post',
    data:params
  });
}
//首页-商家课堂获取详情
export const getCourseDetail = (params) => {
  return request({
    url: '/index/v3/getCourseDetail',
    method: 'post',
    data:params
  });
}

//首页-获取分类列表
export const getCategoryList = (params) => {
  return request({
    url: '/index/v3/getCategoryList',
    method: 'post',
    data:params
  });
}
//首页-查询商家课堂数据
export const queryCourse = (params) => {
  return request({
    url: '/index/v3/queryCourse',
    method: 'post',
    data:params
  });
}
//首页-保存课程学习进度
export const saveStudyRecord = (params) => {
  return request({
    url: '/index/v3/record',
    method: 'post',
    data:params
  });
}
//首页-药帮忙联系人

export const getQrcodeDto = (params) => {
  return request({
    url: '/index/v3/getQrcodeDto',
    method: 'post',
    data:params
  });
}
// 获取公告弹框
export function announceDialog() {
  return request({
    url:'/index/v3/getAnnounceDialog',
    method: 'post'
  })
}

// 弹框确认
export function checkAnnounceDialog(params) {
  return request.post('/index/v3/checkAnnounceDialog', params)
}
// 菜单灰度
export function menuSwitch() {
  return request({
    url: '/index/v3/checkGrayscaleOrgV2',
    method: 'post'
  })
}
