<script>
export default {
  props: {
    value: {
      default: () => [],
      type: Array
    },
    maxCount: {
      default: 100,
      type: Number
    },
    isEdit: {
      default: false,
      type: Boolean
    },
    column: {
      default: 2,
      type: Number
    }
  },
  watch: {
    value: {
      handler(newVal) {
        this.fileList = newVal;
      }
    }
  },
  data() {
    return {
      fileList: this.value
    }
  },
  methods: {
    imgClick(item, index) {
      if (this.$refs[`img${index}`] && this.$refs[`img${index}`][0]) {
        this.$refs[`img${index}`][0].clickHandler();
      } else if (item.url) {
        window.open(item.url)
      }
    },
    deleteImg(e,item) {
      e.stopPropagation();
      this.fileList = this.fileList.filter(val => val.name != item.name);
      this.$emit('input', this.fileList);

    },
  }
}
</script>

<template>
  <div class="imgList">
    <div :class="`fileList x-grid-${column}`" v-if="fileList.length">
      <div v-for="(item, index) in fileList" :key="index"  class="fileItem img" @click="imgClick(item, index)" :title="item.name">
        <div style="font-size: 0;">
          <el-image v-if="/.(png|jpg|jpeg)$/.test(item.url)" style="width: 60px;height: 60px;" :ref="`img${index}`" :preview-src-list="[item.url]" fit="cover" :src="item.url"></el-image>
          <div v-else style="width: 60px;height: 60px;position: relative;">
            <span class="el-icon-document pdf-icon"></span>
          </div>
        </div>
        <div style="flex-grow: 1;">
          <p style="font-size: 12px;">{{ item.name }}</p>
        </div>
        <div style="position: relative;width: 40px;flex-shrink: 0;">
          <div v-if="isEdit" class="el-icon-circle-close delete" @click="deleteImg($event,item)"></div>
        </div>
      </div>
    </div>
    <div v-else>
      <el-empty :image-size="100" description="暂无文件"></el-empty>
    </div>
    <div style="padding: 0 10px;text-align: end;color: rgb(164 164 164);">
      <span v-if="!isEdit">共 {{ fileList.length }} 张</span>
      <span v-else>{{ fileList.length + ' / ' + maxCount }}</span>
    </div>
  </div>
</template>

<style scoped>
.imgList {
  gap: 5px;
  display: flex;
  flex-direction: column;
  background-color: white;
  border-radius: 5px;
  gap: 5px;
}
.fileList {
  width: 100%;
  max-height: 500px;
  overflow-y: auto;
  padding: 5px;
}
.fileList .pdf-icon {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 35px;
  color: rgb(124, 124, 124);
}
.fileList .fileItem {
  display: flex;
  overflow: hidden;
  height: 60px;
  gap: 3px;
  border-radius: 5px;
  transition: all 0.2s;
}
.fileList .img {
  cursor: pointer;
}
.fileItem:hover {
  background-color: rgba(0, 0, 0, 0.07);
}
.fileItem:hover .delete{
  display: block;
  opacity: 1;
}
.img:active {
  background-color: rgba(0, 0, 0, 0.15);
}
.fileItem p {
  margin: 0;
  word-break: break-all;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
  overflow: hidden;
  text-overflow: ellipsis;
  text-align: start !important;
}
.fileItem .delete {
  display: none;
  opacity: 0;
  cursor: pointer;
  position: absolute;
  width: 20px;
  height: 20px;
  left: 50%;
  top: 50%;
  font-size: 20px;
  transform: translate(-50%, -50%);
  transition: all 0.2s;
}
.fileItem .delete:hover {
  color: red;
}
.x-grid-1 {
  display: grid;
  grid-template-columns: repeat(1, 1fr);
  gap: 5px;
}
.x-grid-2 {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  padding: 5px;
  gap: 5px;
}
.x-grid-3 {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  padding: 5px;
  gap: 5px;
}
.x-grid-4 {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  padding: 5px;
  gap: 5px;
}
</style>
