<template>
  <div>
    <div class="i-input-box" v-loading="loading" :element-loading-text="`${progress}%`">
      <input class="i-input" type="file" :multiple="multiple" :accept="allowType.join(',')" @change="onChange">
      <div class="i-style">
        <div class="el-icon-upload i-icon"></div>
        <p>点击上传</p>
        <p style="padding: 0 40px;">
          <span>最多上传{{ maxCount }}张，且大小不超过5MB</span>
        </p>
      </div>
      <div v-if="loading" :style="{width:`${progress}%`}" class="mask"></div>
    </div>
  </div>
</template>
<script>
import { uploadFDFS } from '../../../api/qualificationOnline/index'
export default {
  props: {
    multiple: {
      type: Boolean,
      default: false
    },
    allowType: {
      type: Array,
      default: () => []
    },
    allowSize: {
      type: Array,
      default: () => [100, 1024 * 1024 * 5]
    },
    maxCount: {
      type: Number,
      default: 9
    },
    value: {
      type: Array,
      default: () => []
    },
    bucket: {
      type:Number,
      default: 5
    }
  },
  watch: {
    value: {
      handler(newVal) {
        this.fileData = newVal;
      },
    },
    fileData: {
      handler(newVal) {
        this.$emit('input', newVal)
      }
    }
  },
  data() {
    return {
      loading: false,
      fileData: [],
      progress: 0
    }
  },
  methods: {
    onChange(el) {
      if (!el.target.files.length) return
      if (el.target.files.length > this.maxCount) {
        this.$message.error(`最多上传${this.maxCount}张`);
        return
      }
      const fileList = []
      for (let i = 0; i < el.target.files.length; i++) {
        const file = el.target.files[i];
        //判断文件是否已经存在
        if (this.fileData.some(item => item.name === file.name)) {
          this.$message.warning("该图片已经上传过")
          continue
        }
        //判断文件大小是否符合要求
        if (file.size < this.allowSize[0] || file.size > this.allowSize[1]) {
          this.$message.warning("该图片超过5MB")
          continue;
        }
        //判断文件数量是否符合要求
        if (this.fileData.length + 1 > this.maxCount) {
          this.$message.error(`最多上传${this.maxCount}张`);
          return;
        };
        fileList.push(file);
      }
      el.target.value = ''

      if (!fileList.length) return
      //最高一次上传100张，所以10张10张上传
      /* this.loading = true;
      uploadFDFS(fileList[0]).then(res => {
        console.log(res);

      }) */
     this.loading = true;
     this.multipleUpload(0, this.bucket, fileList);
    },
    multipleUpload(start, bucket, fileList) {
      let temp = start;
      const end = start + bucket > fileList.length ? fileList.length : start + bucket;
      const reqList = [];
      while (start < end) {
        reqList.push(uploadFDFS(fileList[start]));
        start++;
      }
      Promise.all(reqList).then(resList => {
        resList.forEach(item => {
          if (item.code == 200 && this.fileData.length < this.maxCount) {
            this.fileData.push({
              name: fileList[temp].name,
              url: process.env.VUE_APP_UPLOAD_API + item.data,
            })
          }
          temp++;
        })
      }).finally(() => {
        this.progress = ((start / fileList.length) * 100).toFixed(2);
        if (start < fileList.length) {
          this.multipleUpload(start, bucket, fileList);
        } else {
          const timer = setTimeout(() => {
            this.progress = 0;
            this.loading = false;
            clearTimeout(timer);
          }, 1000)
        }
      })
    }
  }
}
</script>
<style scoped>
p {
  margin: 5px 0;
}
.i-input-box {
  position: relative;
  width: max-content;
}
.i-input-box .mask {
  position: absolute;
  height: 100%;
  left: 0;
  top: 0;
  background-color: #007fd996;
  transition: all 0.3s;
}
.i-input-box .loading-mask {
  position: absolute;
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
  background-color: rgba(255, 255, 255);
}
.i-input-box .loading-progress {
  position: absolute;
  width: 0;
  height: 100%;
  left: 0;
  top: 0;
  background-color: #5a9ae7;
  transition: all 0.3s;
}
.i-input-box .i-style {
  position: relative;
  width: 350px;
  height: 150px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items:center;
  overflow: hidden;
  border-radius: 5px;
  background-color: white;
  border: dotted 1px #2e2e2e51;
  color:#2e2e2e51;
  transition: all 0.3s;
}
.i-input-box .i-style p {
  margin: 5px 0;
}
.i-input-box .i-icon {
  width: 42px;
  height: 42px;
  font-size: 42px;
}
.i-input-box .i-input {
  position: absolute;
  cursor: pointer;
  width: 100%;
  height: 100%;
  opacity: 0;
  top: 0;
  left: 0;
  z-index: 1;
}
.i-input-box .i-input:hover + .i-style {
  border-color: #007fd9;
  color: #007fd9;
}
</style>
