<template>
  <el-dialog
    title="售卖时间计划设置"
    :visible="dialogVisible"
    width="780px"
    :before-close="handleClose">
    <div class="operation" style="text-align: end;margin: 16px 0">
      <el-button type="primary" size="small" @click="addPlan">新增</el-button>
    </div>
    <div class="contentBox">
      <xyyTable
        ref="productListTable"
        v-loading="tableLoading"
        :data="tableConfig.data"
        :col="tableConfig.col"
        :list-query="listQuery"
        @get-data="queryList"
      >
        <template slot="time">
          <el-table-column label="售卖时间" width="280px">
            <template slot-scope="{row}">
              <div v-for="item in row.time" :key="item.id">
                <div style="display: inline-block;vertical-align: baseline">{{ formatWeek(item.week) }}：</div>
                <div style="display: inline-block;vertical-align: top">
                  <div v-for="(li,index) in item.time" :key="index">
                    {{ li.startHour }}:{{ li.startMinute }} 至 {{ li.endHour }}:{{ li.endMinute }}
                  </div>
                </div>
              </div>
            </template>
          </el-table-column>
        </template>
        <template slot="operation">
          <el-table-column label="操作" fixed="right" width="150">
            <template slot-scope="{row}">
              <el-button type="text" @click="operationClick(1,row)" :loading="row.opEditLoading">编辑</el-button>
              <el-button type="text" @click="operationClick(2,row)" :loading="row.opDelLoading">删除</el-button>
            </template>
          </el-table-column>
        </template>
      </xyyTable>
    </div>
    <editTimePlan
      v-if="editTimePlanDialogVisible"
      :editTimePlanDialogVisible.sync="editTimePlanDialogVisible"
      :config="editTimePlanConfig"
      @updateList="updateList"
    />
  </el-dialog>
</template>

<script>
import editTimePlan from './editTimePlan'
import {saleTimeList, deleteSaleTimeList, countSaleProduct} from "@/api/product";

export default {
  name: "saleTimePlanSetting",
  components: {
    editTimePlan
  },
  data() {
    return {
      dialogVisible: true,
      tableLoading: false,
      tableConfig: {
        data: [],
        col: [
          {
            name: '计划名称',
            index: 'name'
          },
          {
            name: '售卖时间',
            index: 'time',
            slot: 'time',
          },
          {
            name: '操作',
            index: 'operation',
            slot: 'operation',
          }
        ]
      },
      listQuery: {
        pageSize: 10,
        page: 1,
        total: 0,
      },
      editTimePlanDialogVisible: false,
      editTimePlanConfig: {}
    }
  },
  created() {
    this.queryList()
  },
  methods: {
    async queryList(listQuery) {
      this.tableLoading = true
      if (listQuery) {
        const {pageSize, page} = listQuery;
        this.listQuery.pageSize = pageSize;
        this.listQuery.page = page;
      }
      let params = {};
      const {pageSize, page} = this.listQuery;
      params.page = page;
      params.rows = pageSize;
      try {
        const res = await saleTimeList(params)
        if (res && res.code === 0) {
          this.tableConfig.data = res.data.list.map(item => {
            item.opEditLoading = false
            item.opDelLoading = false
            return item
          });
          this.listQuery.total = res.data.total;
        }
      } catch (err) {
        console.log(err)
      }
      this.tableLoading = false
    },
    operationClick(type, row) {
      switch (type) {
        case 1:
          console.log('编辑')
          this.$set(row, 'opEditLoading', true)
          countSaleProduct({id: row.id}).then(res => {
            this.$set(row, 'opEditLoading', false)
            if (res && res.code === 0) {
              if (res.data > 0) {
                const str = `<p>计划名称：${row.name}</p><p>当前有${res.data}个商品使用了该计划，调整计划后对应商品的售卖时间同步变更，确定调整吗？</p>`
                this.$confirm(str, `温馨提示`, {
                  dangerouslyUseHTMLString: true,
                  distinguishCancelAndClose: true,
                  confirmButtonText: '确认',
                  cancelButtonText: '关闭',
                  type: 'warning'
                }).then(async () => {
                  this.editTimePlanConfig = row
                  this.editTimePlanDialogVisible = true
                }).catch((e) => {
                  console.log(e)
                })
              } else {
                this.editTimePlanConfig = row
                this.editTimePlanDialogVisible = true
              }
            } else {
              this.$message.error(res.message || '计划下关联商品数查询失败！')
            }
          }).catch(e => {
            console.log(e)
            this.$set(row, 'opEditLoading', false)
          })
          break
        case 2:
          console.log('删除')
          this.$set(row, 'opDelLoading', true)
          countSaleProduct({id: row.id}).then(res => {
            this.$set(row, 'opDelLoading', false)
            if (res && res.code === 0) {
              const count = res.data
              let str = `<p>计划名称：${row.name}</p><p>确认删除当前计划吗？</p>`
              if (count > 0) {
                str = `<p>计划名称：${row.name}</p><p>当前有${count}个商品使用了该计划，请先更换商品对应 的计划，再删除计划！</p>`
              }
              this.$confirm(str, `${count > 0 ? '温馨提示' : '删除计划'}`, {
                dangerouslyUseHTMLString: true,
                distinguishCancelAndClose: true,
                confirmButtonText: '确认',
                cancelButtonText: '关闭',
                type: 'warning'
              }).then(async () => {
                if (count > 0) {
                  return false
                } else {
                  const res = await deleteSaleTimeList(row.id)
                  if (res && res.code === 0) {
                    this.$message.success('删除成功！');
                    this.queryList()
                  } else {
                    this.$message.error(res.message || '删除失败！')
                  }
                }
              }).catch((e) => {
                console.log(e)
              })
            } else {
              this.$message.error(res.message || '计划下关联商品数查询失败！')
            }
          }).catch(e => {
            console.log(e)
            this.$set(row, 'opDelLoading', false)
          })

          break
      }
    },
    addPlan() {
      this.editTimePlanConfig = {}
      this.editTimePlanDialogVisible = true
    },
    handleClose() {
      this.$emit('refresh')
      this.$emit('update:saleTimePlanSettingDialogVisible', false)
    },
    formatWeek(s) {
      const num = Number(s)
      let str = ''
      switch (num) {
        case 1:
          str = '周一'
          break
        case 2:
          str = '周二'
          break
        case 3:
          str = '周三'
          break
        case 4:
          str = '周四'
          break
        case 5:
          str = '周五'
          break
        case 6:
          str = '周六'
          break
        case 7:
          str = '周日'
          break
      }
      return str
    },
    updateList() {
      this.queryList()
    }
  }
}
</script>

<style scoped lang="scss">
::v-deep   #tabled tr td .cell {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  white-space: break-spaces;
  font-size: 12px;
  font-family: PingFangSC, PingFangSC-Regular;
  font-weight: 400;
  text-align: left;
  color: #666666;
  line-height: 22px;
  padding-left: 16px;

  .busAreaNameBox {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .productInfo {
    text-align: left;
    font-size: 12px;
    font-family: PingFangSC, PingFangSC-Regular;
    font-weight: 400;
    color: #999999;
    line-height: 18px;

    .productName {
      font-size: 14px;
      font-family: PingFangSC, PingFangSC-Medium;
      font-weight: 500;
      color: rgba(51, 51, 51, 0.85);
      line-height: 22px;
    }

    .manufacturer {
      color: rgba(51, 51, 51, 0.85);
    }
  }

  .el-button {
    height: 22px;
    padding: 0;
    margin: 0;
    text-align: left;
    font-size: 12px;
    line-height: 22px;
  }

  .el-button:before {
    display: none;
  }
}

.contentBox {
  max-height: 500px;
  overflow-y: auto;
}

</style>
