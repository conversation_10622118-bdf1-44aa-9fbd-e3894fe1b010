import request from '@/utils/request';

//获取楼层基本信息列表
export function getFloorList(params) {
    return request({
        url: '/floor/v2/list',
        method: 'post',
        params: params,
    });
}
// 省域接口查询
export function branchsList(params) {
    return request({
      url: '/branch/list/v2/new',
      method: 'get',
      data: params,
    });
}
// 新增楼层
export function addFloor(params) {
    return request({
        url: '/floor/v2/addFloor',
        method: 'post',
        params: params,
    });
}

// 编辑楼层
export function editorFloor(params) {
  return request({
      url: '/floor/v2/editor',
      method: 'post',
      params: params,
  });
}

// 停用楼层
export function stopUsingFloor(params) {
  return request({
      url: '/floor/v2/stopUsing',
      method: 'post',
      params: params,
  });
}

// 启用楼层
export function enableFloor(params) {
  return request({
      url: '/floor/v2/enableFloor',
      method: 'post',
      params: params,
  });
}

//获取新增商品配置列表
export function getListGoods(params) {
  return request({
      url: '/floor/v2/listEcGoods',
      method: 'post',
      params: params,
  });
}

//获取已选择商品配置列表
export function getCheckListGoods(params) {
  return request({
      url: '/floor/v2/goods/list',
      method: 'post',
      params: params,
  });
}

//修改商品排序
export function editGoods(params) {
  return request({
      url: '/floor/v2/editGoods',
      method: 'post',
      params: params,
  });
}

//批量删除商品
export function delGoods(params) {
  return request({
      url: '/floor/v2/delGoods',
      method: 'post',
      params: params,
  });
}

//批量保存商品
export function batchGoods(params) {
  return request({
      url: '/floor/v2/batchGoods',
      method: 'post',
      params: params,
  });
}

// 广告列表查询
export function getAdvertiseList(params) {
    return request({
      url: '/advertise/v2/list',
      method: 'get',
      params: params,
    });
}
// 广告新增
export function getAdvertiseAdd(params) {
    return request({
      url: '/advertise/v2/add',
      method: 'post',
      params: params,
    });
}
// 广告编辑
export function getAdvertiseEdit(params) {
    return request({
      url: '/advertise/v2/edit',
      method: 'post',
      params: params,
    });
}
// 停用广告
export function stopUsingAdvertise(params) {
    return request({
      url: '/advertise/v2/stopUsing',
      method: 'post',
      params: params,
    });
}
// 启用广告
export function enableAdvertise(params) {
    return request({
      url: '/advertise/v2/enable',
      method: 'post',
      params: params,
    });
}
export function getHostName(params) {
  return request({
    url: '/uploadFile/cdn/hostName',
    method: 'post',
    data: params,
  });
}

/**
 * 查询业务类型
 * @param params
 * @returns {AxiosPromise}
 */
export function getBusinessTypes() {
  return request({
    url: '/popDeliveryInfo/getBusinessTypes',
    method: 'get'
  });
}

/**
 * 新增修改快递面单打印配置
 * @param params
 * @returns {AxiosPromise}
 */
export function addOrUpdatePopDeliveryInfo(params) {
  return request({
    url: '/popDeliveryInfo/addOrUpdatePopDeliveryInfo',
    method: 'post',
    data: params,
  });
}

/**
 * 查询快递面单打印配置
 * @returns {AxiosPromise}
 */
export function getPopDeliveryInfo() {
  return request({
    url: '/popDeliveryInfo/getPopDeliveryInfo',
    method: 'get'
  });
}

// 添加、编辑图片楼层广告
export function picAdvSave(params) {
  return request({
    url: '/advertise/v2/picAdvSave',
    method: 'post',
    data: params,
  });
}

// 图片楼层广告列表
export function picAdvList(params) {
  return request({
    url: '/advertise/v2/picAdvList',
    method: 'get',
    params,
  });
}

// 删除图片楼层广告
export function deleteAd(params) {
  return request({
    url: `/advertise/v2/delete/${params.id}`,
    method: 'post',
    data: params,
  });
}

// 楼层批量增加商品
export function batchAddGoods(params, floorId) {
  return request({
    url: `/floor/v2/batchAddGoods/${floorId}`,
    method: 'post',
    data: params,
  });
}

// 根据poiId查询企业基础信息列表
export function listCorpBaseInfoByPoiId(params) {
  return request({
    url: '/corporation/v2/listCorpBaseInfoByPoiId',
    method: 'get',
    params,
  });
}

// 根据poiId查询企业资质信息列表
export function listCorpQualificationByPoiId(params) {
  return request({
    url: '/corporation/v2/listCorpQualificationByPoiId',
    method: 'get',
    params,
  });
}

// 根据poiId查询企业服务质量
export function listCorpServiceByPoiId(params) {
  return request({
    url: '/corporation/v2/listCorpServiceByPoiId',
    method: 'get',
    params,
  });
}

// 次日达配置保存
export function nextDaySave(data) {
  return request({
    url: '/nextday/saveConf',
    method: 'post',
    data,
  });
}
// 次日达配置查询
export function nextDayQueryConf() {
  return request({
    url: '/nextday/queryConf',
    method: 'get',
  });
}
//次日达申请开通
export function nextdayApplyOpen() {
  return request({
    url: '/nextday/applyOpen',
    method: 'post',
  });
}
//操作日志查看
export function queryOperateLog(params) {
  return request({
    url: '/nextday/queryOperateLog',
    method: 'get',
    params
  });
}
//次日达阅读
export function nextdayAgree() {
  return request({
    url: '/nextday/agree',
    method: 'post',

  });
}
//次日达时间
export function timeConfig() {
  return request({
    url: '/nextday/time/config',
    method: 'post',
  });
}

