import request from '../index';

/**
 * 获取子公司选项
 */
export function getBranchList() {
  return request.get('/areaManagement/getBranchList');
}

/**
 * 提报框架-查询拼团主题
 * @param {参数} params
 */
export function apiGetFrameActBaseForReport(params) {
  return request.get('/report/groupbuying/apply/getFrameActBaseForReport', params);
}
/**
 * 提报框架-查询竞价拼团主题
 */
export function apiGetFrameActBaseForAuction(params) {
  return request.get('/report/groupbuying/apply/getFrameActBaseForBidding', params)
}
/**
 * 提报框架-查询提报列表
 * @param {参数} params
 */
export function apiApplyList(params) {
  return request.post('/report/groupbuying/apply/list', params, { headers: { 'Content-Type': 'application/json;charset=UTF-8' } });
}

/**
 * 提报框架-提报列表导出
 * @param {参数} params
 */
export function apiApplyExport(params) {
  return request.post('/report/groupbuying/apply/export', params);
}

/**
 * 提报框架-提报列表下线
 * @param {参数} params
 */
export function apiOffLine(params) {
  return request.get('/report/groupbuying/apply/offLine', params);
}

/**
 * 提报框架-编辑
 * @param {参数} params
 */
export function apiSelectByFrameReportId(params) {
  return request.post('/report/groupbuying/apply/selectByFrameReportId', params);
}

/**
 * 提报框架-确定编辑
 * @param {参数} params
 */
export function apiSelectUpdate(params) {
  return request.post('/report/groupbuying/apply/update', params);
}

/**
 * 提报框架-确定编辑
 * @param {参数} params
 */
export function apiSelectReportGroupCount(params) {
  return request.get('/report/groupbuying/apply/selectReportGroupCount', params);
}

/**
 * 提报框架-获取费用承担方
 * @param {参数} params
 */
export function apiSelectInitiatorList(params) {
  return request.get('/report/groupbuying/apply/selectInitiatorList', params);
}

/**
 * 提报框架-批量修改
 * @param {参数} params
 */
export function apiBatchUpdate(params) {
  return request.postFormData('/report/groupbuying/apply/batchUpdate', params);
}

/**
 * 提报框架-一键提审
 * @param {参数} params
 */
export function batchCommitAudit(params) {
  return request.postFormData('/report/groupbuying/apply/batchCommitAudit', params);
}

export function getTimeStr(params) {
  return request.get('/report/groupbuying/apply/actReportBaseDetail', params);
}

export function uploadGoodsCustomer(params) {
  return request.postFormData('/report/groupbuying/apply/importGroupProduct', params);
}

export function sendCustomerData(params) {
  return request.post('/report/groupbuying/apply/save', params);
}

export function saveActivity(params) {
  return request.post('/report/groupbuying/apply/update', params);
}
export function updateCheck(params) {
  return request.post('/report/groupbuying/apply/updateCheck', params);
}

export function selectOperationLog(params) {
  const str = []
  Object.keys(params).forEach(key=>{
    str.push(`${key}=${params[key]}`)
  })
  return request.post('/report/groupbuying/apply/selectOperationLog?'+str.join('&'));
}

export function getStatusAndCount(params) {
  return request.post('/report/groupbuying/apply/getStatusAndCount', params);
}

// 按钮权限
export function isCanCreateFullGive(data) {
  return request.get('/fullGive/isCanCreateFullGive', data);
}

// 拼团活动提报 下载
export function exportWarningLimitData(params) {
  return request.post('/report/groupbuying/apply/exportWarningLimitData', params, { responseType: 'blob' });
}

// 是否灰度店铺-pop
export function getIsApplyStepPrice(data) {
  return request.get('/report/groupbuying/apply/getIsApplyStepPrice', data);
}

// 拼团主题
export function getBaseNameList(data) {
  return request.get('/report/groupbuying/apply/getBaseNameList', data);
}

export function getSkuByBarcode(params) {
  return request.postFormData('/report/groupbuying/apply/getSkuByBarcode', params);
}

// 创建单个拼团活动判重
export function saveReportCheck(data) {
  return request.post('/report/groupbuying/apply/saveReportCheck', data);
}

// 批量创建拼团活动判重
export function checkImportGroupProduct(params) {
  return request.postFormData('/report/groupbuying/apply/importGroupProduct/check', params);
}

// 单个创建拼团活动
export function addApply(params) {
  return request.post('/report/groupbuying/apply/add', params);
}
export function getSupportPersonalLimitType() {
  return request.get('/report/groupbuying/apply/getSupportPersonalLimitType');
}
// 查询人群
export function getCustomerInfo(params) {
  return request.postFormData('/insight/get', params);
}


export function getMerchantList(data) {
  return request.post('/insight/merchant/list', data);
}

// 拼团销售数据 https://yapi.int.ybm100.com/project/2795/interface/api/129033
/**
 * 获取活动销售数据概要信息
 * @param {参数} params
 */
export function getSummaryInfo(params) {
  return request.post('/promo/activity/saleData/getSummaryInfo', params, {
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
    transformRequest: [function (data) {
      let ret = '';
      Object.keys(data)
        .forEach((item) => {
          ret += `${encodeURIComponent(item)}=${encodeURIComponent(data[item])}&`;
        });
      return ret;
    }],
  });
}

// 随心拼-获取店铺随心拼数据
export function getDefaultDataFromApollo(data) {
  return request.get('/groupFollowHeart/getDefaultDataFromApollo', data);
}

// 导出
export function getApplyExport(data) {
  return request.get('/report/groupbuying/apply/export', data);
}

//切换框架刷新
export function freshFrame(data) {

  return request.get('/recommendReport/fresh',data);
}

//查询表格
export function getTableData(data){
  return request.post('/recommendReport/queryList',data);
}

//更新
export function updateTableData(data){
  return request.post('/recommendReport/update',data);
}

//提交
export function submitTableData(data){
  return request.get('/recommendReport/submit',data);
}

//导出
export function exportTableData(data){
  return request.get('/recommendReport/export',data);
}

//querySetting
export function querySetting(data){
  return request.get('/recommendReport/setting/query',data);
}
//createSetting
export function createSetting(params){
  return request.get('/recommendReport/setting/create',params);
}
//updateSetting
export function updateSetting(data){
  return request.get('/recommendReport/setting/update',data);
}
//已提取待确认
//删除
export function confirmDelete(data){
  return request.post('/groupbuying/auto/report/delete',data);
}
//导出商品列表
export function confirmExport(data)
{
  return request.post('/groupbuying/auto/report/export',data);
}
//清空
export function confirmCleanTable(data)
{
  return request.post('/groupbuying/auto/report/clear',data);
}

//提交审核
export function confirmSubmit(data)
{
  return request.post('/groupbuying/auto/report/submit',data);
}
//一键提报
export function confirmOneKey(data)
{
  return request.post('/groupbuying/auto/report/submit/all',data);
}
//查询表格数据
export function confirmQueryTable(data)
{
  return request.post('/groupbuying/auto/report/page/list',data)
}
//店铺动销商品提报
export function dynamicProducts(params) {
  return request.post('/groupbuying/auto/report/dynamic/products',params)
}
//批量导入活动
export function batchImportActivities(params) {
  return request.post('/groupbuying/auto/report/batch/import/activities',params)
}
//批量导入商品
export function batchImportProducts(params) {
  return request.post('/groupbuying/auto/report/batch/import/products',params)
}
//单个活动提报
export function singleActivity(params) {
  return request.post('/groupbuying/auto/report/single/activity',params)
}
//批量导入商品check
export function productsCheck(params) {
  return request.post('/groupbuying/auto/report/batch/import/products/check',params)
}

export function reportUpdate(params) {
  return request.post('/groupbuying/auto/report/update',params)
}
//删除
export function recommendReportDelete(params) {
  return request.get('/recommendReport/delete',params)
}
/**
 * 提报框架-提报列表下线之前校验是否需要额外提示
 * @param {参数} params
 */
export function apiCheckOffLine(params) {
  return request.get('/report/groupbuying/apply/checkOff', params);
}