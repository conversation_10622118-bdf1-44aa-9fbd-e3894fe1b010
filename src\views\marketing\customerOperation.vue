<template>
  <div class="customerOperation-box">
    <crowd-selector
      :operate-able="true"
      class="crowd-selector-root"
    />
  </div>
</template>
<script>
import CrowdSelector from 'components/xyy/customerOperatoin/crowd-selector';

export default {
  name: 'customerOperation',
  components: { CrowdSelector },
  data() {
    return {};
  },
};
</script>
<style lang="scss" scoped>
.crowd-selector-root{
  background: rgba(255, 255, 255, 1);
  /* padding: 0 20px 16px 20px; */
  border-radius: 4px;
  padding: 15px 20px;
}
</style>
