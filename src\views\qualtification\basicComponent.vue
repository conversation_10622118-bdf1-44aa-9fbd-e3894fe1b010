<template>
  <div
    class="basic-box"
    style="padding-right: 40px"
  >
    <p
      v-if="(basic.remarks && qualStateStr === 6) || (isHistory && qualStateStr === 2)"
      style="color: #f5222d; font-size: 12px; padding-left: 20px"
    >
      {{ basic.remarks ? '驳回原因：' + basic.remarks : '' }}
    </p>
    <el-form
      ref="basic"
      label-width="130px"
      size="small"
      label-position="right"
      :rules="basicRules"
      :model="basic"
    >
      <el-form-item
        ref="companyName"
        label="企业名称："
        class="width50"
        prop="companyName"
      >
        <el-input
          v-if="isDetail"
          v-model.trim="basic.companyName"
          type="text"
          placeholder="请输入营业执照上的企业名称"
        />
        <span v-else>{{ basic.companyName }}</span>
      </el-form-item>
      <el-form-item
        ref="regCode"
        label="营业执照号："
        class="width50"
        prop="regCode"
      >
        <el-input
          v-if="isDetail"
          v-model.trim="basic.regCode"
          type="text"
          placeholder="请输入营业执照号"
        />
        <span v-else>{{ basic.regCode }}</span>
      </el-form-item>
      <el-form-item
        ref="corporationType"
        label="企业类型："
        class="width50"
        prop="corporationType"
      >
        <el-select
          v-if="isDetail"
          v-model.trim="basic.corporationType"
          class="width100"
          @change="changeComType"
        >
          <el-option
            label="经营企业"
            :value="0"
          />
          <el-option
            label="生产企业"
            :value="1"
          />
        </el-select>
        <span v-else>{{ basic.corporationType === 1 ? '生产企业' : '经营企业' }}</span>
      </el-form-item>
      <el-form-item
        ref="addr"
        label="企业地址："
        class="width50"
        prop="addr"
      >
        <div v-if="isDetail">
          <el-form-item class="width25">
            <el-select
              v-model.trim="basic.provId"
              placeholder="省"
              @change="selectChange($event, 'cityList')"
            >
              <el-option
                v-for="(item, index) in provList"
                :key="index"
                :label="item.areaName"
                :value="item.areaCode"
              />
            </el-select>
          </el-form-item>
          <el-form-item class="width25">
            <el-select
              v-model.trim="basic.cityId"
              placeholder="市"
              @change="selectChange($event, 'areaList')"
            >
              <el-option
                v-for="(item, index) in cityList"
                :key="index"
                :label="item.areaName"
                :value="item.areaCode"
              />
            </el-select>
          </el-form-item>
          <el-form-item class="width25">
            <el-select
              v-model.trim="basic.areaId"
              placeholder="区"
              @change="selectChange($event, 'streeList')"
            >
              <el-option
                v-for="(item, index) in areaList"
                :key="index"
                :label="item.areaName"
                :value="item.areaCode"
              />
            </el-select>
          </el-form-item>
          <el-form-item class="width25">
            <el-select
              v-model.trim="basic.streetId"
              placeholder="街道"
              @change="selectChange($event, '')"
            >
              <el-option
                v-for="(item, index) in streeList"
                :key="index"
                :label="item.areaName"
                :value="item.areaCode"
              />
            </el-select>
          </el-form-item>
        </div>
        <span
          v-else
        >{{ basic.prov }} {{ basic.city }} {{ basic.area }} {{ basic.streetName }}</span>
        <div v-if="isDetail">
          <el-input
            v-model.trim="basic.addr"
            type="text"
            placeholder="请输入营业执照上的注册地址"
          />
        </div>
        <span v-else>{{ basic.addr }}</span>
      </el-form-item>
      <el-form-item
        v-if="
          !isDetail &&
            (basic.state === 3 ||
              basic.state === 2 ||
              basic.state === 5 ||
              (basic.state === 1 && isHistory))
        "
        ref="name"
        label="店铺名称："
        class="width50"
        prop="name"
      >
        <span v-if="basic.name">{{ basic.name }}</span>
      </el-form-item>
      <el-form-item
        ref="logoUrl"
        label="店铺LOGO"
        prop="logoUrl"
        v-if="shopConfig.shopPatternCode !== 'ybm'"
      >
        <el-upload
          class="avatar-uploader"
          action=""
          :http-request="uploadImg"
          :before-upload="beforeAvatarUpload"
          :disabled="(isDetail||ifModify ) ? false : true"
          :show-file-list="false"
        >

          <img
            v-if="basic.logoUrl"
            :src="basic.logoUrl"
            class="avatar"
          >
          <i
            v-else
            class="el-icon-plus avatar-uploader-icon"
          />
          <div
            v-if="isDetail"
            slot="tip"
            class="el-upload__tip"
          >
            不超过1MB，支持jpg、png格式
          </div>
        </el-upload>
        <el-input
          v-show="false"
          v-model.trim="basic.logoUrl"
        />
      </el-form-item>
      <el-form-item
        ref="customerServicePhone"
        label="客服电话："
        class="width50"
        prop="customerServicePhone"
        v-if="shopConfig.shopPatternCode !== 'ybm'"
      >
        <el-input
          v-if="isDetail||ifModify"
          v-model.trim="basic.customerServicePhone"
          type="text"
          placeholder="请输入企业客服电话，方便客户联系"
          :disabled="shopConfig.shopPatternCode==='ybm'"
        />
        <span v-else>{{ basic.customerServicePhone }}</span>
      </el-form-item>
      <el-form-item
        label="固定电话："
        class="width50"
      >
        <el-input
          v-if="isDetail||ifModify"
          v-model.trim="basic.fixedPhone"
          type="text"
          maxlength="20"
          placeholder="请输入企业固定电话"
        />
        <span v-else>{{ basic.fixedPhone }}</span>
      </el-form-item>
      <el-form-item
        label="电子邮件："
        class="width50"
        prop="email"
      >
        <el-input
          v-if="isDetail||ifModify"
          v-model.trim="basic.email"
          type="text"
          maxlength="50"
          placeholder="请输入企业电子邮箱"
        />
        <span v-else>{{ basic.email }}</span>
      </el-form-item>
      <el-form-item
        label="网址："
        class="width50"
      >
        <el-input
          v-if="isDetail||ifModify"
          v-model.trim="basic.web"
          type="text"
          maxlength="50"
          placeholder="请输入企业网站地址"
        />
        <span v-else>{{ basic.web }}</span>
      </el-form-item>
      <el-form-item
        label="简介："
        class="width50"
      >
        <el-input
          v-if="isDetail||ifModify"
          v-model.trim="basic.brief"
          type="textarea"
          maxlength="1000"
          placeholder="请输入企业简介"
          show-word-limit
        />
        <span v-else>{{ basic.brief }}</span>
      </el-form-item>
      <!-- 5-11修改店铺经营属性文案 -->
      <el-form-item
        v-if="!qualStateStr || qualStateStr === 2 || qualStateStr === 5"
        label="店铺经营属性："
        class="width50"
        ref="businessAttribute"
        prop="businessAttribute"
        :rules="!qualStateStr ? [{ required: true, message: '请选择店铺经营属性', trigger: 'change' }] : []"
      >
        <div v-if="!qualStateStr" style="color:red;">请按照店铺后期实际售卖商品范围勾选，仅支持单选</div>
        <el-radio-group v-if="!qualStateStr" v-model="basic.businessAttribute">
          <el-radio :label="1">药品店铺</el-radio>
          <el-radio :label="2">非药店铺</el-radio>
          <el-radio :label="3">中药店铺</el-radio>
          <el-radio :label="4">器械店铺</el-radio>
        </el-radio-group>
        <span v-else>{{ {1: '药品店铺', 2: '非药店铺', 3: '中药店铺', 4: '器械店铺'}[basic.businessAttribute] }}</span>
      </el-form-item>


      <el-form-item label="店铺售卖区域：" v-if="!qualStateStr || qualStateStr === 2 || qualStateStr === 5">
        <div style="color: red" v-if="!qualStateStr">请选择店铺的售卖省份，最多可选择7个省份</div>
        <span v-if="qualStateStr === 2 || qualStateStr === 5">{{ drugsAreaText }}</span>
        <el-checkbox-group v-if="!qualStateStr && provList.length" v-model="drugsAreaCodes">
          <el-checkbox
            v-for="(item, index) in provList"
            :key="index"
            :label="item.areaCode"
          >
            {{ item.areaName }}
          </el-checkbox>
        </el-checkbox-group>
      </el-form-item>
      <!-- <el-form-item label="非药品售卖区域：" v-if="!qualStateStr || qualStateStr === 2 || qualStateStr === 5"> -->
      <el-form-item label="非药品售卖区域：" v-if="false">
        <div style="color: red" v-if="!qualStateStr">如店铺售卖非药品，请选择非药品的售卖省份，最多可选择5个省份</div>
        <span v-if="qualStateStr === 2 || qualStateStr === 5">{{ nonDrugAreaText }}</span>
        <el-checkbox-group v-if="!qualStateStr && provList.length" v-model="nonDrugAreaCodes">
          <el-checkbox
            v-for="(item, index) in provList"
            :key="index"
            :label="item.areaCode"
          >
            {{ item.areaName }}
          </el-checkbox>
        </el-checkbox-group>
      </el-form-item>
      <!-- <el-form-item
        v-if="
          !isDetail &&
            !isHistory &&
            basic.state &&
            (basic.state === 3 || basic.state === 5) &&
            basic.drugsArea &&
            basic.drugsArea.length > 0
        "
        label="可售卖区域(药品)"
      >
        {{ drugsAreaStr }}
      </el-form-item> -->
      <!-- <el-form-item
        v-if="
          !isDetail &&
            !isHistory &&
            basic.state &&
            (basic.state === 3 || basic.state === 5) &&
            basic.nonDrugArea &&
            basic.nonDrugArea.length > 0
        "
        label="可售卖区域(非药品)"
      >
        {{ nonDrugAreaStr }}
      </el-form-item> -->
      <el-form-item
        v-if="
          !isDetail &&
            (basic.state === 3 ||
              basic.state === 2 ||
              basic.state === 5 ||
              (basic.state === 1 && isHistory))
        "
        label="经营范围："
      >
        <div>
          {{ businessStr }}
        </div>
        <div
          v-if="
            (basic.checkCorporationBusiness &&
              basic.checkCorporationBusiness.length > 30)||
              (basic.corporationBusiness&&
              basic.corporationBusiness.length > 30)
          "
          style="text-align: right;color: #4183d5;cursor: pointer"
          class="moreBtn"
          @click="showMore"
        >
          {{ moreText }}
        </div>
        <!--        <div v-if="isMoreShow" @click="showMore">-->
        <!--          {{ basic.corporationBusiness.length > 30 ? businessStr : businessStr + '......' }}-->
        <!--        </div>-->
        <!--        <div>展开</div>-->
      </el-form-item>
      <!-- todo -->
      <el-form-item
        v-if="!isDetail && priceType"
        label="价格计算方式："
      >
        <div>
          {{
            priceType === 1
              ? '售价（直接使用单体采购价和连锁采购价进行售卖）'
              : '底价毛利（通过底价/(1-毛利/100)计算单体采购价和连锁采购价）'
          }}
        </div>
      </el-form-item>
      <el-form-item
        v-if="!qualStateStr || qualStateStr === 2 || qualStateStr === 5"
        label="佣金结算方式："
        ref="settlementType"
        prop="settlementType"
        :rules="[{ required: true, message: '请选择佣金结算方式', trigger: 'change' }]"
      >
        <div  v-if="!qualStateStr" style="color:red;">请选择店铺佣金的结算方式，一旦选择立即生效，如需变更次月生效</div>
        <el-radio-group v-model="basic.settlementType" v-if="!qualStateStr">
          <el-radio :label="1">非月结（单据结算实时扣除佣金）</el-radio>
          <el-radio :label="2">月结（单据结算不扣除佣金。次月1号生成本月所有账单的佣金缴纳记录，次月15号之前需按照佣金缴纳记录的“实际需缴纳金额”向平台对公账户统一打款，逾期未打款将影响提现功能）</el-radio>
        </el-radio-group>
        <div v-else>
          {{
            basic.settlementType === 1
              ? '非月结（单据结算实时扣除佣金）'
              : '月结（单据结算不扣除佣金。次月1号生成本月所有账单的佣金缴纳记录，次月15号之前需按照佣金缴纳记录的“实际需缴纳金额”向平台对公账户统一打款，逾期未打款将影响提现功能）'
          }}
        </div>
      </el-form-item>
      <el-form-item
        v-if="!isDetail && (basic.state !== 2 && basic.state !== 5)"
        label="佣金优惠政策："
      >
        <span v-if="basic.commissionDiscountVos.length === 0">暂不享受优惠政策</span>
        <div v-else>
          <div v-for="(item, index) in basic.commissionDiscountVos" :key="index">
            上月实付金额≥ {{ item.limitMoney }} 元，佣金折扣比例：{{ item.discountRate }}
          </div>
        </div>
      </el-form-item>
      <el-form-item
          v-if="!isDetail && basic.toCorTransferVo"
          label="线下转账收款方："
      >
        <div>
          <div class="tipBox">
            {{basic.toCorTransferVo.transferTo == 0?'未设置':basic.toCorTransferVo.transferTo == 1?'平台收款':'商业收款'}}
            <el-tooltip
                v-if="basic.toCorTransferVo.transferTo == 2"
                class="item"
                effect="dark"
                placement="bottom-start"
            >
              <template #content>
                <br>1、客户提交线下转账订单，会将货款转帐至下方的收款账户。若收款账户信息需变更，请您与平台运营及时沟通处理
              </template>
              <p class="span-tip">
                ?
              </p>
            </el-tooltip>
          </div>
        </div>
        <div v-if="basic.toCorTransferVo.accountName">
          公司名称：{{basic.toCorTransferVo.accountName}}
        </div>
        <div v-if="basic.toCorTransferVo.accountBank">
          开户银行：{{basic.toCorTransferVo.accountBank}}
        </div>
        <div v-if="basic.toCorTransferVo.accountSubBank">
          开户支行：{{basic.toCorTransferVo.accountSubBank}}
        </div>
        <div v-if="basic.toCorTransferVo.accountNum">
          银行账号：{{basic.toCorTransferVo.accountNum}}
        </div>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import { uploadFile, getAddresInfo } from '../../api/qual/index';
import { mapState } from 'vuex';
export default {
  name: 'BasicComponent',
  props: {
    corporationType: {
      type: Number,
      default: 0,
    },
    hostName: {
      type: String,
      default: 'http://t-upload.ybm100.com',
    },
    basicData: {
      type: Object,
      default: () => {},
    },
    isDetail: {
      type: Boolean,
      default: true,
    },
    qualStateStr: {
      type: Number,
      default: null,
    },
    isHistory: {
      type: Boolean,
      default: false,
    },
    ifModify:{
      type: Boolean,
      default: false,
    }
  },
  data() {
    const checkAddres = (rule, value, callback) => {
      if (!value || !this.basic.provId || !this.basic.cityId || !this.basic.areaId) {
        return callback(new Error('请输入营业执照上的注册地址'));
      }
      return callback();
    };
    return {
      basic: {
        companyName: '', // 企业名称
        regCode: '', // 营业执照号
        corporationType: 0, // 企业类型
        provId: '', // 省
        prov: '',
        cityId: '', // 市
        city: '',
        areaId: '', // 区
        area: '',
        streetId: '', // 街道
        streetName: '',
        addr: '', // 详细地址
        logoUrl: '', // 企业logo
        customerServicePhone: '', // 客服电话
        email: '', // 邮箱
        fixedPhone: '', // 固定电话
        web: '', // 网址
        brief: '', // 简介
        drugsAreaStr: '',
        nonDrugAreaStr: '',
        settlementType: '', // 佣金结算方式
        businessAttribute:'', // 店铺经营属性
        saleAreaStr:'',
      },
      drugsAreaCodes: [],
      nonDrugAreaCodes: [],
      drugsAreaText: '',
      nonDrugAreaText: '',
      provList: [], // 省份列表
      cityList: [], // 市列表
      areaList: [], // 区域列表
      streeList: [], // 街道列表
      basicRules: {
        companyName: [
          { required: true, message: '请输入企业名称', trigger: 'blur' },
          { min: 1, max: 50, message: '长度在 1 到 50 个字符', trigger: 'blur' },
        ],
        regCode: [
          { required: true, message: '请输入营业执照号', trigger: 'blur' },
          { min: 1, max: 30, message: '长度在 1 到 30 个字符', trigger: 'blur' },
        ],
        corporationType: [{ required: true, message: '请选择企业类型', trigger: 'blur' }],
        addr: [{ required: true, validator: checkAddres, trigger: 'blur' }],
        email: [
          {
            type: 'email',
            message: '请输入正确的邮箱地址',
            trigger: ['blur', 'change'],
          },
        ],
        logoUrl: [{ required: true, message: '请上传店铺LOGO', trigger: 'change' }],
        customerServicePhone: [
          { required: true, message: '请输入企业客服电话，方便客户联系', trigger: 'blur' },
          { min: 1, max: 20, message: '长度在 1 到 20 个字符', trigger: 'blur' },
        ],
      },
      // drugsAreaStr: '',
      // nonDrugAreaStr: '',
      businessStr: '',
      // settlementType: '', // 佣金结算方式
      isMoreShow: false,
      moreText: '展开',
      priceType: '', // 售价模式：空-查询异常，1-售价模式，2-底价模式
    };
  },
  watch: {
    basicData: {
      immediate: true,
      handler(newVale) {
        this.$nextTick(() => {
          if (newVale.id) {
            // this.basic = newVale
          }
        });
      },
    },
  },
  activated() {
    this.getAddres(0, 'provList');
  },
  created() {
    this.basic.corporationType = this.corporationType;
    this.getAddres(0, 'provList');
  },
  methods: {
    showMore() {
      const that = this;
      if (this.moreText === '展开') {
        that.businessStr = '';
        if (
          this.basic.corporationBusiness
          && this.basic.corporationBusiness.length > 0
          && !this.isHistory
        ) {
          this.basic.corporationBusiness.forEach((item) => {
            that.businessStr += `${item.name} `;
          });
        } else if (
          this.basic.checkCorporationBusiness
          && this.basic.checkCorporationBusiness.length > 0
          && this.isHistory
        ) {
          this.basic.checkCorporationBusiness.forEach((item) => {
            that.businessStr += `${item.name} `;
          });
        }
        this.moreText = '收起';
      } else {
        that.businessStr = '';
        if (
          this.basic.corporationBusiness
          && this.basic.corporationBusiness.length > 0
          && !this.isHistory
        ) {
          this.basic.corporationBusiness.forEach((item, index) => {
            if (index < 30) {
              that.businessStr += `${item.name} `;
            }
          });
        } else if (
          this.basic.checkCorporationBusiness
          && this.basic.checkCorporationBusiness.length > 0
          && this.isHistory
        ) {
          this.basic.checkCorporationBusiness.forEach((item, index) => {
            if (index < 30) {
              that.businessStr += `${item.name} `;
            }
          });
        }
        this.businessStr += '......';
        this.moreText = '展开';
      }
    },
    setData(data) {
      const that = this;
      data ? (this.basic = { ...data }) : '';
      this.basic.provId ? this.getAddres(this.basic.provId, 'cityList') : '';
      this.basic.cityId ? this.getAddres(this.basic.cityId, 'areaList') : '';
      this.basic.areaId ? this.getAddres(this.basic.areaId, 'streeList') : '';

      // this.drugsAreaCodes = (this.basic.drugsAreaStr || '').split(',').map(i => Number(i.provId));
      // this.nonDrugAreaCodes = (this.basic.nonDrugAreaStr || '').split(',').map(i => Number(i.provId));
      try {
        this.drugsAreaCodes = JSON.parse(this.basic.saleAreaStr || '[]').map(i => Number(i.provId));
      } catch (error) {}
      // this.drugsAreaCodes = (this.basic.saleAreaStr || []).map(i => Number(i.provId));
      this.nonDrugAreaCodes = (this.basic.nonDrugArea || []).map(i => Number(i.provId));
      if (this.basic.drugsArea && this.basic.drugsArea.length > 0) {
        this.basic.drugsArea.forEach((item) => {
          that.drugsAreaText += `${item.prov} `;
        });
      }
      if (this.basic.nonDrugArea && this.basic.nonDrugArea.length > 0) {
        this.basic.nonDrugArea.forEach((item) => {
          that.nonDrugAreaText += `${item.prov} `;
        });
      }
      if (
        this.basic.corporationBusiness
        && this.basic.corporationBusiness.length > 0
        && !this.isHistory
      ) {
        this.basic.corporationBusiness.forEach((item, index) => {
          if (index < 30) {
            that.businessStr += `${item.name} `;
          }
        });
      } else if (
        this.basic.checkCorporationBusiness
        && this.basic.checkCorporationBusiness.length > 0
        && this.isHistory
      ) {
        this.basic.checkCorporationBusiness.forEach((item, index) => {
          if (index < 30) {
            that.businessStr += `${item.name} `;
          }
        });
      }
      // this.basic.settlementType
      //   ? (this.settlementType = this.basic.settlementType)
      //   : (this.settlementType = '');
      this.basic.priceType
        ? (this.priceType = this.basic.priceType)
        : (this.priceType = '');
    },
    getAddres(code, list) {
      getAddresInfo({ parentCode: code }).then((res) => {
        if (res.code === 0) {
          this[list] = res.data;
        }
      });
    },
    uploadImg(file) {
      uploadFile(file).then((res) => {
        if (res.code === '200') {
          this.basic.logoUrl = `${this.hostName}/${res.data}`;
        } else {
          this.basic.logoUrl = '';
        }
      });
    },
    beforeAvatarUpload(file) {
      const isJPG = file.type === 'image/jpeg' || file.type === 'image/png';
      const isLt2M = file.size / 1024 / 1024 < 1;

      if (!isJPG) {
        this.$message.error('上传图片只能是 JPG、PNG 格式!');
        return false;
      }
      if (!isLt2M) {
        this.$message.error('上传图片大小不能超过 1MB!');
        return false;
      }
      return isJPG && isLt2M;
    },
    selectChange(value, listStr) {
      const that = this;
      switch (listStr) {
        case 'cityList':
          this.cityList = [];
          this.areaList = [];
          this.streeList = [];
          this.basic.cityId = '';
          this.basic.areaId = '';
          this.basic.streetId = '';
          if (that.basic.provId) {
            this.provList.forEach((item) => {
              if (item.areaCode === that.basic.provId) {
                that.basic.prov = item.areaName;
              }
            });
          }
          break;
        case 'areaList':
          this.areaList = [];
          this.streeList = [];
          this.basic.areaId = '';
          this.basic.streetId = '';
          if (this.basic.cityId) {
            this.cityList.forEach((item) => {
              if (item.areaCode === that.basic.cityId) {
                that.basic.city = item.areaName;
              }
            });
          }
          break;
        case 'streeList':
          this.streeList = [];
          this.basic.streetId = '';
          if (this.basic.areaId) {
            this.areaList.forEach((item) => {
              if (item.areaCode === that.basic.areaId) {
                that.basic.area = item.areaName;
              }
            });
          }
          break;
        default:
          if (this.basic.streetId) {
            this.streeList.forEach((item) => {
              if (item.areaCode === that.basic.streetId) {
                that.basic.streetName = item.areaName;
              }
            });
          }
          break;
      }
      this.getAddres(value, listStr);
    },
    changeComType(val) {
      // 切换企业类型
      this.$emit('changeType', val);
    },
    submitForm(f) {
      // const that = this
      let drugsAreaArray = [];
      let nonDrugsAreaArray = [];
      this.drugsAreaCodes.forEach((item) => {
        this.provList.forEach((item2) => {
          if (item === item2.areaCode) {
            drugsAreaArray.push({
              provId: item2.areaCode,
              prov: item2.areaName,
            })
          }
        })
      });
      this.nonDrugAreaCodes.forEach((item) => {
        this.provList.forEach((item2) => {
          if (item === item2.areaCode) {
            nonDrugsAreaArray.push({
              provId: item2.areaCode,
              prov: item2.areaName,
            })
          }
        })
      });
      // this.$set(this.basic, 'drugsAreaArray', drugsAreaArray);
      // this.$set(this.basic, 'nonDrugsAreaArray', nonDrugsAreaArray);
      if (f) {
        this.basic.drugsAreaStr = JSON.stringify(drugsAreaArray);
        this.basic.nonDrugAreaStr = JSON.stringify(nonDrugsAreaArray);

        this.basic.saleAreaStr =  this.basic.drugsAreaStr;
        this.$emit('formBasic', this.basic, 1);
      } else {
        this.$refs.basic.validate((valid, rule) => {
          if (valid) {
            if(!this.qualStateStr && this.drugsAreaCodes.length === 0 && this.nonDrugAreaCodes.length === 0) {
              this.$message.warning('请勾选店铺售卖区域');
              return;
            }
            if (!this.qualStateStr && this.drugsAreaCodes.length > 7) {
              this.$message.warning('药品售卖区域最多可选择7个省份');
              return;
            }
            if (!this.qualStateStr && this.nonDrugAreaCodes.length > 7) {
              this.$message.warning('非药品售卖区域最多可选择7个省份');
              return;
            }
            this.basic.drugsAreaStr = JSON.stringify(drugsAreaArray);
            this.basic.nonDrugAreaStr =  JSON.stringify(nonDrugsAreaArray);

            this.basic.saleAreaStr =  this.basic.drugsAreaStr;

            this.$emit('formBasic', this.basic);
          } else {
            const validAry = rule[Object.keys(rule)[0]];
            const msgName = validAry[0].message;
            // const refs = validAry[0].field
            this.$message.warning({
              message: msgName,
              type: 'warning',
              offset: '60',
            });
            // that.$nextTick(() => {
            //   console.log(that.$refs[refs].$el.getBoundingClientRect())
            // })
            this.$emit('formBasic', false);
          }
        });
      }
    },
  },
  computed: { ...mapState('app', ['shopConfig']) },
};
</script>

<style scoped lang="scss">
.tipBox{
  display: flex;
  align-items: center;
  .span-tip{
    display: flex;
    width: 20px;
    height: 20px;
    font-size: 14px;
    border: 1px solid #4183d5;
    color: #4183d5;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    margin: 0;
    margin-left: 5px;
  }
}
.basic-box {
  .width50 {
    width: 60%;
  }
  .width100 {
    width: 100%;
  }
  .width25 {
    width: 23%;
    display: inline-block;
    margin-right: 5px;
  }
  .padding10 {
    padding-bottom: 18px;
  }
}
.avatar-uploader ::v-deep  .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  background: rgba(0, 0, 0, 0.02);
  width: 126px;
  height: 126px;
  line-height: 126px;
}
.avatar-uploader ::v-deep  .el-upload:hover {
  border-color: #409eff;
}
.avatar-uploader ::v-deep  .el-upload__tip {
  margin-top: 0;
  color: #999999;
  font-size: 12px;
}
.avatar-uploader-icon {
  font-size: 28px;
  color: #666666;
  width: 126px;
  height: 126px;
  line-height: 126px;
  text-align: center;
}
.avatar {
  width: 126px;
  height: 126px;
  display: block;
}
.avatar-uploader ::v-deep  .el-upload-list--picture-card .el-upload-list__item {
  width: 126px;
  height: 126px;
}
</style>
