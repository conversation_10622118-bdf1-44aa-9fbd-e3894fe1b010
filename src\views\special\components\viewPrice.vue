<template>
  <el-dialog title="查看区域价" width="80%"  :visible="dialogTableVisible" :before-close="handleClose">
    <div style="margin-bottom:10px">
      <span style="color:#333333;font-size:14px">商品编码：</span>
      <span style="margin-left:10px">{{ goodsMessage.barcode }}</span>
    </div>
    <div style="margin-bottom:10px">
      <span style="color:#333333;font-size:14px">单品采购价：</span>
      <span style="margin-left:10px">{{ goodsMessage.fob }}</span>
    </div>
    <div style="margin-bottom:10px">
      <span style="color:#333333;font-size:14px">连锁采购价：</span>
      <span style="margin-left:10px">{{ goodsMessage.guidePrice }}</span>
    </div>
    <div style="display: flex;">
      <span style="color:#333333;font-size:14px;margin-right: 10px" >
        分区域价：
      </span>
    <el-table
        class="main-table"
        border
        stripe
        :data="goodsMessage.skuAreaPriceVos"
        :header-cell-style="{ background: '#f9f9f9' }"
      >
        <el-table-column align="center" prop="groupName" label="用户组名称" />
        <el-table-column align="center" prop="price" label="区域价格" >
          <template slot-scope="scope">
            <span style="color: red;">{{ scope.row.price }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="areaNames" label="地域" />
        <el-table-column align="center" prop="customerTypes" label="用户类型" />
      </el-table>
    </div>

  <div slot="footer" style="margin-top: 50px;" >
    <el-button type="primary" size="medium" @click="handleClose">确 定</el-button>
  </div>
</el-dialog>

</template>

<script>
export default {
  name:'viewPrice',
  props: {
    goodsMessage:{
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      dialogTableVisible:true,
      dataSource: []
    }
  },
  methods: {
    handleClose() {
      this.$emit('update:inventoryLogDialogVisible', false)
    },
  },
  mounted() {
    //模拟加载
    // this.loading = true
    // setTimeout(()=>this.loading = false,1000)
  }

}
</script>
<style scoped lang="scss">

</style>
