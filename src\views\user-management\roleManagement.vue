<template>
  <div class="main-box">
    <part-title title="角色列表" />
    <div class="searchMy">
      <el-form
        ref="form"
        class="fix-item-width"
        size="small"
        label-position="right"
        :model="formData"
      >
        <el-row>
          <el-col :lg="6" :sm="12">
            <el-form-item prop="roleName">
              <el-input v-model="formData.roleName" clearable placeholder="请输入角色名称">
                <template slot="prepend">角色名称</template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :lg="6" :sm="12" class="btn-group">
            <el-button size="small" type="primary" @click="searchData">查询</el-button>
            <el-button size="small" @click="resetFields">重置</el-button>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <el-button type="primary" size="small" @click="createRole">新建角色</el-button>
    <div v-loading="loading">
      <el-table
        :data="roleData.list"
        style="margin-top: 10px;"
        border
        max-height="550px"
        :header-cell-style="{ background: '#eeeeee', color: '#666666' }"
      >
        <el-table-column label="角色名称" prop="roleName" />
        <el-table-column label="角色说明" prop="description" />
        <el-table-column label="权限" prop="secondLevelMenuNames">
          <template slot-scope="scope">
            <div v-for="(item,index) in scope.row.secondLevelMenuNames" :key="index">
              <span>{{item.join('、')}}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="160" fixed="right">
          <template slot-scope="{ row }">
            <el-button type="text" @click="editRole(row)">编辑</el-button>
            <el-button type="text" @click="QueryAccountListByRoleId(row)">删除</el-button>
            <el-button type="text" @click="copyFromItem(row)">复制</el-button>
          </template>
        </el-table-column>
        <template slot="empty">
          <div class="noData">
            <p class="img-box">
              <img :src="emptyImg" alt />
            </p>
            <p>暂无数据</p>
          </div>
        </template>
      </el-table>
      <el-row>
        <div class="pagination-container">
          <div
            class="pag-text"
          >共 {{ roleData.total }} 条数据，每页{{ pageInfo.pageSize }}条，共{{ Math.ceil(roleData.total / pageInfo.pageSize) || 0 }}页</div>
          <el-pagination
            :page-sizes="[10, 20, 30, 50]"
            prev-text="上一页"
            next-text="下一页"
            layout="sizes, prev, pager, next, jumper"
            :total="roleData.total"
            :current-page="pageInfo.pageNum"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </el-row>
    </div>
  </div>
</template>
<script>
import { emptyImg } from '@/components/xyy/customerOperatoin/constant';
import partTitle from '@/components/part-title/index';
import { apiListRole , apiDeleteRole , apiQueryAccountListByRoleId } from '@/api/userManagement';
export default {
  name: 'roleManagement',
  components: {partTitle},
  data() {
    return {
      emptyImg,
      loading: false,
      formData: {
        roleName:'',
      },
      roleData: {
        list: [],
        total: 0,
      },
      pageInfo: {
        pageNum: 1,
        pageSize: 10,
      },
    }
  },
  created() {
  },
  mounted() {
    this.searchData();
  },
  activated() {
    if (this.$route.query.refresh) {
      this.getRoleList();
    }
  },
  methods: {
    resetFields() {
      this.$refs.form.resetFields();
      this.searchData();
    },
    searchData() {
      this.pageInfo.pageNum = 1;
      this.getRoleList();
    },
    handleSizeChange(size) {
      this.pageInfo.pageSize = size;
      this.getRoleList();
    },
    handleCurrentChange(val) {
      this.pageInfo.pageNum = val;
      this.getRoleList();
    },
    getRoleList() {
      const that = this;
      this.loading = true;
      let param = {
        ...this.pageInfo,
        ...this.formData
      }
      apiListRole(param).then((res) => {
        if (res.code == 0) {
          that.loading = false
          that.roleData.list = res.data.list || [];
          that.roleData.total = res.data.total;
        }else {
          that.loading = false
           that.$message({
              message: res.message,
              type: 'error'
            })
          }
      }) .catch(() => {});
    },
    // 创建角色
    createRole(){
      this.$router.push({
        path: '/newRole',
        query: { roleType:1 },
      });
    },
    // 编辑角色
    editRole(row){
      this.$router.push({
        path: '/newRole',
        query: { roleId: row.roleId ,roleType:2 },
      });
    },
    // 复制
    async copyFromItem(row) {
      this.$router.push({
        path: '/newRole',
        query: { roleId: row.roleId ,roleType:3 },
      });
    },
    // 查询角色绑定账号
    QueryAccountListByRoleId(row){
      let param = {
        roleId:row.roleId
      }
      apiQueryAccountListByRoleId(param).then((res) => {
        if (res.code == 0) {
          let userList = res.data||[];
          if(userList && userList.length){
            let roleId = '';
            let roleName = '';
            let userName = [];
            userList.forEach((item,index) => {
              if(index === 0){
                roleId = item.roleId;
                roleName = item.roleName;
              }
              userName.push(item.userName);
            });
            userName = userName.join('、');
            const content = `<div><p>该角色存在已绑定的账号，请解除绑定后再进行删除操作！</p><div>"${roleName}"角色目前绑定的账号为：${userName}</div></div>`;
            this.$confirm(content, '提示', {
            showCancelButton: false,
            dangerouslyUseHTMLString: true,
            confirmButtonText: '去解除绑定',
            }).then(() => {
              // 账号管理页面
              const path = '/accountManagement'
              if(this.$store.state.permission.menuGray == 1) {
                const roleIdCopy = roleId
                this.$emit('jumpAccountManagement', roleIdCopy);
              }else {
                const obj = {
                  roleId: roleId
                }
                window.openTab(path,obj)
              }
            })
            .catch(() => {});
          }else{
            this.deleteRole(row);
          }
        }else {
          this.$message({
            message: res.message,
            type: 'error'
          })
        }
      }) .catch(() => {});
    },
    // 删除角色
    deleteRole(row){
      this.$confirm('您确定要删除此角色吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(async () => {
        let param = {
          roleId:row.roleId
        }
        apiDeleteRole(param).then((res) => {
          if (res.code == 0) {
            this.$message.success('删除成功');
            this.getRoleList();
          }else {
            this.$message({
              message: res.message,
              type: 'error'
            })
          }
        }) .catch(() => {});
      }).catch(() => {});
    },
  }
}
</script>

<style lang="scss" scoped>
.main-box {
  background: rgba(255, 255, 255, 1);
  border-radius: 4px;
  padding: 15px 20px;
  .searchMy ::v-deep  .el-form-item__label {
    margin-left: 20px;
    padding: 0;
  }
  .searchMy ::v-deep  .el-input__inner {
    border-radius: 0 4px 4px 0;
  }
  .searchMy ::v-deep  .el-date-editor {
    width: 100%;
  }
  .search-title {
    display: table-cell;
    padding: 0 10px;
    text-align: center;
    border: 1px solid #dcdfe6;
    height: 30px;
    line-height: 30px;
    vertical-align: middle;
    border-right: none;
    border-radius: 4px 0 0 4px;
    color: #333333;
    white-space: nowrap;
  }
  .searchMy ::v-deep  .el-select {
    display: table-cell;
    width: 100%;
  }
  .searchMy ::v-deep  .el-form-item__content {
    width: 100%;
  }
  .searchMy ::v-deep  .el-form-item--small.el-form-item {
    margin-bottom: 10px;
    width: 32%;
  }
  .searchMy ::v-deep  .el-form-item--small .el-form-item__content {
    line-height: 30px;
    width: 100%;
  }
  .searchMy ::v-deep  .el-input-group__prepend {
    background: none;
    color: #333333;
    padding: 0 10px;
  }
  .Fsearch {
    padding: 0px 20px 10px;
    font-weight: 500;
    .searchMsg {
      font-weight: 700;
      width: 200px;
    }
    .sign {
      display: inline-table;
      width: 3px;
      height: 13px;
      background: linear-gradient(360deg, #1d69c4 1%, #8bbdfc 99%);
      border-radius: 2px;
      margin-right: 8px;
    }
  }
  .searchMy ::v-deep  .el-form-item--small.el-form-item {
    margin-bottom: 10px;
    width: 97%;
  }
  form.el-form.fix-item-width {
    .el-row {
      ::v-deep   .el-col .el-form-item {
        .el-form-item__content {
          > .el-input {
            width: 100%;
          }

          > .el-date-editor {
            width: 100%;
          }
        }
      }
    }
  }
  .btn-group {
    display: flex;
    justify-content: flex-start;
  }
  ::v-deep  .pagination-container {
    margin: 15px 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
}
</style>
