<template>
  <div>
    <div class="Fsearch">
      <el-row type="flex" align="middle" justify="space-between" class="my-row">
        <el-row type="flex" align="middle">
          <span class="sign" />
          <div class="searchMsg">基本信息</div>
        </el-row>
      </el-row>
    </div>
    <el-form label-width="120px" size="small" class="busArea">
      <el-row :gutter="20">
        <el-col :span="22" style="line-height: 20px; margin-left: 20px; font-size: 14px">
          <div>商圈名称: {{ busAreaName }}</div>
        </el-col>
        <el-col :span="22" style="line-height: 50px; margin-left: 20px; font-size: 14px">
          <div>商圈描述: {{ busAreaDesc }}</div>
        </el-col>
      </el-row>
      <el-row class="freightList">
        <div class="Fsearch">
          <el-row type="flex" align="middle" justify="space-between">
            <el-row type="flex" align="middle">
              <span class="sign" />
              <div class="searchMsg">城市列表</div>
            </el-row>
          </el-row>
        </div>
      </el-row>
      <div class="check-container" :style="pageTreeStyle()">
        <el-tree
          :data="areaData"
          :props="treeProps"
          :render-content="renderContent"
          @node-expand="handleExpand"
        ></el-tree>
        <div slot-scope="{ node }" :class="'info'">
          {{ node.label }}
        </div>
      </div>
    </el-form>
  </div>
</template>

<script>
import { getQueryById } from '@/api/businessCircle'

export default {
  name: 'businessCircleDetail',
  data() {
    return {
      treeProps: {
        label: 'areaName',
        children: 'children'
      },
      dialogWidth: '95%',
      tableData: [],
      areaData: [],
      createTime: [], // 创建时间
      busAreaDesc: '', // 商圈描述
      busAreaName: '', // 商圈名称
      // 商圈类型下拉框
      businessType: [
        {
          value: '',
          label: '全部'
        },
        {
          value: '1',
          label: '药品'
        },
        {
          value: '2',
          label: '非药'
        }
      ]
    }
  },

  props: {
    dialogShow: Boolean,
    detailId: Number | String,
    isPage: {
      type: Boolean,
      default: true
    }
  },
  mounted() {
    this.changeCss()
    this.searchList()
  },
  methods: {
    pageTreeStyle() {
      if (this.isPage) {
        return {
          minWidth: '300px',
          maxWidth: '750px'
        }
      }
      return {}
    },
    // 查询容器编号
    searchList() {
      const parmas = {
        id: this.detailId
      }
      getQueryById(parmas).then((res) => {
        const { code, data } = res
        if (code === 0) {
          this.busAreaDesc = data.busAreaDesc // 商圈描述
          this.busAreaName = data.busAreaName // 商圈名称
          this.areaData = data.areas
        }
      })
    },
    resetFields() {
      this.formData.businessName = '' // 商圈名称
      this.formData.businessType = '' // 商圈类型
    },
    handleExpand() {
      // 节点被展开时触发的事件
      // 因为该函数执行在renderContent函数之前，所以得加this.$nextTick()
      this.$nextTick(() => {
        this.changeCss()
      })
    },
    renderContent(h, { node }) {
      // 树节点的内容区的渲染 Function
      let classname = ''
      // 由于项目中有三级菜单也有四级级菜单，就要在此做出判断
      if (node.level === 4) {
        classname = 'foo'
      }
      if (node.level === 3 && node.childNodes.length === 0) {
        classname = 'foo'
      }
      return h(
        'p',
        {
          class: classname
        },
        node.label
      )
    },
    changeCss() {
      const levelName = document.getElementsByClassName('foo') // levelname是上面的最底层节点的名字
      for (let i = 0; i < levelName.length; i++) {
        // cssFloat 兼容 ie6-8  styleFloat 兼容ie9及标准浏览器
        levelName[i].parentNode.style.cssFloat = 'left' // 最底层的节点，包括多选框和名字都让他左浮动
        levelName[i].parentNode.style.styleFloat = 'left'
        levelName[i].parentNode.onmouseover = function () {
          this.style.backgroundColor = '#fff'
        }
      }
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep  .el-form-item__label {
  margin-left: 20px;
  padding: 0;
}
::v-deep  .el-input__inner {
  border-radius: 0 4px 4px 0;
}
.search-title {
  display: table-cell;
  padding: 0 10px;
  text-align: center;
  border: 1px solid #dcdfe6;
  height: 30px;
  line-height: 30px;
  vertical-align: middle;
  border-right: none;
  border-radius: 4px 0 0 4px;
  color: #333333;
  white-space: nowrap;
}
.searchMy ::v-deep  .el-select {
  display: table-cell;
}
.searchMy ::v-deep  .el-form-item--small .el-form-item__content {
  line-height: 30px;
}
::v-deep  .el-input-group__prepend {
  background: none;
  color: #333333;
  padding: 0 10px;
}

.Fsearch {
  padding: 20px;
  font-weight: 500;
  .searchMsg {
    font-weight: 700;
    width: 200px;
  }
  .sign {
    display: inline-table;
    width: 3px;
    height: 13px;
    background: linear-gradient(360deg, #1d69c4 1%, #8bbdfc 99%);
    border-radius: 2px;
    margin-right: 8px;
  }
}
.freightList {
  padding: 0 20px;
  .diaSWidth {
    width: 280px;
    margin-left: 20px;
    margin-right: 10px;
  }
  .Fradios {
    margin-left: 40px;
  }
  .footerBtn {
    padding: 20px;
    padding-top: 10px;
    text-align: right;
    box-sizing: border-box;
    padding-bottom: 0;
  }
  .Fsearch {
    padding: 20px 0;
    font-weight: 700;
    .Fradios {
      margin-left: 40px;
    }
    .diaSWidth {
      width: 280px;
      margin-left: 20px;
      margin-right: 10px;
    }
    .footerBtn {
      padding: 20px;
      padding-top: 10px;
      text-align: right;
      box-sizing: border-box;
      padding-bottom: 0;
    }
  }
  .pag-text {
    vertical-align: middle;
    float: left;
    font-size: 12px;
    color: #999999;
    padding-top: 8px;
  }
}
::v-deep  .el-divider--horizontal {
  margin: 0px;
}
::v-deep  .el-form-item__error {
  margin-left: 40px;
}
.searchMy ::v-deep  .el-form-item--small.el-form-item {
  width: 24%;
}
.Fsearch ::v-deep  .el-dialog {
  width: 60%;
}
.span-tip {
  display: inline-block;
  width: 20px;
  height: 20px;
  font-size: 14px;
  border: 1px solid #4183d5;
  color: #4183d5;
  text-align: center;
  line-height: 20px;
  border-radius: 50%;
  margin-left: 5px;
}
// .xyyCityTree {
//   max-height: 300px;
//   overflow: auto;
// }
//  .el-form-item__content {
//   margin-left: -10px;
// }
.check-container {
  min-height: 150px;
  overflow-y: auto;
  border: 1px solid rgba(208, 208, 208, 1);
  margin-left: 2px;
  margin-top: 10px;
  padding: 10px;
  max-height: 300px;
}
</style>
