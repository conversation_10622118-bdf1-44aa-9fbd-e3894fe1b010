<!--created by <PERSON><PERSON><PERSON><PERSON><PERSON> in 2021/3/12-->
<template>
  <el-tree
    ref="cityTree"
    :default-checked-keys="value"
    :default-expanded-keys="defaultExpandedKeys"
    :lazy="lazy"
    :load="loadTree"
    :data="treeData"
    :props="props"
    :accordion="accordion"
    :node-key="nodeKey"
    :show-checkbox="showCheckbox"
    highlight-current
    @check="check"
  />
</template>

<script>
export default {
  name: 'XyyCityTree',
  props: {
    load: Function,
    lazy: <PERSON><PERSON><PERSON>,
    showCheckbox: <PERSON>olean,
    accordion: <PERSON>olean,
    defaultExpandedKeys: Array,
    // eslint-disable-next-line vue/require-prop-type-constructor,no-bitwise
    nodeKey: String | Number,
    value: Array,
    data: Array,
    props: Object,
    halfSelectedKeys: Array,
  },
  data() {
    return { attachParentCode: '$parentCode' };
  },
  computed: {
    treeData() {
      if (!this.data) {
        return [];
      }
      this.data.forEach((item) => {
        this.dataWrapper(item);
      });
      return this.data;
    },
  },
  watch: {
    halfSelectedKeys: {
      handler() {
        this.setHalfCheckedKeys();
      },
    },
  },
  methods: {
    dataWrapper(item) {
      const childPropName = this.props.children;
      const code = item[this.nodeKey];
      const children = item[childPropName];
      if (children && children.length > 0) {
        children.forEach((child) => {
          child[this.attachParentCode] = code;
          this.dataWrapper(child);
          this.setHalfCheckedKeys();
        });
      }
    },
    loadTree(node, resolve) {
      const resolveWrapper = (array) => {
        if (array && array.length > 0) {
          array.forEach((item) => {
            item[this.attachParentCode] = node.data[this.nodeKey];
          });
        }
        resolve(array);
        this.refreshCheckedStatus(node);
      };
      this.load(node, resolveWrapper);
    },
    refreshCheckedStatus() {
      this.$nextTick(() => {
        this.$refs.cityTree.setCheckedKeys(this.value);
        this.refreshHalfCheckedStatus();
      });
    },
    check(data, nodes) {
      const { checkedNodes } = nodes;
      // key 为选中节点，value 为是否要筛选出来 true false
      const selectCode = new Map();
      checkedNodes.forEach((item) => {
        if (!selectCode.has(item[this.nodeKey])) {
          selectCode.set(item[this.nodeKey], true);
          if (item[this.attachParentCode] && selectCode.has(item[this.attachParentCode])) {
            selectCode.set(item[this.nodeKey], false);
          }
        }
      });
      const selectArrayCode = [];
      selectCode.forEach((value, key) => {
        if (value) {
          selectArrayCode.push(key);
        }
      });
      this.$emit('input', selectArrayCode);
      const filterCheckNodes = [];
      checkedNodes.forEach((item) => {
        if (selectArrayCode.indexOf(item[this.nodeKey]) >= 0) {
          filterCheckNodes.push(item);
        }
      });
      this.$emit('check', filterCheckNodes);
    },
    // 设置节点被半选的状态
    setHalfCheckedKeys() {
      this.$nextTick(() => {
        this.refreshHalfCheckedStatus();
      });
    },
    refreshHalfCheckedStatus() {
      if (this.halfSelectedKeys && this.halfSelectedKeys.length > 0) {
        this.halfSelectedKeys.forEach((halfKey) => {
          const node = this.$refs.cityTree.getNode(halfKey);
          if (node) {
            node.indeterminate = true;
          }
        });
      }
    },
    getCheckedNodes(leafOnly, includeHalfChecked) {
      return this.$refs.cityTree.getCheckedNodes(leafOnly, includeHalfChecked);
    },

    getCheckedKeys(leafOnly) {
      return this.$refs.cityTree.getCheckedKeys(leafOnly);
    },
  },
};
</script>

<style scoped></style>
