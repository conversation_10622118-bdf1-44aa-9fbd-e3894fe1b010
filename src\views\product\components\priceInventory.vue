<template>
  <div class="boxDiv">
    <el-row>
      <el-form :model="priceInventoryVo" :rules="priceInventoryVoRules" ref="priceInventoryVo" label-width="130px" class="demo-ruleForm">
        <el-row>
          <el-col :span="12">
            <el-form-item label="单体采购价:" prop="fob">
              <el-input v-model="priceInventoryVo.fob" placeholder="请输入大于0的数字，限2位小数" :disabled="disabled" onkeyup="value=value.replace(/^\D*(\d*(?:\.\d{0,2})?).*$/g, '$1')" @blur="priceInventoryVo.fob=$event.target.value" @change="checkFob"></el-input>
            </el-form-item>
          </el-col>
          <el-checkbox class="checkBox-info" v-model="priceInventoryVo.priceSyncErp==1" @change="priceSyncErpChange" :disabled="disabled">自动同步价格（ERP系统对接后生效）</el-checkbox>
          <div class="price-label-wrapper" v-if="transPriceVo.netContent">
            <div class="price-label">
              <div class="label">单体单{{ transPriceVo.netContentUnit }}价</div>
              <div class="value">{{ getFloatPrice(priceInventoryVo.fob) }}</div>
            </div>
            <el-tooltip 
              class="tooltip-item" 
              effect="dark" 
              placement="top"
              v-if="transPriceVo.netContentUnit && /^(支|瓶)$/.test(transPriceVo.netContentUnit)">
              <i class="el-icon-warning-outline"></i>
              <template slot="content">
                <span style="display: block;">支装价为：盒装价格/每盒支装数量</span>
                <span>将用于药帮忙APP前端展示</span>
              </template>
            </el-tooltip>
          </div>
        </el-row>
        <el-row v-show="shopConfig.priceType===2">
          <el-col :span="12">
            <el-form-item label="单体毛利率:" prop="grossProfitMargin">
              <template v-if="shopConfig.priceType != 1" slot="label">
                <el-tooltip effect="dark" placement="top">
                  <template #content>若毛利为10个点，毛利率应填10.00，而不是0.1</template>
                  <i class="el-icon-warning-outline" />
                </el-tooltip>
                <span>单体毛利率:</span>
              </template>
              <el-input v-model.trim="priceInventoryVo.grossProfitMargin" placeholder="请输入大于0小于100的数字，限2位小数" :disabled="disabled" onkeyup="value=value.replace(/^\D*(\d{0,2}(?:\.\d{0,2})?).*$/g, '$1')" @blur="priceInventoryVo.grossProfitMargin=$event.target.value" @change="grossProfitMarginChange"></el-input>
              <span v-if="shopConfig.priceType == 1" style="font-size:12px">
                <span class="chainPrice-info">示例：若毛利为10个点，毛利率应填10.00，而不是0.1</span>
              </span>
            </el-form-item>
          </el-col>
        </el-row>
        <!-- disabled： ||shopConfig.priceType===2 -->
        <el-row>
          <el-col :span="12">
            <el-form-item label="连锁采购价:" prop="chainPrice">
              <template v-if="shopConfig.priceType != 1" slot="label">
                <el-tooltip effect="dark" placement="top">
                  <template #content>连锁总部、药品批发、批发（商业）”客户类型生效</template>
                  <i class="el-icon-warning-outline" />
                </el-tooltip>
                <span>连锁采购价:</span>
              </template>
              <el-input v-model="priceInventoryVo.chainPrice" placeholder="请输入大于等于0的数字，限2位小数" :disabled="disabled" onkeyup="value=value.replace(/^\D*(\d*(?:\.\d{0,2})?).*$/g, '$1')" @blur="priceInventoryVo.chainPrice=$event.target.value"></el-input>
              <!-- <span v-show="shopConfig.priceType===2" style="font-size:12px">
                <span class="chainPrice-info">示例：若毛利为10个点，毛利率应填10.00，而不是0.1</span>
              </span> -->
            </el-form-item>
          </el-col>
          <el-checkbox class="checkBox-info" :value="priceInventoryVo.chainSyncErp==1" @change="chainPriceSyncErpChange" :disabled="disabled">自动同步价格（ERP系统对接后生效）</el-checkbox>
          <div class="price-label" v-if="transPriceVo.netContent">
            <div class="label">连锁单{{ transPriceVo.netContentUnit }}价</div>
            <div class="value">{{ getFloatPrice(priceInventoryVo.chainPrice) }}</div>
          </div>
        </el-row>
        <!-- <el-row>
          <el-col :span="24">
            <el-form-item label>
              <span style="font-size:12px">
                温馨提示：
                <br />
                <span class="chainPrice-info">连锁采购价</span>目前只针对以下
                <span class="chainPrice-info">连锁客户类型</span>生效，连锁客户类型包含
                <br />
                <span class="chainPrice-info">"连锁总部、药品批发、批发（商业）"</span>
              </span>
            </el-form-item>
          </el-col>
        </el-row> -->
        <el-row v-show="shopConfig.priceType===2">
          <el-col :span="12">
            <el-form-item label="连锁毛利率:" prop="chainGrossProfitMargin">
              <template v-if="shopConfig.priceType != 1" slot="label">
                <el-tooltip effect="dark" placement="top">
                  <template #content>若毛利为10个点，毛利率应填10.00，而不是0.1</template>
                  <i class="el-icon-warning-outline" />
                </el-tooltip>
                <span>连锁毛利率:</span>
              </template>
              <el-input v-model.trim="priceInventoryVo.chainGrossProfitMargin" placeholder="请输入大于0小于100的数字，限2位小数" :disabled="disabled" onkeyup="value=value.replace(/^\D*(\d{0,2}(?:\.\d{0,2})?).*$/g, '$1')" @blur="priceInventoryVo.chainGrossProfitMargin=$event.target.value" @change="chainGrossProfitMarginChange"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="底价:" prop="basePrice">
              <el-input v-model.trim="priceInventoryVo.basePrice" placeholder="请输入大于0的数字，限2位小数" :disabled="disabled" onkeyup="value=value.replace(/^\D*(\d*(?:\.\d{0,2})?).*$/g, '$1')" @blur="priceInventoryVo.basePrice=$event.target.value" @change="basePriceChange"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="建议零售价:" prop="suggestPrice">
              <el-input v-model="priceInventoryVo.suggestPrice" :disabled="disabled" onkeyup="value=value.replace(/^\D*(\d*(?:\.\d{0,2})?).*$/g, '$1')" @blur="priceInventoryVo.suggestPrice=$event.target.value"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-if="isShowAreaPrice">
          <el-col :span="16" style="width: 950px;">
            <el-form-item label="分区域价:" prop="priceSkuPriceVos">
              <areaPrice v-model="priceInventoryVo.priceSkuPriceVos" :priceData="{ fob: priceInventoryVo.fob, chainPrice: priceInventoryVo.chainPrice}" :disabled="disabled"></areaPrice>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item prop="totalStock">
              <template slot="label">
                <el-tooltip
                  effect="dark"
                  placement="top"
                >
                  <template #content>总库存=可售库存+订单占用库存</template>
                  <i class="el-icon-warning-outline" />
                </el-tooltip>
                <span>总库存:</span>
              </template>
              <el-input v-model="priceInventoryVo.totalStock" placeholder="支持输入正整数" :disabled="disabled || allStoreDisabled" type="text" onkeyup="value=value.replace(/[^\d]/g,'')" @blur="priceInventoryVo.totalStock=$event.target.value"></el-input>
            </el-form-item>
          </el-col>
          <el-checkbox class="checkBox-info" v-model="priceInventoryVo.stockSyncErp==1" @change="stockSyncErpChange" :disabled="disabled || allStoreDisabled">自动同步库存（ERP系统对接后生效）</el-checkbox>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item>
              <template slot="label">
                <el-tooltip
                  effect="dark"
                  placement="top"
                >
                  <template #content>可售库存=ERP同步库存/手工设置商品库存-订单占用库存</template>
                  <i class="el-icon-warning-outline" />
                </el-tooltip>
                <span>可售库存:</span>
              </template>
              <el-input v-model="priceInventoryVo.availableQty" placeholder="支持输入正整数" :disabled="true" type="text"></el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="12">
            <el-form-item>
              <template slot="label">
                <el-tooltip
                  effect="dark"
                  placement="top"
                >
                  <template #content>订单占用库存=所有未下推ERP且状态为“待付款”或“待审核”或“出库中”订单的该商品占用库存数量</template>
                  <i class="el-icon-warning-outline" />
                </el-tooltip>
                <span>订单占用库存:</span>
              </template>
              <el-input v-model="priceInventoryVo.occupyStock" placeholder="支持输入正整数" :disabled="true" type="text"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="16">
            <el-form-item prop="oldestProDate">
              <template slot="label">
                <!-- <span v-if="!effectCanEmpty" style="color: red;">*</span> -->
                <span>最老生产日期:</span>
              </template>
              <el-input
                v-model="priceInventoryVo.oldestProDate"
                :disabled="disabled"
                placeholder="该商品所有可售库存中，距离当前时间最远的生产日期，例如：2022-05-23或2022-05"
              />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <span class="checkBox-info" style="margin-left: 60px;">最老生产日期：该商品所有可售库存中，距离当前时间最远的生产日期，例如：2022-05-23或2022-05</span>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="16">
            <el-form-item prop="newProDate">
              <template slot="label">
                <!-- <span v-if="!effectCanEmpty" style="color: red;">*</span> -->
                <span>最新生产日期:</span>
              </template>
              <el-input
                v-model="priceInventoryVo.newProDate"
                :disabled="disabled"
                placeholder="该商品所有可售库存中，距离当前时间最近的生产日期，例如：2022-06-23或2022-06"
              />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <span class="checkBox-info" style="margin-left: 60px;">该商品所有可售库存中，距离当前时间最近的生产日期，例如：2022-06-23或2022-06</span>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="16">
            <el-form-item prop="farEffect">
              <template slot="label">
                <!-- <span v-if="!effectCanEmpty" style="color: red;">*</span> -->
                <span>远效期至:</span>
              </template>
              <el-input v-model="priceInventoryVo.farEffect" :disabled="disabled" placeholder="该商品所有可售库存中，距离当前时间最远的批号对应的有效期至，例如：2023-06-23或2023-06"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <span class="checkBox-info" style="margin-left: 60px;">远效期至：该商品所有可售库存中，距离当前时间最远的批号对应的有效期至，例如：2023-06-23或2023-06</span>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="16">
            <el-form-item prop="nearEffect">
              <template slot="label">
                <!-- <span v-if="!effectCanEmpty" style="color: red;">*</span> -->
                <span>近效期至:</span>
              </template>
              <el-input v-model="priceInventoryVo.nearEffect" :disabled="disabled" placeholder="该商品所有可售库存中，距离当前时间最近的批号对应的有效期至，例如：2023-05-23或2023-05"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <span class="checkBox-info" style="margin-left: 60px;">近效期至：该商品所有可售库存中，距离当前时间最近的批号对应的有效期至，例如：2023-05-23或2023-05</span>
          </el-col>
        </el-row>

        <!-- <el-row>
          <el-col :span="12">
            <el-form-item label="生产日期:" :prop="effectCanEmpty ? 'noRequired' : 'opProDate'">
              <el-date-picker v-model.trim="priceInventoryVo.opProDate" popper-class="install-contr-cell-class" range-separator="至" size="small" format="yyyy-MM-dd" value-format="yyyy-MM-dd" placeholder="请选择商品的生产日期" type="daterange" start-placeholder="开始日期" end-placeholder="结束日期" prefix-icon="el-icon-date" style="width: 100%;" :disabled="disabled" />
            </el-form-item>
          </el-col>
          <span class="checkBox-info">该商品所有可售库存中，距离当前时间最远的和最近的批号对应的生产日期，例如：2021-01-01-2022-05-01</span>
        </el-row> -->
        <!-- <el-row>
          <el-col :span="12">
            <el-form-item :prop="effectCanEmpty ? 'noRequired' : 'opEffect'">
              <template slot="label">
                <el-tooltip v-if="!disabled" effect="dark" placement="top">
                  <template #content>需与库存及实物一致，离近效期1个月的商品不予上架。</template>
                  <i class="el-icon-warning-outline"></i>
                </el-tooltip>
                <span>有效期至:</span>
              </template>
              <el-date-picker v-model.trim="priceInventoryVo.opEffect" popper-class="install-contr-cell-class" range-separator="至" size="small" format="yyyy-MM-dd" value-format="yyyy-MM-dd" placeholder="请选择商品的有效期至" type="daterange" start-placeholder="开始日期" end-placeholder="结束日期" prefix-icon="el-icon-date" style="width: 100%;" :disabled="disabled" />
            </el-form-item>
          </el-col>
          <span class="checkBox-info">该商品所有可售库存中，距离当前时间最远的和最近的批号对应的有效期至，例如：2023-01-01-2024-05-01</span>
        </el-row> -->
      </el-form>
      <el-divider />
    </el-row>
  </div>
</template>

<script>
import { mapState } from 'vuex';
import {  merchantGroupGrey } from '@/api/product'
import areaPrice from './areaPrice.vue'
import { apiApplyListProduct } from '../../../api/product';
export default {
  name: 'PriceInventory',
  components: {
    areaPrice
  },
  props: {
    allStoreDisabled: {
      type: Boolean,
      default: false,
    },
    basicData: {
      type: Object,
      default() {
        return {};
      },
    },
    formModel: {
      type: Object,
      default() {
        return {};
      },
    },
  },
  data() {
    const priceSkuPriceVosValidator = (rule, value, callback) => {
      let msg = ''
      console.log(value);
      if (value && value.length > 0) {
        if (value.some(item => {
          if (!item.groupId) {
            msg = '请选择用户组';
            return true;
          }
          if (item.price == '' && item.priceControlType == 1) {
            msg = '请输入价格';
            return true
          }
          if (!item.priceControlType) {
            msg = '请选择控价方式';
            return true;
          } else if (item.priceControlType != 1 && (!item.ratio || !item.basePriceType)) {
            msg = '价格设置未填写完整'
            console.log(item);

            return true;
          }
          return false
        })) {
          console.log(666);

          callback(new Error(msg))
        } else {
          console.log(777);

          callback();
        }
      }
      callback();
    }
    return {
      effectCanEmpty: false,
      disabled: false,
      provinceList: [],
      businessEndTimeDisabled: false,
      propsData: {},
      priceInventoryVoRules: {
        noRequired: [
          { required: false },
        ],
        fob: [{ required: false, message: '请输入单体采购价', trigger: 'blur' }],
        totalStock: [
          { required: false, message: '总库存不能为空', trigger: 'blur' },
        ],
        basePrice: [
          { required: false, message: '底价不能为空', trigger: 'blur' },
        ],
        grossProfitMargin: [
          { required: false, message: '单体毛利率不能为空', trigger: 'blur' },
        ],
        suggestPrice: [
          {
            required: false,
            validator: this.checkPrice,
            trigger: 'blur',
          },
        ],
        oldestProDate:[{ required: false, message: '最老生产日期不能为空', trigger: 'blur' }],
        newProDate:[{ required: false, message: '最新生产日期不能为空', trigger: 'blur' }],
        nearEffect:[{ required: false, message: '近效期至不能为空', trigger: 'blur' }],
        farEffect:[{ required: false, message: '远效期至不能为空', trigger: 'blur' }],
        chainPrice:[{ required: false, message: '连锁采购价不能为空', trigger: 'blur' }],
        chainGrossProfitMargin:[{ required: false, message: '连锁毛利率不能为空', trigger: 'blur' }],
        priceSkuPriceVos:[{ required: false, validator: priceSkuPriceVosValidator, trigger: ''}]
      },
      priceInventoryVo: {
        fob: '', // 药帮忙价
        chainPrice: '', // 连锁价格
        suggestPrice: '', // 建议零售价
        totalStock: '', // 总库存
        occupyStock: '', // 订单占用库存
        availableQty: '', // 库存
        // opProDate: '', // 生产日期
        // opEffect: '', // 有效期至
        priceSyncErp: 0, // 自动同步价格 1:是；0-否
        chainSyncErp: 0, //自动同步连锁价格 1:是 2:否。
        stockSyncErp: 0, // 自动同步库存 1:是；0-否
        saleType: '', // 药品类型
        grossProfitMargin: '', // 单体毛利率
        chainGrossProfitMargin: '', // 连锁毛利率
        basePrice: '', // 底价
        newProDate: '', // 最新生产日期
        oldestProDate: '', // 最老生产日期
        nearEffect: '', // 近效期至
        farEffect: '', // 远效期至
        priceSkuPriceVos: [],   //分区域价
      },
      // 规格转换显示
      transPriceVo: {
        netContent: '', // 包装净含量
        netContentUnit: '', // 包装净含量单位
      },
      fobCopy: '',
      chainPriceCopy: '',
      highGrossCopy: '',
      suggestPriceCopy: '',
      isShowAreaPrice: false
    };
  },
  computed: { ...mapState('app', ['shopConfig']),
},
  watch: {
    basicData: {
      immediate: true,
      handler(newVale) {
        // this.$nextTick(() => {
        this.propsData = JSON.parse(JSON.stringify(newVale));
        this.initData();
        // });
      },
    },
    formModel: {
      handler(newVal) {
        var base = JSON.parse(JSON.stringify(newVal))
        this.disabled = base.isEdit == 1;
      },
      immediate: true,
      deep: true,
    },
  },
  mounted() {},
  created() {
    window.sessionStorage.setItem('priceInventoryVoRules', JSON.stringify(this.priceInventoryVoRules))
    merchantGroupGrey().then(res => {
      if(res.code === 0) {
        this.isShowAreaPrice = res.result
      }
    })
  },
  methods: {
    checkPrice(rule, value, callback) {
      let proportion = this.priceInventoryVo.suggestPrice - this.priceInventoryVo.fob;
      proportion /= this.priceInventoryVo.suggestPrice;
      if (this.highGrossCopy == 2 || this.highGrossCopy === 3) {
        if (value === '') {
          callback('此商品为高毛商品，必须填写建议零售价');
        } else if (proportion < 0.2 || proportion >= 0.76) {
          callback('高毛商品建议零售价需满足“20%≤(建议零售-药帮忙价)/建议零售价≤75%');
        } else {
          callback();
        }
      } else {
        callback();
      }
    },
    checkData() {
      const { oldestProDate, newProDate, nearEffect, farEffect} = this.priceInventoryVo
      const re = /^(?:(?!0000)[0-9]{4}-(?:(?:0[1-9]|1[0-2])-(?:0[1-9]|1[0-9]|2[0-8])|(?:0[13-9]|1[0-2])-(?:29|30)|(?:0[13578]|1[02])-31)|(?:[0-9]{2}(?:0[48]|[2468][048]|[13579][26])|(?:0[48]|[2468][048]|[13579][26])00)-02-29)$/;
      const re1 = /^(?:(?!0000)[0-9]{4}-(?:(?:0[1-9]|1[0-2])))$/;
      if (
        (oldestProDate && (!re1.test(oldestProDate) && !re.test(oldestProDate)))
        || (newProDate && (!re1.test(newProDate) && !re.test(newProDate)))
        || (nearEffect && (!re1.test(nearEffect) && !re.test(nearEffect)))
        || (farEffect && (!re1.test(farEffect) && !re.test(farEffect)))
      ) {
         return '最老生产日期/最近生产日期/近效期至/远效期至格式不正确，请修改后提交'
      }

      if(oldestProDate === '' && newProDate !== '' && ['100004','100010','100001','100007'].includes(this.basicData.erpFirstCategoryId) ){
        return '请填写最老生产日期'
      }
      if(nearEffect === '' && farEffect !== '' && ['100004','100010','100001','100007'].includes(this.basicData.erpFirstCategoryId) ){
        return '请填写近效期至'
      }
      // if(['100002','100005','100009'].includes(this.basicData.erpFirstCategoryId)){
      //    if ((oldestProDate && !newProDate) || (!oldestProDate && newProDate)) {
      //     return '最老生产日期和最近生产日期需同时存在，请修改后提交'
      //   }  if ((nearEffect && !farEffect) || (!nearEffect && farEffect)) {
      //     return '近效期至和远效期至需同时存在，请修改后提交'
      //   }
      // }
    },
    initData() {
      this.effectCanEmpty = this.propsData.effectCanEmpty;
      this.priceInventoryVo.fob = this.propsData.fob;
      this.priceInventoryVo.chainPrice = this.propsData.chainPrice;
      this.fobCopy = this.propsData.fob;
      this.chainPriceCopy = this.propsData.chainPrice;
      this.priceInventoryVo.suggestPrice = this.propsData.suggestPrice;
      this.suggestPriceCopy = this.propsData.suggestPrice;
      this.priceInventoryVo.availableQty = this.propsData.availableQty;
      this.priceInventoryVo.totalStock = this.propsData.totalStock;
      this.priceInventoryVo.occupyStock = this.propsData.occupyStock;
      this.priceInventoryVo.priceSyncErp = this.propsData.priceSyncErp;
      this.priceInventoryVo.chainSyncErp = this.propsData.chainSyncErp;
      this.priceInventoryVo.stockSyncErp = this.propsData.stockSyncErp;
      this.priceInventoryVo.saleType = this.propsData.saleType;
      this.priceInventoryVo.grossProfitMargin = this.propsData.grossProfitMargin;
      this.priceInventoryVo.chainGrossProfitMargin = this.propsData.chainGrossProfitMargin;
      this.priceInventoryVo.basePrice = this.propsData.basePrice;
      this.highGrossCopy = this.propsData.highGross;
      this.priceInventoryVo.newProDate = this.propsData.newProDate;
      this.priceInventoryVo.oldestProDate = this.propsData.oldestProDate;
      this.priceInventoryVo.nearEffect = this.propsData.nearEffect;
      this.priceInventoryVo.farEffect = this.propsData.farEffect;
      this.priceInventoryVo.priceSkuPriceVos = this.propsData.priceSkuPriceVos;
      this.transPriceVo.netContent = this.propsData.netContent;
      this.transPriceVo.netContentUnit = this.propsData.netContentUnit;
      this.setSelectChange();

      // if (this.propsData.newProDate && this.propsData.oldestProDate) {
      //   const { oldestProDate } = this.propsData;
      //   const { newProDate } = this.propsData;
      //   this.priceInventoryVo.opProDate = [oldestProDate, newProDate];
      // } else {
      //   this.priceInventoryVo.opProDate = [];
      // }

      // if (this.propsData.nearEffect && this.propsData.farEffect) {
      //   const { nearEffect } = this.propsData;
      //   const { farEffect } = this.propsData;
      //   this.priceInventoryVo.opEffect = [nearEffect, farEffect];
      // } else {
      //   this.priceInventoryVo.opEffect = [];
      // }
    },
    chainPriceSyncErpChange(type) {
      if (type) {
        this.priceInventoryVo.chainSyncErp = 1;
      } else {
        this.priceInventoryVo.chainSyncErp = 0;
      }
    },
    priceSyncErpChange(type) {
      if (type) {
        this.priceInventoryVo.priceSyncErp = 1;
      } else {
        this.priceInventoryVo.priceSyncErp = 0;
      }
    },
    stockSyncErpChange(type) {
      if (type) {
        this.priceInventoryVo.stockSyncErp = 1;
      } else {
        this.priceInventoryVo.stockSyncErp = 0;
      }
    },
    setSelectChange() {
      // if (this.priceInventoryVo.saleType === 1) {
      //   ;['opProDate', 'opEffect'].forEach((item) => {
      //     this.priceInventoryVoRules[item].forEach((itemRules) => {
      //       if (
      //         Object.prototype.hasOwnProperty.call(itemRules, 'required') &&
      //         itemRules['required'] === false
      //       ) {
      //         itemRules['required'] = true
      //       }
      //     })
      //   })
      // } else {
      //   ;['opProDate', 'opEffect'].forEach((item) => {
      //     this.priceInventoryVoRules[item].forEach((itemRules) => {
      //       if (
      //         Object.prototype.hasOwnProperty.call(itemRules, 'required') &&
      //         itemRules['required'] === true
      //       ) {
      //         itemRules['required'] = false
      //       }
      //     })
      //   })
      // }
      // if (this.shopConfig.priceType === 2) {
      //   ;['grossProfitMargin', 'basePrice'].forEach((item) => {
      //     this.priceInventoryVoRules[item].forEach((itemRules) => {
      //       if (
      //         Object.prototype.hasOwnProperty.call(itemRules, 'required') &&
      //         itemRules['required'] === false
      //       ) {
      //         itemRules['required'] = true
      //       }
      //     })
      //   })
      // } else {
      //   ;['grossProfitMargin', 'basePrice'].forEach((item) => {
      //     this.priceInventoryVoRules[item].forEach((itemRules) => {
      //       if (
      //         Object.prototype.hasOwnProperty.call(itemRules, 'required') &&
      //         itemRules['required'] === true
      //       ) {
      //         itemRules['required'] = false
      //       }
      //     })
      //   })
      // }
      if (this.highGrossCopy === 2 || this.highGrossCopy === 3) {
        ;['suggestPrice'].forEach((item) => {
          this.priceInventoryVoRules[item].forEach((itemRules) => {
            if (
              Object.prototype.hasOwnProperty.call(itemRules, 'required') &&
              itemRules['required'] === false
            ) {
              itemRules['required'] = true
            }
          })
        })
      } else {
        ;['suggestPrice'].forEach((item) => {
          this.priceInventoryVoRules[item].forEach((itemRules) => {
            if (
              Object.prototype.hasOwnProperty.call(itemRules, 'required') &&
              itemRules['required'] === true
            ) {
              itemRules['required'] = false
            }
          })
        })
      }
    },
    // 计算单体采购价，连锁采购价
    grossProfitMarginChange(value) {
      if (value) {
        this.priceInventoryVo.grossProfitMargin = value
        this.priceInventoryVo.fob = this.numFilter(
          this.priceInventoryVo.basePrice /
            (1 - this.priceInventoryVo.grossProfitMargin / 100)
        )
      } else {
        this.priceInventoryVo.fob = this.fobCopy
      }
    },
    chainGrossProfitMarginChange(value) {
      if (value) {
        this.priceInventoryVo.chainGrossProfitMargin = value
        this.priceInventoryVo.chainPrice = this.numFilter(
          this.priceInventoryVo.basePrice /
            (1 - this.priceInventoryVo.chainGrossProfitMargin / 100)
        )
      } else {
        this.priceInventoryVo.chainPrice = this.chainPriceCopy
      }
    },
    basePriceChange(value) {
      if (this.shopConfig.priceType === 2) {
        this.grossProfitMarginChange(this.priceInventoryVo.grossProfitMargin)
        this.chainGrossProfitMarginChange(
          this.priceInventoryVo.chainGrossProfitMargin
        )
        let proportion =
          this.priceInventoryVo.suggestPrice - this.priceInventoryVo.fob
        proportion = proportion / this.priceInventoryVo.suggestPrice
        if (this.highGrossCopy == 2 || this.highGrossCopy === 3) {
          if (proportion < 0.2 || proportion >= 0.76) {
            this.$message.error(
              '高毛商品建议零售价需满足“20%≤(建议零售-药帮忙价)/建议零售价≤75%,请修改建议零售价'
            )
          }
        }
      }
    },
    // 截取当前数据到小数点后两位
    numFilter(value) {
      const realVal = parseFloat(value).toFixed(2)
      return realVal
    },
    checkFob() {
      if (this.priceInventoryVo.fob) {
        let proportion =
          this.priceInventoryVo.suggestPrice - this.priceInventoryVo.fob
        proportion = proportion / this.priceInventoryVo.suggestPrice
        if (this.highGrossCopy == 2 || this.highGrossCopy === 3) {
          if (proportion < 0.2 || proportion >= 0.76) {
            this.$message.error(
              '高毛商品建议零售价需满足“20%≤(建议零售-药帮忙价)/建议零售价≤75%,请修改建议零售价'
            )
          }
        }
      }
    },
    // 计算函数
    getFloatPrice(price) {
      if(price <= 0) {
        return 0
      }
      return parseFloat(price / this.transPriceVo.netContent).toFixed(2)
    }
  }
}
</script>

<style scoped lang="scss">
.el-button {
  padding: 8px 20px;
}
.el-button.is-circle {
  padding: 7px;
  border: none;
}
.boxDiv {
  width: 100%;
  height: 100%;
  overflow-y: auto;
  padding: 0 0 0 10px;

  .chainPrice-info {
    color: #ff2121;
  }

  .span-tip {
    display: inline-block;
    width: 12px;
    height: 12px;
    font-size: 10px;
    border: 1px solid #999999;
    color: #999999;
    text-align: center;
    line-height: 12px;
    border-radius: 50%;
    margin-right: 2px;
    margin-left: -2px;
  }

  ::v-deep   .el-form {
    width: 100%;

    .el-select {
      margin-right: 14px;
    }

    .el-form-item__label {
      font-size: 12px;
      line-height: 30px;
    }

    .el-form-item__content {
      line-height: 30px;
    }

    .el-input__inner {
      line-height: 30px;
      height: 30px;
      font-size: 12px;
    }

    .el-input__icon {
      line-height: 30px;
    }
  }

  ::v-deep   .el-table__body .el-form-item {
    padding: 20px 0;
  }

  .my-label .el-form-item {
    display: inline-block;
    width: 442px;
  }

  .checkBox-info {
    margin-left: 10px;
    justify-content: center;
    align-items: center;
    line-height: 30px;
    font-size: 12px;
    font-family: PingFangSC, PingFangSC-Regular;
    font-weight: 400;
    text-align: left;
    color: #333333;
  }

  .avatar-uploader ::v-deep   .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
  }

  .avatar-uploader ::v-deep   .el-upload:hover {
    border-color: #409eff;
  }

  .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 64px;
    height: 64px;
    line-height: 64px;
    text-align: center;
  }

  .avatar {
    width: 64px;
    height: 64px;
    display: block;
  }
}

.boxDiv::-webkit-scrollbar {
  width: 0 !important;
}
.price-label-wrapper { 
  display: inline-flex;
  align-items: center;
  .tooltip-item {
    margin-left: 8px;
    color: #909399;
    cursor: pointer;
    font-size: 16px;
  }
}
.price-label {
  display: inline-flex;
  border: 1px solid #DCDFE6;
  border-radius: 4px;
  overflow: hidden;
  margin-left: 10px;
  .label {
    background-color: #F5F7FA;
    padding: 8px 12px;
    font-size: 14px;
    color: #606266;
    border-right: 1px solid #DCDFE6;
  }
  
  .value {
    padding: 8px 12px;
    font-size: 14px;
    color: #303133;
    background-color: #fff;
  }
}
</style>
