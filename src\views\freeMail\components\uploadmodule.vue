<template>
  <div class="row-line">
    <div class="imgItem-l10">
      <el-upload
        action
        :disabled="formType === 'detail'"
        :http-request="(file) => uploadImg(file, 'brand')"
        :before-upload="beforeAvatarUploadForProductImg"
        ref="myUploader"
        :limit="1"
        :class="{
          hide: formData.brandImage.length > 0 || formData.brandLoading
        }"
        :file-list="formData.brandImage"
        name="files"
        list-type="picture-card"
        accept=".jpg, .jpeg, .png, .JPG, .JPEG"
        class="avatar-uploader"
        :on-preview="handlePictureCardPreview"
        :on-remove="
          (file) => {
            return handleRemove(file, 'brand')
          }
        "
      >
        <div style="margin-top: 5px">
          <i class="el-icon-plus">
            <div class="avatar-uploader-icon">上传图片</div>
          </i>
        </div>
      </el-upload>
      <div class="tip-warning-text row-line">
        <span style="color: red" v-if="imagesPermision.includes(0)">*</span
        >包含品牌信息的商品包装正面
      </div>
    </div>
    <div class="imgItem-l10">
      <el-upload
        action
        :disabled="formType === 'detail'"
        :http-request="(file) => uploadImg(file, 'pzwh')"
        :before-upload="beforeAvatarUploadForProductImg"
        ref="myUploader"
        :limit="1"
        :class="{
          hide: formData.pzwhImage.length > 0 || formData.pzwhLoading
        }"
        :file-list="formData.pzwhImage"
        name="files"
        list-type="picture-card"
        accept=".jpg, .jpeg, .png, .JPG, .JPEG"
        class="avatar-uploader"
        :on-preview="handlePictureCardPreview"
        :on-remove="
          (file) => {
            return handleRemove(file, 'pzwh')
          }
        "
      >
        <div style="margin-top: 5px">
          <i class="el-icon-plus">
            <div class="avatar-uploader-icon">上传图片</div>
          </i>
        </div>
      </el-upload>
      <div class="tip-warning-text row-line">
        <span style="color: red" v-if="imagesPermision.includes(1)">*</span
        >批准文号及生产厂家面
      </div>
    </div>
    <div class="imgItem-l10">
      <el-upload
        action
        :disabled="formType === 'detail'"
        :http-request="(file) => uploadImg(file, 'spec')"
        :before-upload="beforeAvatarUploadForProductImg"
        ref="myUploader"
        :limit="1"
        :class="{
          hide: formData.specImage.length > 0 || formData.specLoading
        }"
        :file-list="formData.specImage"
        name="files"
        list-type="picture-card"
        accept=".jpg, .jpeg, .png, .JPG, .JPEG"
        class="avatar-uploader"
        :on-preview="handlePictureCardPreview"
        :on-remove="
          (file) => {
            return handleRemove(file, 'spec')
          }
        "
      >
        <div style="margin-top: 5px">
          <i class="el-icon-plus">
            <div class="avatar-uploader-icon">上传图片</div>
          </i>
        </div>
      </el-upload>
      <div class="tip-warning-text row-line">
        <span style="color: red" v-if="imagesPermision.includes(2)">*</span
        >包装规格面
      </div>
    </div>
    <div class="imgItem-l10">
      <el-upload
        action
        :disabled="formType === 'detail'"
        :http-request="(file) => uploadImg(file, 'changeFile')"
        :before-upload="beforeAvatarUploadForProductImg"
        ref="myUploader"
        :file-list="formData.changeFileImage"
        name="files"
        list-type="picture-card"
        accept=".jpg, .jpeg, .png, .JPG, .JPEG"
        class="avatar-uploader"
        :on-preview="handlePictureCardPreview"
        :on-remove="
          (file) => {
            return handleRemove(file, 'changeFile')
          }
        "
      >
        <div style="margin-top: 5px">
          <i class="el-icon-plus">
            <div class="avatar-uploader-icon">上传图片</div>
          </i>
        </div>
      </el-upload>
      <div class="tip-warning-text row-line">变更文件等</div>
    </div>
    <el-image-viewer
      v-if="showViewer"
      :url-list="srcArr"
      :on-close="closeViewer"
    />
    <el-dialog
      title="放大图片"
      width="800px"
      :visible.sync="visible"
      :modal-append-to-body="false"
      :close-on-click-modal="false"
    >
      <div class="img-class">
        <img :src="bigurl" alt="" style="width: 100%; height: 100%" />
      </div>
      <span slot="footer">
        <el-button
          size="medium"
          style="margin-left: 20px"
          @click="visible = false"
          >关闭</el-button
        >
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { getImgInfo } from '@/utils/util.js'
import ElImageViewer from 'element-ui/packages/image/src/image-viewer'
import { apiUploadProductImage, apiConfig } from '@/api/product'
export default {
  components: { ElImageViewer },
  props: {
    mainType: {
      type: String
    },
    imagesPermision: {
      type: Array,
      default: () => {
        return []
      }
    },
    formType: {
      type: String
    }
  },
  data() {
    return {
      visible: false,
      showViewer: false,
      srcArr: [],
      bigurl: '',
      formData: {
        brandImage: [],
        pzwhImage: [],
        specImage: [],
        changeFileImage: [],
        brandLoading: false,
        pzwhLoading: false,
        specLoading: false,
        changeFileLoading: false
      },
      formDataCopy: {
        brandImage: [],
        pzwhImage: [],
        specImage: [],
        changeFileImage: []
      },
      bigImgUrlPrefix: ''
    }
  },
  created() {
    apiConfig().then((res) => {
      if (res.data) {
        this.bigImgUrlPrefix = res.data.bigImgUrlPrefix // 商品大图地址前缀
      }
    })
  },
  methods: {
    closeViewer() {
      this.showViewer = false
    },

    //图片赋值
    setImages(data) {
      this.formData.brandImage = []
      this.formData.pzwhImage = []
      this.formData.specImage = []
      this.formData.changeFileImage = []

      this.formDataCopy = {
        brandImage: [],
        pzwhImage: [],
        specImage: [],
        changeFileImage: []
      }
      if (!data) {
        return
      }

      let dataArr = data.split(',')
      for (let index = 0; index < dataArr.length; index++) {
        const item = dataArr[index]
        if (item) {
          if (index === 0) {
            this.setImageItem(
              'brandImage',
              {
                name: '图片' + index,
                url: `${this.bigImgUrlPrefix}${item}`,
                uid: 1000 + index
              },
              item
            )
          } else if (index === 1) {
            this.setImageItem(
              'pzwhImage',
              {
                name: '图片' + index,
                url: `${this.bigImgUrlPrefix}${item}`,
                uid: 1000 + index
              },
              item
            )
          } else if (index === 2) {
            this.setImageItem(
              'specImage',
              {
                name: '图片' + index,
                url: `${this.bigImgUrlPrefix}${item}`,
                uid: 1000 + index
              },
              item
            )
          } else {
            this.setImageItem(
              'changeFileImage',
              {
                name: '图片' + index,
                url: `${this.bigImgUrlPrefix}${item}`,
                uid: 1000 + index
              },
              item
            )
          }
        }
      }

      this.$emit('initIMG', this.mainType, this.formDataCopy)
    },
    setImageItem(key, data, url) {
      let datacopy = _.cloneDeep(data)
      datacopy.url = url
      if (key !== 'changeFileImage') {
        this.formData[key] = [data]
        this.formDataCopy[key] = [datacopy]
      } else {
        this.formData[key].push(data)
        this.formDataCopy[key].push(datacopy)
      }
    },
    handleRemove(file, type) {
      if (type === 'brand') {
        this.formData.brandImage = []
        this.formDataCopy.brandImage = []
      } else if (type === 'pzwh') {
        this.formData.pzwhImage = []
        this.formDataCopy.pzwhImage = []
      } else if (type === 'spec') {
        this.formData.specImage = []
        this.formDataCopy.specImage = []
      } else if (type === 'changeFile') {
        this.formData.changeFileImage = this.formData.changeFileImage.filter(
          (item) => {
            return item.uid !== file.uid
          }
        )
        this.formDataCopy.changeFileImage =
          this.formDataCopy.changeFileImage.filter((item) => {
            return item.uid !== file.uid
          })
      }
      this.$emit('imgback', this.mainType, this.formDataCopy)
    },
    handlePictureCardPreview(file) {
      this.srcArr = [file.url]
      // this.visible = true
      this.showViewer = true
    },
    onloadImg(reader, file) {
      const image = new Image()
      image.src = reader.result
      return new Promise((resolve) => {
        image.onload = () => {
          const maxWidth = 1500 // 最大宽度
          const minWidth = 800 // 最小宽度
          const maxSize = 2 * 1024 * 1024 // 最大大小
          const { width } = image
          const { height } = image
          let compressedWidth = width
          let compressedHeight = height
          if (width > maxWidth) {
            compressedWidth = maxWidth
            compressedHeight = height * (maxWidth / width)
          } else if (width < minWidth) {
            compressedWidth = minWidth
            compressedHeight = height * (minWidth / width)
          }
          // 创建canvas对象
          const canvas = document.createElement('canvas')
          const ctx = canvas.getContext('2d')
          canvas.width = compressedWidth
          canvas.height = compressedHeight
          // 绘制图片到canvas
          ctx.drawImage(image, 0, 0, compressedWidth, compressedHeight)
          // 压缩图片大小
          canvas.toBlob((blob) => {
            const { size } = blob
            const ratio = maxSize / size
            let compressedBlob = blob
            if (ratio < 1) {
              compressedBlob = blob.slice(0, size * ratio)
            }
            const compressedFile = new File([compressedBlob], file.name, {
              type: file.type
            })
            resolve(compressedFile)
          }, file.type)
        }
      })
    },
    resetImg(file) {
      const reader = new FileReader()
      reader.readAsDataURL(file)
      return new Promise((resolve) => {
        reader.onload = async () => {
          const res = await this.onloadImg(reader, file)
          resolve(res)
        }
      })
    },
    async uploadImg(file, type) {
      const that = this
      let resetFile = await this.resetImg(file.file)
      const params = {
        file: resetFile,
        type: 1 //文件类型：1商品图片，2详情图片
      }
      this.showUploadWithType(type)
      const res = await apiUploadProductImage(params)
      this.hideUploadWithType(type)
      if (res.code === 0) {
        if (res.data) {
          file.onSuccess()
          this.changeUploadData(type, res, file)
        }
      } else {
        file.onError()
        this.$message.error(res.message)
      }
    },
    hideUploadWithType(type) {
      if (type === 'brand') {
        this.formData.brandLoading = false
      } else if (type === 'pzwh') {
        this.formData.pzwhLoading = false
      } else if (type === 'spec') {
        this.formData.specLoading = false
      } else if (type === 'changeFile') {
        this.formData.changeFileLoading = false
      }
    },
    showUploadWithType(type) {
      if (type === 'brand') {
        this.formData.brandLoading = true
      } else if (type === 'pzwh') {
        this.formData.pzwhLoading = true
      } else if (type === 'spec') {
        this.formData.specLoading = true
      } else if (type === 'changeFile') {
        this.formData.changeFileLoading = true
      }
    },
    changeUploadData(type, res, file) {
      if (type === 'brand') {
        this.formData.brandImage = [
          {
            name: res.data,
            url: `${this.bigImgUrlPrefix}${res.data}`,
            uid: file.file.uid
          }
        ]
        this.formDataCopy.brandImage = [
          {
            name: res.data,
            url: `${res.data}`,
            uid: file.file.uid
          }
        ]
      } else if (type === 'pzwh') {
        this.formData.pzwhImage = [
          {
            name: res.data,
            url: `${this.bigImgUrlPrefix}${res.data}`,
            uid: file.file.uid
          }
        ]
        this.formDataCopy.pzwhImage = [
          {
            name: res.data,
            url: `${res.data}`,
            uid: file.file.uid
          }
        ]
      } else if (type === 'spec') {
        this.formData.specImage = [
          {
            name: res.data,
            url: `${this.bigImgUrlPrefix}${res.data}`,
            uid: file.file.uid
          }
        ]
        this.formDataCopy.specImage = [
          {
            name: res.data,
            url: `${res.data}`,
            uid: file.file.uid
          }
        ]
      } else if (type === 'changeFile') {
        this.formData.changeFileImage.push({
          name: res.data,
          url: `${this.bigImgUrlPrefix}${res.data}`,
          uid: file.file.uid
        })
        this.formDataCopy.changeFileImage.push({
          name: res.data,
          url: `${res.data}`,
          uid: file.file.uid
        })
      }
      this.$emit('imgback', this.mainType, this.formDataCopy)
    },
    getImage(file) {
      return new Promise((resolve, reject) => {
        //上传文件为图片类型
        let img = new Image()
        img.onload = function () {
          resolve(img)
        }
        img.src = URL.createObjectURL(file)
      })
    },
    beforeAvatarUploadForProductImg(file) {
      const isJPG = file.type === 'image/jpeg' || file.type === 'image/png'
      const isLt2M = file.size / 1024 / 1024 < 2

      if (!isJPG) {
        this.$message.error('上传图片只能是 JPG、PNG 格式!')
        return false
      }
      if (!isLt2M) {
        this.$message.error('上传图片大小不能超过 2MB!')
        return false
      }

      return true;

      const that = this
      return new Promise((resolve, reject) => {
        let img = new Image()
        img.onload = function () {
          if (img.height !== img.width) {
            that.$message.error('上传图片请保持1:1比例')
            reject(false)
            return
          }
          if (img.height < 800 || img.height > 1500) {
            that.$message.error('图片宽高请保持在800-1500之间')
            reject(false)
            return
          }

          if (isJPG && isLt2M) {
            resolve(true)
          } else {
            reject(false)
          }
        }
        img.src = URL.createObjectURL(file)
      })
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep  .avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 2px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  background: rgba(0, 0, 0, 0.02);
  width: 64px;
  height: 64px;
  line-height: 64px;
}
::v-deep  .avatar-uploader .el-upload:hover {
  border-color: #409eff;
}
::v-deep  .avatar-uploader .el-upload__tip {
  margin-top: 0;
  color: #999999;
  font-size: 12px;
}
.avatar-uploader-icon {
  opacity: 1;
  font-size: 12px;
  font-family: PingFangSC, PingFangSC-Regular;
  font-weight: 400;
  text-align: left;
  color: rgba(0, 0, 0, 0.65);
  line-height: 14px;
}

::v-deep   .avatar-uploader .el-upload--picture-card {
  width: 64px;
  height: 64px;
}

::v-deep  .avatar-uploader .el-upload-list--picture-card .el-upload-list__item {
  width: 64px;
  height: 64px;
}

::v-deep   .hide .el-upload--picture-card {
  display: none;
}

.row-line {
  display: flex;
  flex-direction: row;
}

.imgItem-l10 {
  margin-left: 30px;
}

.tip-warning-text {
  margin-top: 20px;
  width: 64px;
  line-height: 1.5;
}

.img-class {
  width: 100%;
  height: 500px;
  overflow-y: scroll;
}
</style>