<template>
  <div class="afterSaleWrap">
    <!-- 顶部提示信息 -->
    <el-row v-if="refundCount != 0">
      <div class="refundCountBox">
        <span class="refundCount">*有{{ refundCount }}笔待处理的退款单，请及时处理。</span>
        <span
          class="seeCount"
          @click="jumpPage"
        >{{ isPendingOrders ? '点击查看': '取消查看' }}</span>
      </div>
    </el-row>
    <!-- 查询条件 -->
    <common-header title="售后管理">
      <template slot="header-right">
        <el-button size="small" type="primary" @click="toSerachForm()">查询</el-button>
        <el-button size="small" @click="resetForm">重置</el-button>
        <!-- <el-button size="small" type="primary" @click="exportAfterSaleList">导出</el-button> -->
      </template>

    <el-row :gutter="24" style="margin-right: 0;">
      <el-form
        ref="form"
        :model="ruleForm"
        size="small"
        class="searchMy"
      >
        <el-row :span="24">
          <el-col :span="6">
            <el-form-item
              prop="merchantName"
            >
              <el-input
                v-model="ruleForm.merchantName"
                placeholder="请输入"
                clearable
              >
                <template slot="prepend">
                  下单用户
                </template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item
              prop="orderNo"
            >
              <el-input
                v-model="ruleForm.orderNo"
                placeholder="请输入"
                clearable
              >
                <template slot="prepend">
                  订单编号
                </template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item
              prop="mobile"
            >
              <el-input
                v-model="ruleForm.mobile"
                placeholder="请输入"
                clearable
              >
                <template slot="prepend">
                  手机号码
                </template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item>
              <div class="flex">
              <span
                class="search-title"
              >退款状态</span>
              <el-select
                v-model="selectAuditState"
                placeholder="请选择"
                style="width: 100%;"
              >
                <el-option
                  label="请选择"
                  value
                />
                <el-option
                  label="待客户确认"
                  value="7"
                />
                <el-option
                  label="退款待审核"
                  value="0"
                />
                <el-option
                  label="审核通过，待退货入库"
                  value="3"
                />
                <el-option
                  label="入库完成，待财务退款"
                  value="4"
                />
                <el-option
                  label="入库完成，会计审核中"
                  
                  value="5"
                />
                <el-option
                  label="退款成功"
                  value="1"
                />
                <el-option
                  label="退款关闭"
                  value="-1"
                />
                <el-option
                  label="客户已拒绝"
                  value="8"
                />
                <el-option
                  label="已小额打款"
                  value="9"
                />
                 <el-option
                  label="财务经理审核中"
                  value="10"
                />
                <el-option
                  label="待资金确认"
                  value="11"
                />
                <el-option
                  label="付款中"
                  value="12"
                />
              </el-select>
            </div>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :span="24">
         <el-row>
          <el-col :span="12">
            <el-form-item>
              <span
                class="search-title"
              >申请日期</span>
              <div style="display: table-cell; width: 100%; line-height: 24px">
                <el-date-picker
                  v-model="time"
                  type="datetimerange"
                  format="yyyy-MM-dd HH:mm:ss"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  :default-time="['00:00:00', '23:59:59']"
                  style="width: 100%;"
                  :picker-options="pickerOptions"
                  popper-class="date-style"
                  @change="dateOnChange"
                />
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item
              prop="refundChannel"
            >
            <div class="flex">
              <span
                class="search-title"
              >退款发起方</span>
              <el-select
                v-model="ruleForm.refundChannel"
                size="small"
                placeholder="全部"
                style="width: 100%;"
              >
                <el-option
                  label="全部"
                  value
                />
                <el-option
                  label="用户发起"
                  value="1"
                />
                <el-option
                  label="商家发起"
                  value="4"
                />
                <el-option
                  label="平台发起"
                  value="2"
                />
                <el-option
                  label="系统发起"
                  value="5"
                />
              </el-select>
            </div>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item
              prop="provinceCodes"
            >
            <div class="flex">
              <span
                class="search-title"
              >省份</span>
              <el-select
                v-model="ruleForm.provinceCodes"
                size="small"
                placeholder="请选择"
                multiple
                clearable
                style="width: 100%;"
              >
<!--                <el-option-->
<!--                  label="全国"-->
<!--                  value-->
<!--                />-->
                <el-option
                  v-for="(item,index) in provinceList"
                  :key="index"
                  :label="item.branchName"
                  :value="item.branchCode"
                />
              </el-select>
            </div>
            </el-form-item>
          </el-col>
         </el-row>
         <el-row>
          <el-col :span="6">
            <el-form-item
              prop="refundReasonArr"
            >
            <div class="flex">
              <span
                class="search-title"
              >退款原因</span>
              <el-cascader
                ref="refundReasonArr"
                v-model="refundReasonArr"
                :options="AllRefundReasonList"
                :props="{ label: 'showText', value: 'showText', checkStrictly: true,multiple: true }"
                :show-all-levels="false"
                clearable
                collapse-tags
                @change="handleChangeExport"
              />
              <!-- <el-select
                v-model="ruleForm.refundReason"
                size="small"
                placeholder="请选择"
                style="width: 100%;"
                clearable
              >
                <el-option
                  v-for="(item,index) in AllRefundReasonList"
                  :key="index"
                  :label="item.showText"
                  :value="item.showText"
                />
              </el-select> -->
            </div>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item prop="barcode">
              <el-input v-model="ruleForm.barcode" placeholder="请输入" clearable>
                <template slot="prepend">商品编码</template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item prop="productErpCode">
              <el-input v-model="ruleForm.productErpCode" placeholder="请输入" clearable>
                <template slot="prepend">商品ERP编码</template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item prop="productName">
              <el-input v-model="ruleForm.productName" placeholder="请输入" clearable>
                <template slot="prepend">商品名称</template>
              </el-input>
            </el-form-item>
          </el-col>
         </el-row>
        <el-row>
          <el-col :span="6">
            <el-form-item prop="payType">
              <div class="flex">
              <span class="search-title">支付方式</span>
              <el-select v-model="ruleForm.payType" size="small" placeholder="全部">
                <el-option label="全部" value="" />
                <el-option label="在线支付" :value="1" />
                <el-option label="线下转账" :value="3" />
              </el-select>
            </div>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item prop="payChannel">
              <div class="flex">
              <span class="search-title">支付渠道</span>
              <el-select v-model="ruleForm.payChannel" size="small" placeholder="全部">
                <el-option label="全部" value="" />
                <el-option label="微信" :value="2" />
                <el-option label="支付宝" :value="1" />
                <el-option label="电汇平台" :value="7" />
                <el-option label="电汇商业" :value="8" />
                <el-option label="平安ePay" :value="10" />
                <el-option label="JD银行卡支付" :value="11" />
                <el-option label="京东采购融资" :value="12" />
                <el-option label="农行链e贷" :value="13" />
                <el-option label="小雨点白条" :value="14" />
                <el-option label="金蝶信用付" :value="15" />
              </el-select>
            </div>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item prop="productErpCode">
              <el-input v-model="ruleForm.skuId" placeholder="请输入" clearable>
                <template slot="prepend">sku编码</template>
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="6" style="display:flex">
            <el-form-item prop="payStatus">
        <span class="search-title">退款支付状态</span>
              <el-select v-model="ruleForm.payStatus" size="small" placeholder="全部">
                <el-option label="全部" value="" />
                <el-option label="退款中" :value="4" />
                <el-option label="退款成功" :value="2" />
                <el-option label="退款失败" :value="3" />
              </el-select>
            </el-form-item>
           
          </el-col>
          <el-col :span="6">       
            <el-form-item prop="payType">
            
                <span class="search-title">退款类型</span>
                <el-select
                  v-model="ruleForm.refundMode"
                  size="small"
                  placeholder="请选择"
                  clearable
                >
                  <el-option
                    v-for="(item) in refundModelList"
                    :key="item.id"
                    :label="item.title"
                    :value="item.id"
                  />
                </el-select>
         
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item prop="sellerRemarks">
              <el-input v-model="ruleForm.sellerRemarks" placeholder="请输入" clearable>
                <template slot="prepend">商家备注</template>
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
        </el-row>
      </el-form>
    </el-row>
    </common-header>
    <!-- 顶部导航 -->
    <el-row style="marginTop: 20px">
      <el-tabs
        v-model="activeAfterSaleStatus"
        @tab-click="handleClick"
      >
        <el-tab-pane
          v-for="item in tabData"
          :key="item.tabStatus"
          :name="item.tabStatus"
        >
          <span slot="label">
            {{ item.tabName }}
            <span
              class="countBox"
            >{{ countObj[item.tabStatus] > 999 ? '999+' : countObj[item.tabStatus] || 0 }}</span>
          </span>
        </el-tab-pane>
      </el-tabs>
      <tab-component
        ref="tabCom"
        :table-data="tableData"
        :active-after-sale-status="activeAfterSaleStatus"
        :loading="loading"
        :rule-form="ruleForm"
        @refreshList="refreshList"
        :isTrialMerchant="isTrialMerchant"
      />
    </el-row>
    <el-row>
      <div class="pagination-container">
        <div
          class="pag-text"
        >
          共 {{ listQuery.total }} 条数据，每页{{ listQuery.pageSize }}条，共{{ Math.ceil(listQuery.total / listQuery.pageSize) || 0 }}页
        </div>
        <el-pagination
          :page-sizes="[10, 20, 30, 50]"
          prev-text="上一页"
          next-text="下一页"
          layout="sizes, prev, pager, next, jumper"
          :total="listQuery.total"
          :current-page="listQuery.pageNum"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-row>
  </div>
</template>

<script>
import {
  getListAllRefundReason,
  getListProvinceList,
  getListAfterSale,
  afterSaleStatusCount, getRefundCount,
  getRefundModelList,
} from '@/api/afterSale/index';
import { getIsTrialMerchant } from '@/api/order/index';
import tabComponent from './components/tabComponent';
import commonHeader from "./components/common-header.vue"

export default {
  name: 'AfterSalesIndex',
  components: { tabComponent, commonHeader },
  data() {
    return {
      isTrialMerchant: false, // 是否为灰度商户
      activeAfterSaleStatus: '0',
      selectAuditState: '',
      refundCount: 0,
      loading: false,
      isPendingOrders: true,
      time: [],
      listQuery: {
        total: 0,
        pageNum: 1,
        pageSize: 10,
      },
      tabData: [{
        tabName: '待客户确认',
        tabStatus: '7',
      },
      {
        tabName: '待商家审核',
        tabStatus: '0',
      }, {
        tabName: '待商家收货',
        tabStatus: '3',
      }, {
        tabName: '待商家退款',
        tabStatus: '6',
      }, {
        tabName: '退款成功',
        tabStatus: '1',
      }, {
        tabName: '退款关闭',
        tabStatus: '-1',
      }, {
        tabName: '客户已拒绝',
        tabStatus: '8',
      }],
      refundReasonArr: [],// 给页面上的联级选择器使用
      ruleForm: {
        merchantName: '',
        orderNo: '',
        mobile: '',
        auditState: '6',
        startCreateTime: '',
        endCreateTime: '',
        refundChannel: '',
        provinceCodes: [],
        refundReason: '',
        auditStateListJson: '',
        barcode: '',
        productErpCode: '',
        productName: '',
        payType: '',
        payChannel: '',
        payStatus: '',
        refundMode: '',
        sellerRemarks: '', //商家备注
      },
      AllRefundReasonList: [],
      refundModelList: [], // 退款类型
      provinceList: [],
      tableData: [],
      countObj: {},
      pickerOptions: {
        shortcuts: [
          {
            text: '近6个月',
            onClick(picker) {
              let now = dayjs();
              let last6Months = now.subtract(6, 'month');
              // 证传入的日期数组的日期为Date类型
              now = new Date(now)
              last6Months = new Date(last6Months)
              picker.$emit('pick', [last6Months, now]);
            }
          },
           {
            text: '近1年',
            onClick(picker) {
              let now = dayjs();
              let last1Year = now.subtract(1, 'year');
              // 证传入的日期数组的日期为Date类型
              now = new Date(now)
              last1Year = new Date(last1Year)
              picker.$emit('pick', [last1Year, now]);
            }
          },
          {
            text: '一年前',
            onClick(picker) {
              let now = dayjs();
              let last1Year = now.subtract(1, 'year');
              let last3Year = now.subtract(2, 'year');
              last1Year = new Date(last1Year)
              last3Year = new Date(last3Year)
              picker.$emit('pick', [last3Year, last1Year]);
            }
          }
        ]
      }
    };
  },
  activated() {
    this.activate();
  },
  created() {
    this.initDate();
    this.getAllRefundReasonList();
    this.getProvince();
    this.refreshList(1);
    this.getRefundModelList();
    this.getIsTrialMerchant();
    // 清空页面缓存
    window.clearData['/afterSaleList'] = () => {
      this.resetForm('clearParams');
      this.selectAuditState = "7"
      this.initDate();
      this.refreshList();
    }
  },
  methods: {
    getRefundModelList() {
      getRefundModelList().then((res) => {
        // console.log(res);
        this.refundModelList = res.result || [];
      });
    }, // 获取退款类型
    getIsTrialMerchant() {
      getIsTrialMerchant().then((res) => {
        if (res.code == 0) {
          this.isTrialMerchant = res.result.isTrialMerchant;
        }
      });
    }, // 获取退款类型
    initDate() {
      const date = new Date();
      date.setMonth(date.getMonth() - 3);
      date.toLocaleDateString();
      const y = date.getFullYear();
      let m = date.getMonth() + 1;
      m = m < 10 ? (`0${m}`) : m;
      let d = date.getDate();
      d = d < 10 ? (`0${d}`) : d;
      const time = `${y}-${m}-${d}`;
      this.time = [`${time} 00:00:00`, `${new Date().getFullYear()}-${new Date().getMonth() + 1}-${new Date().getDate()} 23:59:59`];
      this.ruleForm.startCreateTime = new Date(this.time[0]).getTime();
      this.ruleForm.endCreateTime = new Date(this.time[1]).getTime();
    },
    activate() {
      console.log(this.countObj);
      const { query } = this.$route;
      if (query && Object.keys(query).length > 0) {
        this.resetForm('clearParams');
        Object.keys(query).map((key) => {
          // if (key === 'auditState') {
          //   if(query[key] === ''){
          //     this.activeAfterSaleStatus = '0';
          //     this.$set(this.ruleForm, 'auditState', '');
          //   }else {
          //     this.activeAfterSaleStatus = String(query[key]);
          //     this.$set(this.ruleForm, 'auditState', Number(query[key]));
          //   }
          // } else {
          //   this.$set(this.ruleForm, key, query[key]);
          // }
          if (key === 'auditState' && query[key] === '0') {
            this.activeAfterSaleStatus = '0';
            this.isPendingOrders = false;
          } else if (key === 'startCreateTime' || key === 'endCreateTime') {
            this.time = [this.formatDate(query.startCreateTime), this.formatDate(query.endCreateTime)];
          }
          this.$set(this.ruleForm, key, query[key]);
        });
        this.$nextTick(() => {
          // this.toSerachForm();
          this.listQuery = {
            total: 0,
            pageNum: 1,
            pageSize: 10,
          };
          this.getStatusCount(1);
        });
      }
    },
    jumpPage() {
      const hasCount = this.tabData.find((item, index) => {
        if (this.countObj[item.tabStatus] > 0) {
          return item.tabStatus;
        }
      });
      this.activeAfterSaleStatus = hasCount.tabStatus;
      if (this.isPendingOrders) {
        this.$set(this.ruleForm, 'auditStateListJson', '[0]');
        // this.$set(this.ruleForm, 'auditState', '');
      } else {
        this.$set(this.ruleForm, 'auditStateListJson', '');
        // this.$set(this.ruleForm, 'auditState', '0');
      }
      this.isPendingOrders = !this.isPendingOrders;
      this.refreshList(1);
    },
    getProvince() {
      getListProvinceList().then((res) => {
        this.provinceList = res.data || [];
      });
    },
    getAllRefundReasonList() {
      getListAllRefundReason().then((res) => {
        this.AllRefundReasonList = this.cleanEmptyChildren(res.data || []);
      });
    },
    cleanEmptyChildren(data) {
      return data.map((item) => {
        if (item.children && item.children.length > 0) {
          item.children = this.cleanEmptyChildren(item.children);
        }else {
          delete item.children;
        }
        if (item.children && item.children.length === 0) {
          delete item.children;
        }
        return item;
      }); 
    },
    handleClick(tab) {
      this.tableData = [{ ...this.tableData[0] }];
      this.ruleForm = {
        ...this.ruleForm,
        auditState: Number(tab.name),
      };
      this.listQuery = {
        total: 0,
        pageNum: 1,
        pageSize: 10,
      };
      if (tab.name) {
        this.ruleForm.auditState = tab.name;
        const same = this.tabData.some((item, index) => item.tabStatus == tab.name);
        if (same && tab.name == 6) {
          this.selectAuditState = '5';
        } else {
          this.selectAuditState = tab.name;
        }
      }
      this.getDataList();
    },
    handleCurrentChange(val) {
      this.listQuery.pageNum = val;
      this.getDataList();
    },
    handleSizeChange(size) {
      this.listQuery.pageSize = size;
      this.getDataList();
    },
    getStatusCount(from) {
      const params = { ...this.ruleForm };
      delete params.auditState;
      params.provinceCodes = params.provinceCodes.length > 0 ? JSON.stringify(params.provinceCodes) : null
      afterSaleStatusCount({ ...params }).then((res) => {
        if (res && res.data) {
          this.countObj = {};
          res.data.forEach((item) => {
            this.countObj[item.statusType] = item.count;
          });
          if (from === 1) {
            const hasCount = this.tabData.find((item, index) => {
              if (this.countObj[item.tabStatus] > 0) {
                return item.tabStatus;
              }
            });
            this.activeAfterSaleStatus = hasCount.tabStatus;
            this.ruleForm.auditState = this.activeAfterSaleStatus;
            const same = this.tabData.some((item, index) => item.tabStatus == this.activeAfterSaleStatus);
            if (same && this.activeAfterSaleStatus == 6) {
              this.selectAuditState = '5';
            } else {
              this.selectAuditState = this.activeAfterSaleStatus;
            }
            this.refreshList();
          }
        } else {
          this.countObj = {};
        }
      });
    },
    refreshList(from) {
      this.getDataList();
      this.getStatusCount(from);
    },
    dateOnChange(val) {
      if (val === null) {
        this.ruleForm.startCreateTime = '';
        this.ruleForm.endCreateTime = '';
        val = '';
        this.time = [];
      } else if (typeof val[0] === 'string') {
        this.ruleForm.startCreateTime = new Date(val[0]).getTime();
        this.ruleForm.endCreateTime = new Date(val[1]).getTime();
      }
    },
    toSerachForm() {
      if (this.selectAuditState) {
        console.log(this.selectAuditState);
        this.ruleForm.auditState = this.selectAuditState;
        let tempState = this.selectAuditState == '9' ? '1' : this.selectAuditState; // 退款状态为小额打款同样聚焦退款成功tab
        const same = this.tabData.some((item, index) => item.tabStatus == tempState);
        if (same) {
          this.activeAfterSaleStatus = tempState;
        } else {
          this.activeAfterSaleStatus = '6';
        }
      }

      this.listQuery = {
        total: 0,
        pageNum: 1,
        pageSize: 10,
      };
      this.refreshList();
    },
    resetForm(str) {
      this.listQuery = {
        total: 0,
        pageNum: 1,
        pageSize: 10,
      };
      this.selectAuditState = '';
      this.refundReasonArr = []
      this.ruleForm = {
        merchantName: '',
        orderNo: '',
        mobile: '',
        auditState: this.activeAfterSaleStatus,
        startCreateTime: '',
        endCreateTime: '',
        refundChannel: '',
        provinceCodes: [],
        refundReason: '',
        auditStateListJson: '',
        barcode: '',
        productErpCode: '',
        productName: '',
        payType: '',
        payChannel: '',
        payStatus: '',
      };
      if (str !== 'clearParams') {
        this.time = [];
        this.refreshList();
      }
    },
    getDataList() {
      // 查询待处理退款单数
      getRefundCount().then((res) => {
        this.refundCount = (res || {}).result || 0;
      });
      this.loading = true;
      const queryInfo = { ...this.ruleForm, ...this.listQuery };
      delete queryInfo.total;
      queryInfo.pageSize = queryInfo.pageSize ? queryInfo.pageSize : 10;
      queryInfo.provinceCodes = queryInfo.provinceCodes.length > 0 ? JSON.stringify(queryInfo.provinceCodes) : null
      getListAfterSale(queryInfo).then((res) => {
        if (res.code === 0) {
          const { total, pageNum, pageSize } = (res || {}).data || {};
          this.tableData = ((res || {}).data || {}).list || [];
          if (res.data && !res.data.list) {
            this.tableData = [];
          }
          this.listQuery = {
            ...this.listQuery,
            total,
            pageNum,
            pageSize,
          };
        } else {
          this.$message.error(res.msg);
        }
        this.loading = false;
      })
        .catch((error) => {
          console.log(error);
          this.loading = false;
        })
        .finally(() => {
          this.loading = false;
        });
    },
    handleChangeExport(value) {
      const selectedParents = new Set();
      const selectedChildren = new Set();

      // 1. 分别收集选中的一级分类和二级分类
      value.forEach(item => {
        if (item.length === 1) {
          selectedParents.add(item[0]);
        } else if (item.length === 2) {
          selectedChildren.add(item[1]);
          selectedParents.add(item[0]);
        }
      });

      const finalResults = [];

      value.forEach(item => {
        if (item.length === 1) {
          // 一级分类：检查是否有对应的二级分类被选中
          const hasChildSelected = value.some(val =>
            val.length === 2 && val[0] === item[0]
          );
          // 如果没有对应的二级分类被选中，则保留这个一级分类
          if (!hasChildSelected) {
            finalResults.push(item[0]);
          }
        } else if (item.length === 2) {
          // 二级分类：直接保留
          finalResults.push(item[1]);
        }
      });

      // 3. 去重并拼接字符串
      this.ruleForm.refundReason = [...new Set(finalResults)].join(',');
    }
  },
};
</script>

<style lang="scss" scoped>
.flex{
  display: flex;
}
::v-deep  .pagination-container{
  margin: 15px 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
::v-deep  .el-form-item__label {
  padding: 0;
}
.afterSaleWrap {
  padding: 20px 24px 5px;
  .afterSaleTitle {
    font-size: 20px;
    font-weight: 500;
  }
  .btnBox {
    float: right;
  }
  ::v-deep  .el-table__expand-column .el-icon {
    visibility: hidden;
  }
}
.countBox {
  background: #ff4d4f;
  border-radius: 11px;
  color: #fff;
  font-size: 12px;
  margin-left: 2px;
  padding: 0 8px;
}
.refundCountBox {
  margin-bottom: 15px;
  margin-left: 20px;
  .refundCount {
    color: #ff2121;
  }
  .seeCount {
    color: #4183d5;
    cursor: pointer;
  }
}
.Fsearch {
  padding: 0 0 10px;
  font-weight: 500;
  .searchMsg {
    font-weight: 700;
    width: 200px;
  }
  .sign {
    display: inline-table;
    width: 3px;
    height: 13px;
    background: linear-gradient(360deg, #1d69c4 1%, #8bbdfc 99%);
    border-radius: 2px;
    margin-right: 8px;
  }
}
.searchMy ::v-deep  .el-form-item__label {
  margin-left: 20px;
  padding: 0;
}
.searchMy ::v-deep  .el-input__inner {
  border-radius: 0 4px 4px 0;
}
.searchMy ::v-deep  .el-date-editor{
  width: 100%;
}
.search-title {
  display: table-cell;
  padding: 0 10px;
  text-align: center;
  border: 1px solid #dcdfe6;
  height: 30px;
  line-height: 30px;
  vertical-align: middle;
  border-right: none;
  border-radius: 4px 0 0 4px;
  color: #333333;
  white-space: nowrap;
}
.searchMy ::v-deep  .el-select {
  display: table-cell;
  width: 100%;
}
.searchMy ::v-deep  .el-form-item__content{
  width: 100%;
}
.searchMy ::v-deep  .el-form-item--small.el-form-item{
  margin-bottom: 10px;
  width: 32%;
}
.searchMy ::v-deep  .el-form-item--small .el-form-item__content{
  line-height: 30px;
  width: 100%;
}
.searchMy ::v-deep  .el-input-group__prepend {
  background: none;
  color: #333333;
  padding: 0 10px;
}
.Fsearch {
  //padding: 0px 20px 10px;
  font-weight: 500;
  .searchMsg {
    font-weight: 700;
    width: 200px;
  }
  .sign {
    display: inline-table;
    width: 3px;
    height: 13px;
    background: linear-gradient(360deg, #1d69c4 1%, #8bbdfc 99%);
    border-radius: 2px;
    margin-right: 8px;
  }
}
.searchMy ::v-deep  .el-form-item--small.el-form-item{
  margin-bottom: 10px;
  width: 97%;
}
</style>
<style lang="scss">
.date-style .el-picker-panel__body-wrapper {
  .el-picker-panel__body {
    margin: 0;
  }
  .el-picker-panel__sidebar {
    position: absolute;
    padding: 0;
    top: auto;
    bottom: 0.6vh;
    left: 1%;
    z-index: 100;
    display: flex;
    width: 250px;
    border: none;
    align-items: center;
    .el-picker-panel__shortcut {
      border: 1px solid #DCDFE6;
      margin: 0 3px;
      border-radius: 5px;
      font-size: 12px;
      height: 27px;
      line-height: 27px;
      overflow: hidden;
      text-align: center;
    }
    .el-picker-panel__shortcut:hover {
      border-color: #1d69c4;
    }
  }
}
</style>
