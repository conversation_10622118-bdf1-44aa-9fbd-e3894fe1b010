
import Vue from 'vue';
import VueRouter from 'vue-router';
import layout from '@/layout';

import basicSettingRouter from './modules/basicSettings';
import storeManage from './modules/storeManage';
import companyManage from './modules/companyManage';
import customerManage from './modules/customerManage';
import productManage from './modules/productManage';
import tradeManage from './modules/tradeManage';
import settlementManage from './modules/settlementManage';
import marketingManage from './modules/marketingManage';
import statisticsManage from './modules/statisticsManage';
import userManage from './modules/userManage';
import downloadListManage from './modules/downloadListManage';
import qualificationRouter from './modules/qualification';
import pharmacyManage from './modules/pharmacyManage';
import dataAnalysis from './modules/dataAnalysis';

Vue.use(VueRouter);

export const constantRoutes = [
  {
    path: '/',
    redirect: '/home',
  },
  {
    path: '/refresh',
    component: layout,
    children: [
      {
        path: '/refresh',
        component: () => import('@/layout/refresh'),
        name: 'refresh',
        meta: { noCache: true },
      },
    ],
  },
  {
    path: '/home',
    component: layout,
    children: [
      {
        path: '/home',
        component: () => import('@/views/home/<USER>'),
        name: 'home',
        meta: { noCache: true },
      },
      {
        path: '/noticeList',
        name: 'noticeList',
        component: () => import('@/views/home/<USER>/noticeListPage'),
        hidden: true,
      },
      {
        path: '/merchantcourselist',
        name: 'merchantcourselist',
        component: () => import('@/views/home/<USER>/merchantCourseList'),
        hidden: true,
      },
    ],
  },
  {
    path: '/specialPriceDetailPlus',
    name: 'specialPriceDetailPlus',
    component: () => import('@/views/special/specialPriceDetailPlus'),
    hidden: true,
  },
  {
    path: '/businessCircleDetail',
    name: 'businessCircleDetail',
    component: () => import('@/views/business-circle/business-circle-detail.vue'),
    props: route => ({ detailId: route.query.detailId }),
    hidden: true,
  },
  {
    path: '/h5Dashboard',
    name: 'h5Dashboard',
    component: () => import('@/views/h5Dashboard/index'),
    hidden: true,
  },
  {
    path: '/dataAnalysis',
    name: 'dataAnalysis',
    component: () => import('@/views/h5Dashboard/dataAnalysis'),
    hidden: true,
  },
  {
    path: '/noPermission',
    name: 'noPermission',
    component: () => import('@/views/h5Dashboard/noPermission'),
    hidden: true,
  },
  {
    path: '/h5ShopInfo',
    name: 'h5ShopInfo',
    component: () => import('@/views/h5ShopInfo/index'),
    hidden: true,
  },
  
];

export const asyncRoutes = [
  downloadListManage,
  qualificationRouter, // 资质管理
  productManage, // 商品管理
  marketingManage, // 营销管理
  tradeManage, // 交易管理
  statisticsManage, // 数据统计
  dataAnalysis,// 数据分析
  customerManage, // 客户管理
  pharmacyManage, // 药店管理
  basicSettingRouter,
  storeManage, // 店铺管理
  companyManage, // 企业管理
  settlementManage, // 结算管理
  userManage,
];

const createRouter = () => new VueRouter({ routes: constantRoutes });

const router = createRouter();

export function resetRouter() {
  const newRouter = createRouter();
  router.matcher = newRouter.matcher; // reset router
}

export default router;
