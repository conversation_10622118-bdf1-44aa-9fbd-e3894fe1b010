<template>
    <div class="nextDayLiveryInfo" v-loading="pageLoading">
        <div class="title" style="margin-bottom: 20px;"> 次日达服务介绍及开通指引</div>
        <div style="display: flex;">
            <div class="nextDat-content" style="width: 65%;position: relative;min-width: 500px;">

                <img src="./img/haibao.png" alt="" style="width: 100%;">
                <!-- <el-button type="primary" class="applyBtn" @click="dialogVisible = true"></el-button> -->
                <img src="./img/button.png" alt=""  class="applyBtn" @click="dialogVisible = true">
            </div>
            <div style="flex:1" class="Diagram">
                示意图：
                <img src="./img/zstp.png" alt="" style="width: 300px;">
            </div>
        </div>
        <!-- 查阅 -->
        <el-dialog title="次日达规则" :visible.sync="dialogVisible" width="620px" v-loading="pageLoading">
            <div style="line-height: 25px;">
                1.次日达地区：开通后商业根据自己可次日达地区配置开通，可支持省份/城市/区县/乡镇 维度精准配置。<br>

                2.次日达时间：可根据商业实际情况配置，周一至周五，0:00-16:00支付的订单支持次日达。或每天0:00-16:00支付订单支持次日达，00:00-17:00也可以。最晚截单时间为16:00。<br>
                3.配置好后，次日达订单率需≥90%，若上周次日达订单率＜90%，则取消次日达权益。待改进1周后如达标可再次申请开通。<br>

                4.次日达原则为预计达，如产生售后店铺主动解决，如未解决导致售后升级至平台介入处理，则按照订单金额*5%最低1元最高5元的规则赔付客户，如超时发货按照超时发货规则处理。<br>
            </div>
            <span slot="footer" class="dialog-footer">
                <el-button type="primary" @click="submitApply">默认同意以上内容并申请开通</el-button>
                <el-button @click="dialogVisible = false">暂不开通</el-button>
            </span>
        </el-dialog>
    </div>
</template>

<script>
import { nextdayApplyOpen } from '@/api/storeManagement';
export default {
    data() {
        return {
            pageLoading: false,
            dialogVisible: false,
        }
    },
    methods: {
        submitApply() {
            this.pageLoading = true;
            nextdayApplyOpen().then((res) => {
                if (res.code == 0) {
                    this.$message.success('操作成功')
                    this.dialogVisible = false
                } else {
                    this.$message.error(res.msg)
                }
            }).finally(() => {
                this.pageLoading = false;
            });
        }
    }
}
</script>

<style scoped lang="scss">
.nextDayLiveryInfo {
    ::v-deep .el-dialog__body {
        padding: 10px 20px;
    }

    .title {
        font-size: 18px;
    }

    // display: flex;
    padding: 20px 15px;

    p {
        line-height: 20px;
    }

    .Diagram {
        padding-left: 20px;
        display: flex;
        align-items: flex-start;
    }

    .applyBtn {
        position: absolute;
        bottom: 2%;

        left: 50%;
        transform: translateX(-50%);
        height: 5.2%;
        // opacity: 0;
        cursor: pointer;
        width: 22%
    }
}
</style>