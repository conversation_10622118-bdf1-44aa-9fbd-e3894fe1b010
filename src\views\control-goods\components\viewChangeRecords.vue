<template>
  <div>
    <el-dialog
      :visible="dialogVisibl"
      :before-close="handleDialogClose"
      :width="dialogWidth"
      title="操作日志"
      height="300"
      @open="open"
    >
      <el-table
        v-loading="isLoading"
        :data="tableData"
        stripe
        border

        highlight-current-row
        :header-cell-style="{ background: '#F9F9F9', color: '#666666' }"
      >
        <el-table-column
            prop="createTime"
            label="变更时间"

          />
        <el-table-column label="变更内容">
          <template slot-scope="{row}">
            <p v-if="row.remark">
              {{ row.remark }}
            </p>
            <div
              v-else
              v-for="(item, index) in row.contentMap"
              :key="index"
              style="margin-bottom: 5px;"
            >
              <span style="margin-right: 5px;">{{ index }}</span>
              <span>变更前:{{ item.before }} </span>
              <div>变更后：{{ item.after }}</div>

          </div>
          </template>
        </el-table-column>
        <el-table-column
          prop="operateType"
          label="操作项"
        />
        <el-table-column
          prop="operator"
          label="操作人"
        />
      </el-table>

      <!-- <div
        v-if="tablePage.total != 0"
        class="pagination-container"
        style="display: flex;align-items: center;justify-content: space-between;"
      >
        <div class="pag-text">
          共 {{ tablePage.total }} 条数据，每页{{ tablePage.pageSize }}条，共{{
            Math.ceil(tablePage.total / tablePage.pageSize)
          }}页
        </div>
        <el-pagination
          background
          :page-sizes="pageSizes"
          :page-size="tablePage.pageSize"
          :current-page="tablePage.pageNum"
          layout="sizes, prev, pager, next, jumper"
          :total="tablePage.total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div> -->
      <el-row :gutter="20">
        <el-col
          :span="2"
          :offset="22"
          style="float: right"
        >
          <el-button
            type="primary"
            style="margin-top: 10px"
            size="small"
            @click="sureBtn"
          >
            确 定
          </el-button>
        </el-col>
      </el-row>
    </el-dialog>
  </div>
</template>

<script>
import { getAreaLog } from '@/api/businessCircle';
import { getChangeList } from '@/api/product';
import { data } from 'jquery';

export default {
  name: 'ViewChangeRecords',
  model: {
    prop: 'viewProductDialog',
    event: 'onDialogViewProduct',
  },
  props: {
    viewProductDialog: Boolean,
    row: Object,
  },
  data() {
    return {
      show: false,
      dialogWidth: '60%',
      dialogVisibl: false,
      tableData: [], // 弹窗表格
      rowIndex: '',
      // pageSizes: [10, 20, 30, 40],
      // tablePage: {
      //   pageNum: 1,
      //   pageSize: 10,
      //   total: 1,
      // },
      groupId:'',
      busAreaId: '',
      isLoading: false, // 加载
      ruleForm: {
        tempName: '',
        templateType: '',
        tempStatus: '',
        branchCode: null,
        startTime: '',
      },
    };
  },
  mounted() {
    this.$nextTick(() => {
      this.dialogVisibl = true;
    });
  },
  methods: {
    open() {
      this.dialogVisibl = true;
      // this.groupId = groupId
      // this.busAreaId = busAreaId; // 业务商圈ID
      this.searchList(); // 初始化列表
    },
    handleDialogClose() {
      this.dialogVisibl = false;
      this.$emit('onDialogViewProduct', false);
    },
    // 查询
    // searchList1() {
    //   const params = { busAreaId: this.row.id };
    //   Object.assign(params, { pageNum: this.tablePage.pageNum, pageSize: this.tablePage.pageSize });
    //   this.isLoading = true;
    //   getAreaLog(params).then((res) => {
    //     const { code, data } = res;
    //     if (code === 0) {
    //       this.isLoading = false;
    //       this.tableData = data.list;
    //       this.tablePage.total = data.total; // 总数据数量
    //       this.tablePage.pageNum = data.pageNum;
    //     }
    //   });
    // },
    searchList() {
      const params = {groupId: this.row.id}
      this.isLoading = true
      getChangeList(params).then((res) => {
        this.isLoading = false
        const {result} = res
        this.tableData = result
        // this.tablePage.total = result.length
        // this.tablePage.pageNum = 1

      })

    },
    // handleSizeChange(val) {
    //   this.tablePage.pageSize = val;
    //   this.searchList();
    // },
    // handleCurrentChange(val) {
    //   this.tablePage.pageNum = val;
    //   this.searchList();
    // },
    sureBtn() {
      this.handleDialogClose();
    },
  },
};
</script>

<style scoped>
::v-deep   .el-dialog__header{
  background-color: #f5f5f580;;
}
</style>
