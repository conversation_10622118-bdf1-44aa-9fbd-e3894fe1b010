<template>
  <div>
  <el-dialog
    :title="modifyConfig.title"
    :visible="dialogVisible"
    width="950px"
    :before-close="handleClose"
  >
    <div
      v-if="modifyConfig.modifyType==='suggestPrice'" 
      style="margin: 0 0 10px 20px;font-weight: 500;">
      <span>商品编码：</span>
      {{modifyConfig.barcode}}
    </div>
    <el-form ref="ruleForm" :model="formModel" :rules="rules" label-width="130px">
      <el-form-item
        v-if="modifyConfig.modifyType==='suggestPrice'"
        label="单体采购价"
        prop="suggestPrice"
      >
        <el-input
          v-model="formModel.suggestPrice"
          @input="checkPrice"
          style="width: 60%"
          placeholder="请输入大于0的数字，限2位小数"
          :disabled="modifyConfig.priceType===2"
          @blur="inputBlur(formModel.suggestPrice,'suggestPrice')"
        />
        <el-checkbox class="checkBox-info" style="margin-left: 10px" v-model="formModel.priceSyncErp == 1" @change="priceSyncErpChange">自动同步价格（ERP系统对接后生效）</el-checkbox>
      </el-form-item>
      <el-form-item
        v-if="modifyConfig.modifyType==='suggestPrice'&&modifyConfig.priceType===2"
        prop="grossProfitMargin"
      >
       <template slot="label">
          <el-tooltip 
            class="item" 
            effect="dark" 
            content="若毛利为10个点，毛利率应填10.00，而不是0.1" 
            placement="top-start">
            <i class="el-icon-warning-outline" />
          </el-tooltip>
          <span style="margin-left: 5px">单体毛利率:</span>
        </template>
        <el-input
          v-model.trim="formModel.grossProfitMargin"
          style="width: 60%"
          placeholder="请输入大于0小于100的数字，限2位小数"
          onkeyup="value=value.replace(/^\D*(\d{0,2}(?:\.\d{0,2})?).*$/g, '$1')"
          @blur="formModel.grossProfitMargin=$event.target.value"
          @change="grossProfitMarginChange"
        ></el-input>
        <!-- <div style="font-size:12px;color:#ff2121">
          <span>示例：若毛利为10个点，毛利率应填10.00，而不是0.1</span>
        </div> -->
      </el-form-item>
      <el-form-item v-if="modifyConfig.modifyType==='suggestPrice'" prop="chainPrice">
        <template slot="label">
          <el-tooltip 
            class="item" 
            effect="dark" 
            content="连锁采购价目前只针对“连锁总部、药品批发、批发（商业）”客户类型生效" 
            placement="top-start">
            <i class="el-icon-warning-outline" />
          </el-tooltip>
          <span style="margin-left: 5px">连锁采购价</span>
        </template>
        <el-input
          v-model="formModel.chainPrice"
          @input="checkPrices"
          style="width: 60%"
          placeholder="请输入大于等于0的数字，限2位小数"
          :disabled="modifyConfig.priceType===2"
          @blur="inputBlur(formModel.chainPrice,'chainPrice')"
        />
        <el-checkbox class="checkBox-info" style="margin-left: 10px" v-model="formModel.chainPriceSyncErp == 1" @change="chainPriceSyncErpChange">自动同步价格（ERP系统对接后生效）</el-checkbox>
        <!-- <div
          style="font-size:12px;line-height:30px"
          v-if="modifyConfig.modifyType==='suggestPrice'"
        >
          <span>温馨提示：</span>
          <br />
          <span class="colorRed">连锁采购价</span>目前只针对以下
          <span class="colorRed">连锁客户类型</span>生效，连锁客户类型包含
          <span class="colorRed">"连锁总部、药品批发、批发（商业）"</span>
        </div> -->
      </el-form-item>
      <el-form-item
        v-if="modifyConfig.modifyType==='suggestPrice'&&modifyConfig.priceType===2"
        prop="chainGrossProfitMargin"
      >
        <template slot="label">
          <el-tooltip 
            class="item" 
            effect="dark" 
            content="若毛利为10个点，毛利率应填10.00，而不是0.1" 
            placement="top-start">
            <i class="el-icon-warning-outline" />
          </el-tooltip>
          <span style="margin-left: 5px">连锁毛利率:</span>
        </template>
        <el-input
          v-model.trim="formModel.chainGrossProfitMargin"
          style="width: 60%"
          placeholder="请输入大于0小于100的数字，限2位小数"
          onkeyup="value=value.replace(/^\D*(\d{0,2}(?:\.\d{0,2})?).*$/g, '$1')"
          @blur="formModel.chainGrossProfitMargin=$event.target.value"
          @change="chainGrossProfitMarginChange"
        ></el-input>
        <!-- <div style="font-size:12px;color:#ff2121">
          <span>示例：若毛利为10个点，毛利率应填10.00，而不是0.1</span>
        </div> -->
      </el-form-item>
      <el-form-item
        v-if="modifyConfig.modifyType==='suggestPrice'"
        label="底价:"
        :prop="modifyConfig.priceType === 2 ? 'basePrice' : 'noRequired'"
      >
        <el-input
          v-model.trim="formModel.basePrice"
          style="width: 60%"
          placeholder="请输入大于0的数字，限2位小数"
          onkeyup="value=value.replace(/^\D*(\d*(?:\.\d{0,2})?).*$/g, '$1')"
          @blur="formModel.basePrice=$event.target.value"
          @change="basePriceChange"
        ></el-input>
      </el-form-item>
      <el-form-item
        v-if="modifyConfig.modifyType==='suggestPrice' && isShowAreaPrice"
        label="分区域价:"
        prop="priceSkuPriceVos">
        <areaPrice v-model="formModel.priceSkuPriceVos" :priceData="priceData" :disabled="false"></areaPrice>
      </el-form-item>
      <el-form-item
        v-if="modifyConfig.modifyType==='wholease'"
        label="起购数量"
        prop="wholeaseStartCount"
      >
        <el-input
          v-model="formModel.wholeaseStartCount"
          onkeyup="value=value.replace(/[^\d]/g, '')"
          style="width: 60%"
          placeholder="请输入大于0的正整数"
          :disabled="modifyConfig.priceType===2"
          @blur="inputBlur(formModel.wholeaseStartCount,'wholeaseStartCount')"
        />
      </el-form-item>
      <el-form-item
        v-if="modifyConfig.modifyType==='wholease'"
        label="活动价"
        prop="wholeaseActPrice"
      >
        <el-input
          v-model="formModel.wholeaseActPrice"
          style="width: 60%"
          @input="limitInput($event, 'wholeaseActPrice')"
          placeholder="请输入大于0的数字，限2位小数"
          :disabled="modifyConfig.priceType===2"
          @blur="inputBlur(formModel.wholeaseActPrice,'wholeaseActPrice')"
        />
      </el-form-item>
      <el-form-item
        v-if="modifyConfig.modifyType==='wholease'"
        label="原价"
        prop="wholeaseOriPrice"
      >
        <el-input
          v-model="formModel.wholeaseOriPrice"
          style="width: 60%"
          placeholder="请输入大于0的数字，限2位小数"
          :disabled="modifyConfig.priceType===2"
          @input="limitInput($event, 'wholeaseOriPrice')"
          @blur="inputBlur(formModel.wholeaseOriPrice,'wholeaseOriPrice')"
        />
      </el-form-item>
      <el-form-item
        v-if="modifyConfig.modifyType==='totalStock'"
        label="设置总库存"
        prop="totalStock"
      >
        <el-input
          v-model="formModel.totalStock"
          @input="checkAvailableQty"
          style="width: 60%"
          @blur="inputBlur(formModel.totalStock,'totalStock')"
        />
      </el-form-item>
      <el-form-item
        v-if="modifyConfig.modifyType==='wholeaseStock'"
        label="活动总限购库存"
        prop="wholeaseAllStock"
      >
        <el-radio-group v-model.trim="formModel.wholeaseAllStock">
          <el-radio :label="0">不限购</el-radio>
          <el-radio :label="1">限购</el-radio>
        </el-radio-group>
      </el-form-item>
      <!-- wholeaseAllStock: false, //活动总限购库存
        wholeaseAllCount: 0, // 活动总限购数量
        wholeaseSingleStock: false, //单点是否限购
        wholeaseSingleType: 1, // 单点限购类型
        wholeaseSingleCount: 0, //单店限购数量 -->
      <el-form-item
        v-if="modifyConfig.modifyType==='wholeaseStock' && formModel.wholeaseAllStock==1"
        label="总限购数量"
        prop="wholeaseAllCount"
      >
        <el-input
          v-model="formModel.wholeaseAllCount"
          onkeyup="value=value.replace(/[^\d]/g, '')"
          style="width: 60%"
          @blur="inputBlur(formModel.wholeaseAllCount,'wholeaseAllCount')"
        />
      </el-form-item>
      <el-form-item
        v-if="modifyConfig.modifyType==='wholeaseStock'"
        label="单店是否限购"
        prop="wholeaseAllStock"
      >
        <el-radio-group v-model.trim="formModel.wholeaseSingleStock">
          <el-radio :label="0">不限购</el-radio>
          <el-radio :label="1">限购</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item
        v-if="modifyConfig.modifyType==='wholeaseStock' && formModel.wholeaseSingleStock===1"
        label="单店限购类型"
        prop="wholeaseSingleType"
      >
        <el-select style="width: 60%" v-model="formModel.wholeaseSingleType">
          <el-option v-for="item in personalLimitTypeList" :label="item.value" :value="item.code">{{ item.value }}</el-option>
        </el-select>
      </el-form-item>
      <el-form-item
        v-if="modifyConfig.modifyType==='wholeaseStock' && formModel.wholeaseSingleStock===1"
        label="单店限购数量"
        prop="wholeaseSingleCount"
      >
        <el-input
          v-model="formModel.wholeaseSingleCount"
          style="width: 60%"
          @blur="inputBlur(formModel.wholeaseSingleCount,'wholeaseSingleCount')"
          onkeyup="value=value.replace(/[^\d]/g, '')"
        />
      </el-form-item>
      <div v-if="modifyConfig.modifyType==='shelves'">
        <div style="margin-bottom:10px">
          <i class="el-icon-warning"></i>
          <span style="margin-left:4px">您确定要下架此商品吗？</span>
        </div>
        <el-input
          v-model="formModel.shelves"
          type="textarea"
          placeholder="请输入备注"
          style="width: 100%"
          :rows="3"
          maxlength="50"
          show-word-limit
        />
        <!-- @blur="inputBlur(formModel.shelves,'shelves')" -->
      </div>
    </el-form>
    <div class="wholease-tips" v-if="modifyConfig.modifyType === 'wholease'">
      总价：¥{{ sumPrice.toFixed(2) }}（起购数量*活动价格）
    </div>
    <div class="wholease-tips" v-if="modifyConfig.modifyType === 'wholeaseStock'">
      提示：修改限购类型后从修改时间节点开始重新计算数量
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button @click="handleClose" size="small">取 消</el-button>
      <el-button size="small" type="primary" :loading="loading" @click="modifyConfirm">确 定</el-button>
    </span>

  </el-dialog>
  <el-dialog
    title="提示"
    :visible="deleteDialogVisible"
    width="30%"
    top="15%"
    @close="deleteDialogVisible = false"
  >
    <p>确认删除当前商品的所有区域价格？</p>
    <div style="text-align: right">
      <el-button @click="deleteDialogVisible = false">取消</el-button>
      <el-button type="primary" @click="deleteAllAreaPrice">确定</el-button>
    </div>
  </el-dialog>
  </div>
  
</template>

<script>
import { updateStock, updateAmount ,batchDownNew, updateParams} from '@/api/product';
import areaPrice from './areaPrice.vue'
export default {
  name: 'ModifyDialog',
  components: {
    areaPrice
  },
  props: {
    modifyConfig: {
      type: Object,
      default: () => ({
        modifyType: 'suggestPrice',
        title: '设置药帮忙价',
        barcode: '',
        suggestPrice: null,
        chainPrice: null,
        ids:[],
        priceType:'',
        grossProfitMargin:null,
        chainGrossProfitMargin:null,
        basePrice:null,
        priceSyncErp: null,
        chainPriceSyncErp: null,
        skuAreaPriceVos: null
      }),
    },
    isShowAreaPrice: {
      type: Boolean,
      default: false
    }
  },
  created() {
    this.priceData = {
      fob: this.formModel.suggestPrice,
      chainPrice: this.formModel.chainPrice
    }
    setTimeout(() => {
      this.formModel.priceSkuPriceVos = JSON.parse(JSON.stringify(this.modifyConfig.skuAreaPriceVos)) 
    },0)
  },
  data() {
    const validatePass = (rule, value, callback) => {
      if (!value) {
        if(rule.field == 'suggestPrice'){
          callback(new Error('请输入大于0的数字，限2位小数'));
        }else if(rule.field == 'totalStock') {
          callback(new Error('请输入大于0的正数'));
        }else {
          callback();
        }
      } else {
        callback();
      }
    };
    const priceSkuPriceVosValidator = (rule, value, callback) => {
      let msg = ''
      console.log(value);
      if (value && value.length > 0) {
        if (value.some(item => {
          if (!item.groupId) {
            msg = '请选择用户组';
            return true;
          }
          if (item.price == '' && item.priceControlType == 1) {
            msg = '请输入价格';
            return true
          }
          if (!item.priceControlType) {
            msg = '请选择控价方式';
            return true;
          } else if (item.priceControlType != 1 && (!item.ratio || !item.basePriceType)) {
            msg = '价格设置未填写完整'
            console.log(item);

            return true;
          }
          return false
        })) {
          console.log(666);

          callback(new Error(msg))
        } else {
          console.log(777);

          callback();
        }
      }
      callback();
    }
    return {
      
      dialogVisible: true,

      loading: false,
      deleteDialogVisible: false,
      formModel: {
        totalStock: '', // 商品总库存
        suggestPrice: this.modifyConfig.suggestPrice?JSON.parse(JSON.stringify(this.modifyConfig.suggestPrice)):null, // 建议零售价
        chainPrice: this.modifyConfig.chainPrice?JSON.parse(JSON.stringify(this.modifyConfig.chainPrice)):null, // 连锁采购价
        shelves:'',
        //持设置价格是否同步erp
        chainPriceSyncErp: this.modifyConfig.chainPriceSyncErp?JSON.parse(JSON.stringify(this.modifyConfig.chainPriceSyncErp)):null,
        priceSyncErp: this.modifyConfig.priceSyncErp?JSON.parse(JSON.stringify(this.modifyConfig.priceSyncErp)):null,
        priceSkuPriceVos: [], // 分区域价数据
        grossProfitMargin:this.modifyConfig.grossProfitMargin?JSON.parse(JSON.stringify(this.modifyConfig.grossProfitMargin)):null, // 单体毛利率
        chainGrossProfitMargin:this.modifyConfig.chainGrossProfitMargin?JSON.parse(JSON.stringify(this.modifyConfig.chainGrossProfitMargin)):null, // 连锁毛利率
        basePrice:this.modifyConfig.basePrice?JSON.parse(JSON.stringify(this.modifyConfig.basePrice)):null, // 底价
        wholeaseActPrice: this.modifyConfig.wholeaseActPrice?JSON.parse(JSON.stringify(this.modifyConfig.wholeaseActPrice)):null, // 活动价
        wholeaseOriPrice: this.modifyConfig.wholeaseOriPrice?JSON.parse(JSON.stringify(this.modifyConfig.wholeaseOriPrice)):null, //原价
        wholeaseStartCount: this.modifyConfig.wholeaseStartCount?JSON.parse(JSON.stringify(this.modifyConfig.wholeaseStartCount)):null, //起购数量
        wholeaseAllStock: this.modifyConfig.totalLimitQty === "不限" ? 0 : 1, //活动总限购库存
        wholeaseAllCount: this.modifyConfig.totalLimitQty?JSON.parse(JSON.stringify(this.modifyConfig.totalLimitQty)):null, // 活动总限购数量
        wholeaseSingleStock: this.modifyConfig.limitType ? 1 : 0, //单点是否限购
        wholeaseSingleType: this.modifyConfig.limitType?JSON.parse(JSON.stringify(this.modifyConfig.limitType)):null, // 单点限购类型
        wholeaseSingleCount: this.modifyConfig.limitQty ? (this.modifyConfig.limitQty === "不限" ? null : JSON.parse(JSON.stringify(this.modifyConfig.limitQty))) : null, //单店限购数量
      },
      priceData: {},
      personalLimitTypeList: [{
        code: 1, value: "活动期间限购"
      }, {
        code: 2, value: "每天（每天00:00至24:00）"
      }, {
        code: 3, value: "单笔订单限购"
      }, {
        code: 4, value: "每周（周一00:00至周日24:00）"
        }, {
        code: 5, value: "每月（每月1号00:00至每月最后一天24:00）"
      }],
      rules: {
        totalStock: [{ validator: validatePass, trigger: 'blur' }],
        chainPrice: [{ validator: validatePass, trigger: 'blur' }],
        suggestPrice: [{ required: true, validator: validatePass, trigger: 'blur' }],
        grossProfitMargin: [
          {required: true, message: '单体毛利率不能为空', trigger: 'blur'}
        ],
        basePrice: [
          {required: true, message: '底价不能为空', trigger: 'blur'}
        ],
        noRequired: [
          { required: false },
        ],
        wholeaseActPrice: [
          {required: true, message: '活动价不能为空', trigger: 'blur'}
        ],
        wholeaseAllCount: [
          {required: true, message: '总限购数量不能为空', trigger: 'blur'}
        ],
        wholeaseSingleCount: [
          {required: true, message: '单店限购数量不能为空', trigger: 'blur'}
        ],
        priceSkuPriceVos:[{ required: false, validator: priceSkuPriceVosValidator, trigger: ''}]
      },

      suggestPriceCopy:this.modifyConfig.suggestPrice?JSON.parse(JSON.stringify(this.modifyConfig.suggestPrice)):null,
      chainPriceCopy:this.modifyConfig.chainPrice?JSON.parse(JSON.stringify(this.modifyConfig.chainPrice)):null
    };
  },
  computed: {
    sumPrice() {
      return this.formModel.wholeaseActPrice * this.formModel.wholeaseStartCount || 0;
    }
  },
  watch: {
    'formModel.wholeaseAllStock': {
      handler(val) {
        this.formModel.wholeaseAllCount = "";
      },
      deep: true
    },
    'formModel.wholeaseSingleStock': {
      handler(val) {
        this.formModel.wholeaseSingleType = "";
        this.formModel.wholeaseSingleCount = "";
      },
      deep: true
    }
  },
  methods: {
    //限制只能输入两位小数
    limitInput(value, name) {
      this.formModel[name] = ('' + value) // 第一步：转成字符串
        .replace(/[^\d^\.]+/g, '') // 第二步：把不是数字，不是小数点的过滤掉
        .replace(/^0+(\d)/, '$1') // 第三步：第一位0开头，0后面为数字，则过滤掉，取后面的数字
        .replace(/^\./, '0.') // 第四步：如果输入的第一位为小数点，则替换成 0. 实现自动补全
        .match(/^\d*(\.?\d{0,2})/g)[0] || '' // 第五步：最终匹配得到结果 以数字开头，只有一个小数点，而且小数点后面只能有0到2位小数
    },
    handleClose() {
      this.$emit('update:modifyDialogVisible', false);
    },
    modifyConfirm() {
      this.$refs.ruleForm.validate(async (valid) => {
        if (valid) {
          this.loading = true;
          const { barcode } = this.modifyConfig;
          if (this.modifyConfig.modifyType === 'totalStock') {
            const res = await updateStock({ barcode, totalStock: this.formModel.totalStock });
            if (res.code === 0) {
              // this.$message.success(res.message)
              this.$message.success('设置成功');
              this.handleClose();
              this.confirmCallback();
            } else {
              // this.$message.error(res.message)
              this.$message.error(res.message);
            }
          } else if(this.modifyConfig.modifyType === 'suggestPrice'){
            const chainSyncErp = this.formModel.chainPriceSyncErp === null ? 0 : this.formModel.chainPriceSyncErp
            const priceSyncErp = this.formModel.priceSyncErp === null ? 0 : this.formModel.priceSyncErp
            let params = {
              barcode,
              fob: this.formModel.suggestPrice,
              chainPrice: this.formModel.chainPrice,
              basePrice: this.formModel.basePrice,
              priceSkuPriceVos: this.formModel.priceSkuPriceVos,
              chainSyncErp,
              priceSyncErp
            }
            if(this.modifyConfig.priceType === 2){
              params.grossProfitMargin = this.formModel.grossProfitMargin;
              params.chainGrossProfitMargin = this.formModel.chainGrossProfitMargin;
            }
            const res = await updateAmount(params);
            if (res.code === 0) {
              this.$message.success('价格设置成功');
              this.handleClose();
              this.confirmCallback();
            } else {
              // this.$message.error(res.message)
              this.$message.error(res.message);
            }
          }else if(this.modifyConfig.modifyType === 'shelves'){
            const { ids } = this.modifyConfig;
            const params = {
              skuIds: ids,
              remark: this.formModel.shelves,
            };
            const res = await batchDownNew(params);
            if (res.code === 0) {
              this.$message.success({ message: res.message, offset: 100 });
              this.handleClose();
              this.confirmCallback();
            } else if (res.code === 2) {
              this.$alert(res.message, '温馨提示', {
                confirmButtonText: '确定',
                callback: (action) => {
                },
              });
            }else {
              this.$message.error({ message: res.message, offset: 100 });
            }
          } else if (this.modifyConfig.modifyType === 'wholease') {
            if (!this.formModel.wholeaseStartCount) {
              this.$message.warning("起购数量需填写大于0的正整数");
              this.loading = false;
              return;
            } 
            if (!this.formModel.wholeaseActPrice) {
              this.$message.warning("活动价需为正数，最多两位小数");
              this.loading = false;
              return;
            }
            if (!this.formModel.wholeaseOriPrice) {
              this.$message.warning("原价需为正数，最多两位小数");
              this.loading = false;
              return;
            }
            // else if (this.formModel.wholeaseOriPrice && this.formModel.wholeaseActPrice > this.formModel.wholeaseOriPrice) {
            //   this.$message.warning("活动价需小于等于原价");
            //   this.loading = false;
            //   return;
            // }
             else if (!Number.isInteger(this.formModel.wholeaseStartCount / this.modifyConfig.mediumPackageNum)) {
              this.$message.warning(`起购数量需是购买倍数的整数倍，当前购买倍数为${this.modifyConfig.mediumPackageNum}`);
              this.loading = false;
              return;
            } else if (this.formModel.wholeaseStartCount && this.modifyConfig.limitQty && this.formModel.wholeaseStartCount > this.modifyConfig.limitQty) {
              this.$message.warning("起购数量需小于等于单个药店采购上限");
              this.loading = false;
              return;
            } else if (this.formModel.wholeaseStartCount && this.formModel.wholeaseAllStock && this.formModel.wholeaseStartCount > this.modifyConfig.totalLimitQty) {
              this.$message.warning("起购数量需小于等于活动数量总上限");
              this.loading = false;
              return;
            }
            let params = {
              groupNum: this.formModel.wholeaseStartCount,
              groupPrice: this.formModel.wholeaseActPrice,
              snapPrice: this.formModel.wholeaseOriPrice,
              updateType: 1, //修改类型  1 价格 2库存 3供货信息
              actId: this.modifyConfig.actId
            }
            updateParams(params).then(res => {
              if (res.code === 1000) {
                this.$message.success('价格设置成功');
                this.handleClose();
                this.confirmCallback();
              } else {
                // this.$message.error(res.message)
                this.$message.error(res.msg);
              }
            })
          } else if (this.modifyConfig.modifyType === 'wholeaseStock') {
            if (this.formModel.wholeaseAllStock && this.formModel.wholeaseAllCount <= 0) {
              this.$message.warning("总限购数量需填写大于0的正整数");
              this.loading = false;
              return;
            } else if (this.formModel.wholeaseSingleStock && this.formModel.wholeaseSingleCount <= 0) {
              this.$message.warning("单店限购数量需填写大于0的正整数");
              this.loading = false;
              return;
            }
            let params = {
              totalLimitNum: this.formModel.wholeaseAllStock ? this.formModel.wholeaseAllCount : -1,
              personalLimitType: this.formModel.wholeaseSingleStock ? this.formModel.wholeaseSingleType : 0,
              personalLimitNum: this.formModel.wholeaseSingleCount,
              updateType: 2, //修改类型  1 价格 2库存 3供货信息
              actId: this.modifyConfig.actId
            }
            updateParams(params).then(res => {
              if (res.code === 1000) {
                this.$message.success('修改成功');
                this.handleClose();
                this.confirmCallback();
              } else {
                // this.$message.error(res.message)
                this.$message.error(res.msg);
              }
            })
          }
          this.loading = false;
        } else {
          console.log('error submit!!');
          return false;
        }
      });
    },
    checkPrice(value) {
      this.$set(this.formModel, 'suggestPrice', value.match(/^\d*(\.?\d{0,2})/g)[0]);
    },
    checkPrices(value) {
      this.$set(this.formModel, 'chainPrice', value.match(/^\d*(\.?\d{0,2})/g)[0]);
    },
    checkAvailableQty(value) {
      this.$set(this.formModel, 'totalStock', value.replace(/[^0-9]/g, ''));
    },
    inputBlur(value, str) {
      if (value && str) {
        this.$set(this.formModel, str, parseFloat(value));
      }
      this.priceData = {
        fob: this.formModel.suggestPrice,
        chainPrice: this.formModel.chainPrice
      }
    },
    confirmCallback() {
      this.$emit('confirmCallback', true);
    },
    chainPriceSyncErpChange(type) {
      if (type) {
        this.formModel.chainPriceSyncErp = 1;
      } else {
        this.formModel.chainPriceSyncErp = 0;
      }
    },
    priceSyncErpChange(type) {
      if (type) {
        this.formModel.priceSyncErp = 1;
      } else {
        this.formModel.priceSyncErp = 0;
      }
    },
    // 计算单体采购价，连锁采购价
    grossProfitMarginChange(value){
      if(value){
        this.formModel.grossProfitMargin = value;
        this.formModel.suggestPrice = this.numFilter(
          this.formModel.basePrice/(1-this.formModel.grossProfitMargin/100));
      }else{
        this.formModel.suggestPrice = this.suggestPriceCopy;
      }
    },
    chainGrossProfitMarginChange(value){
      if(value){
        this.formModel.chainGrossProfitMargin = value;
        this.formModel.chainPrice = this.numFilter(
          this.formModel.basePrice/(1-this.formModel.chainGrossProfitMargin/100));
      }else{
        this.formModel.chainPrice = this.chainPriceCopy;
      }
    },
    basePriceChange(value){
      if (this.modifyConfig.priceType === 2) {
        this.grossProfitMarginChange(this.formModel.grossProfitMargin);
        this.chainGrossProfitMarginChange(this.formModel.chainGrossProfitMargin);
      }
    },
    // 截取当前数据到小数点后两位
    numFilter(value) {
      const realVal = parseFloat(value).toFixed(2);
      return realVal;
    },
    deleteAllAreaPrice() {
      this.$message.success("成功删除")
      this.deleteDialogVisible = false
    }
  },
};
</script>

<style scoped lang="scss">
.colorRed {
  color: #ff2121;
}
.el-icon-warning {
  color: #ffba00;
}
.wholease-tips {
  color: #E91C1C;
  font-family: PingFangSC-Regular;
  font-weight: 400;
  font-size: 12px;
  padding-left: 100px;
}
</style>
