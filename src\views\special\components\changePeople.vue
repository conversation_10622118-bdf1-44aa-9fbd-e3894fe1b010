<template>
  <div class="choose">
    <el-dialog
      title="选择商品"
      :visible.sync="dialogFormVisible"
      width="900px"
    >
      <div>
        <div style="padding: 15px 20px">
          <el-row :gutter="20">
            <el-col :span="6">
              <el-input v-model="listQuery.id" placeholder="请输入内容" size="small">
                <template slot="prepend"> 人群ID </template>
              </el-input>
            </el-col>
            <el-col :span="6">
              <el-input v-model="listQuery.tagName" placeholder="请输入内容" size="small">
                <template slot="prepend"> 人群名称 </template>
              </el-input>
            </el-col>
            <el-col :span="6">
              <span class="search-title">活动时间</span>
              <div style="width: 100%;display: table-cell">
                <el-date-picker
                  style="width: 100%"
                  v-model="listQuery.createTime"
                  type="date"
                  size="small"
                  format="yyyy-MM-dd"
                  value-format="timestamp"
                  placeholder="选择日期">
                </el-date-picker>
              </div>
            </el-col>
            <el-col :span="6" style="text-align: right">
              <el-button size="small" @click="resetList"> 重置 </el-button>
              <el-button type="primary" size="small" @click="getPeopleList"> 查询 </el-button>
            </el-col>
          </el-row>
        </div>
        <div style="padding: 0 20px">
          <el-table
            ref="goodsTable"
            :data="goodsData"
            stripe
            border
            tooltip-effect="dark"
            style="width: 100%"
          >
            <el-table-column label="" width="40">
              <template slot-scope="scope">
                <el-radio :label="scope.row.id" v-model="peopleData.radio" @change.native="getCurrentRow(scope.row)" style="color: #fff;padding-left: 10px; margin-right: -25px;"></el-radio>
              </template>
            </el-table-column>
            <el-table-column label="人群ID" prop="id"></el-table-column>
            <el-table-column label="人群名称" prop="tagName"></el-table-column>
            <el-table-column label="人群定义">
              <template slot-scope="scope">
                <span v-for="(item,index) in scope.row.tagDef" :key="index">{{item}}</span>
              </template>
            </el-table-column>
            <el-table-column label="创建时间">
              <template slot-scope="scope">
                <span>{{scope.row.createTime | formatDate}}</span>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div class="page-box">
          <Pagination
            v-show="goodsListTotal > 0"
            :total="goodsListTotal"
            :page.sync="listQuery.pageNum"
            :limit.sync="listQuery.pageSize"
            @pagination="getPeopleList(1)"
          />
        </div>
      </div>
      <div
        slot="footer"
        class="dialog-footer"
      >
        <el-button
          size="small"
          @click="dialogFormVisible = false"
        >
          取 消
        </el-button>
        <el-button
          type="primary"
          size="small"
          @click="sendData"
        >
          确 定
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>

import { getProductPeopleList } from '../../../api/activity/special'
import Pagination from '../../../components/Pagination/index.vue'
export default {
  name: 'changePeople',
  data() {
    return {
      dialogFormVisible: false,
      goodsData: [],
      goodsListTotal: 0,
      listQuery: {
        pageNum: 0,
        pageSize: 10,
        id: '',
        tagName: '',
        createTime: ''
      },
      peopleData: {
        radio: '',
        name: ''
      }
    };
  },
  components: { Pagination },
  methods: {
    getChooseData() {},
    getPeopleList(from,data) {
      const params = {...this.listQuery}
      getProductPeopleList(params).then( (res) => {
        !from ? this.dialogFormVisible = true : ''
        if(res.code == 1000) {
          this.goodsData = res.data.list
          this.peopleData.radio = data
        }else {
          this.$message.error(res.msg)
        }
      })
    },
    sendData() {
      this.$emit('sendPeopleData', this.peopleData)
      this.dialogFormVisible = false
    },
    getCurrentRow(value) {
      this.peopleData.radio = value.id
      this.peopleData.name = value.tagName
    },
    resetList() {
      this.listQuery = {
        pageNum: 0,
        pageSize: 10,
        id: '',
        tagName: '',
        createTime: ''
      }
      this.getPeopleList()
    }
  },
};
</script>

<style scoped lang="scss">
.choose {
  ::v-deep  .el-button--primary {
    background: #4183d5;
  }
  ::v-deep  .el-dialog__body {
    padding: 0 0 10px;
  }
  ::v-deep  .el-dialog__header {
    padding: 10px 16px;
    background: #f9f9f9;
  }
  ::v-deep  .el-dialog__headerbtn {
    top: 13px;
  }
  ::v-deep  .el-input-group__prepend{
    background: #ffffff;
  }
  ::v-deep  .el-input__inner {
    border-radius: 0 4px 4px 0;
  }
  .search-title {
    display: table-cell;
    padding: 0 10px;
    text-align: center;
    border: 1px solid #dcdfe6;
    height: 30px;
    line-height: 30px;
    vertical-align: middle;
    border-right: none;
    border-radius: 4px 0 0 4px;
    color: #333333;
    white-space: nowrap;
  }
}
</style>
