<template>
  <div class="content">
    <div style="text-align: right;margin-top: 15px">
      <el-button type="primary" size="small" @click="editOrsave">{{ disable ? '编辑' : '保存' }}</el-button>
    </div>
    <div class="title_line">
      <span style="margin-right: 10px;">店铺区域控销</span>
      <el-button v-if="businessShow"type="primary" size="small" @click="open">申请开通全国商圈</el-button>
    </div>
    <div class="viewContent">
      <div class="saleableArea">
        <p>可售区域</p>
        <div class="treeContent">
          <el-tree
            ref="saleableArea"
            :data="saleableAreaData"
            :props="props"
            :check-strictly="isCheckStrictly"
            show-checkbox
            node-key="areaCode"
            :render-content="renderContent"
            :default-expanded-keys="restrictedAreaData"
            @check="handleCheckChange"
            @node-expand="handleExpand"
          />
        </div>
      </div>
      <div class="restrictedArea">
        <p>禁销区域（以下区域内对应客户不可购买店铺内所有商品）</p>
        <div class="restrictedAreaContent">
          <span class="red_area" v-for="item in unCheckNodes" :key="item.id">{{ item.areaName }}</span>
        </div>
      </div>
    </div>
    <el-dialog :visible="businessVisible" @close="businessVisible = false;">
      <template slot="title">
        <p style="display: flex;justify-content: space-between;width: 90%;align-items: center;">
          <span>新店运营前准备工作有哪些？</span>
          <el-button size="small" @click="downloadFile">点击下载</el-button>
        </p>
      </template>
      <div style="position: relative;width: 100%;height: 500px;overflow: auto;">
        <img style="width: 100%;height: auto;" src="http://oss-ec.ybm100.com/pop/%E5%BC%80%E5%85%A8%E5%9B%BD%E5%95%86%E5%9C%88%E8%A7%84%E5%88%99.png"></img>
      </div>
    </el-dialog>
  </div>
</template>

<script>

import { areasCodes, saleAreas, saveSaleAreas, openNationalBusinessCircleButton, openNationalBusinessCircle } from '@/api/storeManagement/storeAreaSalesControl';

export default {
  name: 'index',
  data() {
    return {
      props: {
        label: 'areaName',
        children: 'childList',
        isLeaf: (data, node) => {
          return node.areaLevel === 4;
        }
      },
      saleableAreaData: [],
      restrictedAreaData: [0],
      checkKeys: [],
      unCheckNodes: [],
      disable: true,
      isCheckStrictly: false,
      expandedData:'',
      businessShow: false,
      businessVisible: false,
      businessCondition: false
    };
  },
  created() {
    this.getSaleAreas();
  },
  mounted() {
    this.openNationalBusinessCircleButton()
    this.handleExpand()
  },
  methods: {
    handleClose() {
      this.$emit('update:showAdd', false)
    },
    openNationalBusinessCircleButton() {
      openNationalBusinessCircleButton().then(res => {
        if (res.code === 0) {
          this.businessShow = res.data.show;
          this.businessCondition = res.data.condition;
        }
      })
    },
    downloadFile() {
      const a = document.createElement('a');
      a.target = "_blank";
      a.download = "开全国商圈文案.pdf";
      a.href = "http://oss-ec.ybm100.com/pop/%E5%BC%80%E5%85%A8%E5%9B%BD%E5%95%86%E5%9C%88%E8%A7%84%E5%88%99.pdf";
      a.click();
    },
    open() {
      if (this.businessCondition) {
        openNationalBusinessCircle().then(res => {
          if (res.code === 0) {
            this.$message.success('开通成功');
            this.openNationalBusinessCircleButton();
            this.getSaleAreas();
          } else {
            this.$message.error(res.message);
          }
        })
      } else {
        this.businessVisible = true;
      }
    },
    handleExpand() {
      this.$nextTick(() => {
        const levelName = document.getElementsByClassName('foo'); // levelname是上面的最底层节点的名字
        for (let i = 0; i < levelName.length; i++) {
          levelName[i].parentNode.style.cssFloat = 'left'; // 最底层的节点，包括多选框和名字都让他左浮动
          levelName[i].parentNode.style.styleFloat = 'left';
        }
      });
    },
    renderContent(h, { node }) {
      // 树节点的内容区的渲染 Function
      let classname = '';
      // 由于项目中有三级菜单也有四级级菜单，就要在此做出判断
      if (node.level === 4) {
        classname = 'foo';
      }
      if (node.level === 3 && node.childNodes.length === 0) {
        classname = 'foo';
      }
      return h('p', { class: classname }, node.label);
    },
    flatten(ary) {
      const list = [];
      ary.forEach(item => {
        const {
          childList,
          ...obj
        } = item;
        list.push(obj);
        if (childList && childList.length) {
          const childrenList = this.flatten(childList);
          list.push(...childrenList);
        }
      });
      return list;
    },
    setDisabled(obj,flag) {
      obj.forEach((item) => {
        this.$set(item, 'disabled', flag);
        if (item.childList) {
          this.setDisabled(item.childList,flag);
        }
      });
      return obj;
    },
    async getAreasCodes() {
      const res = await areasCodes();
      if (res && res.code === 0) {
        this.saleableAreaData = []
        const selectAll = {
          areaName: '全部',
          areaCode: 0,
          areaLevel: 0,
          childList: res.data,
        };
        this.saleableAreaData.push(selectAll);
        this.$nextTick(()=>{
          // this.restrictedAreaData = this.expandedData
          this.$refs.saleableArea.setCheckedKeys(this.expandedData)
          this.setDisabled(this.saleableAreaData,true);
          this.getCheckNodes();
        })

      } else {
        this.$message.error(res.message || '店铺控销可选区域失败');
      }
    },
    async getSaleAreas() {
      const res = await saleAreas();
      if (res && res.code === 0) {
        this.expandedData = [...res.data];
       await this.getAreasCodes();
      } else {
        this.$message.error(res.message || '获取可售区域失败');
      }
    },
    handleCheckChange() {
      this.getCheckNodes();
    },
    getCheckNodes() {
      const nodes = this.$refs.saleableArea.getCheckedNodes();
      //去重
      const selectCode = new Map();
      nodes.forEach(item => {
        if (!selectCode.has(item.areaCode)) {
          selectCode.set(item.areaCode, true);
          if (item.parentCode && selectCode.has(item.parentCode)) {
            selectCode.set(item.areaCode, false);
          }
        }
      });
      const selectKeyAry = [];  //选中的key
      selectCode.forEach((val, key) => {
        if (val) {
          selectKeyAry.push(key);
        }
      });
      this.checkKeys = selectKeyAry;

      const flattenAry = this.flatten(this.saleableAreaData[0].childList);
      const allCheckKeys = this.$refs.saleableArea.getCheckedNodes(false, true)
        .map(item => item.areaCode);

      const unCheckNodes = flattenAry.filter((item) => !allCheckKeys.includes(item.areaCode));

      const unCheckCode = new Map();
      unCheckNodes.forEach(item => {
        if (!unCheckCode.has(item.areaCode)) {
          unCheckCode.set(item.areaCode, true);
          if (item.parentCode && unCheckCode.has(item.parentCode)) {
            unCheckCode.set(item.areaCode, false);
          }
        }
      });
      const unCheckKeys = [];
      unCheckCode.forEach((val, key) => {
        if (val) {
          unCheckKeys.push(key);
        }
      });
      this.unCheckNodes = flattenAry.filter((item) => unCheckKeys.includes(item.areaCode));
    },
    async editOrsave() {
      if (this.disable) {
        //编辑
        this.disable = false
        this.setDisabled(this.saleableAreaData,false);
      } else {
        //保存
        console.log('保存--', this.checkKeys.filter(item => !!item));
        const keys = this.checkKeys.filter(item => !!item);
        if (keys.length > 0) {
          const res = await saveSaleAreas(keys);
          if (res && res.code === 0) {
            this.$message.success('保存成功');
            this.disable = true;
            await this.getSaleAreas();
          } else {
            this.$message.error(res.message || '保存失败');
          }
        } else {
          this.$message.warning('可售区域不能为空，若希望暂时不售卖商品，可以联系运营关店');
        }
      }
    }
  }
};
</script>

<style scoped lang="scss">
.content {
  padding: 16px;

  .viewContent {
    position: relative;

    .saleableArea {
      position: absolute;
      top: 0;
      left: 0;
      width: 45%;
      height: auto;

      .treeContent {
        width: 100%;
        border: 1px solid #666;
        min-height: 600px;
        max-height: 600px;
        overflow-y: auto;
      }
    }

    .restrictedArea {
      position: absolute;
      top: 0;
      right: 0;
      width: 45%;
      height: auto;
      color: red;

      .restrictedAreaContent {
        width: 100%;
        height: auto;
        border: 1px solid red;
        box-sizing: border-box;
        padding: 15px;
        display: inline-block;
        min-height: 600px;
        max-height: 600px;
        overflow-y: auto;

        .red_area {
          display: inline-block;
          padding: 5px 8px;
          color: #f56c6c;
          background: #fef0f0;
          border: 1px solid #fbc4c4;
          border-radius: 3px;
          margin-right: 10px;
          margin-bottom: 10px;
        }
      }
    }
  }


}
</style>
