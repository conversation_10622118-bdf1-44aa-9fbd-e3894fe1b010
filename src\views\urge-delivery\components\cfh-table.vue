<template>
<div>
  <el-table
  stripe
  border
  id="tabled"
  ref="table"
  style="width: 100%;"
  :data="tableData">
    <template slot="empty">
      <div class="empty-data">
        <img :src="emptyData">
        <div>{{ emptyText }}</div>
      </div>
    </template>
    <el-table-column
      label="序号"
      type="index"
      :index="indexMethod"
      width="50">
      <!-- <template slot-scope="scope">
        <p>{{scope.row.order}}</p>
      </template> -->
    </el-table-column>
    <el-table-column
      label="催发货时间">
      <template slot-scope="scope">
        <p>{{formatTime(scope.row.reminderTime)}}</p>
      </template>
    </el-table-column>
    <el-table-column
      label="订单号">
      <template slot-scope="scope">
        <div class="orderForm" v-if="scope.row.orderNo">
          <p>
            YBM单号：
            <span @click="jumpOrderPage(scope.row.orderNo)" style="color: #4184d5">
              {{scope.row.orderNo}}
              <i class="el-icon-document-copy" @click.stop="copyText(scope.row.orderNo)" style="color: #41b3d5"></i>
            </span>
          </p>
          <p>
            订单ID：
            <span>
              {{scope.row.orderId}}
              <i class="el-icon-document-copy" @click.stop="copyText(scope.row.orderId)" style="color: #41b3d5"></i>
            </span>
          </p>
        </div>
      </template>
    </el-table-column>
    <el-table-column
      label="客户">
      <template slot-scope="scope">
        <div v-if="scope.row.customerErpCode">
        <p>
          {{scope.row.customerErpCode}}
          <i 
          class="el-icon-service"
          @click='openService(scope.row.customerName)'
          style="color: #4184d5;cursor: pointer"></i>
        </p>
        <p>{{scope.row.customerName}}</p>
        <p class="btnText">
          <span @click="btnClick(scope.row,'ybmSalesman')">药帮忙业务员</span>
        </p>
        </div>
      </template>
    </el-table-column>
    <el-table-column
      label="客户收货地址">
      <template slot-scope="scope">
        <p>{{scope.row.customerAddress}}</p>
      </template>
    </el-table-column>
    <el-table-column
      label="催发货工单明细">
      <template slot-scope="scope">
        <p>支付方式（{{scope.row.payTypeStr}}）</p>
        <p>
          订单总金额：
          <span>{{scope.row.totalAmount}}</span>
        </p>
        <p style="color: red">
          超时处理赔偿金额：
          <span>
            {{scope.row.compensationAmount}}
            <el-tooltip 
            class="item" 
            effect="dark" 
            content="如您超时未处理，将自动退款，并额外赔偿该订单金额的10%给客户（从保证金中扣除），请及时处理" 
            placement="bottom">
              <i class="el-icon-warning-outline" style="color: #333"></i>
            </el-tooltip>
          </span>
        </p>
      </template>
    </el-table-column>
    <el-table-column
      label="处理状态">
      <template slot-scope="scope">
        <p>{{scope.row.statusStr}}</p>
      </template>
    </el-table-column>
    <el-table-column
      label="操作"
      width="100">
      <template slot-scope="scope">
       <p class="btnText" @click="btnClick(scope.row,'showHistroy')">查看历史</p>
        <div>
          <p 
            class="btnText"
            v-if="[10,30,31,32].includes(scope.row.status)" 
            @click="jumpOrderPage(scope.row.orderNo)">去处理</p>
          <p 
            class="btnText"
            v-if="[10].includes(scope.row.status)"
            @click="btnClick(scope.row,'appeal',scope.$index)">申诉</p>
          <p v-if="[10,31,32].includes(scope.row.status)">
           倒计时信息：
           <span style="color: red;display: block">{{ formattedRemainingTime(scope.row) }}</span>
          </p>
          </div>
      </template>
    </el-table-column>
  </el-table>
  <ybmSalesmanDialog
    v-if="ybmSalesmanVisible"
    :dialog-visible="ybmSalesmanVisible"
    :merchant-id="merchantId"
    @cancelDialog="cancelDialog(1)"
   />
    <appeal-dialog-visible
     v-if="appealDialogVisible"
     :appealDialogVisible="appealDialogVisible"
     :reminder="reminder"
     :index="operateIndex"
     @freshPage="freshPage"
     @cancelDialog="cancelDialog(2)"
    />
   <historyRecordDialog
    v-if="histroyDialogVisible"
    :reminderId="reminderId"
    :histroyDialogVisible="histroyDialogVisible"
    @cancelDialog="cancelDialog(3)"
   />
</div>
</template>

<script>
import empty from '@/assets/notUser.png';
import { queryMsgToken } from '@/api/home';
import ybmSalesmanDialog from './ybmSalesmanDialog.vue';
import appealDialogVisible from './appealDialogVisible.vue';
import historyRecordDialog from './historyRecordDialog.vue';
export default {
    components: {
      ybmSalesmanDialog,
      appealDialogVisible,
      historyRecordDialog
    },
    props: {
      tableData: {
        default: []
      },
      emptyText: {
        type: String,
        default: '暂无数据',
      },

    },
    created() { 
      // 为每个 item 设置定时器  
      this.timer = setInterval(this.updateCurrentTime, 1000);
    },
     beforeDestroy() {  
      // 组件销毁前清除所有定时器  
      if (this.timer) {  
        clearInterval(this.timer);  
      }  
    },
    data() {
      return {
        emptyData: empty, // 空状态图片
        ybmSalesmanVisible: false, // 查看业务员弹窗
        appealDialogVisible: false, // 申诉的弹窗
        histroyDialogVisible: false, // 查看历史记录的弹窗
        reminderId: "", // 操作申诉的记录id
        reminder: "", // 操作申诉的记录
        startTime: null,
        merchantId: "",
        currentTime: "", 
        timer: "",
        operateIndex: -1
      }
    },
    methods: {
      async copyText(text) {
        try {
          await navigator.clipboard.writeText(text)
          this.$message.success("已复制到剪切板")
        }catch(e) {
          this.$message.error("复制失败" + e.message)
        }
      },
      jumpOrderPage(orderNo) {

        window.openTab('/orderList', { orderNo })
      },
      async openService(key) {
        const res = await queryMsgToken();
        if (res && res.code === 0) {
          const { token, domainName } = res.data;
          const str = `&orgId=${key}`;
          const userId = `&userId=${res.data.userId}`;
          window.open(domainName + token + str+userId);
        } else {
          this.$message.error(res.message);
        }
      },
      btnClick(value,targetStr,index) {
        if(targetStr === 'ybmSalesman') {
          this.merchantId = value.merchantId || 1
          return this.ybmSalesmanVisible = true
        }
        if(targetStr === 'appeal') {
          this.reminder = value
          this.operateIndex = index
          return this.appealDialogVisible = true 
        }
        if(targetStr === 'showHistroy') {
          this.reminderId = value.id
          return this.histroyDialogVisible = true
        }
      },
      cancelDialog(cancelTarget) {
        if(cancelTarget === 1) {
          return this.ybmSalesmanVisible = false
        }
        if(cancelTarget === 2) {
          return this.appealDialogVisible = false
        }
        if(cancelTarget === 3) {
          return this.histroyDialogVisible = false
        }
      },
      freshPage(index) {
        this.$emit("freshTable",index)
      },
      formatTime(d) {
        var date = new Date(d);
        var YY = date.getFullYear() + '-';
        var MM =
          (date.getMonth() + 1 < 10
            ? '0' + (date.getMonth() + 1)
            : date.getMonth() + 1) + '-';
        var DD = date.getDate() < 10 ? '0' + date.getDate() : date.getDate();
        var hh =
          (date.getHours() < 10 ? '0' + date.getHours() : date.getHours()) + ':';
        var mm =
          (date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes()) +
          ':';
        var ss =
          date.getSeconds() < 10 ? '0' + date.getSeconds() : date.getSeconds();
        return YY + MM + DD + ' ' + hh + mm + ss;
      },
      indexMethod(index) {  
        return (this.tableData.pageNum - 1) * this.tableData.pageSize + index + 1;  
      },
      updateCurrentTime() {  
        this.currentTime = Date.now();  
      }, 
      formattedRemainingTime(order) {  
        const remainingMilliseconds = order.expireTime - this.currentTime;  
        if (remainingMilliseconds <= 0) {  
          return '已过期';  
        }  
        const totalSeconds = Math.floor(remainingMilliseconds / 1000);  
        const hours = String(Math.floor(totalSeconds / 3600)).padStart(2, '0');  
        const minutes = String(Math.floor((totalSeconds % 3600) / 60)).padStart(2, '0');   
        return `${hours}小时${minutes}分`;  
      }  
    }
}
</script>

<style lang="scss">
p {
    margin: 0;
}
.orderForm {
    span {
        display: block;
        cursor: pointer;
    }
}
.btnText {
  color: #4184d5;
  cursor: pointer;
}
</style>