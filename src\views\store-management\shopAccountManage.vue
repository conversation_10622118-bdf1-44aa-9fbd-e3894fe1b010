<template>
<div class="storeVoucher">
    <div class="sticky-tabs">
        <el-tabs v-model="activeName" @tab-click="handleClick">
            <el-tab-pane label="角色管理" name="roleManagement"/>
            <el-tab-pane label="账号管理" name="accountManagement"/>
        </el-tabs>
    </div>
    <transition name="fade" mode="out-in">
        <keep-alive>
            <component :is="currentComponent" @jumpAccountManagement="jump"></component>
        </keep-alive>
    </transition>
</div>
</template>

<script>
import roleManagement from "@/views/user-management/roleManagement.vue"
import accountManagement from "@/views/user-management/accountManagement.vue"
export default {
    name: "shopCustomerManage",
    components: {
        roleManagement,
        accountManagement
    },
    activated(){
        this.selectComponents(this.$route.query.to)
    },
    data() {
        return {
            activeName: "roleManagement",
            currentComponent: roleManagement,
        }
    },
    methods: {
        handleClick(tab, event) {
            this.$router.replace({
                path: 'shopAccountNumManage',
                query: { to: tab.name },
            });
            this.selectComponents(tab.name)
        },
        selectComponents(target) {
            if(target) {
                this.activeName = target
            }
            switch (target) {
                case "roleManagement":
                    this.currentComponent = roleManagement
                    break;
                case "accountManagement":
                    this.currentComponent = accountManagement
                    break;
                default:
                    break;
            }
        },
        jump(roleId) {
            this.$router.replace({
                path: 'shopAccountNumManage',
                query: { to: "accountManagement",roleId },
            });
            this.selectComponents("accountManagement")
        }
    },
}
</script>

<style>
.storeVoucher {
    margin-top: 10px;
    padding-left: 10px;
}
.sticky-tabs {
    position: sticky;
    top: 0;
    z-index: 1000;
    background: #fff;
    padding: 10px 0;
}
/* 定义过渡动画 */
.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-to,
.fade-leave-from {
  opacity: 1;
}
</style>