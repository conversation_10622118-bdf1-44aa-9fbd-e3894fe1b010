<template>
  <div class="invoiceRecord-box">
    <el-tabs
      v-model="activeTab"
      type="card"
    >
      <el-tab-pane label="发票申请" name="demand">
        <demand/>
      </el-tab-pane>
      <el-tab-pane label="发票记录" name="record">
        <record/>
      </el-tab-pane>
      <el-tab-pane label="发票信息" name="info">
        <Info/>
      </el-tab-pane>
    </el-tabs>

  </div>
</template>

<script>
import Info from './components/Info'
import demand from './components/demand'
import record from './components/record'
export default {
  name: 'invoiceRecord',
  components:{
    Info,
    demand,
    record
  },
  data() {
    return {
      activeTab: 'record'
    }
  }
};
</script>

<style lang="scss" scoped></style>
