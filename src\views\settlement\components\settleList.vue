<template>
  <div class="settlement-list">
    <el-form :model="listQuery" :inline="true" ref="listQuery" label-width="100px">
      <el-form-item label="单据号" prop="businessNo">
        <el-input v-model="listQuery.businessNo" placeholder="请输入"></el-input>
      </el-form-item>
      <el-form-item
        label="单据类型"
        prop="businessType"
      >
        <el-select v-model="listQuery.businessType">
          <el-option
            label="订单"
            :value="1"
          />
          <el-option
            label="退款单"
            :value="2"
          />
          <el-option
            label="调账单"
            :value="3"
          />
        </el-select>
      </el-form-item>
      <!-- <el-form-item label="结算状态" prop="orderSettlementStatus">
        <el-select v-model="listQuery.orderSettlementStatus">
          <el-option
            label="未结算"
            :value="0"
          />
          <el-option
            label="已结算"
            :value="1"
          />
        </el-select>
      </el-form-item> -->
      <el-form-item label="佣金结算方式" prop="settlementType">
        <el-select v-model="listQuery.settlementType">
          <el-option label="全部" :value="0"></el-option>
          <el-option label="月结" :value="2"></el-option>
          <el-option label="非月结" :value="1"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item
        label="结算时间"
        prop="settledTime"
      >
        <el-date-picker
          v-model="listQuery.settledTime"
          type="datetimerange"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          :default-time="['00:00:00', '23:59:59']"
        />
      </el-form-item>
      <el-form-item v-if="type===2" label="支付方式" prop="remitStatus">
        <el-select v-model="listQuery.payType">
          <el-option label="全部" :value="99"></el-option>
          <el-option label="线下转账(电汇平台)" :value="3"></el-option>
          <el-option label="线下转账(电汇商业)" :value="4"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="结算状态" prop="orderSettlementStatus">
        <el-select v-model="listQuery.orderSettlementStatus">
          <el-option label="全部" value="" />
          <el-option label="未结算" :value="0" />
          <el-option label="已结算" :value="1" />
        </el-select>
      </el-form-item>
      <el-form-item label="客户名称" prop="merchantName">
        <el-input v-model="listQuery.merchantName" placeholder="请输入"></el-input>
      </el-form-item>
      <el-form-item label="支付时间" prop="paymentTime">
        <el-date-picker
          v-model="listQuery.paymentTime"
          type="datetimerange"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          :default-time="['00:00:00', '23:59:59']"
        />
      </el-form-item>
      <el-form-item class="btn-item">
        <el-button @click="reset">
          重置
        </el-button>
        <el-button
          type="primary"
          @click="getList(listQuery, true)"
        >
          查询
        </el-button>
      </el-form-item>
    </el-form>
    <el-row
      :gutter="20"
      class="price-box mb15"
    >
      <el-col :span="24">
        <div class="btn-item">
          <el-button
            v-if="type === 1"
            v-permission="['settle_online_exportSettlement']"
            plain
            @click="exportList"
          >
            导出结算单
          </el-button>
          <el-button
            v-else
            v-permission="['settle_offline_exportSettlement']"
            plain
            @click="exportList"
          >
            导出结算单
          </el-button>
        </div>
      </el-col>
    </el-row>
    <el-row :gutter="20" class="price-box mb15">
      <el-col
        :span="6"
      >
        <el-tooltip
          class="item"
          content="当前查询结果所有单据，应结算金额求和"
          placement="left"
        >
          <i class="el-icon-warning-outline" />
        </el-tooltip>
        应结算金额合计(元) <span>{{ statementTotalMoneyTotal }}</span>
      </el-col>

      <el-col
        :span="6"
      >
        <el-tooltip
          class="item"
          content="当前查询结果所有单据，应缴纳佣金求和"
          placement="left"
        >
          <i class="el-icon-warning-outline" />
        </el-tooltip>
        应缴纳佣金合计(元) <span>{{ deductedCommissionTotal }}</span>
      </el-col>

      <el-col
        :span="6"
      >
        <el-tooltip
          class="item"
          content="当前查询结果所有单据，实际需缴纳佣金求和"
          placement="left"
        >
          <i class="el-icon-warning-outline" />
        </el-tooltip>
        实际需缴纳佣金合计(元) <span>{{ actualCommissionMoneyTotal }}</span>
      </el-col>
      <el-col
        :span="6"
      >
        <el-tooltip
          class="item"
          content="当前查询结果所有单据，商品实付金额求和"
          placement="left"
        >
          <i class="el-icon-warning-outline" />
        </el-tooltip>
        商品实付金额合计(元) <span>{{ productActualMoneyTotal }}</span>
      </el-col>
      <el-col
        :span="6"
      >
        <el-tooltip
          class="item"
          content="当前查询结果所有单据，佣金金额求和"
          placement="left"
        >
          <i class="el-icon-warning-outline" />
        </el-tooltip>
        佣金金额合计（元） <span>{{ hireMoneyTotal }}</span>
      </el-col>
      <el-col
        :span="6"
      >
        <el-tooltip
          class="item"
          content="当前查询结果所有单据，佣金优惠求和"
          placement="left"
        >
          <i class="el-icon-warning-outline" />
        </el-tooltip>
        佣金优惠合计(元) <span>{{ commissionDiscountMoneyTotal }}</span>
      </el-col>
    </el-row>
    <xyy-table
      :data="list"
      :list-query="listQuery"
      :col="col"
      @get-data="getList"
    />
    <export-tip
      :change-export="changeExport"
      @handleExoprClose="handleExoprClose"
      @handleChangeExport="handleChangeExport"
    />
  </div>
</template>

<script>
import exportTip from '@/views/other/components/exportTip';
import {
  getOnlineSettleDatas,
  getOnlineSettleInfo,
  getOnlineSettleDataNums,
  exportOnlinePaySettleList,
} from '../../../api/settlement/online';
import {
  getOfflineSettleDatas,
  getOfflineSettleInfo,
  getOfflineSettleDataNums,
  exportOfflineSettlementList,
} from '../../../api/settlement/offline';

export default {
  components: { exportTip },
  props: {
    type: {
      type: Number,
      default: 0,
    },
  },
  data() {
    return {
      listQuery: {
        pageSize: 10,
        page: 1,
        total: 0,
        businessNo: '',
        settledTime: [],
        businessType: '',
        orderSettlementStatus: '',
        settlementType: 0,
        payType: 99,
        merchantName: '',
        paymentTime: []
      },
      list: [],
      col: [
        {
          index: 'businessNo',
          name: '单据号',
          width: 250,
          ellipsis: true,
        },
        {
          index: 'settlementType',
          name: '佣金结算方式',
          width: 150,
          formatter: (row, col, cell) => {
            if (cell === 1) {
              return '非月结';
            }
            if (cell === 2) {
              return '月结';
            }
            return '';
          },
        },
        {
          index: 'businessType',
          name: '单据类型',
          formatter: (row, col, cell) => {
            if (cell === 1) {
              return '订单';
            }
            if (cell === 2) {
              return '退款单';
            }
            if (cell === 3) {
              return '调账单';
            }
          },
        },
        {
          index: 'merchantErpCode',
          name: '客户ERP编码',
          width: 200,
          ellipsis: true,
        },
        {
          index: 'merchantName',
          name: '客户名称',
          width: 200,
          ellipsis: true,
        },
        {
          index: 'productMoney',
          name: '商品金额（元）',
          width: 150,
          formatter: (row, col, cell) => (cell || cell === 0 ? cell.toFixed(2) : ''),
        },
        {
          index: 'freightAmount',
          name: '运费金额（元）',
          width: 150,
          formatter: (row, col, cell) => (cell || cell === 0 ? cell.toFixed(2) : ''),
        },
        {
          index: 'totalMoney',
          name: '单据总额含运费（元）',
          width: 200,
          formatter: (row, col, cell) => (cell || cell === 0 ? cell.toFixed(2) : ''),
        },
        {
          index: 'shopTotalDiscount',
          name: '店铺总优惠（元）',
          width: 150,
          formatter: (row, col, cell) => (cell || cell === 0 ? cell.toFixed(2) : ''),
        },
        {
          index: 'platformTotalDiscount',
          name: '平台总优惠（元）',
          width: 150,
          formatter: (row, col, cell) => (cell || cell === 0 ? cell.toFixed(2) : ''),
        },
        {
          index: 'money',
          name: '单据实付金额（元）',
          width: 150,
          formatter: (row, col, cell) => (cell || cell === 0 ? cell.toFixed(2) : ''),
        },
        {
          index: 'productActualMoney',
          name: '商品实付金额（元）',
          width: 150,
          formatter: (row, col, cell) => (cell || cell === 0 ? cell.toFixed(2) : ''),
        },
        {
          index: 'hireMoney',
          name: '佣金金额（元）',
          width: 150,
          formatter: (row, col, cell) => (cell || cell === 0 ? cell.toFixed(2) : ''),
          renderHeader: (h, { column }) => {
            return h(
              'div', [
                h('span', column.label),
                h('el-tooltip', {
                  props: {
                    content: '单据中每个商品的佣金金额=（商品的实付金额+商品分摊的平台优惠金额）*下单时刻商品的佣金比例\n单据的佣金金额=单据中所有商品的佣金金额求和',
                    placement: 'right',
                  },
                }, [
                  h('i', { class: 'el-icon-warning-outline' }),
                ]),
              ],
            );
          },
        },
        {
          index: 'statementTotalMoney',
          name: '应结算金额（元）',
          width: 150,
          formatter: (row, col, cell) => (cell || cell === 0 ? cell.toFixed(2) : ''),
          renderHeader: (h, { column }) => {
            return h(
              'div', [
                h('span', column.label),
                h('el-tooltip', {
                  props: {
                    content: 'a、当单据佣金结算方式为“非月结”且支付方式为“在线支付、线下转账电汇平台“，应结算金额=单据实付金额-（佣金金额-平台总优惠）\nb、当单据佣金结算方式为“月结”且支付方式为“在线支付、线下转账电汇平台“，应结算金额=单据实付金额\nc、当单据佣金结算方式为“月结”且支付方式为“线下转账电汇商业“，应结算金额=0',
                    placement: 'right',
                  },
                }, [
                  h('i', { class: 'el-icon-warning-outline' }),
                ]),
              ],
            );
          },
        },
        {
          index: 'deductedCommission',
          name: '应缴纳佣金（元）',
          width: 200,
          renderHeader: (h, { column }) => {
            return h(
              'div', [
                h('span', column.label),
                h('el-tooltip', {
                  props: {
                    content: '应缴纳佣金=佣金金额-平台总优惠。单据平台补贴金额冲抵佣金金额后，对应单据商业应给平台缴纳的佣金金额',
                    placement: 'right',
                  },
                }, [
                  h('i', { class: 'el-icon-warning-outline' }),
                ]),
              ],
            );
          },
        },
        {
          index: 'commissionDiscount',
          name: '佣金折扣',
          width: 200,
        },
        {
          index: 'discountReason',
          name: '折扣原因',
          width: 200,
        },
        {
          index: 'actualCommissionMoney',
          name: '实际需缴纳佣金',
          width: 200,
          renderHeader: (h, { column }) => {
            return h(
              'div', [
                h('span', column.label),
                h('el-tooltip', {
                  props: {
                    content: '实际需缴纳佣金=应缴纳佣金*佣金折扣。单据享受佣金折扣政策优惠后，实际需给平台缴纳的佣金金额',
                    placement: 'right',
                  },
                }, [
                  h('i', { class: 'el-icon-warning-outline' }),
                ]),
              ],
            );
          },
        },
        {
          index: 'commissionDiscountMoney',
          name: '佣金优惠',
          width: 200,
          renderHeader: (h, { column }) => {
            return h(
              'div', [
                h('span', column.label),
                h('el-tooltip', {
                  props: {
                    content: '佣金优惠=应缴纳佣金-实际需缴纳佣金。单据因享受佣金折扣政策的佣金优惠金额',
                    placement: 'right',
                  },
                }, [
                  h('i', { class: 'el-icon-warning-outline' }),
                ]),
              ],
            );
          },
        },
        {
          index: 'orderSettlementStatus',
          name: '结算状态',
          formatter: (row, col, cell) => (cell === 1 ? '已结算' : '未结算'),
        },
        {
          index: 'orderPayTime',
          name: '支付时间',
          width: 200,
          formatter: (row, col, cell) => (cell ? new Date(cell + 8 * 3600 * 1000).toJSON().substr(0, 19).replace('T', ' ') : ''),
        },
        {
          index: 'orderSettlementTime',
          name: '结算时间',
          width: 200,
          formatter: (row, col, cell) => (cell ? new Date(cell + 8 * 3600 * 1000).toJSON().substr(0, 19).replace('T', ' ') : ''),
        },
        // {
        //   index: 'payType',
        //   name: '支付方式',
        //   width: 200,
        //   formatter: (row, col, cell) => {
        //     if (cell === 3) {
        //       return '线下转账(电汇平台)'
        //     }
        //     if (cell === 4) {
        //       return '线下转账(电汇商业)'
        //     }
        //     return ''
        //   }
        // },
      ],
      hireMoneyTotal: '', // 佣金金额
      statementTotalMoneyTotal: '', // 应结算金额
      deductedCommissionTotal: '', // 应缴纳佣金合计
      actualCommissionMoneyTotal: '', // 实际需缴纳佣金合计
      commissionDiscountMoneyTotal: '', // 佣金优惠合计
      productActualMoneyTotal: '', // 商品实付金额合计
      changeExport: false,
    };
  },
  created() {
    if (this.type === 2) {
      const obj = {
        index: 'payType',
        name: '支付方式',
        width: 200,
        formatter: (row, col, cell) => {
          if (cell === 3) {
            return '线下转账(电汇平台)';
          }
          if (cell === 4) {
            return '线下转账(电汇商业)';
          }
          return '';
        }
      };
      this.col.push(obj);
    }
    this.getList(this.listQuery);
  },
  methods: {
    getList(listQuery, reset) {
      const loading = this.$loading({
        lock: true,
        text: 'Loading',
        spinner: 'el-icon-loading',
        background: 'rgba(255,255,255, 0.8)'
      })
      this.getSettlePrice()
      const { page, pageSize } = listQuery
      const {
        businessNo,
        businessType,
        orderSettlementStatus,
        settledTime,
        settlementType,
        payType,
        merchantName,
        paymentTime
      } = this.listQuery
      if (this.type === 1) {
        getOnlineSettleDatas({
          pageNum: reset ? 1 : page,
          pageSize,
          businessNo,
          businessType,
          orderSettlementStatus,
          settlementType,
          payType,
          merchantName,
          startOrderPayTime: paymentTime && paymentTime.length ? this.formatDate(paymentTime[0].getTime()) : '',
          endOrderPayTime: paymentTime && paymentTime.length ? this.formatDate(paymentTime[1].getTime()) : '',
          startOrderSettlementTime:
            settledTime && settledTime.length ? this.formatDate(settledTime[0].getTime()) : '',
          endOrderSettlementTime:
            settledTime && settledTime.length ? this.formatDate(settledTime[1].getTime()) : ''
        })
          .then((res) => {
            loading.close();
            if (res.code === '200') {
              const { list, total, pageNum } = res.data;
              this.list = list;
              this.listQuery = {
                ...this.listQuery,
                total,
                page: pageNum,
              };
            } else {
              this.$message.error({ message: res.errorMsg || res.msg, customClass: 'center-msg' });
            }
          })
          .catch(() => {
            loading.close();
          });
      } else if (this.type === 2) {
        getOfflineSettleDatas({
          pageNum: reset ? 1 : page,
          pageSize,
          businessNo,
          businessType,
          orderSettlementStatus,
          settlementType,
          payType,
          merchantName,
          startOrderPayTime: paymentTime && paymentTime.length ? this.formatDate(paymentTime[0].getTime()) : '',
          endOrderPayTime: paymentTime && paymentTime.length ? this.formatDate(paymentTime[1].getTime()) : '',
          startOrderSettlementTime:
            settledTime && settledTime.length ? this.formatDate(settledTime[0].getTime()) : '',
          endOrderSettlementTime:
            settledTime && settledTime.length ? this.formatDate(settledTime[1].getTime()) : '',
        })
          .then((res) => {
            loading.close();
            if (res.code === '200') {
              const { list, total, pageNum } = res.data;
              this.list = list;
              this.listQuery = {
                ...this.listQuery,
                total,
                page: pageNum,
              };
            } else {
              this.$message.error({ message: res.errorMsg || res.msg, customClass: 'center-msg' });
            }
          })
          .catch(() => {
            loading.close();
          });
      }
    },
    /**
     * 用于父组件调用
     */
    reload() {
      this.getList(this.listQuery, true);
    },
    /**
     * 导出账单数据
     */
    exportList() {
      const {
        businessNo,
        businessType,
        orderSettlementStatus,
        settledTime,
        settlementType,
        payType,
        merchantName,
        paymentTime
      } = this.listQuery
      const startOrderSettlementTime =
        settledTime && settledTime.length ? this.formatDate(settledTime[0].getTime()) : ''
      const endOrderSettlementTime =
        settledTime && settledTime.length ? this.formatDate(settledTime[1].getTime()) : ''
      const params = {
        businessNo,
        businessType,
        orderSettlementStatus,
        startOrderSettlementTime,
        endOrderSettlementTime,
        settlementType,
        payType,
        merchantName,
        startOrderPayTime: paymentTime && paymentTime.length ? this.formatDate(paymentTime[0].getTime()) : '',
        endOrderPayTime: paymentTime && paymentTime.length ? this.formatDate(paymentTime[1].getTime()) : ''
      }
      if (this.type === 1) {
        getOnlineSettleDataNums(params)
          .then((res) => {
            if (res.code === '200') {
              if (res.data === 0) {
                this.$message.error({ message: '暂无结算单数据', customClass: 'center-msg' });
              } else if (res.data > 5000) {
                this.$message.error({
                  message: `导出上限为5000条，当前搜索结果导出数据为${res.data}条，超出导出上限`,
                  customClass: 'center-msg',
                });
              } else {
                this.exportData();
              }
            } else {
              this.$message.error({ message: res.errorMsg || res.msg, customClass: 'center-msg' });
            }
          })
          .catch(() => {});
      } else if (this.type === 2) {
        getOfflineSettleDataNums(params)
          .then((res) => {
            if (res.code === '200') {
              if (res.data === 0) {
                this.$message.error({ message: '暂无结算单数据', customClass: 'center-msg' });
              } else if (res.data > 5000) {
                this.$message.error({
                  message: `导出上限为5000条，当前搜索结果导出数据为${res.data}条，超出导出上限`,
                  customClass: 'center-msg',
                });
              } else {
                this.exportData();
              }
            } else {
              this.$message.error({ message: res.errorMsg || res.msg, customClass: 'center-msg' });
            }
          })
          .catch(() => {});
      }
    },
    /**
     * 导出数据
     */
    exportData(link) {
      const {
        businessNo,
        businessType,
        orderSettlementStatus,
        settledTime,
        settlementType,
        payType,
        merchantName,
        paymentTime
      } = this.listQuery
      const startOrderSettlementTime =
        settledTime && settledTime.length ? this.formatDate(settledTime[0].getTime()) : ''
      const endOrderSettlementTime =
        settledTime && settledTime.length ? this.formatDate(settledTime[1].getTime()) : ''
      const params = {
        businessNo,
        businessType,
        orderSettlementStatus,
        startOrderSettlementTime,
        endOrderSettlementTime,
        settlementType,
        payType,
        merchantName,
        startOrderPayTime: paymentTime && paymentTime.length ? this.formatDate(paymentTime[0].getTime()) : '',
        endOrderPayTime: paymentTime && paymentTime.length ? this.formatDate(paymentTime[1].getTime()) : '',
      }
      // const url = `${process.env.VUE_APP_BASE_API}${link}${this.getParams(params)}`
      // const a = document.createElement('a')
      // a.href = url
      // a.click()
      if(this.type === 1){
        exportOnlinePaySettleList(params).then((res) => {
          if (res.code !== 0) {
            this.$message.error(res.message);
            return;
          }
          this.changeExport = true;
        });

      }else if (this.type === 2){
        exportOfflineSettlementList(params).then((res) => {
          if (res.code !== 0) {
            this.$message.error(res.message);
            return;
          }
          this.changeExport = true;
        });
      }
    },
    handleChangeExport(info) {
      this.changeExport = false;
      if (info === 'go') {
        const path = '/downloadList'
        window.openTab(path)
        //that.$router.push({ path: '/downloadList' });
      }
    },
    handleExoprClose() {
      this.changeExport = false;
    },
    /**
     * 获取get请求参数
     */
    getParams(params) {
      let queryStr = '?';
      Object.keys(params).forEach((key) => {
        queryStr += `${key}=${params[key]}&`;
      });
      queryStr = queryStr.substr(0, queryStr.length - 1);
      return queryStr;
    },
    /**
     * 重置数据
     */
    reset() {
      this.$refs.listQuery.resetFields();
      this.getList(this.listQuery, true);
    },
    /**
     * 获取结算单价格
     */
    getSettlePrice() {
      const {
        businessNo,
        businessType,
        orderSettlementStatus,
        settledTime,
        settlementType,
        payType,
        merchantName,
        paymentTime
      } = this.listQuery
      const startOrderSettlementTime =
        settledTime && settledTime.length ? this.formatDate(settledTime[0].getTime()) : ''
      const endOrderSettlementTime =
        settledTime && settledTime.length ? this.formatDate(settledTime[1].getTime()) : ''
      const params = {
        businessNo,
        businessType,
        orderSettlementStatus,
        startOrderSettlementTime,
        endOrderSettlementTime,
        settlementType,
        payType,
        merchantName,
        startOrderPayTime: paymentTime && paymentTime.length ? this.formatDate(paymentTime[0].getTime()) : '',
        endOrderPayTime: paymentTime && paymentTime.length ? this.formatDate(paymentTime[1].getTime()) : '',
      }
      if (this.type === 1) {
        getOnlineSettleInfo(params)
          .then((res) => {
            if (res.code === '200') {
              this.hireMoneyTotal = res.data.hireMoneyTotal;
              this.statementTotalMoneyTotal = res.data.statementTotalMoneyTotal;
              this.deductedCommissionTotal = res.data.deductedCommissionTotal;
              this.actualCommissionMoneyTotal = res.data.actualCommissionMoneyTotal;
              this.commissionDiscountMoneyTotal = res.data.commissionDiscountMoneyTotal;
              this.productActualMoneyTotal = res.data.productActualMoneyTotal;
            } else {
              this.$message.error({ message: res.errorMsg || res.msg, customClass: 'center-msg' });
            }
          })
          .catch(() => {});
      } else if (this.type === 2) {
        getOfflineSettleInfo(params)
          .then((res) => {
            if (res.code === '200') {
              this.hireMoneyTotal = res.data.hireMoneyTotal;
              this.statementTotalMoneyTotal = res.data.statementTotalMoneyTotal;
              this.deductedCommissionTotal = res.data.deductedCommissionTotal;
              this.actualCommissionMoneyTotal = res.data.actualCommissionMoneyTotal;
              this.commissionDiscountMoneyTotal = res.data.commissionDiscountMoneyTotal;
              this.productActualMoneyTotal = res.data.productActualMoneyTotal;
            } else {
              this.$message.error({ message: res.errorMsg || res.msg, customClass: 'center-msg' });
            }
          })
          .catch(() => {});
      }
    },
    /**
     * 格式化日期
     */
    formatDate(date) {
      return new Date(date + 8 * 3600 * 1000).toJSON().substr(0, 19).replace('T', ' ');
    },
  },
};
</script>

<style>
  .el-tooltip__popper {
    white-space: pre-line;
  }
</style>
<style lang="scss" scoped>
.settlement-list {
  .el-form {
    overflow: hidden;
    .el-form-item {
      margin-bottom: 10px;
      ::v-deep  .el-form-item__label {
        height: 30px;
        line-height: 30px;
      }
      ::v-deep  .el-form-item__content {
        height: 30px;
        line-height: 30px;
        .el-input__inner {
          height: 30px;
          line-height: 30px;
        }
        .el-date-editor {
          width: 360px;
          .el-input__icon,
          .el-range-separator {
            line-height: 22px;
          }
        }
        .el-input {
          width: 330px;
        }
        .el-select {
          width: 330px;
          .el-input__icon {
            line-height: 30px;
          }
        }
        .el-button {
          padding: 0 15px;
          line-height: 30px;
          height: 30px;
        }
      }
    }
  }
  .btn-item {
    float: right;
  }
  .price-box {
    font-size: 16px;
    font-family: PingFangSC, PingFangSC-Semibold;
    font-weight: 600;
    color: #303133;
    line-height: 40px;
    overflow: hidden;
    &.mb15 {
      margin-bottom: 15px;
    }
    span {
      font-size: 28px;
    }
    .el-button {
      padding: 0 12px;
      line-height: 30px;
      &.is-plain {
        color: #4183d5;
        border-color: #4183d5;
      }
    }
  }
}
.searchMy ::v-deep  .el-form-item__label {
  margin-left: 20px;
  padding: 0;
}
.searchMy ::v-deep  .el-input__inner {
  border-radius: 0 4px 4px 0;
}
.searchMy ::v-deep  .el-date-editor{
  width: 100%;
}
.search-title {
  display: table-cell;
  padding: 0 10px;
  text-align: center;
  border: 1px solid #dcdfe6;
  height: 30px;
  line-height: 30px;
  vertical-align: middle;
  border-right: none;
  border-radius: 4px 0 0 4px;
  color: #333333;
  white-space: nowrap;
}
.searchMy ::v-deep  .el-select {
  display: table-cell;
  width: 100%;
}
.searchMy ::v-deep  .el-form-item__content{
  width: 100%;
}
.searchMy ::v-deep  .el-form-item--small.el-form-item{
  margin-bottom: 10px;
  width: 32%;
}
.searchMy ::v-deep  .el-form-item--small .el-form-item__content{
  line-height: 30px;
  width: 100%;
}
.searchMy ::v-deep  .el-input-group__prepend {
  background: none;
  color: #333333;
  padding: 0 10px;
}
.Fsearch {
  padding: 0px 20px 10px;
  font-weight: 500;
  .searchMsg {
    font-weight: 700;
    width: 200px;
  }
  .sign {
    display: inline-table;
    width: 3px;
    height: 13px;
    background: linear-gradient(360deg, #1d69c4 1%, #8bbdfc 99%);
    border-radius: 2px;
    margin-right: 8px;
  }
}
</style>
