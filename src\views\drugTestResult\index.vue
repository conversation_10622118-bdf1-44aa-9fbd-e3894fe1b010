<template>
  <div v-loading="loading">
    <i-form v-model="formData" :isHomeEnter="homeEnter" @search="search" @asyncExport="asyncExport"></i-form>
    <div style="padding: 0 20px;">
      <el-row>
        <el-col :xs="16" :md="12" :lg="6" :xl="5">
          <p style="color:#4184d5">已上传药检报告数：{{ totalData.popReleaseCount }}</p>
        </el-col>
        <el-col :xs="16" :md="12" :lg="6" :xl="5">
          <p style="color:#4184d5">药检报告订单条目数：{{ totalData.popFirstSaleCount }}</p>
        </el-col>
        <el-col :xs="16" :md="12" :lg="6" :xl="5">
          <p style="color:#4184d5">药检报告订单覆盖率：{{ totalData.ratio }}</p>
        </el-col>
      </el-row>
      <i-table :tableData="tableData"></i-table>
      <el-pagination
				style="position:relative;left:100%;display:inline-block;transform:translateX(-100%)"
				@size-change="handleSizeChange"
				@current-change="handleCurrentChange"
				:current-page="pagination.pageNum"
				:page-sizes="[20, 50, 100]"
				:page-size="pagination.pageSize"
				background
				layout="total, sizes, prev, pager, next, jumper"
				:total="pagination.total">
			</el-pagination>
      <exportTips :change-export="changeExport" @handleExoprClose="changeExport = false" @handleChangeExport="handleChangeExport"></exportTips>
    </div>
  </div>
</template>
<script>
import iForm from './form.vue'
import iTable from './table.vue'
import { drugReportQuery, drugReportStatistics, drugReportExport } from '../../api/qualificationOnline/index'
import exportTips from '../../views/other/components/exportTip.vue'
export default {
  name: 'drugTestResultManage',
  components: {
    iForm,
    iTable,
    exportTips
  },
  data() {
    return {
      formData: {},
      other: {},
      pagination: {
        pageNum: 1,
        pageSize: 20,
        total: 0
      },
      tableData: [],
      totalData: {
        popFirstSaleCount: '',
        popReleaseCount: '',
        ratio: ''
      },
      loading: false,
      changeExport: false,
      homeEnter: 0,
    }
  },
  activated() {
    this.homeEnter = this.$route.query.homeEnter || 0;
    if (Object.keys(this.formData).length > 0) {
      this.$nextTick(() => {
        if (!this.homeEnter) {
          this.search()
        }
      })
    }
  },
  mounted() {
    this.homeEnter = this.$route.query.homeEnter || 0;
    this.$nextTick(() => {
      if (!this.homeEnter) {
        this.search()
      }
    })
  },
  methods: {
    /**
     *
     * @param { {key: string, value:any}[] } keyValueMap
     */
    search(keyValueMap) {
      const map = keyValueMap ? keyValueMap : []
      const form = {
        ...this.formData,
        ...this.other,
        query: 1,
        pageNum: this.pagination.pageNum,
        pageSize: this.pagination.pageSize
      }
      map.forEach(item => {
        form[item.key] = item.value;
      })
      console.log(form);
      this.loading = true;
      drugReportQuery(form).then(res => {
        if (res.code === 0) {
          this.tableData = res.result.list;
          this.pagination.total = res.result.total;
        } else {
          this.$message.error(res.msg)
        }
        /* this.totalData = res.data.totalData; */
      }).finally(() => {
        this.loading = false;
      })
      drugReportStatistics().then(res => {
        if (res.code === 0) {
          this.totalData = res.result;
        }
      })
    },
    handleCurrentChange(pageNum) {
			this.pagination.pageNum = pageNum;
			this.search();
		},
		handleSizeChange(pageSize) {
			this.pagination.pageSize = pageSize;
			this.search();
		},
    asyncExport() {
      const form = {
        ...this.formData,
        ...this.other,
        pageNum: this.pagination.pageNum,
        pageSize: this.pagination.pageSize
      }
      this.loading = true
      drugReportExport(form).then(res => {
        if (res.code !== 0) {
					this.$message.warning(res.msg);
					return;
				}
        this.changeExport = true;
      }).finally(() => {
        this.loading = false;
      })
    },
    handleChangeExport(info) {
			this.changeExport = false;
			if (info === 'go') {
				const path = '/downloadList';
				window.openTab(path);
				// that.$router.push({ path: '/downloadList' });
			}
		},
  }
}
</script>
<style scoped>
</style>
