<template>
	<div style="padding:7px 10px">
		<div class="header-flex">
			<div class="header-title" :class="shouHeightLine ? 'height-line' : ''" style="font-weight:600;">
				<slot name="title">{{ title }}</slot>
				<span v-if="showFold" class="bar" style="margin-left:10px;" @click="change">{{ bar.msg }}</span>
			</div>
			<div class="header-right">
				<slot name="header-right"></slot>
			</div>
		</div>
		<div class="content" :style="{ height: `${bar.height}px`, paddingLeft:shouHeightLine ? '17px' : '' }">
			<div ref="content" style="padding:5px 0;">
				<slot></slot>
			</div>
		</div>
		<div class="bottom">
			<slot name="bottom"></slot>
		</div>
	</div>
</template>

<script>
export default {
	props: {
		title: {
			type: String,
			default: ''
		},
		showFold: {
			type: Boolean,
			default: true
		},
		shouHeightLine: {
			type: Boolean,
			default: true
		}
	},
	data() {
		return {
			bar: {
				height: "auto",
				msg: "收起",
				status: false
			},

		}
	},
	mounted() {
		this.init();
		this.bar.height = this.$refs.content.clientHeight
		/* this.$refs.content.addEventListener('resize', () => {
			this.contentHeight = this.$refs.content.clientHeight
			this.bar.height = this.contentHeight;
		}) */
		window.addEventListener('resize', () => {
			if (!this.bar.status && this.$refs.content && this.$refs.content.clientHeight) {
				this.bar.height = this.$refs.content.clientHeight;
			}
		})
	},
	methods: {
		change() {
			if (this.bar.status) {
				this.bar.status = false;
				this.bar.msg = "收起";
				this.bar.height = this.$refs.content.clientHeight;
			} else {
				this.bar.status = true;
				this.bar.msg = "展开";
				this.bar.height = 0;
			}
		},
		init() {
			const resizeObserver = new ResizeObserver((entries) => {
				if (!this.bar.status && this.$refs.content && this.$refs.content.clientHeight) {
					this.bar.height = this.$refs.content.clientHeight;
				}
			});
			resizeObserver.observe(this.$refs.content)
		}
	}
}
</script>

<style lang="scss" scoped>
.header-flex {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 10px;
	.header-title {
		font-size: 16px;
	}
	.height-line {
		padding-left: 10px;
		border-left: solid 3px #4183d5;
	}
	.bar {
		font-size:12px;
		cursor:pointer;
		color:#4183d5;
		user-select: none;
	}
}
.content {
	padding-left: 13px;
	overflow: hidden;
	transition: all 0.3s;
}
</style>
