<template>
  <div class="choose">
    <el-dialog title="匹配结果" :visible.sync="dialogFormVisible" width="80%">
      <div class="choose-info">
        <div class="padding-box">
          <div class="goods-detail">
            <el-row>
              <el-col :span="6">
                通用名称：{{ sendSku.commonName ? sendSku.commonName : '-' }}
              </el-col>
              <el-col :span="6">
                规格：{{ sendSku.spec ? sendSku.spec : '-' }}
              </el-col>
              <el-col :span="6">
                批准文号：{{ sendSku.approvalNumber ? sendSku.approvalNumber : '-' }}
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="6">
                生产厂家：{{ sendSku.manufacturer ? sendSku.manufacturer : '-' }}
              </el-col>
              <el-col :span="6">
                产地：{{ sendSku.producer ? sendSku.producer : '-' }}
              </el-col>
              <el-col :span="6">
                条码：{{ sendSku.code ? sendSku.code : '-' }}
              </el-col>
              <el-col :span="6">
                品牌：{{ sendSku.brand ? sendSku.brand : '-' }}
              </el-col>
            </el-row>

          </div>
          <p class="goods-tip">*以下列表中，请选择商品信息一致的商品(规格数字一样描述不一样的，可视为一致)，如果无匹配的商品，请从新增上架途径单独上架</p>
        </div>
        <ul class="goods-ul">
          <li
            v-for="(item, index) in goodsDetail"
            :key="index"
            :class="[
              index === chooseIndex ? 'goods-li choose-li' : 'goods-li',
              item.result === 1 ? item.businessDisableStatus == 0 ? 'disabled-li' : '' : 'disabled-li'
            ]"
            @click="chooseList(item, index)"
          >
            <div class="content-box">
              <div class="img-box">
                <!-- @click.stop="lookImg(item.imageUrl)" -->
                <!-- <img :src="item.imageUrl" alt /> -->
                <el-image :src="item.imageUrl" :preview-src-list="[item.imageUrl]" @click.prevent>
                  <div slot="error" class="image-slot">
                    <el-image src="https://oss-ec.ybm100.com/ybm/product/defaultPhoto.jpg" />
                  </div>
                </el-image>
                <span v-if="item.disableType" class="disableType">{{item.disableType!= 0&& '已停用'}}
                  <el-tooltip
                  placement="top-start"
                  :content="item.disableTypeName + ':' + item.disableNote "
                  >
                    <span style="color:#4183d5">详情：</span>
                  </el-tooltip>
                </span>
              </div>
              <div class="con-box">
                <h4 v-if="item.showName" :style="{ color: item.needRedKey.includes('showName') ? 'red' : '' }">{{item.brand}} {{ item.showName }}</h4>
                <p v-if="item.standardProductId" >标品ID：{{ item.standardProductId }}</p>
                <p v-if="item.spec" :style="{ color: item.needRedKey.includes('spec') ? 'red' : '' }">规格：{{ item.spec }}</p>
                <el-tooltip
                v-if="item.manufacturer"
                class="item"
                effect="dark"
                :content="item.manufacturer"
                placement="top-start"
              >
                <p>
                  <span>生产厂家：{{ item.manufacturer }}</span>
                </p>
              </el-tooltip>
              <el-tooltip
                v-if="item.entrustedManufacturer && [1,3,4].includes(item.spuCategory)"
                class="item"
                effect="dark"
                :content="item.entrustedManufacturer"
                placement="top-start"
              >
                <p>
                  <span>受托生产厂家：{{ item.entrustedManufacturer }}</span>
                </p>
              </el-tooltip>
                <el-tooltip
                  v-if="item.originPlace && item.spuCategory == 2"
                  class="item"
                  :style="{ color: item.needRedKey.includes('originPlace') ? 'red' : '' }"
                  effect="dark"
                  :content="item.originPlace"
                  placement="top-start"
                >
                  <p>
                    <span>产地：{{ item.originPlace }}</span>
                  </p>
                </el-tooltip>
                <el-tooltip
                  v-if="item.approvalNumber && [1,4].includes(item.spuCategory)"
                  class="item"
                  effect="dark"
                  :style="{ color: item.needRedKey.includes('approvalNumber') ? 'red' : '' }"
                  :content="item.approvalNumber"
                  placement="top-start"
                >
                  <p>批准文号：{{ item.approvalNumber }}</p>
                </el-tooltip>
                <el-tooltip
                  v-if="item.qualityStandard && 2 == item.spuCategory"
                  class="item"
                  :style="{ color: item.needRedKey.includes('qualityStandard') ? 'red' : '' }"
                  effect="dark"
                  :content="item.qualityStandard"
                  placement="top-start"
                >
                  <p>质量标准：{{ item.qualityStandard }}</p>
                </el-tooltip>
                <el-tooltip
                  v-if="item.approvalNumber && 3 == item.spuCategory"
                  class="item"
                  effect="dark"
                  :content="item.approvalNumber"
                  :style="{ color: item.needRedKey.includes('approvalNumber') ? 'red' : '' }"
                  placement="top-start"
                >
                  <p>注册证号/备案凭证号：{{ item.approvalNumber }}</p>
                </el-tooltip>
                <p v-if="item.productUnit" :style="{ color: item.needRedKey.includes('productUnit') ? 'red' : '' }">包装单位：{{ item.productUnit }}</p>
                <p v-if="item.code" :style="{ color: item.needRedKey.includes('code') ? 'red' : '' }">条码：{{ item.code }}</p>
                <p v-if="item.businessScopeMultiName" :style="{ color: item.needRedKey.includes('businessScopeMultiName') ? 'red' : '' }">经营范围：{{ item.businessScopeMultiName }}</p>
                <!-- <p v-if="item.drugClassification">处方类型：{{ item.drugClassification }}</p>
                <p v-if="item.storageCondition">存储条件：{{ item.storageCondition }}</p>
                <p v-if="item.term">有效期：{{ item.term }}</p>
                <p v-if="item.size">码数：{{ item.size }}</p>
                <p v-if="item.colour">颜色：{{ item.colour }}</p>
                <p v-if="item.flavor">口味：{{ item.flavor }}</p> -->
              </div>
            </div>
            <p class="tip-box">
              <i class="el-icon-check" />
            </p>
            <p class="text-tip">
              {{
              item.result === 2
              ? '商品经营范围不在企业经营范围内'
              : item.result === 5
              ? '暂无销售权限'
              : item.businessDisableStatus == 0
              ? '暂无销售权限'
              : ''
              }}
            </p>
          </li>
        </ul>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button size="small" @click="dialogFormVisible = false">取 消</el-button>
        <el-button type="primary" size="small" @click="sendData">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getMathList, sendMathReSet } from '../../../api/product';
import { diffRed } from '../config'
export default {
  name: 'ChooseGoodsList',
  data() {
    return {
      dialogFormVisible: false,
      goodsDetail: [],
      chooseId: '',
      chooseIndex: '',
      sendIds: '',
      sendSkuId: '',
      sendRes: '',
      goods: {},
      sendSku: {},
    };
  },
  methods: {
    getChooseData(row, chooseId) {
      if (chooseId) {
        this.chooseId = chooseId;
      }
      if (row) {
        this.sendSku = row;
      }
      this.sendIds = row.id;
      this.sendSkuId = '';
      this.chooseIndex = '';
      getMathList({ id: row.id })
        .then((res) => {
          if (res.code === 0) {
            this.dialogFormVisible = true;
            this.goodsDetail = res.data || [];
            if (this.goodsDetail) {
              this.goodsDetail.forEach((item, index) => {
                if (row.standardProductId == item.standardProductId && !row.matchedStandardIds.length > 0) {
                  this.sendSkuId = item.standardProductId;
                  this.chooseIndex = index;
                  this.goods = item;
                }
              });
              this.goodsDetail = diffRed(this.goodsDetail);
              console.log(this.goodsDetail);
            }
          } else {
            // this.$message.error(res.message)
            this.$message({
              type: 'error',
              message: res.message,
              offset: 60,
            });
            this.goodsDetail = [];
          }
        })
        .catch(() => {
          this.goodsDetail = [];
        });
    },
    chooseList(data, index) {
      if (data.businessDisableStatus == 0) return console.log("data.businessDisableStatus", data.businessDisableStatus);
      if (data.result === 1) {
        this.sendSkuId = data.standardProductId;
        this.chooseIndex = index;
        this.goods = data;
      }
    },
    sendData() {
      if (this.sendIds && this.sendSkuId) {
        sendMathReSet({ id: this.sendIds, standardProductId: Number(this.sendSkuId) }).then(
          (res) => {
            if (res.code === 0) {
              // this.$message.success('选取成功')
              this.$message({
                type: 'success',
                message: '选取成功',
                offset: 60,
              });
              this.dialogFormVisible = false;
              this.$emit('resetList');
            } else {
              // this.$message.error(res.message)
              this.$message({
                type: 'error',
                message: res.message,
                offset: 60,
              });
            }
          },
        );
      } else {
        this.$message({
          type: 'warning',
          message: '请选择商品',
          offset: 60,
        });
      }
    },
  },
};
</script>

<style scoped lang="scss">
.choose {
  ::v-deep  .el-button--primary {
    background: #4183d5;
  }
  ::v-deep  .el-dialog__body {
    padding: 0 0 10px;
    height: 520px;
    overflow-y: scroll;
  }
  ::v-deep  .el-dialog__header {
    padding: 10px 16px;
    background: #f9f9f9;
  }
  ::v-deep  .el-dialog__headerbtn {
    top: 13px;
  }
  ul,
  li {
    list-style: none;
    margin: 0;
  }
  // .choose-info {
  //   max-height: 430px;
  //   overflow-y: scroll;
  // }
  .goods-detail {
    background: #fafafa;
    padding: 15px;
    margin-top: 10px;
    h3,
    p {
      padding: 0;
      margin: 0;
    }
    p {
      padding-top: 5px;
      font-size: 12px;
      color: #666666;
      span {
        margin-right: 10px;
      }
    }
  }
  .goods-tip {
    color: #ff8e00;
    font-size: 12px;
    padding: 8px 0;
    margin: 0;
  }
  .padding-box {
    padding: 0 15px;
  }
  .goods-ul {
    position: relative;
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 10px;
    .goods-li {
      overflow: hidden;
      position: relative;
      background: #f9f9f9;
      border: solid 2px transparent;
      cursor: pointer;
      border-radius: 10px;
      .content-box {
        display: flex;
        gap: 10px;
        padding: 5px;
        height: 100%;
        .img-box {
          padding: 10px 0;
          flex-grow: 0;
          width: 25%;
          overflow: hidden;
          border-radius: 4px;
          height: max-content;
          text-align: center;
          background: #ffffff;

          ::v-deep   .el-image {
            width: 100%;
          }
          .disableType {
          // bottom: 0;
          font-size: 12px;
          color: red;
        }
        }
        .con-box {
          flex-grow: 0;
          width: 75%;
          h4,
          p {
            margin: 0;
            padding: 0;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }
          p {
            padding-top: 5px;
            font-size: 12px;
            color: #666666;
          }
        }
      }
      .tip-box {
        display: none;
        width: 20px;
        height: 20px;
        background: #4183d5;
        border-radius: 50%;
        color: #ffffff;
        position: absolute;
        top: 5px;
        right: 10px;
        margin: 0;
        text-align: center;
      }
      .text-tip {
        display: none;
        width: 100%;
        height: 24px;
        background: #eeeeee;
        font-size: 12px;
        color: #666666;
        text-align: center;
        position: absolute;
        top: 0;
        left: 0;
        margin: 0;
        line-height: 24px;
      }
    }
    li.choose-li {
      border: solid 2px #4183d5;
      background-color: #edf8ff;
      .tip-box {
        display: block;
      }
    }
    li.none-right {
      margin-right: 0;
    }
    li.disabled-li {
      .text-tip {
        display: block;
      }
    }
  }
}
</style>
