<template>
  <el-dialog title="申请提现" :visible.sync="dialogVisible" width="30%" :before-close="handleClose">
    <div class="withdrawAmount">
      <p style="margin: 5px 0; font-size: 18px; font-weight: 600">提现金额:</p>
      <p style="margin: 5px 0; color: #ff2121; margin-left: 20px; font-size: 20px">
        {{ withdrawInfo.applyAmount }}
      </p>
    </div>
    <div style="font-size: 12px; padding: 0; margin-bottom: 5px; padding-top: 10px">
      <!-- *提交成功后，系统会自动按照账单类型拆分成两条提现记录，不同账单类型打款到账时间略有差异，请及时关注
      <p style="margin: 0;">
        在线支付提现金额：<span style="color: #ff2121;">{{ withdrawInfo.onlineCanCashAmount }}</span>
      </p>
      <p style="margin: 0;">
        线下转账（电汇平台+电汇商业）提现金额：<span style="color: #ff2121;">{{ withdrawInfo.offlineCanCashAmount }}</span>
      </p> -->
      <div
        style="padding: 10px 0; border-width: 2px 0; border-color: #e3e3e3; border-style: dashed"
      >
        <p style="margin: 5px 0; display: flex; justify-content: space-between">
          <span style="font-size: 14px"> 在线支付(平安支付)： </span>
          <span style="color: #ff2121">{{ withdrawInfo.onlineCanCashAmount }}元</span>
        </p>
        <p style="margin: 5px 0; display: flex; justify-content: space-between">
          <span style="font-size: 14px">在线支付(直连支付)：</span>
          <span style="color: #ff2121">{{ withdrawInfo.directOnlineCanCashAmount }}元</span>
        </p>
        <p style="margin: 5px 0; display: flex; justify-content: space-between">
          <span style="font-size: 14px">线下转账（电汇平台+电汇商业）：</span>
          <span style="color: #ff2121">{{ withdrawInfo.offlineCanCashAmount }}元</span>
        </p>
      </div>
    </div>
    <!-- <el-form
      ref="withdrawInfo"
      :model="withdrawInfo"
      :rules="rules"
      label-width="80px"
      label-position="top"
    >
      <el-form-item label="账户名称：" prop="accountName">
        <el-input v-model.trim="withdrawInfo.accountName" disabled/>
      </el-form-item>
      <el-form-item label="银行卡号：" prop="accountNum">
        <el-input v-model="withdrawInfo.accountNum" placeholder="请输入企业收款账号" maxlength="30" disabled/>
      </el-form-item>
      <el-form-item label="开户行：" prop="accountBank">
        <el-input v-model.trim="withdrawInfo.accountBank" placeholder="请输入开户行及支行名称" maxlength="50" disabled/>
      </el-form-item>
    </el-form> -->
    <div v-if="withdrawInfo.tips">
      温馨提示：<br />
      <span
        v-for="(item, index) in withdrawInfo.tips.split('\n')"
        :key="index"
        style="display: block"
      >
        {{ item }}
      </span>
    </div>
    <div v-if="!withdrawInfo.limitTips">
      <span style="display: block; color:red;">提现提示：{{withdrawInfo.tipsStyle}}</span>
    </div>
    
    <div v-if="!withdrawInfo.limitTips" slot="footer">
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" @click="applyWithdraw">提交</el-button>
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: 'applyWithdrawalDialog',
  props: {
    applyWithdrawalDialogVisible: {
      type: Boolean,
      default: false
    },
    info: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      dialogVisible: true,
      rules: {
        accountNum: [
          {
            required: true,
            message: '请输入企业收款账号',
            trigger: ['blur', 'change']
          },
          { validator: this.validateNum, trigger: ['change', 'blur'] }
        ],
        accountBank: [
          {
            required: true,
            message: '请输入开户行及支行名称',
            trigger: ['blur', 'change']
          }
        ]
      },
      withdrawInfo: {
        applyAmount: '',
        accountName: '',
        accountNum: '',
        accountBank: ''
      }
    }
  },
  mounted() {
    console.log(this.info)

    this.withdrawInfo = { ...this.info }
  },
  methods: {
    handleClose() {
      this.$emit('update:applyWithdrawalDialogVisible', false)
    },
    applyWithdraw() {
      this.$parent.applyWithdrawFn(this.withdrawInfo)
    },
    validateNum(rules, val, cb) {
      if (!/^\d+$/.test(val)) {
        cb('请输入数字')
      } else {
        cb()
      }
    }
  }
}
</script>

<style scoped></style>
