<template>
  <div class="choose">
    <el-dialog
      title="添加随心拼黑名单商品"
      :visible.sync="dialogFormVisible"
      width="900px"
    >
      <div>
        <div
          class="chooseGood"
          style="padding: 15px 20px;padding-top: 0;"
        >
          <el-row :gutter="20">
            <el-col :span="6">
              <el-input v-model="listQuery.code" placeholder="SKU编码/商品编码" size="small">
                <template slot="prepend">编码查询</template>
              </el-input>
            </el-col>
            <el-col :span="6">
              <el-input v-model="listQuery.showName" placeholder="请输入内容" size="small">
                <template slot="prepend">商品名称</template>
              </el-input>
            </el-col>
            <el-col :span="6">
              <div style="display: flex">
                <span class="search-title">商品状态</span>
                <el-select
                  v-model.trim="listQuery.productStatus"
                  placeholder="请选择"
                  size="samll"
                >
                  <el-option label="全部" :value="0"/>
                  <el-option label="销售中" :value="1"/>
                  <el-option label="已售罄" :value="2"/>
                  <!-- <el-option label="特惠中" :value="3"/> -->
                  <el-option label="已下架" :value="4"/>
                  <!-- <el-option label="秒杀" :value="5"/> -->
                  <el-option label="待上架" :value="6"/>
                  <!-- <el-option label="已录入" :value="7"/>
                  <el-option label="待审核" :value="8"/>
                  <el-option label="审核未通过" :value="9"/>
                  <el-option label="活动结束" :value="10"/> -->
                </el-select>
              </div>
            </el-col>
            <el-col :span="6">
              <div style="display: flex">
                <span class="search-title">商品类型</span>
                <el-select
                  v-model.trim="listQuery.productType"
                  placeholder="请选择"
                  size="samll"
                >
                  <el-option label="全部" :value="0"/>
                  <el-option label="普通商品" :value="1"/>
                  <el-option label="拼团商品" :value="3"/>
				  <el-option label="批购包邮商品" :value="5"></el-option>
                </el-select>
              </div>
            </el-col>
          </el-row>
          <el-row style="padding-top: 10px;">
            <el-col style="text-align: right">
              <el-button size="small" @click="resetList">重置</el-button>
              <el-button type="primary" size="small" @click="getList">查询</el-button>
            </el-col>
          </el-row>
        </div>
        <div style="padding: 0 20px">
          <el-table
            ref="goodsTable"
            :data="goodsData"
            stripe
            border
            tooltip-effect="dark"
            style="width: 100%"
            @selection-change="handleSelectionChange"
          >
            <el-table-column type="selection" width="45" />
            <el-table-column label="SKU编号" prop="id" width="120"></el-table-column>
            <el-table-column label="商品编码" prop="barcode" width="160"></el-table-column>
            <el-table-column label="ERP编码" prop="productCode" width="140"></el-table-column>
            <el-table-column label="商品名称" prop="showName" width="120"></el-table-column>
            <el-table-column label="规格" prop="spec"></el-table-column>
            <el-table-column label="厂家" prop="manufacturer"></el-table-column>
            <el-table-column label="价格" width="160">
              <template slot-scope="scope">
                <p>单体采购价：{{ scope.row.fob }}</p>
                <p>连锁采购价：{{ scope.row.guidePrice }}</p>
              </template>
            </el-table-column>
            <el-table-column label="商品状态">
              <template slot-scope="scope">
                {{ scope.row.productStatus }}
              </template>
            </el-table-column>
            <el-table-column label="商品类型" width="160">
              <template slot-scope="scope">
                {{ scope.row.productType }}
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div class="page-box">
          <Pagination
            v-show="goodsListTotal > 0"
            :total="goodsListTotal"
            :page.sync="listQuery.pageNum"
            :limit.sync="listQuery.pageSize"
            @pagination="getList(1)"
          />
        </div>
      </div>
      <div
        slot="footer"
        class="dialog-footer"
      >
        <el-button
          size="small"
          @click="dialogFormVisible = false"
        >
          取 消
        </el-button>
        <el-button
          type="primary"
          size="small"
          @click="sendData"
        >
          确 定
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>

import { selectSku } from '@/api/market/freeGroup';
import Pagination from '../../../components/Pagination/index.vue';

export default {
  name: 'ChangeGoods',
  components: { Pagination },
  data() {
    return {
      dialogFormVisible: false,
      goodsData: [],
      goodsListTotal: 0,
      listQuery: {
        pageNum: 1,
        pageSize: 10,
        code: '',
        showName: '',
        productStatus: 0,
        productType: 0,
      },
    };
  },
  methods: {
    handleSelectionChange(list) {
      console.log(2323223, list);
    },
    getList(from) {
      const params = { ...this.listQuery };
      selectSku(params).then((res) => {
        // eslint-disable-next-line no-unused-expressions
        !from ? this.dialogFormVisible = true : '';
        if (res.code === 1000) {
          console.log('选择商品数据', res);
          const { data } = res;
          const { csuPageInfo } = data;
          if (csuPageInfo) {
            const { list, total } = csuPageInfo;
            this.goodsData = list;
            this.goodsListTotal = total;
          }
        } else {
          this.$message.error(res.msg);
        }
      });
    },
    sendData() {
      const _selectData = this.$refs.goodsTable.selection;
      console.log(_selectData);
      this.$emit('setChooseGoods', _selectData);
      this.dialogFormVisible = false;
    },
    resetList() {
      this.listQuery = {
        pageNum: 1,
        pageSize: 10,
        code: '',
        showName: '',
        productStatus: 0,
        productType: 0,
      };
      this.getList();
    },
  },
};
</script>

<style scoped lang="scss">
.chooseGood ::v-deep  .el-form-item__label {
    margin-left: 20px;
    padding: 0;
  }
  .chooseGood ::v-deep  .el-input__inner {
    border-radius: 0 4px 4px 0;
    height: 32px;
    line-height: 32px;
    font-size: 13px;
  }
  .chooseGood ::v-deep  .el-date-editor{
    width: 100%;
  }
  .chooseGood ::v-deep  .el-select {
    display: table-cell;
    width: 100%;
  }
  .chooseGood ::v-deep  .el-form-item__content{
    width: 100%;
  }
  .chooseGood ::v-deep  .el-form-item--small.el-form-item{
    margin-bottom: 10px;
    width: 360px;
  }
  .chooseGood ::v-deep  .el-form-item--small .el-form-item__content{
    line-height: 30px;
    width: 100%;
  }
  .chooseGood ::v-deep  .el-input-group__prepend {
    background: none;
    color: #333333;
    padding: 0 10px;
  }
  .chooseGood ::v-deep  .el-input__suffix {
    top: 5px;
  }
.search-title {
  display: table-cell;
  padding: 0 10px;
  text-align: center;
  border: 1px solid #dcdfe6;
  height: 32px;
  line-height: 32px;
  vertical-align: middle;
  border-right: none;
  border-radius: 4px 0 0 4px;
  color: #333333;
  white-space: nowrap;
  font-size: 13px;
}
 .btn-box {
  float: right;
  display: flex;
  align-items: center;
  .showMoreBtns {
    .showMore {
      color: #4184d5;
      font-size: 14px;
      float: right;
      padding: 5px 20px;
      cursor: pointer;
    }
  }
}
</style>
