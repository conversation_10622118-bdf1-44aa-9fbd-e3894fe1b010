<<script>
export default {
  props: {
    start: {
      type: Boolean,
      default: false
    },
    stepElList: {
      type: Array,
      default: () => []
    },
    step: {
      type: Number,
      default: 0
    }
  },
  watch: {
    start(val) {
      this.target = val;
      if (val) {
        this.startNow(0)
      }
    },
    step(val) {
      this.startNow(val - 1)
    }
  },
  computed: {
    startStyle() {
      //document.body.style.overflow = this.target ? 'hidden' : 'auto';
      return this.target ? {
        position: 'fixed',
        zIndex: 9999,
        width: '100vw',
        height: '100vh',
        top: 0,
        left: 0,
        background: 'rgba(0,0,0,.5)',
      } : { display: 'none' }
    }
  },
  data() {
    return {
      focusEl: {
        width: '',
        height: '',
        top: '',
        left: '',
      },
      controlEl: {
        top: '',
        left: '',
      },
      random: Math.random(),
      target: false,
    }
  },
  methods: {
      startNow(step) {
        if (!this.start || !this.stepElList[step]) return;
        const elPositionData = this.stepElList[step].ref.getBoundingClientRect();
        this.focusEl.width = elPositionData.width + 40 + 'px';
        this.focusEl.height = elPositionData.height + 20 + 'px';
        this.focusEl.top = elPositionData.top - 10 + 'px';
        this.focusEl.left = elPositionData.left - 20 + 'px';
        const timer = setTimeout(() => {
          this.controlMove(elPositionData);
          clearTimeout(timer);
        }, 0)
      },
      clear() {
        this.focusEl ={
          width: '',
          height: '',
          top: '',
          left: '',
        }
      },
      controlMove(elPositionData) {
        const { width, height, top, left } = elPositionData;
        const ran = this.$refs[this.random].getBoundingClientRect();
       //上下左右哪个地方差值最大就放哪
       //上 左 右 下
        const arr = [top * window.innerWidth,
          left * window.innerHeight,
          (window.innerWidth - width - left) * window.innerHeight,
          (window.innerHeight - height - top) * window.innerWidth
        ]
        let maxIndex = 0;
        arr.forEach((val, i) => {
          if (val > arr[maxIndex]) {
            maxIndex = i;
          }
        })
        console.log(maxIndex);

        if (maxIndex === 0) {
          this.controlEl.top = top - ran.height - 20 + 'px';
          this.controlEl.left =  window.innerWidth - width - left > left ? left - 20 : left + width - ran.width + 20;
          this.controlEl.left = this.controlEl.left + "px";
        } else if (maxIndex === 1) {
          this.controlEl.left = left - ran.width - 30 + 'px';
          this.controlEl.top = window.innerHeight - height - top > top ? top - 10 : top + height - ran.height + 10;
          this.controlEl.top = this.controlEl.top + "px";
        } else if (maxIndex === 2) {
          this.controlEl.left = left + width + 30 + 'px';
          this.controlEl.top = window.innerHeight - height - top > top ? top - 10 : top + height - ran.height + 10;
          this.controlEl.top = this.controlEl.top + "px";
        } else if (maxIndex === 3) {
          this.controlEl.top = top + height + 20 + 'px';
          this.controlEl.left =  window.innerWidth - width - left > left ? left - 20 : left + width - ran.width + 20;
          this.controlEl.left = this.controlEl.left + "px";
        }
      }
  }
}
</script>

<template>
  <div>
    <div>
      <slot></slot>
    </div>
    <div style="transition: all 0.3s;mix-blend-mode: hard-light;" :style="startStyle">
      <div class="l-focus-el" :style="focusEl">
      </div>
    </div>
    <div v-show="start" class="l-control" :ref="random" :style="controlEl">
        <div style="position: relative;margin-bottom: 10px 0;">
          <slot name="control" :item="stepElList[step - 1]">
            <p style="white-space: nowarp;font-weight: 600;">{{ stepElList[step - 1].title }}</p>
            <p>{{ stepElList[step - 1].content }}</p>
          </slot>
        </div>
        <div style="display: flex;justify-content: end;">
          <button class="l-btn" @click="$emit('end')">跳过</button>
          <button v-if="step > 1" class="l-btn" @click="$emit('next', step - 1)">上一步</button>
          <button v-if="step < stepElList.length" class="l-btn" @click="$emit('next', step + 1)">下一步</button>
          <button v-if="step === stepElList.length" class="l-btn" @click="$emit('end')">确定</button>
        </div>
      </div>
    <!-- <div :style="focusEl" style="z-index:10002;position: fixed;"></div> -->
  </div>
</template>

<style scoped>
p {
  margin: 5px 0;
}
.l-focus-el {
  position: fixed;
  background-color: white;
  border-radius: 5px;
  transition: all 0.3s;
  mix-blend-mode: color;
}
.l-control {
  position: fixed;
  background-color: white;
  border-radius: 5px;
  padding: 5px 10px;
  width: 100%;
  max-width: 300px;
  z-index: 10000;
  top: 100px;
  transition: all 0.3s;
}
.l-btn {
  white-space: nowrap;
}
.l-transition {
  width: 100%;
  max-width: max-content;
  transition: all 0.3s;
}
</style>
