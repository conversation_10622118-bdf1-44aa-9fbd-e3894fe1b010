<template>
  <div class="search-info">
    <span class="sign" />
    <span class="title_line" style="margin-right: 5px">{{ titleInfo }}</span>
    <el-tooltip class="item" :content="tooltip" placement="top">
      <i class="el-icon-warning-outline"></i>
    </el-tooltip>
    <div class="chartBox" :id="chartId"></div>
  </div>
</template>

<script>
import * as echarts from 'echarts';

export default {
  name: 'LineChart2',
  props: {
    titleInfo: {
      type: String,
      default: '标题'
    },
    chartId: {
      type: String,
      default: ''
    },
    tooltip: {
      type: String,
      default: ''
    },
    chartConfig: {
      type: Object,
      default: () => {
        return {};
      }
    }
  },
  data() {
    return {};
  },
  mounted() {
    this.initChart();
  },
  methods: {
    getChartId() {
      return this.chartId;
    },
    initChart() {
      const option = {
        tooltip: {
          trigger: 'axis',
          // formatter: (params) => {
          //   let str = '';
          //   if (params && Array.isArray(params)) {
          //     params.forEach(item => {
          //       console.log(item)
          //       str += item.seriesName + item.value + ' ';
          //     });
          //   }
          //   return str;
          // }
        },
        toolbox: {
          show: true,
          feature: {
            magicType: {            //动态类型切换
              show: true,           //是否显示该工具
              type: ['line', 'bar'], //启用的动态类型
              title: {
                line: '切换为折线图',
                bar: '切换为柱状图'
              },
              emphasis: {
                iconStyle: {
                  color: '#4184D5',
                  textFill: '#4184D5'
                }
              }
            },
            saveAsImage: {          //保存为图片
              show: true,            //是否显示该工具
              title: '保存',
              emphasis: {
                iconStyle: {
                  color: '#4184D5',
                  textFill: '#4184D5'
                }
              }
            }
          }
        },
        legend: this.chartConfig.legend,
        xAxis: [
          {
            type: 'category',
            // boundaryGap: false,
            data: this.chartConfig.abscissa,
            axisPointer: {
              type: 'shadow'
            }

          }
        ],
        yAxis: [
          {
            type: 'value',
            name: this.chartConfig.legend.data[0]
          },
          {
            type: 'value',
            name: this.chartConfig.legend.data[1]
          }
        ],
        series: [
          {
            color: '#4184D5',
            name: this.chartConfig.legend.data[0],
            type: 'line',
            symbol: 'circle',
            data: this.chartConfig.datum,
            markPoint: {
              data: [
                {
                  type: 'max',
                  name: 'Max'
                },
                {
                  type: 'min',
                  name: 'Min'
                }
              ]
            }
          },
          {
            name: this.chartConfig.legend.data[1],
            type: 'line',
            yAxisIndex: 1,
            data: this.chartConfig.datum2,
            itemStyle: {
              color: 'orange'
            },
            markPoint: {
              data: [
                {
                  type: 'max',
                  name: 'Max'
                },
                {
                  type: 'min',
                  name: 'Min'
                }
              ]
            }
          }
        ]
      };
      this.$nextTick(() => {
        const chart = echarts.init(document.getElementById(this.chartId));
        chart && chart.setOption(option);
      });
    }
  }
};
</script>

<style scoped lang="scss">
.search-info {
  padding: 10px 20px;
  font-weight: 500;

  .chartBox {
    width: 100%;
    height: 408px;
  }
}
</style>
