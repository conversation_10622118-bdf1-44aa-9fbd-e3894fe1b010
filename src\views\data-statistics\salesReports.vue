<template>
  <div class="main-box" >
    <el-form :model="form" ref="ruleForm" :rules="rules" :inline="true" size="small">
      <div class="search-info">
        <span class="sign" />
        <span class="searchMsg">统计方式</span>
        <el-form-item prop="statisticalPlacer">
          <el-radio-group
            v-model="form.statisticalPlacer"
            @change="getTemplateRow"
            style="margin-left:30px"
          >
            <span class="radio-style">
              <el-radio :label="1" :key="1" class="radio-info">按天统计</el-radio>
              <el-tooltip effect="dark" placement="top">
                <template #content>只展示查询截止时间前31天内的数据</template>
                <p class="span-tip">!</p>
              </el-tooltip>
            </span>
            <span class="radio-style">
              <el-radio :label="2" :key="2" class="radio-info">按周统计</el-radio>
              <el-tooltip effect="dark" placement="top">
                <template #content>只展示查询截止时间前6个自然月内的数据</template>
                <p class="span-tip">!</p>
              </el-tooltip>
            </span>
            <span class="radio-style">
              <el-radio :label="3" :key="3" class="radio-info">按月统计</el-radio>
              <el-tooltip effect="dark" placement="top">
                <template #content>只展示查询截止时间前12个自然月内的数据</template>
                <p class="span-tip">!</p>
              </el-tooltip>
            </span>
            <span class="radio-style">
              <el-radio :label="4" :key="4" class="radio-info-long">按地区统计</el-radio>
              <el-tooltip effect="dark" placement="top">
                <template #content>只展示查询截止时间前12个自然月内的数据</template>
                <p class="span-tip">!</p>
              </el-tooltip>
            </span>
          </el-radio-group>
        </el-form-item>
      </div>
      <div class="search-info">
        <span class="sign" />
        <span class="searchMsg">筛选条件</span>
        <el-form-item prop="provinceCodeList" style="margin-left:30px">
          <span class="search-title">省份</span>
          <el-select
            v-model.trim="form.provinceCodeList"
            placeholder="全部"
            clearable
            multiple
            collapse-tags
            @change="getProvince('getCity', $event)"
          >
            <el-option
              v-for="item in proviceList"
              :key="item.regionCode"
              :label="item.regionName"
              :value="item.regionCode"
            />
          </el-select>
        </el-form-item>
        <el-form-item prop="cityCodeList">
          <span class="search-title">城市</span>
          <el-select
            v-model.trim="form.cityCodeList"
            placeholder="全部"
            clearable
            multiple
            collapse-tags
            @change="getProvince('getArea', $event)"
          >
            <el-option
              v-for="item in cityList"
              :key="item.regionCode"
              :label="item.regionName"
              :value="item.regionCode"
            />
          </el-select>
        </el-form-item>
        <el-form-item prop="areaCodeList">
          <span class="search-title">区县</span>
          <el-select
            v-model.trim="form.areaCodeList"
            placeholder="全部"
            clearable
            multiple
            collapse-tags
          >
            <el-option
              v-for="item in areaList"
              :key="item.regionCode"
              :label="item.regionName"
              :value="item.regionCode"
            />
          </el-select>
        </el-form-item>
        <el-form-item prop="opTime">
          <span class="search-title">时间</span>
          <div style="display: table-cell; line-height: 24px">
            <el-date-picker
              :picker-options="pickerOptions"
              v-model.trim="form.opTime"
              popper-class="install-contr-cell-class"
              range-separator="至"
              size="small"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              placeholder="选择日期时间"
              type="daterange"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              prefix-icon="el-icon-date"
              class="timeSel"
              @focus="dateTimeFocus()"
            />
          </div>
        </el-form-item>
        <el-row>
          <el-button v-permission="['dataStatistics_salesReport_export']" class="btn-info" size="small" @click="exportExcel">导出</el-button>
          <el-button class="search-btn" size="small" type="primary" @click="btnSearchHandler">查询</el-button>
          <el-button class="search-btn" size="small" @click="resetForm('ruleForm')">重置</el-button>
        </el-row>
      </div>
    </el-form>
    <div class="divider-info"></div>
    <template v-for="(item, index) in lineChartList">
      <line-chart
        ref="lineChart"
        :titleInfo="item.titleInfo"
        :chartId="item.chartId"
        :tooltip="item.tooltip"
        :chartColor="item.chartColor"
        :asyncHandler="item.asyncHandler"
        :queryForm="form"
        class="line_test"
      />
    </template>
    <export-tip
      :change-export="changeExport"
      @handleExoprClose="handleExoprClose"
      @handleChangeExport="handleChangeExport"
    />
  </div>
</template>
<style>
@import '../../assets/css/changeElement.scss';
</style>
<script>
import * as echarts from 'echarts';
import exportTip from '@/views/other/components/exportTip';
import {
  apiQuantity,
  apiPurchase,
  apiOrderNum,
  apiDrugstoreNum,
  apiDrugstoreNumNew,
  apiShopCouponAmount,
  apiCollageNetPurchase,
  apiCollageDrugstoreNum,
  apiChartExport,
  getRegionList
  } from '@/api/data-statistics/index'
import lineChart from './components/lineChart';
import {actionTracking} from "@/track/eventTracking";
import {ExposureTool, VNodeExposureTool} from "@/utils/exposureTools"
const nameMap = {
  quantity : "total_money",
  purchase : "money",
  orderNum : "order",
  drugstoreNum : "merchant",
  drugstoreNumNew : "new_merchant",
  shopCouponAmount : "discount_amount",
  collageNetPurchase : "collage_money",
  collageDrugstoreNum : "collage_merchant"
}

let exposureMonitor = null;
export default {
  name: 'SalesReports',
  components: {lineChart,exportTip},
  data() {
    return {
      form: {
        statisticalPlacer: 1,
        provinceCodeList:[],
        cityCodeList:[],
        areaCodeList:[],
        opTime:[]
      },
      rules: {
        opTime: [
          { required: true, message: '日期为必填项', trigger: 'blur' }
        ]
      },
      lineChartList:[],
      proviceList: [],
      cityList: [],
      areaList: [],
      changeExport: false,
      minDate: '',
      pickerOptions: {
        onPick: ({ maxDate, minDate }) => {
          this.minDate = minDate;
          this.maxDate = maxDate;
        },
        disabledDate: time => {
          var statistical = 0;
          const curDate = new Date().getTime();
          const two = 365 * 2 * 24 * 3600 * 1000;
          const twoyear = curDate - two;

          if(this.form.statisticalPlacer == 1){
            var statistical = 30 * 24 * 3600 * 1000;
            var pastResult = curDate - statistical;
            var pastDate = new Date(pastResult);
            var pastYear = pastDate.getFullYear();
            var pastMonth = pastDate.getMonth() + 1 < 10 ? '0' + (pastDate.getMonth() + 1) : pastDate.getMonth() + 1;
            var pastDay = pastDate.getDate() < 10 ? '0' + pastDate.getDate() : pastDate.getDate();
            const oldTime = pastYear + '-' + pastMonth + '-' + pastDay;
            statistical = curDate - new Date(oldTime).getTime();
          }else{
            var preMonth = 0;
            if(this.form.statisticalPlacer == 2){
              preMonth = 6;
            }else{
              preMonth = 11;
            }
            var pastDate = new Date(curDate);
            var pastYear = pastDate.getFullYear();
            var pastMonth = pastDate.getMonth() + 1 < 10 ? '0' + (pastDate.getMonth() + 1) : pastDate.getMonth() + 1;
            var pastDay = pastDate.getDate() < 10 ? '0' + pastDate.getDate() : pastDate.getDate();
            const NewTime = pastYear + '-' + pastMonth + '-' + pastDay;
            var oldTime = this.GetPreMonthDay(NewTime, preMonth);
            if (Number(this.form.statisticalPlacer) === 3 || Number(this.form.statisticalPlacer) === 4) {
              oldTime = this.formatDate(new Date().getTime() - (372 * 24 * 3600 * 1000), 'YMD');
            }
            statistical = curDate - new Date(oldTime).getTime();
          }
          if (this.minDate) {
            return (
              time.getTime() > Date.now() - 24 * 3600 * 1000||
              time.getTime() < twoyear ||
              time.getTime() > this.minDate.getTime() + (statistical - 24 * 3600 * 1000)||
              time.getTime() < this.minDate.getTime() - (statistical - 24 * 3600 * 1000)
            );
          }
          return time.getTime() > Date.now() - 24 * 3600 * 1000|| time.getTime() < twoyear;
        }
      }
    }
  },
  created() {
    this.getNowTimeDate();
    this.getProvince('getProv');
  },
  mounted() {
    this.getList();
    actionTracking('sales_report_click', { })
  },

  activated(){
    this.$nextTick(()=>{
      let that = this;
      if(exposureMonitor){
        exposureMonitor.end();
      }else {
        exposureMonitor = new VNodeExposureTool(document.querySelector(".main-box").parentElement, (item)=>{
          console.log(`find ${item._uid}`)
          actionTracking('sales_report_exposure', {
            time : new Date().getTime(),
            org_id : that.orgId,
            sales_report : nameMap[item.getChartId()]
          });
        })
      }
      exposureMonitor.begin(this.$refs.lineChart);
    })
  },
  deactivated(){
    exposureMonitor.end();
  },

  methods: {
    getTemplateRow(value){
      this.getNowTimeDate();
    },
    btnSearchHandler(){
      this.$refs.ruleForm.validate(valid => {
        if (valid) {
          this.$refs.lineChart.map((item)=>{
            item.getChart();
          })
        }
      });
    },
    getList(from) {
      this.lineChartList =[];
      this.getQuantity();
      this.getPurchase();
      this.getOrderNum();
      this.getDrugstoreNum();
      this.getDrugstoreNumNew();
      this.getShopCouponAmount();
      this.getCollageNetPurchase();
      this.getCollageDrugstoreNum();
    },
    getQuantity() {
      let quantity = {
        titleInfo:'采购总金额',
        chartId:'quantity',
        chartColor:'#4184D5',
        tooltip:'每天支付成功的订单总额（含运费，含优惠）<br/>统计订单状态：待审核、出库中、配送中、已完成、已退款',
        asyncHandler:apiQuantity
      }
      this.lineChartList.push(quantity);
    },
    getPurchase(){
      let purchase = {
        titleInfo:'净采购金额',
        chartId:'purchase',
        chartColor:'#FF982C',
        tooltip:'每天支付成功的订单实付总额（含运费，不含优惠）-每天退款成功的退款总额（含运费，不含优惠）<br/>统计订单状态：待审核、出库中、配送中、已完成、已退款<br/>统计退款单状态：退款成功',
        asyncHandler:apiPurchase
      }
      this.lineChartList.push(purchase);
    },
    getOrderNum(){
      let purchase = {
        titleInfo:'销售订单数量',
        chartId:'orderNum',
        chartColor:'#BE41D5',
        tooltip:'每天支付成功的销售订单数量<br/>统计订单状态：待审核、出库中、配送中、已完成、已退款',
        asyncHandler:apiOrderNum
      }
      this.lineChartList.push(purchase);
    },
    getDrugstoreNum(){
      let purchase = {
        titleInfo:'采购药店数量',
        chartId:'drugstoreNum',
        chartColor:'#D54141',
        tooltip:'每天支付成功的销售订单对应的客户数量<br/>统计订单状态：待审核、出库中、配送中、已完成、已退款',
        asyncHandler:apiDrugstoreNum
      }
      this.lineChartList.push(purchase);
    },
    getDrugstoreNumNew(){
      let purchase = {
        titleInfo:'新增采购药店数量',
        chartId:'drugstoreNumNew',
        chartColor:'#4184D5',
        tooltip:'每天支付成功店铺首单的客户数量',
        asyncHandler:apiDrugstoreNumNew
      }
      this.lineChartList.push(purchase);
    },
    getShopCouponAmount(){
      let purchase = {
        titleInfo:'店铺优惠券金额',
        chartId:'shopCouponAmount',
        chartColor:'#FF982C',
        tooltip:'每天支付成功的销售订单已使用的店铺券金额<br/>统计订单状态：待审核、出库中、配送中、已完成',
        asyncHandler:apiShopCouponAmount
      }
      this.lineChartList.push(purchase);
    },
    getCollageNetPurchase(){
      let purchase = {
        titleInfo:'拼团活动净采购金额',
        chartId:'collageNetPurchase',
        chartColor:'#BE41D5',
        tooltip:'每天支付成功的拼团订单实付总额（含运费，不含优惠）-每天退款成功的拼团订单退款总额（含运费，不含优惠）<br/>统计订单状态：待审核、出库中、配送中、已完成、已退款<br/>统计退款单状态：退款成功',
        asyncHandler:apiCollageNetPurchase
      }
      this.lineChartList.push(purchase);
    },
    getCollageDrugstoreNum(){
      let purchase = {
        titleInfo:'拼团活动采购药店数量',
        chartId:'collageDrugstoreNum',
        chartColor:'#D54141',
        tooltip:'每天支付成功的拼团订单对应的客户数量<br/>统计订单状态：待审核、出库中、配送中、已完成、已退款',
        asyncHandler:apiCollageDrugstoreNum
      }
      this.lineChartList.push(purchase);
    },
    // 导出
    exportExcel() {
      const that = this;
      actionTracking('sales_report_export', {
        time : new Date().getTime(),
        org_id : this.orgId
      })
      console.log(`$time = ${new Date().getTime()}, org = ${this.orgId}`);
      const {
          statisticalPlacer,
          provinceCodeList,
          cityCodeList,
          areaCodeList,
          opTime
        } = this.form

        var params = {
          statisticalPlacer,
          startTime: opTime&&opTime[0]?opTime[0]:'',
          endTime: opTime&&opTime[1]?opTime[1]:'',
          provinceCodeList:provinceCodeList&&provinceCodeList.length ? '['+provinceCodeList.join(',')+']':'',
          cityCodeList:cityCodeList&&cityCodeList.length ? '['+cityCodeList.join(',')+']':'',
          areaCodeList:areaCodeList&&areaCodeList.length ? '['+areaCodeList.join(',')+']':'',
        };

      apiChartExport(params).then(res => {
        if (res.code !== 0) {
          this.$message.error(res.message);
          return;
        }
        this.changeExport = true;
      });
    },
    // 省市区
    getProvince(type, e) {
      let code = e&&e.length?'['+e.join(',')+']':'';
      const pms = { parentCode: code|| null };
      getRegionList(pms).then((res) => {
        // (res.data || []).unshift({
        //   regionName: '全部',
        //   regionCode: '',
        // });
        if (type === 'getProv') {
          this.proviceList = res.data || [];
        } else if (type === 'getCity') {
          this.cityList = res.data || [];
        } else if (type === 'getArea') {
          this.areaList = res.data || [];
        }
      });
    },
    handleChangeExport(info) {
      this.changeExport = false;
      if (info === 'go') {
        const path = '/downloadList'
        window.openTab(path)
      }
    },
    handleExoprClose() {
      this.changeExport = false;
    },
     // 重置列表数据
    resetForm() {
      this.$refs.ruleForm.resetFields();
      this.getNowTimeDate();
      this.btnSearchHandler();
    },
    dateTimeFocus() {
      this.minDate = undefined;
    },
    getNowTimeDate() {
      const time = new Date(8 * 3600 * 1000 + +new Date() - 24 * 3600 * 1000 )
        .toJSON()
        .substr(0, 10);
      const cc = new Date().getTime();

      console.log(this.form.statisticalPlacer)
      let that = this;
      const method = {
        1 : 'day',
        2 : 'week',
        3 : 'month',
        4 : 'region'
      }[this.form.statisticalPlacer];
      actionTracking('sales_report_statistical_method_click', {
        time : cc,
        org_id : that.orgId,
        service_quality_statistical_method : method
      })
      if(this.form.statisticalPlacer == 1){
        var statistical = 31 * 24 * 3600 * 1000;
        var pastResult = cc - statistical;
        var pastDate = new Date(pastResult);
        var pastYear = pastDate.getFullYear();
        var pastMonth = pastDate.getMonth() + 1 < 10 ? '0' + (pastDate.getMonth() + 1) : pastDate.getMonth() + 1;
        var pastDay = pastDate.getDate() < 10 ? '0' + pastDate.getDate() : pastDate.getDate();
        const oldTime = pastYear + '-' + pastMonth + '-' + pastDay;
        this.form.opTime = [oldTime, time];
      }else{
        var preMonth = 0;
        if(this.form.statisticalPlacer == 2){
          preMonth = 6;
        }else{
          //按月  按地区时间跨度改为372天
          preMonth = 11;
          console.log('按月  按地区时间跨度改为372天')
          const sD = new Date().getTime() - (372 * 24 * 3600 * 1000);
          this.form.opTime = [this.formatDate(sD,'YMD'), time];
          return false
        }
        var pastDate = new Date(cc);
        var pastYear = pastDate.getFullYear();
        var pastMonth = pastDate.getMonth() + 1 < 10 ? '0' + (pastDate.getMonth() + 1) : pastDate.getMonth() + 1;
        var pastDay = pastDate.getDate() < 10 ? '0' + pastDate.getDate() : pastDate.getDate();
        const NewTime = pastYear + '-' + pastMonth + '-' + pastDay;
        var oldTime = this.GetPreMonthDay(NewTime,preMonth);
        this.form.opTime = [oldTime, time];
      }

    },
    GetPreMonthDay(date, monthNum){
      console.log(date)
      console.log(monthNum)
      var dateArr = date.split('-');
      var year = dateArr[0]; //获取当前日期的年份
      var month = dateArr[1]; //获取当前日期的月份
      var day = dateArr[2]; //获取当前日期的日
      var days = new Date(year, month, 0);
      days = days.getDate(); //获取当前日期中月的天数
      var year2 = year;
      var month2 = parseInt(month) - monthNum;
      if (month2 <= 0) {
          var absM = Math.abs(month2);
          year2 = parseInt(year2) - Math.ceil(absM / 12 == 0 ? 1 : parseInt(absM) / 12);
          month2 = 12 - (absM % 12);
      }
      // var day2 = day;
      // var days2 = new Date(year2, month2, 0);
      // days2 = days2.getDate();
      // if (day2 > days2) {
      //     day2 = days2;
      // }
      if (month2 < 10) {
          month2 = '0' + month2;
      }
      var day2 = '01';
      var t2 = year2 + '-' + month2 + '-' + day2;
      return t2;
    },
    GetNextMonthDay(date, monthNum){
      var dateArr = date.split('-');
      var year = dateArr[0]; //获取当前日期的年份
      var month = dateArr[1]; //获取当前日期的月份
      var day = dateArr[2]; //获取当前日期的日
      var days = new Date(year, month, 0);
      days = days.getDate(); //获取当前日期中的月的天数
      var year2 = year;
      var month2 = parseInt(month) + parseInt(monthNum);
      if (month2 > 12) {
          year2 = parseInt(year2) + parseInt((parseInt(month2) / 12 == 0 ? 1 : parseInt(month2) / 12));
          month2 = parseInt(month2) % 12;
      }
      var day2 = day;
      var days2 = new Date(year2, month2, 0);
      days2 = days2.getDate();
      if (day2 > days2) {
          day2 = days2;
      }
      if (month2 < 10) {
          month2 = '0' + month2;
      }

      var t2 = year2 + '-' + month2 + '-' + day2;
      return t2;
    }
  }
}
</script>

<style lang="scss" scoped>
.main-box {
  background: #ffffff;
  .search-info {
    padding: 10px 20px 0;
    font-weight: 500;
    .sign {
      display: inline-table;
      width: 3px;
      height: 13px;
      background: linear-gradient(360deg, #1d69c4 1%, #8bbdfc 99%);
      border-radius: 2px;
      margin-right: 8px;
      text-align: center;
    }
    .searchMsg {
      line-height: 32px;
      font-weight: 700;
    }
    .radio-style {
      padding: 0 30px 0 0;
      .radio-info {
        width: 50px;
      }
      .radio-info-long {
        width: 65px;
      }
    }
    .search-title {
      display: table-cell;
      padding: 0 10px;
      text-align: center;
      border: 1px solid #dcdfe6;
      height: 30px;
      line-height: 30px;
      vertical-align: middle;
      border-right: none;
      border-radius: 4px 0 0 4px;
      color: #333333;
      white-space: nowrap;
    }
    .search-btn {
      margin-left: 10px;
      float: right;
    }
    .btn-info {
      padding: 9px 15px;
      font-size: 12px;
      border-radius: 3px;
      border: 1px solid #4183d5;
      color: #4183d5;
      background: #fff;

      &:hover,
      &:focus {
        background: #4183d5;
        border-color: #4183d5;
        color: #ffffff;
      }
    }
    .timeSel {
      width: 280px;
    }
    ::v-deep  .el-select {
      display: table-cell;
    }
    ::v-deep  .el-form-item__content {
      width: 100%;
    }
    ::v-deep  .el-form--inline .el-form-item {
      display: inline-block;
      margin-right: 20px;
      vertical-align: top;
    }
    ::v-deep  .el-form-item--small .el-form-item__content {
      line-height: 30px;
    }
    ::v-deep  .el-form-item__label {
      margin-left: 20px;
      padding: 0;
    }
    ::v-deep  .el-input__inner {
      border-radius: 0 4px 4px 0;
    }
    // ::v-deep  .el-date-editor {
    //   width: 100%;
    // }
  }
  .span-tip {
    display: inline-block;
    width: 12px;
    height: 12px;
    font-size: 10px;
    border: 1px solid #999999;
    color: #999999;
    text-align: center;
    line-height: 12px;
    border-radius: 50%;
    margin-left: 5px;
  }
  .divider-info {
    margin: 10px 20px;
    border-bottom: 1px solid #f0f2f5;
  }
  #quantity,
  #purchase,
  #orderNum,
  #drugstoreNum,
  #drugstoreNumNew,
  #shopCouponAmount,
  #collageNetPurchase,
  #collageDrugstoreNum {
    width: 100%;
    height: 408px;
  }
}
</style>
