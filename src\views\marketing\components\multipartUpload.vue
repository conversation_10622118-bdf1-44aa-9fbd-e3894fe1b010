<script>
import { givePoolFullGiveTemplate, masterFullGiveTemplate, batchOffLineFullGiveTemplate } from '../../../api/market/mzPromotion'
import IImg from '../../qualificationOnline/components/i-img.vue';
import { importProductByProductType, batchOffline, batchDeleteBlackList } from './multipartUploadFunc'
import { apiDownloadTemplateNormal } from '../../../api/storeManagement/blacklist'
export default {
  components: {
    IImg
  },
  props: {
    type: {
      type: Number,
      default: 0
    },
    //已有的list，判断数量是否超出限制的，没这要求就不传
    curList: {
      type: Array,
      default: () => []
    },
    maxCount: {
      type: Number,
      default: 200
    },
    size: {
      type: Array,
      default: () => [0, 3000000]
    },
  },
  data() {
    return {
      list: [{
        title: '批量导入赠品池',
        //异步，等用时再调用maxCount
        tips:() => ['请按照模板要求录入信息后导入', '商品来源必须为：“普通商品”、“赠品”','商品状态必须为：“待上架”、“销售中”、“已下架”', `单个文件不超过3M，最多可导入${this.maxCount}个商品`],
        download: givePoolFullGiveTemplate,
        upload: importProductByProductType(this, 2)
      },{
        title: '批量导入主品',
        tips: () => ['请按照模版要求录入信息后导入', '商品来源必须为：“普通商品”、“拼团商品”、“批购包邮商品”', '商品状态必须为：“待上架”、“销售中”、“已下架”', `单个文件不可超过3M，最多可导入${this.maxCount}个商品`],
        download: masterFullGiveTemplate,
        upload: importProductByProductType(this, 1)
      }, {
        title: '批量下线满赠活动',
        tips: () => ['请按照模版要求录入信息后导入', '满赠活动状态必须为：“未开始”、“进行中”', '单个文件不可超过3M，最多可导入1000条数据'],
        download: batchOffLineFullGiveTemplate,
        upload: batchOffline(this)
      }, {
        title: '批量删除黑名单',
        tips: () => ['只允许删除已加入黑名单的客户', '单个文件不可超过10M，最多可导入3000条数据'],
        errMsg: '文件不可超过10M，数据不可超过3000条',
        download: () => { apiDownloadTemplateNormal('批量删除黑名单.xlsx') },
        upload: batchDeleteBlackList(this)
      }],
      result: {
        visible: false,
        msgList: [],
        errorUrl: ''
      },
      visible: false,
      loading: false,
      fileEl: document.createElement('input'),
      fileList: [],
    }
  },
  mounted() {
    this.fileEl.type = 'file';
    this.fileEl.accept = '.xlsx, .xls';
    this.fileEl.onchange = (e) => {
      const file = e.target.files[0];
      if (file.size > this.size[1] || file.size < this.size[0]) {
        this.$message.error(this.list[this.type].errMsg ? this.list[this.type].errMsg : `文件大小不合要求，请重新选择`)
        return
      }
      this.fileList = [{
        name: file.name,
        file: file
      }];

      this.fileEl.value = '';
    }
  },
  methods: {
    downloadTemplate() {
      this.list[this.type].download()
    },
    submit() {
      if (!this.fileList.length) {
        this.$message.error(`请先选择文件`)
        return
      }
      this.list[this.type].upload(this.fileList[0].file)
    },
    errorDownload() {
      window.open(this.result.errorUrl);
    }
  }
}
</script>

<template>
  <div style="display: inline-block;">
    <el-button type="primary" size="mini" @click="visible = true;">
      <slot>
        批量导入
      </slot>
    </el-button>
    <el-dialog :title="list[type].title" append-to-body :visible.sync="visible" width="500px" @close="fileList = [];$emit('end')">
      <div v-loading="loading">
        <div class="btn-box">
          <div class="primary" @click="fileEl.click()">
            <span>导入Excel文件</span>
          </div>
          <div class="normal" @click="downloadTemplate">
            <span>下载模板</span>
          </div>
        </div>
        <div>
          <IImg v-if="fileList.length" v-model="fileList" :column="1" :maxCount="1" isEdit></IImg>
        </div>
        <div>
          <p style="font-weight: 600;margin: 10px 0;">温馨提示：</p>
          <p style="font-size: 13px;" v-for="(val, index) in list[type].tips()" :key="index">
            {{ index + 1 }}、{{ val }}
          </p>
        </div>
      </div>
      <div slot="footer">
        <el-button type="normal" size="mini" @click="visible = false;">取消</el-button>
        <el-button type="primary" size="mini" @click="submit">确定</el-button>
      </div>
    </el-dialog>
    <el-dialog title="导入结果通知" :visible.sync="result.visible" append-to-body @close="result.msgList = [];result.errorUrl = '';" width="400px">
      <p style="text-align: center;" v-for="val in result.msgList">{{ val }}</p>
      <div slot="footer">
        <el-button type="normal" size="mini" @click="result.visible = false;">取消</el-button>
        <el-button v-if="result.errorUrl" type="primary" size="mini" @click="errorDownload">下载失败文件</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<style scoped>
p {
  margin: 5px 0;
}
::v-deep   .el-dialog {
  border-radius: 5px;
  overflow: hidden;
}
::v-deep   .el-dialog__header {
  padding: 15px;
  background-color: #f3f3f3;
}
::v-deep   .el-dialog__header span {
  font-size: 14px;
  line-height: normal;
}
::v-deep   .el-dialog__header button {
  right: 15px;
  top: 15px;
}
::v-deep   .el-dialog__body {
  padding: 5px 15px;
  border-bottom: solid 1px #e4eaf1;
}
::v-deep   .el-dialog__footer {
  padding: 15px;
}
.btn-box {
  display: flex;
  gap: 40px;
}
.btn-box .primary, .btn-box .normal {
  flex-grow: 1;
  flex-shrink: 0;
  flex-basis: 0;
  text-align: center;
  padding: 7px;
  border-radius: 3px;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.2s;
}
.btn-box .primary {
  background-color: #4183d5;
  border: solid 1px #3373c0;
  color: #fff;
}
.btn-box .primary:hover {
  background-color: #64a4f3;
  border: solid 1px #4e8fde;
}
.btn-box .primary:active {
  background-color: #397aca;
  border: solid 1px #2c69b4;
}
.btn-box .normal {
  border: solid 1px #5595e3;
  color: #5595e3;
}
.btn-box .normal:hover {
  background-color: #e3f0ff;
  border: solid 1px #62a3f4;
}
.btn-box .normal:active {
  background-color: #d2e2f6;
  border: solid 1px rgb(90, 154, 233);
}
</style>
