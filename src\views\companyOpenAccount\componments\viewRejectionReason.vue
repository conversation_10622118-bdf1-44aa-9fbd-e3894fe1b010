<template>
  <el-dialog
    title="驳回原因"
    :visible.sync="dialogVisible"
    width="30%"
    :before-close="handleClose">
    <span>
      {{failReason}}
    </span>
  </el-dialog>
</template>

<script>
export default {
  name: "viewRejectionReason",
  props: {
    rejectionReasonVisible: {
      type: Boolean,
      default: true
    },
    failReason: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      dialogVisible: true
    }
  },
  methods: {
    handleClose() {
      this.$emit('update:rejectionReasonVisible', false)
    }
  }
}
</script>

<style scoped>

</style>
