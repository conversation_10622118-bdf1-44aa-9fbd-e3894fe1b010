<template>
  <div>
    <el-dialog
      title="退款单审核"
      :visible="stateVisible"
      width="600px"
      @close="closeDialog"
    >
      <div class="contentWrap">
        <div v-if="editRow.payChannel === 8 && activeAfterSaleStatus === '6'" style="color: red">
          如确定退款，请先给客户线下退款！
        </div>
        <div class="contentLine">
          <span>客户名称：</span>
          <span>{{ editRow.merchantName }}</span>
        </div>
        <div class="contentLine">
          <span>退款单编号：</span>
          <span>{{ editRow.refundOrderNo }}</span>
          <span class="showRefundVoucher" @click="toRefundVoucher" v-if="activeAfterSaleStatus === '0'">查看退款凭证</span>
        </div>
        <div class="contentLine">
          <span>订单编号：</span>
          <span>{{ editRow.orderNo }}</span>
        </div>
        <div class="contentLine">
          <span>退款金额：</span>
          <span style="color:#ff0021">{{ editRow.refundFee }}&nbsp;</span>
          <span>
            (包含运费金额：<span style="color:#ff0021">{{ editRow.freight||0 }}</span>)
          </span>
        </div>
        <div class="contentLine">
          <span>退款数量：</span>
          <!-- refundCountDecimal -->
          <span style="color:#ff0021">{{ editRow.refundCountDecimal }}</span>
        </div>
        <div v-if="editRow.payChannel === 8 && activeAfterSaleStatus === '6'">
          <span><span style="color: red;">*</span>付款凭证：</span>
          <el-upload
            action
            :http-request="uploadImg"
            :before-upload="beforeAvatarUpload"
            class="avatar-uploader"
            list-type="picture-card"
            :file-list="evidenceImagesObj"
            :on-remove="handleRemove"
            :on-preview="handlePictureCardPreview"
          >
            <template #default>
              <i class="el-icon-plus" />
            </template>
            <template #tip>
              <div class="el-upload__tip">
                支持jpeg、png、jpg、gif格式<br>
                大小不超5M
              </div>
            </template>
            <!--            <template #file="{ file }">-->
            <!--              <img class="el-upload-list__item-thumbnail" :src="file.url" alt="" />-->
            <!--              <div>-->
            <!--                <span class="el-upload-list__item-actions">-->
            <!--                  <span-->
            <!--                    class="el-upload-list__item-preview"-->
            <!--                    @click="handlePictureCardPreview(file)"-->
            <!--                  >-->
            <!--                    <i class="el-icon-zoom-in" />-->
            <!--                  </span>-->
            <!--                  <span-->
            <!--                    class="el-upload-list__item-delete"-->
            <!--                    @click="handleRemove(file)"-->
            <!--                  >-->
            <!--                    <i class="el-icon-delete" />-->
            <!--                  </span>-->
            <!--                </span>-->
            <!--              </div>-->
            <!--            </template>-->
          </el-upload>
          <!--          <el-dialog v-model="dialogVisible">-->
          <!--            <img-->
          <!--              width="100%"-->
          <!--              :src="dialogImageUrl"-->
          <!--              alt=""-->
          <!--            >-->
          <!--          </el-dialog>-->
        </div>
        <div v-if="editRow.payChannel === 8 && activeAfterSaleStatus === '6'" style="display: flex;">
          <span style="width: 60px;"><span style="color: red;">*</span>备注：</span>
          <el-input
            type="textarea"
            placeholder="请输入备注"
            maxlength="200"
            show-word-limit
            v-model="agreeRefundRemark"
          />
        </div>
      </div>
      <div v-if="activeAfterSaleStatus === '0' && isTrialMerchant" class="textip">
        <p>
          请根据情况选择您的处理方式:<br/>
          1、如同意客户申请，请选择【同意】;<br/>
          2、如不同意客户申请，请选择【拒绝】并备注真实原因;<br/>
          3、如与客户沟通一致使用小额打款解决该售后，请选择【小额打款】;<br/>
          4、如同意客户申请，并额外赔偿客户，请选择【同意并额外赔偿】<br/>
        </p>
      </div>
      <div
        slot="footer"
        style="width:90%;padding-bottom:10px;margin:0 auto;"
      >
        <div style="width:100%;display:flex;justify-content: space-evenly;" v-if="activeAfterSaleStatus === '0'">
          <el-button
            type="primary"
            size="small"
            @click="approvalBtn(editRow.id)"
          >
            同意
          </el-button>
          <el-button
            type="primary"
            size="small"
            @click="openInnerDialog('auditFailed',editRow.auditProcessState, editRow.id)"
          >
            拒绝
          </el-button>
          
          <el-button
            type="primary"
            size="small"
            @click="openSmlAndAdlDialog(editRow, '0')"
            v-if="isTrialMerchant" 
          >
            小额打款
          </el-button>
          <el-button
            type="primary"
            size="small"
            @click="openSmlAndAdlDialog(editRow, '1')"
            v-if="isTrialMerchant" 
          >
            同意并额外赔偿
          </el-button>
        </div>
        <div v-if="activeAfterSaleStatus === '3'">
          <el-button
            type="primary"
            size="small"
            @click="approvalBtn(editRow.id)"
          >
            确认入库
          </el-button>
          <el-button
            type="primary"
            size="small"
            @click="openInnerDialog('rejectWarehousing',editRow.auditProcessState, editRow.id)"
          >
            拒绝入库
          </el-button>
        </div>
        <div v-if="activeAfterSaleStatus === '6'">
          <el-button
            type="primary"
            size="small"
            :loading="qLoading"
            @click="allApproved(editRow.refundFee,1,editRow.id,refuseReasonValue,editRow.auditState,editRow.auditProcessState,editRow.payChannel, editRow.isToBeConfirmedProcess, editRow.refundChannel)"
          >
            确定退款
          </el-button>
          <el-button
            type="primary"
            size="small"
            @click="openInnerDialog('refuseRefund',editRow.auditProcessState, editRow.id)"
          >
            拒绝退款
          </el-button>
        </div>
      </div>
      <el-dialog
        width="40%"
        title="温馨提示"
        :visible.sync="innerVisible"
        append-to-body
      >
        <div style="padding:0 15px">
          <div class="topTip">
            确认拒绝用户的退款申请吗？如确认请填写驳回原因
          </div>
          <div style="margin-top: 10px;font-size: 14px;">
            提示：驳回原因<span v-if="activeAfterSaleStatus === '0' || activeAfterSaleStatus === '3'">及备注</span>将展示给客户查看，请认真填写
          </div>
          <div style="min-height:200px;margin-top:10px">
            <span style="color: red;">*</span> 驳回原因：
            <el-select
              v-model="refuseReasonValue"
              placeholder="请选择"
              size="small"
            >
              <el-option
                v-for="item in refuseReasonData"
                :key="item.id"
                :label="item.refuseReason"
                :value="item.refuseReason"
              />
            </el-select>
            <div style="margin: 20px 0;" />
            <el-input
              v-if="refuseReasonValue === '其他'"
              v-model="otherReasonsValue"
              type="textarea"
              placeholder="请输入其他原因内容"
              maxlength="50"
              show-word-limit
            />
            <div style="margin-top: 10px;font-size: 14px;display: flex;" v-if="activeAfterSaleStatus === '0' || activeAfterSaleStatus === '3'">
              <div style="width: 100px;"><span style="color: red;">*</span> 备注：</div>
              <el-input
                type="textarea"
                placeholder="请输入备注"
                maxlength="200"
                show-word-limit
                v-model="remark"
              />
            </div>
            <div style="margin-top: 10px;font-size: 14px;display: flex;" v-if="activeAfterSaleStatus === '3'">
              <div style="width: 85px;"><span style="color: red;">*</span> 上传凭证</div>
              <div style="width:400px;">
                <i-file
                  v-if="uploadedImgs && uploadedImgs.length < 6"
                  :multiple="true"
                  :bucket="3"
                  :maxCount="6"
                  style="margin-bottom: 10px"
                  v-model="uploadedImgs">
                </i-file>
                <i-img 
                  v-model="uploadedImgs" 
                  :deleteBtn="true"
                  :maxShowLength="6"
                  :showNullEl="false"></i-img>
              </div>
            </div>
          </div>
        </div>
        <div slot="footer">
          <el-button
            type="primary"
            size="small"
            :loading="qLoading"
            @click="allApproved(editRow.refundFee,-1,editRow.id,refuseReasonValue,editRow.auditState,editRow.auditProcessState,'', editRow.isToBeConfirmedProcess, editRow.refundChannel)"
          >
            提 交
          </el-button>
          <el-button
            size="small"
            @click="closeInnerDialog"
          >
            取 消
          </el-button>
        </div>
      </el-dialog>
    </el-dialog>
    <el-image-viewer
      v-if="dialogVisible"
      :url-list="srcArr"
      :on-close="closeImg"
      append-to-body
      :z-index="100000"
    />
    <!-- // 小额打款&同意并额外赔偿二次弹框 -->
    <smlAndAdlDialog ref="smlAndAdlDialog"></smlAndAdlDialog>
  </div>
</template>

<script>
import {
  updateRefundStatus,
  updateAgreeStockInStatus,
  RefundRejectionReason,
  confirmAndRejectRefund,
  confirmAndRejectRefunds,
} from '@/api/afterSale/index';
import { uploadFile } from '@/api/qual';
import { getHostName } from '@/api/storeManagement';
import ElImageViewer from 'element-ui/packages/image/src/image-viewer';
// 小额打款&同意并额外赔偿二次弹框
import smlAndAdlDialog from './smlAndAdlDialog.vue';
import iFile from './iFile.vue';
import iImg from './i-img.vue';

export default {
  name: 'RefundFormApproval',
  components: { ElImageViewer, smlAndAdlDialog,iFile, iImg },
  props: {
    editRow: {
      type: Object,
      default: () => {},
    },
    activeAfterSaleStatus: {
      type: String,
      default: null,
    },
    isTrialMerchant: {
      type: Boolean,
      default: false
    }, // 是否为灰度商户
  },
  data() {
    return {
      hostName: '',
      refuseReasonData: [],
      refuseReasonValue: '',
      otherReasonsValue: '',
      reqFun: null,
      stateVisible: false,
      innerVisible: false,
      query: {
        fee: '',
        auditState: 0,
        closeReason: '',
      },
      evidenceImagesObj: [],
      evidenceImages: [],
      dialogImageUrl: '',
      dialogVisible: false,
      srcArr: [],
      qLoading: false,
      remark: '',
      uploadedImgs: [],
      agreeRefundRemark: ""
    };
  },
  watch: {
    editRow: {
      handler() {
        this.evidenceImagesObj = [];
        this.agreeRefundRemark = ''
      },
      immediate: true,
      deep: true,
    },
  },
  created() {
    getHostName().then((res) => {
      if (res.hostName) {
        this.hostName = res.hostName;
      }
    });
  },
  methods: {
    openSmlAndAdlDialog(val, type){
      this.$refs.smlAndAdlDialog.openDialog(val, type);
    },
    approvalBtn(id) {
      if (this.activeAfterSaleStatus === '0') {
        this.reqFun = updateRefundStatus;
      } else if (this.activeAfterSaleStatus === '3') {
        this.reqFun = updateAgreeStockInStatus;
      }
      this.reqFun({ id }).then((res) => {
        if (res.code === 0) {
          this.$message.success({
            message: '操作成功',
            offset: 100,
          });
          this.$emit('getNewList');
          this.stateVisible = false;
        } else {
          this.$message.error({
            message: res.message,
            offset: 100,
          });
        }
      })
        .catch((error) => {
          console.log(error);
          this.$message({
            message: '操作失败',
            type: 'error',
          });
        });
    },
    openInnerDialog(type, status, id) {
      this.innerVisible = true;
      this.refuseReasonData = [];
      this.refuseReasonValue = '';
      this.remark = ''
      this.uploadedImgs = []
      RefundRejectionReason({ refundOrderId: id }).then((res) => {
        if (res.code === 0) {
          this.refuseReasonData = res.data || [];
        }
      });
    },
    allApproved(fee, state, id, reason, auditState, auditProcessState, payChannel, isToBeConfirmedProcess, refundChannel) {
      const params = { ...this.query };
      if (payChannel === 8 && this.evidenceImagesObj.length < 1 && state === 1) {
        this.$message.warning('请上传打款凭证');
        return;
      }
      params.fee = fee;
      params.auditState = state;
      if (reason === '其他') {
        params.closeReason = this.otherReasonsValue;
      } else {
        params.closeReason = reason;
      }

      // if (auditState === 0 && auditProcessState === 0) {
      //   params.operateType = 4;
      // } else if (auditState === 0 && auditProcessState === -1) {
      //   params.operateType = 6;
      // } else if (auditState === 0 && auditProcessState === 1 && state === 1) {
      //   params.operateType = 7;
      // } else if (auditState === 0 && auditProcessState === 1 && state === -1) {
      //   params.operateType = 8;
      // } else {
      //   params.operateType = '';
      // }
      if ((!(isToBeConfirmedProcess === 1 && refundChannel === 4) && auditState === 0 && auditProcessState === 0) || (isToBeConfirmedProcess === 1 && refundChannel === 4 && auditState === 0 && auditProcessState === 3)) {
        // 客服审核驳回
        params.operateType = 4;
      } else if ((!(isToBeConfirmedProcess === 1 && refundChannel === 5) && auditState === 0 && auditProcessState === -1) || (isToBeConfirmedProcess === 1 && refundChannel === 5 && auditState === 0 && auditProcessState === 3)) {
        // 仓库审核驳回
        params.operateType = 6;
      } else if (auditState === 0 && auditProcessState === 1 && state === 1) {
        // 财务审核通过
        params.operateType = 7;
      } else if (auditState === 0 && auditProcessState === 1 && state === -1) {
        // 财务审核驳回
        params.operateType = 8;
      } else {
        params.operateType = '';
      }

      this.qLoading = true;
      if (state === 1) {
        params.refundId = id;
        params.evidenceImages = '';
        const aryImg = [];
        this.evidenceImagesObj.forEach((item) => {
          let urls = item.url.replace(this.hostName, ' ');
          aryImg.push(urls);
        });
        if(aryImg.length == 0) {
          this.$message.warning('请上传打款凭证');
          this.qLoading = false;
          return
        }
        if(!this.agreeRefundRemark) {
          this.$message.warning('请填写备注');
          this.qLoading = false;
          return
        }
        params.merchantRejectionRemark = this.agreeRefundRemark;
        params.evidenceImages = aryImg.length > 0 ? aryImg.join(',') : '';
        confirmAndRejectRefund(params).then((res) => {
          this.qLoading = false;
          if (res.code === 0) {
            this.$message.success({
              message: '操作成功',
              offset: 100,
            });
            this.$emit('getNewList');
            this.stateVisible = false;
            this.innerVisible = false;
          } else {
            this.$message.error({
              message: res.message,
              offset: 100,
            });
          }
        });
      } else {
        if(this.activeAfterSaleStatus === '0' || this.activeAfterSaleStatus === '3') {
          console.log(this.uploadedImgs)
          if(this.remark) {
            params.merchantRejectionRemark = this.remark;
            if(this.activeAfterSaleStatus === '3') {
              if(this.uploadedImgs.length) {
                params.merchantCertificateImglist = this.uploadedImgs.map(img => img.url).join(',');
              }else {
                this.qLoading = false;
                this.$message.warning('请上传凭证');
                return;
              }
            }
          }else {
            this.qLoading = false;
            this.$message.warning('请填写备注');
            return;
          }
        }
        params.refundOrderId = id;
        confirmAndRejectRefunds(params).then((res) => {
          this.qLoading = false;
          if (res.code === 0) {
            this.$message.success({
              message: '操作成功',
              offset: 100,
            });
            this.$emit('getNewList');
            this.stateVisible = false;
            this.innerVisible = false;
          } else {
            this.$message.error({
              message: res.message,
              offset: 100,
            });
          }
        });
      }
    },
    closeDialog() {
      this.stateVisible = false;
    },
    closeInnerDialog() {
      this.innerVisible = false;
    },
    uploadImg(file) {
      uploadFile(file).then((res) => {
        if (res.code === '200') {
          this.evidenceImagesObj.push({ url: `${this.hostName}/${res.data}` });
        }
      });
    },
    beforeAvatarUpload(file) {
      if (!file) {
        this.$message.error('请上传图片');
        return false;
      }
      const isJPG = file.type === 'image/jpeg'
          || file.type === 'image/png'
          || file.type === 'image/bmp'
          || file.type === 'image/jpg'
          || file.type === 'image/gif';
      const isLt5M = file.size / 1024 / 1024 < 5;

      if (!isJPG || !isLt5M) {
        this.$message.error('图片不满足上传要求，请重新上传');
        return false;
      }
      return isJPG && isLt5M;
    },
    handleRemove(file) {
      console.log(file, 'sss');
      this.evidenceImagesObj.forEach((item, index) => {
        if (item.url === file.url) {
          this.evidenceImagesObj.splice(index, 1);
        }
      });
    },
    handlePictureCardPreview(file) {
      this.dialogVisible = true;
      const ary = [];
      this.srcArr = [];
      this.evidenceImagesObj.forEach((item) => {
        if (item.url === file.url) {
          this.srcArr.push(item.url);
        } else {
          ary.push(item.url);
        }
      });
      this.srcArr = this.srcArr.concat(ary);
    },
    closeImg() {
      this.dialogVisible = false;
    },
    toRefundVoucher() {
      this.stateVisible = false;
      this.$emit('toRefundVoucher')
    }
  },
};

</script>

<style scoped lang="scss">
  .flexBox {
    display: flex;
    align-items: center;
  }

  ::v-deep  .el-dialog__body {
    padding: 10px 20px;
  }

  ::v-deep  .el-dialog__header {
    padding: 10px 20px;
    background: #f9f9f9;
  }

  ::v-deep  .el-dialog__headerbtn {
    top: 15px;
  }

  ::v-deep  .el-dialog__title {
    font-size: 16px;
  }

  .contentWrap {
    width: 90%;
    margin: 0 auto;
    padding: 0;
    word-break: break-all;
    font-size: 14px;
    color: #606266;
  }

  .contentLine {
    height: 30px;
    line-height: 30px;
  }

  .topTip {
    font-size: 16px;
    max-width: 100%;
    margin-bottom: 5px;
    font-weight: bold;
  }
  ::v-deep  .avatar-uploader .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    background: rgba(0, 0, 0, 0.02);
    width: 80px;
    height: 80px;
    line-height: 80px;
  }
  ::v-deep  .avatar-uploader .el-upload:hover {
    border-color: #2a74bd;
  }
  ::v-deep  .avatar-uploader .el-upload__tip {
    margin-top: 0;
    color: #999999;
    font-size: 12px;
  }
  .showRefundVoucher {
    margin-left: 25px;
    cursor: pointer;
    color: #409eff;
  }
  .avatar-uploader-icon {
    opacity: 1;
    font-size: 12px;
    font-family: PingFangSC, PingFangSC-Regular;
    font-weight: 400;
    text-align: left;
    color: rgba(0, 0, 0, 0.65);
    line-height: 14px;
  }
  .avatar {
    width: 80px;
    height: 80px;
    display: block;
  }
  ::v-deep  .avatar-uploader .el-upload-list--picture-card .el-upload-list__item {
    width: 80px;
    height: 80px;
  }

  .textip{
    width: 90%;
    margin: 0 auto;
    color: red
  }

</style>
