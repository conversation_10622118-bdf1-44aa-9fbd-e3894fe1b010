<template>
  <el-dialog
    title="修改赠品数量"
    :visible="true"
    width="600px"
    :close-on-click-modal="false"
    :destroy-on-close="true"
    :before-close="handleDialogClose"
  >
    <div>
      <el-form
        ref="form"
        class="fix-item-width"
        size="small"
        label-position="right"
        label-width="100px"
        :model="listQuery"
      >
        <el-form-item label="赠品数量" prop="cusid">
          <el-input
            v-model.trim="listQuery.count"
            style="width: 200px"
            clearable placeholder="请输入"
             onkeyup="value=value.replace(/[^\d]/g,'')"
          />
        </el-form-item>
        <el-form-item label="商品库存" prop="barcode">
          {{ availableQty }}
        </el-form-item>
      </el-form>
    </div>
    <span slot="footer">
      <el-button size="medium" @click="handleDialogClose">取消</el-button>
      <el-button size="medium" style="margin-left: 20px;" type="primary" @click="confirm">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { updateGiveCount, getCsuAvailableQty } from '@/api/market/mzPromotion';
export default {
  props: {
    actRow: {
      type: Object,
      default: () => {}
    },
  },
  data() {
    return {
      listQuery: {
				count: '',
      },
      availableQty: 0,
    };
  },
  mounted() {
    this.getCsuAvailableQty();
  },
  methods: {
    getCsuAvailableQty() {
      this.listQuery.count = this.actRow.giveSkuDto.giveQty || '';
      getCsuAvailableQty({ csuId: this.actRow.giveSkuDto.csuid }).then((res) => {
        if (res.code === 1000) {
          this.availableQty = res.data.availableQty || 0;
        }
      })
    },
    async confirm() {
      if (!this.listQuery.count) {
				this.$message.error('请填写赠品数量');
				return;
			}
      const res = await updateGiveCount({
        id: this.actRow.id,
        count: this.listQuery.count,
      });
      if (res.code === 1000) {
        this.$message.success('修改成功');
        setTimeout(() => {
          this.$emit('refresh');
          this.handleDialogClose();
        }, 500);
      } else {
        this.$message.error(res.msg);
      }
    },
    handleDialogClose() {
      this.$emit('cancelModal', false);
    },
  },
};
</script>
<style scoped lang="scss">

</style>
