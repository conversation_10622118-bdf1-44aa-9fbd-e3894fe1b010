/*eslint-disable*/
<template>
  <div
    v-loading="isLoading"
    class="main-box"
  >
    <div class="Fsearch">
      <el-row
        type="flex"
        align="middle"
        justify="space-between"
        class="my-row"
      >
        <el-row
          type="flex"
          align="middle"
        >
          <span class="sign" />
          <div class="searchMsg">
            基本信息
          </div>
        </el-row>
        <el-button
          type="primary"
          size="small"
          @click="$router.go(-1)"
        >
          返回
        </el-button>
      </el-row>
    </div>
    <div
      class="list-box"
    >
      <div class="info-box">
        <div class="info-item">
          <p class="info-label">
            <span class="text-span">优惠券名称:</span>
          </p>
          <p class="info-con">
            {{ detailData.name }}
          </p>
        </div>
        <!-- <div class="info-item">
          <p class="info-label">
            <span class="text-span">券类型:</span>
          </p>
          <p class="info-con">
            {{ detailData.couponBasicTemplate.type == 1 ? "满减券" : "" }}
          </p>
        </div> -->
        <div
          class="info-item"
          style="display: flex; align-items: center;"
        >
          <p class="info-label">
            <span class="text-span">适用范围:</span>
          </p>
          <p>
            {{ filterScopeType(detailData.scopeType) }}
          </p>
          <p
            v-if="
              csuIds !== null &&
                csuIds.length >= 0 &&
                (detailData.scopeType === 1||detailData.scopeType===2)
            "
            class="text-span"
            style="margin-left: 10px"
          >
            已选择{{ csuIds !== null ? csuIds.length : '' }}个商品
          </p>
          <el-button
            v-if="
              type !== 'change' && csuIds !== null &&
                csuIds.length >= 0 && (detailData.scopeType === 1||detailData.scopeType===2)
            "
            style="margin-left: 10px"
            type="text"
            @click="showGoodList"
          >
            查看
          </el-button>
          <el-button
            v-if="type === 'change'"
            style="margin-left: 10px"
            type="primary"
            plain
            size="small"
            @click="showGoodList"
          >查看商品</el-button>
          <el-upload
            v-if="type === 'change'"
            ref="upload"
            :show-file-list="false"
            :on-success="uploadSucces"
            :action="uploadFunction()"
            accept=".xls, .xlsx"
            style="margin-left: 10px; margin-top: -1px"
          >
            <el-button
              type="primary"
              plain
              size="small"
              @click="
                () => {
                  showBatchPop = true
                }
              "
              >批量导入</el-button
            >
          </el-upload>
          <el-button
            v-if="type === 'change'"
            style="margin-left: 10px"
            type="primary"
            plain
            size="small"
            @click="downLoadExcelMode()"
            >下载模板</el-button
          >
        </div>
        <div class="info-item">
          <p class="info-label">
            <span class="text-span">使用时间:</span>
          </p>
          <p
            v-if="detailData.validityType === 2"
            class="info-con"
          >
            {{ handleTimeFormat(detailData.startTime) }}
            -
            {{ handleTimeFormat(detailData.endTime) }}
          </p>
          <p
            v-if="detailData.validityType === 1"
            class="info-con"
          >
            {{ detailData.validityDays }}天
          </p>
        </div>
        <div
          class="con-title"
          style="padding-left: 0px; margin-top: 40px"
        >
          <span class="line" /><span>金额信息</span>
        </div>
        <div class="info-item">
          <p class="info-label">
            <span class="text-span">优惠金额:</span>
          </p>
          <p v-if="detailData.type===1" class="info-con">
            {{ detailData.moneyInVoucher }}元
          </p>
          <p v-else class="info-con">
            {{ detailData.discount }}折
            <span v-if="detailData.maxMoneyInVoucher">，最高减{{ detailData.maxMoneyInVoucher }}元</span>
          </p>
        </div>
        <div class="info-item">
          <p class="info-label">
            <span class="text-span">使用门槛:</span>
          </p>
          <p class="info-con">
            <span v-if="detailData.reduceType == 0">
              满{{ detailData.minMoneyToEnable }}元可用</span>
            <span v-else>
              每满 {{ detailData.minMoneyToEnable }}元， 最高可减{{
                detailData.discount
              }}元
            </span>
          </p>
        </div>
        <!-- <div class="info-item">
          <p class="info-label">
            <span class="text-span">发行数量:</span>
          </p>
          <p class="info-con">
            {{
              detailData.couponStock ? detailData.couponStock.totalCount : ""
            }}
          </p>
        </div> -->
      </div>
      <div
        class="sample-box"
        style="margin-top: -20px"
      >
        <h4 style="margin-left: -20px">
          示例图：
        </h4>
        <p>
          <img src="../../assets/image/marketing/sampleCard.png">
        </p>
      </div>
    </div>
    <div v-if="type === 'change'" style="padding: 40px 0 0 150px;">
      <el-button
        size="small"
        type="primary"
        @click="onSubmit('createdForm')"
      >提交</el-button>
      <el-button
        size="small"
        @click="goBack"
      >
        取消
      </el-button>
    </div>
    <el-dialog
      title="查看已选商品"
      :close-on-click-modal="false"
      :visible.sync="addGoodVisible"
      class="my-dialog"
      width="980px"
    >
      <div class="explain-search my-label">
        <el-form
          size="small"
          :inline="true"
        >
          <el-form-item class="my-label">
            <el-input
              v-model.trim="selectGoodSearch.barcode"
              placeholder="请输入"
              class="search-input"
              style="width: 227px"
            >
              <div slot="prepend">
                商品编码
              </div>
            </el-input>
          </el-form-item>
          <el-form-item class="my-label">
            <el-input
              v-model.trim="selectGoodSearch.showName"
              placeholder="请输入"
              class="search-input"
              style="width: 227px"
            >
              <div slot="prepend">
                商品名称
              </div>
            </el-input>
          </el-form-item>
          <el-form-item
            class="search-btn"
            style="float: right"
          >
            <el-button
              type="primary"
              @click="onSearchGood('search')"
            >
              查询
            </el-button>
            <el-button @click="restSelectGood">
              重置
            </el-button>
          </el-form-item>
        </el-form>
      </div>
      <div
        class="explain-table"
        style="padding-bottom: 0"
      >
        <el-table
          :key="Math.random()"
          class="my-table seGood-table"
          :data="lookListData.list"
          stripe
          :row-class-name="tableRowClass"
          max-height="400px"
          :header-cell-style="{ background: '#F9F9F9', color: '#666666' }"
          style="width: 100%"
        >
          <el-table-column
            property="barcode"
            label="商品编码"
          />
          <el-table-column
            property="showName"
            label="商品名称"
          />
          <el-table-column
            property="fob"
            label="售价"
            width="120"
          />
          <el-table-column
            label="状态"
            width="120"
          >
            <template slot-scope="scope">
              <span v-if="scope.row.status == 1">销售中</span>
              <span v-else-if="scope.row.status == 2">已售罄</span>
              <span v-else-if="scope.row.status == 4">下架</span>
              <span v-else-if="scope.row.status == 6">待上架</span>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="explain-pag">
        <Pagination
          v-show="lookListData.totalCount > 0"
          :total="Number(lookListData.totalCount)"
          :page.sync="selectGoodPage.pageNum"
          :limit.sync="selectGoodPage.pageSize"
          @pagination="onSearchGood"
        />
      </div>
      <div
        slot="footer"
        class="dialog-footer"
      >
        <el-button
          type="primary"
          size="small"
          @click="queryLook"
        >
          确 定
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import Pagination from '@/components/Pagination/index.vue';

import {
  productName,
  couponDetail,
  couponSave,
} from '@/api/coupon/index';

export default {
  name: 'CouponDetail',
  components: { Pagination },
  filters: {
    handleTime(time) {
      return dayjs(time).format('YYYY-MM-DD HH:mm:ss');
    },
  },
  data() {
    return {
      type: '',
      isLoading: false,
      detailData: {
        // couponBasicTemplate: {
        //   discount: "",
        //   endTime: "",
        //   minMoneyToEnable: null,
        //   moneyInVoucher: null,
        //   name: "",
        //   reduceType: null,
        //   startTime: "",
        // },
        // couponStock: { totalCount: null },
        // shopActivity: {
        //   scopeType: null,
        // },
      },
      addGoodVisible: false,
      selectGoodSearch: {
        barcode: '',
        showName: '',
      },
      selectGoodPage: {
        pageNum: 1,
        pageSize: 10,
      },
      lookListData: {},
      csuIds: [],
    };
  },

  // created() {
  //   this.getListNews();
  // },
  activated() {
    if (this.$route.query.id) {
      this.getListNews();
    }
    this.type = this.$route.query.type || '';
  },
  methods: {
    /* 提交信息 */
    onSubmit() {
      const param = {
        ...this.detailData,
        csuIds: this.csuIds, // 关联的商品id全场商品时，此字段不填。指定时，必填。
        // totalCount: Number(sendData.couponStock.totalCount), //总库存
      };
      couponSave(param).then((res) => {
        if (res.status === 'success') {
          this.$message({
            message: '修改成功',
            type: 'success',
          });
          if(this.$store.state.permission.menuGray == 1) {
            this.$router.replace({
              path: '/storeVoucher',
              query: { refresh: true,to: "couponList" },
            });
          }else {
            this.$router.replace({
              path: '/couponList',
              query: { refresh: true },
            });
          }
        } else {
          this.$message({
            message: res.msg,
            type: 'error',
          });
        }
      });
    },
    goBack() {
      if(this.$store.state.permission.menuGray == 1) {
        this.$router.replace({
          path: '/storeVoucher',
          query: {to: 'couponList'}
        })
        return
      }
      window.history.go(-1);
    },
    downLoadExcelMode() {
      window.open('/promo/coupon/downloadImportSkuTemplate');
    },
    uploadFunction() {
      let baseURL = '';
      switch (__env__) {
        case 'devlpoment':
          baseURL = '/api'
          // baseURL = 'http://192.168.128.34:8091'
          break
        case 'dev':
          baseURL = 'https://pop.dev.ybm100.com'
          break
        case 'test':
          baseURL = 'https://pop.test.ybm100.com'
          break
        case 'stage':
          baseURL = 'https://pop-new.stage.ybm100.com'
          break
        case 'prod':
          baseURL = 'https://pop.ybm100.com'
          break
      }
      return baseURL + '/promo/coupon/importProduct'
    },
    uploadSucces(res) {
      if (res.code === 1000) {
        res.data.shopSkuVos.forEach((item) => {
          if (item.id && !this.csuIds.includes(item.id)) {
            this.csuIds.push(Number(item.id));
          }
        });
        let con = '';
        const importResult = res.data.importResult || {};
        if (importResult.url) {
          con = `<p>导入成功${importResult.successNum}条商品，失败${importResult.failNum}条，失败原因请下载错误文件：<br><a style="color: #4184D5" href="${importResult.url}" download="下载错误文件">下载错误文件</a></p>`;
        } else {
          con = `<p>导入成功${importResult.successNum}条，失败${importResult.failNum}条</p>`;
        }
        this.$confirm(con, '提示', {
          confirmButtonText: '确定',
          dangerouslyUseHTMLString: true,
          cancelButtonText: '取消',
        }).then(() => {});
      } else {
        this.$message({
          message: res.msg,
          type: 'error',
        });
      }
      this.$refs.upload.clearFiles();
    },
    handleTimeFormat(time) {
      return dayjs(time).format('YYYY-MM-DD HH:mm:ss');
    },
    getListNews() {
      // this.isLoading = true;
      if (this.$route.query.id) {
        const { id } = this.$route.query;
        couponDetail({ id })
          .then((res) => {
            this.isLoading = false;
            if (res.code == 1000) {
              this.detailData = res.data.detail;
              this.csuIds = res.data.detail.csuIds;
            } else {
              this.$message({
                message: res.msg,
                type: 'error',
              });
            }
          })
          .catch((error) => {
            console.log(error);
          });
      }
    },
    showGoodList() {
      this.addGoodVisible = true;
      this.onSearchGood();
    },

    restSelectGood() {
      this.selectGoodSearch = {
        barcode: '',
        showName: '',
      };
      this.selectGoodPage.pageNum = 1;
      this.onSearchGood();
    },
    queryLook() {
      this.selectGoodSearch.showName = '';
      this.selectGoodSearch.barcode = '';
      this.onSearchGood();
      this.addGoodVisible = false;
    },
    onSearchGood(from) {
      const param = {
        templateId: this.$route.query.id,
        pageNum: from && from.page ? from.page : 1,
        pageSize: from && from.limit ? from.limit : 10,
        productName: this.selectGoodSearch.showName,
        barcode: this.selectGoodSearch.barcode,
      };
      productName(param)
        .then((res) => {
          if (res.status === 'success') {
            this.lookListData = res.data;
            this.selectGoodPage.pageNum = res.data.pageNo;
          } else {
            this.$message({
              message: res.msg,
              type: 'error',
            });
          }
        })
        .catch((error) => {
          console.log(error);
        });
    },
    /* table颜色 */
    tableRowClass({ rowIndex }) {
      if (rowIndex % 2 !== 1) {
        return '';
      }
      return 'success-row';
    },
    filterScopeType(type) {
      let str = '';
      switch (type) {
        case 0:
          str = '全店商品';
          break;
        case 1:
          str = '指定商品';
          break;
        case 2:
          str = '指定商品不参加';
          break;
        default:
          str = '';
          break;
      }
      return str;
    }
  },
};
</script>

<style lang="scss" scoped>
@import '../../assets/css/market.css';
.list-box{
  margin-top: 0;
}
.list-box .sample-box{
  top: 15px;
}
.main-box{
  padding-top: 15px;
}
.info-box {
  padding: 5px 15px;

  .info-item {
    padding: 5px;
    padding-left: 20px;
    // width: 480px;

    p {
      padding: 0;
      margin: 0;
      display: inline-block;
      font-size: 14px;
      color: #666666;
      vertical-align: text-top;
    }

    .info-label {
      // width: 90px;
      padding-right: 10px;
      text-align: right;

      .start-span {
        color: #f5222d;
        vertical-align: middle;
        padding-right: 5px;
      }
    }

    .lookBtn {
      vertical-align: text-top !important;
    }

    .info-con {
      width: 75%;
      vertical-align: text-top;
    }
  }
}

.el-table .success-row {
  background: #f9f9f9;
}
</style>
