import request from '@/utils/request';

/**
 * 采购总金额
 * @param {params}查询参数
 */
export function apiQuantity(params) {
  return request({
    url: '/sales/report/quantity',
    method: 'get',
    params,
  });
}
/**
 * 净采购总额
 * @param {params}查询参数
 */
export function apiPurchase(params) {
  return request({
    url: '/sales/report/purchase',
    method: 'get',
    params,
  });
}
/**
 * 销售订单数量
 * @param {params}查询参数
 */
export function apiOrderNum(params) {
  return request({
    url: '/sales/report/order/num',
    method: 'get',
    params,
  });
}
/**
 * 采购药店数量
 * @param {params}查询参数
 */
export function apiDrugstoreNum(params) {
  return request({
    url: '/sales/report/drugstore/num',
    method: 'get',
    params,
  });
}
/**
 * 新增采购药店数量
 * @param {params}查询参数
 */
export function apiDrugstoreNumNew(params) {
  return request({
    url: '/sales/report/drugstore/num/new',
    method: 'get',
    params,
  });
}
/**
 * 店铺优惠券金额
 * @param {params}查询参数
 */
export function apiShopCouponAmount(params) {
  return request({
    url: '/sales/report/shop/coupon/amount',
    method: 'get',
    params,
  });
}
/**
 * 拼团活动采购药店数量
 * @param {params}查询参数
 */
export function apiCollageDrugstoreNum(params) {
  return request({
    url: '/sales/report/collage/drugstore/num',
    method: 'get',
    params,
  });
}
/**
 * 拼团活动净采购金额
 * @param {params}查询参数
 */
export function apiCollageNetPurchase(params) {
  return request({
    url: '/sales/report/collage/net/purchase',
    method: 'get',
    params,
  });
}
/**
 * 导出销售报表数据
 * @param {params}查询参数
 */
export function apiChartExport(params) {
  return request({
    url: '/sales/report/export',
    method: 'get',
    params,
  });
}

// 省市区三级联动
export function getRegionList(params) {
  return request({
    url: '/regionList',
    method: 'get',
    params,
  });
}
// 没有后缀省
export function getRegionListNoSuffix() {
  return request({
    url: '/product/attributeOptions?fieldType=provinceNames',
    method: 'get'
  });
}
// 国外枚举
export function getForeignEnum() {
  return request({
    url: '/product/attributeOptions?fieldType=countryNames',
    method: 'get'
  });
}
// 首页查询是否创建店铺
export function apiCreateShop(params) {
  return request({
    url: '/index/v2/isCreateShop',
    method: 'get',
    params,
  });
}
/**
 * 查询48小时发货率
 * @param {params}查询参数
 */
export function apiDeliveryRate(params) {
  return request({
    url: '/popSellerService/get48SendNum',
    method: 'post',
    data: params,
  });
}
/**
 * 查询商家退款率
 * @param {params}查询参数
 */
export function apiRefundRate(params) {
  return request({
    url: '/popSellerService/getRefundMoneyOrder',
    method: 'post',
    data: params,
  });
}
/**
 * 查询商家退货率
 * @param {params}查询参数
 */
export function apiReturnRate(params) {
  return request({
    url: '/popSellerService/getRefundGoodsOrder',
    method: 'post',
    data: params,
  });
}
/**
 * 获取客服响应率
 * @param {params}查询参数
 */
export function apiResponseRate(params) {
  return request({
    url: '/popSellerService/getServiceResponseRate',
    method: 'post',
    data: params,
  });
}
/**
 * 导出服务质量监控数据
 * @param {params}查询参数
 */
export function apiServiceExport(params) {
  return request({
    url: '/popSellerService/exportSellerService',
    method: 'post',
    data: params,
  });
}
/**
 * 获取发票售后率
 * @param {*} params
 * @returns
 */
export function getInvoiceAfterSale(params) {
  return request({
    url: '/popSellerService/queryInvoiceAsRatio',
    method: 'post',
    data: params,
  });
}
/**
 * 获取资质售后率
 * @param {*} params
 * @returns
 */
export function getQuaAfterSale(params) {
  return request({
    url: '/popSellerService/queryCredentialAsRatio',
    method: 'post',
    data: params,
  });
}

/**
 * 获取客服响应时长
 * @param {*} params
 * @returns
 */
export function querykfResponseTime(params) {
  return request({
    url: '/popSellerService/querykfResponseTime',
    method: 'post',
    data: params,
  });
}
