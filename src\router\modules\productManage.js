import layout from '@/layout';

const productManage = {
  path: '/productManage',
  name: 'productManage',
  component: layout,
  meta: {
    title: '商品管理',
    icon: 'el-icon-s-goods',
  },
  children: [
    {
      path: '/support',
      name: 'support',
      component: () => import('@/views/support/index'),
      meta: { title: '自营商品列表' },
    },
    {
      path: '/productList',
      name: 'productList',
      component: () => import('@/views/product/index'),
      meta: { title: '商品列表' },
    },
    {
      path: '/productList',
      name: 'productList',
      component: () => import('@/views/product/index'),
      meta: { title: '商品上架' }, // 菜单改版
    },
    {
      path: '/priceUserGroup',
      name: 'priceUserGroup',
      component: () => import('@/views/control-goods/priceUserGroup.vue'),
      meta: { title: '分区域定价' },
    },
    {
      path: '/product/errorEditList',
      name: 'errorEditList',
      component: () => import('@/views/product/release/errorEditList'),
      meta: { title: '商品纠错' },
    },
    {
      path: '/auctionProductManagement',
      name: 'auctionProductManagement',
      component: () => import('@/views/product/auctionProductManagement'),
      meta: { title: '竞价商品管理' },
      hidden: true,
    },
    {
      path: `${process.env.VUE_APP_BASE_API}/certificate/list`,
      name: 'certificateList',
      meta: { title: '证书管理' },
    },
    {
      path: '/controlIndex',
      name: 'controlIndex',
      component: () => import('@/views/control-goods/controlIndex.vue'),
      meta: { title: '控销管理' },
      hidden: true,
    },
    {
      path: '/controlUserGroup',
      name: 'controlUserGroup',
      component: () => import('@/views/control-goods/controlUserGroup.vue'),
      meta: { title: '控销用户组' },
    },
    {
      path: '/addControlGroup',
      name: 'addControlGroup',
      component: () => import('@/views/control-goods/addControlGroup.vue'),
      hidden: true,
      meta: { noCache: true, title: '控销用户组' },
    },
    {
      path: '/checkControlGroup',
      name: 'checkControlGroup',
      component: () => import('@/views/control-goods/checkControlGroup.vue'),
      hidden: true,
    },
    {
      path: '/productPreview',
      name: 'productPreview',
      component: () => import('@/views/product/preview'),
      hidden: true,
    },
    {
      path: '/releaseShop',
      name: 'releaseShop',
      component: () => import('@/views/product/release/releaseShop'),
      meta: { title: '发布商品' },
      hidden: true,
    },
    {
      path: '/wholesale',
      name: 'wholesale',
      component: () => import('@/views/product/release/wholesale'),
      meta: { title: '发布商品' },
      hidden: true,
    },
    {
      path: '/singleApply',
      name: 'singleApply',
      component: () => import('@/views/product/release/singleApply'),
      meta: { title: '发布商品' },
      hidden: true,
    },
    {
      path: '/batchGoods',
      name: 'batchGoods',
      component: () => import('@/views/product/batchGoods'),
      meta: { title: '批量发布商品' },
      hidden: true,
    },
    {
      path: `${process.env.VUE_APP_BASE_API}/product/newProductIndex`,
      name: 'newProductIndex',
      meta: { title: '发布商品' },
      hidden: true,
    },
    {
      path: '/product/initialize',
      name: 'newProductIndex',
      component: () => import('@/views/product/release/initialize'),
      meta: { title: '发布商品', noCache: true },
      hidden: true,
    },
    {
      path: '/product/details',
      name: 'details',
      component: () => import('@/views/product/release/details'),
      meta: { title: '商品详情' },
      hidden: true,
    },
    {
      path: '/product/detailsEdit',
      name: 'details',
      component: () => import('@/views/product/release/details'),
      meta: { title: '商品编辑', keepalive: true },
      hidden: true
    },
    {
      path: '/product/putOnShelvesSetting',
      name: 'putOnShelvesSetting',
      component: () => import('@/views/product/release/putOnShelvesSetting'),
      meta: { title: '商品上架设置' },
      hidden: true
    },
    {
      path: '/product/errorEdit',
      name: 'errorEdit',
      component: () => import('@/views/product/release/errorEdit'),
      meta: { title: '商品纠错' },
      hidden: true
    },
    {
      path: '/product/productTimeSetting',
      name: 'productTimeSetting',
      component: () => import('@/views/product/components/productTimeSetting'),
      meta: { title: '商品售卖时间设置' },
      hidden: true,
    },
    {
      path: '/putProduct',
      name: 'putProduct',
      component: () => import('@/views/putProduct/index'),
      meta: { title: '发布商品' },
      hidden: true,
    },
    {
      path: '/putProduct/multiple',
      name: 'putProduct-multiple',
      component: () => import('@/views/putProduct/multiplePurchase/index'),
      meta: { title: '发布批购包邮商品' },
      hidden: true,
    },
    {
      path: '/putProduct/single',
      name: 'putProduct-single',
      component: () => import('@/views/putProduct/singlePurchase/index'),
      meta: { title: '编辑批购包邮商品', noCache: true },
      // meta: { title: '编辑批购包邮商品', keepalive: true }, // 修复缓存不重新请求
      hidden: true,
    },

    // 自营商品列表路由
    {
      path: '/support/productPreview',
      name: 'productPreview',
      component: () => import('@/views/support/preview'),
      hidden: true,
    },
    {
      path: '/support/details',
      name: 'details',
      component: () => import('@/views/support/release/details'),
      meta: { title: '商品详情' },
      hidden: true,
    },
    {
      path: '/support/detailsEdit',
      name: 'details',
      component: () => import('@/views/support/release/details'),
      meta: { title: '商品编辑' },
      hidden: true,
    }, {
      path: '/support/productTimeSetting',
      name: 'productTimeSetting',
      component: () => import('@/views/support/components/productTimeSetting'),
      meta: { title: '商品售卖时间设置' },
      hidden: true,
    },
	{
		path: '/productIssueSet',
		name: 'productIssueSet',
		component: () => import("@/views/product/productIssueSet"),
		meta: { title: '商品上架设置' },
		hidden: true
	},
  {
    path: '/priceUserGroup',
    name: 'priceUserGroup',
    component: () => import('@/views/control-goods/priceUserGroup.vue'),
    meta: { title: '价格用户组' },
  },
  {
    path: '/productInfoChange',
    name: 'productInfoChange',
    component: () => import('@/views/product/productInfoChange.vue'),
    meta: { title: '标品信息变更', showRedDotNum: true, redDotNumKey: 'productsInconsistent' }
  },
  ]
}
export default productManage
