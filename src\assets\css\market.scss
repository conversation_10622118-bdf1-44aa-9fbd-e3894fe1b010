.crumbs {
  //width: 100%;
  height:40px;
  line-height: 40px;
  background:rgba(255,255,255,1);
  border-radius:4px;
  padding-left: 14px;
  font-size: 12px;
  color: #333333;
  .textColor{
    color: #999999;
  }
  a{
    color: #333333;
    text-decoration: none;
  }
}
.con-title{
  padding-left: 15px;
  color: #000000;
  font-size: 14px;
  height: 38px;
  line-height: 38px;
  span{
    display: inline-block;
    vertical-align: middle;
    font-weight: bold;
  }
  .line{
    width:3px;
    height:13px;
    background:linear-gradient(360deg,rgba(29,105,196,1) 0%,rgba(139,189,252,1) 100%);
    border-radius:2px;
    margin-right: 8px;
  }
}
.list-box{
  margin-top: 16px;
  background: #ffffff;
  padding-bottom: 10px;
}
.explain-search{
  padding: 10px 15px 0;
}
.explain-table{
  padding: 0 15px 20px;
}
.noData{
  text-align: center;
  padding: 60px 0;
  .img-box{
    width: 288px;
    height: 170px;
    display: inline-block;
    padding: 0;
    margin: 0;
    img{
      width: 100%;
      height: 100%;
    }
  }
  p{
    line-height: 48px;
    font-size: 14px;
    color: #333333;
    padding: 0;
  }
}
.lookBtn{
  display: inline-block !important;
  vertical-align: middle !important;
  padding-left: 15px !important;
  color: #4183D5 !important;
  cursor: pointer !important;
  font-size: 12px !important;
}
.list-box{
  position: relative;
  .sample-box{
    position: absolute;
    top: 50px;
    right: 60px;
    h4{
      font-size: 14px;
      color: #333333;
      padding-left: 20px;
    }
    p{
      width: 347px;
      height: 200px;
      img{
        width: 100%;
        height: 100%;
      }
    }
  }
}
.box-flex {
  display: -webkit-box;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  -webkit-flex-wrap: nowrap;
  -ms-flex-wrap: nowrap;
  flex-wrap: nowrap;
}
