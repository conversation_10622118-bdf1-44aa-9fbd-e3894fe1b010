<template>
  <div
    ref="carousel"
    class="carousel-box"
  >
    <div
      ref="previewImg"
      class="preview-img"
    >
      <img :src="previewImg">
    </div>
    <div
      ref="swiperBox"
      class="swiper-box"
    >
      <swiper
        ref="swiper"
        :options="options"
        @click-slide="setImg"
      >
        <swiper-slide
          v-for="(img, i) in imgs"
          :key="i"
        >
          <img :src="img">
        </swiper-slide>
      </swiper>
      <div
        class="swiper-button-prev"
        @click="prev"
      />
      <div
        class="swiper-button-next"
        @click="next"
      />
    </div>
  </div>
</template>

<script>
import { Swiper, SwiperSlide } from 'vue-awesome-swiper';
import 'swiper/swiper-bundle.css';

export default {
  components: {
    Swiper,
    SwiperSlide,
  },
  props: {
    imgs: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      options: {
        slidesPerView: 4,
        spaceBetween: 15,
        navigation: {
          nextEl: '.swiper-button-next',
          prevEl: '.swiper-button-prev',
        },
      },
      previewImg: '',
    };
  },
  watch: {
    imgs(arr) {
      if (arr && arr.length) {
        [this.previewImg] = arr;
      }
    },
  },
  created() {
    // ;[this.previewImg] = this.imgs
    this.$nextTick(() => {
      // console.log(this.$refs.carousel.clientWidth)
      const width = this.$refs.carousel.clientWidth;
      this.$refs.previewImg.style.height = width;
      this.$refs.swiperBox.style.height = (width / 4).toFixed(0);
    });
    // console.log(this.previewImg)
  },
  methods: {
    prev() {
      this.$refs.swiper.$swiper.slidePrev();
    },
    next() {
      this.$refs.swiper.$swiper.slideNext();
    },
    setImg(index) {
      this.previewImg = this.imgs[index];
    },
  },
};
</script>

<style lang="scss" scoped>
.carousel-box {
  width: 100%;
  margin: 0 auto;
  .preview-img {
    width: 100%;
    // height: 360px;
    box-sizing: border-box;
    padding: 10px;
    img {
      width: 100%;
      height: 100%;
    }
  }
  .swiper-box {
    width: 100%;
    // height: 55px;
    position: relative;
    .swiper-container {
      width: calc(100% - 90px);
      height: 100%;
      img {
        width: 100%;
        height: 100%;
      }
    }
    .swiper-button-next,
    .swiper-button-prev {
      color: #e8e8e8;
    }
  }
}
</style>
