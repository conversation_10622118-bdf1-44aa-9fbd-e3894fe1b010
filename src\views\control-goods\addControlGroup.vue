<template>
  <div class="controlGroupWrpper">
    <div class="headerBox">
      <part-title title="新增控销组" />
      <el-button type="primary" size="small" @click="goBack">返回</el-button>
    </div>

    <el-form
      ref="basicRef"
      label-width="100px"
      size="small"
      label-position="right"
      :rules="basicRules"
      :model="basic"
      style="width: 500px"
    >
    <el-form-item label="控销组名称" prop="name" ref="name">
      <div class="name-input-wrapper">
        <el-input type="text" maxlength="50" v-model.trim="basic.name" placeholder="输入控销组名称"></el-input>
        <el-button v-if="$route.query.groupId" size="small" type="primary" :loading="btnLoading" @click="submit()">提交</el-button>
      </div>
    </el-form-item>
    </el-form>
    <div class="list-box">
      <div class="text"><span style="color: red">*</span>导入药店：</div>
      <div style="display: flex; padding-bottom: 10px; align-items: flex-start">
        <el-button type="primary" size="small" style="margin-right: 10px" @click="showUploadDialog">
          导入excel文件
        </el-button>
        <el-upload
          action="xxx"
          ref="excludeImport"
          :http-request="uploadFile"
          :show-file-list="false"
          accept=".xls, .xlsx, .XLS, .XLSX"
          style="display: none"
        >
        </el-upload>
        <el-button size="small" @click="downloadTemp">下载模板</el-button>
      </div>
    </div>

    <div class="searchMy">
      <el-form ref="ruleForm" size="small" :inline="true">
        <el-form-item prop="skuId">
          <el-input v-model.trim="searchData.merchantId" oninput="value=value.replace(/[^0-9.]/g,'')" placeholder="药店ID">
            <template slot="prepend">药店ID</template>
          </el-input>
        </el-form-item>
        <el-form-item>
          <el-input v-model.trim="searchData.merchantName" placeholder="药店名称">
            <template slot="prepend">药店名称</template>
          </el-input>
        </el-form-item>
        <el-form-item>
          <el-input v-model.trim="searchData.merchantMobile" oninput="value=value.replace(/[^0-9.]/g,'')" placeholder="手机号">
            <template slot="prepend">手机号</template>
          </el-input>
        </el-form-item>
        <el-form-item class="search-btn">
          <el-button type="primary" @click="getInfo('search')">查询</el-button>
          <el-button @click="resetForm()">重置</el-button>
        </el-form-item>
      </el-form>
    </div>


    <div class="table-box">
      <el-button type="primary" size="mini" style="margin-right:10px;" @click="delBetch">批量删除</el-button>
      <addCustomer 
  :value="merchantIds.map(val => {return { merchantId: val }})" 
  :maxCount="1000" 
  @input="handleAddCustomer">
      </addCustomer>
      <div style="padding: 10px 0px">
        <div class="customer-tabs">
          <el-table
            ref="goodTable"
            v-loading="laodingBoole"
            max-height="397"
            :data="tableData.list"
            stripe
            style="width: 100%"
            :header-cell-style="{ background: '#F9F9F9', color: '#666666' }"
            @selection-change="handleSelectionChange"
          >
            <el-table-column type="selection" width="45" />
            <el-table-column prop="merchantId" label="药店编号" />
            <el-table-column prop="merchantName" label="药店名称" />
            <el-table-column prop="merchantMobile" label="手机号" />
            <el-table-column fixed="right" label="操作" width="100">
              <template slot-scope="scope">
                <el-button type="text" size="small" @click="deleteMerchant(scope.row)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
          <div class="explain-pag">
            <Pagination
              v-show="tableData.total > 0"
              :total="tableData.total"
              :page.sync="pageData.pageNum"
              :limit.sync="pageData.pageSize"
              @pagination="getInfo"
            />
          </div>
        </div>
      </div>
    </div>
    <el-button v-if="!$route.query.groupId" size="small" @click="goBack">取消</el-button>
    <el-button v-if="!$route.query.groupId" size="small" type="primary" :loading="btnLoading" @click="submit()">提交</el-button>
  </div>
</template>
<script>
import Pagination from '@/components/Pagination';
import partTitle from '@/components/part-title/index';
import { update, addGroupRelation, merchantPage, saveMerchantGroup, importMerchants, importMerchantsV2, getMerchantInfo, detailWithMerchant, deleteMerchantGroup } from '@/api/goods/controlGoods.js';
import addCustomer from '../customer-management/components/addCustomer.vue';

export default {
  name: 'AddControlGroup',
  components: {
    Pagination,
    partTitle,
    addCustomer
  },
  data() {
    return {
      searchData: {
        merchantId: '',
        merchantMobile: '',
        merchantName: '',
      },
      tableData: {
        total: 0,
        list: [],
      },
      pageData: {
        pageSize: 10,
        pageNum: 1,
      },
      selectedRows: [],
      laodingBoole: false,
      btnLoading: false,
      basic: { name: '' },
      detailMerchantIDs: [],
      successMerchantIds: [],
      merchantIds: [],
      basicRules: {
        name: [
          { required: true, message: '请输入控销组名称', trigger: 'blur' },
        ],
      },
    };
  },
  created() {
    if (this.$route.query.groupId) {
      this.getDetail();
    } else {
      this.merchantIds = [];
      this.basic.name = '';
      this.resetForm();
    }
  },
  activated() {
    if (this.$route.query.groupId) {
      this.getDetail();
    } else {
      this.merchantIds = [];
      this.basic.name = '';
      this.resetForm();
    }
  },
  methods: {
    getDetail() {
    detailWithMerchant({ id: this.$route.query.groupId }).then((res) => {
      if (res.code === 0) {
        this.basic.name = res.data.name;
        this.merchantIds = res.data.merchantIds || [];
        this.getInfo('init'); 
      }
    });
  },
  showUploadDialog() {
  this.$confirm('1.您可在”客户管理“”客户列表“查询药店ID<br>2.支持上传xlsx,xls文件，大小不超过1M', '提示', {
    dangerouslyUseHTMLString: true,
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'info'
  }).then(() => {
    this.$refs.excludeImport.$el.querySelector('input').click();
  }).catch(() => {
  });
},

uploadFile(params) {
  const { file } = params;
  if (!file) {
    this.$message.warning('请选择要上传的文件');
    return;
  }
  
  if (file.size > 1 * 1024 * 1024) {
    this.$message.error('文件大小不能超过1M');
    return;
  }
  
  const loading = this.$loading({
    lock: true,
    text: 'Loading',
    spinner: 'el-icon-loading',
    background: 'rgba(255,255,255, 0.8)',
  });
  
  const importFunction = this.$route.query.groupId 
    ? importMerchantsV2({ file, groupId: this.$route.query.groupId })
    : importMerchants({ file, successMerchantIds: this.merchantIds });
  
  importFunction.then((res) => {
    loading.close();
    if (res.code === 0) {
      this.merchantIds = res.data.successMerchantIds || [];
      let con = '';
      if (res.data.error > 0) {
        con = `<p>${res.data.success}个导入成功，${res.data.error}个导入失败，失败原因请下载错误文件：<br><a style="color: #ff0021" href="${res.data.errorFileUrl}" download="${res.data.errorFileName}">${res.data.errorFileName}</a></p>`;
      } else {
        con = `<p>${res.data.success}个导入成功，${res.data.error}个导入失败。</p>`;
      }
      this.$confirm(con, '提示', {
        confirmButtonText: '确定',
        dangerouslyUseHTMLString: true,
        cancelButtonText: '取消',
      }).then(() => {
        this.getInfo();
      }).catch(() => {});
    } else {
      this.$message.error(res.msg || res.message || '请求失败');
    }
  }).catch((error) => {
    loading.close();
    this.$message({
      message: error.message || '请求失败',
      type: 'error',
    });
  });
  },
    downloadTemp() {
      window.open('/uploadFile/downloadTemplate?fileName=批量添加药店名单.xlsx');
    },
    getInfo(from) {
    this.laodingBoole = true;
    
    if (from === 'search') {
      this.pageData.pageNum = 1;
    }
    
    if (this.$route.query.groupId) {
      merchantPage({
        groupId: this.$route.query.groupId,
        pageNum: this.pageData.pageNum,
        pageSize: this.pageData.pageSize,
        ...(from === 'search' ? this.searchData : {}) 
      }).then((res) => {
        this.laodingBoole = false;
        if (res.code === 0) {
          this.tableData.list = res.data.list || [];
          this.tableData.total = res.data.total;
        } else {
          this.$message.error(res.message || '查询失败');
        }
      }).catch(() => {
        this.laodingBoole = false;
      });
    } else {
      getMerchantInfo({
        merchantIds: this.merchantIds,
        ...this.searchData,
        ...this.pageData,
      }).then((res) => {
        this.laodingBoole = false;
        if (res.code === 0) {
          this.tableData.list = res.data.list || [];
          this.tableData.total = res.data.total;
        }
      }).catch(() => {
        this.laodingBoole = false;
      });
    }
  },
    resetForm() {
      this.searchData = {
        merchantId: '',
        merchantMobile: '',
        merchantName: '',
      };
      this.getInfo();
    },
delBetch() {
  if (this.selectedRows.length === 0) {
    this.$message.error('请选择要删除的数据');
    return;
  }
  const ids = this.selectedRows.map(item => item.merchantId);
  if (this.$route.query.groupId) {
    deleteMerchantGroup({
      merchantIds: ids,
      id: this.$route.query.groupId
    }).then(res => {
      if (res.code === 0) {
        this.merchantIds = this.merchantIds.filter(item => !ids.includes(item));
        this.selectedRows = [];
        this.getInfo();
        this.$message.success('移除成功');
      } else {
        this.$message.error(res.message || '删除失败');
      }
    });
  } else {
    this.merchantIds = this.merchantIds.filter(item => !ids.includes(item));
    this.selectedRows = [];
    this.getInfo();
    this.$message.success('移除成功');
  }
},
deleteMerchant(row) {
  if (this.$route.query.groupId) {
    deleteMerchantGroup({
      merchantIds: [row.merchantId],
      id: this.$route.query.groupId
    }).then(res => {
      if (res.code === 0) {
        const idx = this.merchantIds.findIndex(i => i == row.merchantId);
        this.merchantIds.splice(idx, 1);
        this.getInfo();
        this.$message.success('移除成功');
      } else {
        this.$message.error(res.message || '删除失败');
      }
    });
  } else {
    const idx = this.merchantIds.findIndex(i => i == row.merchantId);
    this.merchantIds.splice(idx, 1);
    this.getInfo();
    this.$message.success('移除成功');
  }
},
    handleAddCustomer(list) {
  const newMerchantIds = list.map(item => item.merchantId);
  
  const addedMerchantIds = newMerchantIds.filter(id => !this.merchantIds.includes(id));
  
  if (addedMerchantIds.length > 0 && this.$route.query.groupId) {
    addGroupRelation({
      id: this.$route.query.groupId,
      merchantIds: addedMerchantIds
    }).then(res => {
      if (res.code === 0) {
        this.merchantIds = newMerchantIds;
        this.getInfo();
        this.$message.success('添加成功');
      } else {
        this.$message.error(res.message || '添加失败');
      }
    });
  } else {
    this.merchantIds = newMerchantIds;
    this.getInfo();
  }
},
    handleSelectionChange(rows) {
      this.selectedRows = rows;
    },
    goBack() {
      this.$router.go(-1);
    },
    submit() {
      this.btnLoading = true;
      const id = this.$route.query.groupId;
      
      if (id) {
        update({
          name: this.basic.name,
          id
        }).then((res) => {
          this.btnLoading = false;
          if (res.code === 0) {
            this.$message.success('修改成功');
            setTimeout(() => {
              this.$router.push('/controlUserGroup');
            }, 500);
          } else {
            this.$message.error(res.message || '名称修改失败');
          }
        }).catch(() => {
          this.btnLoading = false;
        });
      } else {
        saveMerchantGroup({
          name: this.basic.name,
          merchantIds: this.merchantIds,
        }).then((res) => {
          this.btnLoading = false;
          if (res.code === 0) {
            this.$message.success('提交成功');
            setTimeout(() => {
              this.$router.push('/controlUserGroup');
            }, 500);
          } else {
            this.$message.error(res.message);
          }
        }).catch(() => {
          this.btnLoading = false;
        });
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.controlGroupWrpper {
  padding: 20px;
  .headerBox {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
}
.searchMy {
  margin-top: 30px;
  border-top: 1px solid #EBEEF5;
  padding-top: 20px;
}
.searchMy ::v-deep  .el-form-item__label {
  margin-left: 20px;
  padding: 0;
}
.searchMy ::v-deep  .el-input__inner {
  border-radius: 0 4px 4px 0;
}
.searchMy ::v-deep  .el-form-item__content {
  width: 100%;
}
.searchMy ::v-deep  .el-form-item--small.el-form-item {
  margin-bottom: 10px;
  width: 32%;
}
.searchMy ::v-deep  .el-form-item--small .el-form-item__content {
  line-height: 30px;
  width: 100%;
}
.searchMy ::v-deep  .el-input-group__prepend {
  background: none;
  color: #333333;
  padding: 0 10px;
}
.searchMy ::v-deep   .el-form-item--small.el-form-item {
  width: 24%;
}
.list-box {
  display: flex;
  align-items: center;
  padding-left: 20px;
  // margin: 10px;
  .text {
    font-size: 14px;
    font-weight: 600;
  }
}

.search-btn {
  width: 100% !important;
  text-align: right;
}
.name-input-wrapper {
  display: flex;
  align-items: center;
  
  .el-input {
    margin-right: 10px;
  }
}
</style>
