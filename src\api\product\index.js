import request from '../index'

import request1 from '@/utils/request'

/**
 * 获取商品详情
 * @param {参数} params
 */
export function getProductDetail(params) {
  return request.get('/product/skuInfo', params);
}
/**
 * 编辑商品必填字段
 * @param {参数} params
 */
export function getProductEditProp(params) {
  return request.get('/sku/getEditErpCheckFields', params);
}
/**
 * 添加商品必填字段
 * @param {参数} params
 */
export function addProductEditProp(params) {
  return request.get('/sku/getAddErpCheckFields', params);
}

/**
 * 是否立即发布下拉框
 * @param {参数} params
 */
export function getMatchResultType(params) {
  return request.get('/sku/getMatchResultType', params);
}

/**
 * 发布商品查询
 * @param {参数} params
 */
export function getQueryErpSku(params) {
  return request.get('/sku/queryErpSku', params);
}
/**
 * 商品列表
 * @param {参数} params
 */
export function getQueryProductList(params) {
  return request.get('/product/list', params);
}
/**
 * 恢复 erp 已删除商品
 * @param {参数} params
 */
export function getRecoverValid(params) {
  return request.post('/sku/recoverValid',null, {params});
}


// 批量发布商品页签数量
export function queryErpSkuCount(params) {
  return request.get('/sku/queryErpSkuCount', params);
}

/**
 * 上传文件
 * @param {参数} params
 */
export function uploadFile(from, goods, params) {
  let urlStr = '';
  if (from === 1) {
    if (goods === 'a') {
      urlStr = '/sku/importForStandard';
    } else {
      urlStr = '/sku/importFromExcel';
    }
  } else {
    urlStr = '/sku/batchUpdateImage';
  }
  return request.postFormData(urlStr, { file: params.raw });
}

/**
 * 导出文件
 * @param {参数} params
 */
export function getExport(params) {
  // return request.get('/sku/async/exportErpSku', params, {responseType: 'blob'});
  return request.get('/sku/async/exportErpSku', params);
}

/**
 * 一键发布商品
 * @param {参数} params
 */
export function pushListGoods(params) {
  return request.post('/sku/publish', params);
}

/**
 * 删除发布商品
 * @param {参数} params
 */
export function deleteListGoods(params) {
  return request.post('/sku/delete/sku', params);
}

/**
 * 获取选择商品列表
 * @param {参数} params
 */
export function getMathList(params) {
  return request.get('/sku/match/mult', params);
}

/**
 * 选择商品
 * @param {参数} params
 */
export function sendMathReSet(params) {
  return request.post(
    `/sku/match/mult/reSet?id=${params.id}&standardProductId=${params.standardProductId}`,
    params,
  );
}

/**
 * 验证是否可重新匹配
 * @param {参数} params
 */
export function isResetStatus(params) {
  return request.get('/sku/match/status', params);
}

/**
 * 重新匹配
 * @param {参数} params
 */
export function getResetDataList(params) {
  return request.post('/sku/match/restart', params);
}

/**
 * 获取数据
 * @param {参数} params
 */
export function getMatchCount(params) {
  return request.get('/sku/getMatchCount', params);
}

/**
 * 获取四级分类数据
 * @param {参数} params
 */
export function getShopClassList(params) {
  return request.get('/sku/category', params);
}

/**
 * 获取导入列表
 * @param {参数} params
 */
export function getGoodsExportHistory(params) {
  return request.get('/sku/standSkuMatchResult', params);
}

/**
 * 用标准库69码覆盖商家的69码
 */
export function resetCode() {
  return request.get('/sku/resetCode');
}


/**
 * 商品列表
 */
export function getProductList(params) {
  return request.get('/product/list', params);
}

/**
 * 修改商品库存
 */
export function updateStock(params) {
  return request.post('/product/updateStock', params);
}

/**
 * 修改商品价格
 */
export function updateAmount(params) {
  return request.post('/product/updateAmountV2', params);
}

/**
 * 查看审核不通过原因
 */
export function examineFailReason(params) {
  return request.post('/product/examineFailReasonV2?barcode=' + params);
}

/**
 * 商品上下架
 */
export function batchUpAndDown(params) {
  return request.post('/product/sku/batchUpAndDownV2', params);
}
export function batchUpAndDownMultipart(params) {
  return request.post('/sku/batchUpAndDown', params)
}
/**
 * 删除商品
 */
export function skuDelete(params) {
  return request.post('/product/sku/delete?barcode=' + params);
}

/**
 * 获取商品操作日志
 */
export function getSkulog(params) {
  return request.get('/product/findSkuOperationLog', params);
}

/**
 * 各状态商品数
 */
export function loadStatusCounts(params) {
  params.status == -99 ? params.status = '' : '';

  let url = '';
  for (var i in params) {
    url += i + '=' + params[i] + '&'
  }
  url = url.substring(0, url.length - 1);
  return request.post('/product/loadStatusCounts?' + url);
}

/**
 * 商品列表-信息统计
 */
export function statistics(params) {
  return request.get('/product/statistics', params);
}
/**
 * 批购包邮列表-信息统计
 */
export function pgbyStatistics(params) {
  return request.post('product/statisticsForPGBY', params);
}
/**
 * 商品分类-树型
 */
export function categoryTree(params) {
  return request.get('/product/categoryTree', params);
}

/**
 * 批量更新商品信息
 */
export function batchUpdateInfoFromExcel(params) {
  return request.post('/sku/batchUpdateInfoFromExcel', params);
}

/**
 * 商品列表下载
 */
export function exportSku(params) {
  return request.get('/sku/async/exportSku', params);
}

/**
 * 获取单位列表
 */
export function getUnitsList(params) {
  return request.get('/sku/productUnitList', params);
}

/**
 * 修改商品单位
 */
export function updateProductUnit(params) {
  return request.post('/sku/updateProductUnit', params);
}

export function findAct(params) {
  return request.get('/product/findAct', params);
}
/**
 * 商品改版获取商品选择列表
 */
export function meProduceList(params) {
  return request.get('/product/meProduceList', params);
}
/**
 * 商品改版获取商品分类
 */
export function levelCategory(params) {
  return request.get('/sku/levelCategory', params);
}

/**
 * 商品改版获取商品详情
 */
export function apiDetail(params) {
  return request.get('/product/detail', params);
}

/**
 * 商品改版-商圈查询接口
 */
export function apiPageQuery(params) {
  return request.postFormData('/busArea/pageQuery', params)
}

/**
 * 商品改版商品配置信息
 */
export function apiConfig(params) {
  return request.get('/product/config', params);
}

/**
 * 商品改版商品图片上传
 */
export function apiUploadProductImage(params) {
  return request.postFormData('/uploadFile/uploadProductImage', params);
}

/**
 * 商品改版更新商品信息
 */
export function apiEditSku(params) {
  return request.post('/product/editSku', params);
}

/**
 * 商品改版新建商品信息
 */
export function apiAddSku(params) {
  return request.post('/product/addSku', params);
}


/**
 * 商品改版-商品单位列表（中台）
 */
export function apiProductUnitList(params) {
  return request.get('/sku/productUnitList', params);
}

/**
 * 商品改版-商品属性选项
 */
export function apiAttributeOptions(params) {
  return request.get('/product/attributeOptions', params);
}

/**
 * 商品改版-库存日志
 */
export function apiStockLogs(params) {
  return request.get('/product/stockLogs', params);
}

/**
 * 商品改版-商品初始化
 */
export function apiProductInit(params) {
  return request.get('/product/init', params);
}

/**
 * 商品改版-修改待发布商品的69码
 */
export function apiChangeCode(params) {
  return request.post('/sku/changeCode', params);
}

/**
 * 商品改版-校验是否保护品种
 */
export function apiValidProtectSkuAndForbiddenName(params) {
  return request.get('/product/validProtectSkuAndForbiddenName', params);
}

// 获取药店类型
export function findUserTypes(params) {
  return request.get('/salesControl/findUserTypes', params);
}

// 获取商品助记码
export function apiCamelChars(params) {
  return request.post('/pinyin/camelChars?name=' + params.name);
}

/**
 * 商品下架-新接口
 */
export function batchDownNew(params) {
  return request.post('/product/batchDown', params);
}
// 时间计划-列表
export function saleTimeList(params) {
  return request.get('/skuSaleTime/configList', params);
}

// 时间计划-添加计划
export function addSaleTimeList(params) {
  return request.post('/skuSaleTime/addConfig', params);
}

// 时间计划-更新计划
export function updateSaleTimeList(params) {
  return request.post('/skuSaleTime/updateConfig', params);
}

// 时间计划-删除计划
export function deleteSaleTimeList(id) {
  return request.post('/skuSaleTime/deleteConfig/' + id);
}

// 时间计划-计划下关联商品数
export function countSaleProduct(params) {
  return request.get('/skuSaleTime/countConfigSku', params);
}

// 时间计划-下拉选项
export function SelectSaleTimeList() {
  return request.get('/skuSaleTime/configSelect');
}

// 商品销售时间-列表
export function saleTimeSkuList(params) {
  return request.get('/skuSaleTime/skuList', params);
}

// 商品销售时间-详情
export function saleTimeSkuInfo(params) {
  return request.get('/skuSaleTime/skuInfo', params);
}

// 商品销售时间-批量添加
export function batchSaveTime(params) {
  return request.post('/skuSaleTime/batchSaveTime', params);
}

// 商品销售时间-更新设置
export function updateSkuTime(params) {
  return request.post('/skuSaleTime/updateTime', params);
}

// 商品销售时间-删除设置
export function deleteSkuTime(id) {
  return request.post('/skuSaleTime/deleteSku/' + id);
}

// 商品销售时间-启用禁用
export function changeSkuStatus(params) {
  return request.post('/skuSaleTime/changeSkuStatus', params);
}

// 商品销售时间-导出
export function exportSkuSaleTime(params) {
  return request.get('/skuSaleTime/async/exportSku', params);
}
// 获取导入模板
export function apiDownloadTemplate(params) {
  return request.get('/uploadFile/downloadTemplate', params, { responseType: 'blob' });
}

// 神农商品列表
export function apiShenNongProductlist(params) {
  return request.get('/shenNongProduct/list', params);
}

// 勾选神农商品创建pop品
export function apiBatchPublish(params) {
  return request.post('/shenNongProduct/batchPublish', params);
}

/**
 * 批量修改活动信息
 */
export function batchUpdateActivityInfoFromExcel(params) {
  return request.post('/product/batchUpdateForActivityGoods', params);
}

// fbp店铺查看订单占用
export function fbpOrderStock(params) {
  return request.get('/product/fbpOrderStock', params);
}

// fbp店铺查看总库存
export function fbpTotalStock(params) {
  return request.get('/product/fbpTotalStock', params);
}

/**
 * 更新删除状态商品编码
 */
export function updateDeleteSkuErpCode(params) {
  return request.post('/product/updateDeleteSkuErpCode', params);
}

/**
 * 商品价格日志
 */
export function getSkuAmountLog(params) {
  return request.get('/product/skuAmountLog', params);
}

/**
 * 商品变更日志
 */
export function getSkuLogV2(params) {
  return request.get('/product/skuLogV2', params);
}

// 控销药店明细
export function getMerchantPage(params) {
  return request.get('/skuMerchantGroup/merchantPage', params);
}

// 批量更新商品价格
export function batchUpdatePrice(params) {
  return request.post('/sku/batchUpdatePrice', params);
}

// 批量更新商品库存
export function batchUpdateStock(params) {
  return request.post('/sku/batchUpdateStock', params);
}

// 批量更新商品erp编码
export function batchUpdateErpCode(params) {
  return request.post('/sku/batchUpdateErpCode', params);
}

// 批量修改分区价格
export function batchSetAreaPrice(params) {
  return request.post('/sku//batchSetAreaPrice', params);
}

// 获取自动上架设置信息
export function skuAutoUpInfo(params) {
  return request.get('/corporation/v2/skuAutoUpInfo', params);
}

// 设置是否自动上架
export function updateSkuAutoUpInfo(params) {
  return request.post('/corporation/v2/updateSkuAutoUpInfo', params);
}

// 匹配中台标品列表
export function matchMeProduceList(params) {
  return request.post('/product/match/meProduceList', params);
}

// 商业自建商品是否走新的匹配标品逻辑
export function matchSwitch(params) {
  return request.post('/product/match/switch', params);
}

// 纠错类别
export function correctionCategory(params) {
  return request.get('/sku/correction/category', params);
}

//提交纠错
export function correctionSubmit(params) {
  return request.post('/sku/correction/submit', params);
}

//通过标准库ID查询中台商品信息
export function getSkuByProductId(params) {
  return request.get('/sku/getSkuByProductId', params);
}

//异常更新信息
export function warnInfo(params) {
  return request.get('/product/warn/info', params);
}

//纠错列表
export function correctionPage(params) {
  return request.post('/sku/correction/page', params);
}

//纠错列表导出
export function correctionExport(params) {
  return request.post('/sku/correction/export', params, { responseType: 'blob' });
}

//一键绑定标品
export function onekeybind(params) {
  return request.post('/product/one/key/bind', params);
}


//提交详情
export function correctionDetail(params) {
  return request.get('/sku/correction/detail', params);
}

//一级分类下 需要比对字段
export function compareFields(params) {
  return request.get('/product/match/compare/fields', params);
}

//取消提醒标记
export function standMark(params) {
  return request.post('/product/cancel/stand/mark', params);
}

//未绑定标品的数量
export function noStandardIdCount(params) {
  return request.get('/product/query/noStandardIdCount', params);
}

//修改商品列表
export function updateParams(params) {
  return request.post('/report/wholesale/apply/updateParam', params);
}

/**
 * 批量修改批够包邮商品
 * @param {参数} params
 */
export function batchUpdateSupply(params) {
  return request.postFormData('/report/wholesale/apply/batchUpdateAct', params);
}

export function download() {
	return request.get('/report/wholesale/apply/getTemplateForBatchUpdate',null, {
		responseType: 'blob'
	})
}



//获取灰度列表
export function getGreyList(params) {
  return request.get('/report/wholesale/apply/checkSupportWholesale', params);
}

/**
 * 操作变更日志
 */
export function getOperationLogList(params) {
  return request.get('/report/wholesale/apply/selectOperateLogList', params);
}
/**
 *
 * @returns { Promise<{
 * 		code: number,
 * 		message: string,
 * 		data: {
 * 			saleOutAutoUp: number,
 * 			commonSaleOutAutoUp: number,
 * 			ptSaleOutAutoUp: number,
 * 			wholesaleSaleOutAutoUp: number
 * 		},
 * 		success: boolean,
 * 		fail: boolean
 * }> }
 */
export function skuAutoUpInfoV2() {
	return request.get('/corporation/v2/skuAutoUpInfoV2')
}

/**
 *
 * @param { {
* 			commonSaleOutAutoUp: number,
* 			ptSaleOutAutoUp: number,
* 			wholesaleSaleOutAutoUp: number
* 		} } params
 * @returns { Promise<any> }
 */
export function updateSkuAutoUpInfoV2(params) {
	return request.post('/corporation/v2/updateSkuAutoUpInfoV2',params)
}
export function editCommonSkuScope(params) {
	return request.post('/product/editCommonSkuScope', params)
}

export function getFreeShopConfig(params) {
	return request.post('/productFreeShipping/queryFreeShippingConfig',params)
}
export function setFreeShopConfig(params) {
	return request.post('/productFreeShipping/saveOrUpdateFreeShippingConfig', params)
}
//竞价商品管理
export function productBiddingInfo(params) {
	return request.get('/product/biddingInfo', params)
}
//新增/编辑商品 获取用户组列表
export function getGroupList() {
  return request.get('/price/merchant/group/list')
}

//竞价商品导出
export function exportBiddingSku(params) {
	return request.get('/sku/async/exportBidding/sku', params)
}
//停用商品五要素匹配
export function featureMatching(params) {
	return request.post('/product/matchProducts', params)
}
// 拼团活动审核
export function apiApplyListProduct(params) {
  return request.get('/report/groupbuying/apply/product/list', params);
}
// 导出拼团活动
export function getApplyExport(params) {
  return request.get('/report/groupbuying/apply/product/export', params);
}
// 查看灰度是否展示
export function checkRecommendReport() {
  return request.get('/report/wholesale/apply/checkRecommendReport');
}

//新增用户组
export function addUserGroup(params) {
  const formData = new FormData();
  for (const key in params) {
    formData.append(key, params[key])
  }
  return request.post('/price/merchant/group/save', formData)
}
//查询用户组列表
export function getUserGroupList(params) {
  return request.get('/price/merchant/group/page', params)
}
//查询变更记录
export function getChangeList(params) {
  return request.get('/price/merchant/group/logs' ,params)
}
//查询关联商品数量
export function checkGroupCount(params) {
  return request.get('/price/merchant/group/check/product', params)
}
//删除用户组
export function deleteGroup(params) {
  return request.get('/price/merchant/group/delete',params)
}
//导出
export function exportUserGroup(params) {
  const formData = new FormData()
  for (const key in params) {
    formData.append(key, params[key])
  }
  return request.post('/price/merchant/group/export', formData)
}
// 查看是否符合分区域价商业
export function merchantGroupGrey() {
  return request.get('/price/merchant/group/grey')
}


// 判断是否支持虚拟库存维护
export function fbpStockUseNewOrg(params) {
  return request.get('/fbp/product/fbpStockUseNewOrg', params)
}

// 获取控销商品库存
export function getTotalStock(params) {
  return request.get('/fbp/product/fbpTotalStock', params)
}

// 修改控销商品库存
export function updatelStock(params) {
  const formData = new FormData()
  for (const key in params) {
    formData.append(key, params[key])
  }

  return request.post('/fbp/product/updateStock', formData)
  // request.post('/fbp/product/updateStock', params)
}

//获取变更产品红点提示
export function getChangeRedCount() {
  return request.get('/product/statistics/partHaveMeProductMarkCount')
}

//标品信息变更_使用标品更新商品信息
export function batchUpdateOnStandardProduct(data) {
  return request.post('/product/batchUpdateOnStandardProduct', data)
}
export function loadBiddingStatusCounts(params) {
  return request.post('/product/loadBiddingStatusCounts', params);
}

export function checkSkuScope(params) {
  return request.get('/product/checkSkuScope', params);
}

//判断机构是否为控销
export function KSChecker() {
  return request.get('/sku/isControlSalesByOrgId')
}
// 售后管理
export function afterSalesOrderCount() {
  return request.post('/index/v2/afterSalesOrderCount')
}