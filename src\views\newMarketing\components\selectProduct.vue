<template>
  <el-dialog
    title="选择商品"
    :visible="true"
    width="80%"
    :close-on-click-modal="false"
    :destroy-on-close="true"
    :before-close="handleDialogClose"
  >
    <div>
      <el-form
        ref="form"
        class="fix-item-width"
        size="small"
        label-position="right"
        label-width="100px"
        :model="listQuery"
      >
        <el-row>
          <el-col :lg="6" :sm="12">
            <el-form-item label="csuId" prop="csuid">
              <el-input v-model.trim="listQuery.csuid" clearable placeholder="请输入" />
            </el-form-item>
          </el-col>
          <el-col :lg="6" :sm="12">
            <el-form-item label="商品编码" prop="barcode">
              <el-input v-model.trim="listQuery.barcode" clearable placeholder="请输入" />
            </el-form-item>
          </el-col>
          <el-col :lg="6" :sm="12">
            <el-form-item label="商品名称" prop="showName">
              <el-input v-model.trim="listQuery.showName" clearable placeholder="请输入" />
            </el-form-item>
          </el-col>
          <el-col :lg="6" :sm="12">
            <el-form-item label="商品来源" prop="activityType">
              <el-select
								v-model="listQuery.activityType"
								placeholder="请选择商品来源"
							>
								<el-option
									label="全部"
									:value="null"
								/>
								<el-option
									label="普通商品"
									value="0"
								/>
                <el-option
                  v-if="selectedProductType === 'masterSku'"
									label="拼团商品"
									value="1"
								/>
				<el-option v-if="selectedProductType === 'masterSku'" label="批购包邮商品" value="3"></el-option>
								<el-option
                  v-if="selectedProductType === 'giveSku'"
									label="赠品"
									value="2"
								/>
							</el-select>
            </el-form-item>
          </el-col>
          <el-col :lg="6" :sm="12">
						<el-form-item prop="status" label="商品状态">
							<el-select
								v-model="listQuery.status"
								placeholder="请选择商品状态"
							>
								<el-option
									label="全部"
									:value="null"
								/>
								<el-option
									label="待上架"
									value="6"
								/>
								<el-option
									label="销售中"
									value="1"
								/>
								<el-option
									label="已下架"
									value="4"
								/>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :lg="6" :sm="12" class="btn-group">
            <el-button size="small" type="primary" @click="searchData">查询</el-button>
            <el-button size="small" @click="resetFields">重置</el-button>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <el-table
      :data="list"
      style="margin-top: 10px;"
      border
      v-loading="loading"
      max-height="300px"
      :header-cell-style="{ background: '#eeeeee', color: '#666666' }"
    >
			<el-table-column
				align="center"
				prop="selection"
				width="50"
			>
				<template #default="{ row }">
					<el-radio
						:label="row.id"
						@input="selectChange($event, row)"
						:value="selectedId"
					>
						<span />
					</el-radio>
				</template>
			</el-table-column>
      <el-table-column prop="csuid" label="csuId" width="120" />
      <el-table-column prop="barcode" label="商品编码" width="120" />
      <el-table-column prop="erpCode" label="erp编码" width="120" >
		<template slot-scope="{ row }">
			{{ row.activityType == '3' ? '' : row.erpCode }}
		</template>
	  </el-table-column>
			<el-table-column prop="showName" label="商品名称" width="120" />
      <el-table-column prop="spec" label="规格" width="120" />
			<el-table-column prop="manufacturer" label="厂家" width="120" />
      <el-table-column prop="fob" label="药帮忙价格" width="120" />
			<el-table-column prop="availableQty" label="可售库存" width="120" />
      <el-table-column prop="status" label="商品状态" width="120">
				<template slot-scope="{ row }">
          {{ {1: '销售中', 4: '下架', 6: '待上架', 8: '待审核', 9: '审核未通过', 20: '删除' }[row.status] }}
        </template>
			</el-table-column>
			<el-table-column prop="csuid" label="是否近效期" width="120">
        <template slot-scope="{ row }">
          {{ row.expired ? '是' : '否' }}
        </template>
      </el-table-column>
      <el-table-column prop="tagName" label="商品来源" width="120">
				<template slot-scope="{ row }">
          {{ {1: '拼团商品', 2: '赠品', 0: '普通商品', 3:'批购包邮商品' }[row.activityType] }}
        </template>
			</el-table-column>
    </el-table>
    <div v-show="listQuery.total > 0" class="page-container">
      <div class="pag-text">
        共 {{ listQuery.total }} 条数据，每页{{ listQuery.pageSize }}条，共{{
        Math.ceil(listQuery.total / listQuery.pageSize)
        }}页
      </div>
      <el-pagination
        background
        class="pager"
        :current-page.sync="listQuery.page"
        :page-size.sync="listQuery.pageSize"
        layout="sizes, prev, pager, next, jumper"
        :page-sizes="[10, 20, 30, 50]"
        :total="listQuery.total"
        prev-text="上一页"
        next-text="下一页"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
    <span slot="footer">
      <el-button size="medium" @click="cancel">取消</el-button>
      <el-button size="medium" style="margin-left: 20px;" type="primary" @click="confirm">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { getProductList } from '@/api/product';
export default {
  components: {},
  props: {
    selected: Number | String,
    selectedProductType: String,
    title: {
      type: String,
      default: '选择人群',
    },
    crowdDialogVis: {
      type: Boolean,
      default: false,
    },
		selectedProductList: Array,
  },
  data() {
    return {
      selectedId: '',
			selectedRow: {},
      loading: false,
      listQuery: {
				csuid: '',
        barcode: '',
				showName: '',
        status: null,
        activityType: null,
        page: 1,
        pageSize: 10,
        total: 0,
      },
      list: [],
      selections: '',
      dialogVisible: false,
    };
  },
  mounted() {
    this.searchData();
  },
  methods: {
		selectChange($event, row) {
			this.selectedId = row.id;
			this.selectedRow = row;
    },
    resetFields() {
      this.$refs.form.resetFields();
      this.searchData();
    },
    searchData() {
      this.getList(this.listQuery, true);
    },
    handleSizeChange(val) {
      this.listQuery.pageSize = val;
      this.getList(this.listQuery);
    },
    handleCurrentChange() {
      this.getList(this.listQuery);
    },
    async getList(listQuery, reset) {
      this.loading = true;
      const { page, pageSize } = listQuery;
      const activityTypes = this.selectedProductType === 'masterSku' ? '0,1,3' : '0,2';
      const statuses = listQuery.status ? null : '1,4,6';
      const params = {
        page: reset ? 1 : page,
        ...listQuery,
        rows: pageSize,
        activityTypes,
        statuses,
      };
			const res = await getProductList(params);
			if (res && res.code === 0) {
        this.loading = false;
				this.list = res.result.list || [];
				this.listQuery.total = res.result.total;
				this.listQuery.pageSize = res.result.pageSize;
			} else {
        this.loading = false;
      }
    },
    cancel() {
      this.handleDialogClose();
    },
    confirm() {
      if (!this.selectedId) {
				this.$message.error('请选择商品');
				return;
			}
      if (this.selectedProductType === 'giveSku' && this.selectedRow.fob == '0.0') {
        this.$message.error('当前所选商品为药品，药品作为赠品价格不能为0，请维护单体采购价');
        return;
      }
			if (this.selectedProductList.length) {
				this.$confirm('是否替换已选商品?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: ''
        }).then(() => {
					this.$emit('confirmProduct', this.selectedRow);
				})
			} else {
				this.$emit('confirmProduct', this.selectedRow);
			}
    },
    handleDialogClose() {
      this.$emit('cancelModal', false);
    },
  },
};
</script>
<style scoped lang="scss">
form.el-form.fix-item-width {
  .el-row {
    ::v-deep   .el-col .el-form-item {
      .el-form-item__content {
        > .el-input {
          width: 100%;
        }

        > .el-date-editor {
          width: 100%;
        }
      }
    }
  }
}
.btn-group {
  display: flex;
  justify-content: flex-end;
}
@import '../../../components/xyy/customerOperatoin/style/style.scss';
</style>
