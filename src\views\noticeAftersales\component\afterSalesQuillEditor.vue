<template>
  <div class="editDiv">
    <!--    <div-->
    <!--      v-if="!isEdit"-->
    <!--      class="tipBox"-->
    <!--    />-->
    <quill-editor
      ref="myTextEditor"
      v-model="content"
      :options="editorOption"
      style="height: 200px; margin-bottom: 10px; width: 100%"
      @change="alertValue($event)"
    />
    <div class="textNum">{{ TiLength }}/1000</div>

    <div class="btnDiv">
      <div v-if="isEdit">
        <el-button size="small" @click="content = ''">清空</el-button>
        <el-button type="primary" size="small" @click="sendContent">提交</el-button>
      </div>
      <span v-if="fromEditor === 1">
        <el-button
          v-if="!isEdit"
          v-permission="['shop_notice_edit']"
          size="small"
          type="primary"
          @click="editCon"
        >编辑</el-button>
      </span>
      <span v-else>
        <el-button
          v-permission="['shop_returningNotes_edit']"
          v-if="!isEdit"
          size="small"
          type="primary"
          @click="editCon"
        >编辑</el-button>
      </span>
    </div>
  </div>
</template>
<style>
div.ql-toolbar.ql-snow {
  padding: 0;
  height: 0;
  border-bottom: none;
}
</style>
<script>
import { shopNoticeSave, returnNoticeSave } from '@/api/other/index';
import { quillEditor } from 'vue-quill-editor'; // 调用编辑器
import 'quill/dist/quill.core.css';
import 'quill/dist/quill.snow.css';
import 'quill/dist/quill.bubble.css';

export default {
  name: 'MyQuillEditor',
  components: { quillEditor },
  props: {
    contentF: {
      type: String,
      default: '',
    },
    fromEditor: {
      type: Number,
      default: null,
    },
    contentId: {
      type: Number,
      default: null,
    },
  },
  data() {
    return {
      content: '',
      editorOption: {
        placeholder: this.fromEditor == 1 ? '您还没配置公告内容，前端将隐藏店铺公告区域' : '您还没配置退货须知内容，前端将隐藏退货须知区域',
        modules: {
          toolbar: [
            [], // toggled buttons
          ],
        },
      },
      isEdit: true,
      TiLength: 0,
    };
  },
  watch: {
    contentF(val) {
      console.log(val, '[[[[[[');
      if (!val) {
        this.isEdit = true;
      } else {
        this.isEdit = false;
      }
      this.content = val;
      this.$refs.myTextEditor.quill.enable(false);
    },
  },
  created() {

  },
  mounted() {
    this.TiLength = this.$refs.myTextEditor.quill.getLength() - 1;
  },
  methods: {
    sendContent() {
      console.log(this.content, 'ooo');
      if (this.fromEditor == 1) {
        shopNoticeSave({ id: this.contentId, content: this.content }).then((res) => {
          if (res.code === 0) {
            this.$message.success('保存成功');
            this.isEdit = false;
            this.$refs.myTextEditor.quill.enable(false);
          } else {
            this.$message.error(res.msg);
          }
        });
      } else {
        returnNoticeSave({ id: this.contentId, content: this.content }).then((res) => {
          if (res.code === 0) {
            this.$message.success('保存成功');
            this.isEdit = false;
            this.$refs.myTextEditor.quill.enable(false);
          } else {
            this.$message.error(res.msg);
          }
        });
      }
    },
    editCon() {
      this.isEdit = true;
      this.$refs.myTextEditor.quill.enable(true);
    },

    alertValue(e) {
      e.quill.deleteText(1000, 1);
      if (this.content == '') {
        this.TiLength = 0;
      } else {
        this.TiLength = 1000 - (e.quill.getLength() - 1);
      }
    },
  },
};
</script>

<style scoped lang="scss">
.editDiv {
  padding: 20px 10px;
  width: 60%;
  position: relative;
  .tipBox {
    width: 100%;
    height: 230px;
    opacity: 0;
    position: absolute;
    top: 0;
    left: 0;
    z-index: 100;
  }
}
.textNum {
  text-align: right;
  position: absolute;
  bottom: 72px;
  right: 22px;
  color: #999999;
}

.btnDiv {
  padding-top: 5px;
  width: 100%;
  text-align: right;
}
</style>
