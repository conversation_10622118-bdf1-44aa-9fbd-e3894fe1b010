<template>
  <div class="home">
    <div class="serch">
      <el-row
        type="flex"
        align="middle"
      >
        <span class="sign" />
        <div>查询条件</div>
      </el-row>
    </div>
    <el-row
      class="condition searchMy"
      style="padding:0 20px;"
    >
      <el-form
        ref="ruleForm"
        :inline="true"
        :model="ruleForm"
        size="small"
      >
        <el-form-item prop="name">
          <el-input v-model.trim="ruleForm.name" placeholder="请输入">
            <template slot="prepend">
              活动名称
            </template>
          </el-input>
        </el-form-item>
        <el-form-item prop="id">
          <el-input v-model.trim="ruleForm.id" placeholder="请输入">
            <template slot="prepend">
              活动ID
            </template>
          </el-input>
        </el-form-item>
        <el-form-item prop="skuId">
          <el-input v-model.trim="ruleForm.skuId" placeholder="请输入">
            <template slot="prepend">
              csuId
            </template>
          </el-input>
        </el-form-item>
        <el-form-item prop="skuName">
          <el-input v-model.trim="ruleForm.skuName" placeholder="请输入">
            <template slot="prepend">
              商品名称
            </template>
          </el-input>
        </el-form-item>
        <el-form-item prop="fullGiveType">
          <span class="search-title">活动类型</span>
          <el-select
            v-model="ruleForm.fullGiveType"
            placeholder="请选择活动类型"
          >
            <el-option
              label="全部"
              :value="null"
            />
            <el-option
              label="拼团送赠品"
              value="2"
            />
            <el-option
              label="原品送赠品"
              value="1"
            />
          </el-select>
        </el-form-item>
        <el-form-item prop="status">
          <span class="search-title">活动状态</span>
          <el-select
            v-model="ruleForm.status"
            placeholder="请选择活动状态"
          >
            <el-option
              label="全部"
              :value="null"
            />
            <el-option
              label="未开始"
              value="1"
            />
            <el-option
              label="进行中"
              value="3"
            />
            <el-option
              label="已下线"
              value="5"
            />
          </el-select>
        </el-form-item>
        <el-form-item prop="activityTime">
          <span class="search-title">活动时间</span>
          <div style="display: table-cell; line-height: 24px">
            <el-date-picker
              v-model="ruleForm.activityTime"
              type="datetimerange"
              range-separator="-"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              :default-time="['00:00:00', '23:59:59']"
              style="width: 300px;"
              :clearable="false"
              :picker-options="pickerOptions"
              @change="pickerChangeFn"
            />
          </div>
        </el-form-item>
        <el-form-item class="searchBtn" style="text-align: right;padding-right: 20px">
          <el-button
            type="primary"
            @click="submitForm('ruleForm')"
          >
            查询
          </el-button>
          <el-button @click="resetForm('ruleForm')">
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </el-row>
    <el-divider />
    <div class="serch">
      <el-button
        v-permission="['marketing_full_add']"
        type="primary"
        size="mini"
        @click="creatActive"
      >
        新增
      </el-button>
      <el-button
        v-permission="['marketing_full_add']"
        type="primary"
        size="mini"
        @click="batchDialog = true"
      >
        批量上传
      </el-button>
      <!-- <el-button
        v-permission="['marketing_full_add']"
        type="primary"
        size="small"
        @click="exportData"
      >
        批量下线
      </el-button> -->
      <MultipartUpload v-permission="['marketing_full_add']" :type="2" style="margin:0 10px;">
        批量下线
      </MultipartUpload>
      <el-button
        v-permission="['marketing_full_add']"
        type="primary"
        size="mini"
        @click="exportData"
      >
        导出
      </el-button>

    </div>
    <el-row class="actlist">
      <div class="serch">
        <el-row
          type="flex"
          align="middle"
        >
          <span class="sign" />
          <div>数据列表</div>
        </el-row>
      </div>
      <el-table
        v-loading="isLoading"
        :data="tableData"
        stripe
        border
        style="width: 100%"
        :header-cell-style="{ background: '#F9F9F9', color: '#666666' }"
      >
        <el-table-column
          prop="id"
          label="活动ID"
          width="100"
        />
        <el-table-column
          label="活动名称"
          width="120"
          prop="name"
        />
        <el-table-column
          v-if="false"
          prop="name"
          label="主商品信息"
          width="180"
        >
          <template slot-scope="{row}" v-if="row.masterSkuDto">
						<div>csuId：{{ row.masterSkuDto.csuid }}</div>
						<div>商品编码: {{ row.masterSkuDto.erpCode }}</div>
						<div>商品名称: {{ row.masterSkuDto.showName }}</div>
						<div>规格: {{ row.masterSkuDto.spec }}</div>
						<div>厂家: {{ row.masterSkuDto.manufacturer }}</div>
						<div>商品类型: {{ row.masterSkuDto.skuTypeStr }}</div>
						<div v-if="row.masterSkuDto.skuTypeStr === '拼团商品'">活动状态: {{ row.masterSkuDto.reportStatusStr }}</div>
						<div>
							<div v-if="row.masterSkuDto.skuTypeStr === '拼团商品'">价格：{{ row.masterSkuDto.fob }}</div>
							<div v-else>
								<div v-if="row.masterSkuDto.fob">单体采购价：{{ row.masterSkuDto.fob }}</div>
								<div v-if="row.masterSkuDto.guidePrice">连锁采购价：{{ row.masterSkuDto.guidePrice }}</div>
							</div>
						</div>
						<div v-if="row.masterSkuDto.nearEffect">近至：{{ row.masterSkuDto.nearEffect }}</div>
            <div v-if="row.masterSkuDto.farEffect">远至：{{ row.masterSkuDto.farEffect }}</div>
          </template>
        </el-table-column>
        <el-table-column
          prop="name"
          label="赠品信息"
          width="180"
          v-if="false"
        >
          <template slot-scope="{row}" v-if="row.giveSkuDto">
						<div>csuId：{{ row.giveSkuDto.csuid }}</div>
						<div>商品编码: {{ row.giveSkuDto.erpCode }}</div>
						<div>商品名称: {{ row.giveSkuDto.showName }}</div>
						<div>规格: {{ row.giveSkuDto.spec }}</div>
						<div>厂家: {{ row.giveSkuDto.manufacturer }}</div>
						<div v-if="row.giveSkuDto.nearEffect">近至：{{ row.giveSkuDto.nearEffect }}</div>
            <div v-if="row.giveSkuDto.farEffect">远至：{{ row.giveSkuDto.farEffect }}</div>
						<div>活动价: {{ row.giveSkuDto.giveSkuPrice || '0.00' }}</div>
          </template>
        </el-table-column>
        <el-table-column
          prop="name"
          label="赠品数量限制"
          width="150"
          v-if="false"
        >
          <template slot-scope="{row}" v-if="row.giveSkuDto">
            <div v-if="row.giveSkuDto.giveQty == -1">
							<div>不限制</div>
              <div>商品库存：{{ row.giveSkuDto.availableQty }}</div>
              <div>剩余数量: {{ row.giveSkuDto.availableQty }}</div>
            </div>
						<div v-else>
							<div>限制: {{ row.giveSkuDto.giveQty }}</div>
							<div>剩余数量: {{ row.giveSkuDto.surplusGiveQty }}</div>
						</div>
          </template>
        </el-table-column>
        <el-table-column
          prop="name"
          label="活动规则"
          width="150"
        >
          <template slot-scope="{row}" v-if="row.fullGiveRuleDtoList">
            <div>
              {{ row.fullReductionType === 1 ? '每' : '' }}
              <div v-for="(item, index) in row.fullGiveRuleDtoList" :key="index">
                满{{ row.fullReductionCond == 1 ? item.fullMoney : item.fullCount }}{{ row.fullReductionCond == 1 ? '元' : '件' }}，{{ row.giveSkuType == 2 ? '赠品池任选' : '赠' }} {{ row.giveSkuType == 2 ? item.giveCount : item.num }}件赠品
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column
          prop="customerGroupId"
          label="活动人群"
        >
          <template slot-scope="{row}">
            <div>
              <div>人群ID：{{ row.customerGroupId }}</div>
              <div class="tooltipEllipsisBox">
                人群名称:
                <el-tooltip class="item" effect="dark" :content="row.customerGroupName" placement="top">
                  <span class="tooltipEllipsis">{{ row.customerGroupName }}</span>
                </el-tooltip>
              </div>
							<div class="tooltipEllipsisBox" v-if="row.contentBundleDescriptions">
                人群定义:
                <el-tooltip class="item" effect="dark" placement="top">
                  <template slot="content">
                    <p
                    v-for="(item, index) in row.contentBundleDescriptions[0]"
                    :key="index"
                  >
                    {{ item }}
                  </p>
                  </template>
                  <span class="tooltipEllipsis">{{ (row.contentBundleDescriptions[0] || []).join() }}</span>
                </el-tooltip>
							</div>
            </div>
          </template>
        </el-table-column>
        <el-table-column
          prop="endTime"
          label="活动时间"
        >
          <template slot-scope="{row}">
            <div>{{ row.stime | formatDate }} - {{ row.etime | formatDate }}</div>
          </template>
        </el-table-column>
        <el-table-column
          width="150"
          prop="statusStr"
          label="活动状态"
        />
        <el-table-column
          width="180"
          prop="createTime"
          label="创建时间"
        >
          <template slot-scope="{row}">
            <div>{{ row.ctime | formatDate }}</div>
          </template>
        </el-table-column>
        <el-table-column
          width="100"
          prop="statusDesc"
          label="操作"
          fixed="right"
        >
          <template slot-scope="{row}">
            <div>
              <div v-if="row.status === 1">
                <el-button size="small" type="text" @click="onAction('edit', row)">编辑</el-button>
              </div>
              <div v-if="row.status === 3 && row.giveSkuDtoList && row.giveSkuDtoList.some(item => item.giveQty >= 0)">
                <setGiveGoods :value="[...row.giveSkuDtoList]" :type="row.giveSkuType == 1 ? 0: 1" :onlyChangeTotalNum="true" :disabled="true" title="修改赠品数量" @confirmProduct="changeTotalNum">
                  <el-button size="small" type="text" @click="onAction('editCount', row)">修改赠品数量</el-button>
                </setGiveGoods>
              </div>
              <div>
                <el-button size="small" type="text" @click="onAction('see', row)">查看</el-button>
              </div>
              <div v-if="row.status === 1 || row.status === 3">
                <el-button size="small" type="text" @click="onAction('offLine', row)">下线</el-button>
              </div>
            </div>
          </template>
        </el-table-column>
        <template slot="empty">
          <div class="noData">
            <p class="img-box">
              <img
                src="@/assets/image/marketing/noneImg.png"
                alt=""
              >
            </p>
            <p>暂无数据</p>
          </div>
        </template>
      </el-table>
      <div class="pagination-container">
        <div class="pag-text">
          共 {{ page.totalCount }} 条数据，每页{{ 10 }}条，共{{
            Math.ceil(page.totalCount / 10)
          }}页
        </div>
        <el-pagination
          background
          :current-page.sync="page.pageNum"
          prev-text="上一页"
          next-text="下一页"
          layout="sizes, prev, pager, next, jumper"
          :total="page.totalCount"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-row>
    <el-dialog
      title="批量创建"
      :visible.sync="batchDialog"
      width="40%"
      @close="batchDialog = false; batchFile = ''"
    >
      <div
        class="conten-box"
        style="display: flex;"
      >
        <div>请选择上传批量文件</div>
        <div
          class="uploadBtnBox"
          style="flex: 1;display: flex;margin-left: 10px;"
        >
          <el-upload
            ref="excludeImport"
            class="upload-demo"
            action="xxx"
            :http-request="uploadFile"
            :before-remove="removeImportData"
            :show-file-list="true"
            :limit="1"
            accept=".xls, .xlsx, .XLS, .XLSX"
          >
            <el-button size="small" type="primary">选择文件</el-button>
          </el-upload>
          <el-button style="margin-left: 10px;height: 32px;" size="small" plain @click="handleDownloadTemplate">下载模板</el-button>
        </div>
      </div>
      <p>
        1、单次最多导入1000条；<br>
        2、允许部分成功部分失败。<br>
        3、只支持创建1个主品绑定1个赠品的活动
      </p>
      <div slot="footer" class="dialog-footer">
        <el-button size="small" @click="closeMulitpart">取 消</el-button>
        <el-button type="primary" size="small" @click="handleBatchCreate">确 定</el-button>
      </div>
    </el-dialog>
    <exportTips :change-export="changeExport" @handleExoprClose="changeExport = false" @handleChangeExport="handleChangeExport"></exportTips>
  </div>
</template>

<script>
import { getPromotionList, offLine, batchCreateFullGive, asyncExport, savePromotion, batchUpdateGiveCount } from '@/api/market/mzPromotion';
import editGiveSkuCount from './components/editGiveSkuCount';
import exportTips from '../../views/other/components/exportTip.vue'
import MultipartUpload from './components/multipartUpload.vue';
import setGiveGoods from './components/setGiveGoods.vue'
export default {
  name: 'MzPromotion',
  components: { editGiveSkuCount, exportTips, MultipartUpload, setGiveGoods },
  data() {
    return {
      changeExport: false,
      batchFile: '',
      batchDialog: false,
      isLoading: false,
      ruleForm: {
        id: '',
        name: '',
        skuId: '',
        skuName: '',
        fullGiveType: null,
        status: null,
        activityTime: [],
      },
      tableData: [],
      actRow: {},
      page: {
        pageNum: 1,
        pageSize: 10,
        totalCount: null,
      },
      showEditCount: false,
      pickerMinDate: null,
      pickerMaxDate: null,
      day30: 30 * 24 * 3600 * 1000 * 3, // 以30天为例
      // 日期使用
      pickerOptions: {
        onPick: ({maxDate, minDate }) => {
          if (minDate && this.pickerMinDate) {
            this.pickerMinDate = null;
          } else if (minDate) {
            this.pickerMinDate = minDate.getTime();
          }
        },
        disabledDate: (time) => {
          if (this.pickerMinDate) {
            return (time.getTime() > (this.pickerMinDate + this.day30)) || (time.getTime() < (this.pickerMinDate - this.day30));
          }
          return false;
        }
      },
    };
  },
  activated() {
    const csuid = this.$route.query.csuid;
    if (csuid) {
      this.ruleForm.skuId = csuid;
    } else {
      this.ruleForm.skuId = '';
    }
    this.initDate();
    this.searchTabaleData();
  },
  created() {
    const csuid = this.$route.query.csuid;
    if (csuid) {
      this.ruleForm.skuId = csuid;
    } else {
      this.ruleForm.skuId = '';
    }
    this.initDate();
    this.searchTabaleData();
  },
  methods: {
    closeMulitpart() {
      this.batchDialog = false;
      this.batchFile = ''
      this.$refs.excludeImport.uploadFiles = [];

    },
    changeTotalNum(rows) {
      /* this.actRow.giveSkuDtoList = rows;
      const params = {
        name: this.actRow.name,
        etime: this.actRow.etime,
        stime: this.actRow.stime,
        customerGroupId: this.actRow.customerGroupId,
        id: this.actRow.id || null,
        //masterSkuId: this.masterSku[0].csuid,
        //giveSkuId: this.giveSku[0].csuid,
        //giveQty: this.ruleForm.giveQtyType === -1 ? -1 : this.ruleForm.giveQty,
        fullGiveType: this.actRow.fullGiveType,
        reportId: this.actRow.reportId || null,
        masterSkuList: this.actRow.masterSkuDtoList.map(item => {
          return {
            skuId: item.csuid
          }
        }),
        fullReductionCond: this.actRow.fullReductionCond,
        fullReductionType: this.actRow.fullReductionType,
        giveSkuType: this.actRow.giveSkuType,
        fullGiveRuleDtoList: this.actRow.fullGiveRuleDtoList,
        giveSkuList: this.actRow.giveSkuDtoList.map(item => {
          return {
            skuId: item.csuid,
            ...item
          }
        }),
        getOrderGiveMinQty: -1,
        getOrderGiveMaxQty: -1,
      }; */
      if (this.isLoading) return
      this.isLoading = true;
      batchUpdateGiveCount({
        promoId: this.actRow.id,
        giveSkuType: this.actRow.giveSkuType,
        list: rows.map(row =>{
          return {
            skuId: row.csuid,
            count: row.giveQty
          }
        })
      }).then(res => {
        if (res.code === 1000) {
            this.$message.success('保存成功');
            this.searchTabaleData();
        } else {
          const h = this.$createElement;
          const msgArr = res.msg.split(';');
          const tip = msgArr.map((i) => {
            return h('div', { style: 'color: #F56C6C;' }, i + ';');
          });
          this.$confirm(h('p', null, tip), '温馨提示', {
            confirmButtonText: '确定',
            showCancelButton: false,
          }).then(() => {});
        }
      }).finally(() => {
        this.isLoading = false;
      })

    },
    exportData() {
      if (this.isLoading) return
      this.isLoading = true
      const queryData = {
        ...this.ruleForm,
        stime: new Date(this.ruleForm.activityTime[0]).getTime(),
        etime: new Date(this.ruleForm.activityTime[1]).getTime(),
        pageNum: this.page.pageNum,
        pageSize: this.page.pageSize,
      };
      const query = {}
      delete queryData.activityTime;
      for (const key in queryData) {
        if (queryData[key]) {
          query[key] = queryData[key];
        }
      }
      console.log(queryData);
      asyncExport(query).then(res => {
				if (res.code !== 1000) {
					this.$message.warning(res.msg);
					return;
				}
				this.changeExport = true;
			}).finally(() => {
        this.isLoading = false;
      })
    },
    handleChangeExport(info) {
      this.changeExport = false;
			if (info === 'go') {
				const path = '/downloadList';
				window.openTab(path);
				// that.$router.push({ path: '/downloadList' });
			}
    },
    handleDownloadTemplate() {
      location.href = '/fullGive/downloadImportFullGiveTemplate';
    },
    handleBatchCreate() {
      if (!this.batchFile) {
        this.$message.warning('请先上传文件!');
        return false;
      }
      const fileFormData = new FormData();
      fileFormData.append('batchImportFullGiveFile', this.batchFile);
      const loading = this.$loading({
        lock: true,
        text: 'Loading',
        spinner: 'el-icon-loading',
        background: 'rgba(255,255,255, 0.8)',
      });
      batchCreateFullGive(fileFormData).then((res) => {
        loading.close();
        console.log(2323232, res);
        if (res.code === 1000) {
          let con = '';
          const batchImportResult = res.data.batchImportResult || {};
          if (batchImportResult.failNum > 0) {
            con = `<p>导入成功${batchImportResult.successNum}条，失败${batchImportResult.failNum}条，失败原因请下载错误文件：<br><a style="color: #4184D5" href="${batchImportResult.url}" download="下载错误文件">下载错误文件</a></p>`;
          } else {
            con = `<p>导入成功${batchImportResult.successNum}条，失败${batchImportResult.failNum}条</p>`;
          }
          this.$confirm(con, '提示', {
            confirmButtonText: '确定',
            dangerouslyUseHTMLString: true,
            cancelButtonText: '取消',
          }).then(() => {
            this.batchDialog = false;
            this.batchFile = '';
            this.$refs.excludeImport.uploadFiles = [];
            setTimeout(() => {
              this.searchTabaleData();
            }, 500);
          }).catch(() => {
            this.batchFile = '';
          });
        } else {
          this.batchFile = '';
          this.$message.error(res.msg);
        }
      });
    },
    removeImportData() {
      this.batchFile = '';
    },
    uploadFile(params) {
      const { file } = params;
      if (file) {
        this.batchFile = file;
      } else {
        this.$message.warning('请选择上传文件!');
      }
    },
    initDate() {
      const date = new Date();
      date.setMonth(date.getMonth() - 1);
      date.toLocaleDateString();
      const y = date.getFullYear();
      let m = date.getMonth() + 1;
      m = m < 10 ? (`0${m}`) : m;
      let d = 1;
      d = d < 10 ? (`0${d}`) : d;
      const time = `${y}-${m}-${d}`;
      const endTime = new Date(new Date(`${y}-${m}-${d} 00:00:00`).getTime() - 1000);
      /* if (endTime.getMonth()) */
      endTime.setMonth(date.getMonth() + 1);
      const em = endTime.getMonth() + 1 > 10 ? endTime.getMonth() + 1 : `0${endTime.getMonth() + 1}`;

      this.ruleForm.activityTime = [`${time} 00:00:00`, `${endTime.getFullYear()}-${em}-${endTime.getDate()} 23:59:59`];
      console.log(this.ruleForm);

    },
    disabledDate(current) {
      if (!this.ruleForm.activityTime || this.ruleForm.activityTime.length === 0) {
        return false;
      }
      const diffDate = current.diff(this.ruleForm.activityTime[0], 'days');
      return Math.abs(diffDate) > 30;
    },
    pickerChangeFn(value) {
      if (value === null) {
        this.initDate();
      }
    },
    creatActive() {
      this.$router.push('/addMzActive');
    },
    submitForm() {
      this.page.pageNum = 1;
      this.searchTabaleData();
    },
    resetForm(formName) {
      this.$refs[formName].resetFields();
      Object.keys(this.ruleForm).forEach((key) => {
        this.ruleForm[key] = '';
        this.ruleForm.fullGiveType = null;
        this.ruleForm.status = null;
        this.ruleForm.activityTime = [];
      }, this);
      this.page.pageNum = 1;
      this.initDate();
      this.searchTabaleData();
    },
    searchTabaleData() {
      this.isLoading = true;
      const queryData = {
        ...this.ruleForm,
        stime: new Date(this.ruleForm.activityTime[0]).getTime(),
        etime: new Date(this.ruleForm.activityTime[1]).getTime(),
        pageNum: this.page.pageNum,
        pageSize: this.page.pageSize,
      };
      getPromotionList(queryData)
        .then((res) => {
          this.isLoading = false;
          if (res.success) {
            const { list, total, pageNum } = res.data.pageInfo;
            this.tableData = list;
            this.tableData = this.tableData.map(item => {
              if (item.giveSkuType == 1) {
                item.fullGiveRuleDtoList = item.fullGiveRuleDtoList.map(val => {
                  val.num = val.giveCount * item.giveSkuDtoList.map(obj => obj.everyQty).reduce((total, cur) => total + cur)
                  return val
                })
              }
              return item
            })
            this.page.pageNum = pageNum;
            this.page.totalCount = total;
          } else {
            this.$message.error(res.msg);
            this.tableData = [];
            this.page.pageNum = 1;
            this.page.totalCount = 0;
          }
        })
        .catch((error) => {
          this.isLoading = false;
          console.log(error);
        });
    },

    handleSizeChange(val) {
      this.page.pageSize = val;
      this.searchTabaleData();
    },
    handleCurrentChange(val) {
      this.page.pageNum = val;
      this.searchTabaleData();
    },
    refresh() {
      this.searchTabaleData();
    },
    cancelModal() {
      this.showEditCount = false;
    },
    onAction(type, row) {
      if (type === 'editCount') {
        this.actRow = row;
        this.showEditCount = true;
      } else if (type === 'offLine') {
        this.$confirm('确认下线活动?', '温馨提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          offLine({ id: row.id }).then((res) => {
            if (res.code === 1000) {
              this.$message.success('操作成功');
              setTimeout(() => {
                this.refresh();
              }, 500);
            } else {
              this.$message.error(res.message);
            }
          });
        }).catch(() => {});
      } else {
        this.$router.push(`/addMzActive?id=${row.id}&fromType=${type}`);
      }
    }
  },
};
</script>

<style lang="scss" scoped>
.searchMy ::v-deep  .el-form-item__label {
  margin-left: 20px;
  padding: 0;
}
.searchMy ::v-deep  .el-input__inner {
  border-radius: 0 4px 4px 0;
}
.searchMy ::v-deep  .el-date-editor{
  width: 100%;
}
.search-title {
  display: table-cell;
  padding: 0 10px;
  text-align: center;
  border: 1px solid #dcdfe6;
  height: 30px;
  line-height: 30px;
  vertical-align: middle;
  border-right: none;
  border-radius: 4px 0 0 4px;
  color: #333333;
  white-space: nowrap;
}
.searchMy ::v-deep  .el-select {
  display: table-cell;
  width: 100%;
}
.searchMy ::v-deep  .el-form-item__content{
  width: 100%;
}
.searchMy ::v-deep  .el-form-item--small.el-form-item{
  margin-bottom: 10px;
  width: 32%;
}
.searchMy ::v-deep  .el-form-item--small .el-form-item__content{
  line-height: 30px;
  width: 100%;
}
.searchMy ::v-deep  .el-input-group__prepend {
  background: none;
  color: #333333;
  padding: 0 10px;
}
.searchMy ::v-deep   .el-form-item--small.el-form-item{
  width: 24%;
}
.serch {
  padding: 15px 20px;
  font-weight: 700;
  .sign {
    display: inline-table;
    width: 3px;
    height: 13px;
    background: linear-gradient(360deg, #1d69c4 1%, #8bbdfc 99%);
    border-radius: 2px;
    margin-right: 8px;
  }
}
.searchBtn {
  float: right;
}
::v-deep  .el-divider--horizontal {
  margin: 0px;
}
.actlist {
  padding: 0 20px;
  .serch {
    padding: 15px 0;
    font-weight: 700;
  }
}
.el-pagination {
  margin-top: 10px;
  text-align: right;
}
.tooltipEllipsisBox {
  display: flex;
  align-items: center;
}
.tooltipEllipsis {
  margin-left: 6px;
  display: inline-block;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
::v-deep  .pagination-container{
  margin: 15px 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
::v-deep  .el-pagination .el-pagination__total {
  float: left;
}
</style>
