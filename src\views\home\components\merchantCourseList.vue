<template>
  <div>
    <courseDetail v-if="isShowDetail" ref="courseDetailRef" :isShowDetail="isShowDetail" :courseId="courseData.id" @back="back" :fileType="fileType"/>
    <courseSort  @toDetailPage="toDetailPage" ref="courseSortRef" v-else/>

  </div>
</template>

<script>
import courseSort from './courseSort.vue'
import courseDetail from './courseDetail.vue'
export default {
  components: {
    courseSort,
    courseDetail
  },
  data(){
    return{
      isShowDetail:false,
      courseDetailRef:null,
      courseData:{},
      fileType:1,
      curPage:1,
      courseSortRef:null,
    }
  },
  created(){
    this.$bus.$on('tomerchantcourse',(type)=>{
      this.isShowDetail = type
      console.log('%c [ this.isShowDetail ]-28', 'font-size:13px; background:pink; color:#bf2c9f;', this.isShowDetail)
    })
    this.$bus.$on('tocourseDetail',(item)=>{
      console.log('%c [ item ]-31', 'font-size:13px; background:pink; color:#bf2c9f;', item)
      this.toDetailPage(item)
    })
  },
  methods: {
    toDetailPage(row,curPage){
      this.curPage = curPage
      this.fileType = row.fileType
      this.courseData = row
      this.isShowDetail = true
    },
    back(){
      this.isShowDetail = false
      // this.$nextTick(()=>{
      //   this.$bus.$emit('setCurPage',this.curPage)

      // })

    }
  },

}
</script>

<style scoped></style>
