import layout from '@/layout';

const userManage = {
  path: '/userManage',
  name: 'userManage',
  component: layout,
  meta: {
    title: '用户管理',
    icon: 'el-icon-s-check',
  },
  children: [
    {
      path: '/roleManagement',
      name: 'roleManagement',
      component: () => import('@/views/user-management/roleManagement.vue'),
      meta: { title: '角色管理' },
    },
    {
      path: '/newRole',
      name: 'newRole',
      component: () => import('@/views/user-management/newRole.vue'),
      hidden: true,
    },
    {
      path: '/accountManagement',
      name: 'accountManagement',
      component: () => import('@/views/user-management/accountManagement.vue'),
      meta: { title: '账号管理' },
    },
  ],
};
export default userManage;
