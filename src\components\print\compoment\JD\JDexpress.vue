<template>
  <div
    style="margin: 0 auto;border: 1px solid #000;font-family:黑体;box-sizing: border-box;font-size: 12px;font-weight: bold;overflow: hidden"
    :style="{width: config.templateSize === '76mm*130mm'?'70mm': '94mm'}"
  >
    <div
      style="border-bottom: 1px solid #000;overflow:hidden;text-align: left;background-color: #fff;position: relative;"
      :style="{height: config.templateSize === '76mm*130mm'?'16mm':'14mm'}"
    >
      <div style="width:70%;height:12.5mm;margin-top:0.25mm;border-right: none;overflow:hidden;">
               <!-- <div><img src="https://files.test.ybm100.com/G1/M00/1E/E2/Cgoz02HC3FqARb4uAAAHmUddVbQ352.png" alt=""></div> -->
      </div>
      <div
        v-if="config.proName"
        style="height: 5mm;line-height: 5mm;font-size: 12px;text-align: center;position: absolute;right: 5px"
        :style="{top:config.templateSize === '76mm*130mm'?'5mm':'4mm'}"
      >{{ config.proName }}
      </div>
    </div>
    <div
      style="height: 15mm;border-bottom: 1px solid #000;text-align: left;background-color: #fff;overflow: hidden;position: relative;"
    >
      <div
        style="height: 15mm;border-right: 1px solid #000;overflow: hidden;text-align: center;line-height: 12mm;"
        :style="{width: config.templateSize === '76mm*130mm'?'20%':'15%'}"
      >始发地
      </div>
      <div
        style="width: 35%;height: 15mm;border-right: 1px solid #000;overflow: hidden;position: absolute;top: 0;"
        :style="{width: config.templateSize === '76mm*130mm'?'30%':'35%',left: config.templateSize === '76mm*130mm'?'20%':'15%'}"
      >
        <div style="height: 50%;padding-left: 5px;display: flex;align-items: center;">
          {{ jDKYFace.originalDmsName }}
        </div>
        <div style="width: 100%;border-bottom: 1px solid #000"></div>
        <div style="height: 50%;padding-left: 5px;display: flex;align-items: center;">
          {{ jDKYFace.originalCrossCode }}-{{ jDKYFace.originalTabletrolleyCode }}
        </div>
      </div>
      <div
        style="height: 15mm;border-right: 1px solid #000;overflow: hidden;position: absolute;top: 0;left: 50%;text-align: center;line-height: 12mm;"
        :style="{width: config.templateSize === '76mm*130mm'?'20%':'15%'}"
      >目的地
      </div>
      <div
        style="height: 100%;overflow: hidden;position: absolute;top: 0;"
        :style="{width: config.templateSize === '76mm*130mm'?'30%':'35%',left: config.templateSize === '76mm*130mm'?'70%':'65%'}"
      >
        <div style="height: 50%;padding-left: 5px;display: flex;align-items: center;">
          {{ jDKYFace.destinationDmsName }}
        </div>
        <div style="width: 100%;border-bottom: 1px solid #000"></div>
        <div style="height: 50%;padding-left: 5px;display: flex;align-items: center;">
          {{ jDKYFace.destinationCrossCode }}-{{ jDKYFace.destinationTabletrolleyCode }}
        </div>
      </div>
    </div>
    <div class="page-row three"
         style="height: 9mm;border-bottom: 1px solid #000;text-align: left;background-color: #fff;overflow: hidden;position: relative;"
    >
      <div class="cell_1"
           style="height: 100%;line-height:9mm;text-align:center;border-right: 1px solid #000;overflow: hidden"
           :style="{width:config.templateSize === '76mm*130mm'?'20%':'15%'}"
      >营业部</div>
      <div class="cell_5"
           style="height: 100%;position: absolute;top: 0;left: 15%;line-height:9mm;padding-left: 5px;"
           :style="{width:config.templateSize === '76mm*130mm'?'80%':'85%',left:config.templateSize === '76mm*130mm'?'20%':'15%'}"
      >{{ jDKYFace.printSiteName }}
      </div>
      <!-- <div class="cell_4"></div> -->
    </div>
    <div class="page-row four"
         style="height: 20mm;border-bottom: 1px solid #000;text-align: left;background-color: #fff;overflow: hidden;position: relative;"
    >
      <div class="cell_1"
           style="height:20mm;border-right: 1px solid #000;overflow: hidden;text-align: center;line-height: 16mm;"
           :style="{width:config.templateSize === '76mm*130mm'?'20%':'15%'}"
      >收件信息
      </div>
      <div
        style="height:20mm;overflow: hidden;position: absolute;left: 15%;top: 0;padding-top: 5px;padding-left: 5px;"
        :style="{width:config.templateSize === '76mm*130mm'?'80%':'auto',left:config.templateSize === '76mm*130mm'?'20%':'15%'}"
      >
        {{ config.contactor }} {{ config.merchantName }} {{config.merchantErpCode}}<br>
        {{ config.mobile }}<br>
        {{ config.takeAeliveryAddress }}
      </div>
    </div>
    <div class="page-row five"
         style="height: 9mm;border-bottom: 1px solid #000;text-align: left;background-color: #fff;overflow: hidden;position: relative;"
    >
      <div class="cell_12" style="width: 100%;height: 9mm;text-align: center;line-height: 9mm">
        <span>第{{ config.printCount }}次打印</span><span style="padding-left: 10px"
      > 打印时间：{{ formatDate(config.printTime) }} </span></div>
    </div>
    <div class="page-row five"
         style="width:100%;height: 9mm;border-bottom: 1px solid #000;text-align: left;background-color: #fff;overflow: hidden;position: relative;"
    >
      <div style="width: 25%;height: 9mm;">
        <!--        <div><img src="https://oss-ec.ybm100.com/ybm/popDeliverLog/jingdong_logo.png" alt=""></div>-->
      </div>
      <div class="cell_12"
        style="width: 100%;height: 100%;position: absolute;top: 0;line-height: 8mm;"
        :style="{left:config.templateSize === '76mm*130mm'?'44%':'40%'}"
      >
        <span
          style="font-weight: bold"
          :style="{fontSize: config.templateSize === '76mm*130mm'?'12px':'14px'}"
        >运单号：{{ config.waybillNo }}</span></div>
    </div>
    <div class="page-row six"
         style="height: 17mm;border-bottom: 1px solid #000;text-align: left;background-color: #fff;overflow: hidden;position: relative;"
         :style="{height:config.templateSize === '100mm*115mm'?'14mm':'17mm'}"
    >
      <div class="cell_5"
           style="height:100%;margin-top:0.25mm;overflow: hidden;border-right: 1px solid #000;"
           :style="{width:config.templateSize === '76mm*130mm'?'44mm':'50%'}"
      >
        <!--        <div><img src="https://files.test.ybm100.com/G1/M00/1E/E2/Cgoz02HC3FqARb4uAAAHmUddVbQ352.png" alt=""></div>-->
      </div>
      <div class="cell_5"
        style="height: 100%;position: absolute;left: 50%;top: 0"
        :style="{width:config.templateSize === '76mm*130mm'?'26mm':'50%',left:config.templateSize === '76mm*130mm'?'44mm':'50%'}"
      >
        <div style="width: 100%;height: 100%;overflow: hidden;word-break: break-all"><span style="padding: 5px;"
        >备注：{{ config.remark }}</span></div>
      </div>
    </div>
    <!-- <div class="page-row seven"
         style="height: 5mm;border-bottom: 1px solid #000;text-align: left;background-color: #fff;overflow: hidden;position: relative;"
    >
      <div class="cell_25"
           style="width: 25%;height: 100%;border-right: 1px solid #000;"
      ></div>
      <div class="cell_25"
           style="width: 25%;height: 100%;border-right: 1px solid #000;position: absolute;left: 25%;top: 0"
      ></div>
      <div class="cell_25"
           style="width: 25%;height: 100%;border-right: 1px solid #000;position: absolute;left: 50%;top: 0"
      ></div>
      <div class="cell_25" style="width: 25%;height: 100%;"
      ></div>
    </div> -->
    <div class="page-row eight"
         style="overflow: hidden;text-align: left;background-color: #fff;position: relative;"
         :style="{height: config.templateSize === '100mm*150mm' ? '50mm':'22mm'}"
    >
      <div class="cell_5"
           style="width:50%;height:100%;margin-top:0.25mm;overflow: hidden;border-right: 1px solid #000;"
      >
        <div style="height: 100%;overflow: hidden;">寄方信息：
          {{ config.consignor }}
          {{ config.deliveryMobile }}
          {{ config.mailRegionName }}
          {{ config.mailAddress }}
        </div>
      </div>
      <div class="cell_4" style="width: 50%;position: absolute;top: 0;left: 50%"
        :style="{height:config.templateSize === '100mm*150mm'?'40mm':'20mm'}"
      >
        <div
          style="width: 100%;height: 50%;box-sizing:border-box;padding:5px;word-break: break-all;"
        >
          订单号：{{ config.orderNo }}
        </div>
        <div style="width: 100%;border-bottom: 1px solid #000"></div>
        <div style="width: 100%;height: 50%;padding-left: 5px;padding-top: 3mm">
          始发城市：{{ jDKYFace.sendCity }}
        </div>
      </div>
      <!-- <div style="position: absolute;bottom: 0;right: 0;color: red">www.jd-ex.com</div> -->
    </div>
  </div>
</template>

<script>
export default {
  name: 'JDexpress',
  props: {
    config: {
      type: Object,
      default: () => {
        return {};
      }
    }
  },
  data() {
    return {
      jDKYFace: {}
    };
  },
  created() {
    console.log('== config===',this.config);
    this.jDKYFace = this.config.jDKDFace ? {...this.config.jDKDFace || {}} : { ...this.config.jDKYFace || {} };
  }
};
</script>

<style scoped>
/* @page {
  size: 100mm 113mm;
  margin: 0;
  padding: 0;
} */
</style>
